package com.yy.gameecology.hdzj.element.component.xmodule.pepc;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.wzry.JoinGameResult;
import com.yy.gameecology.activity.client.yrpc.PubgGameGatewayClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.service.pepc.PepcGameLiveService;
import com.yy.gameecology.activity.service.pepc.PepcGameService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PepcActTeamComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.PepcGameComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-02 15:20
 **/
@Component
@RestController
@RequestMapping("/5141")
public class PepcGameComponent extends BaseActComponent<PepcGameComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PepcGameService pepcGameService;

    @Autowired
    private PepcGameLiveService pepcGameLiveService;
    @Autowired
    private PepcActTeamComponent pepcActTeamComponent;

    @Autowired
    private PubgGameGatewayClient pubgGameGatewayClient;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Override
    public Long getComponentId() {
        return ComponentId.PEPC_GAME;
    }


    /**
     * 创建好腾讯的比赛
     */
    @ScheduledExt
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(initialDelay = 7000L, fixedDelay = 30000L)
    public void createRemoteGames() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            PepcGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("createRemoteGames:" + actId, 60, () -> {
                pepcGameService.createRemoteGames(attr);
            });

        }
    }

    /**
     * 创建好腾讯的队伍
     */
    @ScheduledExt
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(initialDelay = 7000L, fixedDelay = 30000L)
    public void createRemoteTeams() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            PepcGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("createRemoteTeams:" + actId, 60, () -> {
                pepcGameService.createRemoteTeams(attr);
            });

        }
    }

    /**
     * 将队伍加入到赛事
     */
    @ScheduledExt
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(initialDelay = 7000L, fixedDelay = 30000L)
    public void signUpRemoteTeamGames() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            PepcGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("signUpRemoteTeamGames:" + actId, 60, () -> {
                pepcGameService.signUpRemoteTeamGames(attr);
            });
        }
    }

    /**
     * 给队员报名
     */
    @ScheduledExt
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(initialDelay = 7000L, fixedDelay = 30000L)
    public void joinRemoteTeams() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            PepcGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("joinRemoteTeams:" + actId, 60, () -> {
                pepcGameService.joinRemoteTeams(attr);
            });
        }
    }

    /**
     * 赛事结算
     */
    @ScheduledExt
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(initialDelay = 7000L, fixedDelay = 30000L)
    public void settleGames() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            PepcGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("settleGames:" + actId, 60, () -> {
                pepcGameService.settleGames(commonService.getNow(actId), attr);
            });
        }
    }


    /**
     * 拉取腾讯的直播数据源地址，保存起来，给页面查询用
     */
    @ScheduledExt
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0/10 * * * * ? ")
    public void genLiveInfo() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            PepcGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            timerSupport.work("genLiveInfo:" + attr.getActId(), 30, () -> {
                Date now = commonService.getNow(actId);
                Date startTimeBegin = DateUtil.getDayBeginTime(now);
                Date startTimeEnd = DateUtil.getDayEndTime(now);
                pepcGameLiveService.genGameLiveInfo(attr.getActId(), now, startTimeBegin, startTimeEnd);
            });
        }
    }


    /**
     * 查询挂件是否展示
     *
     * @param seq
     * @param actId
     * @param sid
     * @param ssid
     * @return
     */
    @GetMapping("queryLayerStatus")
    public Response<Map<String, Object>> queryLayerStatus(@RequestParam("seq") String seq,
                                                          @RequestParam(value = "actId", defaultValue = "0") long actId,
                                                          @RequestParam(value = "sid", defaultValue = "0") long sid,
                                                          @RequestParam(value = "ssid", defaultValue = "0") long ssid) {
        if (actId == 0 || sid == 0 || ssid == 0) {
            return new Response<>(Code.E_DATA_ERROR.getCode(), "para error");
        }

        PepcGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }

        boolean existingGame = pepcGameService.showGameLayer(attr, sid, ssid);

        Map<String, Object> data = new HashMap<>(3);
        data.put("showStatus", existingGame ? 1 : 0);
        data.put("time", System.currentTimeMillis());
        data.put("grey", commonService.isGrey(actId));

        return Response.success(data);
    }

    /**
     * 获取进入游戏h5链接
     */
    @RequestMapping("/jumpGame")
    public Response<?> jumpGame(HttpServletRequest request,
                                Long actId, Long cmptInx, Long gameId, Long sid, Long ssid,
                                @RequestParam(name = "verifyCode", required = false) String verifyCode,
                                @RequestParam(name = "recordId", required = false) String recordId,
                                @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "请登录");
        }
        if (gameId == null) {
            log.warn("jumpGame gameId empty,uid:{}", uid);
            return Response.fail(-2, "赛事不存在");
        }

        String closeRiskCheck = cacheService.getActAttrValue(actId, GeActAttrConst.CLOSE_PEPC_JUMP_GAME_RISK_CHECK);
        //风控
        if (!Const.ONESTR.equals(closeRiskCheck)) {
            try {
                PepcActTeamComponentAttr tAttr = pepcActTeamComponent.tryGetUniqueComponentAttr(actId);
                if(tAttr == null){
                    return Response.fail(-1, "活动未配置");
                }
                zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), tAttr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);
            } catch (SuperException e) {
                log.info("doRiskCheck,actId:{}, uid:{},e:{} ", actId, uid, e.getMessage(), e);
                JoinGameResult joinGameResult = new JoinGameResult();
                joinGameResult.setRiskRecheck(e.getData());
                return Response.fail(e.getCode(), e.getMessage(), joinGameResult);
            }
        }

        try {
            return pepcGameService.jumpGame(getComponentAttr(actId, cmptInx), commonService.getNow(actId), uid, gameId, sid, ssid);
        } catch (SuperException e) {
            log.warn("jumpGame warn actId:{},sid:{},ssid:{},uid:{},e:{}", actId, sid, ssid, uid, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());

        } catch (Exception e) {
            log.error("jumpGame error actId:{},sid:{},ssid:{},uid:{},e:{}", actId, sid, ssid, uid, e.getMessage(), e);
            return Response.fail(999, "网络繁忙");
        }

    }

    /**
     * 赛事数据，用与测试和观察数据
     */
    @RequestMapping("/queryMatchBattleDetail")
    public Response<String> queryMatchBattleDetail(Long actId, Long matchId) {
        return Response.success(pubgGameGatewayClient.queryMatchBattleDetail(matchId).toString());
    }

}
