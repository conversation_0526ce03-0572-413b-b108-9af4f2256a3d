package com.yy.gameecology.hdzj.element.redis;


import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.NewUserFirstEnterComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Component;


/**
 * 新用户首次进模板组件
 * 搭配礼包组件使用
 * 业务逻辑为：用户在活动期间首次进入对应模板,当用户满足购买礼包时，会弹出新手礼包弹窗
 * 不满足的用户会提前初始化进redis,同一个活动下,与礼包组件使用的是同个key
 *
 * <AUTHOR>
 * @date 2021/15 14:25
 */
@UseRedisStore
@Component
public class NewUserFirstEnterComponent extends BaseActComponent<NewUserFirstEnterComponentAttr> {

    private final static String FIRST_ENTER_HASH_KEY = "first_enter";

    @Override
    public Long getComponentId() {
        return ComponentId.NEW_USER_FIRST_ENTER;
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void handleNewUserFirstEnter(UserEnterTemplateEvent event, NewUserFirstEnterComponentAttr attr) {
        long uid = event.getUid();
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());

        if (!ArrayUtils.contains(attr.getBusiIds(), event.getBusiId())) {
            log.info("handleNewUserFirstEnter ingore@actId:{} uid:{} busiId:{} index:{}"
                    , attr.getActId(), uid, event.getBusiId(), attr.getCmptUseInx());
            return;
        }

        boolean isNewUser = false;
        try {

            if (actRedisDao.hsetnx(groupCode, makeKey(attr, FIRST_ENTER_HASH_KEY), String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss())) {
                isNewUser = !actRedisDao.sIsMember(groupCode, attr.getOldUserSetKey(), String.valueOf(uid));
                //新用户首次进入模板弹窗
                if (isNewUser) {
                    GameecologyActivity.Act202103_InHouseTemplateEventRsp.Builder tips
                            = GameecologyActivity.Act202103_InHouseTemplateEventRsp.newBuilder()
                            .setActId(attr.getActId())
                            .setCode(0)
                            .setMessage("success")
                            .setIsShow(1)
                            .setTitle("newUserFirstEnter");
                    GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                            .setUri(GameecologyActivity.PacketType.kAct202103_InHouseTemplateEventRsp_VALUE)
                            .setAct202103InHouseTemplateEventRsp(tips).build();
                    RetryTool.withRetryCheck(attr.getActId(), event.getSeq(), () -> svcSDKService.unicastUid(uid, msg));

                }
            }

            log.info("handleNewUserFirstEnter done@actId:{} uid:{} busiId:{} isNewUser:{} index:{}"
                    , attr.getActId(), uid, event.getBusiId(), isNewUser, attr.getCmptUseInx());
        } catch (Exception e) {
            log.error("handleNewUserFirstEnter error@actId:{} uid:{} busiId:{} isNewUser:{} index:{} {}"
                    , attr.getActId(), uid, event.getBusiId(), isNewUser, attr.getCmptUseInx(), e.getMessage(), e);
        }

    }
}
