package com.yy.gameecology.hdzj.element.redis;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.RelateUidsVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.YoPopupMessage;
import com.yy.gameecology.activity.bean.anchorwelfare.*;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.bean.mq.ZhuiwanAudioEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanPhotoEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanPostEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanSignInEvent;
import com.yy.gameecology.activity.client.thrift.FtsLiveHelperServiceThriftClient;
import com.yy.gameecology.activity.client.thrift.TAnchorPrizeStatServiceClient;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaLoginClient;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.anchorwelfare.AnchorWelfareConst;
import com.yy.gameecology.common.consts.anchorwelfare.TaskState;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.WelfareNoticeInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.AnchorWelfareTaskComponetAttr;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.login.LoginRecord;
import com.yy.protocol.pb.zhuiwan.room.SkillCardProviderService;
import com.yy.protocol.pb.zhuiwan.room.Skillcard;
import com.yy.protocol.pb.zhuiwan.signin.ZhuiwanSign;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;


@UseRedisStore
@RestController
@RequestMapping("/cmpt/anchorWelfareTask")
@Component
public class AnchorWelfareTaskComponet extends BaseActComponent<AnchorWelfareTaskComponetAttr> {


    /**
     * 累计提取
     */
    private static final String TASK_TOTAL_WITHDRAW = "task_total_withdraw";

    /**
     * 等待提取
     */
    private static final String TASK_WAIT_WITHDRAW = "task_wait_withdraw";

    private final static String LOGIN_TASK_FINISH_KEY = "login_task_finish_key";

    /**
     * 用户位于的当前任务天数
     */
    private static final String TASK_CURRENT_LEVEL_KEY = "task_current_level";

    private static final String APP_FIRST_REQ_NOTICE = "app_first_req_notice";

    /**
     * 首次进入pc活动页
     */
    private static final String PC_FIRST_INSERT_ACTIVITY = "pcyy_first_insert_activity";

    /**
     * 定时刷新任务等级
     */
    private static final String SCHEDULE_REFRESH_TASK_KEY = "schedule_refresh_task:%s";

    private static final String TASK_PROGRESS_KEY_FORMAT = "task_progress_%d_%d";

    private static final String TASK_AWARD_KEY = "task_award_%d_%d";


    private static final String TASK_FINISH_TIME_KEY = "task_finish_time_%d_%d";

    private static final String TASK_AWARD_TIME_KEY = "task_award_time_%d_%d";


    private static final String AUTO_REFRESH_SIGN_STATE = "auto_refresh_sign_state_%d_%d";

    private static final String AUTO_SIGN_IN_EVENT_SEQ = "auto_zy_sign_in:%d:%s:YOMI";

    private static final String TASK_OPEN_TIME_KEY = "task_open_time_%d";


    private final static Integer MIN_TASK_LEVEL = 1;
    private final static Integer MAX_TASK_LEVEL = 7;

    /**
     * 连续3日未完成过任意签到任务，第四天提醒
     */
    private final static Integer NEVER_FINISH_DAY = 4;

    /**
     * PC端打开过页面后，3天仍未领取登录奖励，第四天提醒
     */
    private final static Integer NEVER_LOGIN_DAY = 4;

    private final static int UPDATE_RANK_RETRY = 3;

    /**
     * 移除追玩弹窗白名单，现没有移除接口，超时时间设置为当前时间-60秒
     */
    private final static int YOMI_POPUP_EXPIRE_SECOND = -60;



    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    protected ActRedisGroupDao actRedisDao;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private TAnchorPrizeStatServiceClient anchorPrizeStatServiceClient;

    @Autowired
    private FtsLiveHelperServiceThriftClient ftsLiveHelperServiceThriftClient;

    @Autowired
    private ZhuiyaLoginClient zhuiyaLoginClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Reference(registry = "yrpc-reg", protocol = "yrpc", owner = "zhuiya_server_yrpc")
    private SkillCardProviderService skillCardProvider;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private ZhuiyaLoginClient loginClient;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private SignedService signedService;


    @Override
    public Long getComponentId() {
        return ComponentId.ANCHOR_WELFARE_TASK;
    }


    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, AnchorWelfareTaskComponetAttr attr) {
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }

        Integer taskLevel = getTaskLevel(attr, Long.parseLong(event.getMember()));
        if (taskLevel == null) {
            return;
        }

        log.info("onTaskProgressChanged taskLevel:{},event:{}",taskLevel,JsonUtil.toJson(event));
        for (AnchorWelfareTaskComponetAttr.HdztTaskSource taskSource : attr.getTaskSourceList()) {
            if (taskLevel != taskSource.getLevel()) {
                continue;
            }
            if (event.getRankId() != taskSource.getRankId() || event.getPhaseId() != taskSource.getPhaseId()) {
                continue;
            }

            long step = event.getCurrTaskIndex() - event.getStartTaskIndex();
            addProgress(attr, Long.parseLong(event.getMember()), taskSource.getTaskType(), event.getSeq(), now, step);
        }

    }

    @HdzjEventHandler(value = ZhuiwanSignInEvent.class, canRetry = true)
    public void onSignInEvent(ZhuiwanSignInEvent event, AnchorWelfareTaskComponetAttr attr) {
        if (!event.getApp().equals(attr.getApp())) {
            return;
        }
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }

        UserRoleInfo userRoleInfo = getUserRoleInfo(event.getUid(), attr);
        if (userRoleInfo == null) {
            log.warn("onSignInEvent  not in whitelist,  actId:{}, uid:{}", attr.getActId(), event.getUid());
            return;
        }

        addProgress(attr, event.getUid(), AnchorWelfareTaskComponetAttr.TaskType.NORMAL_SIGN.getCode(), event.getSeq(), now, 1);
    }

    @HdzjEventHandler(value = ZhuiwanPostEvent.class, canRetry = true)
    public void onPostEvent(ZhuiwanPostEvent event, AnchorWelfareTaskComponetAttr attr) {
        if (!event.getApp().equals(attr.getApp())) {
            return;
        }
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }

        UserRoleInfo userRoleInfo = getUserRoleInfo(event.getUid(), attr);
        if (userRoleInfo == null) {
            log.warn("onPostEvent  not in whitelist,  actId:{}, uid:{}", attr.getActId(), event.getUid());
            return;
        }

        addProgress(attr, event.getUid(), AnchorWelfareTaskComponetAttr.TaskType.ADD_POST.getCode(), event.getSeq(), now, 1);
    }

    @HdzjEventHandler(value = ZhuiwanPhotoEvent.class, canRetry = true)
    public void onUploadPhotoEvent(ZhuiwanPhotoEvent event, AnchorWelfareTaskComponetAttr attr) {
        if (!event.getApp().equals(attr.getApp())) {
            return;
        }
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }

        UserRoleInfo userRoleInfo = getUserRoleInfo(event.getUid(), attr);
        if (userRoleInfo == null) {
            log.warn("onUploadPhotoEvent  not in whitelist,  actId:{}, uid:{}", attr.getActId(), event.getUid());
            return;
        }

        addProgress(attr, event.getUid(), AnchorWelfareTaskComponetAttr.TaskType.UPLOAD_PHOTO.getCode(), event.getSeq(), now, 1);
    }

    @HdzjEventHandler(value = ZhuiwanAudioEvent.class, canRetry = true)
    public void onUploadAudioEvent(ZhuiwanAudioEvent event, AnchorWelfareTaskComponetAttr attr) {
        if (!event.getApp().equals(attr.getApp())) {
            return;
        }
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }

        UserRoleInfo userRoleInfo = getUserRoleInfo(event.getUid(), attr);
        if (userRoleInfo == null) {
            log.warn("onUploadAudioEvent  not in whitelist,  actId:{}, uid:{}", attr.getActId(), event.getUid());
            return;
        }

        addProgress(attr, event.getUid(), AnchorWelfareTaskComponetAttr.TaskType.UPLOAD_AUDIO.getCode(), event.getSeq(), now, 1);
    }

    public void addProgress(AnchorWelfareTaskComponetAttr attr, long uid, int type, String seq, Date now, long step) {
        log.info("addProgress uid:{},type:{},seq:{},step:{}",uid,type,seq,step);
        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel == null) {
            return;
        }

        //奖池为0，不再累任务进度
        if(!taskAwardPoolValide(attr)){
            log.warn("addProgress taskAwardPoolValide false ,uid:{},taskLevel:{},type:{}", uid, taskLevel, type);
            return;
        }

        TaskGroupConfig groupConfig = attr.getGroupConfig().stream().filter(v -> v.getLevel() == taskLevel).findFirst().orElse(null);
        if (groupConfig == null) {
            log.error("addProgress groupConfig is null,uid:{},taskLevel:{},type:{}", uid, taskLevel, type);
            return;
        }

        List<TaskInfoConfig> taskList = attr.getTaskConfig().get(taskLevel);
        TaskInfoConfig taskInfoConfig = taskList.stream().filter(v -> v.getTaskType() == type).findFirst().orElse(null);
        if (taskInfoConfig == null) {
            log.info("addProgress taskInfoConfig is null,uid:{},taskLevel:{},type:{}", uid, taskLevel, type);
            return;
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        String progressKey = getTaskProgressKey(attr, uid, taskLevel);
        int progress = Convert.toInt(actRedisDao.hget(groupCode, progressKey, String.valueOf(type)));
        if (progress >= taskInfoConfig.getPassValue()) {
            log.info("addProgress ignore by progress@actId:{},uid:{},type:{},seq:{}", attr.getActId(), uid, type, seq);
            return;
        }

        boolean notDone = actRedisDao.setNX(groupCode, makeKey(attr, "seq:taskProgressEvent:" + seq), "1");
        if (!notDone) {
            log.info("addProgress ignore by seq done@actId:{},uid:{},type:{},seq:{}", attr.getActId(), uid, type, seq);
            return;
        }

        //允许部分添加，防止步长大于1时任务完不成
        List<Long> results = actRedisDao.hIncrWithLimit(groupCode, progressKey, String.valueOf(type), step, taskInfoConfig.getPassValue(), true);
        if (results.get(0)!=1 && results.get(0)!=2) {
            log.info("addProgress ignore by incr error@actId:{},uid:{},type:{},seq:{},step:{},results:{}", attr.getActId(), uid, type, seq, step, JsonUtil.toJson(results));
            return;
        }

        long realInc = 0;
        if (results.get(0) == 1) {
            realInc = step;
        } else if (results.get(0) == 2) {
            realInc = results.get(1);
        }
        log.info("addProgress realInc:{},progress:{},uid:{},type:{},taskLevel:{}",realInc,progress,uid,type,taskLevel);
        //当前任务已完成
        if ((progress+realInc) >= taskInfoConfig.getPassValue()) {
            updateTaskFinishTime(attr, uid, taskLevel, now);

            //发送任务完成通知
            JSONObject json = new JSONObject(2);
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), "5091_taskFinish", JsonUtil.toJson(json), StringUtils.EMPTY, uid);
        }

    }

    private void updateTaskFinishTime(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel, Date now) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String finishKey = makeKey(attr, String.format(TASK_FINISH_TIME_KEY, uid, taskLevel));
        actRedisDao.set(groupCode, finishKey, DateUtil.format(now, DateUtil.PATTERN_TYPE1));
    }


    private String getTaskProgressKey(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel) {
        return makeKey(attr, String.format(TASK_PROGRESS_KEY_FORMAT, uid, taskLevel));
    }


    /**
     * 任务列表
     */
    @RequestMapping("/taskList")
    public Response<WelfareTaskInfoVo> taskList(HttpServletRequest request, HttpServletResponse response,
                                                @RequestParam("actId") long actId,
                                                @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx,
                                                @RequestHeader("x-fts-host-name") String app,
                                                @RequestHeader("YYHeader-y0") String hdid,
                                                @RequestHeader(value = "YYHeader-Platform", defaultValue = "0") int clientType) {

        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }


        AnchorWelfareTaskComponetAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }


        Date now = commonService.getNow(actId);
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return Response.fail(1, "不在活动时间");
        }

        try {
            WelfareTaskInfoVo welfareTaskInfoVo = getwelfareTaskDetailInfo(attr, uid, app);
            return Response.success(welfareTaskInfoVo);
        } catch (BusinessException e) {
            log.warn("taskList error@actId:{},uid:{},code:{},messge:{},data:{}", actId, uid, e.getCode(), e.getMessage(), e.getData());
            return new Response(e.getCode(), e.getMessage(), e.getData());
        } catch (Exception e) {
            log.error("taskList error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }

    }

    public WelfareTaskInfoVo getwelfareTaskDetailInfo(AnchorWelfareTaskComponetAttr attr, long uid, String app) {
        WelfareTaskInfoVo result = new WelfareTaskInfoVo();

        //检测是否在白名单
        UserRoleInfo userRoleInfo = getUserRoleInfo(uid, attr);
        if (userRoleInfo == null) {
            log.warn("getwelfareTaskDetailInfo  not in whitelist,  actId:{}, uid:{}", attr.getActId(), uid);
            throw new BusinessException(400, "非白名单用户没有参与资格");
        }
        result.setNewUser(userRoleInfo.getIsNew() == 1 ? true : false);

        Date now = commonService.getNow(attr.getActId());
        String groupCode = getRedisGroupCode(attr.getActId());
        String waitWithdrawKey = makeKey(attr, TASK_WAIT_WITHDRAW);
        long waitWithdraw = actRedisDao.zscore(groupCode, waitWithdrawKey, String.valueOf(uid));

        String totaltWithdrawKey = makeKey(attr, TASK_TOTAL_WITHDRAW);
        long totaltWithdraw = actRedisDao.zscore(groupCode, totaltWithdrawKey, String.valueOf(uid));

        LoginTaskVo loginTaskVo = getLoginTaskInfo(attr, userRoleInfo, uid);
        List<TaskGroupVo> taskGroupList = getwelfareTaskList(attr, userRoleInfo, uid);

        result.setTotalWithdraw(totaltWithdraw);
        result.setWaitWithdraw(waitWithdraw);
        result.setLoginInfo(loginTaskVo);
        result.setTaskGroupList(taskGroupList);

        return result;
    }

    /**
     * 查列表的时候若当前任务开启，且签到任务未完成，则去追玩查下签约状态
     */
    private List<TaskGroupVo> getwelfareTaskList(AnchorWelfareTaskComponetAttr attr, UserRoleInfo userRoleInfo, long uid) {
        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel == null) {
            taskLevel = 0;
        }

        boolean newUser = userRoleInfo.getIsNew() == 1 ? true : false;

        List<TaskGroupConfig> groupConfigs = attr.getGroupConfig();
        Map<Integer, List<TaskInfoConfig>> taskConfigMap = attr.getTaskConfig();

        //自动刷新签到任务的状态，针对任务开启前当天已经去签到了的
        autoHandleSignState(attr,uid,taskLevel);

        List<TaskGroupVo> result = Lists.newArrayListWithCapacity(groupConfigs.size());
        for (TaskGroupConfig groupConfig : groupConfigs) {
            TaskGroupVo outItem = new TaskGroupVo();
            BeanUtils.copyProperties(groupConfig, outItem);
            List<TaskInfoConfig> taskConfig = taskConfigMap.get(groupConfig.getLevel());
            List<WelfareTaskDetailVo> inItem = taskConfig.stream().map(v -> {
                WelfareTaskDetailVo item = new WelfareTaskDetailVo();
                item.setTaskType(v.getTaskType());
                item.setTaskName(v.getTaskName());
                item.setProgressGoal(v.getPassValue());
                item.setUserAward(newUser ? v.getNewUserAward() : v.getOldUserAward());
                return item;
            }).collect(Collectors.toList());

            if (taskLevel > groupConfig.getLevel()) {
                inItem.stream().forEach(v -> {
                    v.setProgress(v.getProgressGoal());
                    v.setState(TaskState.FINISH.getValue());
                });
            } else if (taskLevel < groupConfig.getLevel()) {
                inItem.stream().forEach(v -> {
                    v.setProgress(0);
                    v.setState(TaskState.WAIT_OPEN.getValue());
                });
            } else {
                fillCureentTaskInfo(attr, inItem, taskLevel, uid);
            }

            outItem.setTask(inItem);
            result.add(outItem);
        }

        return result;
    }

    private void autoHandleSignState(AnchorWelfareTaskComponetAttr attr,long uid,int taskLevel) {
        if(taskLevel<MIN_TASK_LEVEL){
            return;
        }
        List<TaskInfoConfig> taskConfig = attr.getTaskConfig().get(taskLevel);
        TaskInfoConfig signTask = taskConfig.stream().filter(v->v.getTaskType()==AnchorWelfareTaskComponetAttr.TaskType.NORMAL_SIGN.getCode()).findFirst().orElse(null);
        if(signTask!=null) {
            String groupCode = getRedisGroupCode(attr.getActId());
            String progressKey = getTaskProgressKey(attr, uid, taskLevel);
            int progress = Convert.toInt(actRedisDao.hget(groupCode, progressKey, String.valueOf(signTask.getTaskType())));
            if(progress<signTask.getPassValue() && unAutoRefreshSignState(attr,uid,taskLevel)) {
                try{
                    ZhuiwanSign.LastSignInRecordReq req = ZhuiwanSign.LastSignInRecordReq.newBuilder().setApp(ZhuiyaPbCommon.App.APP_YOMI).setUid(uid).build();
                    ZhuiwanSign.LastSignInRecordRsp rsp = zhuiyaLoginClient.queryLastSignInRecord(req);
                    if(rsp!=null && rsp.getCode() == ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE && rsp.getResult()!=null){
                        String LastSignDate = rsp.getResult().getLastSignDate();
                        if(LastSignDate!=null && LastSignDate.equals(DateUtil.format(DateUtil.PATTERN_TYPE2))){
                            Date now = commonService.getNow(attr.getActId());
                            String seq = String.format(AUTO_SIGN_IN_EVENT_SEQ,uid,LastSignDate) + ":" + DateUtil.getSeconds();
                            log.info("autoHandleSignState uid:{},taskLevel:{},actId:{}",uid,taskLevel,attr.getActId());
                            addProgress(attr, uid, AnchorWelfareTaskComponetAttr.TaskType.NORMAL_SIGN.getCode(), seq, now, 1);
                            updateAutoRefreshSignState(attr,uid,taskLevel);
                        }
                    }
                }catch(Exception e){
                    log.error("autoHandleSignState error,uid:{},taskLevel:{},actId:{},e:{}",uid,taskLevel,attr.getActId(),e.getMessage(),e);
                }
            }
        }

    }


    private void updateAutoRefreshSignState(AnchorWelfareTaskComponetAttr attr,long uid,int taskLevel) {
        String realDate = DateUtil.format(DateUtil.PATTERN_TYPE1);
        String groupCode = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr, String.format(AUTO_REFRESH_SIGN_STATE, uid, taskLevel));
        actRedisDao.set(groupCode,key,realDate,DateUtil.ONE_WEEK_SECONDS);
    }
    private boolean unAutoRefreshSignState(AnchorWelfareTaskComponetAttr attr,long uid,int taskLevel) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr, String.format(AUTO_REFRESH_SIGN_STATE, uid, taskLevel));
        String value = actRedisDao.get(groupCode,key) ;
        return StringUtil.isBlank(value) ? true : false;
    }

    private void fillCureentTaskInfo(AnchorWelfareTaskComponetAttr attr, List<WelfareTaskDetailVo> task, int taskLevel, long uid) {

        String groupCode = getRedisGroupCode(attr.getActId());
        String progressKey = getTaskProgressKey(attr, uid, taskLevel);
        String awardKey = getTaskAwardKey(attr, uid, taskLevel);
        for (WelfareTaskDetailVo item : task) {
            int progress = Convert.toInt(actRedisDao.hget(groupCode, progressKey, String.valueOf(item.getTaskType())));
            item.setProgress(Math.min(progress, item.getProgressGoal()));
            item.setState(TaskState.UNFINIFH.getValue());
            if (progress >= item.getProgressGoal()) {
                item.setState(TaskState.UNRECEIVE.getValue());
            }
            int hasAward = Convert.toInt(actRedisDao.hget(groupCode, awardKey, String.valueOf(item.getTaskType())));
            if (hasAward >= 1) {
                item.setState(TaskState.FINISH.getValue());
            }

        }
    }

    private LoginTaskVo getLoginTaskInfo(AnchorWelfareTaskComponetAttr attr, UserRoleInfo userRoleInfo, long uid) {
        LoginTaskVo LoginTaskVo = new LoginTaskVo();
        LoginTaskAward loginTaskAward = attr.getLoginTaskAward().get(0);
        LoginTaskVo.setTaskName(loginTaskAward.getTaskName());
        LoginTaskVo.setUserAward(loginTaskAward.getOldUserAward());
        if (userRoleInfo.getIsNew() == 1) {
            LoginTaskVo.setUserAward(loginTaskAward.getNewUserAward());
        }
        if (getLoginState(attr, uid)) {
            LoginTaskVo.setState(TaskState.FINISH.getValue());
        } else {
            LoginTaskVo.setState(TaskState.UNRECEIVE.getValue());
        }
        return LoginTaskVo;

    }

    /**
     * 任务列表-领取奖励,加上风控
     */
    @RequestMapping("/drawAward")
    public Response<Map<String, Object>> drawAward(HttpServletRequest request, HttpServletResponse response,
                                                   @RequestHeader("x-fts-host-name") String app,
                                                   @RequestHeader("YYHeader-y0") String hdid,
                                                   @RequestHeader(value = "YYHeader-Platform", defaultValue = "0") int clientType,
                                                   @RequestParam("actId") long actId,
                                                   @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx,
                                                   @RequestParam(name = "taskType") Integer taskType,
                                                   @RequestParam(name = "level") Integer level,
                                                   String verifyCode, String recordId, String verifyToken) {

        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }

        AnchorWelfareTaskComponetAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }


        Date now = commonService.getNow(actId);
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return Response.fail(1, "不在活动时间");
        }

        if (!app.equals(attr.getApp())) {
            return Response.fail(400, "app not match!");
        }

        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel == null || taskLevel.intValue() != level) {
            log.error("drawAward taskLevel not match level,uid:{},taskType:{},taskLevel:{},level:{}", uid, taskType, taskLevel, level);
            return Response.fail(1, "天参数有误");
        }

        //总奖池判断
        if (!taskAwardPoolValide(attr)) {
            log.warn("drawAward awardpool invalide,  actId:{}, uid:{}", attr.getActId(), uid);
            return Response.fail(SuperException.AWARD_POOL_NOTENOUGH, "奖池为0，活动已结束！");
        }

        try {
            //新风控
            zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid,verifyCode, verifyToken, recordId);
            long packageId = drawAward(attr, uid, taskType, level, now);
            return Response.success(Map.of("packageId", packageId));
        } catch (SuperException e) {
            log.warn("drawAward warn,actId:{},uid:{},e:{},e", actId, uid, e.getMessage(), e);
            if(e.getData()!=null){
                return Response.fail(e.getCode(), e.getMessage(), Map.of("riskRecheck", e.getData()));
            }else{
                return Response.fail(e.getCode(), e.getMessage());
            }
        } catch (BusinessException e) {
            log.warn("drawAward BusinessException @actId:{},uid:{},code:{},messge:{},data:{}", actId, uid, e.getCode(), e.getMessage(), e.getData());
            return new Response(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("drawAward error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }

    }

    public long drawAward(AnchorWelfareTaskComponetAttr attr, long uid, int type, int taskLevel, Date now) {

        log.info("drawAward uid:{},type:{},taskLevel:{}",uid,type,taskLevel);
        TaskGroupConfig groupConfig = attr.getGroupConfig().stream().filter(v -> v.getLevel() == taskLevel).findFirst().orElse(null);
        if (groupConfig == null) {
            log.error("drawAward groupConfig is null,uid:{},taskLevel:{},type:{}", uid, taskLevel, type);
            throw new BusinessException(500, "任务配置有误");
        }

        List<TaskInfoConfig> taskList = attr.getTaskConfig().get(taskLevel);
        TaskInfoConfig taskInfoConfig = taskList.stream().filter(v -> v.getTaskType() == type).findFirst().orElse(null);
        if (taskInfoConfig == null) {
            log.info("drawAward taskInfoConfig is null,uid:{},taskLevel:{},type:{}", uid, taskLevel, type);
            throw new BusinessException(500, "参数有误");
        }

        UserRoleInfo userRoleInfo = getUserRoleInfo(uid, attr);
        if (userRoleInfo == null) {
            log.warn("drawAward  not in whitelist,  actId:{}, uid:{}", attr.getActId(), uid);
            throw new BusinessException(500, "您不符合参与条件，如有疑问请联系YY:80007");
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        String progressKey = getTaskProgressKey(attr, uid, taskLevel);
        int progress = Convert.toInt(actRedisDao.hget(groupCode, progressKey, String.valueOf(type)));
        if (progress < taskInfoConfig.getPassValue()) {
            log.error("drawAward  task not finish@actId:{},uid:{},type:{},taskLevel:{}", attr.getActId(), uid, type, taskLevel);
            throw new BusinessException(500, "任务未完成");
        }

        String awardKey = getTaskAwardKey(attr, uid, taskLevel);
        int hasAward = Convert.toInt(actRedisDao.hget(groupCode, awardKey, String.valueOf(type)));
        if (hasAward >= 1) {
            log.warn("drawAward  has award@actId:{},uid:{},type:{},taskLevel:{}", attr.getActId(), uid, type, taskLevel);
            throw new BusinessException(500, "已领取");
        }

        int awardNum = taskInfoConfig.getOldUserAward();
        if (userRoleInfo.getIsNew() == 1) {
            awardNum = taskInfoConfig.getNewUserAward();
        }

        //插入到待提取账户
        String waitWithdrawKey = makeKey(attr, TASK_WAIT_WITHDRAW);
        String seq =  getWaitWithdrawSeq(attr,uid,taskLevel,type);
        actRedisDao.zIncrWithSeq(groupCode, seq, waitWithdrawKey, String.valueOf(uid), awardNum);

        //更新任务领取奖励时间
        updateTaskAwardTime(attr, uid, taskLevel, now);

        //设置已领取标记
        actRedisDao.hset(groupCode, awardKey, String.valueOf(type), String.valueOf(1));


        //如任务奖励已全部完成，则开启下一轮任务，且为自动提取日，则自动提取
        long finishSize = actRedisDao.hsize(groupCode, awardKey);
        if (finishSize == taskList.size()) {
            //增加任务累计完成统计
            addTaskFinishCountStatistics(attr,taskLevel,uid);

            autoWithdraw(attr, uid, userRoleInfo, taskLevel, now);

            //刷新签到任务等级
            refreshSignTaskLevel(attr, uid, taskLevel, now);
        }
        return 0;
    }

    private void addTaskFinishCountStatistics( AnchorWelfareTaskComponetAttr attr,int taskLevel,long uid) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String seq = getTaskFinishCountStatisticsSeq(attr,taskLevel,uid);
        String key = makeKey(attr,AnchorWelfareConst.TASK_FINISH_COUNT_STATISTIC);
        actRedisDao.hIncrByKeyWithSeq(groupCode,seq,key,String.valueOf(taskLevel),1,DateUtil.TWO_MONTH_SECONDS);
    }

    private String getTaskFinishCountStatisticsSeq( AnchorWelfareTaskComponetAttr attr,int taskLevel,long uid) {
        String seq = makeKey(attr, "seq:taskFinishCountStatis:" + uid + ":" + taskLevel);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())){
            seq = seq + ":" + DateUtil.getSeconds();
        }
        return seq;
    }


    private String getWaitWithdrawSeq(AnchorWelfareTaskComponetAttr attr,long uid,int taskLevel,int type) {
        String seq = makeKey(attr, "seq:wait_withdraw:" + uid + ":" + taskLevel + ":" + type);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())){
            seq = seq + ":" + DateUtil.getSeconds();
        }
        return seq;
    }


    private void refreshSignTaskLevel(AnchorWelfareTaskComponetAttr attr, long uid, int currentLevel, Date now) {

        if (currentLevel >= MAX_TASK_LEVEL) {
            return;
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        String finishKey = makeKey(attr, String.format(TASK_FINISH_TIME_KEY, uid, currentLevel));
        String finishValue = actRedisDao.get(groupCode, finishKey);

        String awardTimeKey = makeKey(attr, String.format(TASK_AWARD_TIME_KEY, uid, currentLevel));
        String awardValue = actRedisDao.get(groupCode, awardTimeKey);

        if (StringUtil.isNotBlank(finishValue) && StringUtil.isNotBlank(awardValue)) {
            if (finishValue.substring(0, 8).equals(awardValue.substring(0, 8))) {

                //领取和完成在同一天，则第二天定时刷新
                String refreshDate = DateUtil.format(DateUtil.add(now, 1), DateUtil.PATTERN_TYPE2);
                String refreshKey = makeKey(attr, String.format(SCHEDULE_REFRESH_TASK_KEY, refreshDate));
                actRedisDao.hset(groupCode, refreshKey, String.valueOf(uid), String.valueOf(currentLevel + 1));
            } else {
                //领取和完成不在同一天，则及时刷新
                refreshTaskLevel(attr, uid, currentLevel + 1, now);

            }
        }


    }


    private void autoWithdraw(AnchorWelfareTaskComponetAttr attr, long uid, UserRoleInfo userRoleInfo, int taskLevel, Date now) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String time = DateUtil.format(now, DateUtil.PATTERN_TYPE1);
        //自动提取日
        TaskGroupConfig taskGroupConfig = attr.getGroupConfig().stream().filter(v -> v.getLevel() == taskLevel).findFirst().orElse(null);
        if (taskGroupConfig.getCanWithdraw() == 1) {
            log.info("autoWithdraw uid:{},taskLevel{},taskGroupConfig:{}", uid, taskLevel, JsonUtil.toJson(taskGroupConfig));

            //总奖池判断
            if (!taskAwardPoolValide(attr)) {
                log.warn("autoWithdraw awardpool invalide,  actId:{}, uid:{},taskLevel:{}", attr.getActId(), uid,taskLevel);
                return;
            }

            String waitWithdrawKey = makeKey(attr, TASK_WAIT_WITHDRAW);
            int waitWithdrawNum = (int) actRedisDao.zscore(groupCode, waitWithdrawKey, String.valueOf(uid));

            //判断奖池
            String totalPoolSeq =  getTaskAwardPoolConsumeSeq(attr,uid,taskLevel);
            List<Long> addResult = actRedisDao.incrValueWithLimitSeq(groupCode, totalPoolSeq, buildAwardPoolKey(attr), waitWithdrawNum, attr.getTotalPool(), false, DateUtil.TWO_MONTH_SECONDS);
            if (addResult.get(0) <= 0) {
                log.warn("autoWithdraw awardpool incrValueWithLimitSeq error  actId:{}, uid:{},taskLevel:{},addResult:{},totalPoolSeq:{}", attr.getActId(), uid,taskLevel,JsonUtil.toJson(addResult),totalPoolSeq);
                return ;
            }

            //奖池每日消耗统计
            String poolDayConsumeSeq = getDayConsumeTaskAwardPoolSeq(attr,uid,taskLevel);
            addPoolDayConsumeStatistics(attr,poolDayConsumeSeq,now,waitWithdrawNum);

            long packageId = attr.getCrystalPackageId();
            long busiId = BusiId.MAKE_FRIEND.getValue();
            String awardName = AnchorWelfareConst.FRIEND_AWARD_NAME;
            if (userRoleInfo.getBusiness() == getTurnoverAppId(BusiId.SKILL_CARD.getValue())) {
                busiId = BusiId.SKILL_CARD.getValue();
                packageId = attr.getDiamondPackageId();
                awardName = AnchorWelfareConst.SKILL_CARD_AWARD_NAME;
            }

            //award mysql的seq为64位,避免seq过长
            String awardSeq = MD5SHAUtil.getMD5(getTaskWelfareSeq(attr,uid,taskLevel));
            String welfareTime = DateUtil.format(now, DateUtil.DEFAULT_PATTERN);
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, busiId, uid, attr.getTaskId(), waitWithdrawNum, packageId, awardSeq, 3);
            boolean sendResult = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            log.info("autoWithdraw doWelfare uid:{},taskLevel:{},waitWithdrawNum:{},sendResult:{},awardSeq:{}", uid, taskLevel, waitWithdrawNum, sendResult,awardSeq);
            if (sendResult) {
                //减少待提现
                String waitWithdrawSeq = getWaitWithdrawDecSeq(attr,uid,taskLevel);
                actRedisDao.zIncrWithSeq(groupCode, waitWithdrawSeq, waitWithdrawKey, String.valueOf(uid), (-waitWithdrawNum));

                //增加累计提现
                String totalWithdrawSeq = getTotalWithdrawAddSeq(attr,uid,taskLevel);
                String totaltWithdrawKey = makeKey(attr, TASK_TOTAL_WITHDRAW);
                actRedisDao.zIncrWithSeq(groupCode, totalWithdrawSeq, totaltWithdrawKey, String.valueOf(uid), waitWithdrawNum);

                //推送顶部toast
                long pushId = IdGenerator.generate(now.getTime(), uid);
                String message = AnchorWelfareConst.POPUP_MESSAGE.replace("{{money}}", String.valueOf(AnchorWelfareConst.formatMoney(waitWithdrawNum))).replace("{{awardName}}", awardName);
                doSendNotice(pushId, uid, AnchorWelfareConst.POPUP_TITLE, message, null);
            }
        }

    }


    private String getDayConsumeTaskAwardPoolSeq(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel) {
        String totalPoolSeq = makeKey(attr, "seq:dayConsumeAwardPoolTaskStatis:" + uid+":"+taskLevel);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            totalPoolSeq = totalPoolSeq + ":" + DateUtil.getSeconds();
        }
        return totalPoolSeq;
    }

    private String getTaskAwardPoolConsumeSeq(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel) {
        String totalPoolSeq = makeKey(attr, "seq:awardPoolTaskConsume:" + uid+":"+taskLevel);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            totalPoolSeq = totalPoolSeq + ":" + DateUtil.getSeconds();
        }
        return totalPoolSeq;
    }

    private String getTotalWithdrawAddSeq(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel) {
        String totalWithdrawSeq = makeKey(attr, "seq:total_withdraw_add:" + uid + ":" + taskLevel);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            totalWithdrawSeq = totalWithdrawSeq + ":" + DateUtil.getSeconds();
        }
        return totalWithdrawSeq;
    }
    private String getWaitWithdrawDecSeq(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel) {
        String waitWithdrawSeq = makeKey(attr, "seq:wait_withdraw_dec:" + uid + ":" + taskLevel);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            waitWithdrawSeq = waitWithdrawSeq + ":" + DateUtil.getSeconds();
        }
        return waitWithdrawSeq;
    }
    private String getTaskWelfareSeq(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel) {
        String awardSeq = makeKey(attr, "seq:task_award_auto:" + uid + ":" + taskLevel);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            awardSeq = awardSeq + ":" + DateUtil.getSeconds();
        }
        return awardSeq;

    }


    private void updateTaskAwardTime(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel, Date now) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String awardTimeKey = makeKey(attr, String.format(TASK_AWARD_TIME_KEY, uid, taskLevel));
        actRedisDao.set(groupCode, awardTimeKey, DateUtil.format(now, DateUtil.PATTERN_TYPE1));
    }


    private String getTaskAwardKey(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel) {
        return makeKey(attr, String.format(TASK_AWARD_KEY, uid, taskLevel));
    }


    /**
     * 查询用户角色
     *
     * @param request
     * @param response
     * @return
     */
    @GetMapping("/queryUserRole")
    public Response<UserRoleInfoVo> queryUserRole(HttpServletRequest request, HttpServletResponse response,
                                                  @RequestParam("actId") long actId,
                                                  @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx) {

        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(401, "you need login first!");
        }

        AnchorWelfareTaskComponetAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }

        Date now = commonService.getNow(actId);
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return Response.fail(1, "不在活动时间");
        }

        //检测是否在白名单
        UserRoleInfo userRoleInfo = getUserRoleInfo(uid, attr);
        if (userRoleInfo == null) {
            log.warn("receiveLoginAward  not in whitelist,  actId:{}, uid:{}", attr.getActId(), uid);
            return Response.fail(400, "您不符合参与条件，如有疑问请联系YY:80007！");
        }

        UserRoleInfoVo result = new UserRoleInfoVo();
        result.setBusiness(userRoleInfo.getBusiness());
        result.setNewUser(userRoleInfo.getIsNew() == 1 ? true : false);
        return Response.success(result);
    }


    /**
     * 完成登录任务，领取登录奖励
     * 新风控、同一实名只参加一次、奖池
     */
    @RequestMapping("/receiveLoginAward")
    public Response<Map<String, Object>> receiveLoginAward(HttpServletRequest request, HttpServletResponse response,
                                                           @RequestHeader("x-fts-host-name") String app,
                                                           @RequestHeader("YYHeader-y0") String hdid,
                                                           @RequestHeader(value = "YYHeader-Platform", defaultValue = "0") int clientType,
                                                           @RequestParam("actId") long actId,
                                                           @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx,
                                                           String verifyCode, String recordId, String verifyToken) {


        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(401, "you need login first!");
        }

        AnchorWelfareTaskComponetAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }

        log.info("receiveLoginAward  actId:{}, uid:{},cmptInx:{},app:{}", attr.getActId(), uid, cmptInx, app);
        Date now = commonService.getNow(actId);
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return Response.fail(1, "不在活动时间");
        }

        if (!app.equals(attr.getApp())) {
            return Response.fail(400, "app not match!");
        }

        //检测是否在白名单
        UserRoleInfo userRoleInfo = getUserRoleInfo(uid, attr);
        if (userRoleInfo == null) {
            //清除用户弹窗资格
            reportUserOrient(actId, uid, attr.getApp(), DateUtil.addSeconds(new Date(), YOMI_POPUP_EXPIRE_SECOND));
            log.warn("receiveLoginAward  not in whitelist,  actId:{}, uid:{}", attr.getActId(), uid);
            return Response.fail(SuperException.IN_BLACK_LIST, "您的账号不符合参与条件，如有疑问可联系YY：80007");
        }

        //总奖池判断
        if (!taskAwardPoolValide(attr)) {
            //清除用户弹窗资格
            reportUserOrient(actId, uid, attr.getApp(), DateUtil.addSeconds(new Date(), YOMI_POPUP_EXPIRE_SECOND));
            log.warn("receiveLoginAward awardpool invalide,  actId:{}, uid:{}", attr.getActId(), uid);
            return Response.fail(SuperException.AWARD_POOL_NOTENOUGH, "奖池为0，活动已结束！");
        }

        try {
            //同一实名只能参加一次
            if (relateUidHasJoinActivity(attr, uid)) {
                log.warn("receiveLoginAward relateUidHasJoinActivity   actId:{}, uid:{}", attr.getActId(), uid);
                reportUserOrient(actId,uid,attr.getApp(),DateUtil.addSeconds(new Date(),YOMI_POPUP_EXPIRE_SECOND));
                return Response.fail(SuperException.RELATE_UID_HAS_JOIN, "抱歉，同一实名用户的不同账号只能参与1次！");
            }

            //新风控
            zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);

            if (getLoginState(attr, uid)) {
                log.warn("receiveLoginAward  has recieve,  actId:{}, uid:{}", attr.getActId(), uid);
                return Response.fail(400, "已领取过奖励！");
            }

            long packageId = attr.getCrystalPackageId();
            long busiId = BusiId.MAKE_FRIEND.getValue();
            String awardName = AnchorWelfareConst.FRIEND_AWARD_NAME;
            if (userRoleInfo.getBusiness() == getTurnoverAppId(BusiId.SKILL_CARD.getValue())) {
                busiId = BusiId.SKILL_CARD.getValue();
                packageId = attr.getDiamondPackageId();
                awardName = AnchorWelfareConst.SKILL_CARD_AWARD_NAME;
            }
            int awardNum = attr.getLoginTaskAward().get(0).getOldUserAward();
            if (userRoleInfo.getIsNew() == 1) {
                awardNum = attr.getLoginTaskAward().get(0).getNewUserAward();
            }

            String time = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE1);



            //增加奖池消耗
            String groupCode = getRedisGroupCode(attr.getActId());
            String totalPoolSeq = getLoginAwardPoolConsumeSeq(attr,uid);
            List<Long> addResult = actRedisDao.incrValueWithLimitSeq(groupCode, totalPoolSeq, buildAwardPoolKey(attr), awardNum, attr.getTotalPool(), false, DateUtil.TWO_MONTH_SECONDS);
            if (addResult.get(0) <= 0) {
                log.warn("receiveLoginAward awardpool incrValueWithLimitSeq,  actId:{}, uid:{},addResult:{},totalPoolSeq:{}", attr.getActId(), uid,JsonUtil.toJson(addResult),totalPoolSeq);
                return Response.fail(SuperException.AWARD_POOL_NOTENOUGH, "奖池为0，活动已结束！");
            }

            //增加奖池日消耗统计
            String poolDayConsumeSeq = getLoginDayConsumePoolSeq(attr,uid);
            addPoolDayConsumeStatistics(attr,poolDayConsumeSeq,now,awardNum);

            log.info("receiveLoginAward uid:{},actId:{},totalPoolSeq:{},poolDayConsumeSeq:{}",uid,actId,totalPoolSeq,poolDayConsumeSeq);
            //award mysql表的seq长度为64，最好decode
            String awardSeq = MD5SHAUtil.getMD5(getLoginWelfareSeq(attr,uid));
            String welfareTime = DateUtil.format(commonService.getNow(attr.getActId()));
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, busiId, uid, attr.getTaskId(), awardNum, packageId, awardSeq, 2);
            boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if (!suc) {
                // 需要人工介入处理
                log.error("receiveLoginAward  doWelfare error,  actId:{}, uid:{}, seq:{}  ret:{}", attr.getActId(), uid, awardSeq, batchWelfareResult);
            }


            //添加累计提现金额
            String totaltWithdrawKey = makeKey(attr, TASK_TOTAL_WITHDRAW);
            String totalWithdrawSeq = getLoginWithdrawSeq(attr,uid);
            actRedisDao.zIncrWithSeq(groupCode, totalWithdrawSeq, totaltWithdrawKey, String.valueOf(uid), awardNum);

            //更新登录任务状态
            refreshLoginState(attr, uid, time);

            //推送顶部toast
            long pushId = IdGenerator.generate(now.getTime(), uid);
            String message = AnchorWelfareConst.POPUP_MESSAGE.replace("{{money}}", String.valueOf(AnchorWelfareConst.formatMoney(awardNum))).replace("{{awardName}}", awardName);
            doSendNotice(pushId, uid, AnchorWelfareConst.POPUP_TITLE, message, null);

            //移除白名单、只能使用真实时间
            reportUserOrient(actId, uid, attr.getApp(), DateUtil.addSeconds(new Date(), YOMI_POPUP_EXPIRE_SECOND));

            //刷新签到任务当前等级
            refreshTaskLevel(attr, uid, MIN_TASK_LEVEL, now);

            return Response.success(Map.of("packageId", packageId));

        } catch (SuperException e) {
            log.warn("receiveLoginAward warn,actId:{},uid:{},e:{},e", actId, uid, e.getMessage(), e);
            if(e.getData()!=null){
                return Response.fail(e.getCode(), e.getMessage(), Map.of("riskRecheck", e.getData()));
            }else{
                return Response.fail(e.getCode(), e.getMessage());
            }
        } catch (Exception e) {
            log.error("receiveLoginAward error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }


    }

    private void addPoolDayConsumeStatistics(AnchorWelfareTaskComponetAttr attr,String poolDayConsumeSeq,Date now,int awardNum){
        String groupCode = getRedisGroupCode(attr.getActId());
        String ymd = DateUtil.format(now,DateUtil.PATTERN_TYPE2);
        actRedisDao.hIncrByKeyWithSeq(groupCode,poolDayConsumeSeq, buildAwardPoolDayConsumeKey(attr),ymd,awardNum,DateUtil.TWO_MONTH_SECONDS);
    }

    private String buildAwardPoolDayConsumeKey(AnchorWelfareTaskComponetAttr attr) {
        return makeKey(attr, AnchorWelfareConst.AWARD_POOL_CONSUME_STATISTIC);
    }


    private String getLoginDayConsumePoolSeq(AnchorWelfareTaskComponetAttr attr,long uid) {
        String totalPoolSeq = makeKey(attr, "seq:LoginDayConsumePoolStatis:" + uid);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            totalPoolSeq = totalPoolSeq + ":" + DateUtil.getSeconds();
        }
        return totalPoolSeq;
    }


    private String getLoginAwardPoolConsumeSeq(AnchorWelfareTaskComponetAttr attr,long uid) {
        String totalPoolSeq = makeKey(attr, "seq:awardPoolLoginConsume:" + uid);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            totalPoolSeq = totalPoolSeq + ":" + DateUtil.getSeconds();
        }
        return totalPoolSeq;
    }
    private String getLoginWelfareSeq(AnchorWelfareTaskComponetAttr attr,long uid) {
        String awardSeq = makeKey(attr, "seq:login_award:" + uid);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            awardSeq = awardSeq + ":" + DateUtil.getSeconds();
        }
        return awardSeq;
    }

    private String getLoginWithdrawSeq(AnchorWelfareTaskComponetAttr attr,long uid) {
        String totalWithdrawSeq = makeKey(attr, "seq:login_task_withdraw:" + uid);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            totalWithdrawSeq = totalWithdrawSeq + ":" + DateUtil.getSeconds();
        }
        return totalWithdrawSeq;
    }


    private void refreshTaskLevel(AnchorWelfareTaskComponetAttr attr, long uid, int level, Date now) {
        log.info("refreshTaskLevel uid:{},level:{}", uid, level);

        //奖池为0，则不再开启任务
        if(!taskAwardPoolValide(attr)) {
            log.warn("refreshTaskLevel award pool not enough,uid:{},level:{}", uid, level);
            return;
        }

        if (level > MAX_TASK_LEVEL) {
            return;
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        Integer currentLevel = getTaskLevel(attr, uid);
        if (currentLevel != null && currentLevel >= level) {
            log.warn("refreshTaskLevel  level lower currentLevel,uid:{},level:{},currentLevel:{}", uid, level, currentLevel);
            return;
        }

        //只能一级级开启
        if(currentLevel != null && (level-currentLevel)>1){
            log.error("refreshTaskLevel  level illegal,uid:{},level:{},currentLevel:{}", uid, level, currentLevel);
            return;
        }
        String hashKey = makeKey(attr, TASK_CURRENT_LEVEL_KEY);
        actRedisDao.hset(groupCode, hashKey, String.valueOf(uid), String.valueOf(level));

        //记录任务开启时间
        String openKey = getTaskOpenKey(attr, uid);
        actRedisDao.hset(groupCode, openKey, String.valueOf(level), DateUtil.format(now, DateUtil.PATTERN_TYPE1));

        Map<Integer, List<AnchorWelfareTaskComponetAttr.AssistRankConfig>> assitConfigs = attr.getAssistRankConfig();
        if (assitConfigs != null && assitConfigs.containsKey(level)) {
            List<AnchorWelfareTaskComponetAttr.AssistRankConfig> inConfigList = assitConfigs.get(level);
            for (AnchorWelfareTaskComponetAttr.AssistRankConfig inConfig : inConfigList) {
                UpdateRankingRequest request = new UpdateRankingRequest();
                String updateRankSeq = getAssistRankSeq(uid,level,inConfig.getTaskType(),attr);
                request.setBusiId(BusiId.SKILL_CARD.getValue());
                request.setActId(attr.getActId());
                request.setSeq(updateRankSeq);
                request.setItemId(inConfig.getApplyItemId());
                request.setActors(Map.of(inConfig.getApplyRoleId(), String.valueOf(uid)));
                request.setCount(1);
                request.setScore(1);
                request.setTimestamp(now.getTime());
                request.setExtData(Maps.newHashMap());
                boolean rs = hdztRankingThriftClient.updateRankingWithRetry(request, UPDATE_RANK_RETRY);
                log.info("refreshTaskLevel update apply rank with uid:{} level:{} rs:{}", uid, level, rs);

            }

        }
    }

    private String getAssistRankSeq(long uid, int level,int taskType,AnchorWelfareTaskComponetAttr attr) {
        String updateRankSeq = String.format("seq:refresh_assist_rank:%d:%d:%d:" , uid, level, taskType);
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())){
            updateRankSeq = updateRankSeq + ":" + DateUtil.getSeconds();
        }
        return updateRankSeq;
    }

    private Integer getTaskLevel(AnchorWelfareTaskComponetAttr attr, long uid) {
        String hashKey = makeKey(attr, TASK_CURRENT_LEVEL_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        String value = actRedisDao.hget(groupCode, hashKey, String.valueOf(uid));
        if (StringUtil.isEmpty(value)) {
            return null;
        } else {
            return Integer.parseInt(value);
        }
    }


    private boolean getLoginState(AnchorWelfareTaskComponetAttr attr, long uid) {
        String hashKey = makeKey(attr, LOGIN_TASK_FINISH_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        String value = actRedisDao.hget(groupCode, hashKey, String.valueOf(uid));
        if (StringUtil.isEmpty(value)) {
            return false;
        } else {
            return true;
        }
    }


    private void refreshLoginState(AnchorWelfareTaskComponetAttr attr, long uid, String time) {
        String hashKey = makeKey(attr, LOGIN_TASK_FINISH_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        actRedisDao.hset(groupCode, hashKey, String.valueOf(uid), time);
    }


    private UserRoleInfo getUserRoleInfo(long uid, AnchorWelfareTaskComponetAttr attr) {
        boolean inBlacklist = whitelistComponent.inWhitelist(attr.getActId(), attr.getBlacklistCmptInx(), String.valueOf(uid));
        if (inBlacklist) {
            log.warn("getUserRoleInfo uid in blacklist uid:{}", uid);
            return null;
        }

        String value = whitelistComponent.getConfigValue(attr.getActId(), attr.getWhitelistCmptInx(), String.valueOf(uid));
        if (StringUtils.isNotEmpty(value)) {
            return JSONUtils.parseObject(value, UserRoleInfo.class);
        } else {
            return null;
        }
    }


    /**
     * 查询入口，未用到
     *
     * @param actId
     * @param noticeSource
     * @param uid
     * @return
     */
    @RequestMapping("/queryWelfareNoticeInfo")
    public Response<WelfareNoticeInfo> taskList(@RequestParam("actId") long actId,
                                                int noticeSource, long uid) {


        WelfareNoticeInfo welfareNoticeInfo = queryWelfareNoticeInfo(actId, noticeSource, uid);
        return Response.success(welfareNoticeInfo);

    }

    /**
     * @param noticeSource       入口请求类型  1-pc上座 2-pc下座 3-app气泡
     * @param uid                uid
     */
    public WelfareNoticeInfo queryWelfareNoticeInfo(long actId, int noticeSource, long uid) {
        WelfareNoticeInfo WelfareNoticeInfo = new WelfareNoticeInfo();
        AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
        if (!actInfoService.inActTime(actId)) {
            WelfareNoticeInfo.setQualification(false);
            return WelfareNoticeInfo;
        }
        UserRoleInfo userRoleInfo = getUserRoleInfo(uid, attr);
        boolean qualification = userRoleInfo == null ? false : true;
        WelfareNoticeInfo.setQualification(qualification);
        if (!qualification) {
            return WelfareNoticeInfo;
        }
        WelfareNoticeInfo.setNew(userRoleInfo != null && userRoleInfo.getIsNew() != null && userRoleInfo.getIsNew() == 1);
        WelfareNoticeInfo.setBusiness(userRoleInfo.getBusiness());


        boolean completeAllTask = hasCompleteAllTask(attr, uid, userRoleInfo);
        WelfareNoticeInfo.setCompleteAllTask(completeAllTask);

        //有活动资格，且没有完成全部任务
        if (qualification && (!completeAllTask)) {
            switch (noticeSource) {
                case 1:
                    getPcUpSeatNoticeInfo(attr, uid, userRoleInfo, WelfareNoticeInfo);
                    break;
                case 2:
                    getPcDownSeatNoticeInfo(attr, uid, userRoleInfo, WelfareNoticeInfo);
                    break;
                case 3:
                    getAppNoticeInfo(attr, uid, userRoleInfo, WelfareNoticeInfo);
                    break;
                default:
            }

        }

        return WelfareNoticeInfo;
    }


    private void getAppNoticeInfo(AnchorWelfareTaskComponetAttr attr, long uid, UserRoleInfo userRoleInfo, WelfareNoticeInfo WelfareNoticeInfo) {

        String rollText = StringUtils.EMPTY;
        String text = StringUtils.EMPTY;
        if(relateUidHasJoinActivity(attr,uid) ){
            log.info("getAppNoticeInfo relateUid has join activity uid:{}",uid);
            WelfareNoticeInfo.setRollText(rollText);
            WelfareNoticeInfo.setText(text);
            return;
        }

        TaskTipInfo taskTipInfo = new TaskTipInfo();
        if (checkNeverFinish(attr, uid, userRoleInfo, NEVER_FINISH_DAY, taskTipInfo)) {
            text = AnchorWelfareConst.APP_TEXT_3.replace("{{money}}", AnchorWelfareConst.formatMoney(taskTipInfo.getAwardNum()));
        } else if (canAutoWithdraw(attr, uid)) {
            text = AnchorWelfareConst.APP_TEXT_2;
        } else if (  appFirstReqNotice(attr, uid)) {
            text = AnchorWelfareConst.APP_TEXT_1;
            //首次气泡提醒后，要保持红点，返回key给客户端去重显示即可
            String key = makeKey(attr, APP_FIRST_REQ_NOTICE);
            WelfareNoticeInfo.setDupKey(key);
        }

        WelfareNoticeInfo.setRollText(rollText);
        WelfareNoticeInfo.setText(text);
    }

    private boolean appFirstReqNotice(AnchorWelfareTaskComponetAttr attr, long uid) {
        return true;
    }

    private void getPcDownSeatNoticeInfo(AnchorWelfareTaskComponetAttr attr, long uid, UserRoleInfo userRoleInfo, WelfareNoticeInfo WelfareNoticeInfo) {

        String rollText = StringUtils.EMPTY;
        String text = StringUtils.EMPTY;
        if(relateUidHasJoinActivity(attr,uid) ){
            log.info("getPcDownSeatNoticeInfo relateUid has join activity uid:{}",uid);
            WelfareNoticeInfo.setRollText(rollText);
            WelfareNoticeInfo.setText(text);
            return;
        }

        TaskTipInfo taskTipInfo = new TaskTipInfo();
        boolean isNew = userRoleInfo.getIsNew() == 1 ? true : false;
        if (checkNeverFinish(attr, uid, userRoleInfo, NEVER_FINISH_DAY, taskTipInfo)) {
            text = AnchorWelfareConst.PC_DOWN_SEAT_TEXT_5.replace("{{money}}", AnchorWelfareConst.formatMoney(taskTipInfo.getAwardNum()));
        } else if (canAutoWithdraw(attr, uid)) {
            text = AnchorWelfareConst.PC_DOWN_SEAT_TEXT_4;
        } else if (currentTaskNeverFinish(attr, uid, userRoleInfo, taskTipInfo)) {
            text = AnchorWelfareConst.PC_DOWN_SEAT_TEXT_3.replace("{{money}}", AnchorWelfareConst.formatMoney(taskTipInfo.getAwardNum()));
        } else if (checkNeverLogin(attr, uid, NEVER_LOGIN_DAY)) {
            text = AnchorWelfareConst.PC_DOWN_SEAT_TEXT_2;
        } else if (!getLoginState(attr, uid)) {
            LoginTaskAward loginTaskAward = attr.getLoginTaskAward().get(0);
            int awardNum = isNew ? loginTaskAward.getNewUserAward() : loginTaskAward.getOldUserAward();
            text = AnchorWelfareConst.PC_DOWN_SEAT_TEXT_1.replace("{{money}}", AnchorWelfareConst.formatMoney(awardNum));
        }
        WelfareNoticeInfo.setRollText(rollText);
        WelfareNoticeInfo.setText(text);
    }


    private void getPcUpSeatNoticeInfo(AnchorWelfareTaskComponetAttr attr, long uid, UserRoleInfo userRoleInfo, WelfareNoticeInfo WelfareNoticeInfo) {

        String rollText = StringUtils.EMPTY;
        String text = StringUtils.EMPTY;
        if(relateUidHasJoinActivity(attr,uid) ){
            log.info("getPcUpSeatNoticeInfo relateUid has join activity uid:{}",uid);
            WelfareNoticeInfo.setRollText(rollText);
            WelfareNoticeInfo.setText(text);
            return;
        }

        TaskTipInfo taskTipInfo = new TaskTipInfo();
        boolean isNew = userRoleInfo.getIsNew() == 1 ? true : false;
        if (!getLoginState(attr, uid)) {
            LoginTaskAward loginTaskAward = attr.getLoginTaskAward().get(0);
            int awardNum = isNew ? loginTaskAward.getNewUserAward() : loginTaskAward.getOldUserAward();
            rollText = AnchorWelfareConst.PC_UP_SEAT_ROLL_TEXT_1.replace("{{money}}", AnchorWelfareConst.formatMoney(awardNum));
        } else if (currentTaskNeverFinish(attr, uid, userRoleInfo, taskTipInfo)) {
            rollText = AnchorWelfareConst.PC_UP_SEAT_ROLL_TEXT_2.replace("{{money}}", AnchorWelfareConst.formatMoney(taskTipInfo.getAwardNum()));
        }

        if (checkNeverFinish(attr, uid, userRoleInfo, NEVER_FINISH_DAY, taskTipInfo)) {
            text = AnchorWelfareConst.PC_UP_SEAT_TEXT_3.replace("{{money}}", AnchorWelfareConst.formatMoney(taskTipInfo.getAwardNum()));
        } else if (canAutoWithdraw(attr, uid)) {
            text = AnchorWelfareConst.PC_UP_SEAT_TEXT_2;
        } else if (checkNeverLogin(attr, uid, NEVER_LOGIN_DAY)) {
            text = AnchorWelfareConst.PC_UP_SEAT_TEXT_1;
        }

        WelfareNoticeInfo.setRollText(rollText);
        WelfareNoticeInfo.setText(text);
    }


    /**
     * 当前任务没有全部完成
     * @return
     */
    private boolean currentTaskNeverFinish(AnchorWelfareTaskComponetAttr attr, long uid, UserRoleInfo userRoleInfo, TaskTipInfo taskTipInfo) {
        boolean needTip = false;
        long awrdNum = 0L;
        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel == null) {
            return needTip;
        }

        boolean isNew = userRoleInfo.getIsNew() == 1 ? true : false;
        List<TaskInfoConfig> taskConfig = attr.getTaskConfig().get(taskLevel);
        String groupCode = getRedisGroupCode(attr.getActId());
        String awardKey = getTaskAwardKey(attr, uid, taskLevel);
        long finishSize = actRedisDao.hsize(groupCode, awardKey);
        if(taskConfig.size()>finishSize){
            needTip = true;
            for(TaskInfoConfig taskInfoConfig: taskConfig){
                int hasAward = Convert.toInt(actRedisDao.hget(groupCode, awardKey, String.valueOf(taskInfoConfig.getTaskType())));
                if(hasAward<=0){
                    awrdNum = awrdNum + (isNew ? taskInfoConfig.getNewUserAward() : taskInfoConfig.getOldUserAward());
                }
            }
        }

        taskTipInfo.setNeedTips(needTip);
        taskTipInfo.setAwardNum(awrdNum);
        return needTip;
    }


    /**
     * 判断是否进入过pc活动页
     *
     * @param
     * @return
     */
    public boolean pcInsertActivity(long actId, long uid) {
        AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
        if (!actInfoService.inActTime(actId)) {
            return false;
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        String pcFirstKey = makeKey(attr, PC_FIRST_INSERT_ACTIVITY);
        String value = actRedisDao.hget(groupCode, pcFirstKey, String.valueOf(uid));
        log.info("pcInsertActivity actId:{},uid:{},value:{}", actId, uid, value);
        return StringUtil.isNotBlank(value) ? true : false;
    }

    private boolean checkNeverLogin(AnchorWelfareTaskComponetAttr attr, long uid, int days) {
        Date now = commonService.getNow(attr.getActId());

        if (!getLoginState(attr, uid)) {
            // PC端打开页面时间未处理
            String groupCode = getRedisGroupCode(attr.getActId());
            String pcFirstKey = makeKey(attr, PC_FIRST_INSERT_ACTIVITY);
            String value = actRedisDao.hget(groupCode, pcFirstKey, String.valueOf(uid));
            Date openDate = null;
            if (StringUtil.isNotBlank(value)) {
                try {
                    openDate = DateUtils.parseDate(value, DateUtil.PATTERN_TYPE1);
                } catch (ParseException e) {
                    log.error("checkNeverLogin parseDate error");
                }
            }
            if (openDate != null) {
                long diff = DateUtil.getDiffDays(openDate, now);
                if (diff >= days) {
                    return true;
                }
            }

            return false;
        } else {
            return false;
        }
    }

    /**
     * 可提取日，若完成全部任务，则不再提示
     * @param attr
     * @param uid
     * @return
     */
    private boolean canAutoWithdraw(AnchorWelfareTaskComponetAttr attr, long uid) {
        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel == null) {
            return false;
        }

        boolean needTip = false;
        TaskGroupConfig taskGroupConfig = attr.getGroupConfig().stream().filter(v -> v.getLevel() == taskLevel).findFirst().orElse(null);
        if (taskGroupConfig != null && taskGroupConfig.getCanWithdraw() == 1) {
            List<TaskInfoConfig> taskConfig = attr.getTaskConfig().get(taskLevel);
            String groupCode = getRedisGroupCode(attr.getActId());
            String awardKey = getTaskAwardKey(attr, uid, taskLevel);
            long finishSize = actRedisDao.hsize(groupCode, awardKey);
            if(taskConfig.size()>finishSize){
                needTip = true;
            }
        }
        return needTip;
    }


    private boolean checkNeverFinish(AnchorWelfareTaskComponetAttr attr, long uid, UserRoleInfo userRoleInfo, int days, TaskTipInfo taskTipInfo) {
        boolean needTips = false;
        long awardNum = 0L;

        Date now = commonService.getNow(attr.getActId());
        boolean isNew = userRoleInfo.getIsNew() == 1 ? true : false;
        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel == null) {
            return needTips;
        }
        List<TaskInfoConfig> taskConfig = attr.getTaskConfig().get(taskLevel);
        String groupCode = getRedisGroupCode(attr.getActId());
        String awardKey = getTaskAwardKey(attr, uid, taskLevel);
        long finishSize = actRedisDao.hsize(groupCode, awardKey);
        if (finishSize == taskConfig.size()) {
            return needTips;
        }

        Date openDate = getTaskOpenDate(attr, uid, groupCode, taskLevel);
        Date latestAwardTime = getLatestTaskAwardTime(attr, uid, groupCode, taskLevel);
        if (latestAwardTime != null) {
            long diff = DateUtil.getDiffDays(latestAwardTime, now);
            needTips = (diff >= days) ? true : false;
        } else if (openDate != null) {
            long diff = DateUtil.getDiffDays(openDate, now);
            needTips = (diff >= days) ? true : false;
        }

        if (needTips) {
            String totaltWithdrawKey = makeKey(attr, TASK_TOTAL_WITHDRAW);
            long totaltWithdraw = actRedisDao.zscore(groupCode, totaltWithdrawKey, String.valueOf(uid));
            long totalAward = 0L;
            Map<Integer, List<TaskInfoConfig>> taskConfigMap = attr.getTaskConfig();
            if (isNew) {
                totalAward = taskConfigMap.values().stream().mapToLong(v -> v.stream().mapToLong(TaskInfoConfig::getNewUserAward).sum()).sum();
                totalAward = totalAward + attr.getLoginTaskAward().get(0).getNewUserAward();
            } else {
                totalAward = taskConfigMap.values().stream().mapToLong(v -> v.stream().mapToLong(TaskInfoConfig::getOldUserAward).sum()).sum();
                totalAward = totalAward + attr.getLoginTaskAward().get(0).getOldUserAward();
            }



            awardNum = Math.max(totalAward - totaltWithdraw, 0);
        }

        taskTipInfo.setNeedTips(needTips);
        taskTipInfo.setAwardNum(awardNum);
        return needTips;
    }


    private Date getLatestTaskAwardTime(AnchorWelfareTaskComponetAttr attr, long uid, String groupCode, int taskLevel) {
        Date openDate = null;
        String awardTimeKey = makeKey(attr, String.format(TASK_AWARD_TIME_KEY, uid, taskLevel));
        String value = actRedisDao.get(groupCode, awardTimeKey);
        if (StringUtil.isNotBlank(value)) {
            try {
                openDate = DateUtils.parseDate(value, DateUtil.PATTERN_TYPE1);
            } catch (ParseException e) {
                log.error("getLatestTaskAwardTime parseDate error");
            }
        }
        return openDate;
    }


    private Date getTaskOpenDate(AnchorWelfareTaskComponetAttr attr, long uid, String groupCode, int taskLevel) {
        Date openDate = null;
        String openKey = getTaskOpenKey(attr, uid);
        String value = actRedisDao.hget(groupCode, openKey, String.valueOf(taskLevel));
        if (StringUtil.isNotBlank(value)) {
            try {
                openDate = DateUtils.parseDate(value, DateUtil.PATTERN_TYPE1);
            } catch (ParseException e) {
                log.error("getTaskOpenDate parseDate error");
            }
        }
        return openDate;
    }


    private String getTaskOpenKey(AnchorWelfareTaskComponetAttr attr, long uid) {
        return makeKey(attr, String.format(TASK_OPEN_TIME_KEY, uid));

    }

    private boolean hasCompleteAllTask(AnchorWelfareTaskComponetAttr attr, long uid, UserRoleInfo userRoleInfo) {
        if (userRoleInfo == null) {
            return false;
        }

        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel == null || taskLevel.intValue() != MAX_TASK_LEVEL) {
            return false;
        }

        List<TaskInfoConfig> taskList = attr.getTaskConfig().get(MAX_TASK_LEVEL);
        String groupCode = getRedisGroupCode(attr.getActId());
        String awardKey = getTaskAwardKey(attr, uid, MAX_TASK_LEVEL);
        long finishSize = actRedisDao.hsize(groupCode, awardKey);

        return finishSize == taskList.size() ? true : false;
    }

    private boolean taskHasFinish(AnchorWelfareTaskComponetAttr attr, long uid, int taskLevel, int type) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String awardKey = getTaskAwardKey(attr, uid, taskLevel);
        int hasAward = Convert.toInt(actRedisDao.hget(groupCode, awardKey, String.valueOf(type)));
        return hasAward >= 1 ? true : false;

    }

    public void doSendNotice(Long pushId, long uid, String title, String message, String link) {
        Map<String, String> extend = Maps.newHashMapWithExpectedSize(2);
        extend.put("ignoreMainPopup","1");

        YoPopupMessage yoMessage = YoPopupMessage.builder()
                .app("yomi")
                //默认android,-1 安卓+ios
                .platform(-1)
                .title(title)
                .content(Base64Utils.encodeToString(message.getBytes()))
                .innerContent(message)
                .icon("https://gamebaby.bs2dl.yy.com/adminweb/ezzxpsxnzcfggprmtdtwimcttrac2wft.png")
                .extend(extend)
                .link(link).build();
        zhuiWanPrizeIssueServiceClient.sendPopupMessage(String.valueOf(pushId), uid, yoMessage);

    }

    private boolean relateUidHasJoinActivity(AnchorWelfareTaskComponetAttr attr, long uid) {

        //测试环境或者灰度状态忽略同一实名检查名单
        if(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())){
            if(StringUtil.isNotBlank(attr.getIgnoreRelateUidb()) && attr.getIgnoreRelateUidb().contains(String.valueOf(uid))){
                return false;
            }
        }

        boolean blackList = whitelistComponent.inWhitelist(attr.getActId(), attr.getRelateUidblacklistCmptInx(), String.valueOf(uid));
        if (blackList) {
            return true;
        }

        RelateUidsVo relateUidsVo = userinfoThriftClient.getRelateUids(uid,2);
        if (relateUidsVo == null || relateUidsVo.getRescode() != 0 || CollectionUtils.isEmpty(relateUidsVo.getMobRelUids())) {
            return false;
        }

        Set<Long> mobUids = relateUidsVo.getMobRelUids();
        mobUids.remove(uid);

        if (mobUids.size() > 0) {
            List<String> uidString = mobUids.stream().map(v -> String.valueOf(v)).collect(Collectors.toList());
            String hashKey = makeKey(attr, LOGIN_TASK_FINISH_KEY);
            String groupCode = getRedisGroupCode(attr.getActId());
            List<Object> objects = actRedisDao.hmGet(groupCode, hashKey, MyListUtils.toObjectList(uidString));
            for (int i = 0; i < uidString.size(); i++) {
                if (objects.get(i) != null) {
                    log.info("relateUidHasJoinActivity has join ,uid:{},joinUid:{}",uid,uidString.get(i));
                    whitelistComponent.ignoreAdd(attr.getActId(), attr.getRelateUidblacklistCmptInx(), String.valueOf(uid), uidString.get(i));
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 用于标识哪个业务触发
     */
    private int getTurnoverAppId(int busiId) {
        BusiId value = BusiId.findByValue(busiId);
        int appId = 0;
        switch (value) {
            case MAKE_FRIEND:
                appId = 2;
                break;
            case SKILL_CARD:
                appId = 34;
                break;
            default:
                break;
        }
        return appId;

    }


    /**
     * 上报到追玩弹窗白名单
     */
    private void reportUserOrient(long actId, long uid, String app, final Date endTime) {
        String key = "act:" + actId + ":" + app;
        zhuiWanPrizeIssueServiceClient.reportUserOrient(uid, key, endTime);
    }


    /**
     * 定时刷新签到任务等级
     */
    @Scheduled(cron = "24 * * * * ?")
    @NeedRecycle(author = "chengaojie", notRecycle = true)
    public void refreshSignTaskLevel() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }

            AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            Date now = commonService.getNow(actId);
            String groupCode = getRedisGroupCode(actId);

            //每日只执行一次任务开启
            String execKey = makeKey(attr, "exec_sign_task_level:" + DateUtil.format(now, DateUtil.PATTERN_TYPE2));
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                return;
            }

            String hashKey = makeKey(attr, String.format(SCHEDULE_REFRESH_TASK_KEY, DateUtil.format(now, DateUtil.PATTERN_TYPE2)));
            long size = actRedisDao.hsize(groupCode, hashKey);
            if (size > 0) {
                log.info("refreshSignTaskLevel actId:{},size:{}",actId,size);
                try (Cursor<Map.Entry<String, String>> cursor = actRedisDao.getRedisTemplate(groupCode)
                        .<String, String>opsForHash()
                        .scan(hashKey, ScanOptions.scanOptions().count(size).match("*").build())) {
                    while (cursor.hasNext()) {
                        Map.Entry<String, String> entry = cursor.next();
                        long uid = Long.parseLong(entry.getKey());
                        int level = Integer.parseInt(entry.getValue());
                        refreshTaskLevel(attr, uid, level, now);
                    }
                }
            }

            //测试环境或者线上灰度环境，执行后把列表删除，避免数据混杂
            if(!SysEvHelper.isDeploy() || commonService.isGrey(actId)) {
                log.info("refreshSignTaskLevel del redis actId:{},size:{},hashKey:{}",actId,size,hashKey);
                actRedisDao.del(groupCode,hashKey);
            }

        }
    }



    /**
     * 手动清除登录任务
     *
     * @param actId
     * @return
     */
    @GetMapping("/manualClearLoginTask")
    public Response manualClearLoginTask(@RequestParam("actId") long actId, long uid) {

        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(1,"not accessible!");
        }

        AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(1, "找不到活动配置");
        }

        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel != null && taskLevel > 1) {
            return Response.fail(1, "已在做签到任务，登录信息不能清除");
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        String progressKey = getTaskProgressKey(attr, uid, MIN_TASK_LEVEL);
        long size = actRedisDao.hsize(groupCode, progressKey);
        if (size > 0) {
            return Response.fail(1, "已在做签到任务，登录信息不能清除");
        }

        String pcFirstKey = makeKey(attr, PC_FIRST_INSERT_ACTIVITY);
        actRedisDao.hdel(groupCode, pcFirstKey, String.valueOf(uid));

        String hashKey = makeKey(attr, LOGIN_TASK_FINISH_KEY);
        actRedisDao.hdel(groupCode, hashKey, String.valueOf(uid));

        hashKey = makeKey(attr, TASK_CURRENT_LEVEL_KEY);
        actRedisDao.hdel(groupCode, hashKey, String.valueOf(uid));

        String openKey = getTaskOpenKey(attr, uid);
        actRedisDao.hdel(groupCode, openKey, String.valueOf(MIN_TASK_LEVEL));

        reportUserOrient(actId, uid, attr.getApp(), DateUtil.addSeconds(new Date(), YOMI_POPUP_EXPIRE_SECOND));

        return Response.ok();

    }


    /**
     * 手动清除普通任务
     *
     * @param actId
     * @param uid
     * @return
     */
    @GetMapping("/manualClearSignTask")
    public Response manualClearSignTask(@RequestParam("actId") long actId, long uid, int level) {

        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(1, "not accessible!");
        }


        AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(1, "找不到活动配置");
        }

        Integer taskLevel = getTaskLevel(attr, uid);
        if (taskLevel != level) {
            return Response.fail(1, "只能清除当前等级任务");
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        String progressKey = getTaskProgressKey(attr, uid, taskLevel);
        actRedisDao.del(groupCode, progressKey);

        String awardKey = getTaskAwardKey(attr, uid, taskLevel);
        actRedisDao.del(groupCode, awardKey);

        String finishKey = makeKey(attr, String.format(TASK_FINISH_TIME_KEY, uid, taskLevel));
        actRedisDao.del(groupCode, finishKey);

        String awardTimeKey = makeKey(attr, String.format(TASK_AWARD_TIME_KEY, uid, taskLevel));
        actRedisDao.del(groupCode, awardTimeKey);

        String refreshKey = makeKey(attr, String.format(AUTO_REFRESH_SIGN_STATE, uid, level));
        actRedisDao.del(groupCode, refreshKey);

        return Response.ok();

    }


    @GetMapping("/manualClearAllTask")
    public Response manualClearAllTask(@RequestParam("actId") long actId, long uid) {

        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(1, "not accessible!");
        }


        AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(1, "找不到活动配置");
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        String totaltWithdrawKey = makeKey(attr, TASK_TOTAL_WITHDRAW);
        actRedisDao.zDel(groupCode, totaltWithdrawKey, String.valueOf(uid));

        String waitWithdrawKey = makeKey(attr, TASK_WAIT_WITHDRAW);
        actRedisDao.zDel(groupCode, waitWithdrawKey, String.valueOf(uid));

        String hashKey = makeKey(attr, LOGIN_TASK_FINISH_KEY);
        actRedisDao.hdel(groupCode, hashKey, String.valueOf(uid));

        hashKey = makeKey(attr, TASK_CURRENT_LEVEL_KEY);
        actRedisDao.hdel(groupCode, hashKey, String.valueOf(uid));

        String key = makeKey(attr, APP_FIRST_REQ_NOTICE);
        actRedisDao.hdel(groupCode, key, String.valueOf(uid));

        String pcFirstKey = makeKey(attr, PC_FIRST_INSERT_ACTIVITY);
        actRedisDao.hdel(groupCode, pcFirstKey, String.valueOf(uid));

        for (int i = 1; i <= 7; i++) {
            String progressKey = getTaskProgressKey(attr, uid, i);
            actRedisDao.del(groupCode, progressKey);

            String awardKey = getTaskAwardKey(attr, uid, i);
            actRedisDao.del(groupCode, awardKey);

            String finishKey = makeKey(attr, String.format(TASK_FINISH_TIME_KEY, uid, i));
            actRedisDao.del(groupCode, finishKey);

            String awardTimeKey = makeKey(attr, String.format(TASK_AWARD_TIME_KEY, uid, i));
            actRedisDao.del(groupCode, awardTimeKey);

            String refreshKey = makeKey(attr, String.format(AUTO_REFRESH_SIGN_STATE, uid, i));
            actRedisDao.del(groupCode, refreshKey);

        }


        String openKey = getTaskOpenKey(attr, uid);
        actRedisDao.del(groupCode, openKey);

        reportUserOrient(actId, uid, attr.getApp(), DateUtil.addSeconds(new Date(), YOMI_POPUP_EXPIRE_SECOND));

        return Response.ok();
    }




    /**
     * 手动刷新白名单，活动未开始前手动刷新
     *
     * @param actId
     * @return
     */
    @RequestMapping("/manualRefreshWhiteUser")
    public Response manualRefreshWhiteUser(HttpServletRequest request, HttpServletResponse response,@RequestParam("actId") long actId) {

        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(1, "未登录");
        }

        if(SysEvHelper.isDeploy()){
            if(uid!=50074524L){
                return Response.fail(1, "生成环境没有权限进行该操作");
            }
        }

        AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(1, "找不到活动配置");
        }

        Date now = commonService.getNow(actId);
        if(SysEvHelper.isDeploy()){
            log.info("manualRefreshWhiteUser uid:{},actId:{}",uid,actId);
        }
        refreshWhiteUser(actId, 2, DateUtil.format(now, DateUtil.PATTERN_TYPE2), attr, now);
        refreshWhiteUser(actId, 34, DateUtil.format(now, DateUtil.PATTERN_TYPE2), attr, now);

        return Response.ok();
    }





    @Scheduled(cron = "6 0 5 * * ?")
    @NeedRecycle(author = "chengaojie", notRecycle = true)
    public void refreshWhiteUser() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }

            Date now = commonService.getNow(actId);
            AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            String ymd = DateUtil.format(now,DateUtil.PATTERN_TYPE2);
            String groupCode = getRedisGroupCode(attr.getActId());
            String execKey = makeKey(attr, "execRefreshWhiteUser:"+ymd);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("refreshWhiteUser has exec execKey:{}",execKey);
                return;
            }
            log.info("refreshWhiteUser actId:{}",actId);
            refreshWhiteUser(actId, 2, DateUtil.format(now, DateUtil.PATTERN_TYPE2), attr, now);
            refreshWhiteUser(actId, 34, DateUtil.format(now, DateUtil.PATTERN_TYPE2), attr, now);
        }
    }

    private void refreshWhiteUser(long actId, int appid, String endDate, AnchorWelfareTaskComponetAttr attr,Date now) {

        int valideLiveDays = attr.getValideLiveDays();
        long cmptInx = attr.getWhitelistCmptInx();
        String startDate = attr.getWhiteUserStartTime();

        List<Long> uidList = anchorPrizeStatServiceClient.queryAnchorExclusivePrizeUids(appid);

        if (CollectionUtils.isNotEmpty(uidList)) {
            log.info("refreshWhiteUser actId:{},appid:{},uidList size:{}",actId,appid,uidList.size());
            List<Long> existUidList = whitelistComponent.getMemberList(actId, cmptInx);
            List<Long> minus = uidList.stream().filter(item -> !existUidList.contains(item)).collect(Collectors.toList());
            log.info("refreshWhiteUser actId:{},appid:{},minus size:{}",actId,appid,minus.size());
            Map<Long, Integer> validLiveDays = null;
            if (appid == 2) {
                validLiveDays = ftsLiveHelperServiceThriftClient.batchGetCompereValidLiveDays(startDate, endDate, minus, valideLiveDays);
            } else {
                validLiveDays = batchQueryAnchorLiveDay(startDate, endDate, minus, valideLiveDays);
            }

            if (validLiveDays != null && validLiveDays.size() > 0) {
                log.info("refreshWhiteUser actId:{},appid:{},validLiveDays size:{}",actId,appid,validLiveDays.size());

                long addNewUser = 0L;
                long addOldUser = 0L;
                Map<Long, LoginRecord.UidLoginRecordVo> loginInfo = getZhuiwanLoginInfo((Lists.newArrayList(validLiveDays.keySet())));
                for (long uid : validLiveDays.keySet()) {
                    if (SysEvHelper.isDeploy()) {
                        if (commonService.isGrey(actId) && !hdztRankingThriftClient.checkWhiteList(actId, RoleType.USER, String.valueOf(uid))) {
                            log.info("refreshWhiteUser grey not in whitelist uid:{},actId:{}",uid,actId);
                            continue;
                        }
                    }
                    UserRoleInfo userRoleInfo = new UserRoleInfo();
                    userRoleInfo.setBusiness(appid);
                    if (loginInfo != null && loginInfo.containsKey(uid)) {
                        addOldUser++;
                        userRoleInfo.setIsNew(0);
                    } else {
                        addNewUser++;
                        userRoleInfo.setIsNew(1);
                    }
                    whitelistComponent.ignoreAdd(actId, cmptInx, String.valueOf(uid), JsonUtil.toJson(userRoleInfo));
                }


                log.info("refreshWhiteUser actId:{},appid:{},addNewUser:{},addOldUser:{}",actId,appid,addNewUser,addOldUser);
                //更新白名单新增用户
                String groupCode = getRedisGroupCode(attr.getActId());
                String dayStatisKey = whiteuerDayAddStatisKey(attr,now);
                String totalStatisKey = makeKey(attr,AnchorWelfareConst.WHITE_USER_TOTAL_STATISTIC);

                actRedisDao.hIncrByKey(groupCode,dayStatisKey,String.valueOf(appid+"_new"),addNewUser);
                actRedisDao.hIncrByKey(groupCode,dayStatisKey,String.valueOf(appid+"_old"),addOldUser);

                actRedisDao.hIncrByKey(groupCode,totalStatisKey,String.valueOf(appid+"_new"),addNewUser);
                actRedisDao.hIncrByKey(groupCode,totalStatisKey,String.valueOf(appid+"_old"),addOldUser);
            }

        }
    }

    private String whiteuerDayAddStatisKey(AnchorWelfareTaskComponetAttr attr,Date now) {
        String ymd = DateUtil.format(now,DateUtil.PATTERN_TYPE2);
       return makeKey(attr,String.format(AnchorWelfareConst.WHITE_USER_DAY_ADD_STATISTIC,ymd));
    }


    private Map<Long, LoginRecord.UidLoginRecordVo> getZhuiwanLoginInfo(List<Long> uids) {
        LoginRecord.BatchLoginRecordRsp rsp = null;
        try {
            LoginRecord.BatchLoginRecordReq req = LoginRecord.BatchLoginRecordReq.newBuilder().setApp(ZhuiyaPbCommon.App.APP_YOMI).addAllUid(uids).build();
            rsp = loginClient.batchQueryLoginRecord(req);
        } catch (Exception e) {
            log.error("getZhuiwanLoginInfo fail:", e);
        }

        if (rsp == null || rsp.getCode() != 0) {
            return Collections.emptyMap();
        }

        return rsp.getLoginRecordMap();
    }

    private Map<Long, Integer> batchQueryAnchorLiveDay(String startDate, String endDate, List<Long> uids, int valideLiveDays) {
        if (org.springframework.util.CollectionUtils.isEmpty(uids)) {
            return null;
        }
        Map<Long, Integer> result = new HashMap<>(uids.size());
        List<List<Long>> partition = Lists.partition(uids, 500);

        for (List<Long> partitionList : partition) {
            Map<Long, Integer> liveDays = queryAnchorLiveDay(startDate, endDate, partitionList, valideLiveDays);
            if (!org.springframework.util.CollectionUtils.isEmpty(liveDays)) {
                result.putAll(liveDays);
            }
        }

        return result;
    }


    private Map<Long, Integer> queryAnchorLiveDay(String startDate, String endDate, List<Long> uids, int valideLiveDays) {
        Skillcard.QueryAnchorLiveDayRsp resp = null;
        try {
            Skillcard.QueryAnchorLiveDayReq req = Skillcard.QueryAnchorLiveDayReq.newBuilder().setStartDay(startDate)
                    .setEndDay(endDate).setDailyLiveTime(3600).addAllUid(uids).build();
            resp = skillCardProvider.queryAnchorLiveDay(req);
        } catch (Exception e) {
            log.error("queryAnchorLiveDay fail:", e);
        }

        if (resp == null || resp.getCode() != 0) {
            return Collections.emptyMap();
        }

        Map<Long, Integer> result = new HashMap<>(resp.getAnchorLiveDayList().size());
        for (Skillcard.AnchorLiveDay item : resp.getAnchorLiveDayList()) {
            if (item.getDay() >= valideLiveDays) {
                result.put(item.getUid(), item.getDay());
            }
        }

        return result;
    }


    /**
     * 查询奖池剩余 ，小于5元清零
     */
    @GetMapping("/queryAwardPool")
    public Response<Map<String, Object>> queryAwardPool(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(name = "actId") long actId,
                                                        @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx) {
        AnchorWelfareTaskComponetAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        Map<String, Object> result = Maps.newHashMap();
        String redisCode = getRedisGroupCode(actId);

        long send = Convert.toLong(actRedisDao.get(redisCode, buildAwardPoolKey(attr)), 0);
        long left = Math.max(attr.getTotalPool() - send, 0);
        left = left >= attr.getMinAwardPool() ? left : 0;

        result.put("total", attr.getTotalPool());
        result.put("send", send);
        result.put("left", left);

        return Response.success(result);
    }

    /**
     * 查询是否签约交友或者聊天室
     * @return
     */
    @RequestMapping("/querySign")
    public Response<Boolean> querySign(HttpServletRequest request, HttpServletResponse response,
                                       @RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(1, "未在活动时间内");
        }

        AnchorWelfareTaskComponetAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        UserRoleInfo userRoleInfo = getUserRoleInfo(uid,attr);
        if(userRoleInfo==null){
            return Response.fail(1, "您的账号不符合参与条件");
        }



        long sid = signedService.getSignedSidByBusiId(uid, BusiId.MAKE_FRIEND.getValue());
        long familyId = signedService.getSignedSidByBusiId(uid,  BusiId.SKILL_CARD.getValue());
        if(sid<=0 && familyId<=0) {
            return Response.success(false);
        }else{
            return Response.success(true);
        }

    }




    private String buildAwardPoolKey(AnchorWelfareTaskComponetAttr attr) {
        return makeKey(attr, AnchorWelfareConst.TASK_AWARD_POOL_AMOUNT);
    }


    private long taskAwardPoolLeft(AnchorWelfareTaskComponetAttr attr){
        String redisCode = getRedisGroupCode(attr.getActId());
        long send = Convert.toLong(actRedisDao.get(redisCode, buildAwardPoolKey(attr)), 0);
        long left = Math.max(attr.getTotalPool() - send, 0);
        left = left >= attr.getMinAwardPool() ? left : 0;
        return left;

    }
    private boolean taskAwardPoolValide(AnchorWelfareTaskComponetAttr attr) {
        String redisCode = getRedisGroupCode(attr.getActId());
        long send = Convert.toLong(actRedisDao.get(redisCode, buildAwardPoolKey(attr)), 0);
        long left = Math.max(attr.getTotalPool() - send, 0);
        return left >= attr.getMinAwardPool() ? true : false;
    }

    public boolean taskAwardPoolValide(long actId) {
        AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
        return taskAwardPoolValide(attr);
    }


    /**
     * pc打开活动页上报
     * @return
     */
    @RequestMapping("/pcOpenActivity")
    public Response pcOpenActivity(HttpServletRequest request, HttpServletResponse response,
                                   @RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex){
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(401, "you need login first!");
        }

        log.info("pcOpenActivity first actId:{},cmptIndex:{},uid:{}",actId,cmptIndex,uid);

        AnchorWelfareTaskComponetAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(1, "未在活动时间内");
        }

        Date now = commonService.getNow(actId);

        //pcyy首次进去活动页记录
        String groupCode = getRedisGroupCode(attr.getActId());
        String pcFirstKey = makeKey(attr, PC_FIRST_INSERT_ACTIVITY);
        boolean first = actRedisDao.hsetnx(groupCode, pcFirstKey, String.valueOf(uid), DateUtil.format(now, DateUtil.PATTERN_TYPE1));
        if (first) {
            //判断奖池
            if (taskAwardPoolValide(attr)) {
                log.info("pcOpenActivity first taskAwardPoolValide actId:{},cmptIndex:{},uid:{}",actId,cmptIndex,uid);
                //添加YOMI弹窗白名单
                ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(attr.getActId());
                Date endDate = new Date(activityInfoVo.getEndTime());
                reportUserOrient(attr.getActId(), uid, attr.getApp(), endDate);
            } else {
                log.info("pcOpenActivity award pool not enough,uid:{}", uid);
            }

        }

        return Response.ok();
    }



    @GetMapping("/taskStatic")
    public Response<String> taskStatic(HttpServletRequest request, HttpServletResponse response, Long actId) {
        doStaticReport(actId, commonService.getNow(actId), getUniqueComponentAttr(actId));
        return Response.ok();
    }

    /**
     * 每小时一次日报
     */
    @NeedRecycle(author = "chengaojie", notRecycle = true)
    @Scheduled(cron = "6 0 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }
            AnchorWelfareTaskComponetAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            log.info("begin staticReport game,actId:{}", actId);
            doStaticReport(actId, commonService.getNow(actId), attr);

        }
    }

    public void doStaticReport(long actId, Date now, AnchorWelfareTaskComponetAttr attr) {

        if(attr==null) {
            log.error("doStaticReport doStaticReport attr is null,actId:{},",actId);
            return;
        }

        String groupCode = getRedisGroupCode(actId);
        String today = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String dayHourCode = DateUtil.format(now, DateUtil.PATTERN_TYPE7);

        String execKey = makeKey(attr, "execAnchorWelfareStatic:"+dayHourCode);
        if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
            log.info("doStaticReport has report execKey:{}",execKey);
            return;
        }

        log.info("doStaticReport begin staticReport game,actId:{}", actId);

        StringBuilder content = new StringBuilder();
        content.append(String.format("### 数据日期：%s\n", dayHourCode));

        //奖池
        content.append("【奖池跟踪】\n");
        long todayConsume = Convert.toLong(actRedisDao.hget(groupCode, buildAwardPoolDayConsumeKey(attr),today),0L);
        content.append("今日发放总金额：").append(AnchorWelfareConst.formatMoney(todayConsume)).append("\n");
        content.append("总奖池剩余金额：").append(AnchorWelfareConst.formatMoney(taskAwardPoolLeft(attr))).append("\n");


        //任务完成情况
        content.append("【累计完成情况跟踪】（指活动期间完成的总人数）\n");
        String loginKey = makeKey(attr, LOGIN_TASK_FINISH_KEY);
        content.append("APP登录任务累计完成人数：").append(actRedisDao.hsize(groupCode,loginKey)).append("\n");
        for(int i=MIN_TASK_LEVEL;i<=MAX_TASK_LEVEL;i++) {
            String key =  makeKey(attr,AnchorWelfareConst.TASK_FINISH_COUNT_STATISTIC);
            long finishCount = Convert.toLong(actRedisDao.hget(groupCode,key,String.valueOf(i)),0);
            content.append("累计完成第"+i+"级全部任务人数：").append(finishCount).append("人\n");
        }

        //白名单人数
        content.append("【白名单】\n");
        List<String> whiteUserKey = Lists.newArrayList("2_new","2_old","34_new","34_old");
        String whiteListTotalKey = makeKey(attr,AnchorWelfareConst.WHITE_USER_TOTAL_STATISTIC);
        List<Object> objects = actRedisDao.hmGet(groupCode,whiteListTotalKey,MyListUtils.toObjectList(whiteUserKey));
        List<Object> dayObjects = actRedisDao.hmGet(groupCode,whiteuerDayAddStatisKey(attr,now),MyListUtils.toObjectList(whiteUserKey));
        content.append("交友新用户白名单：").append(getWhiteUserStatic(objects,dayObjects,0)).append("\n");
        content.append("交友老用户白名单：").append(getWhiteUserStatic(objects,dayObjects,1)).append("\n");
        content.append("聊天室新用户白名单：").append(getWhiteUserStatic(objects,dayObjects,2)).append("\n");
        content.append("聊天室老用户白名单：").append(getWhiteUserStatic(objects,dayObjects,3)).append("\n");

        String msg = buildActRuliuMsg(actId, false, "主持福利数据统计-小时", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, Lists.newArrayList());
    }



    private String getWhiteUserStatic(List<Object> objects , List<Object> dayObjects,int index) {
        long total =  Convert.toLong(objects.get(index),0);
        long dayAdd =  Convert.toLong(dayObjects.get(index),0);
        return "总人数"+total+"（今日新增"+dayAdd+"）";


    }

}
