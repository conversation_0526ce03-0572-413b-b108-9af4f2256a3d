package com.yy.gameecology.hdzj.element.component;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.DanmakuGameInfo;
import com.yy.gameecology.activity.bean.DanmakuGameVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.acttask.RankTaskStaticConfig;
import com.yy.gameecology.activity.bean.hdzt.DanmakuGameMaterial;
import com.yy.gameecology.activity.bean.mq.DanmakuChannelGameEvent;
import com.yy.gameecology.activity.bean.rankroleinfo.SubGuildRoleItem;
import com.yy.gameecology.activity.client.thrift.FtsChPopularClient;
import com.yy.gameecology.activity.client.thrift.FtsRecommendDataThriftClient;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.rolebuilder.impl.SubGuildRoleBuilder;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.SkipChannelInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.DanmakuGameComponentAttr;
import com.yy.gameecology.hdzj.element.redis.TaskCompleteAwardComponent;
import com.yy.protocol.pb.danmu.Danmu;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


/**
 * 弹幕游戏相关
 */
@RestController
@RequestMapping("/cmpt/danmaku")
@Component
public class DanmakuGameComponent extends BaseActComponent<DanmakuGameComponentAttr> {

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private TaskCompleteAwardComponent taskCompleteAwardComponent;

    @Autowired
    private TaskCompleteAwardMySQLComponent taskCompleteAwardMySQLComponent;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    @Autowired
    private FtsRecommendDataThriftClient ftsRecommendDataThriftClient;

    @Autowired
    private FtsChPopularClient ftsChPopularClient;



    @Override
    public Long getComponentId() {
        return ComponentId.DANMAKU_GAME;
    }


    @HdzjEventHandler(value = DanmakuChannelGameEvent.class, canRetry = true)
    public void channelGameChange(DanmakuChannelGameEvent event, DanmakuGameComponentAttr attr) {
        boolean showLayer = false;
        if (event.isPlaying()) {
            showLayer = attr.getGameNameList().stream().anyMatch(p -> event.getAppId().equals(p.getAppId()));
        }
        kafkaService.sendJiaoyouLayerKafka(attr.getActId(), event.getSid(), event.getSsid(), showLayer);

    }

    /**
     * 查询弹幕游戏在线直播间
     */
    @GetMapping("/getGameChannels")
    public Response<DanmakuGameVo> getGameChannels(@RequestParam(name = "actId") long actId,
                                                   @RequestParam(name = "gameType") long gameType) {

        DanmakuGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(-3, "未在活动时间内");
        }

        if (CollectionUtils.isEmpty(attr.getGameNameList())) {
            return Response.fail(404, "活动未配置弹幕游戏");
        }

        DanmakuGameVo result = getDanmakuGameChannels(attr);

        log.info("getGameChannels actId:{}, gameType:{},  result:{}", actId, gameType, JSON.toJSONString(result));
        return Response.success(result);
    }

    /**
     * 弹幕游戏挂件入口是否展示
     *
     * @param actId
     * @param sid
     * @param ssid
     * @return
     */
    @RequestMapping(value = "/queryGameStatus")
    public Response<Map<String, Object>> queryGameStatus(@RequestParam(name = "actId") Long actId,
                                                         Long sid, Long ssid) {

        if (actId == null || sid == null || ssid == null) {
            log.warn("queryGameStatus para error,actid:{},sid:{},ssid:{}", actId, sid, ssid);
            return new Response<>(Code.E_DATA_ERROR.getCode(), "para error", null);
        }

        try {
            DanmakuGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                return Response.fail(404, "活动未启用玩法");
            }

            if (!actInfoService.inActTime(actId)) {
                return Response.fail(-3, "未在活动时间内");
            }

            if (CollectionUtils.isEmpty(attr.getGameNameList())) {
                log.error("queryGameStatus 未配置弹幕游戏appid actId:{}", actId);
                return Response.fail(404, "活动未配置弹幕游戏");
            }

            Map<String, Object> result = queryGameStatus(actId, sid, ssid, attr);
            log.info("queryGameStatus,actId:{},sid:{},ssid:{},result:{}", actId, sid, ssid, JSON.toJSONString(result));
            return new Response<>(Code.OK.getCode(), "ok", result);
        } catch (Exception e) {
            log.error("queryGameStatus exception,actId:{},sid:{},ssid:{},err:{}", actId, sid, ssid, e.getMessage());
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    @GetMapping("/taskStatic")
    public Response<String> taskStatic(HttpServletRequest request, HttpServletResponse response, Long actId, String dayCode) {
        doStaticReport(actId, DateUtil.getDate(dayCode, DateUtil.PATTERN_TYPE2), getUniqueComponentAttr(actId));
        return Response.ok();
    }

    /**
     * 弹幕游戏信息介绍
     */
    @GetMapping("/queryDanmakuGameInfoList")
    public Response<List<DanmakuGameInfo>> queryDanmakuGameInfoList(HttpServletRequest request, HttpServletResponse response, Long actId) {
        DanmakuGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(404, "未配置");
        }
        List<DanmakuGameInfo> gameInfos = attr.getGameNameList()
                .stream()
                .filter(p -> StringUtil.isNotBlank(p.getGameUrl()) && StringUtil.isNotBlank(p.getGameDesc()))
                .map(p -> {
                    DanmakuGameInfo info = new DanmakuGameInfo();
                    info.setGameName(p.getGameName());
                    info.setGameUrl(p.getGameUrl());
                    info.setGameDesc(p.getGameDesc());
                    return info;
                }).toList();
        return Response.success(gameInfos);
    }


    @NeedRecycle(author = "chengaojie", notRecycle = true)
    @Scheduled(cron = "0 0 10 * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}", actId);
                return;
            }
            DanmakuGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}", actId);
                continue;
            }

            doStaticReport(actId, commonService.getNow(actId), attr);
        }
    }


    public void doStaticReport(long actId, Date now, DanmakuGameComponentAttr attr) {
        log.info("DanmakuGameComponent begin staticReport game,actId:{}", actId);

        if (attr == null) {
            log.error("DanmakuGameComponent doStaticReport attr is null,actId:{},", actId);
            return;
        }
        String groupCode = getRedisGroupCode(actId);
        String ymd = DateUtil.format(now, DateUtil.PATTERN_TYPE2);

        String execKey = makeKey(actId, attr.getCmptUseInx(), "send_danmaku_task_day_static:" + ymd);
        if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
            log.info("staticReport has report execKey:{}", execKey);
            return;
        }

        Date preDay = DateUtil.add(now, -1);
        String dayCode = DateUtil.format(preDay, DateUtil.PATTERN_TYPE2);
        StringBuilder content = new StringBuilder();
        content.append(String.format("### 数据日期：%s\n", dayCode));

        List<RankTaskStaticConfig> taskConfigs = attr.getTaskConfig();
        if (CollectionUtils.isNotEmpty(taskConfigs)) {
            Map<String, Long> taskMap;
            if (actId <= 2025026003) {
                taskMap = taskCompleteAwardComponent.getTaskStaticInfo(actId, dayCode);
            } else {
                taskMap = taskCompleteAwardMySQLComponent.getTaskStaticInfo(actId, dayCode);
            }
            for (RankTaskStaticConfig taskConfig : taskConfigs) {
                String hashKey = taskConfig.getTaskRankId() + "|" + taskConfig.getTaskLevel();
                long finishCount = taskMap.containsKey(hashKey) ? taskMap.get(hashKey) : 0;
                content.append(taskConfig.getTaskName()).append("用户数：").append(finishCount).append("\n");
            }
        }

        String msg = buildActRuliuMsg(actId, false, "弹幕游戏任务完成日报", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, Lists.newArrayList());
    }

    public Map<String, Object> queryGameStatus(Long actId, Long sid, Long ssid, DanmakuGameComponentAttr attr) {
        Map<String, Object> result = Maps.newHashMap();

        int showStatus = 0;
        Danmu.QueryGameStatusReq req = Danmu.QueryGameStatusReq.newBuilder().setSid(sid).setSsid(ssid).build();
        Danmu.QueryGameStatusResp rsp = danmakuActivityClient.getReadProxy().queryGameStatus(req);
        if (rsp.getResult().getCode() == 0) {
            if (rsp.getPlaying()) {
                showStatus = attr.getGameNameList().stream().anyMatch(p -> rsp.getAppId().equals(p.getAppId())) ? 1 : 0;
            }
        }
        result.put("showStatus", showStatus);
        result.put("time", System.currentTimeMillis());
        result.put("grey", commonService.isGrey(actId));
        return result;
    }


    public DanmakuGameVo getDanmakuGameChannels(DanmakuGameComponentAttr attr) {
        DanmakuGameVo result = new DanmakuGameVo();
        List<SkipChannelInfo> list = Lists.newArrayList();

        Danmu.QueryAllGameChannelReq req = Danmu.QueryAllGameChannelReq.newBuilder().build();

        Danmu.QueryAllGameChannelResp rsp = danmakuActivityClient.getReadProxy().queryAllGameChannel(req);
        if (rsp.getResult().getCode() == 0) {
            List<Danmu.ChannelGameInfo> gameList = rsp.getChannelGameInfoList();

            List<Long> shieldChannels = attr.getShieldChannels();
            if (!gameList.isEmpty() && shieldChannels != null && shieldChannels.size() > 0) {
                gameList = gameList.stream().filter(v -> !shieldChannels.contains(v.getSid())).collect(Collectors.toList());
            }

            Map<String, String> channelAppIdMap = Maps.newHashMap();
            Map<String, Long> channelAnchorUidMap = Maps.newHashMap();
            Set<Long> anchorUids = new HashSet<>(gameList.size());
            Set<String> memberIds = new HashSet<>(gameList.size());
            Set<Long> sids = new HashSet<>(gameList.size());
            for (Danmu.ChannelGameInfo item : gameList) {
                sids.add(item.getSid());
                anchorUids.add(item.getAnchorUid());
                String memberId = item.getSid() + StringUtil.UNDERSCORE + item.getSsid();
                if (!memberIds.contains(memberId)) {
                    memberIds.add(memberId);
                }
                channelAnchorUidMap.put(memberId, item.getAnchorUid());
                channelAppIdMap.put(memberId, item.getAppId());
            }
            int businessType = MemberInfoService.changeToBusinessType(attr.getBuisId());
            Map<String, String> roleMemberPicMap = ftsRecommendDataThriftClient.batchGetFtsRoomMgrPic(Lists.newArrayList(memberIds), businessType);
            list = gameList.stream().map(v -> {
                SkipChannelInfo item = new SkipChannelInfo();
                item.setSid(v.getSid());
                item.setSsid(v.getSsid());
                String member = v.getSid() + "_" + v.getSsid();
                if (roleMemberPicMap.containsKey(member)) {
                    item.setLogo(roleMemberPicMap.get(member));
                }
                return item;
            }).toList();

            SubGuildRoleBuilder rankBuilder = new SubGuildRoleBuilder();
            Map<String, SubGuildRoleItem> subGuildRoleItemMap = rankBuilder.buildRankByYy(memberIds);
            Map<Long, ChannelBaseInfo> channelBaseInfoMap = commonService.getChannelInfos(List.copyOf(sids), false);
            for (SkipChannelInfo item : list) {
                long sid = item.getSid();
                long ssid = item.getSsid();
                ChannelBaseInfo channelBaseInfo = channelBaseInfoMap.get(sid);
                if (sid != ssid) {
                    String sidSsid = sid + "_" + ssid;
                    if (subGuildRoleItemMap.containsKey(sidSsid)) {
                        SubGuildRoleItem subGuildRoleItem = subGuildRoleItemMap.get(sidSsid);
                        item.setName(subGuildRoleItem.getName());
                    }
                } else if (channelBaseInfo != null) {
                    item.setName(channelBaseInfo.getName());
                }

                if (channelBaseInfo != null && StringUtil.isEmpty(item.getLogo())) {
                    item.setLogo(channelBaseInfo.getLogo());
                }
            }

            Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(Lists.newArrayList(anchorUids), false);
            for (SkipChannelInfo item : list) {
                String sidSsid = item.getSid() + StringUtil.UNDERSCORE + item.getSsid();

                Long anchorUid = channelAnchorUidMap.get(sidSsid);
                UserBaseInfo userInfo = userInfoMap.get(anchorUid);
                String anchorNick = userInfo != null ? userInfo.getNick() : "";

                String gameAppId = channelAppIdMap.get(sidSsid);
                Optional<DanmakuGameMaterial> danmakuGameMaterial = attr.getGameNameList().
                        stream().
                        filter(v -> gameAppId.equals(v.getAppId())).
                        findFirst();
                String gameNameUrl = danmakuGameMaterial.isPresent() ? danmakuGameMaterial.get().getGameNameUrl() : "";

                JSONObject extdat = new JSONObject();
                extdat.put("gameName", gameNameUrl);
                extdat.put("anchorNick", anchorNick);
                item.setExtdat(extdat.toString());
            }
        }

        //读取人气值，根据人气值排序
        list = sortChannelInfo(list, attr);
        result.setGameList(list);

        if (list.isEmpty() && !attr.getRecommendChannels().isEmpty()) {
            String[] recommendChls = attr.getRecommendChannels().toArray(new String[0]);
            int randomIndex = ThreadLocalRandom.current().nextInt(recommendChls.length);
            String recommendChl = recommendChls[randomIndex];

            String[] ssid = recommendChl.split(StringUtil.UNDERSCORE);
            SkipChannelInfo item = new SkipChannelInfo();
            item.setSid(Convert.toLong(ssid[0]));
            item.setSsid(Convert.toLong(ssid[1]));
            result.setRecommendChannel(item);
        }

        return result;

    }

    /**
     * 频道根据产品定义优先级、人气值排序
     * 产品定义优先级较高
     */
    public List<SkipChannelInfo> sortChannelInfo(List<SkipChannelInfo> source, DanmakuGameComponentAttr attr) {
        if (CollectionUtils.isEmpty(source)) {
            return source;
        }

        List<SkipChannelInfo> result = Lists.newArrayList();

        //有自定义规则的先排序（优先级高）
        List<SkipChannelInfo> firstSort = source.stream()
                .filter(p -> attr.getChannelSort().containsKey(p.getSsid()))
                .peek(p -> p.setSort(attr.getChannelSort().get(p.getSsid()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(firstSort)) {
            firstSort.sort((o1, o2) -> Long.compare(o2.getSort(), o1.getSort()));
            result.addAll(firstSort);
        }

        //剩余的频道，用人气值排序
        List<SkipChannelInfo> secondSort = source.stream().filter(p -> !attr.getChannelSort().containsKey(p.getSsid())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(secondSort)) {
            List<ChannelInfoVo> para = secondSort.stream().map(p -> {
                ChannelInfoVo channelInfoVo = new ChannelInfoVo();
                channelInfoVo.setSid(p.getSid());
                channelInfoVo.setSsid(p.getSsid());
                return channelInfoVo;
            }).toList();
            Map<Long, Long> channelScore = ftsChPopularClient.querySubChannelPopular(para);
            secondSort = secondSort.stream().peek(p -> p.setSort(channelScore.getOrDefault(p.getSsid(), 0L))).collect(Collectors.toList());
            secondSort.sort((o1, o2) -> Long.compare(o2.getSort(), o1.getSort()));
            result.addAll(secondSort);
        }

        return result;
    }


}
