package com.yy.gameecology.common.utils;

import java.nio.charset.Charset;

public final class MyCharsets {

	// Basic Encoding Set (contained in lib/rt.jar)
	//-----------------------------------------------------------------------

	/**
	 * UTF-8: eight-bit UCS Transformation Format.
	 */
	public static final Charset UTF_8 = Charset.forName("UTF-8");

	// Extended Encoding Set (contained in lib/charsets.jar)
	//-----------------------------------------------------------------------

	/**
	 * GBK: Simplified Chinese
	 */
	public static final Charset GBK = Charset.forName("GBK");

	/**
	 * GB2312: EUC encoding, Simplified Chinese
	 */
	public static final Charset GB2312 = Charset.forName("GB2312");

}