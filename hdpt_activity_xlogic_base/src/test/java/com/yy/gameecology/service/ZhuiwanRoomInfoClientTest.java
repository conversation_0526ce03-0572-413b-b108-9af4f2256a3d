package com.yy.gameecology.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-10-31 17:47
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties", "classpath:env/local/application-inner.properties"})
public class ZhuiwanRoomInfoClientTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "3");
    }

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Test
    public void listRoomInfoBySsidTest(){
        zhuiwanRoomInfoClient.listRoomInfoBySsid(Lists.newArrayList(1454054224L));
    }

    @Test
    public void listFamilyValidRoomTest(){
        zhuiwanRoomInfoClient.listFamilyValidRoom(10051);
    }
}
