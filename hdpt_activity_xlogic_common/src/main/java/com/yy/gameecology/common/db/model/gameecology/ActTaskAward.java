package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;

/**
 * desc:任务奖励
 *
 * @createBy 曾文帜
 * @create 2020-10-22 15:08
 **/
@TableColumn(underline = true)
public class ActTaskAward implements Serializable {
    public static String TABLE_NAME = "act_task_award";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<ActTaskAward> ROW_MAPPER = null;

    /**
     * 角色类型
     */
    private Integer roleType;

    /**
     * 任务配置
     */
    private Long taskId;

    /**
     * 活动中台奖励task id
     */
    private Long hdztTaskId;

    /**
     * 活动中台奖励package id
     */
    private Long hdztPackageId;

    /**
     * 发放数量
     */
    private Integer amount;

    /**
     * 礼物描述配置
     */
    private String desc;

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getHdztTaskId() {
        return hdztTaskId;
    }

    public void setHdztTaskId(Long hdztTaskId) {
        this.hdztTaskId = hdztTaskId;
    }

    public Long getHdztPackageId() {
        return hdztPackageId;
    }

    public void setHdztPackageId(Long hdztPackageId) {
        this.hdztPackageId = hdztPackageId;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
