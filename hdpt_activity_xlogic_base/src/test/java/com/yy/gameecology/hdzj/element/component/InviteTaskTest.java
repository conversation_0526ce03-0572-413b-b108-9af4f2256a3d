package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.mq.ActShareSuccessEvent;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.hdzj.element.component.attr.InviteTaskComponentAttr;
import com.yy.gameecology.hdzj.utils.ZhuiyaClientUtils;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-13 15:26
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties",
        "classpath:env/local/application-inner.properties"})
public class InviteTaskTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "4");
    }

    @Autowired
    private InviteTaskComponent inviteTaskComponent;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    final long ACT_ID = 2025064001;

    @Test
    public void receiveInviteTest() {
        ActShareSuccessEvent event = new ActShareSuccessEvent();
        event.setShareUid(2959049318L);
        event.setUid(1222222L);
        event.setHdid("123456789");
        event.setIp("127.0.0.125");
        event.setApp("yomi");
        event.setExtJson("{\"actId\":2025064001}");

        var attr = inviteTaskComponent.getUniqueComponentAttr(ACT_ID);
        inviteTaskComponent.onActShareSuccessEvent(event, attr);
    }

    @Test
    public void RiskTest() {
        ZhuiyaPbCommon.Client client = ZhuiyaClientUtils.toClient("123456789", "127.0.0.128", "yomi", 0, "", "");
        var attr = inviteTaskComponent.getUniqueComponentAttr(ACT_ID);
        var riskExt = inviteTaskComponent.buildHelpRiskExt(attr, 2959049318L);
        ZhuiwanRiskClient.RiskCheckResult riskRsp = zhuiwanRiskClient.riskCheck(client, "INVITE_ACT", 50042952L, "", "", "", riskExt);

    }

    @Test
    public void ruliuReportTest() {
        var attr = inviteTaskComponent.getUniqueComponentAttr(ACT_ID);
        inviteTaskComponent.rlReport(attr.getActId(), attr.getCmptUseInx());
    }

}
