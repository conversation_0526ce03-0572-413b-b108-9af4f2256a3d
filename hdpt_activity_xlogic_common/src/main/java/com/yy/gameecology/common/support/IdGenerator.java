package com.yy.gameecology.common.support;


import com.google.common.collect.Maps;
import com.yy.gameecology.common.CommDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;

public class IdGenerator {
    private static final Logger log = LoggerFactory.getLogger(IdGenerator.class);

    public static final int MOD_TIMES = 10;

    private static final String SELECT_TMPL = "select * from id_generator where name=?";

    private static final String UPDATE_TMPL =
            "update id_generator set curr=curr+%s, datver=datver+1, modified=%s where name='%s' and datver=%s";

    private Random rd = new Random(System.currentTimeMillis());

    private static DataSource dataSource;

    /** 标识器集合，key为标识器名字， value为标识器对象 */
    private Map<String, ID> idmap = Maps.newHashMap();

    private static IdGenerator generator = null;

    private IdGenerator() {}

    public synchronized static IdGenerator getInstance() {
        if (generator == null) {
            generator = new IdGenerator();
        }
        return generator;
    }

    public synchronized long getId() throws Exception {
        return getId("DEFAULT_ID");
    }

    /**
     * 从默认数据库连接中获取指定ID的当前值
     *
     * @param identity
     * @return
     * @throws Exception
     */
    public synchronized long getId(String identity) throws Exception {
        identity = trim(identity);
        ID id = idmap.get(identity);
        if (id != null && id.hasNext()) {
            return id.get();
        }

        Connection conn = null;
        try {
            conn = getConnection();
            return getId(identity, conn);
        } finally {
            CommDao.closeConnection(conn);
        }
    }

    /**
     * 从指定数据库连接中获取指定ID的当前值 【注意】： 本函数会做事务提交/回滚，所以调用本函数前， conn 上应没有未提交的事务，否则会被本函数一并提交/回滚 ####
     * 鉴于ID值的重要性，本规则不可妥协 ####
     *
     * @param identity
     * @param conn
     * @return
     * @throws Exception
     */
    public synchronized long getId(String identity, Connection conn) throws Exception {
        identity = trim(identity);
        ID id = idmap.get(identity);
        if (id != null && id.hasNext()) {
            return id.get();
        }

        boolean bAutoCommit = conn.getAutoCommit();
        try {
            conn.setAutoCommit(false);
            id = getIDRecord(identity, conn);
            idmap.put(identity, id);
            CommDao.commit(conn);
            return id.get();
        } catch (Exception e) {
            CommDao.rollback(conn);
            throw e;
        } finally {
            conn.setAutoCommit(bAutoCommit);
        }
    }

    /**
     * 从默认数据库连接中获取指定ID名集的值集
     *
     * @param identities - key为属性名, value为ID名字
     * @return 返回一个Map, key为属性名, value为指定ID名字的ID值
     * @throws Exception
     */
    public Map<String, Long> getIdEntries(Map<String, String> identities) throws Exception {
        Map<String, Long> entries = Maps.newHashMap();
        for (Iterator<String> itr = identities.keySet().iterator(); itr.hasNext();) {
            String key = itr.next();
            String identity = identities.get(key);
            entries.put(key, getId(identity));
        }
        return entries;
    }

    /**
     * 从指定数据库连接中获取指定ID名集的值集 【注意】： 本函数调用的 getId() 会做事务提交/回滚，所以调用本函数前， conn 上应没有未提交的事务，否则会被本函数一并提交/回滚
     * #### 鉴于ID值的重要性，本规则不可妥协 ####
     *
     * @param identities - key为属性名, value为ID名字
     * @return 返回一个Map, key为属性名, value为指定ID名字的ID值
     * @throws Exception
     */
    public Map<String, Long> getIdEntries(Map<String, String> identities, Connection conn) throws Exception {
        Map<String, Long> entries = Maps.newHashMap();
        for (Iterator<String> itr = identities.keySet().iterator(); itr.hasNext();) {
            String key = itr.next();
            String identity = identities.get(key);
            entries.put(key, getId(identity, conn));
        }
        return entries;
    }

    /**
     * 获取数据库链接
     *
     * @return
     * @throws SQLException
     */
    protected Connection getConnection() throws SQLException {
        if (dataSource == null) {
            dataSource = (DataSource) SpringBeanAwareFactory.getBean("dataSource");
        }
        return dataSource.getConnection();
    }

    /**
     * 从数据库中载入标识器数据，构造标识器对象返回 (支持分布式系统布局, 依赖于数据库， 可保证全局唯一)
     *
     * @param name - 标识器名
     * @param conn
     * @return
     * @throws Exception
     */
    @SuppressWarnings("resource")
    private ID getIDRecord(String name, Connection conn) throws Exception {
        long curr = -1;
        int datver = -1;
        int count = -1;

        while (true) {
            PreparedStatement ps = null;
            ResultSet rs = null;
            boolean bUpdateOk = false;

            try {
                // 查询当前值
                ps = conn.prepareStatement(SELECT_TMPL);
                ps.setString(1, name);
                rs = ps.executeQuery();
                if (!rs.next()) {
                    throw new Exception(String.format("no id[%s] exist!", name));
                }

                count = rs.getInt("COUNT");
                if (count < MOD_TIMES) {
                    throw new Exception("分配块长不能小于" + MOD_TIMES + " -> " + count);
                }

                curr = rs.getLong("CURR");
                datver = rs.getInt("DATVER");

                // 更新标识器记录,保证id唯一性
                String update = String.format(UPDATE_TMPL, count, getModified(conn), name, datver);
                ps = conn.prepareStatement(update);
                bUpdateOk = ps.executeUpdate() == 1;
                CommDao.commit(conn);
            } finally {
                CommDao.closeResultSet(rs);
                CommDao.closeStatement(ps);
            }

            // 若更新成功, 则结束循环, 否则睡眠随机毫秒数
            if (bUpdateOk) {
                break;
            }
            sleep();
        }

        // 构造标识器对象并返回
        return new ID(name, curr, count);
    }

    private void sleep() {
        try {
            Thread.sleep(rd.nextInt(500));
        } catch (Throwable t) {
            log.error(t.getMessage(), t);
        }
    }

    private String getModified(Connection conn) throws Exception {
        boolean bMysql = CommDao.isMysql(conn);
        boolean bOracle = CommDao.isOracle(conn);
        if (!bMysql && !bOracle) {
            throw new Exception("only support mysql and oracle!");
        }
        return bMysql ? " now() " : " sysdate ";
    }

    /**
     * 字符串首尾剪切函数，为null则返回空串
     *
     * @param o
     * @return
     */
    private String trim(Object o) {
        if (o == null) {
            return "";
        }
        return String.valueOf(o).trim();
    }

    /**
     * 标识器类 标识器对象一旦生成，其可用标识数就固定下来，用完后该对象就没用了 如此设计可保证每个生成的标识器对象只提供固定的有限的标识值
     *
     * <AUTHOR>
     *
     */
    private class ID {
        private String name;

        private long curr;

        private long max;

        private int model;

        private int remainder;

        public ID(String name, long curr, int count) throws Exception {
            if (curr < 0 || count < 0) {
                throw new Exception(String.format("curr/count must greate zero! -> [%s, %s]", curr, count));
            }
            this.name = name;
            this.curr = curr;
            this.model = 1;
            this.remainder = 0;
            this.max = curr + count;
        }

        public long get() throws Exception {
            while ((++curr % model) != remainder) {}
            if (curr > max) {
                throw new Exception(String.format("no id can alloc by ID[%s, %s]", name, curr));
            }
            return curr;
        }

        public boolean hasNext() {
            return max - curr >= model;
        }
    }
}
