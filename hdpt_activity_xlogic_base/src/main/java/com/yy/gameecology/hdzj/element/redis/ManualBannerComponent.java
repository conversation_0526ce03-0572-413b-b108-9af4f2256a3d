package com.yy.gameecology.hdzj.element.redis;

import cn.hutool.core.lang.id.NanoId;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerLayout;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.RankBuilderComponent;
import com.yy.gameecology.hdzj.element.component.RankingTaskBannerComponent;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.ManualBannerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.RankBuilderComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@UseRedisStore
@RestController
@RequestMapping("5077")
public class ManualBannerComponent extends BaseActComponent<ManualBannerComponentAttr> {

    private static final String SEND_KEY = "send_bro";

    private static final String NICK_PLACEHOLDER = "{nick}";

    private static final String AVATAR_PLACEHOLDER = "{avatar}";

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private RankBuilderComponent rankBuilderComponent;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Override
    public Long getComponentId() {
        return ComponentId.MANUAL_BANNER;
    }

    @GetMapping("config")
    public Response<List<ManualBanner>> queryManualBannerConfig(@RequestParam("actId") long actId) {
        List<ManualBannerComponentAttr> attrs = getAllComponentAttrs(actId);
        if (CollectionUtils.isEmpty(attrs)) {
            return Response.success(Collections.emptyList());
        }

        List<ManualBanner> result = new ArrayList<>(attrs.size());
        for (ManualBannerComponentAttr attr : attrs) {
            ManualBanner banner = new ManualBanner();
            banner.setActId(actId);
            banner.setCmptIndex(attr.getCmptUseInx());
            banner.setRoleType(attr.getRoleType());
            banner.setMemberIds(attr.getMembers());

            String key = makeKey(attr, SEND_KEY);
            long size = actRedisDao.getRedisTemplate(getRedisGroupCode(actId)).opsForHash().size(key);
            banner.setStatus(size >= attr.getMembers().size() ? 1 : 0);

            result.add(banner);
        }

        return Response.success(result);
    }

    @RequestMapping("trigger")
    public Response<List<String>> triggerBroadcastBanner(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex) {
        ManualBannerComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0 || !attr.getTriggerWhitelist().contains(uid)) {
            return Response.fail(403, "trigger not perm!");
        }

        List<String> memberIds = new ArrayList<>(attr.getMembers().size());
        for (String member : attr.getMembers()) {
            String memberId = broadcastBanner(attr, member, attr.getRoleType());
            if (memberId != null) {
                memberIds.add(memberId);
            }
        }

        return Response.success(memberIds);
    }

    @RequestMapping("clear")
    public Response<?> clearBroadcastBanner(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex) {
        ManualBannerComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0 || !attr.getTriggerWhitelist().contains(uid)) {
            return Response.fail(403, "clear not perm!");
        }

        String key = makeKey(attr, SEND_KEY);
        actRedisDao.del(getRedisGroupCode(actId), key);

        return Response.success(StringUtil.EMPTY);
    }

    public String broadcastBanner(ManualBannerComponentAttr attr, String memberId, int roleType) {
        String key = makeKey(attr, SEND_KEY);
        boolean set = actRedisDao.hsetnx(getRedisGroupCode(attr.getActId()), key, memberId, DateUtil.getNowYyyyMMddHHmmss());
        if (!set) {
            return null;
        }

        MemberInfo memberInfo = memberInfoService.getMemberInfo((long) attr.getBusiId(), RoleType.findByValue(roleType), memberId);
        if (memberInfo != null) {
            String nick = roleType == RoleType.GUILD.getValue() ? memberInfo.getAsid() : memberInfo.getName();
            String avatar = memberInfo.getHdLogo();

            nick = replaceWhiteListNick(attr.getActId(), memberId, nick);

            doBroadcastBanner(attr, nick, avatar, 0, 0);
        }

        return memberId;
    }

    /**
     * 白名单昵称替换
     */
    private String replaceWhiteListNick(long actId, String member, String oldNick) {
        RankBuilderComponentAttr att = rankBuilderComponent.getUniqueComponentAttr(actId);
        if (att == null) {
            return oldNick;
        }
        if (att.getMemberNameCmptIndex() <= 0) {
            return oldNick;
        }
        String value = whitelistComponent.getConfigValue(actId, att.getMemberNameCmptIndex(), member);
        if (StringUtil.isNotBlank(value)) {
            return value;
        }

        return oldNick;
    }

    public void doBroadcastBanner(ManualBannerComponentAttr attr, String nick, String avatar, long sid, long ssid) {
        doBroadcastAppBanner(attr, nick, avatar, sid, ssid);
        doBroadcastPCBanner(attr, nick, avatar, sid, ssid);
    }

    public void doBroadcastAppBanner(ManualBannerComponentAttr attr, String nick, String avatar, long sid, long ssid) {
        final String seq = NanoId.randomNanoId();
        AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();

        List<BannerSvgaTextConfig> svgaTexts = attr.getSvgaTexts();
        if (CollectionUtils.isNotEmpty(svgaTexts)) {
            List<Map<String, AppBannerSvgaText>> contentLayers = new ArrayList<>(svgaTexts.size());
            for (BannerSvgaTextConfig svgaText : svgaTexts) {
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
                String richText = svgaText.getText();
                if (StringUtils.contains(richText, NICK_PLACEHOLDER)) {
                    String replacement = nick.length() <= svgaText.getNameCountLimit() ? nick : nick.substring(0, svgaText.getNameCountLimit()) + "...";
                    richText = richText.replace(NICK_PLACEHOLDER, replacement);
                }

                appBannerSvgaText.setText(richText);
                appBannerSvgaText.setNameCountLimit(svgaText.getNameCountLimit());
                appBannerSvgaText.setGravity(svgaText.getGravity());
                if (StringUtils.isNotBlank(svgaText.getImages())) {
                    List<String> images = Splitter.on(',').omitEmptyStrings().trimResults().splitToList(svgaText.getImages());
                    appBannerSvgaText.setImgs(images);
                }

                if (StringUtils.startsWith(svgaText.getFontSize(), StringUtil.OPEN_BRACE)) {
                    Map<String, Integer> fontSize = JSON.parseObject(svgaText.getFontSize(), new TypeReference<Map<String, Integer>>(){});
                    appBannerSvgaText.setFontSize(fontSize);
                }

                contentLayers.add(ImmutableMap.of(svgaText.getKey(), appBannerSvgaText));

            }

            broSvgaConfig.setContentLayers(contentLayers);
        }

        Map<String, String> svgaImgLayers = attr.getSvgaImgLayers();
        if (MapUtils.isNotEmpty(svgaImgLayers)) {
            List<Map<String, String>> imgLayers = new ArrayList<>(svgaImgLayers.size());
            for (String imageKey : svgaImgLayers.keySet()) {
                String image = svgaImgLayers.get(imageKey);
                if (StringUtils.contains(image, AVATAR_PLACEHOLDER)) {
                    image = image.replace(AVATAR_PLACEHOLDER, avatar);
                }

                imgLayers.add(ImmutableMap.of(imageKey, image));
            }

            broSvgaConfig.setImgLayers(imgLayers);
        }

        broSvgaConfig.setJump(attr.getJump());
        broSvgaConfig.setHeight(attr.getHeight());
        broSvgaConfig.setDuration(attr.getDuration());
        broSvgaConfig.setLoops(attr.getLoops());

        AppBannerLayout layout = new AppBannerLayout();
        layout.setType(attr.getLayoutType());
        if (StringUtils.isNotBlank(attr.getLayoutMargin())) {
            layout.setMargin(JSON.parseObject(attr.getLayoutMargin(), new TypeReference<Map<String, List<Integer>>>() {
            }));
        }
        broSvgaConfig.setLayout(layout);
        broSvgaConfig.setLevel(attr.getLevel());
        broSvgaConfig.setWhRatio(attr.getWhRatio());
        broSvgaConfig.setClickLayerName(attr.getClickLayerName());
        broSvgaConfig.setSvgaURL(attr.getSvgaURL());
        broSvgaConfig.setMiniURL(attr.getMiniURL());
        broSvgaConfig.setJumpSvgaURL(attr.getJumpSvgaURL());
        broSvgaConfig.setJumpMiniURL(attr.getJumpMiniURL());

        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, getBroBusiness(attr.getBusiId()),
                AppBannerEvent2.BC_TYPE_BUSI, sid, ssid, "",
                Lists.newArrayList());

        appBannerEvent.setContentType(attr.getContentType());
        appBannerEvent.setAppId(RankingTaskBannerComponent.getTurnoverAppId(attr.getBusiId()));
        appBannerEvent.setSvgaConfig(broSvgaConfig);

        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("processSupperAppBanner app done nick:{}, avatar:{} event:{}", nick, avatar, JSON.toJSONString(appBannerEvent));
    }

    public void doBroadcastPCBanner(ManualBannerComponentAttr attr, String nick, String avatar, long sid, long ssid) {
        final long actId = attr.getActId();
        Map<String, String> extMap = getExtMap(attr, nick, avatar);

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(attr.getCmptUseInx()).setUserNick(nick)
                .setUserScore(0).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        broadCastHelpService.broadcast(actId, BusiId.findByValue(attr.getBusiId()), BroadcastConfig.BroType.BUSI_TEMPLATE.code, sid, ssid, bannerBroMsg);
        log.info("doBroadcastPCBanner done nick:{}, avatar:{}, extMap:{}", nick, avatar, extMap);
    }

    private static Map<String, String> getExtMap(ManualBannerComponentAttr attr, String nick, String avatar) {
        Map<String, String> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("nickName", nick);
        extMap.put("logo", avatar);
        extMap.put("memberId", StringUtils.EMPTY);
        extMap.put("value", StringUtil.ZERO);
        extMap.put("svgaUrl", attr.getSvgaURL());
        extMap.put("asid", StringUtil.ZERO);
        extMap.put("ext", StringUtil.EMPTY);
        extMap.put("busiId", String.valueOf(attr.getBusiId()));
        extMap.put("desc", StringUtil.EMPTY);
        extMap.put("roleType", StringUtil.ZERO);
        return extMap;
    }

    public static int getBroBusiness(int busiId) {
        return switch (busiId) {
            case 400 -> 8;
            case 500 -> 2;
            case 810 -> 1;
            default -> 4;
        };
    }

    @Data
    public static class ManualBanner {
        protected long actId;

        protected long cmptIndex;

        protected int status;

        protected int roleType;

        protected List<String> memberIds;
    }
}
