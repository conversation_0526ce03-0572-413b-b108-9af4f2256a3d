package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;

import lombok.Data;

import java.util.Map;

@Data
public class HeadlineTimeComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected int busiId;

    @ComponentAttrField(labelText = "每小时cp发奖总限额(紫水晶)")
    private Long cpAwardLimit;

    @ComponentAttrField(labelText = "口令抽奖组件index")
    private Long chatLotteryIndex;

    @ComponentAttrField(labelText = "在线单播组件index")
    private Long onlineNoticeIndex;

    @ComponentAttrField(labelText = "cp最后收礼频道index")
    private long lastCpIndex;

    @ComponentAttrField(labelText = "榜单id")
    private int rankId;

    @ComponentAttrField(labelText = "阶段id")
    private int phaseId;

    @ComponentAttrField(labelText = "小时榜cp发奖", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "达成分值", remark = "-1代表奖池不足时发放的奖品"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<Long, AwardAttrConfig> cpTaskPackageReward = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "是否跳过弹幕频道", remark = "非交友业务请配置0,交友业务广播等需要跳过弹幕频道配置1")
    private long excludeDanmuChannel = 0;

    @ComponentAttrField(labelText = "mp4 key配置", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "key"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "value")
            })
    private Map<String, String> mp4LayerExtKeyValues = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "mp4特效url", propType = ComponentAttrCollector.PropType.RESOURCE)
    private String mp4Url;

    @ComponentAttrField(labelText = "优先级", remark = "特效排队显示优先级，值越小，优先级越高；目前默认全屏礼物特效的优先级为999，如果优先级低于全屏礼物特效优先级，需要该值大于999")
    private int mp4Level;

    @ComponentAttrField(labelText = "群id")
    private long groupId;

    @ComponentAttrField(labelText = "群token")
    private String robotToken;
}
