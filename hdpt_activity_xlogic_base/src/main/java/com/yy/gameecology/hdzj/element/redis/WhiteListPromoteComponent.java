package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.PromoteItem;
import com.yy.gameecology.hdzj.bean.PromoteItemConfig;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.WhiteListPromoteComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-07-07 15:28
 **/
@UseRedisStore
@Component
@RestController
@RequestMapping("/hdzk/cmpt/WhiteListPromoteComponent")
public class WhiteListPromoteComponent extends BaseActComponent<WhiteListPromoteComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private MemberInfoService memberInfoService;


    @Override
    public Long getComponentId() {
        return ComponentId.WHITE_LIST_PROMOTE;
    }

    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = true)
    public void handlerPromoteTimeEndEvent(PromotTimeEnd event, WhiteListPromoteComponentAttr attr) {
        log.info("settle,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        try {
            doHandlerPromoteTimeEndEvent(event, attr);
        } catch (Exception e) {
            log.error("严重错误，公会晋级结算失败,开始重试");
            log.error("严重错误，公会晋级结算失败,开始重试:e:{}", e.getMessage(), e);

            String msg = buildRuliuMsg(attr.getActId(), true, attr.getSrcRankId(), attr.getSrcPhaseId(), "严重错误！！！！白名单晋级结算失败, 现在自动触发重试:" + e.getMessage());
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());

            throw e;
        }
    }

    public void doHandlerPromoteTimeEndEvent(PromotTimeEnd event, WhiteListPromoteComponentAttr attr) {
        doSettle(event, attr);
        doPkSettle(event, attr);
    }


    public void doSettle(PromotTimeEnd event, WhiteListPromoteComponentAttr attr) {
        if (!isMyDuty(event, attr)) {
            log.info("not my duty,rankId:{},phaseId:{}", event.getRankId(), event.getPhaseId());
            return;
        }
        Map<String, String> queryRankExt = Maps.newHashMap();
        queryRankExt.put(RankExtParaKey.RANK_TYPE_PROMOT, HdztRankType.PROMOT);
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(event.getActId(), attr.getSrcRankId(), attr.getSrcPhaseId(), "", attr.getSrcTopN(), queryRankExt);
        if (CollectionUtils.isEmpty(ranks)) {
            SysEvHelper.waiting(50);
            ranks = hdztRankingThriftClient.queryRanking(event.getActId(), attr.getSrcRankId(), attr.getSrcPhaseId(), "", attr.getSrcTopN(), queryRankExt);
        }
        if (CollectionUtils.isEmpty(ranks)) {
            throw new RuntimeException("获取榜单数据失败");
        }
        //计算晋级结果
        List<PromoteItem> promoteItems = calPromoteItem(event, attr, ranks);
        Map<String, String> notifyPkMembers = buildNotifyPkMember(promoteItems);

        saveSettleData(event, attr, promoteItems, notifyPkMembers);
    }

    private boolean isMyDuty(PromotTimeEnd event, WhiteListPromoteComponentAttr attr) {
        return event.getRankId() == attr.getSrcRankId() && event.getPhaseId() == attr.getSrcPhaseId();
    }


    private List<PromoteItem> calPromoteItem(PromotTimeEnd event, WhiteListPromoteComponentAttr attr, List<Rank> ranks) {
        List<PromoteItem> result = Lists.newArrayList();
        List<PromoteItemConfig> configs = attr.getPromoteConfig();
        //重要：查询结果丢掉了小数点，为保证高分排前，ranksCopyFixSort不能改变
        List<Rank> ranksCopyFixSort = Lists.newArrayList(ranks);
        Map<String, Rank> rankMap = ranksCopyFixSort.stream().collect(Collectors.toMap(Rank::getMember, rank -> rank));
        //填充在白名单内已经晋级
        for (PromoteItemConfig config : configs) {
            PromoteItem promoteItem = new PromoteItem();
            String expectedMember = StringUtils.trim(config.getExpectedMemberId());
            if (rankMap.containsKey(expectedMember)) {
                Rank rank = rankMap.get(expectedMember);
                promoteItem.setMemberId(rank.getMember());
                promoteItem.setSrcScore(rank.getScore());
                ranksCopyFixSort.remove(rank);
            }
            promoteItem.setAddScoreSrcRankId(config.getAddScoreSrcRankId());
            promoteItem.setDestPhaseId(config.getDestPhaseId());
            promoteItem.setDestRankId(config.getDestRankId());
            promoteItem.setLeftMemberRankSort(config.getLeftMemberRankSort());
            promoteItem.setPkSort(config.getPkSort());

            result.add(promoteItem);
        }


        //按名次填充未在白名单内以晋级
        result = result.stream().sorted(Comparator.comparing(PromoteItem::getLeftMemberRankSort)).collect(Collectors.toList());
        for (PromoteItem promoteItem : result) {
            if (StringUtil.isEmpty(promoteItem.getMemberId())) {
                Rank rank = ranksCopyFixSort.get(0);
                promoteItem.setMemberId(rank.getMember());
                promoteItem.setSrcScore(rank.getScore());
                ranksCopyFixSort.remove(rank);
            }
            //填充roleId
            EnrollmentInfo memberInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), attr.getBusiId(), attr.getRoleType(), promoteItem.getMemberId());
            if (memberInfo == null) {
                throw new RuntimeException("not found memberInfo,memberId:" + promoteItem.getMemberId());
            }
            promoteItem.setRoleId(memberInfo.getDestRoleId());
        }

        log.info("calPromoteItem,result:{}", JSON.toJSONString(result));
        return result;
    }

    /**
     * 按照pk规则排序
     *
     * @return key === 榜单Id_阶段id ; value === pk的成员Id,pk的成员Id
     */
    private Map<String, String> buildNotifyPkMember(List<PromoteItem> promoteItems) {
        Map<String, List<String>> rankMembers = Maps.newHashMap();
        List<PromoteItem> sortPkItems = promoteItems.stream().sorted(Comparator.comparing(PromoteItem::getPkSort)).collect(Collectors.toList());
        for (PromoteItem item : sortPkItems) {
            String key = item.getDestRankId() + "_" + item.getDestPhaseId();
            List<String> pkMembers = rankMembers.getOrDefault(key, Lists.newArrayList());
            pkMembers.add(item.getMemberId());
            rankMembers.put(key, pkMembers);
        }

        Map<String, String> result = Maps.newHashMap();
        for (String key : rankMembers.keySet()) {
            List<String> members = rankMembers.get(key);
            result.put(key, StringUtils.join(members, ","));
        }
        log.info("buildNotifyPkMember,promoteItems:{}", JSON.toJSONString(promoteItems));
        return result;
    }

    /**
     * 保存结算信息，内部保证幂等性，如果有异常，需要整体重试
     */
    private void saveSettleData(PromotTimeEnd event, WhiteListPromoteComponentAttr attr,
                                List<PromoteItem> promoteItems, Map<String, String> notifyPkMembers) {
        //从临时榜晋级到目标榜
        for (PromoteItem item : promoteItems) {
            if (item.getAddScoreSrcRankId() <= 0) {
                continue;
            }
            String seq = makeKey(attr, String.format("settle-update-%s-%s", item.getAddScoreSrcRankId(), item.getMemberId()));
            Map<Long, Long> rankScore = Maps.newHashMap();
            rankScore.put(item.getAddScoreSrcRankId(), item.getSrcScore());
            log.info("updateRankWithRetry,seq:{},actId:{},item:{},memberId:{},roleId:{},rankScore:{}", seq, attr.getActId(), attr.getPromoteItem(), item.getMemberId(), item.getRoleId(), item.getSrcScore());
            boolean updateResult = hdztRankingThriftClient.updateRankWithRetry(seq, attr.getActId()
                    , attr.getPromoteItem(), item.getMemberId(), item.getRoleId(), 0, rankScore, System.currentTimeMillis(), 3, BusiId.MAKE_FRIEND.getValue());
            if (!updateResult) {
                throw new RuntimeException("结算严重错误,累榜失败,请触发重试");
            }
        }
        //设置晋级结算标记
        for (Long promoteRankId : attr.getPromoteNotifySettle().keySet()) {
            String tag = attr.getPromoteNotifySettle().get(promoteRankId);
            boolean result = hdztRankingThriftClient.notifySettleWithRetry(attr.getActId(), promoteRankId, 1, tag, 3);
            if (!result) {
                throw new RuntimeException("结算严重错误,通知结算失败,请触发重试");
            }
        }

        //防止时序问题，要设置pk的promote key没结算出来，先暂存,等结算事件通知的时候，再调用doPkSettle 设置pk顺序
        String redisGroup = getRedisGroupCode(event.getActId());
        String pkMemberHashKey = makePkMemberHashKey(attr);

        for (String rankPhase : notifyPkMembers.keySet()) {
            actRedisDao.getRedisTemplate(redisGroup).opsForHash().put(pkMemberHashKey, rankPhase, notifyPkMembers.get(rankPhase));
            log.info("save notifyPkMembers,key:{},members:{}", pkMemberHashKey, notifyPkMembers.get(rankPhase));
        }

    }


    public void doPkSettle(PromotTimeEnd event, WhiteListPromoteComponentAttr attr) {
        String pkSettleKey = event.getRankId() + "_" + event.getPhaseId();
        if (!attr.getSettlePkRankPhaseId().contains(pkSettleKey)) {
            log.info("not my duty return,rankId:{},phaseId:{}", event.getRankId(), event.getPhaseId());
            return;
        }
        String redisGroup = getRedisGroupCode(event.getActId());
        String key = makePkMemberHashKey(attr);
        String pkMembers = Convert.toString(actRedisDao.getRedisTemplate(redisGroup).opsForHash().get(key, pkSettleKey));
        if (StringUtil.isEmpty(pkMembers)) {
            log.info("not found pkMembers,actId:{},key:{}", event.getActId(), pkSettleKey);
            return;
        }


        String seq = makeKey(attr, String.format("pksettle-%s-%s", event.getRankId(), event.getPhaseId()));
        boolean result = hdztRankingThriftClient.setDynamicPkMembers(event.getActId(), event.getRankId(), event.getPhaseId(), pkMembers, attr.getSetPkOpUid(), seq, 3);
        if (!result) {
            throw new RuntimeException("结算严重错误,设置pk对阵失败,请触发重试");
        }
        String msg = buildRuliuMsg(attr.getActId(), false, attr.getSrcRankId(), attr.getSrcPhaseId(), "白名单晋级结算成功");
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
    }

    private String makePkMemberHashKey(WhiteListPromoteComponentAttr attr) {
        return makeKey(attr, attr.getPkMemberHashKey());
    }

    private String buildRuliuMsg(long actId, boolean error, long rankId, long phaseId, String msg) {
        ActivityInfoVo hdztActivity = hdztRankingThriftClient.queryActivityInfo(actId);

        return "### <font color=\"" + (error ? "red" : "green") + "\">【中控->晋级结算" + (error ? "错误告警" : "通知") + "】</font>\n" + "#### [" + actId + "]" + hdztActivity.getActName() + "\n" +
                "榜单：" + rankId + "\n" +
                "阶段：" + phaseId + "\n" +
                "信息：" + msg + "\n";
    }

    /**
     * 查看中奖名单,给产品线下发奖
     */
    @GetMapping("/querySetting/{actId}")
    public Response<List<String>> querySetting(@PathVariable long actId, long index) {
        WhiteListPromoteComponentAttr attr = getComponentAttr(actId, index);

        List<String> result = Lists.newArrayList();

        List<PromoteItemConfig> configs = attr.getPromoteConfig();
        configs = configs.stream().sorted(Comparator.comparing(PromoteItemConfig::getPkSort)).collect(Collectors.toList());
        for (int i = 0; i < configs.size() - 1; i++) {
            PromoteItemConfig config1 = configs.get(i);
            PromoteItemConfig config2 = configs.get(i + 1);

            String member1 = config1.getExpectedMemberId();
            String asId1 = member1;
            if (StringUtil.isNotBlank(member1)) {
                asId1 = commonService.getAsid(Convert.toLong(member1.split("_")[0], 0)) + "";
            }

            String member2 = config2.getExpectedMemberId();
            String asId2 = member2;
            if (StringUtil.isNotBlank(member2)) {
                asId2 = commonService.getAsid(Convert.toLong(member2.split("_")[0], 0)) + "";
            }

            String member1Name = memberInfoService.getMemberInfo(0L, RoleType.HALL, member1).getName();
            String member2Name = memberInfoService.getMemberInfo(0L, RoleType.HALL, member2).getName();

            String content = String.format("%s|%s(%s,【%s】)  VS  %s(%s,【%s】)|%s",
                    member1Name, member1, asId1, config1.getLeftMemberRankSort(), member2, asId2, config2.getLeftMemberRankSort(), member2Name);
            result.add(content);

            i++;
        }


        return Response.success(result, attr.getExtjson());
    }
}
