package com.yy.gameecology.common.utils;

import com.yy.gameecology.common.bean.ClientInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR> 2023/6/16
 */
public class WebUtil {

    public static final String YY_HEADER_SID = "YYHeader-Sid";

    public static final String YY_HEADER_SSID = "YYHeader-Ssid";

    public static final String YY_HEADER_APP = "x-fts-host-name";

    public static final String YY_HEADER_PLATFORM = "x-fts-platform";

    public static final String YY_HEADER_VERSION = "x-fts-host-version";

    public static final String YY_HEADER_HDID = "x-fts-hdid";

    public static final String YY_GAME_TYPE = "x-fts-game-type";

    public static final String YY_HEADER_HDID_0 = "YYHeader-y0";

    public static String getIp(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String ip = request.getHeader("x-forwarded-for");
        if (StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("x-real-ip");
        }
        if (StringUtils.isEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (StringUtils.isNotEmpty(ip) && ip.contains(",")) {
            return ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }

    public static ClientInfo getClientInfo(HttpServletRequest request) {
        if (request == null) {
            return null;
        }
        String app = request.getHeader(YY_HEADER_APP);
        String platform = request.getHeader(YY_HEADER_PLATFORM);
        String version = request.getHeader(YY_HEADER_VERSION);
        String hdid = request.getHeader(YY_HEADER_HDID);
        if (StringUtils.isEmpty(hdid)) {
            hdid = request.getHeader(YY_HEADER_HDID_0);
        }
        return new ClientInfo(platform, version, hdid, getIp(request), app);
    }

    public static ClientInfo getClientInfo() {
        HttpServletRequest req = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        return getClientInfo(req);
    }

}
