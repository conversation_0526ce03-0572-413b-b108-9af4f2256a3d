package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.bean.SettleViewConfig;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.db.mapper.gameecology.GeActAttrMapper;
import com.yy.gameecology.common.db.model.gameecology.*;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.HdzjComponentAttrTypes;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.ComponentAttrChecker;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2019/8/29
 */
@Component
@DependsOn("springBeanAwareFactory")
public class CacheService {

    private static final Logger log = LoggerFactory.getLogger(CacheService.class);

    @Autowired
    private GeActAttrMapper geActAttrMapper;

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private HdzjComponentAttrTypes hdzjComponentAttrTypes;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    private Map<Long, Map<String, String>> actAttrMap = new HashMap<>();

    //参与活动任务的成员配置 key actId_taskId_memberId
    private Map<String, ActTaskMember> actTaskMemberMap = Collections.emptyMap();

    //参与活动任务的成员配置 key actId_memberId
    private Map<String, List<ActTaskMember>> actMemberTaskMemberMap = Collections.emptyMap();

    //活动任务配置 key taskId
    private Map<Long, ActTask> actTaskMap = Collections.emptyMap();

    //活动任务配置 key actId_hdztRankId_hdztPhaseId
    private Map<String, List<ActTask>> actTaskRankIdPhaseIdMap = Collections.emptyMap();

    //活动任务子任务配置 key taskId
    private Map<Long, List<ActTaskItem>> actTaskItemMap = Collections.emptyMap();

    //活动任务奖励配置 key taskId
    private Map<Long, List<ActTaskAward>> actTaskAwardMap = Collections.emptyMap();

    //榜单展示配置
    private Map<Long, List<ActShowRankConfig>> actShowRankConfigMap = Maps.newLinkedHashMap();

    //挂件展示配置 第一层key actId 第二层key
    private Map<Long, Map<String, ActLayerViewDefine>> actLayerViewDefine = Maps.newLinkedHashMap();

    private Map<Long, Map<String, String>> actResourceMap = Collections.emptyMap();

    /**
     * 活动组件数据存储策略 key actId_cmptId_cmptIndex
     */
//    private Map<String, ComponentStoragePolicy> componentStoragePolicyMap = Maps.newLinkedHashMap();

    public static final String LIMIT_NOTIFY_EVENT_OBJECTS = "com.yy.gameecology.activity.bean.ChannelChatTextInnerEvent,";

    @PostConstruct
    @NeedRecycle(author = "liweizhou", notRecycle = true)
    public void init() {
        Clock clock = new Clock();
        try {
            try {
                if (Const.GEPM != null) {
                    Const.GEPM.reloadAll();
                }
            } catch (Exception e) {
                log.error("init load all ge parameter fail", e);
            }

            Set<Long> actIds = getEffectActIds();

            refreshActAttrMap(actIds);

//            intActTaskMemberCache();

//            initActTaskCache();

//            initActTaskItemCache();

//            initActTaskAwardCache();

            initShowRankConfig(actIds);

            initActLayerViewDefine(actIds);

            initActResourceMap(actIds);

            // 活动组件相关
            initHdzjComponentMaps();

            initHdzjActivityMap();
//            initComponentStoragePolicyMap();

            log.info("init ok:{}", clock.tag());
        } catch (Throwable t) {
            log.error("init exception err:{} {}", t.getMessage(), clock.tag());
        }
    }

    @Order(-3)
    @EventListener(ContextRefreshedEvent.class)
    @NeedRecycle(author = "liqingyang", notRecycle = true)
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() != null) {
            return;
        }

        Set<Long> actIds = getEffectActIds();
        initHdzjComponentAttrMaps(actIds);
    }

    @Scheduled(cron = "${local.cache.refresh.cron}")
    @ScheduledExt(historyRun = true)
    @NeedRecycle(author = "gouliping", notRecycle = true)
    public void refresh() {
        Clock clock = new Clock();
        try {
            try {
                if (Const.GEPM != null) {
                    Const.GEPM.reloadAll();
                }
            } catch (Exception e) {
                log.error("load all ge parameter fail", e);
            }

            Set<Long> actIds = getEffectActIds();

            refreshActAttrMap(actIds);

//            intActTaskMemberCache();

//            initActTaskCache();

//            initActTaskItemCache();

//            initActTaskAwardCache();

            initShowRankConfig(actIds);

            initActLayerViewDefine(actIds);

            initActResourceMap(actIds);

            // 活动组件相关
            initHdzjComponentMaps();
//            initHdzjComponentUiMaps();
            initHdzjComponentAttrMaps(actIds);

            initHdzjActivityMap();
//            initComponentStoragePolicyMap();

            log.info("refresh ok@counter:{}", clock.tag());
        } catch (Throwable t) {
            log.error("refresh exception err:{} {}", t.getMessage(), clock.tag());
        }
    }

    @NotNull
    private Set<Long> getEffectActIds() {
        var actInfos = hdztRankingThriftClient.queryEffectActInfos();
        Set<Long> actIds = new HashSet<>(actInfos.size() + 8);
        actInfos.forEach(act -> {
            actIds.add(act.getActId());
        });

        if (Const.GEPM != null) {
            String actIdConfig = Const.GEPM.getParamValue("load_cache_act_whitelist", StringUtils.EMPTY);
            if (!StringUtils.isEmpty(actIdConfig)) {
                Splitter.on(StringUtil.COMMA).trimResults().omitEmptyStrings().split(actIdConfig).forEach(actIdStr -> {
                    if (StringUtils.isNumeric(actIdStr)) {
                        actIds.add(Long.parseLong(actIdStr));
                    }
                });
            }
        }
        return actIds;
    }

    private void refreshActAttrMap(Set<Long> actIds) {
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("refreshActAttrMap skip empty actIds");
            return;
        }
        GeActAttrExample example = new GeActAttrExample();
        example.or().andActIdIn(List.copyOf(actIds));
        List<GeActAttr> geActAttrs = geActAttrMapper.selectByExample(example);
        actAttrMap = geActAttrs.stream().collect(Collectors.groupingBy(GeActAttr::getActId,
                Collectors.toMap(GeActAttr::getAttrName, GeActAttr::getAttrValue)));
    }

    public Map<Long, Map<String, String>> getActAttrMap() {
        return actAttrMap;
    }


    public List<SettleViewConfig> getSettleViewConfig(long actId) {
        String config = getActAttrValue(actId, GeActAttrConst.SETTLE_VIEW_CONFIG);
        if (StringUtil.isBlank(config)) {
            return null;
        }
        try {
            return JSON.parseArray(config, SettleViewConfig.class);
        } catch (Exception e) {
            log.error("getSettleViewConfig error,actId:{},e:{}", actId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 活动扩展配置
     */
    public String getActAttrValue(Long actId, String attrName) {
        return getActAttrValue(actId, attrName, "");
    }

    public String getActAttrValue(Long actId, String attrName, String defaultValue) {
        if (actAttrMap == null) {
            return defaultValue;
        }
        return actAttrMap.getOrDefault(actId, Maps.newConcurrentMap()).getOrDefault(attrName, defaultValue);
    }

    private void intActTaskMemberCache() {
        List<ActTaskMember> actTaskMembers = gameecologyDao.getAllActTaskMember();
        Map<String, ActTaskMember> tmpMap = Maps.newConcurrentMap();
        Map<String, List<ActTaskMember>> tmpActMemberTaskMemberMap = Maps.newConcurrentMap();
        for (ActTaskMember actTaskMember : actTaskMembers) {
            String key = actTaskMember.getActId() + "_" + actTaskMember.getTaskId() + "_" + actTaskMember.getMemberId();
            tmpMap.put(key, actTaskMember);

            String actMemberIdKey = actTaskMember.getActId() + "_" + actTaskMember.getMemberId();
            List<ActTaskMember> taskMembers = tmpActMemberTaskMemberMap.getOrDefault(actMemberIdKey, Lists.newArrayList());
            taskMembers.add(actTaskMember);
            tmpActMemberTaskMemberMap.put(actMemberIdKey, taskMembers);
        }

        actTaskMemberMap = tmpMap;
        actMemberTaskMemberMap = tmpActMemberTaskMemberMap;
    }

    private void initActTaskCache() {
        Map<Long, ActTask> tmpActTaskMap = Maps.newConcurrentMap();
        Map<String, List<ActTask>> tmpActTaskRankIdPhaseIdMap = Maps.newConcurrentMap();

        List<ActTask> actTasks = gameecologyDao.select(ActTask.class);
        for (ActTask actTask : actTasks) {
            tmpActTaskMap.put(actTask.getTaskId(), actTask);

            String key = actTask.getActId() + "_" + actTask.getHdztRankId() + "_" + actTask.getHdztPhaseId();
            List<ActTask> tasks = tmpActTaskRankIdPhaseIdMap.getOrDefault(key, Lists.newArrayList());
            tasks.add(actTask);
            tmpActTaskRankIdPhaseIdMap.put(key, tasks);
        }

        actTaskMap = tmpActTaskMap;
        actTaskRankIdPhaseIdMap = tmpActTaskRankIdPhaseIdMap;

    }

    private void initActTaskItemCache() {
        Map<Long, List<ActTaskItem>> tmpTaskItemMap = Maps.newConcurrentMap();
        List<ActTaskItem> actTaskItems = gameecologyDao.select(ActTaskItem.class);

        for (ActTaskItem taskItem : actTaskItems) {
            List<ActTaskItem> taskItems = tmpTaskItemMap.getOrDefault(taskItem.getTaskId(), Lists.newArrayList());
            taskItems.add(taskItem);
            tmpTaskItemMap.put(taskItem.getTaskId(), taskItems);
        }

        actTaskItemMap = tmpTaskItemMap;
    }

    private void initActTaskAwardCache() {
        Map<Long, List<ActTaskAward>> tmpActTaskAwardMap = Maps.newConcurrentMap();
        List<ActTaskAward> awards = gameecologyDao.select(ActTaskAward.class);
        for (ActTaskAward award : awards) {
            List<ActTaskAward> actTaskAwards = tmpActTaskAwardMap.getOrDefault(award.getTaskId(), Lists.newArrayList());
            actTaskAwards.add(award);
            tmpActTaskAwardMap.put(award.getTaskId(), actTaskAwards);
        }

        actTaskAwardMap = tmpActTaskAwardMap;
    }

    /**
     * 参与任务的成员信息配置
     */
    public ActTaskMember getActTaskMember(long actId, long taskId, String memberId) {
        String key = actId + "_" + taskId + "_" + memberId;
        return actTaskMemberMap.get(key);
    }

    /**
     * 参与任务的成员信息配置
     */
    public List<ActTaskMember> getActTaskMembers(long actId, String memberId) {
        String key = actId + "_" + memberId;
        return actMemberTaskMemberMap.get(key);
    }


    /**
     * 获取活动任务配置
     */
    public ActTask getActTaskConfig(long actId, long taskId, Integer status) {
        ActTask actTask = actTaskMap.get(taskId);
        if (actTask != null && actTask.getStatus().equals(status)) {
            return actTask;
        }
        return null;
    }

    /**
     * 获取活动任务配置
     */
    public List<ActTask> getActTaskConfigs(long actId, long hdztRankId, long hdztPhaseId) {
        String key = actId + "_" + hdztRankId + "_" + hdztPhaseId;
        return actTaskRankIdPhaseIdMap.get(key);
    }

    /**
     * 子任务配置
     * key: level任务等级 value: 需要完成的任务列表,返回的结果保证从低级到高级的顺序
     */
    public Map<Integer, List<ActTaskItem>> getActTaskItem(long taskId) {
        List<ActTaskItem> allTaskItems = actTaskItemMap.get(taskId);
        if (CollectionUtils.isEmpty(allTaskItems)) {
            return Maps.newHashMap();
        }

        Map<Integer, List<ActTaskItem>> levelTaskItemMap = Maps.newHashMap();
        for (ActTaskItem taskItem : allTaskItems) {
            List<ActTaskItem> levelTaskItems = levelTaskItemMap.getOrDefault(taskItem.getLevel(), Lists.newArrayList());
            levelTaskItems.add(taskItem);
            levelTaskItemMap.put(taskItem.getLevel(), levelTaskItems);
        }

        return levelTaskItemMap;
    }

    /**
     * 获取任务配置的奖励
     */
    public List<ActTaskAward> getTaskAward(long taskId, int roleType) {
        List<ActTaskAward> awards = actTaskAwardMap.get(taskId);
        if (CollectionUtils.isEmpty(awards)) {
            return Lists.newArrayList();
        }
        return awards.stream().filter(x -> x.getRoleType() == roleType).collect(Collectors.toList());
    }


    /**
     * 活动组件, 第一层KEY： act_id, 第二层KEY：cmpt_id|cmpt_use_inx
     */
//    private Map<Long, Map<String, HdzjComponent>> hdzjComponentMaps = Maps.newLinkedHashMap();

    /**
     * 第一层 KEY：act_id，第二层 KEY：cmpt_id, 第三层 KEY cmpt_use_inx
     */
    private Map<Long, Map<Long, Map<Long, HdzjComponent>>> hdzjComponentMap = Collections.emptyMap();

    /**
     * 活动组件属性, 第一层KEY： act_id|cmpt_id|cmpt_use_inx, 第二层KEY：name，第二层值：value
     */
//    private Map<String, Map<String, String>> hdzjComponentAttrMaps = Maps.newLinkedHashMap();

    /**
     * key: act_id|cmpt_id|cmpt_use_inx
     */
    private Map<String, ComponentAttr> hdzjComponentAttrMap = Collections.emptyMap();

    /**
     * 活动组件UI, 第一层KEY： act_id|cmpt_id|cmpt_use_inx, 第二层KEY：name，第二层值：value
     */
    private Map<String, Map<String, String>> hdzjComponentUiMaps = Collections.emptyMap();


    /**
     * 初始化 hdzj_component 缓存数据
     * 使用 LinkedHashMap 保证结果顺序
     */
    private void initHdzjComponentMaps() {
        Clock clock = new Clock();
        try {
            List<HdzjComponent> list = gameecologyDao.select(HdzjComponent.class, null, "order by act_id, show_order, cmpt_id, cmpt_use_inx", false);
            var map = list.stream().collect(Collectors.groupingBy(HdzjComponent::getActId, Collectors.groupingBy(HdzjComponent::getCmptId, Collectors.toMap(HdzjComponent::getCmptUseInx, Function.identity()))));
            this.hdzjComponentMap = map;
            log.info("initHdzjComponentMaps ok -> size:{} {}", map.size(), clock.tag());
        } catch (Throwable t) {
            log.error("initHdzjComponentMaps exception@err:{} {}", t.getMessage(), clock.tag(), t);
        }
    }

    public Map<Long, Map<Long, HdzjComponent>> getHdzjComponentMap(long actId) {
        return this.hdzjComponentMap.getOrDefault(actId, Collections.emptyMap());
    }

    public HdzjComponent getHdzjComponent(long actId, long cmptId, long cmptUseInx) {
        return this.hdzjComponentMap.getOrDefault(actId, Collections.emptyMap()).getOrDefault(cmptId, Collections.emptyMap()).get(cmptUseInx);
    }

    public Map<Long, HdzjComponent> getHdzjComponentMap(long actId, long cmptId) {
        return this.hdzjComponentMap.getOrDefault(actId, Collections.emptyMap()).getOrDefault(cmptId, Collections.emptyMap());
    }

    /**
     * 初始化 hdzj_cmpt_ui 缓存数据
     * 使用 LinkedHashMap 保证结果顺序
     */
    private Map<String, Map<String, String>> initHdzjComponentUiMaps() {
        Clock clock = new Clock();
        try {
            Map<String, Map<String, String>> maps = Maps.newLinkedHashMap();
            List<HdzjComponentUi> list = gameecologyDao.select(HdzjComponentUi.class, null, "order by act_id, cmpt_id, cmpt_use_inx, name", false);
            if (list != null) {
                for (HdzjComponentUi item : list) {
                    String key = makeKey(item.getActId(), item.getCmptId(), item.getCmptUseInx());
                    Map<String, String> map = maps.computeIfAbsent(key, k -> Maps.newLinkedHashMap());
                    map.put(StringUtil.trim(item.getName()), StringUtil.trim(item.getValue()));
                }
            }
            this.hdzjComponentUiMaps = maps;
            log.info("initHdzjComponentUiMaps ok -> " + maps.size() + clock.tag());
            return maps;
        } catch (Throwable t) {
            log.error("initHdzjComponentUiMaps exception@err:{} {}", t.getMessage(), clock.tag(), t);
            return null;
        }
    }

    public Map<String, Map<String, String>> getHdzjComponentUiMaps() {
        return hdzjComponentUiMaps;
    }

    public Map<String, String> getHdzjComponentUiMap(Long actId, Long cmptId, Long cmptUseInx) {
        return getHdzjComponentUiMaps().getOrDefault(makeKey(actId, cmptId, cmptUseInx), Maps.newLinkedHashMap());
    }

    public String getHdzjComponentUiValue(Long actId, Long cmptId, Long cmptUseInx, String name) {
        return getHdzjComponentUiMap(actId, cmptId, cmptUseInx).getOrDefault(name, "");
    }

    /**
     * 初始化 hdzj_cmpt_attr 缓存数据
     * 使用 LinkedHashMap 保证结果顺序
     */
    private synchronized void initHdzjComponentAttrMaps(Set<Long> actIds) {
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("initHdzjComponentAttrMaps skip empty actIds");
            return;
        }
        Clock clock = new Clock();
        try {
            String afterWhere = " and status = 1 and act_id in (" + StringUtils.join(actIds, StringUtil.COMMA) + ") order by act_id, cmpt_id, cmpt_use_inx";
            List<HdzjComponent> components = gameecologyDao.select(HdzjComponent.class, null, afterWhere);

            afterWhere = " and act_id in (" + StringUtils.join(actIds, StringUtil.COMMA) + ") order by act_id, cmpt_id, cmpt_use_inx, name";
            List<HdzjComponentAttr> list = gameecologyDao.select(HdzjComponentAttr.class, null, afterWhere, false);
            Map<String, ComponentAttr> maps = new LinkedHashMap<>(list.size());
            if (CollectionUtils.isNotEmpty(list)) {
                Map<Long, Map<Long, Map<Long, Map<String, String>>>> attrMaps  = list.stream().collect(Collectors.groupingBy(HdzjComponentAttr::getActId, Collectors.groupingBy(HdzjComponentAttr::getCmptId, Collectors.groupingBy(HdzjComponentAttr::getCmptUseInx, Collectors.toMap(HdzjComponentAttr::getName, HdzjComponentAttr::getValue)))));
                for (var component : components) {
                    long actId = component.getActId(), cmptId = component.getCmptId(), cmptUseInx = component.getCmptUseInx();
                    var attrType = hdzjComponentAttrTypes.getComponentAttrType(cmptId);
                    if (attrType == null) {
                        if (!SysEvHelper.isHistory()) {
                            log.error("initHdzjComponentAttrMaps cannot find attr type of actId: {} componentId:{}", actId, cmptId);
                        } else {
                            log.warn("initHdzjComponentAttrMaps cannot find attr type of actId: {} componentId:{}", actId, cmptId);
                        }
                        continue;
                    }

                    Map<String, String> attrs = attrMaps.getOrDefault(actId, Collections.emptyMap()).getOrDefault(cmptId, Collections.emptyMap()).getOrDefault(cmptUseInx, Collections.emptyMap());

                    var attr = parseComponentAttr(actId, cmptId, cmptUseInx, attrType, attrs, component);
                    final String key = makeKey(actId, cmptId, cmptUseInx);
                    postProcessResource(actId, attrType, attr, 0);
                    maps.put(key, attr);
                }
            }

            hdzjComponentAttrMap = maps;
            log.info("initHdzjComponentAttrMaps ok -> {} {}", maps.size(), clock.tag());
        } catch (Throwable t) {
            log.error("initHdzjComponentAttrMaps exception@err:{} {}", t.getMessage(), clock.tag(), t);
        }
    }

    private  <T extends ComponentAttr> T parseComponentAttr(long actId, long cmptId, long cmptUseInx, Class<T> attrType, Map<String, String> attrs, HdzjComponent hdzjComponent) {
        List<String> list = new ArrayList<>(attrs.size());
        ReflectionUtils.doWithFields(attrType, field -> {
            String name = field.getName();
            if (attrs.containsKey(name)) {
                String value = StringUtil.trim(attrs.get(name));
                if (!value.isEmpty()) {
                    Class<?> type = field.getType();
                    if (type == String.class) {
                        list.add(String.format("\"%s\":%s", name, JSON.toJSONString(value)));
                    } else if (type == Date.class || type == Character.class || type == Character.TYPE || type == LocalTime.class || type == Duration.class) {
                        list.add(String.format("\"%s\":\"%s\"", name, value));
                    } else {
                        list.add(String.format("\"%s\":%s", name, value));
                    }
                }
            }
        });

        // 将 json 转成目标对象，并填充必要属性
        String json = "{" + String.join(",", list) + "}";
        T attr = JSON.parseObject(json, attrType);
        attr.setCmptId(cmptId);
        attr.setActId(actId);
        attr.setCmptUseInx(cmptUseInx);
        attr.setExtjson(StringUtil.trim(hdzjComponent.getExtjson()));
        return attr;
    }

    private void postProcessResource(long actId, Class<?> clazz, Object obj, int level) {
        if (obj == null) {
            return;
        }

        if (level > 3) {
            log.error("postProcessResource called with level={}", level);
            return;
        }

        final Map<String, String> resourceMap = getActResources(actId);

        ReflectionUtils.doWithFields(clazz, field -> {
            var annotation = AnnotationUtils.getAnnotation(field, ComponentAttrField.class);
            if (annotation == null) {
                return;
            }

            ReflectionUtils.makeAccessible(field);
            if (ComponentAttrCollector.PropType.RESOURCE.equals(annotation.propType()) && field.getType() == String.class) {
                Object value = ReflectionUtils.getField(field, obj);
                if (value instanceof String resourceKey) {
                    var newValue = resourceMap.getOrDefault(resourceKey, resourceKey);
                    ReflectionUtils.setField(field, obj, newValue);
                }
            }

            var subFields = annotation.subFields();
            if (ArrayUtils.isNotEmpty(subFields)) {
                // Map<Key, Resource> or Map<Key1, Map<Key2, Resource>>
                if (Map.class.isAssignableFrom(field.getType())) {
                    Arrays.stream(annotation.subFields())
                            .filter(subField -> Constant.VALUE.equals(subField.fieldName()) || Constant.MAP_LIST_VALUE.equals(subField.fieldName()))
                            .findFirst().ifPresent(subField -> {
                                Object value = ReflectionUtils.getField(field, obj);
                                if (!(value instanceof Map map)) {
                                    return;
                                }

                                if (MapUtils.isEmpty(map)) {
                                    return;
                                }

                                //
                                if (Constant.VALUE.equals(subField.fieldName())) {
                                    Class<?> subType = subField.type();
                                    if (subFields.length == 2) {
                                        if (ComponentAttrCollector.PropType.RESOURCE.equals(subField.propType()) && subType == String.class) {
                                            replaceMapValueResource(resourceMap, map);
                                        } else if (!subType.isPrimitive() && !ComponentAttrChecker.isBasicType(subType)) {
                                            map.values().forEach(mapValue -> postProcessResource(actId, subType, mapValue, level + 1));
                                        }
                                    } else if (subFields.length == 3) {
                                        for (var object : map.entrySet()) {
                                            if (object instanceof Map.Entry<?,?> entry) {
                                                if (entry.getValue() instanceof Map subMap) {
                                                    if (MapUtils.isEmpty(subMap)) {
                                                        continue;
                                                    }

                                                    if (ComponentAttrCollector.PropType.RESOURCE.equals(subField.propType()) && subType == String.class) {
                                                        replaceMapValueResource(resourceMap, subMap);
                                                    } else if (!subType.isPrimitive() && !ComponentAttrChecker.isBasicType(subType)) {
                                                        subMap.values().forEach(mapValue -> postProcessResource(actId, subType, mapValue, level + 1));
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else if (Constant.MAP_LIST_VALUE.equals(subField.fieldName())) {
                                    map.values().forEach(mapValue -> {
                                        if (mapValue instanceof List list) {
                                            if (CollectionUtils.isEmpty(list)) {
                                                return;
                                            }

                                            Class<?> subType = subField.type();
                                            if (ComponentAttrCollector.PropType.RESOURCE.equals(subField.propType()) && subType == String.class) {
                                                replaceListValueResource(resourceMap, list);
                                            } else if (!subType.isPrimitive() && !ComponentAttrChecker.isBasicType(subType)) {
                                                list.forEach(item -> postProcessResource(actId, subType, item, level + 1));
                                            }
                                        }
                                    });
                                }
                            });
                } else if (Collection.class.isAssignableFrom(field.getType())) {
                    Arrays.stream(annotation.subFields())
                            .filter(subField -> Constant.LIST_VALUE_TYPE.equals(subField.fieldName()))
                            .findFirst().ifPresent(subField -> {
                                Object value = ReflectionUtils.getField(field, obj);
                                if (!(value instanceof Collection collection)) {
                                    return;
                                }

                                Class<?> subType = subField.type();
                                if (subType == String.class && ComponentAttrCollector.PropType.RESOURCE.equals(subField.propType())) {
                                    if (value instanceof List list) {
                                        replaceListValueResource(resourceMap, list);
                                    }
                                } else if (!subType.isPrimitive() && !ComponentAttrChecker.isBasicType(subType)) {
                                    collection.forEach(item -> postProcessResource(actId, subType, item, level + 1));
                                }
                            });
                }
            }
        });
    }

    private void replaceListValueResource(Map<String, String> resourceMap, List list) {
        ListIterator<Object> iterator = list.listIterator();
        while (iterator.hasNext()) {
            Object object = iterator.next();
            if (object instanceof String resourceKey) {
                iterator.set(resourceMap.getOrDefault(resourceKey, resourceKey));
            }
        }
    }

    private void replaceMapValueResource(Map<String, String> resourceMap, Map map) {
        map.replaceAll((key, oldValue) -> {
            if (oldValue instanceof String resourceKey) {
                return resourceMap.getOrDefault(resourceKey, resourceKey);
            }

            return oldValue;
        });
    }

    public <T extends ComponentAttr> T getComponentAttr(long actId, long cmptId, long cmptUseInx) {
        final String key = makeKey(actId, cmptId, cmptUseInx);
        return (T) hdzjComponentAttrMap.get(key);
    }

    /**
     * 给cache做个保底的查询
     * @param actId
     * @param cmptId
     * @param cmptUseInx
     * @param clazz
     * @param hdzjComponent
     * @return
     * @param <T>
     */
    public <T extends ComponentAttr> T getComponentAttrFromDb(long actId, long cmptId, long cmptUseInx, Class<T> clazz, HdzjComponent hdzjComponent) {
        if (SysEvHelper.isHistory()) {
            log.info("getComponentAttrFromDb is called actId:{} cmptId:{} cmptUseInx:{}", actId, cmptId, cmptUseInx);
        } else {
            log.error("getComponentAttrFromDb is called actId:{} cmptId:{} cmptUseInx:{}", actId, cmptId, cmptUseInx);
        }
        HdzjComponentAttr me = new HdzjComponentAttr();
        me.setActId(actId);
        me.setCmptId(cmptId);
        me.setCmptUseInx(cmptUseInx);
        var componentAttrs = gameecologyDao.select(HdzjComponentAttr.class, me);
        Map<String, String> attrs = componentAttrs.stream().collect(Collectors.toMap(HdzjComponentAttr::getName, HdzjComponentAttr::getValue));

        var attr = parseComponentAttr(actId, cmptId, cmptUseInx, clazz, attrs, hdzjComponent);
        postProcessResource(actId, clazz, attr, 0);

        return attr;
    }

    /**
     * 平台活动, KEY： hdzj_activity
     */
    private Map<Long, HdzjActivity> hdzjActivityMap = Maps.newLinkedHashMap();


    /**
     * 初始化 hdzj_activity 缓存数据
     * 使用 LinkedHashMap 保证结果顺序
     */
    private Map<Long, HdzjActivity> initHdzjActivityMap() {
        Clock clock = new Clock();
        try {
            Map<Long, HdzjActivity> map = Maps.newLinkedHashMap();
            List<HdzjActivity> list = gameecologyDao.select(HdzjActivity.class, null, "order by act_id", false);
            if (list != null) {
                for (HdzjActivity item : list) {
                    Long actId = item.getActId();
                    map.put(actId, item);
                }
            }
            this.hdzjActivityMap = map;
            log.info("initHdzjActivityMap ok -> " + map.size() + clock.tag());
            return map;
        } catch (Throwable t) {
            log.error("initHdzjActivityMap exception@err:{} {}", t.getMessage(), clock.tag(), t);
            return null;
        }
    }

//    private void initComponentStoragePolicyMap() {
//        Clock clock = new Clock();
//        try {
//            Map<String, ComponentStoragePolicy> map = Maps.newLinkedHashMap();
//            List<ComponentStoragePolicy> list = gameecologyDao.select(ComponentStoragePolicy.class, null, "order by act_id", false);
//            if (list != null) {
//                for (ComponentStoragePolicy item : list) {
//                    map.put(makeKey(item.getActId(), item.getCmptId(), item.getCmptUseInx()), item);
//                }
//            }
//            this.componentStoragePolicyMap = map;
//            log.info("initComponentStoragePolicyMap ok -> " + map.size() + clock.tag());
//        } catch (Throwable t) {
//            log.error("initComponentStoragePolicyMap exception@err:{} {}", t.getMessage(), clock.tag(), t);
//        }
//    }


    private void initShowRankConfig(Set<Long> actIds) {
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("initShowRankConfig skip empty actIds");
            return;
        }
        Clock clock = new Clock();
        try {
            ActShowRankConfig where = new ActShowRankConfig();
            where.setStatus(1);
            String afterWhere = " and act_id in (" + StringUtils.join(actIds, StringUtil.COMMA) + ") order by sort asc ";
            List<ActShowRankConfig> configs = gameecologyDao.select(ActShowRankConfig.class, where, afterWhere);
            Map<Long, List<ActShowRankConfig>> tmp = Maps.newLinkedHashMap();
            for (ActShowRankConfig config : configs) {
                List<ActShowRankConfig> configList = tmp.getOrDefault(config.getActId(), Lists.newArrayList());
                configList.add(config);
                tmp.put(config.getActId(), configList);
            }
            actShowRankConfigMap = tmp;
        } catch (Throwable t) {
            log.error("initShowRankConfig exception@err:{} {}", t.getMessage(), clock.tag(), t);
        }
    }

    private void initActLayerViewDefine(Set<Long> actIds) {
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("initActLayerViewDefine skip empty actIds");
            return;
        }
        Clock clock = new Clock();
        try {
            ActLayerViewDefine where = new ActLayerViewDefine();
            where.setStatus(1);
            String afterWhere = " and act_id in (" + StringUtils.join(actIds, StringUtil.COMMA) + ") order by sort asc";
            List<ActLayerViewDefine> configs = gameecologyDao.select(ActLayerViewDefine.class, where, afterWhere);
            Map<Long, Map<String, ActLayerViewDefine>> tmp = Maps.newLinkedHashMap();
            for (ActLayerViewDefine config : configs) {
                Map<String, ActLayerViewDefine> configList = tmp.getOrDefault(config.getActId(), Maps.newLinkedHashMap());
                configList.put(config.getItemTypeKey(), config);
                tmp.put(config.getActId(), configList);
            }
            actLayerViewDefine = tmp;
        } catch (Throwable t) {
            log.error("initActLayerViewDefine exception@err:{} {}", t.getMessage(), clock.tag(), t);
        }
    }

    public Map<String, ActLayerViewDefine> getActLayerViewDefine(Long actId) {
        return actLayerViewDefine.getOrDefault(actId, Collections.emptyMap());
    }

    public boolean containsLayerViewDefine(Long actId) {
        return MapUtils.isNotEmpty(getActLayerViewDefine(actId));
    }

    private void initActResourceMap(Set<Long> actIds) {
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("initActResourceMap skip empty actIds");
            return;
        }

        Clock clock = new Clock();
        try {
            String afterWhere = " and act_id in (" + StringUtils.join(actIds, StringUtil.COMMA) + ") order by act_id, resource_key";
            var resources = gameecologyDao.select(ActResource.class, null, afterWhere);
            Map<Long, Map<String, String>> tmp = new HashMap<>(resources.size());
            for (ActResource resource : resources) {
                Map<String, String> map = tmp.computeIfAbsent(resource.getActId(), k -> new HashMap<>(resources.size()));
                map.put(resource.getResourceKey(), resource.getResourceUrl());
            }

            actResourceMap = tmp;
            log.info("initActResourceMap ok -> {} {}", actResourceMap.size(), clock.tag());
        } catch (Exception e) {
            log.error("initActResourceMap fail:", e);
        }
    }

    public Map<String, String> getActResourceMap(long actId) {
        return this.actResourceMap.getOrDefault(actId, Collections.emptyMap());
    }

    private Map<String, String> getActResources(long actId) {
        ActResource me = new ActResource();
        me.setActId(actId);
        var resources = gameecologyDao.select(ActResource.class, me);
        if (CollectionUtils.isEmpty(resources)) {
            return Collections.emptyMap();
        }

        return resources.stream().collect(Collectors.toMap(ActResource::getResourceKey, ActResource::getResourceUrl));
    }

    @VisibleForTesting
    public void forceReloadActivityAndCmp() {
        Set<Long> actIds = getEffectActIds();
        initHdzjComponentMaps();
//        initHdzjComponentUiMaps();
        initHdzjComponentAttrMaps(actIds);
        initHdzjActivityMap();
    }

    /**
     * 获取活动榜单展示配置
     *
     * @param actId 活动id
     */
    public List<ActShowRankConfig> getActShowRankConfig(Long actId) {
        return actShowRankConfigMap.get(actId);
    }


    public Map<Long, HdzjActivity> getHdzjActivityMap() {
        return hdzjActivityMap;
    }

    public HdzjActivity getHdzjActivity(long actId) {
        return hdzjActivityMap.get(actId);
    }

    /**
     * 获取组件存储策略
     */
    public ComponentStoragePolicy getComponentStoragePolicy(long actId, long cmptId, long cmptIndex) {
        ComponentStoragePolicy me = new ComponentStoragePolicy();
        me.setActId(actId);
        me.setCmptId(cmptId);
        me.setCmptUseInx(cmptIndex);
        return gameecologyDao.selectOne(ComponentStoragePolicy.class, me, StringUtils.EMPTY);
    }


    public static String makeKey(Long n1, Long n2) {
        return n1 + "|" + n2;
    }

    public static String makeKey(Long n1, Long n2, Long n3) {
        return n1 + "|" + n2 + "|" + n3;
    }

    public static String makeKey(Long n1, Long n2, Long n3, Long n4) {
        return n1 + "|" + n2 + "|" + n3 + "|" + n4;
    }

    public static String makeKey(Long n1, Long n2, Long n3, Long n4, Long n5) {
        return n1 + "|" + n2 + "|" + n3 + "|" + n4 + "|" + n5;
    }
}
