package com.yy.gameecology.activity;

import aj.org.objectweb.asm.*;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.redis.AnchorCustomTaskComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/7/24 10:10
 **/
@Slf4j
public class SvcMethodUsageAnalyzer {
    public static void main(String[] args) throws IOException {
        new SvcMethodUsageAnalyzer(AnchorCustomTaskComponent.class).analyze(Arrays.asList("onRankingScoreChanged"));
    }

    // 幂等组件方法

    private final Class<?> targetClz;

    public SvcMethodUsageAnalyzer(Class<?> clz) {
        // 需要分析的类
        this.targetClz = clz;
    }

    public void analyze(List<String> rootMethods) throws IOException {
        String className = targetClz.getName();
        // 使用ASM解析字节码
        ClassReader classReader = new ClassReader(className);
        classReader.accept(new MethodCallVisitor(), ClassReader.EXPAND_FRAMES);

        // 创建MethodCallTreeVisitor实例
        MethodCallTreeVisitor visitor = new MethodCallTreeVisitor(className);

        // 解析字节码并构建方法调用树
        classReader.accept(visitor, 0);

        // 输出方法调用树
        // System.out.println(visitor.getMethodCallTree());
        Map<String, String> methodCalls = visitor.getMethodCalls();

        for (String rootMethod : rootMethods) {
            String trace = "";
            boolean hasSvcMethodCall = hasSvcMethodCall(trace, rootMethod, methodCalls);
            if (hasSvcMethodCall) {
                log.warn("className={} hasSvcMethodCall,rootMethod={}", className, rootMethod);
            }
        }
    }

    private static final Set<String> HANDLED_METHODS = new HashSet<>();

    private static boolean hasSvcMethodCall(String trace, String rootMethod, Map<String, String> methodCalls) {
        String callMethods = methodCalls.get(rootMethod);
        if (StringUtils.isEmpty(callMethods)) {
            return false;
        }

        if (HANDLED_METHODS.contains(rootMethod)) {
            // 解决循环调用
            return false;
        }
        HANDLED_METHODS.add(rootMethod);
        if (StringUtil.isEmpty(trace)) {
            trace = rootMethod;
        } else {
            trace = trace + "," + rootMethod;
        }

        for (String method : callMethods.split(StringUtil.COMMA)) {
            if (StringUtil.isEmpty(method)) {
                continue;
            }
            if (CHECK_METHOD.equals(method)) {
                // 后面的方法不需要处理了
                return false;
            }

            if (SVC_METHODS.contains(method)) {
                log.info("trace={},svc method ={}", trace, method);
                return true;
            }

            if (hasSvcMethodCall(trace, method, methodCalls)) {
                return true;
            }
        }

        return false;
    }

    private static final String CHECK_METHOD = "withRetryCheck";

    private static final List<String> SVC_METHODS = Arrays.asList(
            "broadcastAll", "broadcast", "broadcastFamily", "unicastUid",
            "broadcastSub", "broadcastTop", "broadcastTemplate", "broadcastAllChanelsInSkillCard",
            "broadcastFamilyInSkillCard", "broadcastTemplate");

    // 自定义的ClassVisitor类
    private static class MethodCallVisitor extends ClassVisitor {
        public MethodCallVisitor() {
            super(Opcodes.ASM7);
        }

        @Override
        public MethodVisitor visitMethod(int access, String name, String descriptor, String signature, String[] exceptions) {
            // 输出方法调用关系
            // System.out.println("Method " + name + " is called.");

            return super.visitMethod(access, name, descriptor, signature, exceptions);
        }

        @Override
        public void visitOuterClass(String owner, String name, String descriptor) {
            // System.out.println("owner " + owner + "name " + name + " descriptor " + descriptor);
            super.visitOuterClass(owner, name, descriptor);
        }

        @Override
        public void visitSource(String source, String debug) {
            // System.out.println("visitSource " + source + " debug " + debug);
            super.visitSource(source, debug);
        }

        @Override
        public FieldVisitor visitField(int access, String name, String descriptor, String signature, Object value) {
            // broadCastHelpService
            // System.out.println("visitField access " + access + " name " + name + " descriptor =" + descriptor + " signature =" + signature + " value" + value);

            return super.visitField(access, name, descriptor, signature, value);
        }
    }

    // 自定义的ClassVisitor类
    private static class MethodCallTreeVisitor extends ClassVisitor {
        private final String className;
        private final Map<String, String> methodCalls;
        private final StringBuilder methodCallTree;

        public MethodCallTreeVisitor(String className) {
            super(Opcodes.ASM7);
            this.className = className;
            this.methodCalls = new HashMap<>();
            this.methodCallTree = new StringBuilder();
        }

        @Override
        public MethodVisitor visitMethod(int access, String name, String descriptor, String signature, String[] exceptions) {
            // 将方法添加到方法调用树中
            methodCalls.put(name, "");

            return new MethodCallVisitor2(name);
        }

        @Override
        public void visitEnd() {
            // 构建方法调用树的树形结构
            for (Map.Entry<String, String> entry : methodCalls.entrySet()) {
                methodCallTree.append(entry.getKey());
                methodCallTree.append("->");
                methodCallTree.append(entry.getValue());
                methodCallTree.append("\n");
            }
        }

        public String getMethodCallTree() {
            return methodCallTree.toString();
        }

        public Map<String, String> getMethodCalls() {
            return methodCalls;
        }

        // 自定义的MethodVisitor类
        private class MethodCallVisitor2 extends MethodVisitor {
            private final String methodName;

            public MethodCallVisitor2(String methodName) {
                super(Opcodes.ASM7);
                if (methodName.contains("$")) {
                    // lambda方式调用
                    this.methodName = methodName.split("\\$")[1];
                } else {
                    this.methodName = methodName;
                }
            }

            @Override
            public void visitMethodInsn(int opcode, String owner, String name, String descriptor, boolean isInterface) {
                // 在方法调用树中记录调用关系
                if (methodCalls.containsKey(methodName)) {
                    String calls = methodCalls.get(methodName);
                    calls += name + ",";
                    methodCalls.put(methodName, calls);
                }

                super.visitMethodInsn(opcode, owner, name, descriptor, isInterface);
            }
        }
    }
}
