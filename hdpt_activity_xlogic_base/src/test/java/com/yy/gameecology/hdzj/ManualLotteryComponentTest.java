package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.redis.ManualLotteryComponent;
import com.yy.gameecology.hdzj.element.component.attr.ManualLotteryComponentAttr;
import com.yy.thrift.broadcast.Template;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class ManualLotteryComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    AtomicLong counter = new AtomicLong(0);

    @Autowired
    private ManualLotteryComponent manualLotteryComponent;

    @Test
    public void onSendGiftEvent() throws Exception {
        ManualLotteryComponentAttr attr = manualLotteryComponent.getComponentAttr(2022033001, 1);
        SendGiftEvent event = new SendGiftEvent();
        event.setGiftId("A");
        event.setTemplate(Template.Gamebaby);
        event.setGiftNum(500L);
        event.setSeq(DateUtil.today("yyyyMMddHHmmsss-") + counter.incrementAndGet());
        event.setSendUid(248699782L);
        event.setRecvUid(248697624L);
        event.setSid(39626143L);
        event.setSsid(39626143L);
        manualLotteryComponent.onSendGiftEvent(event, attr);

        event.setGiftId("A");
        event.setTemplate(Template.Gamebaby);
        event.setGiftNum(520L);
        event.setSeq(DateUtil.today("yyyyMMddHHmmsss-") + counter.incrementAndGet());
        manualLotteryComponent.onSendGiftEvent(event, attr);

        event.setGiftId("A");
        event.setTemplate(Template.Gamebaby);
        event.setGiftNum(521L);
        event.setSeq(DateUtil.today("yyyyMMddHHmmsss-") + counter.incrementAndGet());
        manualLotteryComponent.onSendGiftEvent(event, attr);

        event.setGiftId("A");
        event.setTemplate(Template.Gamebaby);
        event.setGiftNum(1045L);
        event.setSeq(DateUtil.today("yyyyMMddHHmmsss-") + counter.incrementAndGet());
        manualLotteryComponent.onSendGiftEvent(event, attr);

        event.setGiftId("B");
        event.setTemplate(Template.Gamebaby);
        event.setGiftNum(1L);
        event.setSeq(DateUtil.today("yyyyMMddHHmmsss-") + counter.incrementAndGet());
        manualLotteryComponent.onSendGiftEvent(event, attr);

        event.setGiftId("B");
        event.setTemplate(Template.Gamebaby);
        event.setGiftNum(10L);
        event.setSeq(DateUtil.today("yyyyMMddHHmmsss-") + counter.incrementAndGet());
        manualLotteryComponent.onSendGiftEvent(event, attr);

        System.out.println("done!");
    }


    @Test
    public void issueLotteryChance() {
        ManualLotteryComponentAttr attr = manualLotteryComponent.getComponentAttr(2022033001, 1);

        List<HdztAwardLotteryMsg.Award> list = Lists.newArrayList();
        HdztAwardLotteryMsg.Award a1 = new HdztAwardLotteryMsg.Award();
        a1.setUid(12345);
        a1.setGiftCode("A");
        a1.setGiftNum(10);
        list.add(a1);

        HdztAwardLotteryMsg.Award a2 = new HdztAwardLotteryMsg.Award();
        a2.setUid(248699782L);
        a2.setGiftCode("ACT2022033001_XYZY");
        a2.setGiftNum(10);
        list.add(a2);

        HdztAwardLotteryMsg.Award a3 = new HdztAwardLotteryMsg.Award();
        a3.setUid(248697624L);
        a3.setGiftCode("ACT2022033001_XYZY");
        a3.setGiftNum(20);
        list.add(a3);

        HdztAwardLotteryMsg event = new HdztAwardLotteryMsg();
        event.setBusiId(400);
        event.setSeq(DateUtil.today("yyyyMMddHHmmsss-") + counter.incrementAndGet());
        event.setData(list);

        manualLotteryComponent.issueLotteryChance(event, attr);
    }
}
