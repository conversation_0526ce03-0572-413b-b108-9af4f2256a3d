package com.yy.gameecology.activity.config.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import java.io.Serializable;


@Configuration
public class RedisConfig0 extends BaseRedisConfig {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public RedisConfig0(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        super(builderCustomizers);
    }

    /* ##############################  gameecology redis  ############################# */

    @Bean
    @ConfigurationProperties("gameecology.group0.redis")
    public RedisProperties redisGroup0Properties() {
        return new RedisProperties();
    }

    @Bean
    public RedisConnectionFactory redisGroup0ConnectionFactory(RedisProperties redisGroup0Properties) {
        return new LettuceConnectionFactory(getRedisConfig(redisGroup0Properties), getClientConfig(redisGroup0Properties));
    }

    @Bean
    public StringRedisTemplate stringRedisGroup0Template(RedisConnectionFactory redisGroup0ConnectionFactory) {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(redisGroup0ConnectionFactory);
        return stringRedisTemplate;
    }

    @Bean
    public RedisTemplate<Serializable, Serializable> redisGroup0Template(RedisConnectionFactory redisGroup0ConnectionFactory) {
        RedisTemplate<Serializable, Serializable> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisGroup0ConnectionFactory);
        return redisTemplate;
    }

    @Bean
    public RedissonClient redissonClient0(RedisProperties redisGroup0Properties) {
        Config config = getRedissonConfig(redisGroup0Properties);
        config.setCheckLockSyncedSlaves(false);
        log.info("Creating RedissonClient for group0 with config: host={}, port={}, database={}",
                redisGroup0Properties.getHost(),
                redisGroup0Properties.getPort(),
                redisGroup0Properties.getDatabase());
        return Redisson.create(config);
    }

}
