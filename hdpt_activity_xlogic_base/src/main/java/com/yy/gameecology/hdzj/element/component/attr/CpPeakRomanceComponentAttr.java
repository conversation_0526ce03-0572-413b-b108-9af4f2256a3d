package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.ChampionCpShows;
import com.yy.gameecology.hdzj.element.component.attr.bean.LotteryAwardConfig;
import lombok.Data;

import java.util.List;


@Data
public class CpPeakRomanceComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务id")
    private long busiId ;

    @ComponentAttrField(labelText = "强厅角色")
    private long tingActor;

    @ComponentAttrField(labelText = "CP榜id")
    private long rankId;

    @ComponentAttrField(labelText = "CP榜阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "顶楼高度")
    private long heighLimit;


    @ComponentAttrField(labelText = "抽奖奖池ID")
    private long lotteryTaskId;

    @ComponentAttrField(labelText = "抽奖奖池映射，入场秀图标配置",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = LotteryAwardConfig.class)})
    private List<LotteryAwardConfig> awardConfigs;



    @ComponentAttrField(labelText = "获取webdb信息的templateType",remark = "交友 1 聊天室 810")
    private int templateType = 810;

    @ComponentAttrField(labelText = "广播模板", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broTemplate;

    @ComponentAttrField(labelText = "全服中奖记录topn")
    private int allAwardTopN=50;
}
