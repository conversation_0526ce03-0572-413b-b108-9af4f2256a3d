package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerLayout;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.bean.PairBean;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PersonalTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * desc: 个人专属任务，根据业务统计为每个人指定不同的完成目标数值
 * <p>
 * 完成任务广播协议
 * 1. uri = 100008,bannerId = 4, bannerType = 0 每个活动可以自定义
 *
 * @createBy 郭立平
 * @create 2022-03-31 17:36
 **/
@UseRedisStore
@Component
@RestController
@RequestMapping("/cmpt/PersonalTask")
public class PersonalTaskComponent extends BaseActComponent<PersonalTaskComponentAttr> {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    protected SignedService signedService;

    @Autowired
    protected EnrollmentNewService enrollmentService;

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    // 保存完成任务目标值的 hash key
    private static final String ENLIST_KEY = "enlist";

    // J结算防重 string key
    public static final String SETTLE_DONE_KEY = "settle_done";

    // J结算防重 string key
    public static final String TASK_FINISH_KEY = "task_finish";

    private static final String MILLION_UNIT = "万";
    private static final String BILLION_UNIT = "亿";
    private static final BigDecimal ONE_HUNDRED_THOUSAND = new BigDecimal(100000);
    private static final BigDecimal ONE_HUNDRED_MILLION = new BigDecimal(100000000);
    private static final BigDecimal TEN_THOUSAND = new BigDecimal(10000);

    @Override
    public Long getComponentId() {
        return ComponentId.PERSONAL_TASK;
    }

    /**
     * 发放奖励定时器
     */
    @Scheduled(cron = "*/5 * * * * ? ")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void giveAwards() {
        this.giveAwards(2, 3600, 60 * 24);
    }

    //榜单变化事件
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, PersonalTaskComponentAttr attr) {
        if (!attr.isMyDuty(event.getRankId(), event.getPhaseId())) {
            return;
        }

        Clock clock = new Clock();
        try {
            String memberId = event.getMember();
            long phaseScore = event.getPhaseScore();
            long myTarget = this.getPersonalTaskTarget(attr, memberId);
            PairBean taskScoreRate = attr.getTaskScoreRate();
            long currTaskScore = phaseScore * taskScoreRate.getSecond() / taskScoreRate.getFirst();
            if (currTaskScore < myTarget) {
                return;
            }

            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String finishKey = makeKey(attr, TASK_FINISH_KEY + ":" + memberId);
            String finishSummary = DateUtil.today() + ", " + event.getSeq() + ", " + myTarget + ", " + currTaskScore;
            if (!actRedisDao.setNX(groupCode, finishKey, finishSummary)) {
                return;
            }
            //配置不发广播
            if(attr.getBannerBroType() >= 0) {
                long targetScore = myTarget * taskScoreRate.getFirst() / taskScoreRate.getSecond();
                broBanner(attr, Long.parseLong(memberId), targetScore);
            }
            log.info("onRankingScoreChanged ok@finishKey:{}, finishSummary:{} {}", finishKey, finishSummary, clock.tag());
        } catch (Throwable t) {
            log.error("onRankingScoreChanged exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }

    //榜单变化事件
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onPhaseTimeEnd(PhaseTimeEnd event, PersonalTaskComponentAttr attr) {
        if (!attr.isMyDuty(event.getRankId(), event.getPhaseId())) {
            return;
        }

        Clock clock = new Clock();
        String subKeyTime = attr.getTimeKeyFormat().isEmpty() ? "" : DateUtil.today(attr.getTimeKeyFormat());
        String ekey = event.getEkey() + "#" + event.getSeq();

        try {
            // 所有榜成员都查出来
            QueryRankingByScoreResponse response = hdztRankService.queryRankingByScore(attr.getActId(), attr.getRankId(), attr.getPhaseId(), subKeyTime, HdztRankType.PHASE_ALL);
            if (response == null || response.code != 0 || CollectionUtils.isEmpty(response.rankingByScoreInfos)) {
                log.error("doSettle fail@ekey:{}, subKeyTime:{}, response:{} {}", ekey, subKeyTime, response, clock.tag());
                return;
            }

            // 计算奖励
            List<PersonalTaskAwardBean> personalTaskAwardBeanList = calcAward(event.getSeq(), ekey, attr, response.rankingByScoreInfos);

            List<AwardBean> awards = personalTaskAwardBeanList.stream()
                    .filter(item -> !CollectionUtils.isEmpty(item.getTaskPackageIds()))
                    .map(item -> (AwardBean) item)
                    .collect(Collectors.toList());
            // 防重处理
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String doneSummary = DateUtil.today() + ", " + ekey;
            String doneKey = makeKey(attr, SETTLE_DONE_KEY);
            if (!actRedisDao.setNX(groupCode, doneKey, doneSummary)) {
                log.error("settle fail@duplicated settle, ekey:{}, doneKey:{} {}", ekey, doneKey, clock.tag());
                return;
            }

            // 保存奖励
            this.saveAward(attr, ekey, awards);

            //数据上报
            BusiId busiId = BusiId.findByValue(Math.toIntExact(attr.getBusiId()));
            long time = commonService.getNow(attr.getActId()).getTime();

            for (PersonalTaskAwardBean awardBean : personalTaskAwardBeanList) {
                if (CollectionUtils.isEmpty(awardBean.getTaskPackageIds())) {
                    continue;
                }
                //注意 : 记录里的extInt 放大100倍
                bigDataService.saveNoRankDataToFile("", attr.getActId(),
                        busiId, time, awardBean.getReceiver(), RoleType.ANCHOR,
                        awardBean.getAward(), BigDataScoreType.PERSONAL_TASK_SETTLE, awardBean.getTargetScore() + "",
                        Convert.toInt(awardBean.getPercentage() * 100), awardBean.getScore());
            }


            log.info("doSettle ok@ekey:{}, subKeyTime:{}, size:{}/{} {}", ekey, subKeyTime, response.rankingByScoreInfos.size(), awards.size(), clock.tag());
        } catch (Throwable t) {
            log.error("doSettle exception@ekey:{}, subKeyTime:{}, err:{} {}", ekey, subKeyTime, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 获取我的任务状态
     */
    @RequestMapping("/getMyStatus")
    public Response getMyStatus(HttpServletRequest req, HttpServletResponse resp, long actId, @RequestParam(required = false, defaultValue = "1") long cmptUseInx, long id,
                                @RequestParam(required = false, defaultValue = "0") int type) {
        Clock clock = new Clock();
        try {
            return getMyStatus(actId, cmptUseInx, id, type);
        } catch (Exception e) {
            log.error("getMyStatus exception@actId:{}, cmptUseInx:{}, uid:{}, type:{}, err:{} {}", actId, cmptUseInx, id, e.getMessage(), type, clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 产品要的查询接口
     *
     * @param actId
     * @param cmptUseInx
     * @param scoreMultiple score对应的倍数，
     * @param awardMultiple 奖励对应的倍数
     * @param queryMode     0=查询全部,1=查询有完成个人任务， 2=查询未完成个人任务，3=获得奖金
     * @return
     */
    @RequestMapping("/getAllStatus")
    public Response getAllStatus(HttpServletRequest req, long actId, @RequestParam(required = false, defaultValue = "1") long cmptUseInx,
                                 @RequestParam(required = false, defaultValue = "1") int scoreMultiple,
                                 @RequestParam(required = false, defaultValue = "1") int awardMultiple,
                                 @RequestParam(required = false, defaultValue = "0") int queryMode) throws Exception {
        String ip = RequestUtil.getRealIp(req);
        Assert.isTrue(!SysEvHelper.isDeploy() || isOfficeIp(ip), "只允许办公网IP发起请求");

        PersonalTaskComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "活动参数异常");

        Clock tag = new Clock();
        String subKeyTime = attr.getTimeKeyFormat().isEmpty() ? "" : DateUtil.today(attr.getTimeKeyFormat());
        String ekey = UUID.randomUUID().toString();

        // 所有榜成员都查出来
        QueryRankingByScoreResponse response = hdztRankService.queryRankingByScore(attr.getActId(), attr.getRankId(), attr.getPhaseId(), subKeyTime, HdztRankType.PHASE_ALL);
        if (response == null || response.code != 0 || response.rankingByScoreInfos == null) {
            log.error("doSettle fail@ekey:{}, subKeyTime:{}, response:{}", ekey, subKeyTime, response);
            throw new BadRequestException("榜单数据请求异常");
        }
        tag.tag();
        //获取全部的目标分数
        String key = makeKey(attr, ENLIST_KEY);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        Map<Object, Object> targetScoreMap = actRedisGroupDao.hGetAll(groupCode, key);
        List<RankingByScoreInfo> rankingByScoreInfos = Lists.newArrayList(response.rankingByScoreInfos);
        Set<String> rankMemberSet = rankingByScoreInfos.stream().map(RankingByScoreInfo::getMemberId).collect(Collectors.toSet());
        List<RankingByScoreInfo> targetMembers = targetScoreMap.keySet().stream().filter(item -> !rankMemberSet.contains(item.toString()))
                .map(item -> new RankingByScoreInfo(item.toString(), 0, ""))
                .collect(Collectors.toList());

        rankingByScoreInfos.addAll(targetMembers);
        // 计算奖励
        List<PersonalTaskAwardBean> personalTaskAwardBeanList = calcAward(ekey, ekey, attr, rankingByScoreInfos);
        tag.tag();

        Predicate<PersonalTaskAwardBean> predicate = (bean) -> true;
        final int queryMode1 = 1, queryMode2 = 2, queryMode3 = 3;
        if (queryMode == queryMode1) {
            predicate = (bean) -> bean.getScore() >= bean.getTargetScore();
        } else if (queryMode == queryMode2) {
            predicate = (bean) -> bean.getScore() < bean.getTargetScore();
        } else if (queryMode == queryMode3) {
            predicate = (bean) -> bean.getAward() > 0;
        }
        //完成人数
        long count = personalTaskAwardBeanList.stream().filter(bean -> bean.getScore() >= bean.getTargetScore()).count();
        //获得的总奖金
        int totalAward = personalTaskAwardBeanList.stream().filter(bean -> bean.getAward() > 0).mapToInt(PersonalTaskAwardBean::getAward).sum() / awardMultiple;

        List<PersonalTaskAwardBean> queryPersonalTaskAwardBeanList = personalTaskAwardBeanList.stream().filter(predicate).collect(Collectors.toList());
        List<Long> uids = queryPersonalTaskAwardBeanList.stream().map(AwardBean::getReceiver).map(Long::parseLong).collect(Collectors.toList());
        tag.tag();
        List<List<Long>> partition = Lists.partition(uids, 500);
        Map<Long, WebdbUserInfo> userInfo = Maps.newHashMap();
        for (List<Long> uidLists : partition) {
            Map<Long, WebdbUserInfo> userInfoItem = commonService.batchYyUserInfo(uidLists);
            userInfo.putAll(userInfoItem);
        }
        tag.tag();
        List<PersonalTaskAwardVo> list = queryPersonalTaskAwardBeanList.stream()
                .map(item -> toPersonalTaskAwardVo(item, scoreMultiple, awardMultiple, userInfo))
                .sorted(Comparator.comparingLong(PersonalTaskAwardVo::getScore).reversed())
                .collect(Collectors.toList());
        PersonalTaskDataVo result = new PersonalTaskDataVo(count, totalAward, list);
        log.info("getAllStatus info@query:{},size:{} {}", req.getQueryString(), list.size(), tag.tag());
        return Response.success(result);
    }

    private PersonalTaskAwardVo toPersonalTaskAwardVo(PersonalTaskAwardBean awardBean, int scoreMultiple, int awardMultiple, Map<Long, WebdbUserInfo> userInfo) {
        WebdbUserInfo webdbUserInfo = userInfo.get(Long.valueOf(awardBean.getReceiver()));
        long uid = Long.parseLong(awardBean.getReceiver());
        String nick = webdbUserInfo == null ? "" : webdbUserInfo.getNick();
        PersonalTaskAwardVo vo = new PersonalTaskAwardVo();

        vo.setUid(uid);
        vo.setNick(nick);
        vo.setPercentage(awardBean.getPercentage());
        vo.setTargetScore(awardBean.getTargetScore() / scoreMultiple);
        vo.setScore(awardBean.getScore() / scoreMultiple);
        vo.setAward(awardBean.getAward() / awardMultiple);
        return vo;
    }

    /**
     * 获取我的任务状态
     * type 0 获取用户信息， type 1 获取频道信息
     */
    public Response<PersonalTaskStatus> getMyStatus(long actId, long cmptUseInx, long id, int type) {
        PersonalTaskComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        Clock clock = new Clock();
        try {

            if (id <= 0) {
                return Response.fail(-2, "暂无任务数据");
            }
            String memberId = String.valueOf(id);
            final long myTarget = getPersonalTaskTarget(attr, memberId);
            //起始等级
            final int myTargetLevel = attr.getTargetLevel(myTarget);
            long taskBarMaxScore = attr.getTaskBarMaxScore();
            String nickname = "";
            String logo = "";
            long uid = 0;
            long asid = 0;
            if(type == 0) {
                uid = id;
                UserBaseInfo ui = commonService.getUserInfo(id, true);
                nickname = ui.getNick();
                logo = ui.getLogo();
            } else {
                asid = id;
                ChannelBaseInfo ci = commonService.getChannelInfo(id, false);
                if (ci != null) {
                    asid = ci.getAsid();
                    nickname = ci.getName();
                    logo = ci.getLogo();
                }
            }

            //需要报名的场景
            if(attr.getAutoEnrol() == 0) {
                EnrollmentInfo ei = enrollmentService.tryGetFirstEnrolMemberCache(attr.getActId(), attr.getBusiId(), attr.getRoleType(), memberId);
                if (ei == null) {
                    PersonalTaskStatus status = new PersonalTaskStatus(uid, asid, nickname, logo, myTarget, taskBarMaxScore);
                    status.setNextScore(myTarget);
                    status.setAwardRate(attr.getScorePercentage(myTarget * 10));
                    status.setAwardName(attr.getPackageName());
                    return new Response<>(Response.OK, "无任务资格 或 暂无任务数据", status);
                }
            }

            String subKeyTime = attr.getTimeKeyFormat().isEmpty() ? "" : DateUtil.today(attr.getTimeKeyFormat());
            List<String> memberIds = Collections.singletonList(memberId);
            MemberRankingInfoRequest mriReq = new MemberRankingInfoRequest(actId, attr.getRankId(), attr.getPhaseId(), memberIds, subKeyTime,
                    "", "", Const.MEMBER_RANKING_QUERY_VALUE_SCORE, HdztRankType.PHASE_ALL, null);
            MemberRankingInfoResponse mriResp = hdztRankingThriftClient.getProxy().queryMemberRankingInfo(mriReq);

            if (mriResp.getCode() != 0) {
                return Response.fail(mriResp.code, mriResp.reason);
            }

            if (mriResp.memberRankingInfos == null || !mriResp.memberRankingInfos.containsKey(memberId)) {
                PersonalTaskStatus status = new PersonalTaskStatus(uid, asid, nickname, logo, myTarget, taskBarMaxScore);
                status.setNextScore(myTarget);
                status.setAwardRate(attr.getScorePercentage(myTarget * 10));
                status.setAwardName(attr.getPackageName());
                status.setMyTargetLevel(myTargetLevel);
                return new Response<>(Response.OK, "暂无分值", status);
            }

            MemberRankingInfo mri = mriResp.memberRankingInfos.get(memberId);
            long currTaskScore = attr.getTaskScore(mri.score);
            long nextAskScore = attr.getNextAskScore(mri.score, myTarget);
            double nextPercentage = attr.getNextPercentage(mri.score, myTarget);
            int curLevel = attr.getCurLevel(mri.score);
            int maxLevel = attr.getMaxLevel();
            if (currTaskScore < myTarget) {
                PersonalTaskStatus pti = new PersonalTaskStatus(uid, asid, nickname, logo, currTaskScore, myTarget, taskBarMaxScore,
                        attr.getPackageName(), attr.getScorePercentage(myTarget * 10), 0, nextAskScore, nextPercentage, curLevel, maxLevel);
                pti.setNextScore(nextAskScore + currTaskScore);
                pti.setMyTargetLevel(myTargetLevel);
                return new Response<>(Response.OK, "ok", pti);
            }

            long awardCount = attr.getAwardCount(mri.score);
            double percentage = attr.getScorePercentage(mri.score);
            PersonalTaskStatus pti = new PersonalTaskStatus(uid, asid, nickname, logo, currTaskScore, myTarget,
                    taskBarMaxScore, attr.getPackageName(), percentage, awardCount, nextAskScore, nextPercentage, curLevel, maxLevel);
            pti.setAwardLimit(awardCount == attr.getLimit());
            pti.setNextScore(nextAskScore + currTaskScore);
            pti.setLastLevel(pti.getNextScore() == attr.getTaskBarMaxScore());
            pti.setMyTargetLevel(myTargetLevel);
            return new Response<>(Response.OK, "ok", pti);
        } catch (Exception e) {
            log.error("getMyStatus exception@actId:{}, cmptUseInx:{}, id:{}, type:{}, err:{} {}", actId, cmptUseInx, id, type, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 计算奖励
     */
    private List<PersonalTaskAwardBean> calcAward(String eseq, String ekey, PersonalTaskComponentAttr attr, List<RankingByScoreInfo> realScores) {
        int inx = 0;
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        List<PersonalTaskAwardBean> result = Lists.newArrayList();

        //获取全部的目标分数
        String key = makeKey(attr, ENLIST_KEY);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        Map<Object, Object> targetScoreMap = actRedisGroupDao.hGetAll(groupCode, key);

        for (RankingByScoreInfo elm : realScores) {
            inx++;

            String myseq = java.util.UUID.randomUUID() + "#PCT" + (result.size() + 1);
            String receiver = attr.getReceiver(elm.memberId);

            // 若奖励百分比null或为0，没有奖励，直接跳过
            double percentage = attr.getScorePercentage(elm.score);
            Integer packageNum = (int) attr.getAwardCount(elm.score);
            //percentage = Convert.toInt(percentage);

            // 若没有完成个人的目标值，没有奖励，直接跳过
            long myCurrScore = attr.getTaskScore(elm.score);
            long myTargetScore = Convert.toLong(targetScoreMap.get(elm.memberId), attr.getDefaultTaskScore());
            Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
            if (packageNum > 0 && myCurrScore >= myTargetScore) {
                // 完成任务-准备好奖励内容
                long taskId = attr.getTaskId();
                long packageId = attr.getPackageId();
                Map<Long, Integer> packageIdNumMap = ImmutableMap.of(packageId, packageNum);
                taskPackageIds = ImmutableMap.of(taskId, packageIdNumMap);
                log.info("calcAward ok@{}/{}, memberId:{}, tid:{}, pid:{}, pnum:{}, eseq:{}, myseq:{}, ekey:{}",
                        result.size(), inx, elm.memberId, taskId, packageId, packageNum, eseq, myseq, ekey);
            } else if (percentage <= 0 || packageNum <= 0) {
                log.info("calcAward skip@inx:{}, no award for memberId:{} score:{}, percentage:{}, packageNum:{} eseq:{}, ekey:{}",
                        inx, elm.memberId, elm.score, percentage, packageNum, eseq, ekey);
            } else {
                log.info("calcAward ignore@inx:{}, memberId:{}, curr:{}/{} not finish task:{}, eseq:{}, ekey:{}",
                        inx, elm.memberId, myCurrScore, elm.score, myTargetScore, eseq, ekey);
            }

            PersonalTaskAwardBean personalTaskAward = new PersonalTaskAwardBean(myseq, attr.getBusiId(), receiver, time, taskPackageIds, percentage, myTargetScore, myCurrScore);
            result.add(personalTaskAward);
        }

        return result;
    }

    /**
     * 获取成员的分值
     */
    public long getPersonalTaskTarget(PersonalTaskComponentAttr attr, String memberId) {
        String key = makeKey(attr, ENLIST_KEY);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String target = actRedisGroupDao.hget(groupCode, key, memberId);
        return Convert.toLong(target, attr.getDefaultTaskScore());
    }

    /**
     * 任务达成广播
     */
    private void broBanner(PersonalTaskComponentAttr attr, long uid, long phaseScore) {
        UserBaseInfo userInfo = commonService.getUserInfo(uid, true);
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        long bannerId = attr.getBannerId();
        long bannerType = attr.getBannerType();
        String logo = StringUtil.isNotBlank(userInfo.getHdLogo()) ? userInfo.getHdLogo() : userInfo.getLogo();

        Map<String, String> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("memberId", uid+"");
        extMap.put("nickName",  new String(Base64.getDecoder().decode(nick), StandardCharsets.UTF_8));
        extMap.put("logo", logo);
        extMap.put("ext", amountConversion(new BigDecimal(phaseScore)));
        extMap.put("roleType", String.valueOf(attr.getRoleType()));

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setUserUid(uid).setUserNick(nick).setUserLogo(logo).setBannerId(bannerId).setBannerType(bannerType)
                .setUserScore(phaseScore).setJsonData(JSON.toJSONString(extMap))
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        UserCurrentChannel channel = commonService.getNoCacheUserCurrentChannel(uid);
        BusiId busiId = BusiId.findByValue((int) attr.getBusiId());

        if (channel != null) {
            broadCastHelpService.broadcast(attr.getActId(), busiId, attr.getBannerBroType(), channel.getTopsid(), channel.getSubsid(), bannerBroMsg);
            if(attr.getBannerSvag() != null && attr.getBannerSvag().size() > 0) {
                List<BannerSvagConfig> list = new ArrayList<>(attr.getBannerSvag().values());
                BannerSvagConfig svagConfig = list.get(0);

                AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();
                //svga内嵌文字
                List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, svagConfig, uid+"", phaseScore, userInfo);
                broSvgaConfig.setContentLayers(broContentLayers);
                //svga内嵌图片
                MemberInfo memberInfo = new MemberInfo();
                memberInfo.setHdLogo(logo);
                List<Map<String, String>> broImgLayers = getSvgaImageConfig(attr, memberInfo);
                broSvgaConfig.setImgLayers(broImgLayers);

                broSvgaConfig.setLoops(attr.getLoops());

                AppBannerLayout layout = new AppBannerLayout();
                layout.setType(attr.getLayoutType());
                if (StringUtil.isNotBlank(attr.getLayoutMargin())) {
                    layout.setMargin(JSON.parseObject(attr.getLayoutMargin(), new TypeReference<Map<String, List<Integer>>>() {
                    }));
                }
                broSvgaConfig.setLayout(layout);

                broSvgaConfig.setWhRatio(attr.getWhRatio());
                broSvgaConfig.setClickLayerName(svagConfig.getClickLayerName());
                broSvgaConfig.setSvgaURL(svagConfig.getSvgaURL());
                broSvgaConfig.setJumpSvgaURL(svagConfig.getJumpSvgaURL());
                broSvgaConfig.setMiniURL(svagConfig.getMiniURL());
                broSvgaConfig.setJumpMiniURL(svagConfig.getJumpMiniURL());

                //只广播本频道
               AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), java.util.UUID.randomUUID().toString(), attr.getBroBusiId(),
                        1, channel.getTopsid(), channel.getSubsid(), "",
                        Lists.newArrayList());
                appBannerEvent.setUid(uid);
                appBannerEvent.setUidList(Lists.newArrayList(uid));
                appBannerEvent.setContentType(6);
                appBannerEvent.setAppId(getTurnoverAppId((int)attr.getBusiId()));
                appBannerEvent.setSvgaConfig(broSvgaConfig);
                kafkaService.sendAppBannerKafka(appBannerEvent);
                log.info("personalTask app done seq:{}, member:{} event:{}", java.util.UUID.randomUUID().toString(), uid, JSON.toJSONString(appBannerEvent));
            }
        }
    }

    /**
     * svga 内嵌文字配置
     */
    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(PersonalTaskComponentAttr attr, BannerSvagConfig svagConfig, String member, long phaseScore, UserBaseInfo userInfo) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        String contentLayerCodes = svagConfig.getContentLayerCodes();
        if (StringUtil.isNotBlank(contentLayerCodes)) {
            String[] contentLayerCodeArr = contentLayerCodes.split(",");
            for (String contentLayerCode : contentLayerCodeArr) {
                Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
                BannerSvgaTextConfig textConfig = attr.getSvgaText().get(contentLayerCode);
                if (textConfig == null) {
                    continue;
                }
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();

                //配置动态替换文本
                String text = contextReplace(textConfig.getText(), member, phaseScore, userInfo);
                if (attr.getTextDynamicValue() != null) {
                    Map<String, String> replaceValue = attr.getTextDynamicValue();
                    for (String key : replaceValue.keySet()) {
                        text = text.replace(key, replaceValue.get(key));
                    }
                }
                appBannerSvgaText.setText(text);
                appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
                appBannerSvgaText.setGravity(textConfig.getGravity());
                if (StringUtil.isNotBlank(textConfig.getImages())) {
                    appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
                }
                if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                    appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
                }
                broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

                if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                    broContentLayers.add(broSvgaTextLayer);
                }
            }

        }

        return broContentLayers;
    }

    private String contextReplace(String context, String member, long phaseScore, UserBaseInfo userInfo) {
        long uid = Convert.toLong(member);
        context = context.replace("{nick}", HtmlUtils.htmlEscape(new String(Base64.getDecoder().decode(userInfo.getNick()), StandardCharsets.UTF_8)));
        context = context.replace("{rongyaozhi}", amountConversion(new BigDecimal(phaseScore)));
        return context;
    }

    private List<Map<String, String>> getSvgaImageConfig(PersonalTaskComponentAttr attr, MemberInfo memberInfo) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }

        Map<String, String> imageMap = attr.getSvgaImgLayers();
        for (String imageKey : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String image = imageMap.get(imageKey);
            String value = replaceImage(image, memberInfo);
            broImgLayer.put(imageKey, value);
            broImgLayers.add(broImgLayer);
        }

        return broImgLayers;
    }

    private String replaceImage(String context, MemberInfo memberInfo) {
        return context.replace("{header}", Convert.toString(memberInfo.getHdLogo()));
    }

    private int getTurnoverAppId(int busiId) {
        BusiId value = BusiId.findByValue(busiId);
        int appId = 0;
        switch (value) {
            case GAME_BABY:
                appId = 36;
                break;
            case MAKE_FRIEND:
                appId = 2;
                break;
            case SKILL_CARD:
                appId = 34;
                break;
            default:break;
        }
        return appId;
    }

    @RequestMapping("/testBroAppBanner")
    public Response<String> testBroAppBanner(long actId, long index, String member, String subChannel, long score) {
        if (SysEvHelper.isDeploy()) {
            if (!commonService.isGrey(actId)) {
                return Response.success("测试接口线上不执行");
            }
            if (!hdztRankingThriftClient.checkWhiteList(actId, RoleType.GUILD, subChannel)) {
                return Response.success("测试成员不在白名单");
            }
        }

        PersonalTaskComponentAttr attr = getComponentAttr(actId, index);

        broBanner(attr, Long.parseLong(member), score);

        return Response.success("调用成功");
    }

    /**
     * 将数字转换成以万为单位或者以亿为单位，因为在前端数字太大显示有问题
     * @param amount
     * @return
     */
    public static String amountConversion(BigDecimal amount){
        if (amount == null) {
            return null;
        }
        if (amount.abs().compareTo(ONE_HUNDRED_THOUSAND) < 0) {
            //如果小于10万
            return amount.stripTrailingZeros().toPlainString();
        }
        if (amount.abs().compareTo(ONE_HUNDRED_MILLION) < 0) {
            //如果大于10万小于1亿
            return amount.divide(TEN_THOUSAND, 4, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() + MILLION_UNIT;
        }
        return amount.divide(ONE_HUNDRED_MILLION, 4, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString() + BILLION_UNIT;
    }

}
