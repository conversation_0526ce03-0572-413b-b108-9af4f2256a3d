package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.hdzj.element.redis.TailLightComponent;
import com.yy.gameecology.hdzj.element.component.attr.TailLightComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties"})
public class TailLightComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TailLightComponent tailLightComponent;

    @Test
    public void tailLightWeb() throws Exception {

        TailLightComponentAttr attr = tailLightComponent.getComponentAttr(2021053001L, 1);


        while (true) {
            try {
                JSONObject jsonObject =  tailLightComponent.tailLightWeb(2021053001L,50046298,true);
                System.out.println(jsonObject);
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }

    }
    @Test
    public void tailLightPb() throws Exception {

        TailLightComponentAttr attr = tailLightComponent.getComponentAttr(2021053001L, 1);


        while (true) {
            try {
                GameecologyActivity.Act202008_TailLightResp.Builder  result =  tailLightComponent.tailLight(2021053001L,50046298,true);
                System.out.println(JsonFormat.printToString(result.build()));
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }

    }



}
