package com.yy.gameecology.activity.config.redis;

import com.yy.gameecology.common.support.SysEvHelper;
import io.lettuce.core.ReadFrom;
import io.lettuce.core.resource.DefaultClientResources;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.SentinelServersConfig;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.redisson.config.Config;
import java.util.ArrayList;
import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-07-23 15:35
 **/
public class BaseRedisConfig {

    protected final ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers;

    public BaseRedisConfig(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        this.builderCustomizers = builderCustomizers;
    }

    protected RedisConfiguration getRedisConfig(RedisProperties redisProperties) {
        if (redisProperties.getSentinel() != null) {
            RedisSentinelConfiguration config = new RedisSentinelConfiguration();
            config.master(redisProperties.getSentinel().getMaster());
            config.setSentinels(createSentinels(redisProperties.getSentinel()));
            config.setPassword(RedisPassword.of(redisProperties.getPassword()));
            config.setDatabase(redisProperties.getDatabase());
            return config;
        } else if (redisProperties.getCluster() != null) {
            RedisProperties.Cluster clusterProperties = redisProperties.getCluster();
            RedisClusterConfiguration config = new RedisClusterConfiguration(clusterProperties.getNodes());
            if (clusterProperties.getMaxRedirects() != null) {
                config.setMaxRedirects(clusterProperties.getMaxRedirects());
            }
            config.setPassword(RedisPassword.of(redisProperties.getPassword()));
            return config;
        } else {
            RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
            config.setHostName(redisProperties.getHost());
            config.setPort(redisProperties.getPort());
            config.setPassword(RedisPassword.of(redisProperties.getPassword()));
            config.setDatabase(redisProperties.getDatabase());
            return config;
        }
    }

    protected Config getRedissonConfig(RedisProperties redisProperties) {
        Config config = new Config();
        try {
            // 根据 Redis 配置类型创建相应的 Redisson 配置
            if (redisProperties.getCluster() != null &&
                    redisProperties.getCluster().getNodes() != null &&
                    !redisProperties.getCluster().getNodes().isEmpty()) {
                // 集群模式配置
                ClusterServersConfig clusterConfig = config.useClusterServers();
                redisProperties.getCluster().getNodes().forEach(node -> {
                    clusterConfig.addNodeAddress("redis://" + node);
                });
                if (StringUtils.hasText(redisProperties.getPassword())) {
                    clusterConfig.setPassword(redisProperties.getPassword());
                }
                clusterConfig.setConnectTimeout((int) redisProperties.getTimeout().toMillis());

            } else if (redisProperties.getSentinel() != null &&
                    redisProperties.getSentinel().getNodes() != null &&
                    !redisProperties.getSentinel().getNodes().isEmpty()) {
                // 哨兵模式配置
                SentinelServersConfig sentinelConfig = config.useSentinelServers();
                sentinelConfig.setMasterName(redisProperties.getSentinel().getMaster());
                redisProperties.getSentinel().getNodes().forEach(node -> {
                    sentinelConfig.addSentinelAddress("redis://" + node);
                });
                if (StringUtils.hasText(redisProperties.getPassword())) {
                    sentinelConfig.setPassword(redisProperties.getPassword());
                }
                sentinelConfig.setConnectTimeout((int) redisProperties.getTimeout().toMillis());
                sentinelConfig.setDatabase(redisProperties.getDatabase());
            } else {
                // 单机模式配置
                SingleServerConfig singleConfig = config.useSingleServer();
                String address = String.format("redis://%s:%d",
                        redisProperties.getHost(),
                        redisProperties.getPort());
                singleConfig.setAddress(address);
                if (StringUtils.hasText(redisProperties.getPassword())) {
                    singleConfig.setPassword(redisProperties.getPassword());
                }
                singleConfig.setConnectTimeout((int) redisProperties.getTimeout().toMillis());
                singleConfig.setDatabase(redisProperties.getDatabase());
                // 连接池配置
                if (redisProperties.getLettuce() != null &&
                        redisProperties.getLettuce().getPool() != null) {
                    RedisProperties.Pool pool = redisProperties.getLettuce().getPool();
                    singleConfig.setConnectionPoolSize(pool.getMaxActive());
                    singleConfig.setConnectionMinimumIdleSize(pool.getMinIdle());
                }
            }
            return config;
        }  catch (Exception e) {
            throw new RuntimeException("Failed to create RedissonClient", e);
        }
    }

    protected LettuceClientConfiguration getClientConfig(RedisProperties redisProperties) {
        LettucePoolingClientConfiguration.LettucePoolingClientConfigurationBuilder builder = LettucePoolingClientConfiguration.builder();
        RedisProperties.Pool poolProperties = redisProperties.getLettuce().getPool();
        if (poolProperties != null) {
            GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
            poolConfig.setMaxTotal(poolProperties.getMaxActive());
            poolConfig.setMaxIdle(poolProperties.getMaxIdle());
            poolConfig.setMinIdle(poolProperties.getMinIdle());
            if (poolProperties.getTimeBetweenEvictionRuns() != null) {
                poolConfig.setTimeBetweenEvictionRunsMillis(poolProperties.getTimeBetweenEvictionRuns().toMillis());
                poolConfig.setMinEvictableIdleTimeMillis(poolConfig.getTimeBetweenEvictionRunsMillis() * 3);
                poolConfig.setNumTestsPerEvictionRun(-1);
                poolConfig.setTestWhileIdle(true);
            }
            if (poolProperties.getMaxWait() != null) {
                poolConfig.setMaxWaitMillis(poolProperties.getMaxWait().toMillis());
            }
            builder.poolConfig(poolConfig);
        }
        if (redisProperties.isSsl()) {
            builder.useSsl();
        }
        if (redisProperties.getTimeout() != null) {
            builder.commandTimeout(redisProperties.getTimeout());
        }
        RedisProperties.Lettuce lettuce = redisProperties.getLettuce();
        if (lettuce.getShutdownTimeout() != null && !lettuce.getShutdownTimeout().isZero()) {
            builder.shutdownTimeout(lettuce.getShutdownTimeout());
        }
        if (SysEvHelper.isHistory()) {
            builder.readFrom(ReadFrom.MASTER);
        }
        builder.clientResources(DefaultClientResources.create());
        customize(builder);
        return builder.build();
    }

    private void customize(LettuceClientConfiguration.LettuceClientConfigurationBuilder builder) {
        this.builderCustomizers.orderedStream().forEach((customizer) -> customizer.customize(builder));
    }

    protected List<RedisNode> createSentinels(RedisProperties.Sentinel sentinel) {
        List<RedisNode> nodes = new ArrayList<>();
        for (String node : sentinel.getNodes()) {
            try {
                String[] parts = StringUtils.split(node, ":");
                Assert.state(parts.length == 2, "Must be defined as 'host:port'");
                nodes.add(new RedisNode(parts[0], Integer.valueOf(parts[1])));
            } catch (RuntimeException ex) {
                throw new IllegalStateException("Invalid redis sentinel " + "property '" + node + "'", ex);
            }
        }
        return nodes;
    }

}
