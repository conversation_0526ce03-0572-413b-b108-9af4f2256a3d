package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.ReduceAwardPoolReq;
import com.yy.gameecology.hdzj.bean.ReduceAwardPoolResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AwardPoolBalanceComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;

/**
 * desc: 改成用中台奖池实现
 *
 * <AUTHOR>
 * @date 2022-10-29 15:26
 **/
@Deprecated
@Component
public class AwardPoolBalanceComponent extends BaseActComponent<AwardPoolBalanceComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String BALANCE_KEY = "balance";

    @Override
    public Long getComponentId() {
        return ComponentId.AWARD_POOL_BALANCE;
    }

    public ReduceAwardPoolResp invokeReduce(ReduceAwardPoolReq req) {
        Clock clock = new Clock();
        log.info("invokeReduce begin,actId:{},busiId:{},mode:{},amount:{},seq:{}"
                , req.getActId(), req.getBusiId(), req.getDeduceMode(), req.getDeduceAmount(), req.getSeq());


        ReduceAwardPoolResp resp = null;
        try {
            resp = reduce(req);
        } catch (Exception e) {
            log.error("reduce error,attr:{},req:{},e:{}", JSON.toJSONString(req), e.getMessage(), e);
            resp = new ReduceAwardPoolResp();
            resp.setCode(999);
            resp.setMsg(e.getMessage());
        }

        log.info("invokeReduce ok,actId:{},busiId:{},mode:{},amount:{},seq:{},rsp:{},clock:{}"
                , req.getActId(), req.getBusiId(), req.getDeduceMode(), req.getDeduceAmount(), req.getSeq(), JSON.toJSONString(resp), clock.tag());
        return resp;
    }


    private ReduceAwardPoolResp reduce(ReduceAwardPoolReq req) {
        Assert.hasText(req.getBusiId(), "busiid 不能为空");
        Assert.isTrue(req.getActId() > 0, "actid 不能为空");
        Assert.hasText(req.getSeq(), "seq 不能为空");

        AwardPoolBalanceComponentAttr attr = getComponentAttr(req.getActId(), req.getIndex());

        ReduceAwardPoolResp resp = new ReduceAwardPoolResp();

        String group = getRedisGroupCode(attr.getActId());
        String seq = makeKey(attr, "seq:" + req.getBusiId() + ":" + req.getSeq());
        //总扣奖池限额控制
        long poolLimit = attr.getAwardPoolConfig();
        boolean canPartialAdd = req.getDeduceMode() == 0;
        List<Long> result = actRedisDao
                .incrValueWithLimitSeq(group, seq, buildKey(attr), req.getDeduceAmount(), poolLimit, canPartialAdd, attr.getSeqExpireSeconds());

        //扣减成功
        if (result.get(0) > 0) {
            long realReduce = result.get(0) == 1 ? req.getDeduceAmount() : result.get(1);
            resp.setDeduceAmount(realReduce);
            resp.setCode(0);
        } else {
            resp.setCode(1);
            resp.setMsg("余额不足，无法扣减");
        }
        return resp;
    }

    private String buildKey(AwardPoolBalanceComponentAttr attr) {
        return makeKey(attr, BALANCE_KEY);
    }
}
