package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.acttask.TaskPackageIdConfig;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardReplaceConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.TaskAttrConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-10-30 16:19
 **/
@Data
public class UserTaskAchieveComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "榜单id", remark = "监听榜单变化事件，用于中控组件过任务")
    private long rankId;

    @ComponentAttrField(labelText = "神豪标签榜单id",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class, remark = "配置了的显示神豪标签")
            }, remark = "配置了的显示神豪标签")
    private Set<Long> showRagRankId = Sets.newHashSet();

    @ComponentAttrField(labelText = "阶段id", remark = "监听榜单变化事件，用于中控组件过任务")
    private long phaseId;

    @ComponentAttrField(labelText = "广播模板", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broTemplate;

    @ComponentAttrField(labelText = "去重key过期时间(秒)", remark = "0==key不过期  ; >0设置过期时间为相应秒 ;  <0不做key去重检查")
    private long repeatedCheckExpire;

    @ComponentAttrField(labelText = "玩法提醒最小值", remark = "闭区间")
    private long noticeRangeMin;

    @ComponentAttrField(labelText = "玩法提醒最大值", remark = "开区间")
    private long noticeRangeMax;

    @ComponentAttrField(labelText = "玩法提醒url",remark = " {$offsetScore}==还差多少完成任务")
    private String noticeAppPopUrl;

    @ComponentAttrField(labelText = "任务贡献成员类型",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class, remark = "role type用string 兼容lua反序列化")
            }, remark = "抽奖受益者，多个用逗号隔开，用于从榜单变化事件中actor提取对象。如果不配置则不会给受益者发奖")
    private Set<String> contributeRoleType = Sets.newHashSet();

    @ComponentAttrField(labelText = "任务等级配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级ID(索引)，必须从0开始", remark = "从0开始"),
                    @SubField(fieldName = Constant.VALUE, type = TaskAttrConfig.class, labelText = "任务配置")})
    private Map<Long, TaskAttrConfig> taskConfig = Maps.newLinkedHashMap();

    public List<Long> getTaskConfigValues() {
        List<Long> score = Lists.newArrayList();
        for (Long leve : taskConfig.keySet()) {
            score.add(taskConfig.get(leve).getScore());
        }
        return score;
    }


    @ComponentAttrField(labelText = "任务奖励(废弃)",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级ID"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖池ID"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖包ID")
            })
    private Map<Long, Map<Long, Long>> taskAward = Maps.newHashMap();

    @ComponentAttrField(labelText = "任务奖励", subFields = {
            @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "任务等级ID"),
            @SubField(fieldName = Constant.VALUE, type = AwardReplaceConfig.class)
    })
    private Map<Long, AwardReplaceConfig> taskAward2 = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "总奖池上限(分)", remark = "如奖池不够，则主播用户平分，设置大于0时有效")
    private long taskAwardPoolLimit = 0;

    @ComponentAttrField(labelText = "任务奖励价格(分)", remark = "用于计算发奖总奖池上限",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖池ID"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖包ID"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖品价格")
            })
    private Map<Long, Map<Long, Long>> taskAwardPrice = Maps.newHashMap();

//    @ComponentAttrField(labelText = "奖池不足发放奖池ID", remark = "奖池不足，发放配置")
//    private long partialAwardTaskId = 0;
//
//    @ComponentAttrField(labelText = "奖池不足发奖奖包ID", remark = "奖池不足，发放配置")
//    private long partialAwardPackageId = 0;




    @ComponentAttrField(labelText = "移动端完成任务弹窗提醒url",remark = "自动替换参数 {$taskName}==当前完成任务名称 {$award}==奖品名称")
    private String taskCompleteAppPopUrl;

    @ComponentAttrField(labelText = "宝箱过期时间(s)")
    private long boxExpireSeconds;

    @ComponentAttrField(labelText = "移动端宝箱过期时间(s)",remark = "实际只控制显示和隐藏")
    private long appBoxExpireSeconds;

    @ComponentAttrField(labelText = "开启祝福口令抽奖模式",remark = "开启后，变为口令抽奖玩法，原有宝箱功能变为引导功能，实质不会抽奖")
    private boolean channelChatLotteryEnable;

    @ComponentAttrField(labelText = "祝福抽奖组件索引id")
    private long chatLotteryIndex;

    @ComponentAttrField(labelText = "宝箱抽奖奖池ID")
    private long boxTaskId;

    @ComponentAttrField(labelText = "单个宝箱开启次数上限")
    private long openBoxLimit;

    @ComponentAttrField(labelText = "任务宝箱",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "任务等级ID"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "广播范围", remark = " 2-子频道广播 3-顶级频道下所有子厅广播/家族下面所有子厅 4-全模板"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "宝箱扩展信息")
            })
    private Map<Long, Map<Long, String>> taskBox = Maps.newHashMap();

    @ComponentAttrField(labelText = "触发用户开宝箱直接发奖", remark = "打开了这个开关触发宝箱的神豪，点击打开宝箱的时候不抽奖，直接发奖")
    private boolean boxSenderAward = false;

    @ComponentAttrField(labelText = "触发用户宝箱奖品", remark = "打开了这个开关触发宝箱的神豪，点击打开宝箱的时候不抽奖，直接发奖"
            , subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = AwardAttrConfig.class, labelText = "发奖配置")
    })
    private List<AwardAttrConfig> boxSenderAwardConfig = Lists.newArrayList();

    @ComponentAttrField(labelText = "app高光横幅索引")
    private long appBigBannerComponent;

    @ComponentAttrField(labelText = "app高光横幅延迟广播时间(毫秒)")
    private long appBigBannerDelay;


    @ComponentAttrField(labelText = "可查询统计接口数据uid", remark = "多个时逗号分隔"
            , subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> queryTaskInfoUid = Sets.newHashSet();



}
