package com.yy.gameecology.activity.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.RankDataEvent;
import com.yy.gameecology.activity.bean.event.*;
import com.yy.gameecology.activity.bean.mq.HdzkWzryGameEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.HdzkBaseEvent;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.commons.ActAttrCont;
import com.yy.gameecology.activity.commons.enums.App;
import com.yy.gameecology.common.consts.MqConst;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-10-31 20:56
 **/
@Service
public class KafkaService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Value("${kafka.hdzt.ranking.updating.topic}")
    private String rankingUpdatingTopic;

    @Value("${kafka.hdzk.wzry.game.events.topic}")
    private String hdzkWzryGameEventTopic;

    @Value("${kafka.hdzk.common.events.topic}")
    private String hdzkCommonEventTopic;

    private static final String ZHUIWAN_PUBLIC_SCREEN_NOTIFY = "zhuiwan_public_screen_notify_topic";

    @Autowired
    private CommonService commonService;

    @Autowired
    @Qualifier("hdztWxKafkaTemplate")
    private KafkaTemplate<String, String> hdztWxKafkaTemplate;

    @Autowired
    @Qualifier("hdztSzKafkaTemplate")
    private KafkaTemplate<String, String> hdztSzKafkaTemplate;


    @Autowired
    @Qualifier("jiaoyouWxKafkaTemplate")
    private KafkaTemplate<String, String> jiaoyouWxKafkaTemplate;

    @Autowired
    @Qualifier("jiaoyouSzKafkaTemplate")
    private KafkaTemplate<String, String> jiaoyouSzKafkaTemplate;

    @Autowired
    @Qualifier("zhuiwanKafkaTemplate")
    private KafkaTemplate<String, String> zhuiwanKafkaTemplate;

    @Autowired
    @Qualifier("hdptWxKafkaTemplate")
    private KafkaTemplate<String, String> hdptWxKafkaTemplate;

    @Autowired
    @Qualifier("hdptSzKafkaTemplate")
    private KafkaTemplate<String, String> hdptSzKafkaTemplate;



    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    private final static String CHK_MSG ="历史环境禁止写数据-updateRanking";


    public void updateRanking(RankDataEvent rankDataEvent) {
        String msg = "1|" + JSON.toJSONString(rankDataEvent);
        log.info("begin updateRanking event:{}", rankDataEvent);

        if (SysEvHelper.checkHistory(CHK_MSG, true)) {
            throw new RuntimeException(CHK_MSG);
        }

        try {
            hdztWxKafkaTemplate.send(rankingUpdatingTopic, msg).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("hdztKafkaTemplateWx.send error,e:{},event:{}", e.getMessage(), msg, e);
            try {
                hdztSzKafkaTemplate.send(rankingUpdatingTopic, msg).get(10, TimeUnit.SECONDS);
            } catch (Exception e1) {
                log.error("hdztKafkaTemplateSz.send fail,e:{},event:{}", e1.getMessage(), msg, e1);
            }
        }
    }

    /**
     * 王者荣耀赛事事件
     */
    public void sendHdzkWzryGameEvent(HdzkWzryGameEvent event) {
        String msg = JSON.toJSONString(event);
        log.info("begin sendHdzkWzryGameEvent event:{}", event);

        if (SysEvHelper.checkHistory(CHK_MSG, true)) {
            throw new RuntimeException(CHK_MSG);
        }

        try {
            hdztWxKafkaTemplate.send(hdzkWzryGameEventTopic, msg).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("sendHdzkWzryGameEvent.send error,e:{},event:{}", e.getMessage(), msg, e);
            try {
                hdztSzKafkaTemplate.send(hdzkWzryGameEventTopic, msg).get(10, TimeUnit.SECONDS);
            } catch (Exception e1) {
                log.error("sendHdzkWzryGameEvent.send fail,e:{},event:{}", e1.getMessage(), msg, e1);
            }
        }
    }


    /**
     * 活动中控通用事件
     */
    public void sendHdzkCommonEvent(HdzkBaseEvent event) {
        String msg = event.getUri() + "|" + JSON.toJSONString(event);
        log.info("begin sendHdzkCommonEvent event:{}", event);

        if (SysEvHelper.checkHistory(CHK_MSG, true)) {
            throw new RuntimeException(CHK_MSG);
        }

        try {
            hdptWxKafkaTemplate.send(hdzkCommonEventTopic, msg).get(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("sendHdzkWxCommonEvent.send error,e:{},event:{}", e.getMessage(), msg, e);
            try {
                hdptSzKafkaTemplate.send(hdzkCommonEventTopic, msg).get(3, TimeUnit.SECONDS);
            } catch (Exception e1) {
                log.error("sendHdzkSzCommonEvent.send fail,e:{},event:{}", e1.getMessage(), msg, e1);
            }
        }
    }

    public void sendJiaoyouLayerKafka(long actId, long sid, long ssid, boolean showLayer) {
        JyLayerPushEvent event = buildJiaoyouLayerEvent(actId, sid, ssid, showLayer);
        sendJiaoyouLayerKafka(event);
    }

    /**
     * app横幅广播
     */
    @Deprecated
    public void sendAppBannerKafka(AppBannerEvent event) {
        sendJiaoyouKafka(MqConst.APP_ACT_BANNER_EVENT, JSON.toJSONString(event));
    }

    @Deprecated
    public void sendAppBannerKafkaExcludeDanmuku(AppBannerEvent event) {
        List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
        List<String> filterList = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.COLON + channel.getSsid()).toList();
        event.setFilterList(filterList);
        sendJiaoyouKafka(MqConst.APP_ACT_BANNER_EVENT, JSON.toJSONString(event));
    }

    public void sendAppBannerKafkaExcludeDanmuku(AppBannerEvent2 event) {
        List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
        List<String> filterList = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.COLON + channel.getSsid()).toList();
        event.setFilterList(filterList);
        log.info("sendAppBannerKafkaExcludeDanmuku {}", filterList);
        sendAppBannerKafka(event);
    }

    public List<String> listFilterList() {
        List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
        List<String> filterList = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.COLON + channel.getSsid()).toList();
        return filterList;
    }

    /**
     * app横幅广播
     */
    public void sendAppBannerKafka(AppBannerEvent2 event) {
        sendJiaoyouKafka(MqConst.APP_ACT_BANNER_EVENT, JSON.toJSONString(event));
    }

    /**
     * 追玩公屏通知
     * @param event
     */
    public void sendZhuiwanPublicScreenNotifyEvent(PublicScreenNotifyEvent event) {
        String msg = JSON.toJSONString(event);
        log.info("begin sendZhuiwanPublicScreenNotifyEvent, event:{}", msg);

        try {
            zhuiwanKafkaTemplate.send(ZHUIWAN_PUBLIC_SCREEN_NOTIFY, msg).get(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("sendZhuiwanPublicScreenNotifyEvent send error,e:{},event:{}", e.getMessage(), msg, e);
        }
    }

    @Deprecated
    public AppBannerEvent buildAppBannerEvent(long actId, String seq, int business, int bcType, long sid, long ssid, String icon, List<Long> uidList, String context) {
        AppBannerEvent event = new AppBannerEvent();
        event.setActivityID(actId);
        event.setProducerSeqID(seq);
        event.setProducerTime(System.currentTimeMillis() / 1000);
        event.setBusiness(business);
        event.setBcType(bcType);
        event.setSid(sid);
        event.setSsid(ssid);
        event.setIcon(icon);
        event.setUidList(uidList);
        event.setUrlList(ImmutableList.of("https://peiwan.bs2dl.yy.com/DreamerMobile/hdzb_1px_img.png"));
        event.setContext(context == null ? "" : context);
        event.setFromService("hdzk_" + SysEvHelper.getGroup());
        event.setFromIP(SystemUtil.getIp());

        //灰度状态，全服广播/全业务时设置广播白名单
        if(commonService.isGrey(actId) ) {
            if ((bcType == AppBannerEvent.BC_TYPE_BUSI || bcType == AppBannerEvent.BC_TYPE_ALL_BUSI)) {
                String attr = commonService.getActAttr(actId, ActAttrCont.ACT_GREY_BUSICAST_SIDS);
                if (StringUtils.isEmpty(attr)) {
                    throw new SuperException("灰度状态，全服广播设置广播白名单", 500);
                }

                List<Long> sids = Splitter.on(',').omitEmptyStrings().trimResults().splitToList(attr)
                        .stream().filter(StringUtils::isNumeric).map(Long::parseLong).collect(Collectors.toList());

                event.setGraySidList(sids);
            }
        }

        return event;
    }

    public AppBannerEvent2 buildAppBannerEvent2(long actId, String seq, int business, int bcType) {
        return buildAppBannerEvent2(actId, seq, business, bcType, 0, 0, StringUtils.EMPTY, Collections.emptyList());
    }

    public AppBannerEvent2 buildAppBannerEvent2(long actId, String seq, int business, int bcType, long sid, long ssid, String icon, List<Long> uidList) {
        AppBannerEvent2 event = new AppBannerEvent2();
        event.setActivityID(actId);
        event.setProducerSeqID(seq);
        event.setProducerTime(System.currentTimeMillis() / 1000);
        event.setBusiness(business);
        event.setBcType(bcType);
        event.setSid(sid);
        event.setSsid(ssid);
        event.setIcon(icon);
        event.setUidList(uidList);
        event.setUrlList(ImmutableList.of("https://peiwan.bs2dl.yy.com/DreamerMobile/hdzb_1px_img.png"));
        event.setFromService("hdzk_" + SysEvHelper.getGroup());
        event.setFromIP(SystemUtil.getIp());

        //灰度状态，全服广播/全业务时设置广播白名单
        if(commonService.isGrey(actId)) {
            if (bcType == AppBannerEvent2.BC_TYPE_BUSI || bcType == AppBannerEvent2.BC_TYPE_ALL_BUSI) {
                String attr = commonService.getActAttr(actId, ActAttrCont.ACT_GREY_BUSICAST_SIDS);
                if (StringUtils.isEmpty(attr)) {
                    throw new SuperException("灰度状态，全服广播设置广播白名单", 500);
                }

                List<Long> sids = Splitter.on(',').omitEmptyStrings().trimResults().splitToList(attr)
                        .stream().filter(StringUtils::isNumeric).map(Long::parseLong).collect(Collectors.toList());

                event.setGraySidList(sids);
            }
        }

        return event;
    }

    public PublicScreenNotifyEvent buildPublicScreenNotifyEvent(long actId, String seq, String message, int notifyType, long uid, long sid, long ssid, long familyId, List<String> notShowApps) {
        PublicScreenNotifyEvent event = new PublicScreenNotifyEvent();
        event.setSeq(seq);
        event.setMessage(message);
        event.setNotifyType(notifyType);
        event.setUid(uid);
        event.setSid(sid);
        event.setSsid(ssid);
        event.setFamilyId(familyId);
        event.setNotShowApps(notShowApps);
        //灰度状态，全服广播/全业务时设置广播白名单
        if (commonService.isGrey(actId)) {
            String attr = commonService.getActAttr(actId, ActAttrCont.ACT_GREY_BUSICAST_SIDS);
            if (StringUtils.isEmpty(attr)) {
                throw new SuperException("灰度状态，全服广播设置广播白名单", 500);
            }

            List<Long> sids = Splitter.on(',').omitEmptyStrings().trimResults().splitToList(attr)
                    .stream().filter(StringUtils::isNumeric).map(Long::parseLong).collect(Collectors.toList());

            event.setGraySidList(sids);
        }
        return event;
    }

    private JyLayerPushEvent buildJiaoyouLayerEvent(long actId, long sid, long ssid, boolean showLayer) {
        JyLayerPushEvent event = new JyLayerPushEvent();
        String seq = UUID.randomUUID().toString().replaceAll("-", "");
        event.setProducerSeqID(seq);
        event.setProducerTime(System.currentTimeMillis() / 1000);
        event.setEventType(1);
        event.setUid(0);
        event.setSid(sid);
        event.setSsid(ssid);
        event.setFromService("hdzk_" + SysEvHelper.getGroup());
        event.setFromIP(SystemUtil.getIp());
        event.setActivityID(actId);
        // 挂件更新状态 1 -打开 2 -关闭 3 -更新 新挂件逻辑使用
        event.setStatus(showLayer ? 1 : 2);
        event.setPayload("");
        return event;
    }

    public void sendJiaoyouLayerKafka(JyLayerPushEvent event) {
        String msg = JSON.toJSONString(event);
        log.info("begin pushJiaoyouLayer event:{}", msg);

        try {
            jiaoyouWxKafkaTemplate.send(MqConst.PENDANT_LIST_UPDATE_EVENT, msg).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("pushJiaoyouLayer wx.send error,e:{},event:{}", e.getMessage(), msg, e);
            try {
                jiaoyouSzKafkaTemplate.send(MqConst.PENDANT_LIST_UPDATE_EVENT, msg).get(10, TimeUnit.SECONDS);
            } catch (Exception e1) {
                log.error("pushJiaoyouLayer sz.send fail,e:{},event:{}", e1.getMessage(), msg, e1);
            }
        }
    }

    public void sendJiaoyouKafka(String topic, String msg) {
        log.info("begin sendJiaoyouKafka,topic:{}, event:{}", topic, msg);

        try {
            jiaoyouWxKafkaTemplate.send(topic, msg).get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("sendJiaoyouKafka wx.send error,e:{},event:{}", e.getMessage(), msg, e);
            try {
                jiaoyouSzKafkaTemplate.send(topic, msg).get(10, TimeUnit.SECONDS);
            } catch (Exception e1) {
                log.error("sendJiaoyouKafka sz.send fail,e:{},event:{}", e1.getMessage(), msg, e1);
            }
        }
    }

    /**
     * 追玩通用弹窗kafka
     * 对应app客户端协议：6188 2576
     */
    public void sendAppPopUp(AppPopUpEvent event) {
        String msg = JSON.toJSONString(event);
        try {
            zhuiwanKafkaTemplate.send(MqConst.ZHUIWAN_APP_POP_UP, msg).get(10, TimeUnit.SECONDS);
            log.info("sendAppPopUp done,event:{}", msg);
        } catch (Exception e) {
            log.error("sendAppPopUp sz.send fail,e:{},event:{}", e.getMessage(), msg, e);
        }
    }

    public void sendActRiskReCheckEvent(ActRiskReCheckEvent event) {
        String msg = JSON.toJSONString(event);
        try {
            zhuiwanKafkaTemplate.send(MqConst.ZHUIWAN_ACT_RISK_RE_CHECK_TOPIC, msg).get(10, TimeUnit.SECONDS);
            log.info("sendActRiskReCheckEvent done,event:{}", msg);
        } catch (Exception e) {
            log.error("sendActRiskReCheckEvent sz.send fail,e:{},event:{}", e.getMessage(), msg, e);
        }
    }

    @NotNull
    public  ActRiskReCheckEvent buildActRiskReCheckEvent(String app, long actId, long uid, String riskStrategyKey, int recheckCode, String msg, String title, String recordId, String param) {
        ActRiskReCheckEvent event = new ActRiskReCheckEvent();
        event.setMsgTp(0);

        ActRiskReCheckEvent.AppChallengeUnicastReq req = new ActRiskReCheckEvent.AppChallengeUnicastReq();
        req.setUid(uid);
        req.setType(2);
        req.setScene(riskStrategyKey);
        req.setCode(recheckCode);
        req.setMsg(msg);
        req.setApp(App.fromHostName(app));
        //此处必须act开头
        req.setFrom("act:" + actId);
        req.setTitle(title);
        if(recheckCode == SuperException.RISK_RESULT_UDB_RECHECK){
            req.setRecordId(Convert.toLong(recordId,0L));
        }else if(recheckCode == SuperException.RISK_RESULT_RECHECK){
            req.setMobileMask(recordId);
        }
        req.setParam(param);
        event.setAppChallengeUnicastReq(req);
        return event;
    }

    public static int getBroBusiId(BusiId busiId) {
        return switch (busiId) {
            case MAKE_FRIEND -> 2;
            case SKILL_CARD -> 1;
            case GAME_BABY -> 8;
            default -> 4;
        };
    }

    public static int getTurnoverAppId(int busiId) {
        BusiId value = BusiId.findByValue(busiId);
        int appId = 0;
        switch (value) {
            case GAME_BABY:
                appId = 36;
                break;
            case MAKE_FRIEND:
                appId = 2;
                break;
            case SKILL_CARD:
                appId = 34;
                break;
            default:break;
        }
        return appId;
    }
}
