package com.yy.gameecology.activity.config.redis;

import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.ActAttrCont;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.common.consts.ActStatus;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-07-22 14:12
 **/
@Component
public class RedisConfigManager {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CacheService cacheService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    /**
     * 没指定的默认分组
     */
    public final static String DEFAULT_GROUP_CODE = "0";

    /**
     * 老的分组规则，用第0套redis，然后根据运行环境分1~4库，兼容老的数据。
     */
    public final static String OLD_ACT_GROUP_CODE = "-1";

    /**
     * 数据归档pika库
     */
    public final static String PIKA_GROUP_CODE_101 = "101";

    /**
     * 临时存放非活动seq
     * 4 ==> 对应到目前最多4套redis
     */
    public String temp_act_group = "0";

    /**
     * old
     */
    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Qualifier("stringRedisGroup0Template")
    private StringRedisTemplate stringRedisGroup0Template;

    @Autowired
    @Qualifier("stringRedisGroup1Template")
    private StringRedisTemplate stringRedisGroup1Template;

    @Autowired
    @Qualifier("stringRedisGroup2Template")
    private StringRedisTemplate stringRedisGroup2Template;

    @Autowired
    @Qualifier("stringRedisGroup3Template")
    private StringRedisTemplate stringRedisGroup3Template;

    @Autowired
    @Qualifier("stringRedisGroup4Template")
    private StringRedisTemplate stringRedisGroup4Template;

    @Autowired
    @Qualifier("redissonClient0")
    private RedissonClient redissonClient0;

    @Autowired
    @Qualifier("redissonClient1")
    private RedissonClient redissonClient1;

    @Autowired
    @Qualifier("redissonClient2")
    private RedissonClient redissonClient2;

    @Autowired
    @Qualifier("redissonClient3")
    private RedissonClient redissonClient3;

    @Autowired
    @Qualifier("redissonClient4")
    private RedissonClient redissonClient4;

    @Autowired
    @Qualifier("redissonClient101")
    private RedissonClient redissonClient101;

    /**
     * redis 热备数据
     */
    @Autowired
    @Qualifier("stringRedisGroup101Template")
    private StringRedisTemplate stringRedisGroup101Template;

    /**
     * 无活动id区分的redis group,在线离线区分开
     */
    public String getDefaultGroupCode() {
        if (SysEvHelper.isHistory()) {
            return PIKA_GROUP_CODE_101;
        } else {
            return DEFAULT_GROUP_CODE;
        }
    }

    /**
     * 获取redis分组编码
     * 获取逻辑：
     * 如果活动没配置分组，则按照老的逻辑，用第1套redis，然后根据运行环境分1~4库，兼容老的数据。
     * <p>
     * 新的活动原则上都要配。
     * <p>
     * 新活动配置分组，则按照实际配置的来选择redis，并且用0库
     *
     * @param actId 活动id
     * @return 分组编码
     */
    public String getGroupCode(Long actId) {
        return getGroupCode(actId, false);
    }

    /**
     * 获取redis分组编码
     * 获取逻辑：
     * 如果活动没配置分组，则按照老的逻辑，用第1套redis，然后根据运行环境分1~4库，兼容老的数据。
     * <p>
     * 新的活动原则上都要配。
     * <p>
     * 新活动配置分组，则按照实际配置的来选择redis，并且用0库
     *
     * @param actId 活动id
     * @param fixSourceGroup 固定返回热库，不返回数据归档冷库
     * @return 分组编码
     */
    public String getGroupCode(Long actId, boolean fixSourceGroup) {
        if (Convert.toLong(actId, 0) == 0) {
            return OLD_ACT_GROUP_CODE;
        }
        String groupCode = cacheService.getActAttrValue(actId, ActAttrCont.ACT_REDIS_GROUP_CODE);
        if (StringUtil.isBlank(groupCode)) {
            return OLD_ACT_GROUP_CODE;
        }

        //数据归档冷库
        if (!fixSourceGroup) {
            String archiveGroupCode = getArchiveGroupCode(actId);
            if (StringUtil.isNotBlank(archiveGroupCode)) {
                ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCache(actId);
                if (activityInfoVo != null && ActStatus.ARCHIVE.equals(activityInfoVo.getStatus())) {
                    return archiveGroupCode;
                }
            }
        }

        return groupCode;
    }

    /**
     * 数据归档冷库
     */
    public String getArchiveGroupCode(Long actId) {
        return cacheService.getActAttrValue(actId, ActAttrCont.ACT_REDIS_ARCHIVE_GROUP_CODE);
    }

    public StringRedisTemplate getRedisTemplate(String groupCode) {
        //老方式，按照部署环境分库
        if (OLD_ACT_GROUP_CODE.equals(groupCode)) {
            return stringRedisTemplate;
        }

        // 一般放公用 和 默认的0套
        // 按照db隔离
        if (DEFAULT_GROUP_CODE.equals(groupCode)) {
            return stringRedisTemplate;
        } else if (StringUtil.ONE.equals(groupCode)) {
            return stringRedisGroup1Template;
        } else if (StringUtil.TWO.equals(groupCode)) {
            return stringRedisGroup2Template;

        } else if (StringUtil.THREE.equals(groupCode)) {
            return stringRedisGroup3Template;
        } else if (StringUtil.FOUR.equals(groupCode)) {
            return stringRedisGroup4Template;
        } else if (PIKA_GROUP_CODE_101.equals(groupCode)) {
            return stringRedisGroup101Template;
        }

        throw new RuntimeException("redis group code not config:" + groupCode);
    }

    public StringRedisTemplate getLockerRedisTemplate() {
        return getRedisTemplate(getDefaultGroupCode());
    }

    public RedissonClient getRedissonClient() {
        return getRedissonClient(getDefaultGroupCode());
    }

    public RedissonClient getRedissonClient(String groupCode) {
        //老方式，按照部署环境分库
        if (OLD_ACT_GROUP_CODE.equals(groupCode)) {
            return redissonClient0;
        }

        // 一般放公用 和 默认的0套
        // 按照db隔离
        if (DEFAULT_GROUP_CODE.equals(groupCode)) {
            return redissonClient0;
        } else if (StringUtil.ONE.equals(groupCode)) {
            return redissonClient1;
        } else if (StringUtil.TWO.equals(groupCode)) {
            return redissonClient2;

        } else if (StringUtil.THREE.equals(groupCode)) {
            return redissonClient3;
        } else if (StringUtil.FOUR.equals(groupCode)) {
            return redissonClient4;
        } else if (PIKA_GROUP_CODE_101.equals(groupCode)) {
            return redissonClient101;
        }

        throw new RuntimeException("redisson group code not config:" + groupCode);
    }
}
