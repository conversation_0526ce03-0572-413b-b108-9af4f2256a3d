package com.yy.gameecology.activity.client.yrpc;

import com.alibaba.fastjson.JSON;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.event.ActRiskReCheckEvent;
import com.yy.gameecology.activity.commons.enums.App;
import com.yy.gameecology.common.consts.ZhuiwanApp;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.utils.ZhuiyaClientUtils;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.risk.ZhuiyaRisk;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2023/4/24 14:13
 **/
@Component
public class ZhuiwanRiskClient {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "yrpc", owner = "zhuiya_server_yrpc", registry = {"yrpc-reg"}, lazy = true)
    private ZhuiyaRiskService proxy;

    @Reference(protocol = "yrpc", owner = "zhuiya_server_yrpc", registry = {"yrpc-reg"}, lazy = true, retries = 2, cluster = "failover")
    private ZhuiyaRiskService readProxy;

    public ZhuiyaRiskService getProxy() {
        return proxy;
    }

    public ZhuiyaRiskService getReadProxy() {
        return readProxy;
    }


    /**
     * 是否命中风控
     **/
    public boolean hitRiskOfPc(long uid, String ip, String riskStrategyKey) {
        ZhuiyaRisk.Client client = ZhuiyaRisk.Client.newBuilder()
                .setApp(ZhuiyaRisk.App.APP_PCYY)
                .setIp(ip)
                .setPlatform(ZhuiyaRisk.Platform.PLATFORM_PC)
                .build();
        ZhuiyaRisk.RiskReq riskReq = ZhuiyaRisk.RiskReq.newBuilder()
                .setUserId(uid)
                .setStrategyKey(riskStrategyKey)
                .setClient(client)
                .build();
        ZhuiyaRisk.RiskRsp riskRsp = this.getReadProxy().riskCheck(riskReq);

        boolean hitRisk = riskRsp.getRiskResult() != ZhuiyaRisk.RiskResult.RISK_RESULT_VALID;

        log.info("hitRiskOfPc,hitRisk-{} uid={} req={} risk resp={}", hitRisk, uid, JsonFormat.printToString(riskReq), JsonFormat.printToString(riskRsp));

        return hitRisk;
    }

    /**
     * 是否命中风控
     **/
    public boolean hitRiskOfApp(long uid, String hdid, String ip, String app,String pkg, int clientType, String riskStrategyKey) {
        ZhuiyaRisk.Client client = ZhuiyaRisk.Client.newBuilder()
                .setApp(getApp(app))
                .setIp(Convert.toString(ip,""))
                .setHdid(Convert.toString(hdid,""))
                .setPkg(pkg)
                .setPlatform(ZhuiyaRisk.Platform.forNumber(clientType))
                .build();
        ZhuiyaRisk.RiskReq riskReq = ZhuiyaRisk.RiskReq.newBuilder()
                .setUserId(uid)
                .setStrategyKey(riskStrategyKey)
                .setClient(client)
                .build();
        ZhuiyaRisk.RiskRsp riskRsp = this.getReadProxy().riskCheck(riskReq);

        boolean hitRisk = riskRsp.getRiskResult() != ZhuiyaRisk.RiskResult.RISK_RESULT_VALID;

        log.info("hitRiskOfApp,hitRisk-{} uid={} req={} risk resp={}", hitRisk, uid, JsonFormat.printToString(riskReq), JsonFormat.printToString(riskRsp));

        return hitRisk;
    }

    public static ZhuiyaRisk.App getApp(String app) {
        if (ZhuiwanApp.dreamer.name().equals(app) || ZhuiwanApp.zhuiwan.name().equals(app)) {
            return ZhuiyaRisk.App.APP_ZHUIWAN;
        } else if (ZhuiwanApp.yomi.name().equals(app) || "yymobile".equalsIgnoreCase(app)) {
            return ZhuiyaRisk.App.APP_YOMI;
        } else if ("pcyy".equalsIgnoreCase(app)) {
            return ZhuiyaRisk.App.APP_PCYY;
        }

        return ZhuiyaRisk.App.APP_YOMI;
    }

    // 客户端与规范定义映射
    public static ZhuiyaRisk.Platform getPlatform(String platform) {
        if (Objects.equals(platform, "3")) {
            return ZhuiyaRisk.Platform.PLATFORM_ANDROID;
        } else if (Objects.equals(platform, "4")) {
            return ZhuiyaRisk.Platform.PLATFORM_IOS;
        }
        return ZhuiyaRisk.Platform.PLATFORM_PC;
    }

    public void doRiskCheck(ZhuiyaPbCommon.Client client, String riskStrategyKey, Long uid, String verifyCode, String verifyToken, String recordId) {
        doRiskCheck(client, riskStrategyKey, uid, verifyCode, verifyToken, recordId, null);
    }

    public void doRiskCheck(ZhuiyaPbCommon.Client client, String riskStrategyKey, Long uid, String verifyCode, String verifyToken, String recordId, Map<String,String> riskExt) {
        if (StringUtil.isBlank(riskStrategyKey)) {
            log.info("not config riskStrategyKey");
            return;
        }

        log.info("client:{} riskStrategyKey={}", client,riskStrategyKey);
        ZhuiyaRisk.RiskRsp rsp = riskCheck(riskStrategyKey, uid, client, verifyCode, recordId, verifyToken, riskExt);
        if (rsp == null) {
            throw new SuperException("请求不合法，请稍后再试！", SuperException.E_PARAM_ILLEGAL);
        }

        if (ZhuiwanRiskClient.isFailResult(rsp.getRiskResult())) {
            if(rsp.getRiskResult() == ZhuiyaRisk.RiskResult.RISK_RESULT_FORBID){
                if(rsp.getTipContent().contains("当前网络环境存在风险，请切换网络后重试")){
                    throw new SuperException(rsp.getTipContent(), SuperException.RISK_RESULT_FORBID);
                }
            }
            throw new SuperException(rsp.getTipContent(), SuperException.E_PARAM_ILLEGAL);
        }

        if (rsp.getRiskResult() == ZhuiyaRisk.RiskResult.RISK_RESULT_UDB_RECHECK) {
            throw new SuperException("请先完成验证", SuperException.RISK_RESULT_UDB_RECHECK, rsp.getMobileMask());
        }

        if (rsp.getRiskResult() == ZhuiyaRisk.RiskResult.RISK_RESULT_RECHECK) {
            throw new SuperException("请先完成验证", SuperException.RISK_RESULT_RECHECK, rsp.getMobileMask());
        }
    }

    @NotNull
    public ActRiskReCheckEvent buildActRiskReCheckEvent(String app, long actId, long uid, String riskStrategyKey, int recheckCode, String msg, String title, String recordId, String param) {
        ActRiskReCheckEvent event = new ActRiskReCheckEvent();
        event.setMsgTp(0);

        ActRiskReCheckEvent.AppChallengeUnicastReq req = new ActRiskReCheckEvent.AppChallengeUnicastReq();
        req.setUid(uid);
        req.setType(1);
        req.setScene(riskStrategyKey);
        req.setCode(recheckCode);
        req.setMsg(msg);
        req.setApp(App.fromHostName(app));
        req.setFrom("act:" + actId);
        req.setTitle(title);
        if(recheckCode == SuperException.RISK_RESULT_UDB_RECHECK){
            req.setRecordId(cn.hutool.core.convert.Convert.toLong(recordId,0L));
        }else if(recheckCode == SuperException.RISK_RESULT_RECHECK){
            req.setMobileMask(recordId);
        }
        req.setParam(param);
        event.setAppChallengeUnicastReq(req);
        return event;
    }

    public RiskCheckResult riskCheck(ZhuiyaPbCommon.Client client, String riskStrategyKey, Long uid, String verifyCode, String verifyToken, String recordId, Map<String,String> riskExt) {
        RiskCheckResult result = new RiskCheckResult();
        if (StringUtil.isBlank(riskStrategyKey)) {
            log.info("not config riskStrategyKey");
            return result;
        }

        log.info("client:{} riskStrategyKey={}", client,riskStrategyKey);
        ZhuiyaRisk.RiskRsp rsp = riskCheck(riskStrategyKey, uid, client, verifyCode, recordId, verifyToken, riskExt);
        if (rsp == null) {
            throw new SuperException("请求不合法，请稍后再试！", SuperException.E_PARAM_ILLEGAL);
        }

        result.setForbid(rsp.getRiskResult() == ZhuiyaRisk.RiskResult.RISK_RESULT_FORBID);
        result.setMsg(rsp.getMessage());

        if (rsp.getRiskResult() == ZhuiyaRisk.RiskResult.RISK_RESULT_UDB_RECHECK) {
            result.setRecheckCode(SuperException.RISK_RESULT_UDB_RECHECK);
            result.setRecheckId(rsp.getMobileMask());
        }

        if (rsp.getRiskResult() == ZhuiyaRisk.RiskResult.RISK_RESULT_RECHECK) {
            result.setRecheckCode(SuperException.RISK_RESULT_RECHECK);
            result.setRecheckId(rsp.getMobileMask());
        }

        return result;
    }




    public ZhuiyaRisk.RiskRsp riskCheck(String strategyKey, long uid, ZhuiyaPbCommon.Client client, String verifyCode, String recordId, String verifyToken, Map<String,String> riskExt) {
        String riskExtStr = MapUtils.isNotEmpty(riskExt) ? JSON.toJSONString(riskExt) : "";
        ZhuiyaRisk.RiskReq req = ZhuiyaRisk.RiskReq.newBuilder()
                .setClient(ZhuiyaClientUtils.toRiskClient(client))
                .setStrategyKey(strategyKey)
                .setVerifyCode(StringUtils.stripToEmpty(verifyCode))
                .setUserId(uid)
                .setIsReport(false)
                .setRecordId(cn.hutool.core.convert.Convert.toLong(recordId, 0L))
                .setVerifyToken(StringUtils.stripToEmpty(verifyToken))
                .setUserLabel(ZhuiyaRisk.UserLabel.UNKNOWN)
                .setRiskExtension(riskExtStr)
                .build();

        ZhuiyaRisk.RiskRsp riskRsp = getReadProxy().riskCheck(req);
        log.info("riskCheck strategyKey:{},uid:{},client:{},verifyCode:{},recordId:{},verifyToken:{},rsp:{}", strategyKey, uid, client, verifyCode, recordId, JsonFormat.printToString(req), JsonFormat.printToString(riskRsp));
        return riskRsp;
    }

    public  ZhuiyaPbCommon.Client fetchClientInfo(HttpServletRequest request) throws SuperException {
        ZhuiyaPbCommon.Client client = ZhuiyaClientUtils.getClient(request);
        if (client.getApp() != ZhuiyaPbCommon.App.APP_PCYY) {
            if (StringUtils.isEmpty(client.getHdid())) {
                throw new SuperException("请求不合法，请稍后重试",SuperException.E_PARAM_ILLEGAL);
            }
        }

        return client;
    }

    public static boolean isFailResult(ZhuiyaRisk.RiskResult riskResult) {
        if (riskResult == null) {
            return true;
        }

        return riskResult != ZhuiyaRisk.RiskResult.RISK_RESULT_VALID && riskResult != ZhuiyaRisk.RiskResult.RISK_RESULT_RECHECK && riskResult != ZhuiyaRisk.RiskResult.RISK_RESULT_UDB_RECHECK;
    }

    @Data
    public static class RiskCheckResult {
        /**
         * true的时候高风险禁止
         */
        private boolean forbid;

        private int recheckCode;
        /**
         * 触发二次挑战的id
         */
        private String recheckId;

        private String msg;
    }
}
