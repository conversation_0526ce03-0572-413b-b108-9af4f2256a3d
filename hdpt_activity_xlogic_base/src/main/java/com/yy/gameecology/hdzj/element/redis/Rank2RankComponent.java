package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.Rank2RankComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import com.yy.thrift.hdztranking.UpdateRankingResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/17
 */
@UseRedisStore
@Component
public class Rank2RankComponent extends BaseActComponent<Rank2RankComponentAttr> {

    private static final String IDEMPOTENT_KEY = "id:%s";

    @Autowired
    private HdztRankingThriftClient rankingThriftClient;

    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(RankingTimeEnd event, Rank2RankComponentAttr attr) {
        if (event.getActId() != attr.getActId()) {
            return;
        }
        if (SysEvHelper.isDev()) {
            log.info("onRankingTimeEnd start event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        }
        if (CollectionUtils.isEmpty(attr.getSources())) {
            return;
        }

        if (attr.getRankScoreAwardMap().isEmpty()) {
            return;
        }
        if (attr.getSources().stream().noneMatch(
                rankAndPhaseId -> rankAndPhaseId.getRankId() == event.getRankId()
                        && rankAndPhaseId.getPhaseId() == 0)) {
            return;
        }
        log.info("onRankingTimeEnd event:{}", JSON.toJSONString(event));

        String key = makeKey(attr, String.format(IDEMPOTENT_KEY, event.getEkey()));
        if (idempotent(key, attr.getActId())) {
            log.info("idempotent not pass event={},key={}", event, key);
            return;
        }
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(),
                DateUtil.getDate(event.getEndTime()));
        long maxRank = Collections.max(attr.getRankScoreAwardMap().keySet());
        List<Rank> ranks = rankingThriftClient.queryRanking(attr.getActId(), event.getRankId(), 0,
                timeCode, maxRank,
                Collections.emptyMap());
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn(
                    "rank 2 rank by onRankingTimeEnd,query ranks is empty,timeCode:{},actId:{},rankId:{}",
                    timeCode, attr.getActId(), event.getRankId());
            return;
        }
        for (Rank rank : ranks) {
            if (!attr.getRankScoreAwardMap().containsKey((long) rank.getRank())) {
                log.info("not found rank award config ,rank={}", JSON.toJSONString(rank));
                return;
            }

            UpdateRankingRequest request = new UpdateRankingRequest();
            request.setBusiId(attr.getBizId());
            request.setActId(attr.getActId());
            request.setSeq(event.getSeq() + rank.getMember() + rank.getRank());
            HashMap<Long, String> actors = Maps.newHashMap();
            actors.put(attr.getActorId(), rank.getMember());
            request.setActors(actors);
            request.setItemId(attr.getSinkItemId());
            request.setCount(1L);
            request.setScore(attr.getRankScoreAwardMap().get((long) rank.getRank()));
            request.setTimestamp(DateUtil.getDate(event.getEndTime()).getTime());
            try {
                UpdateRankingResult result = rankingThriftClient.getProxy().updateRanking(request);
                log.info("rank 2 rank by onRankingTimeEnd is completed,request:{},sourceRankId:{},result:{}", request, event.getRankId(), result);
            } catch (TException e) {
                log.error("rank 2 rank by onRankingTimeEnd is error,request:{},sourceRankId:{}", request, event.getRankId(), e);
            }
        }
    }

    @Override
    public Long getComponentId() {
        return ComponentId.RANK_2_RANK;
    }

    private boolean idempotent(String ekey, long actId) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        if (StringUtils.isNotBlank(ekey)) {
            return !actRedisDao.setNX(groupCode, ekey, "1");
        }
        return false;
    }
}
