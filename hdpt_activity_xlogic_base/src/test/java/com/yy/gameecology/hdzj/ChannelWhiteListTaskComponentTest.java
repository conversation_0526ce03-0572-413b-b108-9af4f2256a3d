package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.hdzj.bean.WhiteListTaskVo;
import com.yy.gameecology.hdzj.element.redis.ChannelWhiteListTaskComponent;
import com.yy.thrift.hdztranking.BusiId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-07-18 18:02
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class ChannelWhiteListTaskComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private ChannelWhiteListTaskComponent channelWhiteListTaskComponent;

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Test
    public void test() {
        broadCastHelpService.getUserInfo(1666346447, BusiId.MAKE_FRIEND);

        Response<WhiteListTaskVo> ret = channelWhiteListTaskComponent.queryTaskInfo(2022061001, 500, 1828691651);
    }
}
