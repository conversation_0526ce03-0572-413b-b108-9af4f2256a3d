package com.yy.gameecology.activity.client.yrpc;

import org.apache.dubbo.config.annotation.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-10-31 19:46
 **/
@Component
public class RoomNoYrpcClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Reference(protocol = "yrpc", owner = "skillcard_server_yrpc", registry = {"yrpc-reg"}, lazy = true)
    private RoomNoYrpc proxy;


    @Reference(protocol = "yrpc", owner = "skillcard_server_yrpc", registry = {"yrpc-reg"}, lazy = true, retries = 2, cluster = "failover")
    private RoomNoYrpc proxyRead;

    public RoomNoYrpc getWriteProxy() {
        return proxy;
    }

    public RoomNoYrpc getReadProxy() {
        return proxyRead;
    }
}
