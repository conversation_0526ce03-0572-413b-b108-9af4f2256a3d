package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.fts_recommend_data_base.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FtsRecommendDataThriftClient {

    private static final ConcurrentMap<String, Optional<String>> PIC_CACHE = CacheBuilder.newBuilder()
            .expireAfterAccess(5, TimeUnit.MINUTES)
            .maximumSize(1000).<String, Optional<String>>build().asMap();

    @Reference(protocol = "nythrift", owner = "${thrift.client.fts_recmd_data.s2sname}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "10"})
    private FtsRecommendDataBaseService.Iface proxy;

    @Reference(protocol = "nythrift", owner = "${thrift.client.fts_recmd_data.s2sname}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "10"}, retries = 2 , cluster = "failover")
    private FtsRecommendDataBaseService.Iface readProxy;

    public FtsRecommendDataBaseService.Iface getProxy() {
        return proxy;
    }

    public FtsRecommendDataBaseService.Iface getReadProxy() {
        return readProxy;
    }

    public Map<String, String> batchGetFtsRoomMgrPic(List<String> memberIds,int businessType) {

        Map<String, String> roleMemberPicMap = batchGetRoomMgrPicByChannel(memberIds);
        Map<String, String> recommendPicMap = batchGetRecommendConfigPicture(memberIds, businessType);
        for(String memberId : memberIds) {
            if(StringUtil.isEmpty(roleMemberPicMap.get(memberId)) && StringUtil.notEmpty(recommendPicMap.get(memberId))) {
                roleMemberPicMap.put(memberId,recommendPicMap.get(memberId));
            }
        }

        return roleMemberPicMap;
    }

    public Map<String, String> batchGetRoomMgrPicByChannel(Collection<String> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return Collections.emptyMap();
        }

        Map<String, String> result = new HashMap<>(memberIds.size());
        Set<String> lackMemberIds = new HashSet<>(memberIds.size());
        for (String memberId : memberIds) {
            Optional<String> optional = PIC_CACHE.get(memberId);
            if (!SysEvHelper.isDeploy() || optional == null) {
                lackMemberIds.add(memberId);
                continue;
            }

            optional.ifPresent(s -> result.put(memberId, s));
        }

        if (lackMemberIds.size() == 0) {
            return result;
        }

        Map<String, String> picMap = batchGetRoomMgrPicByChannel(lackMemberIds, RecommPictureType.kPictureType_1x1);
        if (SysEvHelper.isDeploy()) {
            for (String memberId : lackMemberIds) {
                PIC_CACHE.putIfAbsent(memberId, Optional.ofNullable(picMap.get(memberId)));
            }
        }

        result.putAll(picMap);

        return result;
    }

    public Map<String, String> batchGetRoomMgrPicByChannel(Collection<String> memberIds, RecommPictureType picType) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return Collections.emptyMap();
        }

        List<Channel> channels = memberIds.stream()
                .map(memberId -> StringUtils.split(memberId, ":_"))
                .filter(arr -> ArrayUtils.getLength(arr) >= 2)
                .filter(arr -> StringUtils.isNumeric(arr[0]) && StringUtils.isNumeric(arr[1]))
                .map(arr -> new Channel(Long.parseLong(arr[0]), Long.parseLong(arr[1])))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(channels)) {
            return Collections.emptyMap();
        }

        BatchGetRoomMgrPictureChannelRsp rsp = null;
        BatchGetRoomMgrPictureByChannelReq req = new BatchGetRoomMgrPictureByChannelReq(channels, true);
        try {
            rsp = getReadProxy().BatchGetRoomMgrPictureByChannel(req);
        } catch (Exception e) {
            log.error("batchGetRoomMgrPicByChannel exception:", e);
        }

        if (rsp == null || rsp.ret != 0) {
            log.error("batchGetRoomMgrPicByChannel exception,rsp:{}", rsp);
            return Collections.emptyMap();
        }

        Map<String, String> result = Maps.newHashMap();
        if (MapUtils.isNotEmpty(rsp.getPictures_map())) {
            for (Channel channel : rsp.pictures_map.keySet()) {
                String key = channel.sid + "_" + channel.ssid;
                RoomMgrPictureInfo picInfo = rsp.pictures_map.get(channel);
                RecommPictureInfo pic = doGetPicInfo(picInfo, picType);
                if (pic != null && StringUtils.isNotEmpty(pic.picture)) {
                    result.put(key, pic.picture);
                }
            }
        }
        //新加优先级更高的资料图，可覆盖厅推荐图
        if (MapUtils.isNotEmpty(rsp.getTing_res_pic())) {
            for (Channel channel : rsp.getTing_res_pic().keySet()) {
                String key = channel.sid + "_" + channel.ssid;
                String picInfo = rsp.getTing_res_pic().get(channel);
                if (StringUtils.isNotEmpty(picInfo)) {
                    result.put(key, picInfo);
                }
            }
        }

        return result;
    }

    private static RecommPictureInfo doGetPicInfo(RoomMgrPictureInfo picInfo, RecommPictureType picType) {
        switch (picType) {
            case kPictureType_1x1:
                return picInfo.pic_1x1;
            case kPictureType_4x3:
                return picInfo.pic_4x3;
            case kPictureType_2x3:
                return picInfo.pic_2x3;
            case kPictureType_11x10:
                return picInfo.pic_11x10;
            case kPictrueType_Title:
            case kPictureType_All:
                return picInfo.title;
            case kPictureType_16x9:
                return picInfo.pic_16x9;
            case kPictureType_4x5:
                return picInfo.pic_4x5;
            default:
                return null;
        }
    }

    /**
     * 批量获取boss后台配置的推荐图
     *
     * @param memberIds    厅列表,格式为：{sid}_{ssid}
     * @param businessType 1 交友 2 约战 3 宝贝 4 打通房 101 派单房 201 游戏房 301 语音房 401 YY开黑房 501 技能卡房
     * @return key 厅 value 推荐图url
     **/
    @Cached(timeToLiveMillis = CacheTimeout.PW_TUAN)
    public Map<String, String> batchGetRecommendConfigPicture(List<String> memberIds, int businessType) {
        Map<String, String> result = Maps.newHashMap();
        List<List<String>> listList = Lists.partition(memberIds, 50);
        for (List<String> list : listList) {
            Map<String, String> map = batchGetRecommendConfigPictureWithSizeLimit(list, businessType);
            result.putAll(map);
        }

        return result;
    }

    @Cached(timeToLiveMillis = CacheTimeout.PW_TUAN)
    public String getRecommendConfigPicture(String memberId, int businessType) {
        return batchGetRecommendConfigPicture(Arrays.asList(memberId), businessType).getOrDefault(memberId, "");
    }


    private Map<String, String> batchGetRecommendConfigPictureWithSizeLimit(List<String> memberIds, int businessType) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return Collections.emptyMap();
        }

        Map<String, String> result = new HashMap<>(memberIds.size());

        List<Channel> channels = memberIds.stream().map(memberId -> StringUtils.split(memberId, ":_"))
                .filter(arr -> ArrayUtils.getLength(arr) >= 2)
                .filter(arr -> StringUtils.isNumeric(arr[0]) && StringUtils.isNumeric(arr[1]))
                .map(arr -> new Channel(Long.parseLong(arr[0]), Long.parseLong(arr[1])))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(channels)) {
            log.warn("batchGetRecommendConfig channels empty,memberIds:{},businessType:{}", JSON.toJSONString(memberIds), businessType);
            return Collections.emptyMap();
        }

        BatchGetPictureReq req = new BatchGetPictureReq();
        req.setBusiness_type(businessType);
        req.setPic_type(RecommPictureType.kPictureType_1x1.getValue());
        req.setAudit_passed_only(true);
        req.setInfo_list(channels);

        try {
            BatchPicturesRet resp = getReadProxy().BatchGetRecommendConfigPicture(req);
            log.info("BatchGetRecommendConfigPicture resp:{}", resp);
            if(resp == null || resp.ret != 0 ){
                log.error("batchGetRecommendConfig error,req:{},rsp:{}", req, resp);
            }
            if (resp == null || resp.ret != 0 || CollectionUtils.isEmpty(resp.picture_list)) {
                return Collections.emptyMap();
            }

            for (ChannelPicture channelPicture : resp.picture_list) {
                String picture = channelPicture.getPicture();
                if (StringUtils.isEmpty(picture)) {
                    continue;
                }
                Channel channel = channelPicture.getChannel();
                String key = channel.sid + "_" + channel.ssid;
                result.put(key, channelPicture.getPicture());
            }
        } catch (Exception ex) {
            log.error("batchGetRecommendConfigPicture error,memberIds={},businessType={}", JSON.toJSONString(memberIds), businessType, ex);
        }

        return result;
    }
}
