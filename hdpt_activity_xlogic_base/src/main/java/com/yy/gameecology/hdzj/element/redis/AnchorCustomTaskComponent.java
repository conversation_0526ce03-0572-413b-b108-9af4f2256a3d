package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.BabyMissionItem;
import com.yy.gameecology.activity.bean.actlayer.TaskItem;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.FtsGroupCenterThriftClient;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.PairBean;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.ActTaskScore;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardRecordInfo;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.HdztTaskPackage;
import com.yy.gameecology.hdzj.bean.SunshineTaskConfig;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AnchorCustomTaskComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.service.Pair;
import com.yy.thrift.fts_group_center.QueryType;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztaward.PackageLeftQuota;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 主播成就，高光任务
 */
@UseRedisStore
//@Component
@RestController
@RequestMapping("/hdzk/cmpt/AnchorCustomTaskComponent")
public class AnchorCustomTaskComponent extends BaseActComponent<AnchorCustomTaskComponentAttr> {

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private BigDataService bigDataService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private FtsGroupCenterThriftClient ftsGroupCenterThriftClient;


    @Override
    public Long getComponentId() {
        return ComponentId.ANCHOR_CUSTOM_TASK;
    }

    //https://activitysys.bs2cdn.yy.com/gonghuigaogaung_20221025173017.svga


    /**
     * 个人突破任务分值配置,用于固定类型，每天不会变
     */
    private static final String MEMBER_SINGLE_FIXED_TASK = "member_single_fixed_task";

    /**
     * 个人突破任务奖励，用于固定类型，每天不会变
     */
    private static final String MEMBER_SINGLE_FIXED_AWARD = "member_single_fixed_award";

    /**
     * 用于每天会变的类型，个人突破任务每日的值 %s == yyyyMMdd
     */
    private static final String MEMBER_SINGLE_DAY_TASK = "member_single_day_task:%s";

    /**
     * 用于每天会变的奖励配置 %s == yyyyMMdd
     */
    private static final String MEMBER_SINGLE_DAY_AWARD_CONFIG = "member_single_day_award_config:%s";

    /**
     * 用于每天会变的类型，个人突破任务每日的值 %s == yyyyMMdd
     */
    private static final String MEMBER_SINGLE_DAY_COMPLETE = "member_single_day_complete:%s";

    /**
     * 总奖池总已发放
     */
    private static final String AWARD_RELEASE_AMOUNT = "award_release_amount";

    /**
     * :{yyyyMMdd}:{memberId}
     * 个人日已发放
     */
    private static final String DAY_AWARD_RELEASE_AMOUNT = "day_award_release_amount:%s:%s";

    /**
     * :{uid}
     * 奖励记录，给前端展示用
     */
    private static final String AWARD_RECORD_LIST_VIEW = "award_record_list_view:%s";

    /**
     * :{yyyyMMdd}
     * 需要发奖的key
     */
    private static final String AWARD_RECORD_SET_VIEW = "award_record_set_view:%s";


    //个人突破任务
    private static final String SINGLE = "single";

    /**
     * 个人突破任务展示名称
     */
    private static final String SINGLE_NAME = "突破";

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, AnchorCustomTaskComponentAttr attr) {
        invokeOnRankingScoreChanged(event, attr);
    }

    /**
     * 重要：里面的数据操作，都要保证幂等性，可安全重试！！！
     */
    public void invokeOnRankingScoreChanged(RankingScoreChanged event, AnchorCustomTaskComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        if (SysEvHelper.isDev()) {
            log.info("log SunshineTaskComponent handlerTaskEvent,event:{}", JSON.toJSONString(event));
        }

        long actId = event.getActId();
        //榜单更新时间
        Date rankUpdateDate = DateUtil.getDate(event.getOccurTime());
        String member = event.getMember();

        handleTask(event, attr, actId, member, rankUpdateDate);
    }


    /**
     * 更新突破任务次日门槛
     * 这一步是必须的，否则可能会出现中间某天主播没收礼，造成数据按天不连续
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onPhaseTimeEndEvent(PhaseTimeEnd event, AnchorCustomTaskComponentAttr attr) {
        if (event.getRankId() != attr.getRankId()) {
            return;
        }
        if (!attr.isAutoIncTask()) {
            log.info("not auto inc task");
            return;
        }
        //等待其他时间完成
        SysEvHelper.waiting(10000);

        Clock clock = new Clock();
        long actId = event.getActId();
        Date now = commonService.getNow(actId);
        String taskTime = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        log.info("batchInitDailyTask begin,actId:{},event:{},attr:{}", actId, JSON.toJSONString(event), JSON.toJSONString(attr));
        String key = buildSingleDayTaskKey(attr, taskTime);
        Map<Object, Object> taskScore = actRedisDao.hScan(getRedisGroupCode(actId), key, SysEvHelper.isDev() ? 5 : 5000);
        for (Object keyItem : taskScore.keySet()) {
            String member = Convert.toString(keyItem);
            EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(actId, 0L, RoleType.ANCHOR.getValue(), member, 3);
            getOrInitSingleTaskValue(actId, attr, member, enrollmentInfo.getDestRoleId(), taskTime);
        }
        log.info("batchInitDailyTask done,actId:{},event:{},attr:{},size:{},clock:{}", actId, JSON.toJSONString(event), JSON.toJSONString(attr), taskScore.keySet().size(), clock.tag());
    }

    /**
     * 个人突破和全站突破任务合并查询(挂件查询用)
     */
    public List<BabyMissionItem> querySingleAndAllTask(Date now, long actId, long uid, long roleId, long index) {
        AnchorCustomTaskComponentAttr attr = getComponentAttr(actId, index);
        BabyMissionItem mission = new BabyMissionItem();

        String taskTime = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);
        long score = queryAnchorTaskScore(taskTime, actId, uid, index);
        mission.setCompletedCount(score);

        List<ActTaskScore> taskScores = queryMemberTaskConfigSortByScore(actId, attr, uid + "", roleId, taskTime);
        List<TaskItem> taskConfig = convertTaskConfigView(attr, taskScores);
        mission.setTaskItems(taskConfig);

        PairBean pairBean = getCurLevelConfig(score, taskConfig);
        long curLevelConfig = pairBean.getSecond();

        mission.setTotalCount(curLevelConfig);
        mission.setLevel(pairBean.getFirst());
        int levelIndex = Convert.toInt(pairBean.getFirst()) - 1;
        if (levelIndex >= 0 && levelIndex < taskConfig.size()) {
            mission.setTaskName(taskConfig.get(levelIndex).getName());
        }

        return Lists.newArrayList(mission);
    }

    @GetMapping("/getTaskRecord/{actId}")
    public Response<Map<String, Object>> getTaskRecord(HttpServletRequest req, HttpServletResponse resp, @PathVariable long actId, String member, long index) {
        long uid = loginService.getLoginYYUid(req, resp);
        return getTaskRecord(uid, actId, member, index);
    }

    @GetMapping("/initSingleTaskConfig/{actId}")
    public Response<String> initSingleTaskConfig(HttpServletRequest req, HttpServletResponse resp, @PathVariable long actId, long index) {
        long uid = loginService.getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(100, "未登录");
        }
        initSingleTaskData(uid, actId, index);
        return Response.success("ok");
    }


    private void handleTask(RankingScoreChanged event, AnchorCustomTaskComponentAttr attr, long actId, String member, Date eventTime) {
        String taskTime = DateUtil.format(eventTime, DateUtil.PATTERN_TYPE2);
        long uid = Convert.toLong(member);
        long roleId = getMemberRoleId(actId, member);
        List<ActTaskScore> memberTaskConfig = queryMemberTaskConfigSortByScore(actId, attr, member, roleId, taskTime);
        long rankScore = event.getRankScore();
        //本次更新分数所过的任务
        List<ActTaskScore> completedList = calculateAnchorTask(memberTaskConfig, rankScore, event.getItemScore());
        if (CollectionUtils.isEmpty(completedList)) {
            return;
        }
        //扣减奖池，保存过任务记录,用于结算发奖
        PairBean awardAmount = recordCompleteTask(taskTime, attr, memberTaskConfig, completedList, member, roleId);
        long realAward = awardAmount.getFirst();
        if (realAward <= 0) {
            log.warn("award-log-tag|奖池已经用完，本次不发放奖励,uid:{},actId:{},rankScore:{},itemScore:{}", member, attr.getActId(), event.getRankScore(), event.getItemScore());
            return;
        }

        RetryTool.withRetryCheck(actId, event.getSeq(), () -> {
            // 发奖广播,只发最高级
            ActTaskScore score = completedList.get(completedList.size() - 1);
            sendBanner(attr, event, uid, score);
            // 单播通知
            sendNotice(actId, attr, uid, score, awardAmount);
        });
    }

    /**
     * 查询用户过任务配置列表
     */
    private List<ActTaskScore> queryMemberTaskConfigSortByScore(long actId, AnchorCustomTaskComponentAttr attr, String member, long roleId, String taskTime) {
        List<ActTaskScore> actTaskScores = Lists.newArrayList();
        //个人任务
        Pair configTask = getOrInitSingleTaskValue(actId, attr, member, roleId, taskTime);
        if (configTask != null && configTask.first > 0) {
            ActTaskScore score = new ActTaskScore();
            score.setScore(configTask.first);
            score.setLevel(1L);
            score.setTaskType(SINGLE);
            score.setAward(Convert.toString(configTask.second));
            actTaskScores.add(score);
        }
        //全站任务
        String taskType = attr.getRoleIdCommonTaskType().get(roleId);
        if (StringUtil.isNotBlank(taskType)) {
            Map<Long, SunshineTaskConfig> levelScore = attr.getTaskConfig().get(taskType);
            for (Long level : levelScore.keySet()) {
                SunshineTaskConfig config = levelScore.get(level);
                ActTaskScore task = new ActTaskScore();
                task.setTaskType(taskType);
                task.setLevel(level);
                task.setRemark(config.getName());
                task.setScore(config.getPassValue());
                task.setAward(config.getAward() + "");
                actTaskScores.add(task);
            }
        }

        //按分值排序
        actTaskScores.sort(Comparator.comparing(ActTaskScore::getScore));
        return actTaskScores;
    }


    private Pair getOrInitSingleTaskValue(long actId, AnchorCustomTaskComponentAttr attr, String member, long roleId, String taskTime) {
        //非自动增长模式,返回配置固定值
        if (!attr.isAutoIncTask()) {
            long configTaskVal = getSingleTaskFixTaskValue(actId, attr, member, roleId, taskTime);
            long taskAward = getFixTaskAward(actId, attr, member, configTaskVal);
            return new Pair(configTaskVal == 0 ? attr.getDefaultTaskScore() : configTaskVal, taskAward);
        }

        //当天任务有初始化，取当天任务配置
        String redisCode = getRedisGroupCode(attr.getActId());
        String taskDayKey = buildSingleDayTaskKey(attr, taskTime);
        String taskValue = actRedisDao.hget(redisCode, taskDayKey, member);
        if (StringUtil.isNotBlank(taskValue)) {
            String taskAward = actRedisDao.hget(redisCode, buildSingleDayTaskAwardKey(attr, taskTime), member);
            return new Pair(Convert.toLong(taskValue), Convert.toLong(taskAward));
        }

        //如果前一天没有任务，并且没配置默认突破任务，则代表无突破任务
        String preDayCode = DateUtil.format(DateUtil.add(commonService.getNow(actId), -1), DateUtil.PATTERN_TYPE2);
        String preDayTaskScore = actRedisDao.hget(redisCode, preDayCode, member);
        if (StringUtil.isBlank(preDayTaskScore) && attr.getDefaultTaskScore() <= 0) {
            return null;
        }

        //用前一天任务初始化今天任务配置
        Pair pair = buildTodayTask(actId, attr, preDayTaskScore, member, preDayCode, taskTime);
        if (pair != null) {
            boolean scoreRet = actRedisDao.hsetnx(redisCode, taskDayKey, member, Convert.toString(pair.first));
            boolean awardRet = actRedisDao.hsetnx(redisCode, buildSingleDayTaskAwardKey(attr, taskTime), member, Convert.toString(pair.second));
            log.info("init day score,member:{},preScore:{},initTaskValue:{},saveRet:{}{}", member, preDayTaskScore, pair.first, scoreRet, awardRet);
        }

        return pair;
    }

    /**
     * 固定突破任务值
     */
    private long getSingleTaskFixTaskValue(long actId, AnchorCustomTaskComponentAttr attr, String member, long roleId, String taskTime) {
        String configScore = actRedisDao.hget(getRedisGroupCode(actId), buildSingleFixedTaskKey(attr), member);
        return Convert.toLong(configScore, 0);
    }


    private long getFixTaskAward(long actId, AnchorCustomTaskComponentAttr attr, String member, long configTaskVal) {
        //没过任务分值，则用默认奖励
        if (configTaskVal == 0) {
            return attr.getDefaultTaskAward();
        }
        //按比例返奖模式
        else if (StringUtil.isNotBlank(attr.getAwardRate())) {
            BigDecimal rate = new BigDecimal(attr.getAwardRate());
            return rate.multiply(new BigDecimal(configTaskVal)).longValue();
        }
        //自定义返奖模式
        else {
            String configAward = actRedisDao.hget(getRedisGroupCode(actId), buildSingleFixTaskAwardKey(attr), member);
            return Convert.toLong(configAward, attr.getDefaultTaskAward());
        }
    }

    private Pair buildTodayTask(long actId, AnchorCustomTaskComponentAttr attr, String preTaskScore, String member, String preDayCode, String taskTime) {
        long taskScore = 0;
        long taskAward = 0;
        if (StringUtil.isNotBlank(preTaskScore)) {
            String preComplete = buildMemberDayComplete(attr, preDayCode);
            if (StringUtil.isBlank(preComplete)) {
                taskScore = Convert.toLong(preTaskScore);
            } else {
                //如果有完成,以前一天最高荣耀值为准
                Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getRankId(), attr.getPhaseId(),
                        preDayCode, member, Maps.newHashMap());
                taskScore = rank.getScore();
                taskAward = new BigDecimal(taskScore).multiply(new BigDecimal(attr.getAwardRate())).longValue();
            }
        } else {
            log.info("use default task,member:{}, score:{}", member, attr.getDefaultTaskScore());
            taskScore = attr.getDefaultTaskScore();
            taskAward = attr.getDefaultTaskAward();
        }

        return new Pair(taskScore, taskAward);
    }

    private long getMemberRoleId(long actId, String member) {
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(actId, 0L, RoleType.ANCHOR.getValue(), member, 5);
        if (enrollmentInfo == null) {
            throw new RuntimeException("未找到报名信息actId:" + actId + ",memberId:" + member);
        }
        return enrollmentInfo.getDestRoleId();
    }

    private PairBean recordCompleteTask(String taskTime, AnchorCustomTaskComponentAttr attr, List<ActTaskScore> memberTaskConfig, List<ActTaskScore> completedList, String member, long roleId) {
        log.info("recordCompleteTask,taskTime:{},list:{},member:{}", taskTime, JSON.toJSONString(completedList), member);
        boolean partialAward = false;
        //本次送礼触发总发放的奖励
        long totalAward = 0;
        String group = getRedisGroupCode(attr.getActId());
        long dayAwardLimit = attr.getRoleAwardDayLimit().getOrDefault(roleId, 0L);
        for (ActTaskScore complete : completedList) {
            long award = calCurTaskIncAward(memberTaskConfig, complete);
            long itemRealAward = 0;
            boolean dayLimitPartialAward = false;

            //日限额控制
            if (dayAwardLimit > 0) {
                String dayLimitSeq = makeKey(attr, "seq:daylimit:" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
                String dayLimitKey = buildMemberDayRelease(attr, taskTime, member);
                List<Long> memberResult = actRedisDao.incrValueWithLimitSeq(group, dayLimitSeq, dayLimitKey, award, dayAwardLimit, true, attr.getSeqExpireSeconds());
                if (memberResult.get(0) > 0) {
                    itemRealAward = memberResult.get(0) == 1 ? award : memberResult.get(1);
                }
                if (itemRealAward == 0) {
                    log.warn("award-log-tag|日限额已用完,本次任务关卡不发放奖励,member:{},act:{},award:{},itemRealAward:{},completeTask:{}", member, attr.getActId(), award, itemRealAward, JSON.toJSONString(complete));
                    continue;
                }
                dayLimitPartialAward = itemRealAward > 0 && itemRealAward < award;
                if (dayLimitPartialAward) {
                    award = itemRealAward;
                    log.info("award-log-tag|用户达到日限额，部分发放，actId:{},memberId:{},award:{},itemRealAward:{}", attr.getActId(), member, award, itemRealAward);
                }
            }

            //总扣奖池限额控制
            itemRealAward = calRealAward(group, taskTime, attr, complete, member, award);

            if (itemRealAward == 0) {
                String msg = buildActRuliuMsg(attr.getActId(), false
                        , "宝藏任务奖池已用完", String.format("本次不发放奖励\n member:%s \n 原奖励:%s\n", member, award));
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());

                log.warn("award-log-tag|奖池已用完,本次任务关卡不发放奖励,member:{},act:{},award:{},itemRealAward:{},completeTask:{}", member, attr.getActId(), award, itemRealAward, JSON.toJSONString(complete));
                continue;
            }

            totalAward = totalAward + itemRealAward;
            //是否部分发奖
            boolean poolPartialAward = itemRealAward > 0 && itemRealAward < award;
            if (poolPartialAward) {
                partialAward = true;

                String msg = buildActRuliuMsg(attr.getActId(), false
                        , "宝藏任务奖池已用完", String.format("本次部分发放奖励\n member:%s \n 原奖励:%s\n 实发:%s", member, award, itemRealAward));
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
                log.info("award-log-tag|总奖池已用完，部分发放，actId:{},memberId:{},award:{},itemRealAward:{}", attr.getActId(), member, award, itemRealAward);
            }

            //发奖记录展示
            AwardRecordInfo recordInfo = new AwardRecordInfo();
            recordInfo.setDate(commonService.getNow(attr.getActId()).getTime());
            recordInfo.setTaskType(complete.getTaskType());
            recordInfo.setTaskLevel(complete.getLevel());
            recordInfo.setAward(itemRealAward);
            recordInfo.setAwardMode(poolPartialAward ? 3 : (dayLimitPartialAward ? 2 : 1));
            String recordSeq = makeKey(attr, "seq:record:" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
            actRedisDao.lPushWithSeq(group, recordSeq, buildAwardListViewKey(attr, member), JSON.toJSONString(recordInfo), attr.getSeqExpireSeconds());

            //待发放记录
            String needReleaseKey = buildAwardRecordSetViewKey(taskTime, attr);
            actRedisDao.hset(group, needReleaseKey, member + "," + complete.getTaskType() + "," + complete.getLevel(), itemRealAward + "");

            //奖励发放
            releaseAward(attr, complete, taskTime, member, itemRealAward);
            log.info("award-log-tag|award ok,actId:{},taskTime:{},taskType:{},member:{},itemRealAward:{}", attr.getActId(), taskTime, complete.getTaskType(), member, itemRealAward);

        }

        return new PairBean(totalAward, partialAward ? 1 : 0);
    }

    /**
     * 根据总限额结算本次应发
     */
    private long calRealAward(String group, String taskTime, AnchorCustomTaskComponentAttr attr, ActTaskScore complete, String member, long awardNum) {
        long poolLimit = attr.getAwardPoolConfig();
        if (poolLimit <= 0) {
            return calRealAwardFromHdzt(taskTime, attr, complete, member, awardNum);
        }

        long itemRealAward = 0;
        String poolLimitSeq = makeKey(attr, "seq:poollimit::" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
        List<Long> result = actRedisDao.incrValueWithLimitSeq(group, poolLimitSeq, buildAwardReleaseKey(attr), awardNum, poolLimit, true, attr.getSeqExpireSeconds());
        if (result.get(0) > 0) {
            itemRealAward = result.get(0) == 1 ? awardNum : result.get(1);
        }

        return itemRealAward;
    }

    private long calRealAwardFromHdzt(String taskTime, AnchorCustomTaskComponentAttr attr, ActTaskScore complete, String member, long awardNum) {
        String poolLimitSeq = attr.getActId() + ":" + attr.getCmptId() + ":" + attr.getCmptUseInx() + ":" + taskTime + ":"
                + complete.getTaskType() + ":" + complete.getLevel() + ":" + member;
        String time = DateUtil.format(new Date());
        HdztTaskPackage hdztTaskPackage = getAwardPoolHdztConfig(attr);
        Map<Long, Map<Long, Integer>> taskPackageIds = ImmutableMap.of(hdztTaskPackage.getTaskId(), ImmutableMap.of(hdztTaskPackage.getPackageId(), (int) awardNum));
        BatchWelfareResult result = hdztAwardServiceClient
                .doBatchWelfare(poolLimitSeq, Convert.toLong(member), taskPackageIds, time, 2, ImmutableMap.of("partialDeduction", "1"));
        //奖池已用完
        if (result != null && result.getCode() == WelfareCode.NO_AWARD_DJ_5204) {
            log.warn("calRealAwardFromHdzt,奖池已用完,seq:{},taskPackageIds:{},result:{}", poolLimitSeq, JSON.toJSONString(taskPackageIds), JSON.toJSONString(result));
            return 0;
        }
        if (result == null || result.getCode() != 0) {
            throw new SuperException("扣奖池失败", result.getCode());
        }

        String realCal = result.getExtData().get("realAmount");
        if (StringUtil.isBlank(realCal)) {
            log.info("奖池返回错误,缺失realAmount:{}", JSON.toJSONString(result));
            throw new SuperException("奖池返回错误", result.getCode());
        }
        JSONObject jsonObject = JSON.parseObject(realCal);
        Long realAward = jsonObject.getLong(hdztTaskPackage.getPackageId() + "");
        if (realAward == null) {
            log.info("奖池返回错误,缺失实际扣减数量:{}", JSON.toJSONString(result));
            throw new SuperException("奖池返回错误", result.getCode());
        }


        return realAward.longValue();
    }

    /**
     * 奖励发放
     */
    private void releaseAward(AnchorCustomTaskComponentAttr attr, ActTaskScore score, String taskTime, String member, long amount) {
        Map<Long, Map<Long, Long>> busiTaskIdPackageId = attr.getBusiTaskIdPackageId();
        for (Long busiId : busiTaskIdPackageId.keySet()) {
            for (Long taskId : busiTaskIdPackageId.get(busiId).keySet()) {
                long packageId = busiTaskIdPackageId.get(busiId).get(taskId);
                String seq = attr.getActId() + "_" + attr.getCmptId() + attr.getCmptUseInx() + "_" + taskTime + "_" + member + "_" + score.getTaskType() + "_" + score.getLevel();
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), busiId, Convert.toLong(member), taskId, Convert.toInt(amount), packageId, seq, Maps.newHashMap());
            }
        }
    }


    /**
     * 专题页查询任务信息
     */
    public Response<Map<String, Object>> getTaskRecord(long uid, long actId, String member, long componentUseIndex) {
        AnchorCustomTaskComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        String group = getRedisGroupCode(actId);
        Map<String, Object> result = Maps.newHashMap();
        Date now = commonService.getNow(actId);
        //当前奖池剩余数额
        result.put("awardLeft", getPoolLeft(actId, componentUseIndex));

        //我的奖励信息列表
        List<AwardRecordInfo> myAwardRecord = getAwardInfo(group, attr, uid + "");
        result.put("awardinfo", myAwardRecord);

        List<AwardRecordInfo> memberAwardRecord = getAwardInfo(group, attr, member);
        //他的总奖励
        result.put("totalAward", memberAwardRecord.stream().mapToLong(AwardRecordInfo::getAward).sum());

        //他的今日奖励
        long beginTime = DateUtil.getDayFirstSecond(now).getTime();
        long endTime = beginTime + DateUtil.ONE_DAY_MILL_SECONDS;
        result.put("dayAward", memberAwardRecord
                .stream()
                .filter(p -> p.getDate() >= beginTime && p.getDate() < endTime)
                .mapToLong(AwardRecordInfo::getAward).sum());

        long roleId = 0;
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(actId, 0L, RoleType.ANCHOR.getValue(), member, 5);
        if (enrollmentInfo != null) {
            roleId = enrollmentInfo.destRoleId;
        } else {
            roleId = queryAnchorRoleId(actId, attr.getRankMemberBusiId(), member);
        }
        if (roleId != 0) {
            List<BabyMissionItem> babyMissionItems = querySingleAndAllTask(now, actId, Convert.toLong(member), roleId, componentUseIndex);
            if (babyMissionItems != null) {
                result.put("missions", babyMissionItems);
            }
        }

        return Response.success(result);
    }

    private long queryAnchorRoleId(long actId, int busiId, String member) {
        Map<String, String> queryMap = Maps.newHashMap();
        queryMap.put(QueryType.TypeCompere.name(), member);
        Map<String, String> groupResult = ftsGroupCenterThriftClient.queryGroupV2((int) actId, queryMap, busiId, actId, commonService.isGrey(actId));
        return Convert.toLong(groupResult.getOrDefault(QueryType.TypeCompere.name(), ""), 0);
    }

    private List<AwardRecordInfo> getAwardInfo(String group, AnchorCustomTaskComponentAttr attr, String member) {
        List<AwardRecordInfo> awardRecordInfos = Lists.newArrayList();
        List<String> awardRecord = actRedisDao.lrange(group, buildAwardListViewKey(attr, member), 0, 1000);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            for (String record : awardRecord) {
                AwardRecordInfo info = JSON.parseObject(record, AwardRecordInfo.class);
                awardRecordInfos.add(info);
            }
        }

        return awardRecordInfos;
    }

    /**
     * 奖池剩余数量
     */
    public long getPoolLeft(long actId, long index) {
        AnchorCustomTaskComponentAttr attr = getComponentAttr(actId, index);
        //内置奖池
        if (attr.getAwardPoolConfig() > 0) {
            String group = getRedisGroupCode(attr.getActId());
            long award = Convert.toLong(actRedisDao.get(group, buildAwardReleaseKey(attr)), 0);
            return attr.getAwardPoolConfig() - award;
        }
        //活动中台奖池
        else {
            HdztTaskPackage hdztTaskPackage = getAwardPoolHdztConfig(attr);
            if (hdztTaskPackage == null) {
                throw new SuperException("hdztTaskPackage not config", 999);
            }
            Map<Long, PackageLeftQuota> quotaMap = hdztAwardServiceClient.getTaskLeftQuota(hdztTaskPackage.getTaskId(), commonService.getNow(actId));
            PackageLeftQuota packageLeftQuota = quotaMap.get(hdztTaskPackage.getPackageId());
            if (packageLeftQuota == null) {
                return 0;
            }
            return packageLeftQuota.getTotalLeft();
        }
    }

    /**
     * 返回上一级任务分值
     */
    private long calOldAwardScore(List<ActTaskScore> memberTaskConfig, ActTaskScore complete) {
        for (int i = 0; i < memberTaskConfig.size(); i++) {
            ActTaskScore config = memberTaskConfig.get(i);
            if (config.getLevel().equals(complete.getLevel()) && config.getTaskType().equals(complete.getTaskType())) {
                return i == 0 ? 0 : memberTaskConfig.get(i - 1).getScore();
            }
        }
        return 0;
    }

    /**
     * 算出本次完成任务的增量奖励
     */
    private long calCurTaskIncAward(List<ActTaskScore> memberTaskConfig, ActTaskScore completeTask) {
        ActTaskScore preTask = null;
        for (int i = 0; i < memberTaskConfig.size(); i++) {
            ActTaskScore config = memberTaskConfig.get(i);
            if (config.getLevel().equals(completeTask.getLevel()) && config.getTaskType().equals(completeTask.getTaskType()) && i > 0) {
                preTask = memberTaskConfig.get(i - 1);
            }
        }

        long incAward = Convert.toLong(completeTask.getAward()) - Convert.toLong(preTask == null ? 0 : preTask.getAward());
        //避免出现一些变态配置，更高级的，奖励反而更少
        return Math.max(0, incAward);
    }


    private List<ActTaskScore> calculateAnchorTask(List<ActTaskScore> memberTaskConfig, long total, long score) {
        List<ActTaskScore> completedList = new ArrayList<>();

        long src = total - score;
        for (ActTaskScore actTaskScore : memberTaskConfig) {
            Long target = actTaskScore.getScore();
            //完成任务
            if (src < target && total >= target) {
                actTaskScore.setCompleted(1L);
                actTaskScore.setcTime(new Date());
                completedList.add(actTaskScore);
            }
        }
        return completedList;

    }


    /**
     * 当前阶段的总任务数
     */
    private PairBean getCurLevelConfig(long score, List<TaskItem> taskConfig) {
        long curMinScore = 0;
        long curMaxScore = 0;
        for (int i = 0; i < taskConfig.size(); i++) {
            TaskItem item = taskConfig.get(i);
            long curGapScore = item.getPassValue();
            curMaxScore = curMinScore + curGapScore;
            if (score >= curMinScore && score < curMaxScore) {
                return new PairBean(i + 1, curMaxScore);
            }

            curMinScore = curMinScore + curGapScore;
        }

        return new PairBean(score >= curMaxScore ? taskConfig.size() : 1, curMaxScore);
    }

    private List<TaskItem> convertTaskConfigView(AnchorCustomTaskComponentAttr attr, List<ActTaskScore> taskScores) {
        List<TaskItem> result = Lists.newLinkedList();
        for (int i = 0; i < taskScores.size(); i++) {
            ActTaskScore taskScore = taskScores.get(i);
            TaskItem item = new TaskItem();
            long passValue = i == 0 ? taskScore.getScore() : taskScore.getScore() - taskScores.get(i - 1).getScore();
            item.setPassValue(passValue);
            String taskName = SINGLE.equals(taskScore.getTaskType()) ? SINGLE_NAME : taskScore.getRemark();
            item.setName(taskName);
            item.setRemark(taskName + taskScore.getLevel());
            JSONObject ext = null;
            if (StringUtil.isNotBlank(taskScore.getExtJson())) {
                ext = JSON.parseObject(taskScore.getExtJson());
            }
            if (ext == null) {
                ext = new JSONObject();
            }
            //这里是非分段
            String desc = String.format(attr.getAwardDescTips(), taskScore.getScore(), taskName);
            ext.put("desc", desc);
            ext.put("award", taskScore.getAward());
            item.setExtjson(JSON.toJSONString(ext));
            result.add(item);
        }
        return result;
    }

    /**
     * 查询任务榜分数
     */
    public long queryAnchorTaskScore(String taskTime, long actId, long uid, long index) {
        AnchorCustomTaskComponentAttr attr = getComponentAttr(actId, index);
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getRankId(), attr.getPhaseId(), taskTime,
                uid + "", Maps.newHashMap());
        return rank == null ? 0 : Math.max(0, rank.getScore());
    }


    /**
     * 触发过任务常规高光、霸屏等横幅
     */
    private void sendBanner(AnchorCustomTaskComponentAttr attr, RankingScoreChanged event, long uid, ActTaskScore taskScore) {
        String taskType = taskScore.getTaskType();
        if (!attr.getTaskBanner().containsKey(taskType)) {
            return;
        }
        if (!attr.getTaskBanner().get(taskType).containsKey(taskScore.getLevel())) {
            return;
        }
        List<BroadcastConfig> configs = attr.getTaskBanner().get(taskType).get(taskScore.getLevel());
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        for (BroadcastConfig config : configs) {
            log.info("invokeBro begin,actId:{},uid:{},taskType:{},level:{},config:{}", attr.getActId(), uid, taskType, taskScore.getLevel(), JSON.toJSONString(config));
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    invokeBro(attr, event, uid, taskScore, config);
                }
            });
        }
    }

    private void invokeBro(AnchorCustomTaskComponentAttr attr, RankingScoreChanged event, long uid, ActTaskScore taskScore, BroadcastConfig config) {
        String taskType = taskScore.getTaskType();
        //延迟广播
        if (config.getDelayMillSeconds() > 0) {
            log.info("invokeBro delay,actId:{},uid:{},taskType:{},level:{},config:{}", attr.getActId(), uid, taskType, taskScore.getLevel(), JSON.toJSONString(config));
            SysEvHelper.waiting(config.getDelayMillSeconds());
        }

        int broType = config.getBroType();
        String bannerUrl;
        //优先用任务配置,其次用默认
        if (StringUtil.isNotBlank(config.getBannerUrl())) {
            bannerUrl = config.getBannerUrl();
        } else {
            bannerUrl = attr.getDefaultBannerUrl();
        }
        UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        Long score = taskScore.getScore();

        String busiId = BusiId.MAKE_FRIEND.getValue() + "";
        String asId = "";
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, RoleType.ANCHOR.getValue(), uid + "");
        if (enrollmentInfo != null) {
            busiId = enrollmentInfo.getRoleBusiId() + "";
            asId = enrollmentInfo.getSignAsid() + "";
        } else {
            log.error("bro can not get enrollmentInfo,actId:{},memberId:{}", actId, uid + "");
        }

        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("nickName", nick);
        extMap.put("logo", StringUtil.isNotBlank(userInfo.getHdLogo()) ? userInfo.getHdLogo() : userInfo.getLogo());
        extMap.put("value", score + "");
        extMap.put("svgaUrl", bannerUrl);
        extMap.put("award", taskScore.getAward());
        extMap.put("asid", asId);
        extMap.put("ext", config.getExt());
        extMap.put("busiId", busiId);

        int bannerId = config.getBannerId();
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(bannerId).setUserNick(nick)
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        if (broType == BroadcastConfig.BroType.ACT_BUSI.code) {
            broadCastHelpService.broadcast(actId, BusiId.GAME_ECOLOGY, broType, 0L, 0L, bannerBroMsg);
        } else {
            UserCurrentChannel channel = commonService.getUserCurrentChannel(uid);
            if (channel == null) {
                log.info("log SunshineTaskComponent bro done ,user not in channel uid:{} task:{}", uid, JSON.toJSONString(taskScore));
                return;
            }
            broadCastHelpService.broadcast(actId, BusiId.findByValue(Convert.toInt(event.getBusiId())), broType, channel.getTopsid(), channel.getSubsid(), bannerBroMsg);
        }
        log.info("log SunshineTaskComponent bro done uid:{} task:{}", uid, JSON.toJSONString(taskScore));

        bigDataService.saveNoRankDataToFile(actId, BusiId.GAME_ECOLOGY, System.currentTimeMillis(), uid + ""
                , RoleType.ANCHOR, score, BigDataScoreType.SUNSHINE_TASK_BANNER, config.getDesc() + "|" + config.getBannerId() + "|" + config.getBroType());
    }

    private void sendNotice(long actId, AnchorCustomTaskComponentAttr attr, long uid, ActTaskScore taskScore, PairBean awardAmount) {
        List<TaskItem> taskItems = convertTaskConfigView(attr, Lists.newArrayList(taskScore));
        TaskItem item = taskItems.get(0);
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.ANCHOR_CUSTOM_TASK_NOTICE)
                .setNoticeMsg("")
                .setNoticeValue("")
                .setExtJson(JSON.toJSONString(ImmutableMap.of("taskName", item.getName()
                        , "taskExt", item.getExtjson()
                        , "partialAward", awardAmount.getSecond()
                        , "realRelease", awardAmount.getFirst())));
        GameecologyActivity.GameEcologyMsg msgPanel = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.unicastUid(uid, msgPanel);
        log.info("sendNotice,actId:{},uid:{},taskScore:{},item:{}", actId, uid, JSON.toJSONString(taskScore), JSON.toJSONString(item));
    }

    public void initSingleTaskData(long opUid, long actId, long index) {
        AnchorCustomTaskComponentAttr attr = getComponentAttr(actId, index);
        String url = attr.getInitSingleTaskUrl();
        if (StringUtil.isEmpty(url)) {
            log.info("url not config,actId:{}", actId);
            return;
        }
        if (!attr.getInitSingleTaskAuthUid().contains(opUid)) {
            throw new RuntimeException("初始化突破任务权限不足,opUid:" + opUid);
        }
        List<String> contents = ReadFileUtil.readFromFile(url, "utf8");
        Assert.notNull(contents);
        //转成long,用作格式校验
        Map<Long, Long> uidScores = Maps.newHashMap();
        for (String content : contents) {
            String[] array = content.split(",");
            uidScores.put(Convert.toLong(array[0]), Convert.toLong(array[1]));
        }
        String key = buildSingleFixedTaskKey(attr);
        actRedisDao.getRedisTemplate(getRedisGroupCode(actId)).executePipelined((RedisConnection connection) -> {
            for (Long uid : uidScores.keySet()) {
                connection.hSet(key.getBytes(), Convert.toString(uid).getBytes(), Convert.toString(uidScores.get(uid)).getBytes());
            }
            return null;
        });
        log.info("initSingleTaskData ok,actId:{},index:{},key:{},size:{}", actId, index, key, uidScores.keySet().size());
    }


    /**
     * 个人突破任务配置分值key
     */
    private String buildSingleFixedTaskKey(AnchorCustomTaskComponentAttr attr) {
        return makeKey(attr, MEMBER_SINGLE_FIXED_TASK);
    }

    private String buildSingleDayTaskKey(AnchorCustomTaskComponentAttr attr, String dayCode) {
        return makeKey(attr, String.format(MEMBER_SINGLE_DAY_TASK, dayCode));
    }

    private String buildSingleFixTaskAwardKey(AnchorCustomTaskComponentAttr attr) {
        return makeKey(attr, MEMBER_SINGLE_FIXED_AWARD);
    }

    private String buildSingleDayTaskAwardKey(AnchorCustomTaskComponentAttr attr, String dayCode) {
        return makeKey(attr, String.format(MEMBER_SINGLE_DAY_AWARD_CONFIG, dayCode));
    }

    private String buildMemberDayComplete(AnchorCustomTaskComponentAttr attr, String dayCode) {
        return makeKey(attr, String.format(MEMBER_SINGLE_DAY_COMPLETE, dayCode));
    }

    private String buildMemberDayRelease(AnchorCustomTaskComponentAttr attr, String taskTime, String member) {
        return makeKey(attr, String.format(DAY_AWARD_RELEASE_AMOUNT, taskTime, member));
    }

    /**
     * 奖池已发放奖励数量
     */
    private String buildAwardReleaseKey(AnchorCustomTaskComponentAttr attr) {
        return makeKey(attr, AWARD_RELEASE_AMOUNT);
    }

    /**
     * 发奖记录展示
     */
    private String buildAwardListViewKey(AnchorCustomTaskComponentAttr attr, String member) {
        return makeKey(attr, String.format(AWARD_RECORD_LIST_VIEW, member));
    }

    /**
     * 待发奖记录
     */
    private String buildAwardRecordSetViewKey(String taskTime, AnchorCustomTaskComponentAttr attr) {
        return makeKey(attr, String.format(AWARD_RECORD_SET_VIEW, taskTime));
    }

    private HdztTaskPackage getAwardPoolHdztConfig(AnchorCustomTaskComponentAttr attr) {
        return CollectionUtils.isEmpty(attr.getAwardPollHdztConfig()) ? null : attr.getAwardPollHdztConfig().get(0);
    }


}
