package com.yy.gameecology.activity.componenttest;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.hdzj.element.redis.UserTaskAchieveComponent;
import com.yy.gameecology.hdzj.element.component.attr.UserTaskAchieveComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-03 16:35
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=4"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-4.properties"
})
public class UserTaskAchieveComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "4");
    }

    @Autowired
    private UserTaskAchieveComponent userTaskAchieveComponent;

    private static final long ACT_ID = 2023114001;

    private static final long CMPT_INDEX = 810;

    @Test
    public void updateTaskTest() throws Exception {
        String eventContent = "{\"actId\":2023114001,\"actors\":{81001:\"50042952\",81003:\"2464561497\",81051:\"152004\",81070:\"10051\"},\"busiId\":810,\"ekey\":\"\",\"index\":0,\"itemCount\":1,\"itemId\":\"HDLW_001\",\"itemScore\":10,\"member\":\"2464561497\",\"occurTime\":\"2023-11-24 18:00:08\",\"phaseId\":1,\"phaseScore\":10,\"rankId\":21,\"rankScore\":10,\"seq\":\"a8dc9eaf-c1d8-4f67-9c9a-ca5060db0fa5\",\"timeKey\":0,\"timestamp\":\"2023-11-24 18:00:08\",\"uri\":2002}";
        UserTaskAchieveComponentAttr attr = userTaskAchieveComponent.getComponentAttr(ACT_ID, CMPT_INDEX);
        RankingScoreChanged event = JSON.parseObject(eventContent, RankingScoreChanged.class);
        event.setSeq(UUID.randomUUID().toString());
        event.setItemScore(10);
        userTaskAchieveComponent.onRankingScoreChanged(event, attr);


        event.setSeq(UUID.randomUUID().toString());
        event.setItemScore(50000);
        userTaskAchieveComponent.onRankingScoreChanged(event, attr);

        event.setSeq(UUID.randomUUID().toString());
        event.setItemScore(100000000);
        userTaskAchieveComponent.onRankingScoreChanged(event, attr);

        event.setSeq(UUID.randomUUID().toString());
        event.setItemScore(100000000);
        userTaskAchieveComponent.onRankingScoreChanged(event, attr);
    }

    @Test
    public void userTaskAchieveComponentTest() throws Exception {

        //---累榜过任务
        String eventContent = "{\"actId\":2023114001,\"actors\":{81001:\"50042952\",81003:\"2464561497\",81051:\"152004\",81070:\"10051\"},\"busiId\":810,\"ekey\":\"\",\"index\":0,\"itemCount\":1,\"itemId\":\"HDLW_001\",\"itemScore\":10,\"member\":\"2464561497\",\"occurTime\":\"2023-11-24 18:00:08\",\"phaseId\":1,\"phaseScore\":10,\"rankId\":21,\"rankScore\":10,\"seq\":\"a8dc9eaf-c1d8-4f67-9c9a-ca5060db0fa5\",\"timeKey\":0,\"timestamp\":\"2023-11-24 18:00:08\",\"uri\":2002}";
        UserTaskAchieveComponentAttr attr = userTaskAchieveComponent.getComponentAttr(ACT_ID, CMPT_INDEX);
        RankingScoreChanged event = JSON.parseObject(eventContent, RankingScoreChanged.class);
        event.setSeq(UUID.randomUUID().toString());
        event.setItemScore(10);
        userTaskAchieveComponent.onRankingScoreChanged(event, attr);


        event.setSeq(UUID.randomUUID().toString());
        event.setItemScore(10000);
        userTaskAchieveComponent.onRankingScoreChanged(event, attr);

        event.setSeq(UUID.randomUUID().toString());
        event.setItemScore(5000000);
        userTaskAchieveComponent.onRankingScoreChanged(event, attr);

        //---用户成就查询
        userTaskAchieveComponent.queryTaskInfo(2464561497L, ACT_ID, CMPT_INDEX);

        //进频道读取宝箱
//        userTaskAchieveComponent.listBox(attr, ACT_ID, 2464561497L, 1454054224L, 1454054224L);

        //抽宝箱
        userTaskAchieveComponent.openBox(attr, ACT_ID, 2464561497L, "6f69d282-f5a7-4557-b1d5-41539b4ef5a9");
    }

    @Test
    public void openBoxTest() throws Exception {
        UserTaskAchieveComponentAttr attr = userTaskAchieveComponent.getComponentAttr(ACT_ID, CMPT_INDEX);
        userTaskAchieveComponent.openBox(attr, ACT_ID, 2464561497L, "245bd5da-8953-463a-871d-9b4a4e717728");
    }

}
