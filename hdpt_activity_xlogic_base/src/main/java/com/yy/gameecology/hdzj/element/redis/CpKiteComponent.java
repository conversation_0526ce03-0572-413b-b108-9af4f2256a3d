package com.yy.gameecology.hdzj.element.redis;



import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.driftbottle.CpGiftDetailVo;
import com.yy.gameecology.activity.bean.driftbottle.DriftBottleGiftVo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.kite.*;
import com.yy.gameecology.activity.bean.race.LotteryInfoVo;
import com.yy.gameecology.activity.bean.rank.RankItemUserAnchor;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.service.rankext.RankExtHandler;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpKiteComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
@UseRedisStore
@RequestMapping("/cpKite")
@RestController
@Component
public class CpKiteComponent extends BaseActComponent<CpKiteComponentAttr> implements RankExtHandler {


    /**
     * 风筝当前高度
     */
    private static final String DRIFT_BOTTLE_TOTAL_STEP = "drift_bottle_total_step";

    /**
     * 路线图
     */
    private static final String ROAD_MAP_CP_ZSET_KEY = "road_map_cp_zset:%s";



    /**
     * 最强CP未弹窗key
     */
    private static final String TOP_ONE_ENTER_CHANNEL_NOTICE = "top_one_enter_channel_notice";

    private static final String LAETST_ENTER_CHANNEL_NOTICE = "latest_enter_channel_notice";

    private static final String LAST_RECEIVE_GIFT_CHANNEL = "last_receive_gift_channel:%s";

    public static final String VISIT_BEST_CP = "visit_best_cp:%s";

    /**
     * 用户进房只发一次口令抽奖
     */
    public static final String INSERT_ROOM_SEND_TOAST = "insert_room_send_toast:%s";


    /**
     * 送祝福、抽大奖
     */
    private final String LATEST_PASSWORD_LOTTERY ="latest_password_lottery";


    private final String SEND_VISITNUMS_GIFT ="send_visitnums_gift";

    /**
     * 漂流瓶幸运CP
     */
    private static final String CP_KITE_LUCKY_STATISTIC  = "kite_lucky_statistics";

    private static final long TOPONE_BRO_BANNER_ID = 5100001L;

    private static final String TOPONE_NOTICE_TYPE = "5100_top1";

    private static final String LATEST_NOTICE_TYPE = "5100_latest";

    private static final String CHANNEL_CHAT_AWARD = "5100_chatAward";

    private static final String VISITNUMS_NOTICE_TYPE = "5100_visitnums";


    private static final String CP_ROOM_TOAST = "5100_cp_room_toast";
    
	private static final String TOP_ONE_NOTICE_UNICAST_TEXT = "获得%s礼物%d个！";
   
	private static final String TOP_ONE_NOTICE_UNICAST_TEXT_TWO =  "获得%s礼物%d个\n+%s礼物%d个！";
   
	private static final String TOP_ONE_AWARD_POOL_NOTENOUGH_NOTICE_UNICAST_TEXT = "获得%s！";
    
	private static final String LATEST_NOTICE_UNICAST_TEXT = "获得%s！";
   

    private static final String VISITNUMS_NOTICE_UNICAST_TEXT = "获得%s人膜拜头像框1天！";

    public static final String TARGET_STR = "金秋辉煌胜者为王";

    /**
     * 发放礼物总金额
     */
    private static final String KITE_TOTAL_AWARD_STATISTIC = "kite_total_award_statistics";

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private RetryTool retryTool;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.CP_KITE;
    }

    @Override
    public List<String> supportKeys() {
        return null;
    }



    /**
     * 监听榜单变化
     * 更新漂流路线图、最新送礼的cp
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void handleScoreChange(RankingScoreChanged event, CpKiteComponentAttr attr) {
        long rankId = attr.getIdleRankId();
        log.info("CpKiteComponent  handleScoreChange rankId:{},event:{},attr:{}", rankId,JsonUtil.toJson(event), JsonUtil.toJson(attr));

        if (event.getRankId() != rankId) {
            return;
        }
        if (event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String eventSeq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String[] members = event.getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long babyUid = Long.parseLong(members[1]);
        long score = event.getItemScore();
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getOccurTime()));
        String groupCode = getRedisGroupCode(attr.getActId());
        String totalStepKey = getTotalStepKey(attr);
        String totalStepSeq = makeKey(attr, "seq:totalStep:" + eventSeq);
        Long totalStep = actRedisDao.hIncrByKeyWithSeq(groupCode, totalStepSeq, totalStepKey, timeCode, score, DateUtil.ONE_MONTH_SECONDS);
        if (totalStep == null) {
            log.error("CpKiteComponent handleScoreChange ignore by seq has done, actId:{},member:{},eventSeq:{},timeCode:{}", attr.getActId(), event.getMember(), eventSeq,timeCode);
            return;
        }

        //sorted存储路线图，保证totalStep顺序
        KiteRoadMapCpInfo roadMapCpInfoVo = new KiteRoadMapCpInfo();
        roadMapCpInfoVo.setUserUid(userUid);
        roadMapCpInfoVo.setBabyUid(babyUid);
        roadMapCpInfoVo.setAddStep(score);
        roadMapCpInfoVo.setCureentStep(totalStep);
        String roadMapZsetKey = getRoadMapCpZsetKey(attr, timeCode);
        actRedisDao.zAdd(groupCode,roadMapZsetKey,JsonUtil.toJson(roadMapCpInfoVo),totalStep);


        //记录当前主持送礼时最新room，兼容没有传厅的情况
        String key = makeKey(attr, String.format(LAST_RECEIVE_GIFT_CHANNEL, timeCode));
        String value = event.getActors().get(attr.getTingActor());
        if(StringUtils.isNotEmpty(value)) {
            actRedisDao.hset(groupCode, key, String.valueOf(babyUid), value);
        }

    }

    /**
     榜单结算
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void handlePhaseTimeEnd(PhaseTimeEnd event, CpKiteComponentAttr attr) {
        long rankId = attr.getIdleRankId();
        log.info("CpKiteComponent  handlePhaseTimeEnd rankId:{},event:{},attr:{}",rankId, JsonUtil.toJson(event), JsonUtil.toJson(attr));
        if (event.getRankId() != rankId) {
            return;
        }
        if (event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), rankId, attr.getPhaseId(), timeCode, Const.ONE, null);
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("handlePhaseTimeEnd empty rank,actId:{},rankId:{},phaseId:{},timeCode:{}", event.getActId(), event.getRankId(), event.getPhaseId(), timeCode);
            return;
        }

        //最强CP
        releaseTopOneAward(attr, ranks.get(0), timeCode,event);

        //幸运CP
        releaseLatestCpAward(attr,timeCode,event);


    }


    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, CpKiteComponentAttr attr) {
        long uid = event.getUid(), actId = attr.getActId(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        log.info("onUserEnterTemplate uid:{},actId:{},extJson:{},sid:{}", uid, attr.getActId(), extJson, sid);
        boolean actEnterEvent = StringUtil.isNotBlank(extJson) && extJson.contains(String.valueOf(attr.getActId()));
        //协议只发1次，确保是本次活动触发的，才弹窗
        if (!actEnterEvent) {
            log.warn("not this actId UserEnterTemplateEvent uid:{}", uid);
            return;
        }

        //TOP1 获奖弹窗
        String topKey = makeKey(attr, TOP_ONE_ENTER_CHANNEL_NOTICE);
        String redisGroup = getRedisGroupCode(attr.getActId());
        String value = actRedisDao.hget(redisGroup, topKey, Convert.toString((uid)));
        if (StringUtils.isNotEmpty(value)) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, value, StringUtils.EMPTY, uid);
            actRedisDao.hdel(redisGroup, topKey, Convert.toString((uid)));
            log.info("onUserEnterTemplate popup done,noteType:{},uid:{},data:{}", TOPONE_NOTICE_TYPE, uid, value);
        }
        String latestKey = makeKey(attr, LAETST_ENTER_CHANNEL_NOTICE);
        String latestValue = actRedisDao.hget(redisGroup,latestKey,String.valueOf(uid));
        if (StringUtils.isNotEmpty(latestValue)) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), LATEST_NOTICE_TYPE, latestValue, StringUtils.EMPTY, uid);
            actRedisDao.hdel(redisGroup, latestKey, Convert.toString((uid)));
            log.info("onUserEnterTemplate popup done,noteType:{},uid:{},data:{}", LATEST_NOTICE_TYPE, uid, latestValue);
        }


        //发放进房口令，一个用户只发送一次
        String currentTing = sid + "_" + ssid;
        Date now = commonService.getNow(attr.getActId());
        String lastBestCpTing = getLastBestCpTing(attr,now);
        if(lastBestCpTing != null && lastBestCpTing.contains(currentTing)) {
            String groupCode = getRedisGroupCode(attr.getActId());
            String timeCode = DateUtil.format(DateUtil.addHours(now,-1), DateUtil.PATTERN_TYPE7);
            String key = makeKey(attr, String.format(INSERT_ROOM_SEND_TOAST, timeCode));
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
            if(first) {
                JSONObject chatJson = new JSONObject();
                chatJson.put("text", "公屏发送“金秋辉煌，胜者为王”有机会获得秋日祝福！");
                chatJson.put("sid", sid);
                chatJson.put("ssid", ssid);

                commonBroadCastService.commonNoticeUnicast(attr.getActId(), CP_ROOM_TOAST, JsonUtil.toJson(chatJson) ,StringUtils.EMPTY
                        , event.getUid());
            }

        }

    }


    /**
     * 口令抽奖
     */
    @HdzjEventHandler(value = ChannelChatTextInnerEvent.class, canRetry = false)
    public void onChannelTextChatInnerEvent(ChannelChatTextInnerEvent event, CpKiteComponentAttr attr) {
        long sid = event.getTopsid();
        long ssid = event.getSubsid();
        long uid = event.getUid();
        String currentTing = sid + "_" + ssid;
        Date now = commonService.getNow(attr.getActId());
        String lastBestCpTing = getLastBestCpTing(attr,now);
        String seq = event.getSeq();
        if(textMatch(event.getChat()) && StringUtils.isNotEmpty(lastBestCpTing) && lastBestCpTing.contains(currentTing)) {
            String timeCode = DateUtil.format(DateUtil.addHours(now,-1), DateUtil.PATTERN_TYPE7);
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String key = makeKey(attr, String.format(VISIT_BEST_CP, timeCode));
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
            if(first){
                //判断是否发放膜拜头像框
                long visitNums = actRedisDao.hlen(groupCode, key);
                releaseAwardByVisitNums(timeCode,visitNums,attr,now,seq);

                String time = DateUtil.format(now);
                String lotterySeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_chat_"+ timeCode + "_" + uid));
                BatchLotteryResult result = hdztAwardServiceClient.doLottery(time, attr.getBusiId(),
                        uid, attr.getLotteryTaskId(), 1, 0, lotterySeq);
                log.info("onChannelTextChatInnerEvent uid:{} lottery result:{}", uid, JsonUtil.toJson(result));
                Response<List<CpKiteComponentAttr.Award>> response = lotteryAward(result, attr);
                if(response.success()) {
                    JSONObject extJson = new JSONObject();
                    extJson.put("hit", false);
                    if(!response.getData().isEmpty()) {
                        CpKiteComponentAttr.Award award = response.getData().get(0);
                        extJson.put("name", award.getName());
                        extJson.put("icon", award.getImg());
                        extJson.put("awardCount", 1);
                        extJson.put("hit", true);
                    }
                    commonBroadCastService.commonNoticeUnicast(attr.getActId(), CHANNEL_CHAT_AWARD, extJson.toJSONString()
                            ,StringUtils.EMPTY , uid);
                }
            }else{
                log.warn("onChannelTextChatInnerEvent not first uid:{},sid:{},ssid:{}",uid,sid,ssid);
            }
        }else{
            log.info("onChannelTextChatInnerEvent not match uid:{},sid:{},ssid:{},chat:{}",uid,sid,ssid,event.getChat());
        }

    }


    private void releaseAwardByVisitNums(String timeCode,long visitNums,CpKiteComponentAttr attr,Date now,String eventSeq) {
        String time = DateUtil.format(now);
        VisitNumsGiftVo visitNumsGiftVo = attr.findVisitGift(visitNums);
        if(visitNumsGiftVo!=null) {
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String sendVisitGiftKey = makeKey(attr, SEND_VISITNUMS_GIFT);
            if(actRedisDao.hsetnx(groupCode,sendVisitGiftKey,timeCode+":"+visitNumsGiftVo.getVisitNum(),DateUtil.getNowYyyyMMddHHmmss())){
                String lotteryKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
                String value = actRedisDao.hget(groupCode, lotteryKey,timeCode);
                if(StringUtils.isNotEmpty(value)) {
                    BestCpInfo bestCpInfo = JSON.parseObject(value, BestCpInfo.class);
                    String userHashSeq = MD5SHAUtil.getMD5(makeKey(attr,eventSeq + "_visitnums_"+ timeCode + "_" + bestCpInfo.getUserUid() ));
                    String babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr,eventSeq + "_visitnums_"+ timeCode + "_" + bestCpInfo.getBabyUid() ));

                    hdztAwardServiceClient.doWelfare(time, BusiId.GAME_ECOLOGY.getValue(), bestCpInfo.getUserUid(),
                            visitNumsGiftVo.getTaskId(), 1, visitNumsGiftVo.getPackageId(), userHashSeq, 3);
                    hdztAwardServiceClient.doWelfare(time, BusiId.GAME_ECOLOGY.getValue(), bestCpInfo.getBabyUid(),
                            visitNumsGiftVo.getTaskId(), 1, visitNumsGiftVo.getPackageId(), babyHashSeq, 3);

                    KiteNoticeInfo json = new KiteNoticeInfo();
                    String noticeTime = null;
                    try {
                        noticeTime = DateUtil.format(DateUtils.parseDate(timeCode,DateUtil.PATTERN_TYPE7),DateUtil.PATTERN_TYPE13);
                    } catch (ParseException e) {
                        log.error("CpKiteComponent noticeTime error ,timeCode:{}",timeCode);
                    }

                    String visitNumsText = visitNums>=100 ? "百" : "十";
                    String awardTexts = String.format(VISITNUMS_NOTICE_UNICAST_TEXT,visitNumsText);
                    json.setAwardText(awardTexts);
                    json.setAwardLogo(Lists.newArrayList(visitNumsGiftVo.getGiftLogo()));
                    json.setAwardTime(timeCode);
                    json.setVisitNums(visitNums);
                    log.info("releaseAwardByVisitNums commonNoticeUnicast actId:{},timeCode:{},userUid:{},babyUid:{}",attr.getActId(),timeCode,bestCpInfo.getUserUid(),bestCpInfo.getBabyUid());
                    commonBroadCastService.commonNoticeUnicast(attr.getActId(), VISITNUMS_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, bestCpInfo.getUserUid());
                    commonBroadCastService.commonNoticeUnicast(attr.getActId(), VISITNUMS_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, bestCpInfo.getBabyUid());

                }
            }else{
                log.info("releaseAwardByVisitNums has send gift timeCode:{}",timeCode);
            }
        }


    }

    public Response<List<CpKiteComponentAttr.Award>> lotteryAward(BatchLotteryResult batchLotteryResult, CpKiteComponentAttr attr) {
        if (batchLotteryResult.getCode() != 0) {
            return Response.fail(3, batchLotteryResult.getReason());
        }
        List<CpKiteComponentAttr.Award> awardList = Lists.newArrayList();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Long, Long> recordIds = batchLotteryResult.getRecordPackages();
        Map<Long, Integer> pidCount = Maps.newHashMap();
        for (Long pid : recordIds.values()) {
            pidCount.merge(pid, 1, Integer::sum);
        }

        for (Map.Entry<Long, Integer> entry : pidCount.entrySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(entry.getKey());
            if (awardModelInfo != null) {
                if(awardModelInfo.getPackageName().contains("谢谢参与")) {
                    continue;
                }
                CpKiteComponentAttr.Award award = new CpKiteComponentAttr.Award();
                award.setName(awardModelInfo.getPackageName());
                award.setImg(awardModelInfo.getPackageImage());
                award.setNum(entry.getValue() == 1 ? 0 : entry.getValue());
                awardList.add(award);
            }
        }
        return Response.success(awardList);
    }

    public Map<Long, AwardModelInfo> packageInfoMap(CpKiteComponentAttr attr) {
        try {

            Map<Long, AwardModelInfo> visit = hdztAwardServiceClient.queryAwardTasks(attr.getLotteryTaskId());
            return  visit == null ? Collections.emptyMap() : visit;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }


    public boolean textMatch(String chatText) {
        String cleanedInput = chatText.replaceAll("[\\p{P}\\p{S}\\s]+", "");
        return containsTargetSequence(cleanedInput, TARGET_STR);
    }

    public static boolean containsTargetSequence(String input, String target) {
        int targetIndex = 0;
        for (int i = 0; i < input.length(); i++) {
            if (input.charAt(i) == target.charAt(targetIndex)) {
                targetIndex++;
                if (targetIndex == target.length()) {
                    return true;
                }
            }
        }
        return false;
    }
    public String getLastBestCpTing(CpKiteComponentAttr attr, Date now) {

        String timeCode = DateUtil.format(DateUtil.addHours(now,-1), DateUtil.PATTERN_TYPE7);
        String redisGroup = getRedisGroupCode(attr.getActId());
        String lotteryKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
        String value = actRedisDao.hget(redisGroup, lotteryKey,timeCode);

        if(StringUtils.isNotEmpty(value)) {
            LotteryInfoVo bestCpInfo = JSON.parseObject(value, LotteryInfoVo.class);
            return bestCpInfo.getSid() + "_" + bestCpInfo.getSsid();

        }

        return "";
    }


    /**
     * 风筝路线图
     */
    @RequestMapping(value = "/getRoadMapCpList")
    public Response<KiteRoadMap> getRoadMapCpList(@RequestParam("actId") long actId,
                                                  @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx,
                                                  String dateStr) {

        CpKiteComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        List<KiteRoadMapCpInfo> list = Lists.newArrayList();
        String groupCode = getRedisGroupCode(attr.getActId());
        String roadMapZsetKey = getRoadMapCpZsetKey(attr, dateStr);
        Set<ZSetOperations.TypedTuple<String>> tuples  = actRedisDao.zrevRange(groupCode,roadMapZsetKey,attr.getRoadMapTopN());

        KiteRoadMap rsp = new KiteRoadMap();
        if (!CollectionUtils.isEmpty(tuples)) {
            Set<Long> uids = Sets.newHashSetWithExpectedSize(tuples.size()*2);
            for (ZSetOperations.TypedTuple<String> tuple : tuples) {
                KiteRoadMapCpInfo record = JSON.parseObject( tuple.getValue(), KiteRoadMapCpInfo.class);
                uids.add(record.getUserUid());
                uids.add(record.getBabyUid());
                list.add(record);
            }
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,810);
            list.stream().forEach(v -> {
                long babyUid = v.getBabyUid();
                long userUid = v.getUserUid();
                //获取正在开播频道
                ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(babyUid);
                if (onMicChannel != null) {
                    v.setSid(onMicChannel.getSid());
                    v.setSsid(onMicChannel.getSsid());
                }

                if(userInfos!=null && userInfos.containsKey(userUid)) {
                    v.setUserLogo(userInfos.get(userUid).getAvatarUrl());
                    v.setUserNick(userInfos.get(userUid).getNick());
                }
                if(userInfos!=null && userInfos.containsKey(babyUid)) {
                    v.setBabyLogo(userInfos.get(babyUid).getAvatarUrl());
                    v.setBabyNick(userInfos.get(babyUid).getNick());
                }

            });

            rsp.setList(list);
            rsp.setNickExtUsers(multiNickUsers);
        }


        return  Response.success(rsp);

    }

    /**
     * 历史最强cp膜拜信息
     */
    @RequestMapping("/getBestCp")
    public Response<KiteLotteryCpInfo>  getBestCp(Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx, String dateStr){
        CpKiteComponentAttr attr = getComponentAttr(actId,cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }


        KiteLotteryCpInfo rsp = new KiteLotteryCpInfo();

        Date now = commonService.getNow(actId);
        String lastTimeCode = DateUtil.format(DateUtil.addHours(now,-1), DateUtil.PATTERN_TYPE7);

        String redisGroup = getRedisGroupCode(attr.getActId());
        String lotteryKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
        String value = actRedisDao.hget(redisGroup, lotteryKey,dateStr);
        if(StringUtils.isNotEmpty(value)){
            rsp.setShow(true);
            BestCpInfo bestCpInfo = JSON.parseObject(value,BestCpInfo.class);
            List<Long> uids = Lists.newArrayList(bestCpInfo.getUserUid(),bestCpInfo.getBabyUid());
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,810);
            UserInfoVo userInfoVo = userInfos.get(bestCpInfo.getUserUid());
            UserInfoVo babyInfoVo =  userInfos.get(bestCpInfo.getBabyUid());
            if (userInfoVo != null) {
                bestCpInfo.setUserNick(userInfoVo.getNick());
                bestCpInfo.setUserLogo(userInfoVo.getAvatarUrl());
            }
            if(babyInfoVo!=null) {
                bestCpInfo.setBabyNick(babyInfoVo.getNick());
                bestCpInfo.setBabyLogo(babyInfoVo.getAvatarUrl());
            }
            if(StringUtils.isNotEmpty(bestCpInfo.getAwardText())){
                bestCpInfo.setAwardText("已获得"+bestCpInfo.getAwardText());
            }

            rsp.setCpInfo(bestCpInfo);
            rsp.setNickExtUsers(multiNickUsers);

            String visitBestCpKey = makeKey(attr, String.format(VISIT_BEST_CP, dateStr));
            long visitNums = actRedisDao.hlen(redisGroup, visitBestCpKey);

            rsp.setVisitNums(visitNums);
            if( !lastTimeCode.equals(dateStr)) {
                rsp.setVisitEnd(true);
            }else{
                rsp.setVisitEnd(false);
            }
            rsp.setChatText("金秋辉煌，胜者为王");

        }else{
            rsp.setShow(false);
        }

        return Response.success(rsp);
    }


    /**
     * 查询奖池剩余 ，小于5元清零
     */
    @GetMapping("/queryAwardPool")
    public Response<Map<String, Object>> queryAwardPool(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(name = "actId") long actId,
                                                        @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx) {
        CpKiteComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        Map<String, Object> result = Maps.newHashMap();

        long send = getAwardPoolConsume(attr);
        long left = Math.max(attr.getTotalPool() - send, 0);

        result.put("total", attr.getTotalPool());
        result.put("send", send);
        result.put("left", left);

        return Response.success(result);
    }


    private long getAwardPoolLeft(CpKiteComponentAttr attr) {
        long send = getAwardPoolConsume(attr);
        long left = Math.max(attr.getTotalPool() - send, 0);
        return left;
    }
    private long getAwardPoolConsume(CpKiteComponentAttr attr) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String totalAwardKey = makeKey(attr,KITE_TOTAL_AWARD_STATISTIC);
        return Convert.toLong(actRedisDao.get(redisCode, totalAwardKey), 0);
    }





    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        CpKiteComponentAttr attr = tryGetUniqueComponentAttr(rankReq.getActId());
        if (attr == null || CollectionUtils.isEmpty(objectList)) {
            return objectList;
        }

        if(attr.getIdleRankId()==rankReq.getRankId()) {

            RankItemUserAnchor rankItem = (RankItemUserAnchor) objectList.get(0);
            if (rankItem.getViewExt() == null) {
                rankItem.setViewExt(new HashMap<>(2));
            }

            if (rankItem.getUserRankItem() != null && rankItem.getBabyRankItem() != null) {
                long totalStep = getTotalStep(attr, rankReq.getDateStr());
                long exchangeGiftStep = exchangeGiftStep(attr,totalStep);
                rankItem.getViewExt().put("totalStep", String.valueOf(totalStep));
                if (totalStep > 0) {
                    List<CpGiftDetailVo> giftDetail = getGiftDetailByTotalStep(attr,rankReq.getDateStr(),exchangeGiftStep);
                    rankItem.getViewExt().put("giftDetail",JsonUtil.toJson(giftDetail));
                }
            }

        }



        return objectList;
    }

    private List<CpGiftDetailVo> getGiftDetailByTotalStep(CpKiteComponentAttr attr ,String timeCode,long exchangeGiftStep) {
        //从大到小排序
        List<DriftBottleGiftVo> top1ConfigGift = attr.getTop1GiftInfo().stream().sorted(Comparator.comparing(DriftBottleGiftVo::getStep).reversed()).toList();
        DriftBottleGiftVo minStepConfig = getMinStepAwardConfig(attr);

        //剩余奖池为0
        if(getAwardPoolLeft(attr)<=0){
            List<CpGiftDetailVo> result = top1ConfigGift.stream().map(v->{
                CpGiftDetailVo item = new CpGiftDetailVo();
                item.setGiftName(v.getGiftName());
                item.setGiftLogo(v.getGiftLogo());
                item.setGiftCount(0);
                return item;
            }).collect(Collectors.toList());
            return result;
        }

        List<CpGiftDetailVo> giftLst = Lists.newArrayList();
        DriftBottleGiftVo awardAConfig = attr.findAward(exchangeGiftStep);
        if (awardAConfig == null) {
            log.error("getGiftDetailByTotalStep calAwardConfig is null,actId:{},timeCode:{}",attr.getActId(),timeCode);
            return giftLst;
        }

        long multipleStep = exchangeGiftStep / awardAConfig.getStep();
        if (multipleStep <= 0) {
            log.error("getGiftDetailByTotalStep multipleStep <=0 ,actId:{},timeCode:{}",attr.getActId(),timeCode);
            return giftLst;
        }

        long awardACount = multipleStep * awardAConfig.getStep() * attr.getStepExchangeGiftUnit() / awardAConfig.getGiftPrice();
        long remainderStep = exchangeGiftStep % awardAConfig.getStep();
        DriftBottleGiftVo awardBConfig = null;
        long awardBCount = 0L;
        if (remainderStep > 0) {
            awardBConfig = minStepConfig;
            if (awardBConfig == null) {
                log.error("releaseTopOneAward getMinStepAwardConfig is null");
                return giftLst;
            }
            awardBCount = remainderStep * attr.getStepExchangeGiftUnit() / awardBConfig.getGiftPrice();
        }

        CpGiftDetailVo itemA = new CpGiftDetailVo();
        itemA.setGiftName(awardAConfig.getGiftName());
        itemA.setGiftCount(awardACount*2);
        itemA.setGiftLogo(awardAConfig.getGiftLogo());
        if(awardBCount>0){
            giftLst.add(itemA);
            CpGiftDetailVo itemB = new CpGiftDetailVo();
            itemB.setGiftName(awardBConfig.getGiftName());
            itemB.setGiftCount(awardBCount*2);
            itemB.setGiftLogo(awardBConfig.getGiftLogo());
            giftLst.add(itemB);
        }else{
            if(awardAConfig.getStep()==attr.getTop1GiftMinStep()){
                CpGiftDetailVo itemB = new CpGiftDetailVo();
                itemB.setGiftName(top1ConfigGift.get(0).getGiftName());
                itemB.setGiftCount(0L);
                itemB.setGiftLogo(top1ConfigGift.get(0).getGiftLogo());
                giftLst.add(itemB);
                giftLst.add(itemA);
            }else{
                giftLst.add(itemA);
                CpGiftDetailVo itemB = new CpGiftDetailVo();
                itemB.setGiftName(minStepConfig.getGiftName());
                itemB.setGiftCount(0L);
                itemB.setGiftLogo(minStepConfig.getGiftLogo());
                giftLst.add(itemB);
            }
        }

        return giftLst;
    }


    private long exchangeGiftStep(CpKiteComponentAttr attr,long totalStep ) {
        long exchangeGiftStep = totalStep;
        if(attr.getExchangeGiftStepLimit()>0 && totalStep>attr.getExchangeGiftStepLimit()){
            exchangeGiftStep = attr.getExchangeGiftStepLimit();
        }
        return exchangeGiftStep;
    }



    private void releaseLatestCpAward(CpKiteComponentAttr attr, String timeCode,PhaseTimeEnd event) {
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String groupCode = getRedisGroupCode(attr.getActId());

        String roadMapZsetKey = getRoadMapCpZsetKey(attr, timeCode);
        Set<ZSetOperations.TypedTuple<String>> roadMapZset = actRedisDao.zrevRange(groupCode,roadMapZsetKey,1);

        if (CollectionUtils.isNotEmpty(roadMapZset)) {
            KiteRoadMapCpInfo record = JSON.parseObject(roadMapZset.stream().findFirst().get().getValue(), KiteRoadMapCpInfo.class);
            long userUid = record.getUserUid();
            long babyUid = record.getBabyUid();
            String welfareTime = DateUtil.format(commonService.getNow(attr.getActId()));
            String userHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_latestcp_"+ timeCode + "_" + userUid ));
            String babyHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_latestcp_"+ timeCode + "_" + babyUid ));
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), userUid, attr.getLatestCpTaskId(), 1, attr.getLatestCpPackageId(), userHashSeq, 2);
            boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if(!suc) {
                // 需要人工介入处理
                log.error("DriftBottleComponent user releaseLatestCpAward error,  actId:{}, uid:{}, seq:{}  ret:{}", attr.getActId(), userUid, userHashSeq, batchWelfareResult);
            }

            if(userUid!=babyUid){
                batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), babyUid, attr.getLatestCpTaskId(), 1, attr.getLatestCpPackageId(), babyHashSeq, 2);
                suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
                if(!suc) {
                    // 需要人工介入处理
                    log.error("DriftBottleComponent baby releaseLatestCpAward error,  actId:{}, uid:{}, seq:{}  ret:{}", attr.getActId(), babyUid, babyHashSeq, batchWelfareResult);
                }
            }


            //数据统计-保存幸运CP
            saveLuckyCpInfo( attr, userUid, babyUid, timeCode, record.getCureentStep());

            //幸运CP弹窗
            String noticeSeq = "seq:latestnotice:" + seq;
            retryTool.asyWithRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
                try {
                    Thread.sleep(7000);
                    unicastLuckyCpNotice(timeCode,userUid,babyUid,attr);
                } catch (InterruptedException e) {
                    log.error("delay send unicast interrupted:", e);
                }

            },Const.GENERAL_POOL);

        }
    }

    private void saveLuckyCpInfo(CpKiteComponentAttr attr, long userUid, long babyUid, String timeCode, long cureentStep) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        String luckyKey = makeKey(attr,CP_KITE_LUCKY_STATISTIC);
        KiteRoadMapCpInfo toponeInfo = new KiteRoadMapCpInfo();
        toponeInfo.setUserUid(userUid);
        toponeInfo.setBabyUid(babyUid);
        toponeInfo.setCureentStep(cureentStep);
        actRedisDao.hset(redisGroup,luckyKey,timeCode, JsonUtil.toJson(toponeInfo));
    }

    private void unicastLuckyCpNotice(String timeCode,long userUid,long babyUid,CpKiteComponentAttr attr) {

        //幸运CP弹窗
        KiteNoticeInfo json = new KiteNoticeInfo();
        String noticeTime = null;

        try {
            noticeTime = DateUtil.format(DateUtils.parseDate(timeCode,DateUtil.PATTERN_TYPE7),DateUtil.PATTERN_TYPE13);
        } catch (ParseException e) {
            log.error("CpKiteComponent unicastLuckyCpNotice noticeTime error ,timeCode:{}",timeCode);
        }

        String awardTexts = String.format(LATEST_NOTICE_UNICAST_TEXT,attr.getLatestCpShowName());
        json.setAwardText(awardTexts);
        json.setAwardLogo(Lists.newArrayList(attr.getLatestCpShowUrl()));
        json.setAwardTime(timeCode);
        log.info("releaseLatestCpAward commonNoticeUnicast actId:{},timeCode:{},userUid:{},babyUid:{}",attr.getActId(),timeCode,userUid,babyUid);
        String key = makeKey(attr, LAETST_ENTER_CHANNEL_NOTICE);
        UserCurrentChannel userChannel = commonService.getUserCurrentChannel(userUid);
        UserCurrentChannel babyChannel = commonService.getUserCurrentChannel(babyUid);
        if(userChannel!=null){
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), LATEST_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, userUid);
        }else{
            log.warn("releaseLatestCpAward userUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),userUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(userUid),JsonUtil.toJson(json));
        }

        if(babyChannel!=null) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), LATEST_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, babyUid);
        }else{
            log.warn("releaseLatestCpAward babyUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),babyUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(babyUid),JsonUtil.toJson(json));
        }

    }


    private void releaseTopOneShow(CpKiteComponentAttr attr, Rank rank, String timeCode,PhaseTimeEnd event) {
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String[] members = rank.getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long babyUid = Long.parseLong(members[1]);
        String welfareTime = DateUtil.format(commonService.getNow(attr.getActId()));
        String userHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + userUid));
        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), userUid, attr.getLatestCpTaskId(), 1, attr.getTop1CpPackageId(), userHashSeq, 2);
        boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if(!suc) {
            // 需要人工介入处理
            log.error("releaseTopOneShow user doWelfare error,  actId:{}, uid:{}, seq:{},taskId:{}, packageId:{}, ret:{}", attr.getActId(), userUid, userHashSeq,
                    attr.getLatestCpTaskId(), attr.getTop1CpPackageId(), batchWelfareResult);
        }

        String babyHashSeq = null;
        if(userUid!=babyUid){
            babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + babyUid ));
            batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), babyUid,attr.getLatestCpTaskId(), 1, attr.getTop1CpPackageId(), babyHashSeq, 2);
            suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if(!suc) {
                // 需要人工介入处理
                log.error("releaseTopOneShow baby doWelfare  error,  actId:{}, uid:{}, seq:{}, taskId:{}, packageId:{}, ret:{}", attr.getActId(), babyUid, babyHashSeq,
                        attr.getLatestCpTaskId(), attr.getTop1CpPackageId(),batchWelfareResult);
            }
        }

        log.info("CpKiteComponent releaseTopOneShow ,actId:{},timeCode:{},userUid:{},babyUid:{}",attr.getActId(),timeCode,userUid,babyUid);


        //保存本轮最强CP
        saveToponeCpInfoShow(attr,userUid,babyUid,timeCode,rank.getScore());

        //cp特效
        broTopBanner(userUid,babyUid,attr,seq);

        //弹窗
        String noticeSeq = makeKey(attr, "seq:topnotice:" + seq);
        retryTool.asyWithRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            try {
                Thread.sleep(7000);
                unicastTopNoticeShow(timeCode,userUid,babyUid,attr);
            } catch (InterruptedException e) {
                log.error("delay send unicast interrupted:", e);
            }

        },Const.GENERAL_POOL);
    }

    private void saveToponeCpInfoShow(CpKiteComponentAttr attr,long userUid,long babyUid,String timeCode,long addStep) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        UserCurrentChannel channel = getAnchorAwardChannel(attr, babyUid,timeCode);
        if (channel != null) {
            String awardText  = String.format("%s",attr.getTop1CpShowName());

            String lotteryKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
            BestCpInfo value = new BestCpInfo();
            value.setUserUid(userUid);
            value.setBabyUid(babyUid);
            value.setSid(channel.getTopsid());
            value.setSsid(channel.getSubsid());
            value.setAwardText(awardText);
            value.setStaticAwardText(awardText);
            value.setAddStep(addStep);
            actRedisDao.hset(redisGroup, lotteryKey,timeCode,JsonUtil.toJson(value));
        }else{
            log.warn("handlePhaseTimeEndSaveData password lottery channel is null,userUid:{},anchorUid:{},timeCode:{}",userUid,babyUid,timeCode);
        }
    }

    private void unicastTopNoticeShow(String timeCode,long userUid,long babyUid,CpKiteComponentAttr attr) {
        KiteNoticeInfo json = new KiteNoticeInfo();
        String noticeTime = null;
        try {
            noticeTime = DateUtil.format(DateUtils.parseDate(timeCode,DateUtil.PATTERN_TYPE7),DateUtil.PATTERN_TYPE13);
        } catch (ParseException e) {
            log.error("CpKiteComponent noticeTime error ,timeCode:{}",timeCode);
        }


        String awardTexts = String.format(TOP_ONE_AWARD_POOL_NOTENOUGH_NOTICE_UNICAST_TEXT,attr.getTop1CpShowName());
        List<String> awardLogo = Lists.newArrayList(attr.getLatestCpShowUrl());
        json.setAwardText(awardTexts);
        json.setAwardLogo(awardLogo);
        json.setAwardTime(timeCode);
        log.info("releaseTopOneAward unicastTopNoticeShow actId:{},timeCode:{},userUid:{},babyUid:{},awardTexts:{}",attr.getActId(),timeCode,userUid,babyUid,awardTexts);

        String key = makeKey(attr, TOP_ONE_ENTER_CHANNEL_NOTICE);
        UserCurrentChannel userChannel = commonService.getUserCurrentChannel(userUid);
        UserCurrentChannel babyChannel = commonService.getUserCurrentChannel(babyUid);
        if(userChannel!=null){
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, userUid);
        }else{
            log.warn("unicastTopNoticeShow userUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),userUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(userUid),JsonUtil.toJson(json));
        }

        if(babyChannel!=null) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, babyUid);
        }else{
            log.warn("unicastTopNoticeShow babyUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),babyUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(babyUid),JsonUtil.toJson(json));
        }
    }
    private void releaseTopOneAward(CpKiteComponentAttr attr, Rank rank, String timeCode,PhaseTimeEnd event) {

        //结算时奖池为0，则发放进场秀
        if(getAwardPoolLeft(attr)<=0){
             releaseTopOneShow( attr,  rank,  timeCode, event);
             return;
        }
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String[] members = rank.getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long babyUid = Long.parseLong(members[1]);

        //获取CP总里程
        long totalStep = getTotalStep(attr, timeCode);
        if (totalStep <= 0) {
            log.error("releaseTopOneAward totalStep is 0,actId:{},timeCode:{},members:{}",attr.getActId(),timeCode,rank.getMember());
            return;
        }

        long exchangeGiftStep = exchangeGiftStep(attr,totalStep);
        DriftBottleGiftVo awardAConfig = attr.findAward(exchangeGiftStep);
        if (awardAConfig == null) {
            log.error("releaseTopOneAward calAwardConfig is null,actId:{},timeCode:{},members:{}",attr.getActId(),timeCode,rank.getMember());
            return;
        }

        long multipleStep = exchangeGiftStep / awardAConfig.getStep();
        if (multipleStep <= 0) {
            log.error("releaseTopOneAward multipleStep <=0 ,actId:{},timeCode:{},members:{}",attr.getActId(),timeCode,rank.getMember());
            return;
        }

        long awardACount = multipleStep * awardAConfig.getStep() * attr.getStepExchangeGiftUnit() / awardAConfig.getGiftPrice();
        long remainderStep = exchangeGiftStep % awardAConfig.getStep();
        DriftBottleGiftVo awardBConfig = null;
        long awardBCount = 0L;
        if (remainderStep > 0) {
            awardBConfig = getMinStepAwardConfig(attr);
            if (awardBConfig == null) {
                log.error("releaseTopOneAward getMinStepAwardConfig is null");
                return;
            }
            awardBCount = remainderStep * attr.getStepExchangeGiftUnit() / awardBConfig.getGiftPrice();
        }

        String welfareTime = DateUtil.format(commonService.getNow(attr.getActId()));
        String userHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + userUid +"_A"));
        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), userUid, awardAConfig.getTaskId(), (int)awardACount, awardAConfig.getPackageId(), userHashSeq, 2);
        boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if(!suc) {
            // 需要人工介入处理
            log.error("CpKiteComponent user doWelfare A error,  actId:{}, uid:{}, seq:{},taskId:{}, packageId:{},awardACount:{}, ret:{}", attr.getActId(), userUid, userHashSeq,
                    awardAConfig.getTaskId(), awardAConfig.getPackageId(),awardACount, batchWelfareResult);
        }

        String babyHashSeq = null;
        if(userUid!=babyUid){
            babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + babyUid +"_A"));
            batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), babyUid, awardAConfig.getTaskId(), (int)awardACount, awardAConfig.getPackageId(), babyHashSeq, 2);
            suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if(!suc) {
                // 需要人工介入处理
                log.error("CpKiteComponent baby doWelfare A error,  actId:{}, uid:{}, seq:{}, taskId:{}, packageId:{},awardACount:{}, ret:{}", attr.getActId(), babyUid, babyHashSeq,
                        awardAConfig.getTaskId(), awardAConfig.getPackageId(),awardACount,batchWelfareResult);
            }
        }

        if(awardBCount>0) {
            userHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + userUid +"_B"));
            babyHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + babyUid +"_B"));
            batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), userUid, awardBConfig.getTaskId(), (int)awardBCount, awardBConfig.getPackageId(), userHashSeq, 2);
            suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if(!suc) {
                // 需要人工介入处理
                log.error("CpKiteComponent user doWelfare B error,  actId:{}, uid:{}, seq:{}  ,taskId:{}, packageId:{},awardBCount:{},ret:{}", attr.getActId(), userUid, userHashSeq,
                        awardBConfig.getTaskId(), awardBConfig.getPackageId(),awardBCount,batchWelfareResult);
            }
            if(userUid!=babyUid){
                batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), babyUid, awardBConfig.getTaskId(), (int)awardBCount, awardBConfig.getPackageId(), babyHashSeq, 2);
                suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
                if(!suc) {
                    // 需要人工介入处理
                    log.error("CpKiteComponent baby doWelfare B error,  actId:{}, uid:{}, seq:{} ,taskId:{}, packageId:{},awardBCount:{},  ret:{}", attr.getActId(), babyUid, babyHashSeq,
                            awardBConfig.getTaskId(), awardBConfig.getPackageId(),awardBCount, batchWelfareResult);
                }
            }
        }

        log.info("CpKiteComponent releaseTopOneAward ,actId:{},timeCode:{},userUid:{},babyUid:{},totalStep:{},awardACount:{},awardBCount:{},exchangeGiftStep:{}",attr.getActId(),timeCode,userUid,babyUid,
                totalStep,awardACount,awardBCount,exchangeGiftStep);



        //保存本轮最强CP
        DriftBottleGiftVo finalAwardBConfig = awardBConfig;
        long finalAwardBCount = awardBCount;
        saveToponeCpInfo(attr,userUid,babyUid,timeCode,awardAConfig, finalAwardBConfig,awardACount, finalAwardBCount,rank.getScore());

        //累计发放金额
        String redisGroup = getRedisGroupCode(attr.getActId());
        String totalAwardKey = makeKey(attr,KITE_TOTAL_AWARD_STATISTIC);
        String totalAwardSeq = makeKey(attr,"seq:totalaward:"+seq+":"+timeCode);
        actRedisDao.incrValueWithSeq(redisGroup,totalAwardSeq,totalAwardKey,2*exchangeGiftStep*attr.getStepExchangeGiftUnit(),24 * 60 * 60);

        //cp特效
        broTopBanner(userUid,babyUid,attr,seq);

        //弹窗
        String noticeSeq = makeKey(attr, "seq:topnotice:" + seq);
        retryTool.asyWithRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            try {
                Thread.sleep(7000);
                unicastTopNotice(timeCode,userUid,babyUid,attr,exchangeGiftStep,awardAConfig, finalAwardBConfig,awardACount, finalAwardBCount);
            } catch (InterruptedException e) {
                log.error("delay send unicast interrupted:", e);
            }

        },Const.GENERAL_POOL);

    }



    private void saveToponeCpInfo(CpKiteComponentAttr attr,long userUid,long babyUid,String timeCode,DriftBottleGiftVo awardAConfig,DriftBottleGiftVo awardBConfig,
                                  long awardACount,long awardBCount,long addStep) {
        String redisGroup = getRedisGroupCode(attr.getActId());
        UserCurrentChannel channel = getAnchorAwardChannel(attr, babyUid,timeCode);
        if (channel != null) {
            String awardText = null;
            String staticAwardText = null;
            if(awardBCount>0){
                awardText = String.format("%s礼物%d个+%s礼物%d个",awardAConfig.getGiftName(),awardACount,awardBConfig.getGiftName(),awardBCount);
                staticAwardText = String.format("%s礼物%d个+%s礼物%d个",awardAConfig.getGiftName(),awardACount*2,awardBConfig.getGiftName(),awardBCount*2);
            }else{
                awardText = String.format("%s礼物%d个",awardAConfig.getGiftName(),awardACount);
                staticAwardText = String.format("%s礼物%d个",awardAConfig.getGiftName(),awardACount*2);
            }
            String lotteryKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
            BestCpInfo value = new BestCpInfo();
            value.setUserUid(userUid);
            value.setBabyUid(babyUid);
            value.setSid(channel.getTopsid());
            value.setSsid(channel.getSubsid());
            value.setAwardText(awardText);
            value.setStaticAwardText(staticAwardText);
            value.setAddStep(addStep);
            actRedisDao.hset(redisGroup, lotteryKey,timeCode,JsonUtil.toJson(value));
        }else{
            log.warn("handlePhaseTimeEndSaveData password lottery channel is null,userUid:{},anchorUid:{},timeCode:{}",userUid,babyUid,timeCode);
        }
    }

    /**
     * 获取口令抽奖的频道
     * 优先用主播当时在线频道，如果主播不在线，则用最近一次收礼频道
     */
    private UserCurrentChannel getAnchorAwardChannel(CpKiteComponentAttr attr, long anchorUid,String timeCode) {
        UserCurrentChannel channel = commonService.getUserCurrentChannel(anchorUid);
        if (channel != null) {
            return channel;
        }

        //最近一次收礼的频道
        String key = makeKey(attr, String.format(LAST_RECEIVE_GIFT_CHANNEL, timeCode));
        String lastReceive = actRedisDao.hget(getRedisGroupCode(attr.getActId()), key, Convert.toString(anchorUid));
        if (StringUtils.isNotEmpty(lastReceive)) {
            channel = new UserCurrentChannel();
            String[] array = lastReceive.split("_");
            channel.setTopsid(Convert.toLong(array[0]));
            channel.setSubsid(Convert.toLong(array[1]));
            return channel;
        }

        log.error("getAnchorAwardChannel null,anchorUid:{}", anchorUid);
        return null;
    }

    private void unicastTopNotice(String timeCode,long userUid,long babyUid,CpKiteComponentAttr attr,long exchangeGiftStep,DriftBottleGiftVo awardAConfig,DriftBottleGiftVo awardBConfig,
                                  long awardACount,long awardBCount) {
        KiteNoticeInfo json = new KiteNoticeInfo();
        String noticeTime = null;
        try {
            noticeTime = DateUtil.format(DateUtils.parseDate(timeCode,DateUtil.PATTERN_TYPE7),DateUtil.PATTERN_TYPE13);
        } catch (ParseException e) {
            log.error("CpKiteComponent noticeTime error ,timeCode:{}",timeCode);
        }


        String awardTexts = null;
        List<String> awardLogo = Lists.newArrayList();
        if(awardBCount>0){
            awardTexts = String.format(TOP_ONE_NOTICE_UNICAST_TEXT_TWO,awardAConfig.getGiftName(),awardACount,awardBConfig.getGiftName(),awardBCount);
            awardLogo.add(awardAConfig.getGiftLogo());
            awardLogo.add(awardBConfig.getGiftLogo());
        }else{
            awardTexts = String.format(TOP_ONE_NOTICE_UNICAST_TEXT,awardAConfig.getGiftName(),awardACount);
            awardLogo.add(awardAConfig.getGiftLogo());
        }

        json.setAwardText(awardTexts);
        json.setAwardLogo(awardLogo);
        json.setAwardTime(timeCode);
        log.info("releaseTopOneAward commonNoticeUnicast actId:{},timeCode:{},userUid:{},babyUid:{},awardTexts:{}",attr.getActId(),timeCode,userUid,babyUid,awardTexts);

        String key = makeKey(attr, TOP_ONE_ENTER_CHANNEL_NOTICE);
        UserCurrentChannel userChannel = commonService.getUserCurrentChannel(userUid);
        UserCurrentChannel babyChannel = commonService.getUserCurrentChannel(babyUid);
        if(userChannel!=null){
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, userUid);
        }else{
            log.warn("releaseTopOneAward userUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),userUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(userUid),JsonUtil.toJson(json));
        }

        if(babyChannel!=null) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, babyUid);
        }else{
            log.warn("releaseTopOneAward babyUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),babyUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(babyUid),JsonUtil.toJson(json));
        }
    }

    private  void broTopBanner(long userUid ,long babyUid, CpKiteComponentAttr attr,String seq) {
        //TOP1CP动画
        Set<Long> uids = new HashSet<>();
        uids.add(userUid);
        uids.add(babyUid);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,810);

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo babyInfoVo = userInfos.get(babyUid);
        ext.put("userUid",userUid);
        ext.put("babyUid",babyUid);
        ext.put("svgaUrl",attr.getTop1SvgaUrl());
        if(userInfoVo!=null ){
            ext.put("userLogo",userInfoVo.getAvatarUrl());
            ext.put("userNick", Base64Utils.encodeToString(Convert.toString(userInfoVo.getNick()).getBytes()));
        }

        if(babyInfoVo!=null) {
            ext.put("babyLogo",babyInfoVo.getAvatarUrl());
            ext.put("babyNick",Base64Utils.encodeToString(Convert.toString(babyInfoVo.getNick()).getBytes()));
        }

        log.info("broTopBanner commonBannerBroadcast ext:{}",JsonUtil.toJson(ext));
        commonBroadCastService.commonBannerBroadcast(0, 0, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), 4
                , attr.getActId(), 0L,0L, TOPONE_BRO_BANNER_ID, 0L, ext);

        //TOP1发送app cp特效
        broTopOneCpApp( seq, attr, userInfos, userUid, babyUid);



    }

    private void broTopOneCpApp(String seq,CpKiteComponentAttr attr,Map<Long, UserInfoVo> userInfoVoMap,long userUid,long babyUid) {

        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        svgaConfig.setDuration(8);
        svgaConfig.setSvgaURL(attr.getTop1SvgaUrl());

        List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, userInfoVoMap, userUid,babyUid);
        svgaConfig.setContentLayers(broContentLayers);

        //头像
        svgaConfig.setImgLayers(buildSvgaImageConfig(attr,userInfoVoMap,userUid,babyUid));

        final int bcType = FstAppBroadcastType.ALL_TEMPLATE;
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getAppBannerBusiId(),
                bcType, 0, 0, "",
                Lists.newArrayList());

        appBannerEvent.setContentType(6);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setSvgaConfig(svgaConfig);
        appBannerEvent.setUidList(Lists.newArrayList(userUid,babyUid));
        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("broTopOneCpApp app done seq:{}, userUid:{} ,babyUid:{},event:{}",  seq, userUid,babyUid, JSON.toJSONString(appBannerEvent));
    }

    private List<Map<String, String>> buildSvgaImageConfig(CpKiteComponentAttr attr, Map<Long, UserInfoVo> userInfoVoMap, long userUid, long babyUid) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }

        Map<Integer, String> imageMap = attr.getSvgaImgLayers();
        UserInfoVo userInfoVo = userInfoVoMap.get(userUid);
        UserInfoVo babyInfoVo = userInfoVoMap.get(babyUid);
        for (Integer userType : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String imageKey = imageMap.get(userType);
            String image;
            if (userType == 1) {
                image = babyInfoVo != null ? babyInfoVo.getAvatarUrl() : StringUtil.EMPTY;
            } else {
                image = userInfoVo != null ? userInfoVo.getAvatarUrl() : StringUtil.EMPTY;
            }
            broImgLayer.put(imageKey, image);
            broImgLayers.add(broImgLayer);
        }

        return broImgLayers;

    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(CpKiteComponentAttr attr,Map<Long, UserInfoVo> userInfoVoMap,long userUid,long babyUid) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        Map<Integer, BannerSvgaTextConfig> textMap = attr.getSvgaText();

        for (Integer userType : textMap.keySet()) {
            Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
            BannerSvgaTextConfig textConfig = attr.getSvgaText().get(userType);
            if (textConfig == null) {
                continue;
            }
            AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
            //配置动态替换文本
            String text = StringUtils.EMPTY;
            if(userType==3){
                text = textConfig.getText();
            }else{
                UserInfoVo userInfoVo = userType==1? userInfoVoMap.get(babyUid) : userInfoVoMap.get(userUid);
                text = textConfig.getText().replace("{nick}","{"+userInfoVo.getUid()+":n}");
            }

            appBannerSvgaText.setText(text);
            appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
            appBannerSvgaText.setGravity(textConfig.getGravity());

            if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
            }
            broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

            if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                broContentLayers.add(broSvgaTextLayer);
            }
        }

        return broContentLayers;

    }


    private DriftBottleGiftVo getMinStepAwardConfig(CpKiteComponentAttr attr) {

        return attr.getTop1GiftInfo().stream().filter(v -> v.getStep() == attr.getTop1GiftMinStep()).findFirst().orElse(null);
    }


    private Long getTotalStep(CpKiteComponentAttr attr, String timeCode) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String totalStepKey = getTotalStepKey(attr);
        String stepValue = actRedisDao.hget(groupCode, totalStepKey, timeCode);
        return Convert.toLong(stepValue, 0L);
    }


    private String getTotalStepKey(CpKiteComponentAttr attr) {
        return makeKey(attr, DRIFT_BOTTLE_TOTAL_STEP);
    }

    public String getRoadMapCpZsetKey(CpKiteComponentAttr attr, String timeCode) {
        return makeKey(attr, String.format(ROAD_MAP_CP_ZSET_KEY, timeCode));
    }

    @GetMapping("/taskStatic")
    public Response<String> taskStatic(HttpServletRequest request, HttpServletResponse response, Long actId) {
        Date now = commonService.getNow(actId);
        Date noticeDate = DateUtil.addHours(now,-1);
        doStaticReport(actId, now, getUniqueComponentAttr(actId),noticeDate);
        return Response.ok();
    }


    /**
     * 每小时一次日报
     */
    @NeedRecycle(author = "chengaojie", notRecycle = true)
    @Scheduled(cron = "11 0 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }
            CpKiteComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            Date now = commonService.getNow(attr.getActId());
            Date noticeDate = DateUtil.addHours(now,-1);
            String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE7);
            String groupCode = getRedisGroupCode(actId);
            String execKey = makeKey(attr, "execKiteStatic:"+timeCode);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}",execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId,now);

            doStaticReport(actId, now, attr,noticeDate);

        }

    }

    public void doStaticReport(long actId, Date now, CpKiteComponentAttr attr,Date noticeDate) {
        if(attr==null) {
            log.error("doStaticReport doStaticReport attr is null,actId:{}",actId);
            return;
        }
        String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE7);
        String noticeTime = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE14);

        StringBuilder content = new StringBuilder();
        content.append(String.format("### 场次时间：%s场\n", noticeTime));

        long totalStep = getTotalStep(attr, timeCode);
        content.append("全服累计m：").append(totalStep).append("\n");
        Set<Long> uids = new HashSet<>();
        //最强cp
        String groupCode = getRedisGroupCode(actId);
        String toponeKey = makeKey(attr, LATEST_PASSWORD_LOTTERY);
        String topValue = actRedisDao.hget(groupCode,toponeKey,timeCode);
        BestCpInfo toponeInfo = null;
        if (StringUtils.isNotEmpty(topValue)) {
            toponeInfo = JSONUtils.parseObject(topValue, BestCpInfo.class);
            uids.add(toponeInfo.getUserUid());
            uids.add(toponeInfo.getBabyUid());
        }

        //幸运CP
        String luckyKey = makeKey(attr,CP_KITE_LUCKY_STATISTIC);
        String luckyValue = actRedisDao.hget(groupCode,luckyKey,timeCode);
        KiteRoadMapCpInfo luckyInfo = null;
        if (StringUtils.isNotEmpty(luckyValue)) {
            luckyInfo = JSONUtils.parseObject(luckyValue, KiteRoadMapCpInfo.class);
            uids.add(luckyInfo.getUserUid());
            uids.add(luckyInfo.getBabyUid());
        }

        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(uids), Template.getTemplate(810));
        if(toponeInfo!=null){
            content.append("本轮累计礼物：").append(toponeInfo.getStaticAwardText()).append("\n");
            UserInfoVo userInfo = userInfoVoMap.get(toponeInfo.getUserUid());
            UserInfoVo babyInfo = userInfoVoMap.get(toponeInfo.getBabyUid());
            content.append("本轮最强CP：");
            content.append(babyInfo.getNick()).append("(").append(babyInfo.getUid()).append(")&");
            content.append(userInfo.getNick()).append("(").append(userInfo.getUid()).append(")\n");
            content.append("主持地址：").append(babyInfo.getAvatarUrl()).append("\n");
            content.append("神豪地址：").append(userInfo.getAvatarUrl()).append("\n");
            content.append("贡献 ").append(toponeInfo.getAddStep()).append("KM\n");
        }
        if(luckyInfo!=null) {
            UserInfoVo userInfo = userInfoVoMap.get(luckyInfo.getUserUid());
            UserInfoVo babyInfo = userInfoVoMap.get(luckyInfo.getBabyUid());
            content.append("本轮幸运CP：");
            content.append(babyInfo.getNick()).append("(").append(babyInfo.getUid()).append(")&");
            content.append(userInfo.getNick()).append("(").append(userInfo.getUid()).append(")\n");
            content.append("主持地址：").append(babyInfo.getAvatarUrl()).append("\n");
            content.append("神豪地址：").append(userInfo.getAvatarUrl()).append("\n");
        }

        String totalAwardKey = makeKey(attr,KITE_TOTAL_AWARD_STATISTIC);
        String totalAwardValue = actRedisDao.get(groupCode,totalAwardKey);

        if(StringUtils.isNotEmpty(totalAwardValue)) {
            content.append("截止当前，玩法累计发放礼物金额：").append(formatMoney(Convert.toLong(totalAwardValue,0))).append("\n");
        }

        String msg = buildActRuliuMsg(actId, false, "风筝情缘玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

    }

    public static String formatMoney(long awardNum) {
        DecimalFormat df = new DecimalFormat("#.#");
        return df.format((float)awardNum/1000);
    }


    /**
     * 测试环境造膜拜人数
     */
    @RequestMapping("/mockVisitTest")
    public Response mockVisitTest(HttpServletRequest req, HttpServletResponse resp, long actId,String timeCode, int sum) {
        long uid = getLoginYYUid(req, resp);
        if(uid == 50046296L  || SysEvHelper.isDev()) {
            CpKiteComponentAttr attr = tryGetUniqueComponentAttr(actId);

            String key = makeKey(attr, String.format(VISIT_BEST_CP, timeCode));
            for (int i = 0; i < sum; i++) {
                actRedisDao.hset(getRedisGroupCode(attr.getActId()), key, i + "", DateUtil.getNowYyyyMMddHHmmss());
            }
        }

        return Response.success("succ");
    }

}
