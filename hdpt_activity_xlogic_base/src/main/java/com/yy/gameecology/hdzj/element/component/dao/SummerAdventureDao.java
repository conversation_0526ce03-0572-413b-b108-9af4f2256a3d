package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.hdzj.element.component.attr.bean.SummerAdventureDice;
import com.yy.gameecology.hdzj.element.component.attr.bean.SummerAdventureRecord;
import com.yy.gameecology.hdzj.element.component.attr.bean.SummerAdventureReward;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 夏日探险DAO
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Repository
public class SummerAdventureDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    // ==================== 骰子相关SQL ====================
    
    private static final String GET_DICE_INFO_SQL = 
        "SELECT * FROM cmpt_5159_summer_dice WHERE act_id = ? AND cmpt_use_inx = ? AND uid = ? AND anchor_uid = ?";
    
    private static final String INSERT_DICE_SQL = 
        "INSERT INTO cmpt_5159_summer_dice (act_id, cmpt_use_inx, uid, anchor_uid, cp_member, dice_count, total_dice_count, create_time, update_time) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) " +
        "ON DUPLICATE KEY UPDATE dice_count = dice_count + VALUES(dice_count), total_dice_count = total_dice_count + VALUES(dice_count), update_time = VALUES(update_time)";
    
    private static final String UPDATE_DICE_COUNT_SQL = 
        "UPDATE cmpt_5159_summer_dice SET dice_count = dice_count + ?, update_time = ? " +
        "WHERE act_id = ? AND cmpt_use_inx = ? AND uid = ? AND anchor_uid = ? AND dice_count >= ABS(?)";
    
    private static final String GET_CP_DICE_RANKING_SQL = 
        "SELECT * FROM cmpt_5159_summer_dice WHERE act_id = ? AND cmpt_use_inx = ? " +
        "ORDER BY total_dice_count DESC, create_time ASC LIMIT ?";

    // ==================== 奖励配置相关SQL ====================
    
    private static final String GET_GRID_REWARDS_SQL = 
        "SELECT * FROM cmpt_5159_summer_rewards WHERE act_id = ? AND cmpt_use_inx = ? AND enabled = 1 " +
        "ORDER BY grid_position ASC";
    
    private static final String GET_GRID_REWARD_BY_POSITION_SQL = 
        "SELECT * FROM cmpt_5159_summer_rewards WHERE act_id = ? AND cmpt_use_inx = ? AND grid_position = ? AND enabled = 1";
    
    private static final String UPDATE_REWARD_STOCK_SQL = 
        "UPDATE cmpt_5159_summer_rewards SET allocated_count = allocated_count + ? " +
        "WHERE act_id = ? AND cmpt_use_inx = ? AND grid_position = ? AND reward_id = ? " +
        "AND (reward_stock = -1 OR allocated_count + ? <= reward_stock)";

    // ==================== 探险记录相关SQL ====================
    
    private static final String INSERT_ADVENTURE_RECORD_SQL = 
        "INSERT INTO cmpt_5159_adventure_records (record_id, act_id, cmpt_use_inx, uid, anchor_uid, cp_member, " +
        "used_dice_count, adventure_path, reward_list, start_time, end_time, create_time) " +
        "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    private static final String GET_ADVENTURE_RECORDS_SQL = 
        "SELECT * FROM cmpt_5159_adventure_records WHERE act_id = ? AND cmpt_use_inx = ? AND uid = ? AND anchor_uid = ? " +
        "ORDER BY create_time DESC LIMIT ?";
    
    private static final String GET_RECENT_ADVENTURE_RECORDS_SQL = 
        "SELECT * FROM cmpt_5159_adventure_records WHERE act_id = ? AND cmpt_use_inx = ? " +
        "ORDER BY create_time DESC LIMIT ?";

    // ==================== 骰子相关方法 ====================

    /**
     * 获取CP的骰子信息
     */
    public SummerAdventureDice getDiceInfo(long actId, long cmptUseInx, long uid, long anchorUid) {
        try {
            List<SummerAdventureDice> results = gameecologyDao.getJdbcTemplate().query(
                GET_DICE_INFO_SQL, 
                new BeanPropertyRowMapper<>(SummerAdventureDice.class),
                actId, cmptUseInx, uid, anchorUid
            );
            return results.isEmpty() ? null : results.get(0);
        } catch (Exception e) {
            log.error("获取骰子信息失败, actId:{}, cmptUseInx:{}, uid:{}, anchorUid:{}", 
                actId, cmptUseInx, uid, anchorUid, e);
            return null;
        }
    }

    /**
     * 增加骰子数量
     */
    @Transactional
    public boolean addDice(long actId, long cmptUseInx, long uid, long anchorUid, String cpMember, int diceCount) {
        try {
            long now = System.currentTimeMillis();
            int result = gameecologyDao.getJdbcTemplate().update(
                INSERT_DICE_SQL,
                actId, cmptUseInx, uid, anchorUid, cpMember, diceCount, diceCount, now, now
            );
            return result > 0;
        } catch (Exception e) {
            log.error("增加骰子失败, actId:{}, cmptUseInx:{}, uid:{}, anchorUid:{}, diceCount:{}", 
                actId, cmptUseInx, uid, anchorUid, diceCount, e);
            return false;
        }
    }

    /**
     * 更新骰子数量（可增加或减少）
     */
    @Transactional
    public boolean updateDiceCount(long actId, long cmptUseInx, long uid, long anchorUid, int deltaCount) {
        try {
            long now = System.currentTimeMillis();
            int result = gameecologyDao.getJdbcTemplate().update(
                UPDATE_DICE_COUNT_SQL,
                deltaCount, now, actId, cmptUseInx, uid, anchorUid, Math.abs(deltaCount)
            );
            return result > 0;
        } catch (Exception e) {
            log.error("更新骰子数量失败, actId:{}, cmptUseInx:{}, uid:{}, anchorUid:{}, deltaCount:{}", 
                actId, cmptUseInx, uid, anchorUid, deltaCount, e);
            return false;
        }
    }

    /**
     * 获取骰子排行榜
     */
    public List<SummerAdventureDice> getDiceRanking(long actId, long cmptUseInx, int limit) {
        try {
            return gameecologyDao.getJdbcTemplate().query(
                GET_CP_DICE_RANKING_SQL,
                new BeanPropertyRowMapper<>(SummerAdventureDice.class),
                actId, cmptUseInx, limit
            );
        } catch (Exception e) {
            log.error("获取骰子排行榜失败, actId:{}, cmptUseInx:{}, limit:{}", actId, cmptUseInx, limit, e);
            return List.of();
        }
    }

    // ==================== 奖励配置相关方法 ====================

    /**
     * 获取所有地图格子的奖励配置
     */
    public List<SummerAdventureReward> getGridRewards(long actId, long cmptUseInx) {
        try {
            return gameecologyDao.getJdbcTemplate().query(
                GET_GRID_REWARDS_SQL,
                new BeanPropertyRowMapper<>(SummerAdventureReward.class),
                actId, cmptUseInx
            );
        } catch (Exception e) {
            log.error("获取地图奖励配置失败, actId:{}, cmptUseInx:{}", actId, cmptUseInx, e);
            return List.of();
        }
    }

    /**
     * 获取指定位置的奖励配置
     */
    public List<SummerAdventureReward> getGridRewardByPosition(long actId, long cmptUseInx, int gridPosition) {
        try {
            return gameecologyDao.getJdbcTemplate().query(
                GET_GRID_REWARD_BY_POSITION_SQL,
                new BeanPropertyRowMapper<>(SummerAdventureReward.class),
                actId, cmptUseInx, gridPosition
            );
        } catch (Exception e) {
            log.error("获取指定位置奖励配置失败, actId:{}, cmptUseInx:{}, gridPosition:{}", 
                actId, cmptUseInx, gridPosition, e);
            return List.of();
        }
    }

    /**
     * 更新奖励库存
     */
    @Transactional
    public boolean updateRewardStock(long actId, long cmptUseInx, int gridPosition, String rewardId, int count) {
        try {
            int result = gameecologyDao.getJdbcTemplate().update(
                UPDATE_REWARD_STOCK_SQL,
                count, actId, cmptUseInx, gridPosition, rewardId, count
            );
            return result > 0;
        } catch (Exception e) {
            log.error("更新奖励库存失败, actId:{}, cmptUseInx:{}, gridPosition:{}, rewardId:{}, count:{}", 
                actId, cmptUseInx, gridPosition, rewardId, count, e);
            return false;
        }
    }

    // ==================== 探险记录相关方法 ====================

    /**
     * 插入探险记录
     */
    @Transactional
    public boolean insertAdventureRecord(SummerAdventureRecord record) {
        try {
            int result = gameecologyDao.getJdbcTemplate().update(
                INSERT_ADVENTURE_RECORD_SQL,
                record.getRecordId(), record.getActId(), record.getCmptUseInx(),
                record.getUid(), record.getAnchorUid(), record.getCpMember(),
                record.getUsedDiceCount(), record.getAdventurePath(), record.getRewardList(),
                record.getStartTime(), record.getEndTime(), record.getCreateTime()
            );
            return result > 0;
        } catch (Exception e) {
            log.error("插入探险记录失败, recordId:{}", record.getRecordId(), e);
            return false;
        }
    }

    /**
     * 获取CP的探险记录
     */
    public List<SummerAdventureRecord> getAdventureRecords(long actId, long cmptUseInx, long uid, long anchorUid, int limit) {
        try {
            return gameecologyDao.getJdbcTemplate().query(
                GET_ADVENTURE_RECORDS_SQL,
                new BeanPropertyRowMapper<>(SummerAdventureRecord.class),
                actId, cmptUseInx, uid, anchorUid, limit
            );
        } catch (Exception e) {
            log.error("获取探险记录失败, actId:{}, cmptUseInx:{}, uid:{}, anchorUid:{}", 
                actId, cmptUseInx, uid, anchorUid, e);
            return List.of();
        }
    }

    /**
     * 获取最近的探险记录（用于展示）
     */
    public List<SummerAdventureRecord> getRecentAdventureRecords(long actId, long cmptUseInx, int limit) {
        try {
            return gameecologyDao.getJdbcTemplate().query(
                GET_RECENT_ADVENTURE_RECORDS_SQL,
                new BeanPropertyRowMapper<>(SummerAdventureRecord.class),
                actId, cmptUseInx, limit
            );
        } catch (Exception e) {
            log.error("获取最近探险记录失败, actId:{}, cmptUseInx:{}", actId, cmptUseInx, e);
            return List.of();
        }
    }
}
