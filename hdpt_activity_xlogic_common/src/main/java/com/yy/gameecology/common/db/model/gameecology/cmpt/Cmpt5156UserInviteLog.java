package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-11 15:38
 **/
@Data
@TableColumn(underline = true)
public class Cmpt5156UserInviteLog implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5156_user_invite_log";


    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5156UserInviteLog> ROW_MAPPER = null;

    private Long id;
    /**
     * act_id
     */
    private Long actId;

    /**
     * cmpt_use_inx
     */
    private Long cmptUseInx;

    /**
     * uid
     */
    private Long uid;

    /**
     * user_hdid
     */
    private String userHdid;

    /**
     * 被邀请人uid
     */
    private Long invitedUid;

    /**
     * 被邀请人设备号
     */
    private String invitedHdid;

    /**
     * 邀请失败状态
     */
    private Integer invitedState;

    /**
     * 邀请备注字段
     */
    private String invitedDesc;

    /**
     * 扩展字段
     */
    private String extJson;

    /**
     * 创建时间
     */
    private Date createTime;


}
