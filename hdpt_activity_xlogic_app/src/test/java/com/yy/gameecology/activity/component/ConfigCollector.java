package com.yy.gameecology.activity.component;

import com.yy.gameecology.activity.BaseTest;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.listener.ApplicationDetector;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.ComponentAttrChecker;
import com.yy.gameecology.hdzj.element.IdempotentComponentMethodCallSvcChecker;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

/**
 * 组件配置收集器
 *
 * <AUTHOR>
 * @date 2021/12/7 14:50
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=1"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-1.properties"
})
public class ConfigCollector extends BaseTest {
    @Autowired
    private ComponentAttrCollector componentAttrCollector;

    @Autowired
    private ComponentAttrChecker componentAttrChecker;

    @Autowired
    private ApplicationDetector applicationDetector;

    static {
        System.setProperty("group", "1");
    }

    @Test
    public void synComponentAttrDefineTest() {
//        componentAttrCollector.synComponentAttrDefine(ComponentId.WHITE_LIST_PROMOTE);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.ACT_LAYER_CONFIG);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.ENERGY_STATION);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.ACT_LAYER_CONFIG);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.ENERGY_STATION);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.CHANNEL_WHITE_LIST_TASK);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.SUNSHINE_TASK_V3);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.ANCHOR_TEAM);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.NOVICE_PACKAGE);
//        componentAttrCollector.synComponentAttrDefine(2026L);
//         componentAttrCollector.synComponentAttrDefine(ComponentId.TASK_CONTRIBUTE_LOTTERY);
//         componentAttrCollector.synComponentAttrDefine(ComponentId.ACT_LAYER_CONFIG);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.RANK_BUILDER);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.FLIPPING_CARD);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.ANCHOR_WELFARE_TASK_ENTRY);
        //       componentAttrCollector.synComponentAttrDefine(ComponentId.DRIFT_BOTTLE);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.CP_CITY_WALK);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.USER_TASK_ACHIEVE);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.CP_PEAK_ROMANCE);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.PEPC_PUSH);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.PEPC_APP_POP);

//        componentAttrCollector.synComponentAttrDefine(ComponentId.PEPC_RANK);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.PEPC_GAME);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.PEPC_AWARD);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.PUZZLE);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.LAYER_INDIVIDUAL_CP);
//        componentAttrCollector.synComponentAttrDefine(ComponentId.CP_INFO);
        componentAttrCollector.synComponentAttrDefine(ComponentId.HEADLINE_TIME);
        componentAttrCollector.synComponentAttrDefine(ComponentId.PUZZLE);

    }

    @Test
    public void synAllTest() {
        componentAttrCollector.synAll();
    }

    @Test
    public void findCustomizeTimerTest() {
        componentAttrChecker.findCustomizeTimer();
    }

    @Test
    public void findTimerAndInitMethodTest() {
        applicationDetector.findTimerAndInitMethod();
    }

    @Test
    public void synDropDownSourceTest() {
        componentAttrCollector.synDropDownSource();
    }

    @Test
    public void buildAllTimerInfoTest() {
        applicationDetector.buildAllTimerInfo();
    }

    @Autowired
    private IdempotentComponentMethodCallSvcChecker idempotentComponentMethodCallSvcChecker;

    @Test
    public void checkSvcMethodCallTest() throws IOException {
        idempotentComponentMethodCallSvcChecker.checkSvcMethodCall();
    }
}
