package com.yy.gameecology.activity.commons.enums;

public enum App {

    /**
     * 追玩
     */
    ZHUIWAN("zhuiwan"),
    /**
     * Yo语音
     */
    YOMI("yomi"),
    /**
     * Yo开黑
     */
    YOKH("yokh"),
    /**
     * hyoo语音(用于商户号申请，联系人-李敏、谭日东)
     */
    HNHONYU("hnhonyu"),
    /**
     * pcyy
     */
    PCYY("pcyy"),

    YYPC("yypc"),

    /**
     * YaYa语音
     */
    YAYA("yaya"),

    /**
     *  蓝语(用于商户号申请，联系人-李敏、谭日东)
     */
    HNLANYU("hnlanyu"),

    /**
     * PC交友
     */
    LOVE("love"),

    /**
     * 浏览器，yo.yy.com
     */
    BROWSER("browser"),

    /**
     * 手Y
     */
    YY("yy")

    ;
    public static final String DREAMER = "dreamer";
    public final String hostName;

    App(String hostName) {
        this.hostName = hostName;
    }

    public String getHostName() {
        return hostName;
    }

    public static App fromHostName(String hostName, App defaultValue) {
        if (DREAMER.equals(hostName)) {
            return App.ZHUIWAN;
        }

        App[] apps = App.values();
        for (App app : apps) {
            if (app.getHostName().equals(hostName)) {
                return app;
            }
        }
        return defaultValue;
    }

    public static App fromHostName(String hostName) {
        return fromHostName(hostName, null);
    }

    public static boolean isPC(App app) {
        return app == PCYY || app == LOVE;
    }
}

