package com.yy.gameecology.hdzj.element.redis;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.AwardRecordRsp;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;

import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;

import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.TravelDiaryAwardRecord;
import com.yy.gameecology.hdzj.consts.ComponentId;


import com.yy.gameecology.hdzj.element.component.attr.CpTravelDiaryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.TravelDiaryAwardMap;
import com.yy.gameecology.hdzj.element.component.attr.TravelDiaryMissionConfig;

import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.*;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.yy.gameecology.hdzj.BaseActComponent;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.scheduling.annotation.Scheduled;

import java.text.DecimalFormat;
import java.util.*;


@UseRedisStore
@RequestMapping("/5106")
@RestController
@Component
public class CpTravelDiaryComponent extends BaseActComponent<CpTravelDiaryComponentAttr> {


    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SignedService signedService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;


    private static final String DAILY_TASK_FINISH_NOTICE_TYPE = "5106_daily_task_finish";
    private static final String DAILY_TASK_AWARD_NOTICE_TYPE = "5106_daily_task_award";

    private static final long TASK_FINISH_BRO_BANNER_ID = 5106001L;

    private static final String TASK_AWARD_NOTICE_TYPE = "5106_task_award";

    private static final String TASK_AWARD_LIST = "task_award_list";

    /**
     * 发放礼物总金额
     */
    private static final String TRAVEL_DIARY_TOTAL_AWARD_CONSUME = "travel_diary_total_award_consume";

    private static final String DAILY_TASK_FINISH_STATISTIC = "daily_task_finish_statistics:%s";

    private static final String TASK_FINISH_STATISTIC = "task_finish_statistics";

    /**
     * 主播最后收礼频道
     * <p>
     * TODO 监听榜单变化事件，记录每个主播最近一次收礼频道
     */
    private static final String LAST_RECEIVE_GIFT_CHANNEL = "last_receive_gift_channel";

    private static final Integer DAILY_TASK_TYPE = 1;
    private static final Integer  TASK_TYPE = 2;

    private static final long TING_ACTOR = 81050L;

    @Override
    public Long getComponentId() {
        return ComponentId.CP_TRAVEL_DIARY;
    }


    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, CpTravelDiaryComponentAttr attr) {

        log.info("onTaskProgressChanged event:{}",JsonUtil.toJson(event));
        Date date = DateUtil.getDate(event.getOccurTime(), DateUtil.DEFAULT_PATTERN);

        //日贡献
        if(attr.getCpDailyRankId() == event.getRankId() && attr.getPhaseId() == event.getPhaseId()) {
            String eventSeq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
            String member = event.getMember();
            String[] members = member.split("\\|");
            long userId = Convert.toLong(members[0]);
            long anchorUid = Convert.toLong(members[1]);
            for( long startIndex = event.getStartTaskIndex()+1 ;startIndex<=event.getCurrTaskIndex();startIndex++) {
                handleDailyTaskComplete(attr,  startIndex, userId, anchorUid, eventSeq+ "_" + startIndex,date);
            }
        }else if (attr.getCpRankId() == event.getRankId() && attr.getPhaseId() == event.getPhaseId()) {
            String eventSeq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
            String member = event.getMember();
            String[] members = member.split("\\|");
            long userId = Convert.toLong(members[0]);
            long anchorUid = Convert.toLong(members[1]);

            //记录当前主持送礼时最新room，兼容没有传厅的情况
            String giftChlkey = makeKey(attr, LAST_RECEIVE_GIFT_CHANNEL);
            String subChannel = event.getActors().get(TING_ACTOR);
            if(StringUtils.isNotEmpty(subChannel)){
                actRedisDao.hset(getRedisGroupCode(attr.getActId()), giftChlkey, String.valueOf(anchorUid), subChannel);
            }

            for( long startIndex = event.getStartTaskIndex()+1 ;startIndex<=event.getCurrTaskIndex();startIndex++) {
                handleTaskComplete(attr,  startIndex, userId, anchorUid, eventSeq+ "_" + startIndex);
            }
        }
    }




    private void handleTaskComplete(CpTravelDiaryComponentAttr attr, long taskId, long uid, long anchorUid, String eventSeq) {

        //奖池不足时发放进场秀
        if (getAwardPoolLeft(attr) <= 0) {
            releasTaskCompleteShow(attr, taskId, uid, anchorUid, eventSeq);
            return;
        }

        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        TravelDiaryAwardMap award = getAwardConfig(attr, taskId);
        if (award == null) {
            log.error("handleTaskComplete releaseaward error award is null,actId:{},userUid:{},babyUid{}", attr.getActId(), uid, anchorUid);
            return;
        }

        //累计发放金额
        String groupCode = getRedisGroupCode(attr.getActId());
        String totalAwardKey = makeKey(attr, TRAVEL_DIARY_TOTAL_AWARD_CONSUME);
        String totalPoolSeq = makeKey(attr, "seq:totalAwardConsume:" + eventSeq);
        List<Long> addResult = actRedisDao.incrValueWithSeq(groupCode, totalPoolSeq, totalAwardKey, award.getGiftAmount() * 2, DateUtil.ONE_MONTH_SECONDS);
        if (addResult.get(0) <= 0) {
            log.warn("handleTaskComplete awardpool incrValueWithSeq error  actId:{},uid:{},anchorUid:{},taskId:{},addResult:{},totalPoolSeq:{}", attr.getActId(), uid, anchorUid, taskId, JsonUtil.toJson(addResult), totalPoolSeq);
            return;
        }

        String userHashSeq = MD5SHAUtil.getMD5(makeKey(attr, eventSeq + "_user_" + uid));
        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), uid, award.getTaskId(), 1, award.getPackageId(), userHashSeq, 2);
        boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if (!suc) {
            // 需要人工介入处理
            log.error("handleTaskComplete user doWelfare error,  actId:{}, uid:{}, seq:{},taskId:{}, packageId:{}, ret:{}", attr.getActId(), uid, userHashSeq, award.getTaskId(),
                    award.getPackageId(), batchWelfareResult);
        }


        String babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr, eventSeq + "_baby_" + anchorUid));
        batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), anchorUid, award.getTaskId(), 1, award.getPackageId(), babyHashSeq, 2);
        suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if (!suc) {
            // 需要人工介入处理
            log.error("handleTaskComplete  baby doWelfare error,  actId:{}, uid:{}, seq:{},taskId:{}, packageId:{}, ret:{}", attr.getActId(), anchorUid, babyHashSeq, award.getTaskId(),
                    award.getPackageId(), batchWelfareResult);
        }


        //累计任务完成顶部广播
        String bannerSeq = "seq:banner:" + eventSeq;
        RetryTool.withRetryCheck(attr.getActId(), bannerSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            broCpBanner(uid, anchorUid, attr, taskId);
        });


        //累计任务完成获奖弹窗
        String noticeSeq = "seq:award:" + eventSeq;
        RetryTool.withRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            broAwardNotice(uid, anchorUid, attr, taskId, award);
        });


        //添加获奖记录
        addAwardRecord(uid, anchorUid, taskId, TASK_TYPE, award.getGiftName(), attr);

        //任务统计
        updateTaskStatic(attr, taskId, eventSeq);
    }

    private void updateTaskStatic(CpTravelDiaryComponentAttr attr,long taskId, String eventSeq) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr,TASK_FINISH_STATISTIC);
        String seq = "seq:taskStatic:" + eventSeq;
        actRedisDao.hIncrByKeyWithSeq(groupCode,seq,key,String.valueOf(taskId),1,DateUtil.ONE_WEEK_SECONDS);
    }


    //奖池不足发放入场秀
    private void releasTaskCompleteShow(CpTravelDiaryComponentAttr attr, long taskId, long uid, long anchorUid, String eventSeq) {

        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        String userHashSeq = MD5SHAUtil.getMD5(makeKey(attr, eventSeq + "_user_" + uid));
        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), uid, attr.getTravelTaskId(), 1, attr.getTravelPackageId(), userHashSeq, 2);
        boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if (!suc) {
            // 需要人工介入处理
            log.error("releasTaskCompleteShow user doWelfare error,  actId:{}, uid:{}, seq:{} ,taskId:{},packageId:{}, ret:{}", attr.getActId(), uid, userHashSeq,  attr.getTravelTaskId(),
                    attr.getTravelPackageId(),batchWelfareResult);
        }

        String babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr, eventSeq + "_baby_" + anchorUid));
        batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), anchorUid, attr.getTravelTaskId(), 1, attr.getTravelPackageId(), babyHashSeq, 2);
        suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if (!suc) {
            // 需要人工介入处理
            log.error("releasTaskCompleteShow  baby doWelfare error,  actId:{}, uid:{}, seq:{} ,taskId:{},packageId:{},ret:{}", attr.getActId(), anchorUid, babyHashSeq, attr.getTravelTaskId(),
                    attr.getTravelPackageId(),batchWelfareResult);
        }


        //累计任务完成顶部广播
        String bannerSeq = "seq:banner:" + eventSeq;
        RetryTool.withRetryCheck(attr.getActId(), bannerSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            broCpBanner(uid, anchorUid, attr, taskId);
        });


        //累计任务完成获奖弹窗
        String noticeSeq = "seq:award:" + eventSeq;
        RetryTool.withRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            broAwardShowNotice(uid, anchorUid, attr, taskId);
        });

        //添加获奖记录
        addAwardRecord(uid, anchorUid, taskId, TASK_TYPE, attr.getTravelShowName(), attr);

        //任务完成统计
        updateTaskStatic(attr,taskId,eventSeq);

    }

    private void broAwardShowNotice(long userUid , long babyUid, CpTravelDiaryComponentAttr attr, long taskId) {
        TravelDiaryMissionConfig missionConfig = attr.getMissions().stream().filter(v->v.getLevel()==taskId).findFirst().orElse(null);

        Map<String, Object> noticeValue = Maps.newHashMap();
        noticeValue.put("level", taskId);
        noticeValue.put("score", missionConfig.getScore());
        noticeValue.put("giftName", attr.getTravelShowName());
        noticeValue.put("giftIcon", attr.getTravelShowIcon());
        noticeValue.put("cpMember", userUid+"|"+babyUid);
        log.info("broAwardShowNotice commonNoticeUnicast actId:{},userUid:{},babyUid:{},taskId:{}",attr.getActId(),userUid,babyUid,taskId);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(),TASK_AWARD_NOTICE_TYPE , JsonUtil.toJson(noticeValue), StringUtils.EMPTY, userUid);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), TASK_AWARD_NOTICE_TYPE, JsonUtil.toJson(noticeValue), StringUtils.EMPTY, babyUid);
    }

    private void broCpBanner(long userUid , long babyUid, CpTravelDiaryComponentAttr attr, long taskId) {
        Set<Long> uids = new HashSet<>();
        uids.add(userUid);
        uids.add(babyUid);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,attr.getTemplateType());

        TravelDiaryMissionConfig missionConfig = attr.getMissions().stream().filter(v->v.getLevel()==taskId).findFirst().orElse(null);
        String levelName = missionConfig !=null ? missionConfig.getLevelName() : StringUtil.EMPTY;

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo babyInfoVo = userInfos.get(babyUid);
        ext.put("userUid",userUid);
        ext.put("babyUid",babyUid);
        if(userInfoVo!=null ){
            ext.put("userLogo",userInfoVo.getAvatarUrl());
            ext.put("userNick",userInfoVo.getNick());
        }

        if(babyInfoVo!=null) {
            ext.put("babyLogo",babyInfoVo.getAvatarUrl());
            ext.put("babyNick",babyInfoVo.getNick());
        }
        ext.put("nickExtUsers",JsonUtil.toJson(multiNickUsers));
        ext.put("level", taskId);
        ext.put("levelName", levelName);
        UserCurrentChannel channel = getAnchorAwardChannel(attr, babyUid);
        if(channel!=null){
            ext.put("sid",channel.getTopsid());
            ext.put("ssid",channel.getSubsid());
        }

        long broadSid = 0L;
        long broadSsid = 0L;
        if( missionConfig.getBroType()==2){
            broadSid = channel!=null ? channel.getTopsid() : 0L;
            broadSsid  = channel!=null ? channel.getSubsid() : 0L;
        }else if(missionConfig.getBroType()==3){
            broadSid = channel!=null ? channel.getTopsid() : 0L;
        }

        commonBroadCastService.commonBannerBroadcast(broadSid, broadSsid, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), missionConfig.getBroType()
                , attr.getActId(), 0L,0L, TASK_FINISH_BRO_BANNER_ID, 0L, ext);
        log.info("broCpBanner commonBannerBroadcast ext:{}",JsonUtil.toJson(ext));
    }

    private void broAwardNotice(long userUid , long babyUid, CpTravelDiaryComponentAttr attr, long taskId, TravelDiaryAwardMap diaryAward) {
        TravelDiaryMissionConfig missionConfig = attr.getMissions().stream().filter(v->v.getLevel()==taskId).findFirst().orElse(null);

        Map<String, Object> noticeValue = Maps.newHashMap();
        noticeValue.put("level", taskId);
        noticeValue.put("score", missionConfig.getScore());
        noticeValue.put("giftName", diaryAward.getGiftName());
        noticeValue.put("giftIcon", diaryAward.getGiftIcon());
        noticeValue.put("cpMember", userUid+"|"+babyUid);
        log.info("broAwardNotice commonNoticeUnicast actId:{},userUid:{},babyUid:{},taskId:{}",attr.getActId(),userUid,babyUid,taskId);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(),TASK_AWARD_NOTICE_TYPE , JsonUtil.toJson(noticeValue), StringUtils.EMPTY, userUid);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), TASK_AWARD_NOTICE_TYPE, JsonUtil.toJson(noticeValue), StringUtils.EMPTY, babyUid);
    }

    private UserCurrentChannel getAnchorAwardChannel(CpTravelDiaryComponentAttr attr, long anchorUid) {
        UserCurrentChannel channel = commonService.getUserCurrentChannel(anchorUid);
        if (channel != null) {
            return channel;
        }

        //最近一次收礼的频道
        String key = makeKey(attr, LAST_RECEIVE_GIFT_CHANNEL);
        String lastReceive = actRedisDao.hget(getRedisGroupCode(attr.getActId()), key, Convert.toString(anchorUid));
        if (StringUtils.isNotEmpty(lastReceive)) {
            channel = new UserCurrentChannel();
            String[] array = lastReceive.split("_");
            channel.setTopsid(Convert.toLong(array[0]));
            channel.setSubsid(Convert.toLong(array[1]));
            return channel;
        }

        log.error("getAnchorAwardChannel null,anchorUid:{}", anchorUid);
        return null;
    }
    private void handleDailyTaskComplete(CpTravelDiaryComponentAttr attr, long taskId, long uid, long anchorUid, String eventSeq,Date now) {

        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        TravelDiaryAwardMap diaryAward = getDailyAwardConfig(attr, taskId);
        if (diaryAward == null) {
            log.error("handleDailyTaskComplete getDailyAwardConfig error diaryAward is null,actId:{},userUid:{},babyUid{},taskId:{},eventSeq:{}", attr.getActId(), uid, anchorUid,
                    taskId,eventSeq);
            return;
        }

        String userHashSeq = MD5SHAUtil.getMD5(makeKey(attr, eventSeq + "_daily_user_" + uid));
        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), uid, diaryAward.getTaskId(), 1, diaryAward.getPackageId(), userHashSeq, 2);
        boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if (!suc) {
            // 需要人工介入处理
            log.error("handleDailyTaskComplete user doWelfare error,  actId:{}, uid:{}, seq:{} ,taskId:{},packageId:{}, ret:{}", attr.getActId(), uid, userHashSeq,diaryAward.getTaskId(),
                    diaryAward.getPackageId(), batchWelfareResult);
        }


        String babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr, eventSeq + "_daily_baby_" + anchorUid));
        batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), anchorUid, diaryAward.getTaskId(), 1, diaryAward.getPackageId(), babyHashSeq, 2);
        suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if (!suc) {
            // 需要人工介入处理
            log.error("handleDailyTaskComplete  baby doWelfare error,  actId:{}, uid:{}, seq:{}, taskId:{},packageId:{},ret:{}", attr.getActId(), anchorUid, babyHashSeq, diaryAward.getTaskId(),
                    diaryAward.getPackageId(),batchWelfareResult);
        }


        //每日任务顶部广播
        String bannerSeq = "seq:dailynotce:" + eventSeq;
        RetryTool.withRetryCheck(attr.getActId(), bannerSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            broDailyCpNotice(uid, anchorUid, attr, taskId);
        });


        //每日任务获奖弹窗
        String noticeSeq = "seq:dailyaward:" + eventSeq;
        RetryTool.withRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            broDailyAwardNotice(uid, anchorUid, attr, taskId, diaryAward);
        });


        //添加获奖记录
        addAwardRecord(uid, anchorUid, taskId, DAILY_TASK_TYPE, diaryAward.getGiftName(), attr);

        //任务达成统计需求
        updateDailyTaskStatic(attr, taskId, now, eventSeq);

    }

    private void updateDailyTaskStatic(CpTravelDiaryComponentAttr attr,long taskId,Date now, String eventSeq) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String ymd = DateUtil.format(now,DateUtil.PATTERN_TYPE2);
        String key = makeKey(attr,String.format(DAILY_TASK_FINISH_STATISTIC,ymd));
        String seq = "seq:dailyTaskStatic:" + eventSeq;
        actRedisDao.hIncrByKeyWithSeq(groupCode,seq,key,String.valueOf(taskId),1,DateUtil.ONE_WEEK_SECONDS);
    }


    public TravelDiaryAwardMap getAwardConfig( CpTravelDiaryComponentAttr attr,long level) {
        List<TravelDiaryAwardMap> awardMap = attr.getAwardMap();
        if(CollectionUtils.isNotEmpty(awardMap)){
            return awardMap.stream().filter(v->v.getLevel()==(int)level).findFirst().orElse(null);
        }else{
            return null;
        }

    }
    public TravelDiaryAwardMap getDailyAwardConfig( CpTravelDiaryComponentAttr attr,long level) {
        List<TravelDiaryAwardMap> dailyAwardMap = attr.getDailyAwardMap();
        if(CollectionUtils.isNotEmpty(dailyAwardMap)){
            return dailyAwardMap.stream().filter(v->v.getLevel()==(int)level).findFirst().orElse(null);
        }else{
            return null;
        }

    }

    private void addAwardRecord(long userUid,long babyUid,long level,int taskType,String giftName, CpTravelDiaryComponentAttr attr) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String awardKey = makeKey(attr,TASK_AWARD_LIST);
        TravelDiaryAwardRecord record = new TravelDiaryAwardRecord();
        record.setUserUid(userUid);
        record.setBabyUid(babyUid);
        record.setLevel((int)level);
        record.setTaskType(taskType);
        record.setGiftName(giftName);
        actRedisDao.lpush(groupCode, awardKey, JsonUtil.toJson(record));
    }
    private void broDailyAwardNotice(long userUid , long babyUid, CpTravelDiaryComponentAttr attr, long taskId, TravelDiaryAwardMap diaryAward) {

        TravelDiaryMissionConfig missionConfig = attr.getDailyMissions().stream().filter(v->v.getLevel()==taskId).findFirst().orElse(null);

        Map<String, Object> noticeValue = Maps.newHashMap();
        noticeValue.put("level", taskId);
        noticeValue.put("score", missionConfig.getScore());
        noticeValue.put("giftName", diaryAward.getGiftName());
        noticeValue.put("giftIcon", diaryAward.getGiftIcon());
        noticeValue.put("cpMember", userUid+"|"+babyUid);
        log.info("broDailyCpNotice commonNoticeUnicast actId:{},userUid:{},babyUid:{},taskId:{}",attr.getActId(),userUid,babyUid,taskId);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), DAILY_TASK_AWARD_NOTICE_TYPE, JsonUtil.toJson(noticeValue), StringUtils.EMPTY, userUid);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), DAILY_TASK_AWARD_NOTICE_TYPE, JsonUtil.toJson(noticeValue), StringUtils.EMPTY, babyUid);
    }


    private void broDailyCpNotice(long userUid , long babyUid, CpTravelDiaryComponentAttr attr, long taskId) {
        Set<Long> uids = new HashSet<>();
        uids.add(userUid);
        uids.add(babyUid);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,attr.getTemplateType());

        TravelDiaryMissionConfig missionConfig = attr.getDailyMissions().stream().filter(v->v.getLevel()==taskId).findFirst().orElse(null);
        String levelName = missionConfig !=null ? missionConfig.getLevelName() : StringUtil.EMPTY;

        //每日任务达成弹窗
        Map<String, Object> noticeValue = Maps.newHashMap();
        noticeValue.put("level", taskId);
        noticeValue.put("levelName", levelName);
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo babyInfoVo = userInfos.get(babyUid);
        noticeValue.put("userUid",userUid);
        noticeValue.put("babyUid",babyUid);
        if(userInfoVo!=null ){
            noticeValue.put("userLogo",userInfoVo.getAvatarUrl());
            noticeValue.put("userNick",userInfoVo.getNick());
        }
        if(babyInfoVo!=null) {
            noticeValue.put("babyLogo",babyInfoVo.getAvatarUrl());
            noticeValue.put("babyNick",babyInfoVo.getNick());
        }
        noticeValue.put("nickExtUsers",JsonUtil.toJson(multiNickUsers));

        log.info("broDailyCpNotice commonNoticeUnicast actId:{},userUid:{},babyUid:{},taskId:{}",attr.getActId(),userUid,babyUid,taskId);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), DAILY_TASK_FINISH_NOTICE_TYPE, JsonUtil.toJson(noticeValue), StringUtils.EMPTY, userUid);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), DAILY_TASK_FINISH_NOTICE_TYPE, JsonUtil.toJson(noticeValue), StringUtils.EMPTY, babyUid);

    }

    /**
     * 获取当前用户关联的CP列表
     * @return
     */
    @RequestMapping("/getCpList")
    public Response getCpList(HttpServletRequest request, HttpServletResponse response, long actId, long cmptInx) {
        long uid = getLoginYYUid(request, response);
        if(uid <= 0) {
            return Response.fail(400, "未登陆");
        }

        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }

        List<CpMember> cpMembers = new ArrayList<>();
        CpTravelDiaryComponentAttr attr = getComponentAttr(actId, cmptInx);
        Map<String, QueryRankingRequest> reqMap = Maps.newHashMap();
        QueryRankingRequest contributeReq = new QueryRankingRequest();
        contributeReq.setActId(actId);
        contributeReq.setRankingId(attr.getCpContributeRankId());
        contributeReq.setPhaseId(attr.getPhaseId());
        contributeReq.setFindSrcMember(Convert.toString(uid));
        contributeReq.setRankingCount(attr.getRankLimit());
        reqMap.put(Convert.toString(uid), contributeReq);
        Set<Long> uids = Sets.newHashSetWithExpectedSize((int)attr.getRankLimit());
        uids.add(uid);
        Map<String, BatchRankingItem> conTop = hdztRankingThriftClient.queryBatchRanking(reqMap, null);
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(Convert.toLong(rank.getMember()));
            }
        }

        Map<String, QueryRankingRequest> antiReqMap = Maps.newHashMap();
        QueryRankingRequest antiContributeReq = new QueryRankingRequest();
        antiContributeReq.setActId(actId);
        antiContributeReq.setRankingId(attr.getCpAntiContributeRankId());
        antiContributeReq.setPhaseId(attr.getPhaseId());
        antiContributeReq.setFindSrcMember(Convert.toString(uid));
        antiContributeReq.setRankingCount(attr.getRankLimit());
        antiReqMap.put(Convert.toString(uid), antiContributeReq);
        Map<String, BatchRankingItem> conAntiTop = hdztRankingThriftClient.queryBatchRanking(antiReqMap, null);
        for (BatchRankingItem value : conAntiTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(Convert.toLong(rank.getMember()));
            }
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,attr.getTemplateType());
        UserInfoVo loginUserInfo = userInfos.get(uid);
        MemInfo loginMemInfo = new MemInfo();
        loginMemInfo.setUid(uid);
        loginMemInfo.setName(loginUserInfo!=null ? loginUserInfo.getNick(): StringUtil.EMPTY);
        loginMemInfo.setAvatar(loginUserInfo!=null ? loginUserInfo.getAvatarUrl() :StringUtil.EMPTY);
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                CpMember cpMember = new CpMember();
                long toUid = Convert.toLong(rank.getMember());
                String cpMemberStr = toUid + "|" + uid;
                UserInfoVo toUserInfo = userInfos.get(toUid);
                MemInfo toMemInfo = new MemInfo();
                toMemInfo.setUid(toUid);
                toMemInfo.setName(toUserInfo!=null ? toUserInfo.getNick() : StringUtil.EMPTY);
                toMemInfo.setAvatar(toUserInfo!=null ? toUserInfo.getAvatarUrl() : StringUtil.EMPTY);
                cpMember.setCpMember(cpMemberStr);
                cpMember.setAnchor(loginMemInfo);
                cpMember.setUser(toMemInfo);
                cpMember.setScore(rank.getScore());

                //获取正在开播频道
                ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(uid);
                if (onMicChannel != null) {
                    cpMember.setSid(onMicChannel.getSid());
                    cpMember.setSsid(onMicChannel.getSsid());
                }

                cpMembers.add(cpMember);
            }
        }
        for (BatchRankingItem value : conAntiTop.values()) {
            for (Rank rank : value.getData()) {
                CpMember cpMember = new CpMember();
                long toUid = Convert.toLong(rank.getMember());
                String cpMemberStr = uid + "|" + toUid;
                UserInfoVo toUserInfo = userInfos.get(toUid);
                MemInfo toMemInfo = new MemInfo();
                toMemInfo.setUid(toUid);
                toMemInfo.setName(toUserInfo!=null ? toUserInfo.getNick() : StringUtil.EMPTY);
                toMemInfo.setAvatar(toUserInfo!=null ? toUserInfo.getAvatarUrl() : StringUtil.EMPTY);
                cpMember.setCpMember(cpMemberStr);
                cpMember.setAnchor(toMemInfo);
                cpMember.setUser(loginMemInfo);
                cpMember.setScore(rank.getScore());
                //获取正在开播频道
                ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(toUid);
                if (onMicChannel != null) {
                    cpMember.setSid(onMicChannel.getSid());
                    cpMember.setSsid(onMicChannel.getSsid());
                }
                cpMembers.add(cpMember);
            }
        }

        if(CollectionUtils.isNotEmpty(cpMembers)){
            cpMembers.sort(Comparator.comparing(CpMember::getScore).reversed());
        }

        CpListRsp  rsp = new CpListRsp();
        rsp.setCpMembers(cpMembers);
        rsp.setNickExtUsers(multiNickUsers);
        rsp.setUid(uid);
        rsp.setAvatar(userInfos.get(uid).getAvatarUrl());
        rsp.setSign(signedService.getSignedSidByBusiId(uid, attr.getBusiId()) > 0);

        return Response.success(rsp);
    }


    /**
     * 获取任务完成进度
     * @return
     */
    @RequestMapping("/getCpMissions")
    public Response getCpMissions(HttpServletRequest request, HttpServletResponse response, String cpMember, long actId, long cmptInx) {
        long uid = getLoginYYUid(request, response);
        if(uid <= 0) {
            return Response.fail(400, "未登陆");
        }

        int status = actInfoService.actTimeStatus(actId);
        if (status < 0) {
            return Response.fail(3, "活动未开始!");
        }

        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }

        UserBaseInfo loginUserInfo = null;
        if(StringUtil.isEmpty(cpMember)) {
            cpMember = "1001|"+uid;
            List<Long> uids = new ArrayList<>();
            uids.add(Convert.toLong(uid));
            Map<Long, UserBaseInfo> userMap = commonService.batchGetUserInfos(uids, false);
            loginUserInfo = userMap.get(uid);
        }

        CpTravelDiaryComponentAttr attr = getComponentAttr(actId, cmptInx);
        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        ActorQueryItem queryItem = new ActorQueryItem();
        ActorQueryItem dailyQueryItem = new ActorQueryItem();
        queryItem.setActorId(cpMember);
        queryItem.setRankingId(attr.getCpRankId());
        queryItem.setPhaseId(attr.getPhaseId());
        queryItem.setWithStatus(false);
        queryCpScorePara.add(queryItem);

        dailyQueryItem.setRankingId(attr.getCpDailyRankId());
        dailyQueryItem.setPhaseId(attr.getPhaseId());
        dailyQueryItem.setActorId(cpMember);
        dailyQueryItem.setWithStatus(false);
        String dateKey = DateUtil.format(commonService.getNow(actId), "yyyyMMdd");
        dailyQueryItem.setDateStr(dateKey);
        queryCpScorePara.add(dailyQueryItem);
        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);

        CpMission cpMission = new CpMission();
        if (!org.springframework.util.CollectionUtils.isEmpty(actorInfoItems)) {
            ActorInfoItem actorInfoItem = actorInfoItems.get(0);
            cpMission.setTotalScore(actorInfoItem.getScore());
            if(actorInfoItems.size() > 1) {
                ActorInfoItem actorInfoDailyItem = actorInfoItems.get(1);
                cpMission.setDailyScore(actorInfoDailyItem.getScore());
            }
        }
        List<Mission> missions = new ArrayList<>();
        int i = 0;
        for (TravelDiaryMissionConfig missionVal : attr.getMissions()) {
            Mission mission = new Mission();
            mission.setTaskId(++i);
            mission.setScore(missionVal.getScore());
            mission.setFinish(missionVal.getScore() <= cpMission.getTotalScore());
            missions.add(mission);
        }
        List<Mission> dailyMissions = new ArrayList<>();
        i = 0;
        for (TravelDiaryMissionConfig dailyMissionVal : attr.getDailyMissions()) {
            Mission mission = new Mission();
            mission.setTaskId(++i);
            mission.setScore(dailyMissionVal.getScore());
            mission.setFinish(dailyMissionVal.getScore() <= cpMission.getDailyScore());
            dailyMissions.add(mission);
        }
        cpMission.setMissions(missions);
        cpMission.setDailyMissions(dailyMissions);
        if(loginUserInfo != null) {
            cpMission.setAvatar(loginUserInfo.getHdLogo());
            cpMission.setNick(loginUserInfo.getNick());
        }

        return Response.success(cpMission);

    }


    /**
     * 查询奖池剩余
     */
    @GetMapping("/queryAwardPool")
    public Response<Map<String, Object>> queryAwardPool(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(name = "actId") long actId,
                                                        @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx) {
        CpTravelDiaryComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        Map<String, Object> result = Maps.newHashMap();

        long send = getAwardPoolConsume(attr);
        long left = Math.max(attr.getTotalPool() - send, 0);

        result.put("total", attr.getTotalPool());
        result.put("send", send);
        result.put("left", left);

        return Response.success(result);
    }


    @RequestMapping("/queryAwardRecord")
    public Response<AwardRecordRsp<TravelDiaryAwardRecord>> queryAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                                                                Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx) {

        CpTravelDiaryComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }


        String groupCode = getRedisGroupCode(attr.getActId());
        String awardKey = makeKey(attr,TASK_AWARD_LIST);

        AwardRecordRsp rsp = new AwardRecordRsp();
        List<String> awardRecord = actRedisDao.lrange(groupCode,awardKey,0,29);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            List<TravelDiaryAwardRecord> list = Lists.newArrayListWithCapacity(awardRecord.size());
            Set<Long> uids = Sets.newHashSetWithExpectedSize(100);
            for (String item : awardRecord) {
                TravelDiaryAwardRecord record = JSON.parseObject(item, TravelDiaryAwardRecord.class);
                uids.add(record.getUserUid());
                uids.add(record.getBabyUid());
                list.add(record);
            }

            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers,attr.getTemplateType());
            for(TravelDiaryAwardRecord item : list) {
                UserInfoVo userInfoVo = userInfos.get(item.getUserUid());
                UserInfoVo babyInfoVo =  userInfos.get(item.getBabyUid());
                if (userInfoVo != null) {
                    item.setUserLogo(userInfoVo.getAvatarUrl());
                }
                if(babyInfoVo!=null) {
                    item.setBabyLogo(babyInfoVo.getAvatarUrl());
                }

                //查询在线频道
                //获取正在开播频道
                ChannelInfoVo onMicChannel = onMicService.getOnMicChannel(item.getBabyUid());
                if (onMicChannel != null) {
                    item.setSid(onMicChannel.getSid());
                    item.setSsid(onMicChannel.getSsid());
                }
            }

            rsp.setList(list);
            rsp.setNickExtUsers(multiNickUsers);
        }

        return Response.success(rsp);
    }

    private long getAwardPoolConsume(CpTravelDiaryComponentAttr attr) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String totalAwardKey = makeKey(attr,TRAVEL_DIARY_TOTAL_AWARD_CONSUME);
        return Convert.toLong(actRedisDao.get(redisCode, totalAwardKey), 0);
    }

    private long getAwardPoolLeft(CpTravelDiaryComponentAttr attr) {
        long send = getAwardPoolConsume(attr);
        long left = Math.max(attr.getTotalPool() - send, 0);
        return left;
    }


    @GetMapping("/taskStatic")
    public Response<String> taskStatic(HttpServletRequest request, HttpServletResponse response, Long actId) {
        Date now = commonService.getNow(actId);
        doStaticReport(actId, now, getUniqueComponentAttr(actId));
        return Response.ok();
    }


    /**
     * 每小时一次日报
     */
    @NeedRecycle(author = "chengaojie", notRecycle = true)
    @Scheduled(cron = "11 0 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }

            CpTravelDiaryComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            Date now = commonService.getNow(attr.getActId());
            //使用物理时间过滤是否已发送过如流
            String timeCode = DateUtil.format(new Date(),DateUtil.PATTERN_TYPE7);
            String groupCode = getRedisGroupCode(actId);
            String execKey = makeKey(attr, "execStatic:"+timeCode);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}",execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId,now);

            doStaticReport(actId, now, attr);
        }
    }

    public void doStaticReport(long actId, Date now, CpTravelDiaryComponentAttr attr) {
        if(attr==null) {
            log.error("doStaticReport doStaticReport attr is null,actId:{}",actId);
            return;
        }
        String timeCode = DateUtil.format(now,DateUtil.PATTERN_TYPE2);

        StringBuilder content = new StringBuilder();
        content.append("截止当前时间，本日任务达成次数\n");

        String groupCode = getRedisGroupCode(attr.getActId());
        String dailyKey = makeKey(attr,String.format(DAILY_TASK_FINISH_STATISTIC,timeCode));
        Map<Object, Object> dailyTask = actRedisDao.hGetAll(groupCode,dailyKey);
        for(TravelDiaryMissionConfig item : attr.getDailyMissions()) {
            long count = Convert.toInt(dailyTask.getOrDefault(item.getLevel()+"", 0));
            content.append("本日LV").append(item.getLevel()).append("：").append(count).append("\n");
        }

        content.append("截止当前时间，累计任务达成次数\n");
        String key = makeKey(attr,TASK_FINISH_STATISTIC);
        Map<Object, Object> task = actRedisDao.hGetAll(groupCode,key);
        for(TravelDiaryMissionConfig item : attr.getMissions()) {
            long count = Convert.toInt(task.getOrDefault(item.getLevel()+"", 0));
            content.append("累计LV").append(item.getLevel()).append("：").append(count).append("\n");
        }

        String totalAwardKey = makeKey(attr,TRAVEL_DIARY_TOTAL_AWARD_CONSUME);
        String totalAwardValue = actRedisDao.get(groupCode,totalAwardKey);

        content.append("总礼物金额（元）：").append(formatMoney(Convert.toLong(totalAwardValue,0))).append("\n");
        String msg = buildActRuliuMsg(actId, false, "旅行日记玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

    }

    public static String formatMoney(long awardNum) {
        DecimalFormat df = new DecimalFormat("#.#");
        return df.format((float)awardNum/1000);
    }



    @Data
    private static class CpListRsp {
        List<CpMember> cpMembers;

        Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private long uid;

        private String avatar;

        private boolean sign;
    }

    @Data
    private static class CpMember {
        private String cpMember;

        private MemInfo anchor;

        private MemInfo user;

        private long score;
        private long sid;
        private long ssid;

    }

    @Data
    private static class MemInfo {
        private long uid;

        private String name;

        private String avatar;
    }

    @Data
    private static class CpMission {
        private List<Mission> missions;

        private List<Mission> dailyMissions;

        private long dailyScore;

        private long totalScore;

        private String avatar;

        private String nick;
    }

    @Data
    private static class Mission {
        private long taskId;

        private boolean finish;

        private long score;
    }


    @Data
    private static class AwardRecord {
        private long taskId;

        private boolean finish;

        private long score;
    }




}
