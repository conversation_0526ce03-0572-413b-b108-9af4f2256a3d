package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ActResultGroupVoNew;
import com.yy.gameecology.activity.bean.ActResultVo;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.service.ActResultService;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.model.gameecology.ActResult;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.HonorConfig;
import com.yy.gameecology.hdzj.bean.PKMembers;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ActivityGloryReportComponentAttr;
import com.yy.thrift.hdztranking.*;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 阶段结束产生荣耀报告
 */
@UseRedisStore
@Slf4j
@Component
public class ActivityGloryReportComponent extends BaseActComponent<ActivityGloryReportComponentAttr> {

    @Autowired
    private ActResultService actResultService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.ACTIVITY_GLORY_REPORT;
    }


    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void handlePhaseTimeEnd(PhaseTimeEnd event, ActivityGloryReportComponentAttr attr) {
        log.info("ActivityGloryReportComponent phase time end handle with event:{}", event);
        handleSettle(event.getActId(), event.getRankId(), event.getPhaseId(), attr);
    }

    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void handleRankingTimeEnd(RankingTimeEnd event, ActivityGloryReportComponentAttr attr) {
        log.info("ActivityGloryReportComponent handle ranking time end with event:{}", event);
        handleSettle(event.getActId(), event.getRankId(), 0, attr);
    }

    public void handleSettle(long actId, long rankId, long phaseId, ActivityGloryReportComponentAttr attr) {
        if (StringUtils.isEmpty(attr.getGloryLink())) {
            return;
        }

        if (attr.getActId() != actId) {
            return;
        }
        String rankPhaseKey = rankId + "_" + phaseId;
        final String groupId = attr.getRankGroupMap().get(rankPhaseKey);
        if (groupId == null) {
            return;
        }

        List<HonorConfig> honorConfigs = attr.getConfigsMap().get(groupId);
        List<ActivityGloryReportComponentAttr.ActResult> configActResults = attr.getActResults();
        Map<String, ActivityGloryReportComponentAttr.ActResult> groupActResultMap = new LinkedHashMap<>(configActResults.size());
        for (ActivityGloryReportComponentAttr.ActResult configActResult : configActResults) {
            String key = configActResult.getType() + ":" + configActResult.getGroupId() + ":" + configActResult.getRank();
            groupActResultMap.put(key, configActResult);
        }

        log.debug("ActivityGloryReportComponent build groupActResultMap:{}", groupActResultMap);
        List<ActResult> actResults = new ArrayList<>();
        Set<Integer> types = Sets.newHashSet();
        List<Rank> targetRanks = null;
        for (HonorConfig config : honorConfigs) {
            if (config.getPhaseId() != phaseId || config.getRankId() != rankId) {
                continue;
            }

            types.add(config.getType());
            //获取前n个
            if (targetRanks == null) {
                targetRanks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, "", 50, Maps.newHashMap());
            }

            String configGroup = groupId;

            List<Rank> ranks;
            if ("group".equals(config.getRankType())) {
                MemberGroup memberGroup = hdztRankingThriftClient.queryQualificationGroup(actId, rankId, phaseId);
                log.info("get memberGroup of rankId:{}, phaseId:{}, group:{}", rankId, phaseId, JSON.toJSONString(memberGroup));
                GroupItem groupItem = memberGroup.getGroupItems().stream().filter(item -> Objects.equals(item.getCode(), config.getRankGroupCode()) || Objects.equals(item.getName(), config.getRankGroupCode())).findFirst().orElse(null);
                Set<String> members = groupItem.getMemberItems().stream().map(GroupMemberItem::getMemberId).collect(Collectors.toSet());
                ranks = targetRanks.stream().filter(item -> members.contains(item.getMember())).collect(Collectors.toList());
                configGroup = groupId + config.getGroupCode();
            } else {
                ranks = targetRanks;
                if ("pk".equals(config.getRankType())) {
                    List<PKMembers<Rank>> pkMembers = setUpPK(actId, config.getRankId(), config.getPhaseId(), ranks);
                    if (config.isWinner()) {
                        ranks = pkMembers.stream().map(PKMembers::getWinner).collect(Collectors.toList());
                    } else {
                        ranks = pkMembers.stream().map(PKMembers::getLoser).collect(Collectors.toList());
                    }
                }
            }
            ranks.sort(Comparator.comparingInt(Rank::getRank));

            int startIndex = config.getStartIndex() - 1;
            int rankIndex = config.getRankIndex();
            for (int i = 0; i < config.getCount(); i++) {
                if (startIndex < ranks.size()) {
                    String key = config.getType() + ":" + configGroup + ":" + rankIndex;
                    ActivityGloryReportComponentAttr.ActResult configActResult = groupActResultMap.get(key);
                    ActResult actResult = new ActResult();
                    if (configActResult != null) {
                        actResult.setTitle(configActResult.getTitle());
                        actResult.setRoleType(configActResult.getRoleType());
                    }

                    actResult.setActId(actId);
                    actResult.setMemberId(ranks.get(startIndex).getMember());
                    long score = ranks.get(startIndex).getScore();
                    actResult.setExtData("{\"score\":" + score + "}");
                    actResult.setType(config.getType());
                    actResult.setGroupId(configGroup);
                    actResult.setRank(rankIndex);
                    actResult.setStatus(1);
                    actResults.add(actResult);
                    startIndex++;
                    rankIndex++;
                }
            }
        }

        Map<String, List<String>> roleTypeMemberId = Maps.newHashMap();
        actResults.forEach(x -> {
            final String memberId = x.getMemberId();
            if (StringUtil.isBlank(memberId)) {
                return;
            }

            String key = x.getType() + "_" + x.getRoleType();
            List<String> memberIds = roleTypeMemberId.getOrDefault(key, Lists.newArrayList());
            memberIds.add(x.getMemberId());
            roleTypeMemberId.put(key, memberIds);
        });

        //key-- type_roleType, key memberId, value-- memberId 成员昵称头像信息
        Map<String, Map<String, MemberInfo>> roleTypeMemberInfo = actResultService.queryMemberInfo(actId, roleTypeMemberId);

        Map<String, ActResultGroupVoNew> groupVoMap = Maps.newLinkedHashMap();
        for (ActivityGloryReportComponentAttr.GroupDefine group : attr.getGroupDefines()) {
            ActResultGroupVoNew actResultGroupVo = new ActResultGroupVoNew();
            actResultGroupVo.setGroupId(group.getGroupId());
            actResultGroupVo.setGroupName(group.getGroupName());
            actResultGroupVo.setType(group.getType());

            List<List<ActResultVo>> actResultVos = Lists.newArrayList();
            //分组成员信息
            actResults.stream().filter(x -> x.getGroupId().equals(group.getGroupId())).forEach(item -> {
                List<ActResultVo> items = Lists.newArrayList();
                ActResultVo actResultVo = new ActResultVo();
                String memberId = item.getMemberId();
                int type = item.getType();
                if (StringUtil.isBlank(memberId)) {
                    actResultVo.setTitle(item.getTitle());
                    actResultVo.setRank(item.getRank());
                    items.add(actResultVo);
                    if (item.getRoleType() == 100200) {
                        ActResultVo empty = new ActResultVo();
                        empty.setTitle(item.getTitle());
                        empty.setRank(item.getRank());
                        items.add(empty);
                    }
                } else if (memberId.contains("|")) {
                    String[] memberIds = memberId.split("\\|");

                    String userUid = memberIds[0];
                    ActResultVo userInfo = actResultService.buildActResult(actId, userUid, item, 1);
                    MemberInfo userMemberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + RoleType.USER.getValue(), Maps.newHashMap())
                            .getOrDefault(userUid, null);
                    if (userMemberInfo != null) {
                        userInfo.setNick(actResultService.convert2Nick(userMemberInfo.getName(), userUid));
                        userInfo.setAvatarInfo(userMemberInfo.getLogo());
                    }
                    items.add(userInfo);

                    String anchorUid = memberIds[1];
                    ActResultVo anchorInfo = actResultService.buildActResult(actId, anchorUid, item, 1);
                    MemberInfo anchorMemberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + RoleType.ANCHOR.getValue(), Maps.newHashMap())
                            .getOrDefault(anchorUid, null);
                    if (anchorMemberInfo != null) {
                        anchorInfo.setNick(actResultService.convert2Nick(anchorMemberInfo.getName(), anchorUid));
                        anchorInfo.setAvatarInfo(anchorMemberInfo.getLogo());
                    }
                    items.add(anchorInfo);

                } else {
                    actResultVo = actResultService.buildActResult(actId, memberId, item, 1);
                    MemberInfo memberInfo = roleTypeMemberInfo
                            .getOrDefault(type + "_" + item.getRoleType(), Maps.newHashMap())
                            .getOrDefault(memberId, null);
                    if (memberInfo != null) {
                        actResultVo.setNick(actResultService.convert2Nick(memberInfo.getName(), memberId));
                        actResultVo.setAvatarInfo(memberInfo.getLogo());
                        actResultVo.setAsid(memberInfo.getAsid());
                    }

                    //主播增加开播状态
                    if (actResultVo.getRoleType() == RoleType.ANCHOR.getValue()) {
                        ChannelInfoVo channelInfoVo = onMicService.getOnMicChannel(Convert.toLong(actResultVo.getMemberId(), 0));
                        if (channelInfoVo != null) {
                            actResultVo.setSid(channelInfoVo.getSid());
                            actResultVo.setSsid(channelInfoVo.getSsid());
                        }
                    }
                    items.add(actResultVo);
                }
                actResultVos.add(items);
            });
            if (CollectionUtils.isNotEmpty(actResultVos)) {
                actResultGroupVo.setActResults(actResultVos);
                groupVoMap.put(group.getGroupId(), actResultGroupVo);
            }
        }

        String groupCode = redisConfigManager.getGroupCode(actId);

        String key = makeKey(attr, "GLORY_HASH");

        Map<String, String> map = groupVoMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> JSON.toJSONString(entry.getValue())));
        actRedisDao.hmset(groupCode, key, map, 20 * 24 * 60 * 60);
    }

    @Scheduled(cron = "3/5 * * * * ?")
    @NeedRecycle(author = "liqingyang", notRecycle = true)
    public void tryReportData() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        List<ActivityInfoVo> effectActInfos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return;
        }

        List<Long> effectActIds = effectActInfos.stream().map(ActivityInfoVo::getActId).collect(Collectors.toList());
        Collections.shuffle(effectActIds);
        for (long actId : effectActIds) {
            if (!actIds.contains(actId)) {
                continue;
            }

            List<ActivityGloryReportComponentAttr> attrs = this.getAllComponentAttrs(actId);
            if (CollectionUtils.isEmpty(attrs)) {
                continue;
            }

            String groupCode = redisConfigManager.getGroupCode(actId);

            outer:
            for (ActivityGloryReportComponentAttr attr : attrs) {
                List<ActivityGloryReportComponentAttr.GroupDefine> groupDefines = attr.getGroupDefines();
                if (CollectionUtils.isEmpty(groupDefines)) {
                    continue;
                }
                String key = makeKey(attr, "GLORY_HASH");
                Map<Object, Object> allGlorys = actRedisDao.hGetAll(groupCode, key);
                for (ActivityGloryReportComponentAttr.GroupDefine groupDefine : groupDefines) {
                    if (!allGlorys.containsKey(groupDefine.getGroupId())) {
                        continue outer;
                    }
                }

                String existKey = makeKey(attr, "REPORT_DATA");
                boolean succ = actRedisDao.setNX(groupCode, existKey, DateUtil.getNowYyyyMMddHHmmss(), 5 * 24 * 60 * 60);
                if (!succ) {
                    continue;
                }

                Map<String, ActResultGroupVoNew> groupVoMap = new HashMap<>(allGlorys.size());
                for (Map.Entry<Object, Object> entry : allGlorys.entrySet()) {
                    String groupId = (String) entry.getKey();
                    String jsonStr = (String) entry.getValue();
                    ActResultGroupVoNew groupVo = JSON.parseObject(jsonStr, ActResultGroupVoNew.class);
                    groupVoMap.put(groupId, groupVo);
                }

                String reportKey = RandomStringUtils.random(31, true, true);
                actRedisDao.set(groupCode, reportKey, JSON.toJSONString(groupVoMap), 20 * 24 * 60 * 60);
                String link = attr.getGloryLink();
                if (StringUtils.contains(link, "?") && !StringUtils.endsWith(link, "&")) {
                    link += "&";
                }

                if (!StringUtils.contains(link, "?")) {
                    link += "?";
                }

                link += ("actId=" + actId + "&seq=" + reportKey);

                String msg;
                try {
                    String templateName = makeKey(attr, "TPL_NAME");
                    Template template = new Template(templateName, attr.getTemplate(), null);
                    msg = FreeMarkerTemplateUtils.processTemplateIntoString(template, ImmutableMap.of("link", link));
                } catch (Exception e) {
                    log.warn("ActivityGloryReportComponent processTemplate exception:", e);
                    actRedisDao.del(groupCode, existKey);
                    return;
                }

                log.info("ActivityGloryReportComponent gen link:{}, groupVoMap:{}", link, groupVoMap);

                //baiduInfoFlowRobotService.sendNotifyByActAttrKey(attr.getActId(), "activity_report_notice", msg, Collections.emptyList());
                baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
            }

        }
    }

    public String getReportData(long actId, String seq) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        return actRedisDao.get(groupCode, seq);
    }
}
