package com.yy.gameecology.hdzj.element.history;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.bean.mq.HdzkWzryGameEvent;
import com.yy.gameecology.activity.bean.mq.SignInEvent;
import com.yy.gameecology.activity.client.thrift.TurnoverAccountServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaLoginClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaSignInClient;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeActAttrConst;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.wzry.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.WzryTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.WzryTaskComponentAttr.*;
import com.yy.gameecology.hdzj.utils.ZhuiyaClientUtils;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon.Client;
import com.yy.protocol.pb.zhuiwan.login.LoginRecord;
import com.yy.protocol.pb.zhuiwan.risk.ZhuiyaRisk;
import com.yy.protocol.pb.zhuiwan.signin.SignIn;
import com.yy.thrift.hdztranking.BusiId;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.eclipse.jetty.util.ajax.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * @Author: CXZ
 * @Desciption: 王者荣耀任务
 * @Date: 2024/1/16 14:37
 * @Modified:
 */
@RequiredArgsConstructor
@Slf4j
@Component
@RestController
@RequestMapping("/cmpt/wzryTask")
public class WzryTaskComponent extends BaseActComponent<WzryTaskComponentAttr> {


    private final ZhuiyaLoginClient loginClient;
    private final ZhuiyaSignInClient signInClient;
    private final ZhuiwanRiskClient zhuiwanRiskClient;
    private final TurnoverAccountServiceClient turnoverAccountServiceClient;
    private final ActInfoService actInfoService;
    private static final String TASK_PROGRESS_KEY_FORMAT = "progress_%d_%s_%d";
    private static final String TASK_AWARD_KEY_FORMAT = "award_%d_%s_%d";
    private static final String TASK_START_KEY_FORMAT = "start_progress_key_%d_%d";
    private static final String AWARD_DESC_FORMAT = "通过%s获得";

    @Autowired
    private ThreadPoolManager threadPoolManager;

    /**
     * 完成任务人数日统计-登陆
     */
    private static final String TASK_DAY_AMOUNT_LOGIN = "task_day_amount_login";

    /**
     * 完成任务人数日统计-完成一场赏金赛
     */
    private static final String TASK_DAY_AMOUNT_GAME = "task_day_amount_game";

    /**
     * 完成任务人数日统计-签到
     */
    private static final String TASK_DAY_AMOUNT_SIGN = "task_day_amount_sign_%s";

    /**
     * 完成任务人数日统计-开黑
     */
    private static final String TASK_DAY_AMOUNT_KAIHEI = "task_day_amount_kaihei";




    @Override
    public Long getComponentId() {
        return ComponentId.WZRY_TASK;
    }


    @HdzjEventHandler(value = SignInEvent.class, canRetry = true)
    public void onSignInEvent(SignInEvent event, WzryTaskComponentAttr attr) {
        if (attr.getSignInId() != event.getConfigId()) {
            return;
        }
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }
        Client client = ZhuiyaClientUtils.toClient(event.getDeviceId(), "", event.getApp(), event.getClientType(), "", "");
        releaseSignIn(attr, event.getUid(), event.getDate(), event.getDay(), event.isFirstSignIn(), now, client);
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, WzryTaskComponentAttr attr) {
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }
        for (HdztTaskSource taskSource : attr.getTaskSourceList()) {
            if (event.getRankId() != taskSource.getRankId() && event.getPhaseId() != taskSource.getPhaseId()) {
                continue;
            }
            addProgress(attr, Long.parseLong(event.getMember()), taskSource.getType(), event.getSeq(), now);
        }

    }

    @HdzjEventHandler(value = HdzkWzryGameEvent.class, canRetry = true)
    public void onHdzkWzryGameEvent(HdzkWzryGameEvent event, WzryTaskComponentAttr attr) {
        if (event.getGameState() != 900) {
            log.info("onHdzkWzryGameEvent ignore,event:{}", event);
            return;
        }
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }
        for (Long uid : event.getUser().keySet()) {
            addProgress(attr, uid, TaskType.WZRY_GAME.getCode(), event.getSeq() + "_" + uid, now);
        }

    }

    /**
     * 获取任务详情
     *
     * @param request
     * @param response
     * @param actId
     * @param cmptIndex
     * @return
     */
    @GetMapping("/taskList")
    public Response<TaskDetailVo> taskList(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        WzryTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        Client client = ZhuiyaClientUtils.getClient(request);
        Date now = commonService.getNow(actId);
        try {
            TaskDetailVo signInVo = getTaskDetailVo(attr, uid, now, client);
            return Response.success(signInVo);
        } catch (BusinessException e) {
            log.warn("signIn error@actId:{},uid:{},code:{},messge:{},data:{}", actId, uid, e.getCode(), e.getMessage(), e.getData());
            return new Response(e.getCode(), e.getMessage(), e.getData());
        } catch (Exception e) {
            log.error("signIn error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }
    }

    /**
     * 登录弹窗
     */
    @GetMapping("/loginTip")
    public Response<LoginTipVo> loginTip(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        WzryTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        Client client = ZhuiyaClientUtils.getClient(request);
        Date now = commonService.getNow(actId);
        try {
            LoginTipVo loginTipVo = loginTip(attr, uid, now, client);
            return Response.success(loginTipVo);
        } catch (BusinessException e) {
            log.warn("loginTip error@actId:{},uid:{},code:{},messge:{},data:{}", actId, uid, e.getCode(), e.getMessage(), e.getData());
            return new Response(e.getCode(), e.getMessage(), e.getData());
        } catch (Exception e) {
            log.error("loginTip error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }
    }

    /**
     * 获取开黑房间
     */
    @GetMapping("/getJumpChannel")
    public Response<SidSsidVo> getJumpChannel(Long actId, Long cmptIndex) {
        WzryTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        SidSsidVo sidSsidVo = getJumpChannel(attr);
        return Response.success(sidSsidVo);

    }

    /**
     * 签到
     */
    @GetMapping("/signIn")
    public Response<SignInVo> signIn(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        WzryTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        Client client = ZhuiyaClientUtils.getClient(request);
        Date now = commonService.getNow(actId);
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return Response.fail(1, "不在活动时间");
        }
        try {
            SignInVo signInVo = signIn(attr, uid, now, client);
            return Response.success(signInVo);
        } catch (BusinessException e) {
            log.warn("signIn error@actId:{},uid:{},code:{},messge:{},data:{}", actId, uid, e.getCode(), e.getMessage(), e.getData());
            return new Response(e.getCode(), e.getMessage(), e.getData());
        } catch (Exception e) {
            log.error("signIn error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }
    }

    /**
     * 领取奖励
     */
    @GetMapping("/drawAward")
    public Response<Map<String, Integer>> drawAward(HttpServletRequest request, HttpServletResponse response,
                                                    Long actId, Long cmptIndex, Integer taskType) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        WzryTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        Date now = commonService.getNow(actId);
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return Response.fail(1, "不在活动时间");
        }

        Client client = ZhuiyaClientUtils.getClient(request);

        try {
            int award = drawAward(attr, uid, taskType, now, client);
            return Response.success(Map.of("award", award));
        } catch (BusinessException e) {
            log.warn("drawAward error@actId:{},uid:{},code:{},messge:{},data:{}", actId, uid, e.getCode(), e.getMessage(), e.getData());
            return new Response(e.getCode(), e.getMessage(), e.getData());
        } catch (Exception e) {
            log.error("drawAward error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }
    }

    /**
     * 门票领取红点
     *
     * @param request
     * @param response
     * @param actId
     * @param cmptIndex
     * @return
     */
    @GetMapping("/hasTaskAward")
    public Response<Map<String, Boolean>> hasTaskAward(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        WzryTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        Date now = commonService.getNow(actId);
        boolean hasAward = false;
        try {
            hasAward = hasAward(attr, uid, now);
        } catch (Exception e) {
            log.error("hasTaskAward error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
        }
        return Response.success(Map.of("hasAward", hasAward));
    }


    @GetMapping("/doWelfarePiaoTest")
    public Response<String> doWelfarePiaoTest(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex, Long addUid, int count) {
        long uid = getLoginYYUid(request, response);
        if (uid != 50042952 || !commonService.isGrey(actId)) {
            return Response.fail(-1, "未授权");
        }
        WzryTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        Client client = ZhuiyaClientUtils.toClient("", "", "", 0, "", "");
        doWelfarePiao(attr, addUid, count, "手工新增", commonService.getNow(actId), client);

        return Response.ok();
    }
    /**
     * 查询开黑频道-白名单
     *
     * @param attr
     * @return
     */
    public SidSsidVo getJumpChannel(WzryTaskComponentAttr attr) {
        String channelString = cacheService.getActAttrValue(attr.getActId(), GeActAttrConst.GAME_TEAM_CHANNEL);
        if (StringUtil.isBlank(channelString)) {
            return new SidSsidVo(NumberUtils.LONG_ZERO, NumberUtils.LONG_ZERO);
        }
        String[] split = channelString.split(",");
        int index = split.length > 1 ? RandomUtils.nextInt(split.length) : 0;
        String[] channel = split[index].split("_");
        return new SidSsidVo(Long.parseLong(channel[0]), Long.parseLong(channel[1]));
    }

    /**
     * 获取任务详情
     *
     * @param attr
     * @param uid
     * @param now
     * @param client
     * @return
     */

    public TaskDetailVo getTaskDetailVo(WzryTaskComponentAttr attr, long uid, Date now, Client client) {
        boolean newUser = isNewUseNotException(attr, uid, client.getHdid(), now);
        SignInDetailVo signInDetail = getSignInDetail(attr, uid, newUser, now, client);
        long balance = turnoverAccountServiceClient.getUserAccountFilterByUidSlave(uid, attr.getTurnoverAppId(), attr.getPiaoCurrentType());
        List<TaskInfoVo> taskList = getTaskList(attr, uid, newUser, now);
        long firstTaskCount = taskList.stream().filter(TaskInfoVo::isFirst).count();
        //新用户标识在所有任务完成过一轮之后就会消失
        newUser = newUser && (signInDetail.isFirst() || firstTaskCount > 0);
        return new TaskDetailVo(balance, newUser, signInDetail, taskList);

    }

    /**
     * 获取任务列表
     *
     * @param attr
     * @param uid
     * @param newUser
     * @param now
     * @return
     */
    public List<TaskInfoVo> getTaskList(WzryTaskComponentAttr attr, long uid, boolean newUser, Date now) {

        List<String> taskKeyList = Lists.newArrayList();

        List<WzryTaskComponentAttr.TaskConfig> taskConfigList = Lists.newArrayList();
        for (WzryTaskComponentAttr.TaskConfig config : attr.getTaskConfigList()) {
            if (!config.isShow()) {
                continue;
            }
            taskConfigList.add(config);
            taskKeyList.add(getTaskProgressKey(attr, config, now, uid));
            taskKeyList.add(getStartTaskKey(attr, config, uid));
            taskKeyList.add(getAwardTaskKey(attr, config, now, uid));
        }

        List<TaskInfoVo> taskInfoVos = Lists.newArrayList();
        if (!taskConfigList.isEmpty()) {
            int doing = 0;
            int hasAward = 1;
            int finish = 2;
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            List<String> progressList = actRedisDao.getRedisTemplate(groupCode).opsForValue().multiGet(taskKeyList);
            List<List<String>> progressPartition = Lists.partition(progressList, 3);
            for (int i = 0; i < taskConfigList.size(); i++) {
                WzryTaskComponentAttr.TaskConfig taskConfig = taskConfigList.get(i);
                TaskInfoVo taskInfoVo = toTaskInfoVo(taskConfig);
                String myKey = taskKeyList.get(3 * i);
                List<String> progressItem = progressPartition.get(i);
                long progress = Convert.toLong(progressItem.get(0));
                String starKey = Convert.toString(progressItem.get(1));
                int award = Convert.toInt(progressItem.get(2));
                boolean isNew = newUser && (StringUtil.isBlank(starKey) || myKey.equals(starKey));
                taskInfoVo.setAward(isNew ? taskConfig.getNewUserAward() : taskConfig.getAward());
                if (progress >= taskInfoVo.getProgressGoal()) {
                    taskInfoVo.setProgress(taskInfoVo.getProgressGoal());
                    taskInfoVo.setState(finish);
                } else {
                    taskInfoVo.setProgress(progress);
                    taskInfoVo.setState(doing);
                }
                if (award > 0) {
                    taskInfoVo.setState(hasAward);
                }
                taskInfoVo.setFirst(isNew);
                taskInfoVos.add(taskInfoVo);
            }
        }
        return taskInfoVos;
    }

    private TaskInfoVo toTaskInfoVo(WzryTaskComponentAttr.TaskConfig taskConfig) {
        TaskInfoVo vo = new TaskInfoVo();
        vo.setTaskName(taskConfig.getName());
        vo.setTaskIcon(taskConfig.getIcon());
        vo.setAward(taskConfig.getAward());
        vo.setTaskType(taskConfig.getType());
        vo.setProgressGoal(taskConfig.getGoal());
        vo.setProgress(NumberUtils.LONG_ZERO);
        vo.setState(NumberUtils.INTEGER_ZERO);
        return vo;
    }

    /**
     * 获取签到详情
     *
     * @param attr
     * @param uid
     * @param newUser
     * @param now
     * @param client
     * @return
     */
    public SignInDetailVo getSignInDetail(WzryTaskComponentAttr attr, long uid, boolean newUser, Date now, Client client) {
        if (attr.getSignInId() <= 0 || attr.getPeriod() <= 0) {
            return null;
        }

        SignIn.SignInDetailsReq detailsReq = SignIn.SignInDetailsReq.newBuilder().setUid(uid)
                .setConfigId(attr.getSignInId())
                .setGreyTime(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId()) ? now.getTime() : 0L)
                .setClient(client).build();
        SignIn.SignInDetailsResp resp = signInClient.getSignInDetails(detailsReq);
        if (resp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
            throw new BusinessException(resp.getCode(), resp.getMessage());
        }

        SignIn.SignInDetailsVo details = resp.getDetails();
        Map<Integer, Integer> signInAward = attr.getSignInAward();
        int period = attr.getPeriod();
        boolean first = false;
        //是否开始新的周期
        boolean isNewPeriod = DateUtil.betweenDay(new Date(details.getStartDate()), now, true) >= period;
        if (newUser && details.getFirst() && !isNewPeriod) {
            signInAward = attr.getNewUserSignInAward();
            first = true;
        }
        List<SignInDayVo> signinDetailVoList = Lists.newArrayList();
        int totalAward = 0;
        int days = isNewPeriod ? 0 : details.getRecordList().size();
        boolean today = false;

        if (!isNewPeriod && !details.getRecordList().isEmpty()) {
            for (SignIn.SignInRecordVo daySign : details.getRecordList()) {
                int day = daySign.getDay();
                int award = MapUtils.getInteger(signInAward, day, 0);
                totalAward += award;
                signinDetailVoList.add(new SignInDayVo(day, award, true));
                if (DateUtil.isSameDay(new Date(daySign.getDate()), now)) {
                    today = true;
                }
            }
        }
        for (int i = days + 1; i <= period; i++) {
            int award = MapUtils.getInteger(signInAward, i, 0);
            totalAward += award;
            signinDetailVoList.add(new SignInDayVo(i, award, false));
        }

        return new SignInDetailVo(days, totalAward, today, first, signinDetailVoList);
    }

    /**
     * 登录弹窗
     *
     * @param attr
     * @param uid
     * @param now
     * @param client
     * @return
     */

    public LoginTipVo loginTip(WzryTaskComponentAttr attr, long uid, Date now, Client client) {
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return new LoginTipVo(false, 0);
        }

        Optional<WzryTaskComponentAttr.TaskConfig> loginTaskOp = attr.getTaskConfigList()
                .stream().filter(i -> Objects.equals(i.getType(), TaskType.LONGIN.getCode())).findFirst();

        if (StringUtil.isBlank(client.getHdid()) || client.getApp() != ZhuiyaPbCommon.App.APP_YOMI) {
            throw new BusinessException(500, "设备异常，请刷新试试");
        }

        if (loginTaskOp.isEmpty()) {
            return new LoginTipVo(false, 0);
        }
        int award = 0;
        try {
            award = autoReleaseAward(attr, uid, loginTaskOp.get(), now, UUID.randomUUID().toString(), client);
        } catch (BusinessException e) {
            //只有第一次风控才弹窗提醒
            if (e.getCode() == 1005) {
                String groupCode = redisConfigManager.getGroupCode(attr.getActId());
                boolean first = actRedisDao.setNX(groupCode, makeKey(attr, "loginRisk_" + uid), "1");
                if (first) {
                    throw e;
                }
            }
            return new LoginTipVo(false, 0);
        }

        return new LoginTipVo(award > 0, award);
    }

    /**
     * 签到
     *
     * @param attr
     * @param uid
     * @param now
     * @param client
     * @return
     */

    public SignInVo signIn(WzryTaskComponentAttr attr, long uid, Date now, Client client) {
        if (StringUtil.isBlank(client.getHdid()) || client.getApp() != ZhuiyaPbCommon.App.APP_YOMI) {
            throw new BusinessException(500, "设备异常，请刷新试试");
        }
        if (attr.getSignInId() <= 0 || attr.getPeriod() <= 0) {
            throw new BusinessException(500, "暂无签到任务");
        }
        SignIn.SignInReq req = SignIn.SignInReq.newBuilder().setUid(uid)
                .setConfigId(attr.getSignInId())
                .setGreyTime(!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId()) ? now.getTime() : 0L)
                .setClient(client).build();
        SignIn.SignInResp signInResp = signInClient.signIn(req);
        if (signInResp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
            throw new BusinessException(signInResp.getCode(), signInResp.getMessage(), signInResp.getData());
        }
        SignIn.SignInRecordVo record = signInResp.getRecord();

        int day = record.getDay();

        Integer award = releaseSignIn(attr, uid, new Date(record.getDate()), record.getDay(), record.getFirst(), now, client);

        return new SignInVo(day, award);
    }

    /**
     * 发放签到奖励
     *
     * @param attr
     * @param uid
     * @param signDay
     * @param day
     * @param firstSignIn
     * @param now
     * @return
     */
    public Integer releaseSignIn(WzryTaskComponentAttr attr, long uid, Date signDay, int day, boolean firstSignIn, Date now, Client client) {
        Integer newSignInAward = attr.getNewUserSignInAward().get(day);
        Integer award = attr.getSignInAward().get(day);
        if (award == null && newSignInAward == null) {
            return 0;
        }

        award = firstSignIn && isNewUser(attr, uid, client.getHdid(), now) ? newSignInAward : award;
        if (award == null) {
            return 0;
        }
        String seq = makeKey(attr, "signIn_" + uid + "_" + DateUtil.format(signDay, DatePattern.PURE_DATE_PATTERN));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        boolean noExist = actRedisDao.setNX(groupCode, seq, "1", 24 * 60 * 60);
        if (!noExist) {
            log.info("releaseSignIn ignore@uid:{},signDay:{}", uid, signDay);
            return award;
        }
        doWelfarePiao(attr, uid, award, "签到", now, client);

        addStaticRecord(attr.getActId(), makeKey(attr, String.format(TASK_DAY_AMOUNT_SIGN, day)), uid, now);

        return award;
    }

    /**
     * 增加任务进度
     *
     * @param attr
     * @param uid
     * @param type
     * @param seq
     * @param time
     */
    public void addProgress(WzryTaskComponentAttr attr, long uid, int type, String seq, Date time) {
        Optional<WzryTaskComponentAttr.TaskConfig> taskOp = attr.getTaskConfigList().stream()
                .filter(i -> Objects.equals(i.getType(), type)).findFirst();
        if (taskOp.isEmpty()) {
            log.info("addProgress ignore@actId:{},uid:{},type:{},seq:{}", attr.getActId(), uid, type, seq);
            return;
        }
        WzryTaskComponentAttr.TaskConfig task = taskOp.get();
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String progressKey = getTaskProgressKey(attr, task, time, uid);
        int progress = Convert.toInt(actRedisDao.get(groupCode, progressKey));
        if (progress >= task.getGoal()) {
            log.info("addProgress ignore by progress@actId:{},uid:{},type:{},seq:{}", attr.getActId(), uid, type, seq);
            return;
        }
        boolean notDone = actRedisDao.setNX(groupCode, makeKey(attr, "task:" + seq), "1");
        if (!notDone) {
            log.info("addProgress ignore by seq done@actId:{},uid:{},type:{},seq:{}", attr.getActId(), uid, type, seq);
            return;
        }


        List<Long> results = actRedisDao.incrValueWithLimit(groupCode, progressKey, 1, task.getGoal(), false);
        if (!Objects.equals(results.get(0), NumberUtils.LONG_ONE)) {
            log.info("addProgress ignore by incr error@actId:{},uid:{},type:{},seq:{}", attr.getActId(), uid, type, seq);
            return;
        }
        String awardKey = getAwardTaskKey(attr, task, time, uid);
        long rs = actRedisDao.incrValue(groupCode, awardKey, 1);
        actRedisDao.setNX(groupCode, getStartTaskKey(attr, task, uid), progressKey);

        String staticKey = TaskType.WZRY_GAME.getCode() == type ? TASK_DAY_AMOUNT_GAME : TASK_DAY_AMOUNT_KAIHEI;
        addStaticRecord(attr.getActId(), makeKey(attr, staticKey), uid, time);

        log.info("addProgress done@actId:{},uid:{},type:{},seq:{},rs:{}", attr.getActId(), uid, type, seq, rs);
    }

    /**
     * 领取奖励
     *
     * @param attr
     * @param uid
     * @param type
     * @param now
     */
    public int drawAward(WzryTaskComponentAttr attr, long uid, int type, Date now, Client client) {
        Optional<WzryTaskComponentAttr.TaskConfig> taskConfigOp = attr.getTaskConfigList().stream().filter(i -> Objects.equals(i.getType(), type)).findFirst();
        if (taskConfigOp.isEmpty()) {
            throw new BusinessException(500, "参数异常");
        }

        WzryTaskComponentAttr.TaskConfig taskConfig = taskConfigOp.get();
        String awardTaskKey = getAwardTaskKey(attr, taskConfig, now, uid);
        String startTaskKey = getStartTaskKey(attr, taskConfig, uid);
        String taskProgressKey = getTaskProgressKey(attr, taskConfig, now, uid);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        List<String> taskAwards = actRedisDao.getRedisTemplate(groupCode).opsForValue().multiGet(List.of(awardTaskKey, startTaskKey));
        int awardCount = Convert.toInt(taskAwards.get(0));
        if (awardCount <= 0) {
            throw new BusinessException(500, "暂无可领取奖励");
        }
        boolean newUser = isNewUser(attr, uid, client.getHdid(), now);
        boolean firstPeriod = StringUtil.isBlank(taskAwards.get(1)) || taskProgressKey.equals(taskAwards.get(1));
        int award = newUser && firstPeriod ? taskConfig.getNewUserAward() : taskConfig.getAward();
        if (award <= 0) {
            throw new RuntimeException("配置异常");
        }

        risk(uid, attr.getRiskStrategyKey(), newUser, client);

        int totalAward = 0;
        for (int i = 0; i < awardCount; i++) {
            List<Long> results = actRedisDao.incrValueWithLimit(groupCode, awardTaskKey, -1, 0, false);
            if (!Objects.equals(results.get(0), NumberUtils.LONG_ONE)) {
                break;
            }
            totalAward += award;
            //发放奖励
            doWelfarePiao(attr, uid, award, taskConfig.getTaskDesc(), now, client);
        }

        if (totalAward <= 0) {
            throw new BusinessException(500, "暂无可领取奖励");
        }
        return totalAward;

    }

    /**
     * 发放任务奖励-自动
     *
     * @param attr
     * @param uid
     * @param taskConfig
     * @param now
     * @param seq
     * @param client
     * @return
     */
    private int autoReleaseAward(WzryTaskComponentAttr attr, long uid, TaskConfig taskConfig, Date now, String seq, Client client) {
        if (taskConfig == null) {
            log.info("autoReleaseAward ignore task empty@uid:{},seq:{}", uid, seq);
            return 0;
        }

        Integer type = taskConfig.getType();
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String startTaskKey = getStartTaskKey(attr, taskConfig, uid);
        String taskProgressKey = getTaskProgressKey(attr, taskConfig, now, uid);
        List<String> progressList = actRedisDao.getRedisTemplate(groupCode).opsForValue().multiGet(List.of(taskProgressKey, startTaskKey));
        int progress = Convert.toInt(progressList.get(0));
        if (progress >= taskConfig.getGoal()) {
            log.info("autoReleaseAward ignore progress@actId:{},uid:{},seq:{},type:{}", attr.getActId(), uid, seq, type);
            return 0;
        }
        //seq 去重
        boolean notDone = actRedisDao.setNX(groupCode, makeKey(attr, "taskAward:" + seq), "1");
        if (!notDone) {
            log.info("autoReleaseAward ignore by done@actId:{},uid:{},type:{},seq:{}", attr.getActId(), uid, type, seq);
            return 0;
        }

        boolean newUser = isNewUser(attr, uid, client.getHdid(), now);
        boolean firstPeriod = StringUtil.isBlank(progressList.get(1)) || taskProgressKey.equals(progressList.get(1));
        int award = newUser && firstPeriod ? taskConfig.getNewUserAward() : taskConfig.getAward();
        if (award <= 0) {
            log.info("autoReleaseAward ignore award zero @actId:{},uid:{},seq:{},type:{}", attr.getActId(), uid, seq, type);
            return 0;
        }
        risk(uid, attr.getRiskStrategyKey(), newUser, client);
        List<Long> results = actRedisDao.incrValueWithLimit(groupCode, taskProgressKey, 1, taskConfig.getGoal(), false);
        if (!Objects.equals(results.get(0), NumberUtils.LONG_ONE)) {
            log.info("autoReleaseAward ignore award zero @actId:{},uid:{},seq:{},type:{}", attr.getActId(), uid, seq, type);
            return 0;
        }
        //发放奖励
        doWelfarePiao(attr, uid, award, taskConfig.getTaskDesc(), now, client);
        actRedisDao.setNX(groupCode, startTaskKey, taskProgressKey);

        addStaticRecord(attr.getActId(), makeKey(attr, TASK_DAY_AMOUNT_LOGIN), uid, now);

        return award;

    }

    public void doWelfarePiao(WzryTaskComponentAttr attr, long uid, int count, String taskDesc, Date now, Client client) {
        String seq = UUID.randomUUID().toString();
        String time = DateUtil.format(now, DatePattern.NORM_DATETIME_PATTERN);

        Map<String, Object> issueExt = Maps.newHashMap();
        issueExt.put("desc", String.format(AWARD_DESC_FORMAT, taskDesc));
        Integer usedChannel = ZhuiyaClientUtils.getUsedChannel(client);
        if (usedChannel != null) {
            issueExt.put("usedChannel", usedChannel);
        }
        Map<String, String> ext = Map.of(Const.AWARD_ISSUE_EXT, JSON.toString(issueExt));
        hdztAwardServiceClient.doWelfareV2(time, BusiId.SKILL_CARD.getValue(), uid, attr.getPiaoTaskId(), count, attr.getPiaoPackageId(), seq, ext);

    }

    private boolean hasAward(WzryTaskComponentAttr attr, long uid, Date now) {
        List<String> keys = attr.getTaskConfigList().stream().filter(WzryTaskComponentAttr.TaskConfig::isShow).map(i -> getAwardTaskKey(attr, i, now, uid)).toList();
        if (CollectionUtils.isEmpty(keys)) {
            return false;
        }

        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        List<String> awardCountList = actRedisDao.getRedisTemplate(groupCode).opsForValue().multiGet(keys);
        for (String awardCount : awardCountList) {
            if (Convert.toInt(awardCount) > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 风控
     *
     * @param uid
     * @param riskStrategyKey
     * @param isNewUser
     * @param client
     */

    private void risk(long uid, String riskStrategyKey, boolean isNewUser, Client client) {

        if (StringUtil.isBlank(riskStrategyKey)) {
            return;
        }
        ZhuiyaRisk.RiskReq riskReq = ZhuiyaRisk.RiskReq.newBuilder().setUserId(uid).setStrategyKey(riskStrategyKey).setClient(ZhuiyaClientUtils.toRiskClient(client)).setUserLabel(isNewUser ? ZhuiyaRisk.UserLabel.NEW : ZhuiyaRisk.UserLabel.EXISTED).build();

        ZhuiyaRisk.RiskRsp riskRsp = zhuiwanRiskClient.getProxy().riskCheck(riskReq);
        if (riskRsp.getCode() != NumberUtils.INTEGER_ZERO) {
            log.warn("risk error@uid:{}{}", uid, riskRsp.getMessage());
            throw new BusinessException(500, "系统异常，请稍后重试");
        }
        ZhuiyaRisk.RiskResult riskResult = riskRsp.getRiskResult();
        if (riskResult != ZhuiyaRisk.RiskResult.RISK_RESULT_VALID) {
            log.warn("risk invalid@uid:{},riskStrategyKey:{}", uid, riskResult);
            throw new BusinessException(1005, riskRsp.getTipContent());
        }
    }

    private boolean isNewUseNotException(WzryTaskComponentAttr attr, long uid, String hdid, Date now) {
        try {
            return isNewUser(attr, uid, hdid, now);
        } catch (Exception e) {
            log.warn("isNewUseNotException uid:{},hdid:{}", uid, hdid, e);
        }
        return false;
    }

    /**
     * 新用户判断
     *
     * @param attr
     * @param uid
     * @param hdid
     * @return
     */
    private boolean isNewUser(WzryTaskComponentAttr attr, long uid, String hdid, Date now) {
        String key = makeKey(attr, "userNew:" + uid + ":" + hdid);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String isNewString = actRedisDao.get(groupCode, key);
        Integer newUserDay = attr.getNewUserDay();

        long firstLoginTime;
        if (StringUtil.isBlank(isNewString)) {
            LoginRecord.LoginRecordReq req = LoginRecord.LoginRecordReq.newBuilder().setUid(uid).setHdid(hdid)
                    .setApp(ZhuiyaPbCommon.App.APP_YOMI).build();
            LoginRecord.LoginRecordRsp loginRecordRsp = loginClient.queryLoginRecord(req);
            log.info("isNewUser info @req:{},rsp:{}", JsonUtils.serialize(req), JsonUtils.serialize(loginRecordRsp));
            if (loginRecordRsp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
                throw new BusinessException(500, "系统异常，请稍后重试");
            }
            LoginRecord.LoginRecordVo loginInfo = loginRecordRsp.getResult();
            if (loginInfo.getUidFirstLoginTime() == 0 || loginInfo.getDeviceFirstLoginTime() == 0) {
                throw new BusinessException(401, "登录数据异常,请重新登录");
            }

            firstLoginTime = Math.min(loginInfo.getUidFirstLoginTime(), loginInfo.getDeviceFirstLoginTime());
            actRedisDao.set(groupCode, key, String.valueOf(firstLoginTime), 24 * 60 * 60);
        } else {
            firstLoginTime = Long.parseLong(isNewString);
        }
        return DateUtil.betweenDay(new Date(firstLoginTime), now, true) < newUserDay;
    }

    private void addStaticRecord(long actId, String key, long uid, Date now) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            String dayCode = com.yy.gameecology.common.utils.DateUtil.format(now, com.yy.gameecology.common.utils.DateUtil.PATTERN_TYPE2);
            log.info("addStaticRecord actId:{},key:{},uid:{},now:{}", actId, key, uid, now);
            actRedisDao.hIncrByKey(getRedisGroupCode(actId), key, dayCode, 1);
        });
    }

    public Map<String, String> queryStaticReport(long actId, WzryTaskComponentAttr attr, String dayCode) {
        Map<String, String> result = Maps.newLinkedHashMap();

        String redisCode = getRedisGroupCode(actId);

        String loginTask = makeKey(attr, TASK_DAY_AMOUNT_LOGIN);
        result.put("登陆任务完成人数", Convert.toString(actRedisDao.hget(redisCode, loginTask, dayCode), "0"));

        String gameTask = makeKey(attr, TASK_DAY_AMOUNT_GAME);
        result.put("赏金赛任务完成人数", Convert.toString(actRedisDao.hget(redisCode, gameTask, dayCode), "0"));

        String sign1 = makeKey(attr, String.format(TASK_DAY_AMOUNT_SIGN, 1));
        result.put("签到1天完成人数", Convert.toString(actRedisDao.hget(redisCode, sign1, dayCode), "0"));

        String sign3 = makeKey(attr, String.format(TASK_DAY_AMOUNT_SIGN, 3));
        result.put("签到3天完成人数", Convert.toString(actRedisDao.hget(redisCode, sign3, dayCode), "0"));

        String sign5 = makeKey(attr, String.format(TASK_DAY_AMOUNT_SIGN, 5));
        result.put("签到5天完成人数", Convert.toString(actRedisDao.hget(redisCode, sign5, dayCode), "0"));

        String khTask = makeKey(attr, TASK_DAY_AMOUNT_KAIHEI);
        result.put("开黑完成人数", Convert.toString(actRedisDao.hget(redisCode, khTask, dayCode), "0"));


        return result;
    }


    /**
     * 任务进度key
     *
     * @param attr
     * @param taskConfig
     * @param time
     * @param uid
     * @return
     */
    private String getTaskProgressKey(WzryTaskComponentAttr attr, WzryTaskComponentAttr.TaskConfig taskConfig, Date time, long uid) {
        String taskDateString = getTaskDateKey(taskConfig.getPeriodType(), time);
        return makeKey(attr, String.format(TASK_PROGRESS_KEY_FORMAT, taskConfig.getType(), taskDateString, uid));
    }


    /**
     * 任务完成获得奖励次数key
     *
     * @param attr
     * @param taskConfig
     * @param time
     * @param uid
     * @return
     */
    private String getAwardTaskKey(WzryTaskComponentAttr attr, WzryTaskComponentAttr.TaskConfig taskConfig, Date time, long uid) {

        String taskDateString = getTaskDateKey(taskConfig.getPeriodType(), time);
        return makeKey(attr, String.format(TASK_AWARD_KEY_FORMAT, taskConfig.getType(), taskDateString, uid));
    }

    /**
     * 任务开始完成的进度key
     *
     * @param attr
     * @param taskConfig
     * @param uid
     * @return
     */
    private String getStartTaskKey(WzryTaskComponentAttr attr, WzryTaskComponentAttr.TaskConfig taskConfig, long uid) {
        return makeKey(attr, String.format(TASK_START_KEY_FORMAT, taskConfig.getType(), uid));
    }

    /**
     * 任务周期时间
     *
     * @param periodType
     * @param time
     * @return
     */
    private String getTaskDateKey(int periodType, Date time) {
        return Objects.equals(periodType, 2) ? DateUtil.beginOfWeek(time).toString(DatePattern.PURE_DATE_PATTERN) : String.valueOf(periodType);
    }


}
