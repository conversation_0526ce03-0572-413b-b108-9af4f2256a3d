package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.ActivityTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeStart;
import com.yy.gameecology.activity.bean.mq.ChannelFightEndEvent;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingHandle;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ChanelFightWallComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.common.constants.CommonConstants;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 乱斗墙组件
 * 需求地址：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/4xX96ppH_s/d6usL3ZffWXD50
 * <p>
 * 1. 监听交友乱斗玩法结束事件,根据每场乱斗的乱斗值排名(主播可以多次上榜,因此不能以主播id作为榜单member)
 * <p>
 * pb协议
 * 1. 主动请求(进房请求)
 * uri = 100023
 * opTarget = GetData
 * cmptId = 5065
 * actId / cmptIndex 以实际活动配置为主
 * <p>
 * 2. 响应数据(主动请求&变化时更新)
 * uri = 100024
 * retCode = 1 成功
 * retMsg = 失败时的错误信息
 * retContent =
 * <code>
 * {
 * "ranks":[
 * {
 * "rank":"排名",
 * "avatar":"主播头像",
 * "uid":"主播uid",
 * "nickname":"主播昵称",
 * "value":"乱斗值"
 * }
 * ],
 * "topOne":{
 * "avatar":"主播头像",
 * "uid":"主播uid",
 * "nickname":"主播昵称",
 * "value":"乱斗值",
 * "startTime":"展示开始时间,毫秒级时间戳",
 * "endTime":"展示结束时间,毫秒级时间戳",
 * "sysTime":"系统时间,毫秒级时间戳"
 * }
 * }
 * </code>
 * <p>
 * 3. 高光广播
 * uri = 100008
 * bannerId = 8
 * jsonData:{"nickname":"","avatar":"","value":"","svga":""}
 *
 * <AUTHOR>
 * @since 2023/5/23 14:44
 **/
@UseRedisStore
@Component
@Slf4j
@RestController
@RequestMapping("cmpt/channelFightWall")
public class ChanelFightWallComponent extends BaseActComponent<ChanelFightWallComponentAttr> implements ComponentRankingHandle<ChanelFightWallComponentAttr> {
    private static final String OP_TARGET = "GetData";
    private static final String TOP_ONE_CACHE_KEY = "topOne";
    private static final String CHANNEL_FIGHT_END_SEQ = "ChannelFightEndSeq";
    private static final int DELAY_SECOND = 10;
    private static final int THREE_SECOND = 3000;
    private static final String RANK_KEY = "rank";

    @Override
    public Long getComponentId() {
        return ComponentId.CHANNEL_FIGHT_WALL;
    }

    /**
     * 监听交友乱斗结束事件,上报榜单数据
     * {"charm":600,"compereUid":2837155741,"endTime":1684825790,"extraCharm":0,"extraRevenue":0,"extraSid":81886868,
     * "extraSsid":2768800432,"extraUid":2535568193,"gameId":"81886868:2395881380:81886868:2768800432:1684825180",
     * "receiveTimestamp":1684825791617,"revenue":2000,"serialNo":1684825180,"sid":81886868,"ssid":2395881380,
     * "startTime":1684825180,"timestamp":1684825791,"winning":1}
     **/
    @HdzjEventHandler(value = ChannelFightEndEvent.class, canRetry = true)
    public void onChannelFightEndEvent(ChannelFightEndEvent event, ChanelFightWallComponentAttr attr) {
        String key = makeKey(attr, CHANNEL_FIGHT_END_SEQ);
        long actId = attr.getActId();
        String groupCode = getRedisGroupCode(actId);
        if (actRedisDao.sIsMember(groupCode, key, event.getGameId())) {
            log.info("seq is handle {}", event.getGameId());
            return;
        }

        Secret secret = locker.lock(event.getGameId());
        if (secret == null) {
            log.info("lock not success ,gameId={}", event.getGameId());
            return;
        }

        log.info("handleChannelFightEndEvent event={}", JSON.toJSONString(event));
        List<Pair<Long, Long>> members = getRankMember(event, attr.isOnlyWinner());
        if (members.isEmpty()) {
            log.warn("not found rank member");
            return;
        }

        long eventTime = getEventTime(event, actId);
        long actEndTime = hdztRankingThriftClient.queryActivityCache(actId).getEndTime();
        if (eventTime > actEndTime) {
            log.info("act is end,eventTime={}", eventTime);
            return;
        }

        List<Pair<Long, Long>> handleMembers = new ArrayList<>();

        // index是用来保证同分，胜者排前
        // (actEndTime - eventTime - index) 会作为分值的小数部分
        for (int index = 0; index < members.size(); index++) {
            Pair<Long, Long> member = members.get(index);
            if (member.getValue() <= 0) {
                // 0分的数值就不记录了
                continue;
            }
            boolean result = addRank(attr, eventTime, member.getKey(), member.getValue(), (actEndTime - eventTime - index));
            if (result) {
                handleMembers.add(member);
            } else if (member.getValue() >= attr.getHighlightThreshold()) {
                RetryTool.withRetryCheck(actId, event.getGameId(), () -> broadcastHighlight(actId, member.getKey(), member.getValue(), attr.getBannerSvga()));
            }
        }
        actRedisDao.sAdd(groupCode, key, event.getGameId());

        handleMembers.forEach((member) -> onRankingScoreChanged(member, attr, eventTime));

        broadcastChannelFightWall(attr);
    }

    private long getEventTime(ChannelFightEndEvent event, long actId) {
        long eventTime = event.getTimestamp() * 1000;

        // 事件时间和收到的时间相差3秒，按收到的事件的时间算
        if (Math.abs(event.getReceiveTimestamp() - eventTime) >= THREE_SECOND) {
            eventTime = event.getReceiveTimestamp();
            log.info("onChannelFightEndEvent adjust time@actId:{},gameId:{}", actId, event.getGameId());
        }

        // 取虚拟时间
        if (!SysEvHelper.isDeploy() || commonService.isGrey(actId)) {
            eventTime = commonService.getNow(actId).getTime();
        }

        return eventTime;
    }

    private boolean addRank(ChanelFightWallComponentAttr attr, long eventTime, long uid, long score, long leftTime) {
        if (ftsBaseInfoBridgeClient.isHatCompere(uid)) {
            return false;
        }

        String rankKey = makeKey(attr, RANK_KEY + CommonConstants.GROUP_CHAR_SEPERATOR + DateUtil.format(new Date(eventTime), DateUtil.PATTERN_TYPE2));
        String groupCode = getRedisGroupCode(attr.getActId());
        long oldScore = actRedisDao.zscore(groupCode, rankKey, uid + "");
        if (score > oldScore) {
            // 新分值大于旧分值
            double newScore = Convert.toDouble(score + "." + leftTime);
            // zAdd会更新这个member的score
            actRedisDao.zAdd(groupCode, rankKey, uid + "", newScore);

            return true;
        } else {
            log.info("oldScore={},score={}", oldScore, score);
        }

        return false;
    }

    /**
     * Pair<String, Long> 左边是member：{uid}，右边是分值
     **/
    private List<Pair<Long, Long>> getRankMember(ChannelFightEndEvent event, boolean onlyWinner) {
        List<Pair<Long, Long>> members = new ArrayList<>();
        int winning = event.getWinning();
        if (winning == 0) {
            // 平局不上榜
            log.info("draw is not need to update ranking");
            return members;
        }

        final int one = 1, two = 2;
        if (onlyWinner) {
            // 只取胜者
            if (winning == one) {
                // 胜
                members.add(Pair.of(event.getCompereUid(), event.getRevenue() / 10));
            } else if (winning == two) {
                // 败
                members.add(Pair.of(event.getExtraUid(), event.getExtraRevenue() / 10));
            }
        } else {
            // 乱斗双方一起取
            // 将胜者放在前面，当同分并且有一方主动弃权时,胜者排在前面(利用榜单的先到排前机制)
            if (winning == one) {
                members.add(Pair.of(event.getCompereUid(), event.getRevenue() / 10));
                members.add(Pair.of(event.getExtraUid(), event.getExtraRevenue() / 10));
            } else {
                members.add(Pair.of(event.getExtraUid(), event.getExtraRevenue() / 10));
                members.add(Pair.of(event.getCompereUid(), event.getRevenue() / 10));
            }
        }

        return members;
    }

    private void onRankingScoreChanged(Pair<Long, Long> member, ChanelFightWallComponentAttr attr, long eventTime) {
        long actId = attr.getActId();
        long myScore = member.getValue();
        long uid = member.getKey();

        List<Rank> ranks = queryRank(attr, "", eventTime);
        boolean needBroadcastHighlight = myScore >= attr.getHighlightThreshold();
        Optional<Rank> myRankOptional = ranks.stream()
                .filter(rank -> Objects.equals(rank.getMember(), member.getKey() + "") && rank.getScore() == myScore)
                .findFirst();
        if (!myRankOptional.isPresent()) {
            if (needBroadcastHighlight) {
                broadcastHighlight(actId, uid, myScore, attr.getBannerSvga());
            }

            // 不在前10名,说明榜单展示没变化,可以不主动推
            return;
        }

        int myRank = myRankOptional.get().getRank();
        boolean needBroadcastTopOne = false;

        // 第一名,判断是否需要霸屏
        Date now = commonService.getNow(actId);
        TopOneBean topOneBean = getTopOneBean(attr, now);
        if (myRank == 1) {
            // 当前没有第一名,并且自己达到了霸屏的门槛
            if (topOneBean == null && myScore >= attr.getTopOneThreshold()) {
                needBroadcastTopOne = true;
                topOneBean = new TopOneBean();
            } else if (topOneBean != null && myScore > topOneBean.getValue()) {
                // 超过了当前的第一名
                needBroadcastTopOne = true;
            }

            if (needBroadcastTopOne) {
                UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, true);
                topOneBean.setAvatar(userBaseInfo.getLogo());
                topOneBean.setNickname(userBaseInfo.getNick());
                topOneBean.setUid(uid);
                topOneBean.setStartTime(now.getTime());
                topOneBean.setEndTime(DateUtil.addMinutes(now, attr.getTopOneShowDuration()).getTime());
                topOneBean.setRank(myRank);
                topOneBean.setValue(myScore);
                updateTopOneBean(attr, topOneBean);
            }
        }

        // 广播高光横幅
        if (needBroadcastHighlight) {
            broadcastHighlight(actId, uid, myScore, attr.getBannerSvga());
        }

        int delaySecond = 0;
        if (needBroadcastHighlight && needBroadcastTopOne) {
            // 高光并且同时是topOne,需要延迟
            delaySecond = DELAY_SECOND;
        }

        broadcast(delaySecond, ranks, topOneBean, now, attr);
    }

    @HdzjEventHandler(value = ActivityTimeEnd.class, canRetry = true)
    public void onActivityEnd(ActivityTimeEnd event, ChanelFightWallComponentAttr attr) {
        RetryTool.withRetryCheck(attr.getActId(), event.getSeq(), () -> broadcastChannelFightWall(attr));
    }

    @HdzjEventHandler(value = PhaseTimeStart.class, canRetry = true)
    public void onPhaseTimeStart(PhaseTimeStart event, ChanelFightWallComponentAttr attr) {
        if (attr.getRankId() != event.getRankId() || attr.getPhaseId() != event.getPhaseId()) {
            return;
        }
        
        RetryTool.withRetryCheck(attr.getActId(), event.getSeq(), () -> broadcastChannelFightWall(attr));
    }

    public void broadcastChannelFightWall(ChanelFightWallComponentAttr attr) {
        long actId = attr.getActId();
        CommonPBOperateRequest request = new CommonPBOperateRequest();
        request.setActId(actId);
        request.setCmptIndex(attr.getCmptUseInx());
        request.setCmptId(getComponentId());
        request.setOpTarget(OP_TARGET);
        CommonPBOperateResp resp = commonOperatePbRequest(request);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonOperateRespUri_VALUE)
                .setCommonOperateResp(resp.toPb(attr))
                .build();

        broadCastHelpService.broadcast(attr.getActId(), Arrays.asList(BusiId.MAKE_FRIEND.getValue()), msg);
        log.info("broadcastChannelFightWall with actId:{}, msg:{}", actId, msg);
    }

    private void broadcast(int delaySecond, List<Rank> ranks, TopOneBean topOneBean, Date now, ChanelFightWallComponentAttr attr) {
        ActivityInfoVo actInfo = hdztRankingThriftClient.queryActivityInfo(attr.getActId());
        String content = buildContent(actInfo, ranks, topOneBean, now.getTime());
        CommonPBOperateResp resp = new CommonPBOperateResp(1, content, "success");
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonOperateRespUri_VALUE)
                .setCommonOperateResp(resp.toPb(attr))
                .build();

        if (delaySecond > 0) {
            Const.EXECUTOR_DELAY_GENERAL.schedule(
                    () -> broadCastHelpService.broadcast(attr.getActId(), Arrays.asList(BusiId.MAKE_FRIEND.getValue()), msg)
                    , delaySecond, TimeUnit.SECONDS);
        } else {
            broadCastHelpService.broadcast(attr.getActId(), Arrays.asList(BusiId.MAKE_FRIEND.getValue()), msg);
        }
        log.info("broadcast msg={}", JsonFormat.printToString(msg));
    }

    private TopOneBean getTopOneBean(ChanelFightWallComponentAttr attr, Date now) {
        String topOne = makeKey(attr, TOP_ONE_CACHE_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        String json = actRedisDao.get(groupCode, topOne);
        if (StringUtils.isEmpty(json)) {
            return null;
        }

        // 应对模拟时间
        TopOneBean topOneBean = JSON.parseObject(json, TopOneBean.class);
        if (topOneBean.getEndTime() <= now.getTime()) {
            actRedisDao.del(groupCode, topOne);
            return null;
        }

        return topOneBean;
    }

    private void updateTopOneBean(ChanelFightWallComponentAttr attr, TopOneBean topOneBean) {
        String topOne = makeKey(attr, TOP_ONE_CACHE_KEY);
        actRedisDao.set(getRedisGroupCode(attr.getActId()), topOne, JSON.toJSONString(topOneBean), attr.getTopOneShowDuration() * 60L);
        log.info("updateTopOneBean,bean={}", JSON.toJSONString(topOneBean));
    }

    private String buildContent(ActivityInfoVo actInfo, List<Rank> ranks, TopOneBean topOneBean, long now) {
        Map<String, Object> contentMap = Maps.newHashMap();
        if (topOneBean != null) {
            topOneBean.setSysTime(now);
            contentMap.put("topOne", topOneBean);
        }

        List<RankBean> rankBeanList = new ArrayList<>();
        List<Long> uids = ranks.stream().map(Rank::getMember).map(Long::valueOf).distinct().collect(Collectors.toList());
        Map<Long, UserBaseInfo> userBaseInfoMap = commonService.batchGetUserInfos(uids, true);
        ranks.forEach(rank -> {
            RankBean rankBean = new RankBean();
            rankBean.setRank(rank.getRank());
            rankBean.setValue(rank.getScore());
            rankBean.setUid(Convert.toLong(rank.getMember(), 0));
            rankBean.setAvatar(userBaseInfoMap.get(rankBean.getUid()).getLogo());
            rankBean.setNickname(userBaseInfoMap.get(rankBean.getUid()).getNick());
            rankBeanList.add(rankBean);
        });
        contentMap.put("ranks", rankBeanList);
        contentMap.put("actBeginTime", actInfo.getBeginTime());
        contentMap.put("actEndTime", actInfo.getEndTime());
        contentMap.put("actBeginTimeShow", actInfo.getBeginTimeShow());
        contentMap.put("actEndTimeShow", actInfo.getEndTimeShow());
        contentMap.put("actCurrentTime", actInfo.getCurrentTime());
        return JSON.toJSONString(contentMap);
    }

    private void broadcastHighlight(long actId, long uid, long score, String bannerUrl) {
        Map<String, String> extMap = Maps.newHashMap();
        UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, true);
        extMap.put("nickname", userBaseInfo.getNick());
        extMap.put("avatar", StringUtil.isNotBlank(userBaseInfo.getHdLogo()) ? userBaseInfo.getHdLogo() : userBaseInfo.getLogo());
        extMap.put("value", score + "");
        extMap.put("svgaUrl", bannerUrl);
        extMap.put("uid", uid + "");

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(8).setUserNick(userBaseInfo.getNick())
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        broadCastHelpService.broadcast(actId, Arrays.asList(BusiId.MAKE_FRIEND.getValue()), bannerBroMsg);
        log.info("broadcastHighlight,msg={}", JsonFormat.printToString(bannerBroMsg));
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        String optTarget = request.getOpTarget();
        if (!Objects.equals(optTarget, OP_TARGET)) {
            log.info("not getData opt");
            return null;
        }

        long actId = request.getActId();
        Date now = commonService.getNow(actId);
        ChanelFightWallComponentAttr attr = getComponentAttr(actId, request.getCmptIndex());
        List<Rank> ranks = queryRank(attr, StringUtils.EMPTY, now.getTime());
        TopOneBean topOneBean = getTopOneBean(attr, now);
        ActivityInfoVo actInfo = hdztRankingThriftClient.queryActivityInfo(actId);

        String content = buildContent(actInfo, ranks, topOneBean, now.getTime());

        return new CommonPBOperateResp(1, content, "success");
    }

    private List<Rank> queryRank(ChanelFightWallComponentAttr attr, String date, long time) {
        if (StringUtils.isEmpty(date)) {
            date = DateUtil.format(new Date(time), DateUtil.PATTERN_TYPE2);
        }

        if (date.compareTo(attr.getLastActDate()) > 0) {
            date = attr.getLastActDate();
        }
        String rankKey = makeKey(attr, RANK_KEY + CommonConstants.GROUP_CHAR_SEPERATOR + date);
        String groupCode = getRedisGroupCode(attr.getActId());

        Set<ZSetOperations.TypedTuple<String>> set = actRedisDao.zrevRangeByScoreWithScore(groupCode, rankKey, 0, Long.MAX_VALUE, attr.getRankCount());
        List<Rank> ranks = new ArrayList<>();
        int index = 1;
        for (ZSetOperations.TypedTuple<String> tuple : set) {
            Rank rank = new Rank();
            rank.setRank(index);
            rank.setMember(tuple.getValue());
            rank.setScore(tuple.getScore().longValue());
            index++;
            ranks.add(rank);
        }

        return ranks;
    }

    /**
     * 查询榜单
     **/
    @Override
    public List<Rank> queryRank(ChanelFightWallComponentAttr attr, RankingInfo rankingInfo, long rankId, long phaseId, String dateStr, long count, String pointedMember, Map<String, String> ext) {
        return queryRank(attr, dateStr, commonService.getNow(attr.getActId()).getTime());
    }

    @Data
    private static class TopOneBean extends RankBean {
        private long startTime;
        private long endTime;
        private long sysTime;
    }

    @Data
    private static class RankBean {
        private String avatar;
        private long uid;
        private String nickname;
        private long value;
        private int rank;
    }

    @RequestMapping("test")
    public Response<String> test(long myUid, long myScore, long actId, int componentIndex) {
        if (SysEvHelper.isDeploy()) {
            return Response.ok("P");
        }
        ChanelFightWallComponentAttr attr = getComponentAttr(actId, componentIndex);
        if (attr == null) {
            return Response.ok("没有找到配置");
        }

        Random random = new Random();

        String json = "{\"charm\":20778,\"compereUid\":2908899211,\"endTime\":1684895565,\"extraCharm\":12488,\"extraRevenue\":113500,\"extraSid\":1457673797,\"extraSsid\":2813276625,\"extraUid\":67833005,\"gameId\":\"5180550:2810030400:1457673797:2813276625:1684894955\",\"receiveTimestamp\":1684895567091,\"revenue\":143100,\"serialNo\":1684894955,\"sid\":5180550,\"ssid\":2810030400,\"startTime\":1684894955,\"timestamp\":1684895567,\"winning\":1}";
        ChannelFightEndEvent event = JSON.parseObject(json, ChannelFightEndEvent.class);
        event.setGameId(UUID.randomUUID().toString());
        event.setSerialNo(System.currentTimeMillis() / 1000);
        event.setRevenue(myScore);
        event.setCompereUid(myUid);
        event.setExtraUid(event.getExtraUid() + random.nextInt(100000));

        onChannelFightEndEvent(event, attr);

        return Response.ok("Success");
    }
}
