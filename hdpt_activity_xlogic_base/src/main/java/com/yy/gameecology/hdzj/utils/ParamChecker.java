package com.yy.gameecology.hdzj.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/2/21 11:10
 **/
public class ParamChecker {
    public static void checkParam(String json) {
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            checkObjectParam(jsonObject, 1);
        } catch (Exception ex) {
            try {
                JSONArray array = JSON.parseArray(json);
                for (Object o : array) {
                    if (o instanceof JSONObject) {
                        checkObjectParam((JSONObject) o, 1);
                    }
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("输入字符串非JSON格式:" + json);
            }
        }
    }

    /**
     * 最多嵌套3层
     **/
    private static void checkObjectParam(JSONObject jsonObject, int level) {
        if (jsonObject == null || jsonObject.size() == 0) {
            return;
        }
        final int three = 3;
        if (level > three) {
            throw new IllegalArgumentException("参数嵌套不能超过3层");
        }

        for (String key : jsonObject.keySet()) {
            Object innerValue = jsonObject.get(key);
            if (innerValue == null) {
                continue;
            }

            // BT
            if (isBasicType(innerValue.getClass())) {
                continue;
            }

            // OT
            if (innerValue instanceof JSONObject) {
                checkObjectParam((JSONObject) innerValue, level + 1);
                continue;
            }

            // List
            if (isCollectionType(innerValue.getClass())) {
                JSONArray array = JSON.parseArray(JSON.toJSONString(innerValue));
                checkArrayParam(array, level);
            }
        }
    }

    private static Set<Class<?>> basicTypeSet = Sets.newHashSet(Byte.class, Short.class, Integer.class, Long.class
            , Float.class, Double.class, Character.class, Boolean.class, String.class, Date.class);

    private static boolean isBasicType(Class<?> clz) {
        return clz.isPrimitive() || basicTypeSet.contains(clz);
    }

    private static boolean isCollectionType(Class<?> clz) {
        return List.class.isAssignableFrom(clz) || Set.class.isAssignableFrom(clz);
    }

    /**
     * List
     **/
    private static void checkArrayParam(JSONArray array, int level) {
        if (array == null || array.isEmpty()) {
            return;
        }
        final int three = 3;
        if (level > three) {
            throw new IllegalArgumentException("参数嵌套不能超过3层");
        }
        for (int index = 0; index < array.size(); index++) {
            Object arrayContent = array.get(index);
            if (arrayContent == null) {
                continue;
            }
            if (isBasicType(arrayContent.getClass())) {
                continue;
            }

            // List<List>
            if (isCollectionType(arrayContent.getClass())) {
                checkArrayParam(JSON.parseArray(JSON.toJSONString(arrayContent)), level + 1);
            }

            // List<OT>
            if (arrayContent instanceof JSONObject) {
                checkSimpleObject((JSONObject) arrayContent);
            }
        }
    }

    /**
     * List<OT> OT的所有属性都只能是简单对象
     **/
    private static void checkSimpleObject(JSONObject jsonObject) {
        for (String key : jsonObject.keySet()) {
            Object subObject = jsonObject.get(key);
            if (subObject == null) {
                continue;
            }
            if (isBasicType(subObject.getClass())) {
                continue;
            }

            throw new IllegalArgumentException("列表不能包含拥有复杂属性的对象");
        }
    }
}
