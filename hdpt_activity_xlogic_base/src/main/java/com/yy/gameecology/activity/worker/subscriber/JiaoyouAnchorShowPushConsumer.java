package com.yy.gameecology.activity.worker.subscriber;

import com.yy.gameecology.activity.bean.StartShowEvent;
import com.yy.gameecology.activity.bean.mq.FriendPushLiveNotifyEvent;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import com.yy.gameecology.hdzj.element.history.AnchorStartBannerComponent;
import com.yy.thrift.hdztranking.BusiId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 交友道具消费事件
 *
 * <AUTHOR> 2021/8/26
 */
@Component
public class JiaoyouAnchorShowPushConsumer {

    private static final Logger log = LoggerFactory.getLogger(JiaoyouAnchorShowPushConsumer.class);

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    private static final String EVENT_SEQ_KEY = "jiaoyou_anchor_show_push_%s:%s";
    /**
     * 过期时间：2天，单位：秒
     */
    private static final long EXPIRE_SEC = 2 * 24 * 60 * 60;

    @Autowired
    private AnchorStartBannerComponent startBannerComponent;

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    public void handle(FriendPushLiveNotifyEvent pushLiveNotifyEvent) {

        if (pushLiveNotifyEvent == null) {
            log.info("jiaoyouAnchorShowPush ignore.event is null");
            return;
        }

        String seq = pushLiveNotifyEvent.getSeqId();
        String seqKey = String.format(EVENT_SEQ_KEY, SysEvHelper.getGroup(), seq);
        if (!actRedisDao.setNX(redisConfigManager.temp_act_group, seqKey, StringUtil.ONE, EXPIRE_SEC)) {
            log.warn("jiaoyouAnchorShowPush ignore. seq double,seq:{}", seq);
            return;
        }
        startBannerComponent.notify(pushLiveNotifyEvent.getUid(), BusiId.MAKE_FRIEND);

        StartShowEvent startShowEvent = new StartShowEvent(seq, BusiId.MAKE_FRIEND, pushLiveNotifyEvent.getUid(), new Date(pushLiveNotifyEvent.getNotifyTime() * 1000));

        try {
            hdzjEventDispatcher.notify(startShowEvent);
        } catch (Throwable e) {
            log.error("[handleMessage notify] err message:{} startShowEvent:{}", e.getMessage(), startShowEvent, e);
        }

        log.info("jiaoyouAnchorShowPush ok. seq:{}", seq);
    }

}
