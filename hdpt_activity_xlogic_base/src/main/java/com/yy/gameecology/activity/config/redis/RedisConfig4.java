package com.yy.gameecology.activity.config.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.Serializable;


@Configuration
public class RedisConfig4 extends BaseRedisConfig {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /* ##############################  gameecology redis  ############################# */

    public RedisConfig4(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        super(builderCustomizers);
    }

    @Bean
    @ConfigurationProperties("gameecology.group4.redis")
    public RedisProperties redisGroup4Properties() {
        return new RedisProperties();
    }

    @Bean
    public RedisConnectionFactory redisGroup4ConnectionFactory(RedisProperties redisGroup4Properties) {
        return new LettuceConnectionFactory(getRedisConfig(redisGroup4Properties), getClientConfig(redisGroup4Properties));
    }

    @Bean
    public StringRedisTemplate stringRedisGroup4Template(RedisConnectionFactory redisGroup4ConnectionFactory) {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(redisGroup4ConnectionFactory);
        return stringRedisTemplate;
    }

    @Bean
    public RedisTemplate<Serializable, Serializable> redisGroup4Template(RedisConnectionFactory redisGroup4ConnectionFactory) {
        RedisTemplate<Serializable, Serializable> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisGroup4ConnectionFactory);
        return redisTemplate;
    }

    @Bean
    public RedissonClient redissonClient4(RedisProperties redisGroup4Properties) {
        Config config = getRedissonConfig(redisGroup4Properties);
        config.setCheckLockSyncedSlaves(false);
        log.info("Creating RedissonClient for group4 with config: host={}, port={}, database={}",
                redisGroup4Properties.getHost(),
                redisGroup4Properties.getPort(),
                redisGroup4Properties.getDatabase());
        return Redisson.create(config);
    }

}
