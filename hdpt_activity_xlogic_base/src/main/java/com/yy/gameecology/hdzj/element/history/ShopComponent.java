package com.yy.gameecology.hdzj.element.history;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.AnchorStartShowEvent;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.yrpc.CurrencyClient;
import com.yy.gameecology.activity.exception.BadConfigurationException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ShopComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * @Author: CXZ
 * @Desciption: 商城
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
@UseRedisStore
@Component
public class ShopComponent extends BaseActComponent<ShopComponentAttr> {
    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private CurrencyClient currencyClient;

    private static final int NOTIFY_FREQUENCY_ONCE = 1;

    private static final int NOTIFY_FREQUENCY_DAILY_ONCE = 2;

    private static final String EXCHANGE_COMMODITY_LUA = "exchange_commodity.lua";

    /**
     * 用户钱包hash key
     */
    public static final String USER_WALLET_KEY = "user_wallet:%s";
    /**
     * 用户购买记录
     */
    public static final String USER_BUY_RECORD_KEY = "user_bug_record:%s";

    /**
     * 限制商品的购买数量记录
     */
    public static final String LIMIT_RECORD_KEY = "limit_record";

    /**
     * 提醒弹窗标识
     **/
    private final static String FIRST_KEY = "first_key";

    /**
     * 虚拟资产系统定的自身业务ID - 具体仔细 关啟华
     * TODO::本来应该是 1， 但这里故意给个无效值 -1， 接入虚拟资产系统后还未经过严格测试，后面若哪位同学要用，请确认后改成 1 - added by guoliping / 2024-01-19
     */
    private final static int CURRENCY_BUSI_ID = -1;

    private Map<String, Releaser> releaserMap = Maps.newHashMap();

    @Override
    public Long getComponentId() {
        return ComponentId.SHOP;
    }

    /**
     * 用户上麦
     **/
    @HdzjEventHandler(value = AnchorStartShowEvent.class, canRetry = true)
    public void onAnchorStartShow(AnchorStartShowEvent event, ShopComponentAttr attr) {
        if (!attr.isOpenNotify()) {
            return;
        }
        // 业务不同
        if (attr.getBusiId() != event.getBusiId()) {
            return;
        }

        long actId = attr.getActId();
        long uid = event.getUid();
        String day = commonService.getNowDateTime(actId).format(DateUtil.YYYY_MM_DD);
        if (CollectionUtils.isNotEmpty(attr.getNotifyDays()) && !attr.getNotifyDays().contains(day)) {
            log.info("onAnchorStartShow ignore limit day@uid:{} actId:{} index:{} day:{}", uid, actId, attr.getCmptUseInx(), day);
            return;
        }

        // 检查账户是否存在余额
        JSONObject wallet = getWallet(attr, uid);
        long totalNum = 0;
        for (Object value : wallet.values()) {
            totalNum += Convert.toLong(value, 0);
        }
        if (totalNum <= 0) {
            log.info("onAnchorStartShow ignore totalNum={},uid={}", totalNum, uid);
            return;
        }

        // 频率限制
        int frequency = attr.getNotifyFrequency();
        if (frequency == NOTIFY_FREQUENCY_ONCE || frequency == NOTIFY_FREQUENCY_DAILY_ONCE) {
            String key = frequency == NOTIFY_FREQUENCY_ONCE ? makeKey(attr, FIRST_KEY) : makeKey(attr, FIRST_KEY + ":" + day);
            String groupCode = redisConfigManager.getGroupCode(actId);
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
            if (!first) {
                log.info("onAnchorStartShow ignore not first@uid:{} actId:{} index:{} frequency:{} day:{}", uid, actId, attr.getCmptUseInx(), frequency, day);
                return;
            }
        }

        commonBroadCastService.commonNoticeUnicast(actId, attr.getNotifyType(), attr.getNotifyValue(), "", uid);
    }


    /**
     * 商城兑换
     *
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @param id         5活动已经结束 4 活动未开始 3用户自己导致的限制（购买过） 2是余额不足，1限制购买，0成功
     */
    public Response exchange(long actId, long cmptUseInx, long uid, String id) {

        ShopComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
        if (activityInfoVo == null) {
            log.error("exchange error@not find activityInfoVo,actId:{}", actId);
            return exchangeFail(4, "商城未开启，敬请期待。", attr);
        }


        Date now = commonService.getNow(actId);

        long starTime = Optional.ofNullable(attr.getStartDate()).map(Date::getTime).orElse(activityInfoVo.getBeginTime());
        long endTime = Optional.ofNullable(attr.getEndDate()).map(Date::getTime).orElse(activityInfoVo.getEndTime());

        if (starTime > now.getTime()) {
            return exchangeFail(4, "商城未开启，敬请期待。", attr);
        }
        if (endTime < now.getTime()) {
            return exchangeFail(5, "活动已结束。", attr);
        }


        ShopComponentAttr.Commodity commodity = attr.getCommodityMap().get(id);
        //检查商品配置
        checkCommodityConfig(commodity);

        if (commodity.getStatus() != 1) {
            return exchangeFail(1, "购买数量已达到限制额。", attr);
        }

        //扣费
        int exchangeResult = exchangeCommodity(uid, commodity, now, attr);

        String seq = UUID.randomUUID().toString();
        //2是余额不足，1限制购买，0是可以购买
        if (exchangeResult == 0) {
            //发放商品和记录
            if (StringUtils.isBlank(commodity.getReleaseType())) {
                Map<Long, Integer> packageIds = Maps.newHashMap(Collections.singletonMap(commodity.getPackageId(), commodity.getCount()));
                Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap(Collections.singletonMap(attr.getTaskId(), packageIds));
                hdztAwardServiceClient.doBatchWelfare(uid, taskPackageIds, DateUtil.getNowYyyyMMddHHmmss(), 3, Maps.newHashMap());
            } else {
                Releaser releaser = releaserMap.get(commodity.getReleaseType().trim());
                releaser.release(uid, commodity, now, seq, attr);
            }
            addUserPurchaseRecord(uid, commodity, now, seq, attr);
            log.info("exchange success done@uid:{} cmptUseInx:{} id:{}", uid, cmptUseInx, id);

            JSONObject commodityBase = new JSONObject();
            commodityBase.put("id", commodity.getId());
            commodityBase.put("name", commodity.getName());
            commodityBase.put("icon", commodity.getIcon());
            commodityBase.put("type", commodity.getType());
            return Response.success(commodityBase);
        } else {
            log.info("exchange fail@uid:{} cmptUseInx:{} id:{} exchangeResult:{}", uid, cmptUseInx, id, exchangeResult);
            return exchangeFail(exchangeResult, exchangeResult == 2 ? "余额不足" : "购买数量已达到限制额", attr);
        }
    }

    /**
     * 兑换错误返回
     *
     * @param code
     * @param message
     * @param attr
     * @param <T>
     * @return
     */
    private <T> Response<T> exchangeFail(int code, String message, ShopComponentAttr attr) {
        return Response.fail(code, Convert.toString(attr.getTipMsgMap().get("exchange-" + code), message));
    }


    /**
     * 商品检查
     *
     * @param commodity
     * @return
     */
    private boolean checkCommodityConfig(ShopComponentAttr.Commodity commodity) {
        if (commodity == null) {
            throw new ParameterException("id error.");
        }
        //检查发放配置
        boolean canRelease = (StringUtils.isBlank(commodity.getReleaseType()) && releaserMap.containsKey(commodity.getReleaseType()))
                || (StringUtils.isBlank(commodity.getReleaseType()) && commodity.getPackageId() > 0);
        if (!canRelease) {
            throw new BadConfigurationException("commodity release error,config:" + JSON.toJSONString(commodity));
        }
        return true;
    }

    /**
     * 增加用户钱币
     *
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @param currency
     * @param count
     */
    public void addUserCurrency(long actId, long cmptUseInx, long uid, String currency, int count) {
        ShopComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr.getPayWay() == ShopComponentAttr.PAY_WAY_SELF) {
            Assert.notNull(attr, "not find attr by cmptUseInx = " + cmptUseInx);
            Assert.isTrue(attr.getCurrencyMap().containsKey(currency), "currency error,currency=" + currency);
            Assert.isTrue(count > 0, "count error,count=" + count);
            String walletKey = makeKey(attr, String.format(USER_WALLET_KEY, uid));
            String groupCode = redisConfigManager.getGroupCode(actId);
            actRedisDao.hIncrByKey(groupCode, walletKey, currency, count);
        }
    }

    /**
     * 增加购买记录
     *
     * @param uid
     * @param commodity
     * @param date
     * @param seq
     * @param attr
     */
    private void addUserPurchaseRecord(long uid, ShopComponentAttr.Commodity commodity, Date date, String seq, ShopComponentAttr attr) {
        String key = makeKey(attr, String.format(USER_BUY_RECORD_KEY, uid));
        PurchaseRecord record = new PurchaseRecord(uid, attr.getActId(), commodity.getId(), commodity.getName(),
                BusiId.findByValue((int) attr.getBusiId()), seq, date.getTime(), commodity.getCount());
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        actRedisDao.getRedisTemplate(groupCode).opsForList().leftPush(key, JSON.toJSONString(record));
    }

    /**
     * 查询很购买记录
     *
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @param count
     * @return
     */
    public List<JSONObject> queryUserPurchaseRecord(long actId, long cmptUseInx, long uid, Integer count) {
        ShopComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);
        count = Convert.toInt(count, Integer.MAX_VALUE);
        if (count <= 0) {
            return Lists.newArrayList();
        }
        String key = makeKey(attr, String.format(USER_BUY_RECORD_KEY, uid));
        String groupCode = redisConfigManager.getGroupCode(actId);
        List<String> recordStringList = actRedisDao.getRedisTemplate(groupCode).opsForList().range(key, 0, count - 1);
        if (recordStringList.isEmpty()) {
            return Lists.newArrayList();
        }
        String json = "[" + StringUtils.join(recordStringList, ",") + "]";
        List<PurchaseRecord> records = JSONObject.parseArray(json, PurchaseRecord.class);

        return records.stream().map(record -> {
            JSONObject item = new JSONObject();
            item.put("id", record.getCommodityId());
            item.put("time", record.getCreateTime());
            item.put("name", record.getName());
            return item;
        }).collect(Collectors.toList());
    }


    /**
     * 查询用户的商城
     *
     * @param actId
     * @param cmptUseInx
     * @param uid
     * @return
     */
    public Response queryUserInfo(long actId, long cmptUseInx, long uid) {

        ShopComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);

        Date now = commonService.getNow(actId);

        Date starShowDate = attr.getStartShowDate();
        if (starShowDate != null) {
            if (starShowDate.after(now)) {
                return Response.fail(2, "商城未开启，敬请期待。");
            }
        }

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
        if (activityInfoVo == null) {
            log.error("queryUserInfo error@not find activityInfoVo,actId:{}", actId);
            return Response.fail(2, "商城未开启，敬请期待。");
        }

        long starTime = Optional.ofNullable(attr.getStartDate()).map(Date::getTime).orElse(activityInfoVo.getBeginTime());
        long endTime = Optional.ofNullable(attr.getEndDate()).map(Date::getTime).orElse(activityInfoVo.getEndTime());
        //商城状态：4未开始，5已结束
        int shopStatus = starTime > now.getTime() ? 4 : endTime < now.getTime() ? 5 : 0;

        JSONObject data = new JSONObject();

        UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
        data.put("logo", userInfo.getLogo());
        data.put("nick", userInfo.getNick());
        data.put("shopName", attr.getShopName());

        // 获取用户钱包
        String groupCode = redisConfigManager.getGroupCode(actId);
        JSONObject wallet = getWallet(attr, uid);
        data.put("wallet", wallet);

        //查询合成礼物的剩余数量和兑换信息信息

        List<ShopComponentAttr.Commodity> commodities = Lists.newArrayList(attr.getCommodityMap().values());

        //批量查找商品限购信息，减少redis请求
        Map<String, Integer> limitKeyMap = commodities.stream()
                .map(commodity -> getLimitKeyMap(commodity, now, uid).entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (k1, k2) -> k1));

        String limitRecordKey = makeKey(attr, String.format(LIMIT_RECORD_KEY, uid));

        List<String> limitKeys = Lists.newArrayList(limitKeyMap.keySet());
        List<Object> limitValues = limitKeys.isEmpty() ? Lists.newArrayList() :
                actRedisDao.hmGet(groupCode, limitRecordKey, MyListUtils.toObjectList(limitKeys));

        Map<String, Integer> limitMap = IntStream.range(0, limitKeys.size()).mapToObj(Integer::new)
                .collect(Collectors.toMap(i -> limitKeys.get(i), i -> Convert.toInt(limitValues.get(i))));

        //生产商品信息列表
        List<JSONObject> awardList = commodities.stream()
                //0不展示
                .filter(commodity -> commodity.getStatus() != 0)
                .map(commodity -> {

                    JSONObject item = new JSONObject();

                    Map<String, Integer> commodityLimitMap = getLimitKeyMap(commodity, now, uid);

                    //限量展示
                    Optional<Integer> showRemain =
                            commodityLimitMap.entrySet().stream()
                                    .filter(entry -> entry.getKey().split("-")[0].equals(attr.getRemainShowKey()))
                                    .map(entry -> entry.getValue() - limitMap.get(entry.getKey()))
                                    .findFirst();

                    // 5活动已经结束 4 活动未开始 3用户自己导致的限制（购买过） 2是余额不足，1限制购买，0是可以购买
                    int buyStatus = shopStatus;

                    //商品状态限制，是为了线上紧急控制
                    if (buyStatus == 0 && commodity.getStatus() != 1) {
                        buyStatus = 1;
                        showRemain = showRemain.isPresent() ? Optional.of(0) : showRemain;
                    }

                    //判断限购
                    if (buyStatus == 0) {
                        //最小限量
                        OptionalInt minRemain =
                                commodityLimitMap.entrySet().stream()
                                        .map(entry -> entry.getValue() - limitMap.get(entry.getKey()))
                                        .mapToInt(Integer::intValue).min();


                        if (minRemain.isPresent() && minRemain.getAsInt() <= 0) {
                            //用户限量
                            OptionalInt userRemain =
                                    commodityLimitMap.entrySet().stream()
                                            .filter(entry -> entry.getKey().startsWith("user"))
                                            .map(entry -> entry.getValue() - limitMap.get(entry.getKey()))
                                            .mapToInt(Integer::intValue).min();
                            //是用户行为限购
                            buyStatus = userRemain.isPresent() && userRemain.getAsInt() <= 0 ? 3 : 1;
                        }

                    }
                    //没有限购要检查余额
                    if (buyStatus == 0) {
                        //钱包小于价格的货币数量 大于0是不能购买
                        long insufficientCount = commodity.getPrice().entrySet().stream()
                                .filter(currency -> Convert.toInt(currency.getValue()) < currency.getValue())
                                .count();
                        //余额不足
                        buyStatus = insufficientCount > 0 ? 2 : 0;
                    }

                    item.put("id", commodity.getId());
                    item.put("name", commodity.getName());
                    item.put("price", commodity.getPrice());
                    item.put("icon", commodity.getIcon());
                    item.put("remain", showRemain.orElse(-1));
                    item.put("canExchange", buyStatus);
                    return item;
                }).collect(Collectors.toList());


        data.put("commodityList", awardList);
        return Response.success(data);
    }

    /**
     * 获取用户钱包
     **/
    private JSONObject getWallet(ShopComponentAttr attr, long uid) {
        JSONObject wallet = new JSONObject();
        Map<String, String> currencyMap = attr.getCurrencyMap();
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        if (attr.getPayWay() == ShopComponentAttr.PAY_WAY_SELF) {
            String walletKey = makeKey(attr, String.format(USER_WALLET_KEY, uid));
            Map<Object, Object> walletInfo = actRedisDao.hGetAll(groupCode, walletKey);
            currencyMap.entrySet().stream().forEachOrdered(currency -> {
                long num = Convert.toLong(walletInfo.get(currency.getKey()));
                wallet.put(currency.getKey(), num);
            });
        } else if (attr.getPayWay() == ShopComponentAttr.PAY_WAY_SETTLE_SYSTEM) {
            List<String> cids = Lists.newArrayList(currencyMap.keySet());
            Map<String, Long> balances = currencyClient.balance(uid, CURRENCY_BUSI_ID, cids);
            if (balances == null) {
                throw new SuperException("余额查询失败，请稍后再试！", Code.E_UNKNOWN.getCode());
            }
            cids.forEach(cid -> wallet.put(cid, balances.get(cid)));
        } else {
            throw new SuperException(Code.E_DATA_ERROR);
        }

        return wallet;
    }


    /**
     * 查询的商城商品信息
     *
     * @param actId
     * @param cmptUseInx
     * @return
     */
    public Response queryCommodityInfo(long actId, long cmptUseInx) {

        ShopComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + cmptUseInx);

        JSONObject data = new JSONObject();
        data.put("logo", "");
        data.put("nick", "");
        data.put("shopName", attr.getShopName());

        //获取用户钱包

        Map<String, String> currencyMap = attr.getCurrencyMap();

        JSONObject wallet = new JSONObject();
        currencyMap.entrySet().stream().forEachOrdered(currency -> {
            wallet.put(currency.getKey(), 0);
        });
        data.put("wallet", wallet);
        //查询合成礼物的剩余数量和兑换信息信息
        List<ShopComponentAttr.Commodity> commodities = Lists.newArrayList(attr.getCommodityMap().values());
        //生产商品信息列表
        List<JSONObject> awardList = commodities.stream()
                //0不展示
                .filter(commodity -> commodity.getStatus() != 0)
                .map(commodity -> {
                    JSONObject item = new JSONObject();
                    item.put("id", commodity.getId());
                    item.put("name", commodity.getName());
                    item.put("price", commodity.getPrice());
                    item.put("icon", commodity.getIcon());
                    item.put("remain", 0);
                    item.put("canExchange", 2);
                    return item;
                }).collect(Collectors.toList());

        data.put("commodityList", awardList);
        return Response.success(data);
    }

    private Map<String, Integer> getLimitKeyMap(ShopComponentAttr.Commodity commodity, Date date, long uid) {
        Map<String, Integer> limitMap = Maps.newHashMap();
        if (commodity.getTotalLimit() > 0) {
            limitMap.put("totalLimit-" + commodity.getId(), commodity.getTotalLimit());
        }
        if (commodity.getDayLimit() > 0) {
            String day = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
            limitMap.put("dayLimit-" + commodity.getId() + "-" + day, commodity.getDayLimit());
        }
        if (commodity.getUserLimit() > 0) {
            limitMap.put("userLimit-" + commodity.getId() + "-" + uid, commodity.getUserLimit());
        }
        if (commodity.getUserDayLimit() > 0) {
            String day = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
            limitMap.put("userDayLimit-" + commodity.getId() + "-" + uid + "-" + day, commodity.getUserDayLimit());
        }
        return limitMap;
    }

    /**
     * 检查和扣款 ，2是余额不足，1限制购买，0是可以购买
     *
     * @param uid
     * @param commodity
     * @param date
     * @return
     */
    private Integer exchangeCommodity(long uid, ShopComponentAttr.Commodity commodity, Date date, ShopComponentAttr attr) {
        if (attr.getPayWay() == ShopComponentAttr.PAY_WAY_SELF) {
            String walletKey = makeKey(attr, String.format(USER_WALLET_KEY, uid));
            String limitRecordKey = makeKey(attr, String.format(LIMIT_RECORD_KEY, uid));
            Map<String, Integer> limitMap = getLimitKeyMap(commodity, date, uid);
            List<String> keys = Arrays.asList(walletKey, limitRecordKey);
            List<String> argv = Arrays.asList(String.valueOf(uid), commodity.getId(), JSON.toJSONString(limitMap), JSON.toJSONString(commodity.getPrice()));
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            Long result = actRedisDao.executeLua(groupCode, EXCHANGE_COMMODITY_LUA, Long.class, keys, argv);
            log.info("exchangeCommodity info@uid:{},commodityId:{} day:{},index:{} result:{}"
                    , uid, commodity.getId(), attr.getCmptUseInx(), result);
            return result.intValue();
        } else if (attr.getPayWay() == ShopComponentAttr.PAY_WAY_SETTLE_SYSTEM) {
            // 检查用户钱包是否足够 -- 等启华提供批量接口
            Map<String, Long> priceMap = commodity.getPrice();
            Map<String, Long> balances = currencyClient.balance(uid, CURRENCY_BUSI_ID, Lists.newArrayList(priceMap.keySet()));
            if (balances == null) {
                throw new SuperException("余额查询失败，请稍后再试！", Code.E_UNKNOWN.getCode());
            }
            priceMap.keySet().forEach(cid -> {
                if (balances.get(cid) < priceMap.get(cid)) {
                    throw new SuperException(Code.E_BALANCE);
                }
            });
            // 从用户钱包扣费 -- 等启华提供批量接口
            String seq = java.util.UUID.randomUUID().toString();
            int ret = currencyClient.consume(uid, CURRENCY_BUSI_ID, priceMap, seq);
            log.info("currencyClient.consume done@uid:{}, busiId:{}, priceMap:{}, seq:{}", uid, CURRENCY_BUSI_ID, JSON.toJSONString(priceMap), seq);
            // 扣费的 关注几个业务异常状态码 NOT_ENOUGH(3, "NOT ENOUGH"), PARAMETER_ERROR(4, "PARAMETER ERROR")
            if (ret == 1) {
                return 0;
            } else if (ret == 3) {
                return 2;
            }
            throw new SuperException("扣除操作失败：" + ret, Code.E_UNKNOWN.getCode());
        } else {
            throw new SuperException(Code.E_DATA_ERROR);
        }
    }

    /**
     * 特殊的商品方法器
     */
    public static interface Releaser {
        void release(long uid, ShopComponentAttr.Commodity commodity, Date date, String seq, ShopComponentAttr attr);
    }

    @Bean
    public Releaser defatutReleaser() {
        return
                (long uid, ShopComponentAttr.Commodity commodity, Date date, String seq, ShopComponentAttr attr) -> {
                    Map<Long, Integer> packageIds = Maps.newHashMap(Collections.singletonMap(commodity.getPackageId(), commodity.getCount()));
                    Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap(Collections.singletonMap(attr.getTaskId(), packageIds));
                    hdztAwardServiceClient.doBatchWelfare(uid, taskPackageIds, DateUtil.getNowYyyyMMddHHmmss(), 3, Maps.newHashMap());
                };
    }

    @Lazy
    @Autowired
    public void setReleaserMap(Map<String, Releaser> releaserMap) {
        this.releaserMap = releaserMap;
    }

    public static class PurchaseRecord {
        /**
         * uid
         */
        private long uid;
        /**
         * 活动id
         */
        private long actId;

        /**
         * 商品id
         */
        private String commodityId;

        /**
         * 商品名称
         */
        private String name;
        /**
         * 业务
         */
        private BusiId busiId;
        /**
         * seq
         */
        private String seq;
        /**
         * 记录产生时间
         */
        private long createTime;
        /**
         * 购买数量
         */
        private long count;

        public long getUid() {
            return uid;
        }

        public void setUid(long uid) {
            this.uid = uid;
        }

        public long getActId() {
            return actId;
        }

        public void setActId(long actId) {
            this.actId = actId;
        }

        public String getCommodityId() {
            return commodityId;
        }

        public void setCommodityId(String commodityId) {
            this.commodityId = commodityId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public BusiId getBusiId() {
            return busiId;
        }

        public void setBusiId(BusiId busiId) {
            this.busiId = busiId;
        }

        public String getSeq() {
            return seq;
        }

        public void setSeq(String seq) {
            this.seq = seq;
        }

        public long getCreateTime() {
            return createTime;
        }

        public void setCreateTime(long createTime) {
            this.createTime = createTime;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }

        public PurchaseRecord() {
        }

        public PurchaseRecord(long uid, long actId, String commodityId, String name, BusiId busiId, String seq, long createTime, long count) {
            this.uid = uid;
            this.actId = actId;
            this.commodityId = commodityId;
            this.busiId = busiId;
            this.seq = seq;
            this.createTime = createTime;
            this.count = count;
            this.name = name;
        }
    }
}
