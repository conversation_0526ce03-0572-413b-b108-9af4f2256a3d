package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardConfig;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.exception.GameEcologyBusinessException;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.BigDataService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.bean.HashIncParameter;
import com.yy.gameecology.common.consts.ReceiveAwardExpireType;
import com.yy.gameecology.common.db.model.gameecology.ActTaskAwardRecord;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.ReceiveAwardConfig;
import com.yy.gameecology.hdzj.bean.ReceiveAwardInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ReceiveAwardComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * desc:奖励领取组件
 *
 * @createBy 曾文帜
 * @create 2021-08-10 16:48
 **/
@UseRedisStore
@Component
public class ReceiveAwardComponent extends BaseActComponent<ReceiveAwardComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommonService commonService;


    @Autowired
    protected HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private ActInfoService actInfoService;

    @Autowired
    private BigDataService bigDataService;

    /**
     * 用户资格余额
     * %s_%s: uid_dayCode,如果有效期不是天的，则留空
     */
    private static final String USER_QUALIFICATION_BALANCE = "user_qua_balance:%s:%s";

    /**
     * 用户发放数量记录
     * %s daycode
     */
    private static final String USER_TASK_PACKAGE_RELEASE_AMOUNT = "user_task_package_release_amount:%s";


    @Override
    public Long getComponentId() {
        return ComponentId.RECEIVE_REWARD;
    }

    public void addAwardQualificationWithRetry(long cmptUseInx, String seq, long actId, long uid, String awardCode, long amount, int retryTimes) {
        //尽可能重试发放资格
        int retry = 0;
        while (retry < retryTimes) {
            try {
                this.addAwardQualification(cmptUseInx, seq, actId, uid, awardCode, amount);
                log.info("addAwardQualification ok,seq:{},uid:{},actId:{},cmptUseInx:{},awardCode:{},amount:{}", seq, uid, actId, cmptUseInx, awardCode, amount);
                break;
            } catch (Exception e) {
                log.error("addAwardQualification error,seq:{},uid:{},actId:{},cmptUseInx:{},awardCode:{},amount:{},e:{}", seq, uid, actId, cmptUseInx, awardCode, amount, e.getMessage(), e);
                retry++;
            }
        }
    }

    /**
     * 增加领取奖励资格
     *
     * @throws GameEcologyBusinessException
     */
    public void addAwardQualification(long cmptUseInx, String seq, long actId, long uid, String awardCode, long amount) throws GameEcologyBusinessException {
        // 参数校验
        ReceiveAwardConfig config = this.getComponentAttr(actId, cmptUseInx).getReceiveAwardConfigMap().get(awardCode);
        if (config == null) {
            throw new GameEcologyBusinessException(String.format("awardCode:%s 不存在", awardCode), 1);
        }

        ReceiveAwardComponentAttr attr = getComponentAttr(actId, cmptUseInx);

        Date now = commonService.getNow(actId);
        String groupCode = getRedisGroupCode(actId);
        try {
            String balanceKey = buildQuaBalanceKey(now, cmptUseInx, actId, uid, config);
            //inc和seq写成1个lua,保证数据一致性
            String seqKey = makeKey(actId, this.getComponentId(), cmptUseInx, "seq:" + seq);
            Long result = actRedisDao.hIncrByKeyWithSeq(groupCode, seqKey, balanceKey, awardCode, amount, 0);
            log.info("addAwardQualification done,seq:{},actId:{},cmptUseInx:{},awardCode:{},amount:{},balanceKey:{},result:{}", seq, actId, cmptUseInx, awardCode, amount, balanceKey, result);

            //增加数据统计信息
            //long actId, BusiId busiId, long busiTime, String member, RoleType roleType, long score, int scoreType, String ext
            bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(attr.getBusiId()), now.getTime(), uid + "", RoleType.ANCHOR, amount, 16, awardCode);

        } catch (Exception e) {
            log.error("addAwardQualification error,seq:{},actId:{},cmptUseInx:{},awardCode:{},amount:{},e:{}", seq, actId, cmptUseInx, awardCode, amount, e.getMessage(), e);
            throw new GameEcologyBusinessException(e.getMessage(), 2);
        }

    }

    /**
     * 用户领奖资格余额key
     */
    private String buildQuaBalanceKey(Date now, long cmptUseInx, long actId, long uid, ReceiveAwardConfig config) {
        return buildQuaBalanceKey(now, cmptUseInx, actId, uid, config.getExpireType());
    }

    private String buildQuaBalanceKey(Date now, long cmptUseInx, long actId, long uid, int expireType) {
        String dayCode = expireType == ReceiveAwardExpireType.DAY ? DateUtil.format(now, DateUtil.PATTERN_TYPE2) : "";
        return makeKey(actId, this.getComponentId(), cmptUseInx, String.format(USER_QUALIFICATION_BALANCE, dayCode, uid));
    }

    /**
     * 扣减资格,领取奖励
     *
     * @param actId          活动id
     * @param uid            uid
     * @param awardCode      奖励编码
     * @param partialRelease 库存不足的时候是否允许部分领取
     */
    public void releaseAward(long actId, long cmptUseInx, long uid, String awardCode, int partialRelease) throws GameEcologyBusinessException {
        log.info("releaseAward begin,actId:{},uid:{},cmptUseInx:{},awardCode:{}，partialRelease:{}", actId, uid, cmptUseInx, awardCode, partialRelease);
        ReceiveAwardComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        ReceiveAwardConfig config = attr.getReceiveAwardConfigMap().get(awardCode);

        if (config == null) {
            throw new GameEcologyBusinessException(String.format("awardCode:%s 不存在", awardCode), 1);
        }
        if (!actInfoService.inActTime(actId)) {
            throw new GameEcologyBusinessException("未在活动时间内", 2);
        }

        Date now = commonService.getNow(actId);
        String groupCode = getRedisGroupCode(actId);
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        //最终要发放的奖品
        Map<Long, Map<Long, Integer>> taskIdPackageId = Maps.newHashMap();
        //用于记录最终要发放的奖品
        List<ActTaskAwardRecord> actTaskAwardRecords = Lists.newArrayList();
        //redis操作参数
        List<HashIncParameter> hashRedisParas = Lists.newArrayList();


        // 初步判断余额
        String balanceKey = buildQuaBalanceKey(now, cmptUseInx, actId, uid, config);
        long balance = Convert.toLong(actRedisDao.hget(groupCode, balanceKey, awardCode), 0);
        if (balance <= 0) {
            throw new GameEcologyBusinessException("领奖资格余额不足", 3);
        }
        HashIncParameter balancePara = new HashIncParameter(balanceKey, awardCode, "-1", "-1", "0");
        hashRedisParas.add(balancePara);

        // 初步判断库存
        List<HdztAwardConfig> awardConfigs = attr.getHdztAwardConfigs().getOrDefault(awardCode, new ArrayList<>());
        for (HdztAwardConfig awardConfig : awardConfigs) {
            List<String> awardPackageName = Lists.newArrayList();
            if (awardConfig.getDayLimit() > 0) {
                String awardAmountKey = makeKey(getComponentAttr(actId, cmptUseInx), String.format(USER_TASK_PACKAGE_RELEASE_AMOUNT, dayCode));
                long alreadyRelease = Convert.toLong(actRedisDao.hget(groupCode, awardAmountKey, awardCode), 0);
                long awardConfigAmount = awardConfig.getAmount();
                //超出日限额，不允许部分领取
                if (alreadyRelease + awardConfigAmount > awardConfig.getDayLimit() && partialRelease != 1) {
                    throw new GameEcologyBusinessException("超出日限额", 999);
                }
                //超出日限额，允许部分领取
                else if (alreadyRelease + awardConfigAmount >= awardConfig.getDayLimit() && partialRelease == 1) {
                    log.warn("超日日限额，用户同意部分领取,actId:{},cmptUseInx:{},awardCode:{}，partialRelease:{}", actId, cmptUseInx, awardCode, partialRelease);
                    continue;
                }
                //没有超出日限额
                else {
                    putTaskIdPackageId(taskIdPackageId, awardConfig);
                    awardPackageName.add(awardConfig.getName());
                }

                HashIncParameter hasRedisPara = new HashIncParameter(awardAmountKey, awardCode, awardConfigAmount + "", "1", awardConfig.getDayLimit() + "");
                hashRedisParas.add(hasRedisPara);

            } else {
                putTaskIdPackageId(taskIdPackageId, awardConfig);
                awardPackageName.add(awardConfig.getName());
            }

            ActTaskAwardRecord awardRecord = buildAwardRecord(actId, cmptUseInx, uid, String.join(",", awardPackageName), config.getTaskName());
            actTaskAwardRecords.add(awardRecord);
        }


        //lua 扣减领取资格,增加领取记录,保证原子性和数据一致性
        boolean result = actRedisDao.hBatchIncrWithLimit(groupCode, hashRedisParas);
        if (!result) {
            throw new GameEcologyBusinessException("操作太频繁，请稍后重试", 2);
        }

        //---操作完redis，下面操作已经不能回滚


        //发奖
        String time = DateUtil.format(now);
        int retry = this.getComponentAttr(actId, cmptUseInx).getRetry();
        hdztAwardServiceClient.doBatchWelfare(uid, taskIdPackageId, time, retry, Maps.newHashMap());

        //增加领取记录
        log.info("insert awardRecord begin:{}", JSON.toJSONString(actTaskAwardRecords));
        gameecologyDao.batchInsert(ActTaskAwardRecord.class, actTaskAwardRecords, ActTaskAwardRecord.TABLE_NAME);
        log.info("insert awardRecord done:{},", JSON.toJSONString(actTaskAwardRecords));

        log.info("releaseAward ok,actId:{},uid:{},cmptUseInx:{},awardCode:{}，partialRelease:{}", actId, uid, cmptUseInx, awardCode, partialRelease);
    }

    private ActTaskAwardRecord buildAwardRecord(long actId, long cmptUseInx, long uid, String packageName, String taskName) {
        ActTaskAwardRecord record = new ActTaskAwardRecord();
        record.setCtime(new Date());
        record.setActId(actId);
        record.setSeq("");
        record.setTaskId(this.getComponentId() + cmptUseInx);
        record.setPackageId(0L);
        record.setPackageName(packageName);
        record.setPackageNum(0L);
        record.setMemberId(uid + "");
        record.setStatus(1L);
        record.setPlatform(0L);
        record.setRemark(taskName);
        record.setCtime(new Date());
        record.setUtime(new Date());
        return record;
    }

    private void putTaskIdPackageId(Map<Long, Map<Long, Integer>> taskIdPackageId, HdztAwardConfig awardConfig) {
        Map<Long, Integer> packageIdAmount = taskIdPackageId.getOrDefault(awardConfig.getTaskId(), Maps.newHashMap());
        int amount = packageIdAmount.getOrDefault(awardConfig.getPackageId(), 0);
        packageIdAmount.put(awardConfig.getPackageId(), amount + awardConfig.getAmount());
        taskIdPackageId.put(awardConfig.getTaskId(), packageIdAmount);
    }

    /**
     * 奖励领取状态读取
     *
     * @param actId
     * @param uid
     */
    public List<ReceiveAwardInfo> queryAwardRecordStatus(long actId, long uid, long cmptUseInx) {
        Date now = commonService.getNow(actId);
        String groupCode = getRedisGroupCode(actId);

        //需要返回的领取状态信息
        List<ReceiveAwardInfo> awardInfos = Lists.newArrayList();

        //按天领取资格余额
        String dayBalanceKey = buildQuaBalanceKey(now, cmptUseInx, actId, uid, ReceiveAwardExpireType.DAY);
        Map<Object, Object> dayBalance = actRedisDao.hGetAll(groupCode, dayBalanceKey);
        //按活动领取资格余额
        String actBalanceKey = buildQuaBalanceKey(now, cmptUseInx, actId, uid, ReceiveAwardExpireType.ACT);
        Map<Object, Object> actBalance = actRedisDao.hGetAll(groupCode, actBalanceKey);

        ReceiveAwardComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        Map<String, ReceiveAwardConfig> configMap = attr.getReceiveAwardConfigMap();
        for (String awardCode : configMap.keySet()) {
            ReceiveAwardInfo awardInfo = new ReceiveAwardInfo();

            ReceiveAwardConfig config = configMap.get(awardCode);
            awardInfo.setReceiveAwardConfig(config);
            Object balance = null;
            if (config.getExpireType() == ReceiveAwardExpireType.ACT) {
                balance = actBalance.get(awardCode);
            } else {
                balance = dayBalance.get(awardCode);
            }

            //设置领取状态
            if (balance == null) {
                awardInfo.setStatus(0);
            } else if (Convert.toInt(balance, 0) >= 1) {
                awardInfo.setStatus(1);
            } else {
                awardInfo.setStatus(2);
            }

            awardInfos.add(awardInfo);
        }

        return awardInfos;

    }

    /**
     * 奖励记录读取
     *
     * @param actId
     * @param uid
     * @param cmptUseInx
     * @return
     */
    public List<ActTaskAwardRecord> queryAwardRecord(long actId, long uid, long cmptUseInx) {
        ActTaskAwardRecord where = new ActTaskAwardRecord();
        where.setActId(actId);
        Long taskId = this.getComponentId() + cmptUseInx;
        where.setTaskId(taskId);
        where.setMemberId(uid + "");
        return gameecologyDao.select(ActTaskAwardRecord.class, where);
    }
}
