package com.yy.gameecology.common.locker.redisson;

import com.yy.gameecology.common.locker.AbstractLocker;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class RedissonLocker {

    private static final Logger log = LoggerFactory.getLogger(AbstractLocker.class);

    public boolean lock(RLock lock, int ttl, int count) {
        if(lock == null) {
            return false;
        }
        if(!isValid(lock.getName())){
            return false;
        }

        int inx = 0;
        try {
            count = (count <= 0) ? 1 : count;
            ttl = (ttl <= 0) ? Integer.MAX_VALUE : ttl;

            while (++inx <= count) {
                boolean lockSucc = lock.tryLock(1, ttl, TimeUnit.SECONDS);
                if (lockSucc) {
                    log.info("redisson lock ok@try {}/{}, name:{}", inx, count, lock.getName());
                    return true;
                }
                if (inx < count) {
                    log.warn("redisson lock fail@try {}/{}, name:{}, ttl:{}, retry after 1s", inx, count, lock.getName(), ttl);
                } else {
                    log.warn("redisson lock fail@can't lock {} by try {} times", lock.getName(), count);
                }
            }
        } catch (Throwable t) {
            log.error("lock exception@inx:{}, name:{}, err:{}", inx, lock.getName(), t, t);
        }
        return false;
    }

    public void unLock(RLock lock) {
        if(lock == null) {
            return;
        }
        lock.unlock();
    }

    protected void sleep(int seconds) {
        try {
            Thread.sleep(seconds * 1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    protected boolean isValid(String name) {
        return name != null && name.trim().length() > 0;
    }
}
