package com.yy.gameecology.activity.dao.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.KeyCopyResult;
import com.yy.gameecology.activity.bean.UpdateTaskReq;
import com.yy.gameecology.activity.bean.UpdateTaskResult;
import com.yy.gameecology.activity.commons.RedisSupport;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.service.datatransfer.RedisDataCopy;
import com.yy.gameecology.activity.service.datatransfer.RedisDataCopyFactory;
import com.yy.gameecology.common.bean.HashIncParameter;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import io.lettuce.core.KeyScanCursor;
import io.lettuce.core.ScanArgs;
import io.lettuce.core.api.async.RedisKeyAsyncCommands;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;


@Repository
public class ActRedisGroupDao {


    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private RedisDataCopyFactory redisDataCopyFactory;

    /**
     * @param groupCode 通过com.yy.gameecology.activity.config.redis.RedisConfigManager#getGroupCode(java.lang.Long)获得 ，如果没有活动id使用 com.yy.gameecology.activity.config.redis.RedisConfigManager#default_group_code
     * @return 根据redis分组id获得
     */
    public StringRedisTemplate getRedisTemplate(String groupCode) {
        return redisConfigManager.getRedisTemplate(groupCode);
    }

    /**
     * 获取redis服务器的时间（毫秒数）
     */
    public long time(String groupCode) {
        List<Object> list = getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            connection.time();
            return null;
        });
        return (long) list.get(0);
    }

    public <T> List<Object> zBatchIncrDouble(String groupCode, List<String> keys, List<T> vals, List<Double> incs) {
        return getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zIncrBy(keys.get(index).getBytes(), incs.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }

    public <T> List<Object> zBatchIncr(String groupCode, List<String> keys, List<T> vals, List<Long> incs) {
        return getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zIncrBy(keys.get(index).getBytes(), incs.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }

    public <T> List<Object> zBatchAdd(String groupCode, List<String> keys, List<T> vals, List<Long> scores) {
        return getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zAdd(keys.get(index).getBytes(), scores.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }

    public <T> List<Object> zBatchAddDouble(String groupCode, List<String> keys, List<T> vals, List<Double> scores) {
        return getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (int index = 0; index < keys.size(); index++) {
                connection.zAdd(keys.get(index).getBytes(), scores.get(index), vals.get(index).toString().getBytes());
            }
            return null;
        });
    }

    public <T> List<Object> zBatchIncrWithTime(String groupCode, List<String> keys, List<T> rows, List<Long> incs) {
        List<String> argv = new ArrayList<>();
        for (int i = 0; i < keys.size(); i++) {
            argv.add(rows.get(i).toString());
            argv.add(incs.get(i).toString());
        }
        argv.add(getTime());
        return executeLua(groupCode, RedisSupport.ZBATCHINCR_WITH_TIME, List.class, keys, argv);
    }

    public String get(String groupCode, final String key) {
        return getRedisTemplate(groupCode).opsForValue().get(key);
    }

    public long incrValue(String groupCode, final String key, final long step) {
        Long rs = getRedisTemplate(groupCode).opsForValue().increment(key, step);
        return rs == null ? 0 : rs;
    }

    public void set(String groupCode, final String key, final String value) {
        getRedisTemplate(groupCode).opsForValue().set(key, value);
    }

    public void set(String groupCode, final String key, final String value, long expireSec) {
        getRedisTemplate(groupCode).opsForValue().set(key, value, expireSec, TimeUnit.SECONDS);
    }

    public boolean setNX(String groupCode, final String key, String val) {
        Boolean succ = getRedisTemplate(groupCode).opsForValue().setIfAbsent(key, val);
        return succ != null && succ;
    }

    public boolean setNX(String groupCode, final String key, String val, final long sec) {
        Boolean succ = getRedisTemplate(groupCode).execute((RedisCallback<Boolean>) connection -> {
            Boolean setNX = connection.setNX(key.getBytes(), val.getBytes());
            if (Boolean.TRUE.equals(setNX)) {
                connection.expire(key.getBytes(), sec);
            }
            return setNX;
        });
        return succ != null && succ;
    }

    public boolean hasKey(String groupCode, String key) {
        return getRedisTemplate(groupCode).hasKey(key);
    }

    public void del(String groupCode, final String key) {
        getRedisTemplate(groupCode).delete(key);
    }

    public void delKeys(String groupCode, Set<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        getRedisTemplate(groupCode).delete(keys);
    }

    public String hget(String groupCode, final String key, final String hashKey) {
        Object obj = getRedisTemplate(groupCode).opsForHash().get(key, hashKey);
        if (obj == null) {
            return null;
        } else {
            return obj.toString();
        }
    }

    public Map<Object, Object> hGetAll(String groupCode, final String key) {
        return getRedisTemplate(groupCode).opsForHash().entries(key);
    }

    public Map<Object, Object> hScan(String groupCode, String key, int batchSize) {
        Map<Object, Object> reuslt = Maps.newHashMap();
        //get
        Cursor<Map.Entry<Object, Object>> cursor =
                getRedisTemplate(groupCode).opsForHash().scan(key, ScanOptions.scanOptions().count(batchSize).match("*").build());
        while (cursor.hasNext()) {
            Map.Entry<Object, Object> data = cursor.next();
            reuslt.put(data.getKey(), data.getValue());
        }
        try {
            cursor.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return reuslt;
    }

    public List<Object> hmGet(String groupCode, final String key, List<Object> hashKeys) {
        return getRedisTemplate(groupCode).opsForHash().multiGet(key, hashKeys);
    }

    public void hset(String groupCode, final String key, final String hashKey, final String value) {
        getRedisTemplate(groupCode).opsForHash().put(key, hashKey, value);
    }

    public List<Object> hset(String groupCode, final String key, final String hashKey, final String value,
                             long expireSecond) {
        List<Object> list = getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.hSet(key.getBytes(), hashKey.getBytes(), value.getBytes());
                if (expireSecond > 0) {
                    connection.expire(key.getBytes(), expireSecond);
                }
                return connection.closePipeline();
            }
        });
        return list;
    }

    public List<Object> hsetBatchKey(String group, final String key, final List<String> hashKeys, final List<String> values,
                                     Long expireSecond) {
        List<Object> list = getRedisTemplate(group).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                for (int i = 0; i < hashKeys.size(); ++i) {
                    //String key = keys.get(i);
                    String hashKey = hashKeys.get(i);
                    String value = values.get(i);
                    //long expireSecond = expireSeconds.get(i);
                    connection.hSet(key.getBytes(), hashKey.getBytes(), value.getBytes());
                }
                if (expireSecond > 0) {
                    connection.expire(key.getBytes(), expireSecond);
                }
                return connection.closePipeline();
            }
        });
        return list;
    }

    /**
     * scan满足条件的key
     **/
    public Set<String> scanKey(String groupCode, String keyPattern, int count) {
        return getRedisTemplate(groupCode).execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keys = Sets.newHashSet();
            // 最多一次取1000进行匹配
            int limit = Math.min(count, 1000);

            RedisKeyAsyncCommands multiKeyCommands = (RedisKeyAsyncCommands) connection.getNativeConnection();

            ScanArgs scanParams = new ScanArgs();
            scanParams.match(keyPattern);
            scanParams.limit(limit);

            try {
                KeyScanCursor<byte[]> cursor = (KeyScanCursor<byte[]>) multiKeyCommands.scan(scanParams).get();
                addKeys(keys, cursor);
                while (!cursor.isFinished()) {
                    cursor = (KeyScanCursor<byte[]>) multiKeyCommands.scan(cursor, scanParams).get();
                    addKeys(keys, cursor);
                }
            } catch (Exception ex) {
                log.error("scanKey error,groupCode={},keyPattern={},count={},e:{}", groupCode, keyPattern, count, ex.getMessage(), ex);
            }
            return keys;
        });
    }

    private void addKeys(Set<String> keys, KeyScanCursor<byte[]> cursor) {
        cursor.getKeys().forEach(key -> keys.add(new String(key)));
    }

    public void hmset(String groupCode, final String key, final Map<String, String> map) {
        getRedisTemplate(groupCode).opsForHash().putAll(key, map);
    }

    public void hmset(String groupCode, final String key, final Map<String, String> map, long expireSecond) {
        getRedisTemplate(groupCode).opsForHash().putAll(key, map);
        getRedisTemplate(groupCode).expire(key, expireSecond, TimeUnit.SECONDS);
    }

    public long hIncrByKey(String groupCode, final String key, final String hashKey, final long value) {
        return getRedisTemplate(groupCode).boundHashOps(key).increment(hashKey, value);
    }

    public long hIncrByKey(String groupCode, final String key, final String hashKey, final long value, long expireSecond) {
        List<Object> list = getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.hIncrBy(key.getBytes(), hashKey.getBytes(), value);
                if (expireSecond > 0) {
                    connection.expire(key.getBytes(), expireSecond);
                }
                return connection.closePipeline();
            }
        });
        return Convert.toLong(list.get(0));
    }

    public long hlen(String groupCode, final String key) {
        return getRedisTemplate(groupCode).boundHashOps(key).size();
    }

    public long hsize(String groupCode, final String key) {
        Long size = getRedisTemplate(groupCode).boundHashOps(key).size();
        return size==null ? 0l : size;
    }


    public boolean hsetnx(String groupCode, final String key, final String hashKey, final String value) {
        return getRedisTemplate(groupCode).boundHashOps(key).putIfAbsent(hashKey, value);
    }

    public List<Object> hdelKey(String groupCode, final String key, final List<String> hashKey) {
        List<Object> list = getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                for (int i = 0; i < hashKey.size(); ++i) {
                    connection.hDel(key.getBytes(), hashKey.get(i).getBytes());
                }
                return connection.closePipeline();
            }
        });
        return list;
    }

    public void hdel(String groupCode, final String key, String hashKey) {
        getRedisTemplate(groupCode).opsForHash().delete(key, hashKey);
    }


    public Double zScore(String groupCode, String key, String obj) {
        return getRedisTemplate(groupCode).opsForZSet().score(key, obj);
    }

    public long zscore(String groupCode, String key, String row) {
        return Convert.toLong(getRedisTemplate(groupCode).opsForZSet().score(key, row));
    }

    /**
     * 只支持分数为正整数的zset
     */
    public List<Long> zScoreAndRank(String groupCode, String key, String obj) {
        List<Object> list = getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.zScore(key.getBytes(), obj.getBytes());
                connection.zRevRank(key.getBytes(), obj.getBytes());
                return connection.closePipeline();
            }
        });

        // 表示 zset key 或 member 不存在
        if (list.get(0) == null) {
            return Arrays.asList(0L, 0L);
        } else {
            long score = Convert.toLong(list.get(0));
            long rank = Convert.toLong(list.get(1)) + 1;
            return Arrays.asList(score, rank);
        }
    }

    public Long zRevRank(String groupCode, String key, String obj) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRank(key, obj);
    }

    public boolean zAdd(String groupCode, String key, String obj, double score) {
        return getRedisTemplate(groupCode).opsForZSet().add(key, obj, score);
    }

    public long zIncr(String groupCode, String key, String obj, long score) {
        return getRedisTemplate(groupCode).opsForZSet().incrementScore(key, obj, score).longValue();
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevRange(String groupCode, String key, long num) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeWithScores(key, 0, num - 1);
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevRange(String groupCode, String key, long start, long end) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeWithScores(key, start, end);
    }

    public Set<String> zrevRangeByScore(String groupCode, String key, long min, long max) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeByScore(key, min, max);
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevRangeByScoreWithScore(String groupCode, String key, long min, long max) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeByScoreWithScores(key, min, max);
    }

    public Set<ZSetOperations.TypedTuple<String>> zrevRangeByScoreWithScore(String groupCode, String key, long min, long max, int count) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRangeByScoreWithScores(key, min, max, 0, count);
    }

    public Set<String> zrevRangeNoScores(String groupCode, String key, long num) {
        return getRedisTemplate(groupCode).opsForZSet().reverseRange(key, 0, num - 1);
    }

    public List<Set<String>> zBatchRevRangeNoScores(String groupCode, List<String> keys, long num) {
        List<Object> list = getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            for (String key : keys) {
                connection.zRevRange(key.getBytes(), 0, num - 1);
            }
            return null;
        });
        List<Set<String>> result = Lists.newArrayList();
        for (Object obj : list) {
            if (obj == null) {
                result.add(Sets.newLinkedHashSet());
            } else {
                Set<String> set = (Set<String>) obj;
                result.add(set);
            }
        }
        return result;
    }

    public Set<ZSetOperations.TypedTuple<String>> zrange(String groupCode, String key, long num) {
        return getRedisTemplate(groupCode).opsForZSet().rangeWithScores(key, 0, num - 1);
    }

    public long zcard(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForZSet().size(key);
    }

    public long zcount(String groupCode, String key, double min, double max) {
        return getRedisTemplate(groupCode).opsForZSet().count(key, min, max);
    }

    public void zDel(String groupCode, final String key, final String obj) {
        getRedisTemplate(groupCode).opsForZSet().remove(key, obj);
    }

    public Long getExpire(String groupCode, String key) {
        return getRedisTemplate(groupCode).getExpire(key);
    }

    public boolean sIsMember(String groupCode, String key, String member) {
        return getRedisTemplate(groupCode).opsForSet().isMember(key, member);
    }

    public <T> T execute(@NonNull ComponentAttr attr, @NonNull Function<StringRedisTemplate, T> fun) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        StringRedisTemplate redisTemplate = getRedisTemplate(groupCode);
        return fun.apply(redisTemplate);
    }

    public <T> T executeLua(String groupCode, String scriptLuaName, Class<T> returnClz, List<String> keys, List<String> argv) {
        DefaultRedisScript<T> script = new DefaultRedisScript<>();
        script.setScriptText(RedisSupport.getScript(scriptLuaName));
        script.setResultType(returnClz);
        return getRedisTemplate(groupCode).execute(script, keys, argv.toArray());
    }

    public boolean hExists(String groupCode, String key, String row) {
        return getRedisTemplate(groupCode).opsForHash().hasKey(key, row);
    }

    public Set<String> sMembers(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForSet().members(key);
    }

    public long sAdd(String groupCode, String key, String member) {
        return getRedisTemplate(groupCode).opsForSet().add(key, member);
    }

    public Long sadd(String group, String key, String... values) {
        return getRedisTemplate(group).opsForSet().add(key, values);
    }

    public long sBatchAdd(String groupCode, String key, String... member) {
        return getRedisTemplate(groupCode).opsForSet().add(key, member);
    }

    public Long zAddSets(String group, String key, Set<ZSetOperations.TypedTuple<String>> tuples) {
        return getRedisTemplate(group).opsForZSet().add(key, tuples);
    }

    public Long sRem(String groupCode, String key, String... values) {
        return getRedisTemplate(groupCode).opsForSet().remove(key, values);
    }


    /**
     * 带上时间戳的zset, 同分下, 后到的排后面
     *
     * @param key    zset key
     * @param rowkey zset rowkey
     * @param incr   zset incr score
     * @return
     */
    public long zincrWithTime(String groupCode, String key, String rowkey, long incr) {
        Long ret = zincrWithTime(groupCode, key, rowkey, incr, false);
        return Convert.toLong(ret, 0);
    }


    /**
     * 带上时间戳的zset, 同分下, 后到的排后面
     *
     * @param requireNotAbsent true == 需要原本rowkey已存在才能增加，如果原本rowkey不存在则返回null
     * @return null 增加失败
     */
    public Long zincrWithTime(String groupCode, String key, String rowkey, long incr, boolean requireNotAbsent) {
        return executeLua(groupCode, RedisSupport.ZINCR_WITH_TIME, Long.class, Collections.singletonList(key),
                Arrays.asList(rowkey, incr + "", getTime(), requireNotAbsent ? "1" : "0"));
    }

    /**
     * 带上时间戳的zset, 同分下, 后到的排后面
     *
     * @param requireNotAbsent true == 需要原本rowkey已存在才能增加，如果原本rowkey不存在则返回null
     * @return null 增加失败
     */
    public Long zAddWithTime(String groupCode, String key, String rowkey, long incr, boolean requireNotAbsent) {
        return executeLua(groupCode, RedisSupport.ZADD_WITH_TIME, Long.class, Collections.singletonList(key),
                Arrays.asList(rowkey, incr + "", getTime(), requireNotAbsent ? "1" : "0"));
    }

    /**
     * zset 的nx
     *
     * @param key
     * @param member
     * @param sec
     * @return
     */
    public boolean zSetNX(String groupCode, String key, String member, long sec) {
        Assert.isTrue(sec > 0, "sec less than 0,sec=" + sec);
        return executeLua(groupCode, RedisSupport.ZSET_NX, Boolean.class, Collections.singletonList(key),
                Lists.newArrayList(member, sec + "", System.currentTimeMillis() + ""));
    }

    /**
     * 带上后缀的zincr
     *
     * @param key
     * @param rowkey
     * @param incr
     * @param suffix 小于1的小数字符串
     * @return
     */
    public long zincrWithSuffix(String groupCode, String key, String rowkey, long incr, String suffix) {
        String requireNotAbsent = "0";
        Long result = executeLua(groupCode, RedisSupport.ZINCR_WITH_TIME, Long.class, Collections.singletonList(key),
                Arrays.asList(rowkey, incr + "", suffix, requireNotAbsent));
        return result == null ? 0 : result.longValue();
    }

    /**
     * FIXME: 本代码支持到 2034/11/19 01:27:27
     *
     * @return
     */
    public String getTime() {
        return "0." + (Integer.MAX_VALUE - System.currentTimeMillis() / 1000);
    }

    /**
     * @param pattern
     * @return
     */
    public Set<String> keys(String groupCode, String pattern) {
        return getRedisTemplate(groupCode).keys(pattern);
    }


    public boolean setExpire(String groupCode, String key, long seconds) {
        return getRedisTemplate(groupCode).expire(key, seconds, TimeUnit.SECONDS);
    }


    /**
     * hash 累积 step，当 step>=0 时不能超过 limit， 当 step<0时不能小于limit
     *
     * @return 返回2个元素的list， 第一个元素是结果码， 第二个元素是操作后的分值
     * 1: 累积 step 后不违反 limit 限定，返回累积后的新值
     * -1：step>=0时递增失败（递增后会大于limit），返回旧值
     * -2：step<0时递减失败 （递减后会小于limit），返回旧值
     */
    public List<Long> hIncrWithLimit(String groupCode, final String key, final String field, final long step, long limit) {
        return hIncrWithLimit(groupCode, key, field, step, limit, false);
    }

    /**
     * hash 累积 step，当 step>=0 时不能超过 limit， 当 step<0时不能小于limit
     *
     * @return 返回2个元素的list， 第一个元素是结果码， 第二个元素是分值
     * <p>
     * 返回码
     * 1: 累积 step 后不违反 limit 限定，返回累积后的新值
     * 2: 允许部分增加时，累计部分值后，刚好等于 limit
     * <p>
     * 返回值
     * -1：step>=0时递增失败（递增后会大于limit），返回旧值
     * -2：step<0时递减失败 （递减后会小于limit），返回旧值
     * 2: 实际操作的值
     */
    public List<Long> hIncrWithLimit(String groupCode, final String key, final String field, final long step, long limit, boolean canPartialAdd) {
        List<String> keys = Lists.newArrayList(key);
        List<String> argv = Lists.newArrayList(field, String.valueOf(step), String.valueOf(limit), String.valueOf(canPartialAdd ? 1 : 0));
        String result = this.executeLua(groupCode, "hash_incr_with_limit.lua", String.class, keys, argv);
        List<Long> array = JSON.parseArray(result, Long.class);
        return array;
    }

    /**
     * hash 累积 step，当 step>=0 时不能超过 limit， 当 step<0时不能小于limit
     *
     * @return 返回3个元素的list， 第一个元素是结果码， 第二个元素是分值，第三个是1：seq首次添加的结果，0：seq重复，返回旧结果
     * <p>
     * 返回码
     * 1: 累积 step 后不违反 limit 限定，返回累积后的新值
     * 2: 允许部分增加时，累计部分值后，刚好等于 limit
     * <=0：增加失败
     * <p>
     * 返回值
     * -1：step>=0时递增失败（递增后会大于limit），返回旧值
     * -2：step<0时递减失败 （递减后会小于limit），返回旧值
     * 2: 实际操作的值
     */
    public List<Long> hIncrWithLimitSeq(String groupCode, final String seq, final String key, final String field, final long step, long limit, boolean canPartialAdd, long seqExpireSeconds) {
        List<String> keys = ImmutableList.of(key, seq);
        List<String> argv = ImmutableList.of(field, String.valueOf(step), String.valueOf(limit), String.valueOf(canPartialAdd ? 1 : 0), String.valueOf(seqExpireSeconds));
        String result = this.executeLua(groupCode, "seq/hinc_with_limit_seq.lua", String.class, keys, argv);
        return JSON.parseArray(result, Long.class);
    }

    /**
     * 批量有限制地递增，必须符合限制才修改数据，如果不符合限制则返回错误
     *
     * @param hashIncParameters 递增参数
     * @return true成功，false
     */
    public boolean hBatchIncrWithLimit(String groupCode, List<HashIncParameter> hashIncParameters) {
        List<String> keys = Lists.newArrayList();
        List<String> argv = Lists.newArrayList(JSON.toJSONString(hashIncParameters));
        Boolean result = this.executeLua(groupCode, "hash_batch_inc_with_limit.lua", Boolean.class, keys, argv);
        return Convert.toBoolean(result, false);
    }

    public boolean hCompareAndSet(String groupCode, final String key, final String field, final String expect, String update) {
        List<String> keys = Lists.newArrayList(key);
        List<String> argv = Lists.newArrayList(field, expect, update);
        Long result = this.executeLua(groupCode, RedisSupport.HASH_CAS, Long.class, keys, argv);
        return result == 1;
    }

    public boolean hCompareAndDel(String groupCode, final String key, final String field, final String expect) {
        List<String> keys = Lists.newArrayList(key);
        List<String> argv = Lists.newArrayList(field, expect);
        Long result = this.executeLua(groupCode, RedisSupport.HASH_CAD, Long.class, keys, argv);
        return result == 1;
    }


    /**
     * zset 累积 step，当 step>=0 时不能超过 limit， 当 step<0时不能小于limit
     *
     * @return 返回2个元素的list， 第一个元素是结果码， 第二个元素是操作后的分值
     * 1: 累积 step 后不违反 limit 限定，返回累积后的新值
     * -1：step>=0时递增失败（递增后会大于limit），返回旧值
     * -2：step<0时递减失败 （递减后会小于limit），返回旧值
     */
    public List<Long> zIncrWithLimit(String groupCode, String key, String member, long step, long limit) {
        List<String> keys = Lists.newArrayList(key);
        List<String> argv = Lists.newArrayList(member, String.valueOf(step), String.valueOf(limit));
        String result = this.executeLua(groupCode, "zset_incr_with_limit.lua", String.class, keys, argv);
        return JSON.parseArray(result, Long.class);
    }


    /**
     * 返回老值，并且当新值大于老值时更新否则不更新
     */
    public long hSetGrownReturnOld(String groupCode, String hashKey, String receiver, long score) {
        List<String> argv = Lists.newArrayList(receiver, String.valueOf(score));
        List<String> keys = Lists.newArrayList(hashKey);
        return this.executeLua(groupCode, "hset_grown_return_old.lua", Long.class, keys, argv);
    }

    /**
     * pk反转
     */
    public String pkOverTake(String groupCode, String hashKey, String field, String winnerId, long score, long configScore) {
        List<String> argv = Lists.newArrayList(field, winnerId, String.valueOf(score), String.valueOf(configScore));
        List<String> keys = Lists.newArrayList(hashKey);

        return this.executeLua(groupCode, "pk_over_take.lua", String.class, keys, argv);
    }

    /**
     * 返回null的时候seq重复，历史已经操作过，本次不做操作
     *
     * @param expireTime seq 去重有效过期时间,单位:秒
     */
    public Long hIncrByKeyWithSeq(String groupCode, String seq, final String key, final String hashKey, final long value, long expireTime) {
        List<String> keys = Lists.newArrayList(key, hashKey);
        List<String> argv = Lists.newArrayList(seq, value + "", expireTime + "");
        return this.executeLua(groupCode, "seq/hinc_with_seq.lua", Long.class, keys, argv);
    }

    public Long zIncrWithSeq(String groupCode, String seq, final String key, final String member, final long value) {
        List<String> keys = Arrays.asList(key, member);
        List<String> args = Arrays.asList(seq, value + "");

        String result = this.executeLua(groupCode, "seq/zIncr_with_seq.lua", String.class, keys, args);

        return Convert.toLong(result);
    }

    /**
     *
     * 放头部
     * 返回null的时候seq重复，历史已经操作过，本次不做操作
     *
     * @param expireSeconds seq 去重有效过期时间,单位:秒
     */
    public void lPushWithSeq(final String groupCode, final String seq, final String key, final String value, long expireSeconds) {
        List<String> keys = Lists.newArrayList(key);
        List<String> argv = Lists.newArrayList(seq, value, expireSeconds + "");
        this.executeLua(groupCode, "seq/lpush_with_seq.lua", Long.class, keys, argv);
    }

    /**
     *
     * 放尾巴
     * 返回null的时候seq重复，历史已经操作过，本次不做操作
     *
     * @param expireSeconds seq 去重有效过期时间,单位:秒
     */
    public void rPushWithSeq(final String groupCode, final String seq, final String key, final String value, long expireSeconds) {
        List<String> keys = Lists.newArrayList(key);
        List<String> argv = Lists.newArrayList(seq, value, expireSeconds + "");
        this.executeLua(groupCode, "seq/rpush_with_seq.lua", Long.class, keys, argv);
    }

    /**
     * 读取 list 指定位置的元素
     */
    public String lindex(String groupCode, String key, long index) {
        return getRedisTemplate(groupCode).opsForList().index(key, index);
    }

    /**
     * 移除表中所有与 value 相等的值
     */
    public long lrem(String groupCode, String key, String value) {
        return getRedisTemplate(groupCode).opsForList().remove(key, 0, value);
    }

    /**
     * 从队首弹出一个元素
     */
    public String lpop(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForList().leftPop(key);
    }

    public String rpop(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForList().rightPop(key);
    }

    public long rpush(String groupCode, String key, String value) {
        return getRedisTemplate(groupCode).opsForList().rightPush(key, value);
    }

    public long lpush(String groupCode, String key, String value) {
        return getRedisTemplate(groupCode).opsForList().leftPush(key, value);
    }

    public List<String> lrange(String groupCode, String key, long start, long end) {
        return getRedisTemplate(groupCode).opsForList().range(key, start, end);
    }


    public long rPush(String group, String key, String... values) {
        return getRedisTemplate(group).opsForList().rightPushAll(key, values);
    }


    public long llen(String groupCode, String key) {
        return getRedisTemplate(groupCode).opsForList().size(key);
    }

    public void rename(String group, final String old_key, final String new_key) {
        getRedisTemplate(group).rename(old_key, new_key);
    }

    public boolean expire(String group, final String key, final long time) {
        return getRedisTemplate(group).expire(key, time, TimeUnit.SECONDS);
    }

    /**
     * ---------------------------------------------------------------------------------
     * -- 通用排队倒计时，用于排队开宝箱等玩法  - guoliping/2021-11-13
     * --
     * -- lua的核心逻辑：
     * --  1）list元素结构为：${倒计时开始时刻}|${排队条目ID}，${倒计时开始时刻}初始为0，开始后为 yyyyMMddHHmmss（为便于维护不用秒数）
     * --  2）先验证现场是否被破坏（队首元素 和 将要处理的倒计时条目是否相等）
     * --  3）若 队首元素的头部为 0，则初始化启动数据
     * --  4）已启动的倒计时为0以下则移除倒计时条目
     * --
     * --  返回结果：1：成功， 非1：失败（此时数据没有变化）
     * ---------------------------------------------------------------------------------
     */
    public long lineUpCountdown(String groupCode, String listKey, String itemKey, Long left, String itemId, Long startTime, Long nowTime, Long expireSeconds) {
        List<String> keys = Lists.newArrayList(listKey, itemKey);
        List<String> argv = Lists.newArrayList(left.toString(), itemId, startTime.toString(), nowTime.toString(), expireSeconds.toString());
        return this.executeLua(groupCode, "line_up_countdown.lua", Long.class, keys, argv);
    }


    /**
     * 把事项添加到队列，只能实现先进先出需求，不存在插队情况。
     * <p>
     * 出队的时候调用  popLineUp(java.lang.String, java.lang.String)
     *
     * @param queueName       队列名称
     * @param subQueueName    子队列名称
     * @param id              队列元素唯一id,避免入队内容有重复时产生的冲突
     * @param content         执行内容
     * @param intervalSeconds 距离上1个元素出队列的时间间隔,如果加入队列时里面元素为空，则不需要排队
     * @return 预计出队列时间, 单位秒
     */
    public long addLineUp(String groupCode, String queueName, String subQueueName, String id, String content, long intervalSeconds) {
        queueName = "que:" + queueName;
        if (StringUtil.isBlank(subQueueName)) {
            subQueueName = "sub";
        }
        List<String> keys = Lists.newArrayList(queueName);
        List<String> argv = Lists.newArrayList(id, content, intervalSeconds + "", System.currentTimeMillis() / 1000 + "", subQueueName);
        return Convert.toLong(this.executeLua(groupCode, "lineUpAdd.lua", Long.class, keys, argv), 0);
    }

    /**
     * 从队列中取出到期的内容
     *
     * @param queueName 队列名称
     * @return 队列中内容，如果返回空则表示没有相关内容到期出队列
     */
    public String popLineUp(String groupCode, String queueName) {
        queueName = "que:" + queueName;
        List<String> keys = Lists.newArrayList(queueName);
        List<String> argv = Lists.newArrayList(System.currentTimeMillis() / 1000 + "");
        return this.executeLua(groupCode, "lineUpPop.lua", String.class, keys, argv);
    }

    public long zAddWithLimitLen(String groupCode, String key, String member, long score, long maxLen, String duplicatedCheckKey, String checkMember) {
        List<String> keys = Lists.newArrayList(key, StringUtil.trim(duplicatedCheckKey));
        List<String> argv = Lists.newArrayList(member, String.valueOf(score), String.valueOf(maxLen), checkMember);
        return this.executeLua(groupCode, "zadd_with_limit_len.lua", Long.class, keys, argv);
    }

    /**
     * string 累积 step，当 step>=0 时不能超过 limit， 当 step<0时不能小于limit
     *
     * @return 返回2个元素的list， 第一个元素是结果码， 第二个元素是分值
     * <p>
     * 返回码
     * 1: 累积 step 后不违反 limit 限定，返回累积后的新值
     * 2: 允许部分增加时，累计部分值后，刚好等于 limit
     * <p>
     * <p>
     * 返回值
     * -1：step>=0时递增失败（递增后会大于limit），返回旧值
     * -2：step<0时递减失败 （递减后会小于limit），返回旧值
     * 1: 完整累计step后的新值（没有违反limit，也不是部分累计）
     * 2: 实际操作的增量值（一定小于 step）
     */
    public List<Long> incrValueWithLimit(String groupCode, final String key, final long step, long limit, boolean canPartialAdd) {
        List<String> keys = Lists.newArrayList(key);
        List<String> argv = Lists.newArrayList(String.valueOf(step), String.valueOf(limit), String.valueOf(canPartialAdd ? 1 : 0));
        String result = this.executeLua(groupCode, "string_incr_with_limit.lua", String.class, keys, argv);
        List<Long> array = JSON.parseArray(result, Long.class);
        return array;
    }


    /**
     * string 累积 step，当 step>=0 时不能超过 limit， 当 step<0时不能小于limit
     * ---------------------------------------------------------------
     * 返回码
     * 1: 累积 step 后不违反 limit 限定，返回累积后的新值
     * 2: 允许部分增加时，累计部分值后，刚好等于 limit
     * ---------------------------------------------------------------
     * 返回值
     * -1：step>=0时递增失败（递增后会大于limit），返回旧值
     * -2：step<0时递减失败 （递减后会小于limit），返回旧值
     * 1: 完整累计step后的新值（没有违反limit，也不是部分累计）
     * 2: 实际操作的增量值（一定小于 step）
     *
     * @param seq              去重seq,如果seq有重复，返回值是之前状态的值
     * @param step             改变步长
     * @param limit            累积值限制
     * @param canPartialAdd    增加后，超过limit时，是否允许部分增长
     * @param seqExpireSeconds seq去重时限
     * @return -1：step>=0时递增失败（递增后会大于limit），返回旧值
     */
    public List<Long> incrValueWithLimitSeq(String groupCode, final String seq, final String key, final long step, long limit, boolean canPartialAdd, long seqExpireSeconds) {
        List<String> keys = Lists.newArrayList(key);
        List<String> argv = Lists.newArrayList(String.valueOf(step), String.valueOf(limit), String.valueOf(canPartialAdd ? 1 : 0), seq, seqExpireSeconds + "");
        String result = this.executeLua(groupCode, "seq/string_incr_with_limit_seq.lua", String.class, keys, argv);
        return JSON.parseArray(result, Long.class);
    }

    /**
     *
     * @param groupCode
     * @param seq
     * @param key
     * @param score
     * @param seqExpireSeconds
     * @return [newScore, code]
     * newScore：执行incr后的新值
     * code：1，本次执行seq未重复，成功执行；0，本次执行seq已存在，返回的是首次执行成功的newScore；-1，出现了预料之外的异常
     */
    public List<Long> incrValueWithSeq(String groupCode, final String seq, final String key, final long score, long seqExpireSeconds) {
        String result = this.executeLua(groupCode, "seq/string_incr_with_seq.lua", String.class, ImmutableList.of(key, seq), ImmutableList.of(String.valueOf(score), String.valueOf(seqExpireSeconds)));
        return JSON.parseArray(result, Long.class);
    }

    /**
     * 复制redis 数据
     *
     * @param oldRedisGroup 源头redis
     * @param newRedisGroup 目标 redis
     * @param key           redis key
     */
    public KeyCopyResult copy(String oldRedisGroup, String newRedisGroup, String key) {
        DataType dataType = this.getRedisTemplate(oldRedisGroup).type(key);
        RedisDataCopy redisDataCopy = redisDataCopyFactory.getInstance(dataType, key);
        return redisDataCopy.copy(oldRedisGroup, newRedisGroup, key);
    }

    public void unlink(String group, final List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        this.getRedisTemplate(group).executePipelined((RedisCallback<Object>) connection -> {
            keys.forEach(key -> connection.unlink(key.getBytes()));
            return null;
        });
    }

    /**
     * 更新任务
     */
    public UpdateTaskResult updateTask(String groupCode, UpdateTaskReq updateTaskReq) {
        Clock clock = new Clock();
        List<String> keys = Lists.newArrayList();
        String reqContent = JSON.toJSONString(updateTaskReq);
        List<String> argv = Lists.newArrayList(reqContent);
        String result = this.executeLua(groupCode, "update_task.lua", String.class, keys, argv);
        log.info("updateTask done,group:{},clock:{},req:{},result:{}", groupCode, clock.tag(), reqContent, result);
        return JSON.parseObject(result, UpdateTaskResult.class);
    }

    /**
     * get & del
     */
    public String GetAndDel(String groupCode, String key) {
        List<String> argv = new ArrayList<>();
        List<String> keys = Lists.newArrayList(key);
        return this.executeLua(groupCode, "get_and_del.lua", String.class, keys, argv);
    }

}
