package com.yy.gameecology.activity.service.datatransfer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.KeyCopyResult;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CacheService;
import com.yy.gameecology.common.consts.ActStatus;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.model.gameecology.ComponentStoragePolicy;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-08-22 17:24
 **/
@Service
public class RedisDataTransferService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    /**
     * 组件id  split : 的索引下标
     * act:2022081001:hdzj_cmpt:2014:0:seq:1661759193182_1828691651_2176989064_20423
     */
    private static final int KEY_CMPT_INDEX = 3;

    /**
     * 组件索引 split : 的索引下标
     * act:2022081001:hdzj_cmpt:2014:0:seq:1661759193182_1828691651_2176989064_20423
     */
    private static final int KEY_CMPT_INDEX_INDEX = 4;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    /**
     * 目标归档库，数据复制完成将写入这个标记位
     */
    public static final String ACT_DONE_ARCHIVE_SUFFIX = "done_archive_tag";

    public static final String NEED_COPY_ACT_LIST = "hdzk_need_copy_act_list";

    public static final String NEED_DEL_ACT_LIST = "hdzk_need_del_act_list";

    private static final String DEL_DONE_HASH_KEY = "zk_del_done_act_id_hash";

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Lazy
    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private CacheService cacheService;

    public String popNeedCopyActId() {
        return actRedisGroupDao.lpop(RedisConfigManager.DEFAULT_GROUP_CODE, NEED_COPY_ACT_LIST);
    }

    public String popNeedDelActId(){
        return actRedisGroupDao.lpop(RedisConfigManager.DEFAULT_GROUP_CODE, NEED_DEL_ACT_LIST);
    }

    public void addCopyRedisTask(long actId) {
        actRedisGroupDao.rpush(RedisConfigManager.DEFAULT_GROUP_CODE, NEED_COPY_ACT_LIST, Convert.toString(actId));
    }

    public void addDelRedisTask(long actId) {
        actRedisGroupDao.rpush(RedisConfigManager.DEFAULT_GROUP_CODE, NEED_DEL_ACT_LIST, Convert.toString(actId));
    }

    /**
     * 复制redis数据
     */
    public void copyRedisData(long actId) {
        Clock clock = new Clock();
        String actGroupArchive = redisConfigManager.getArchiveGroupCode(actId);
        Assert.isTrue(StringUtils.isNotBlank(actGroupArchive), "未配置冷化redis group code,actId:" + actId);

        ActivityInfoVo activityInfo = hdztRankingThriftClient.queryActivityNoCache(actId);
        Assert.isTrue(!ActStatus.ARCHIVE.equals(activityInfo.getStatus()), "活动数据已归档，无法复制:" + actId);
        String sourceGroup = redisConfigManager.getGroupCode(actId, true);

        log.info("copyRedisData begin,actId:{}", actId);

        //扫描所有key，fixSourceGroup==固定返回热库分组
        Set<String> redisKeys = scanActRedisKey(actId, true);
        clock.tag();


        Set<String> realCopyKeys = filterNeedCopyKey(actId, redisKeys);

        //copy 到冷化库
        List<KeyCopyResult> keyCopyResults = copyRedisKey(actId, sourceGroup, actGroupArchive, realCopyKeys);
        clock.tag();

        //检查key复制是否完整
        checkKeys(actId, realCopyKeys);
        clock.tag();

        //写入完成标记位
        actRedisGroupDao.set(actGroupArchive, Const.addActivityPrefix(actId, ACT_DONE_ARCHIVE_SUFFIX), DateUtil.format(new Date()));

        //如流通知
        sendCopyDoneMsg(actId, redisKeys.size(), redisKeys.size() - realCopyKeys.size(), sourceGroup, actGroupArchive, keyCopyResults, clock);

        log.info("transferRedisData done,actId:{},cost:{}", actId, clock.tag());
    }

    /**
     * 是否已完成数据归档
     */
    public boolean idDoneArchive(long actId){
        String archiveGroup = redisConfigManager.getArchiveGroupCode(actId);
        String doneTag = actRedisGroupDao.get(archiveGroup, Const.addActivityPrefix(actId, ACT_DONE_ARCHIVE_SUFFIX));
        return StringUtils.isNotBlank(doneTag);
    }

    public boolean isDoneDelSourceRedis(long actId) {
        String sourceGroup = redisConfigManager.getGroupCode(actId, true);
        return actRedisGroupDao.hExists(sourceGroup, DEL_DONE_HASH_KEY, String.valueOf(actId));
    }

    /**
     * 将原来redis数据删除
     */
    public void delRedisData(long actId) {
        Clock clock = new Clock();
        ActivityInfoVo activityInfo = hdztRankingThriftClient.queryActivityNoCache(actId);
        Assert.isTrue(ActStatus.ARCHIVE.equals(activityInfo.getStatus()), "活动非归档状态:" + actId);
        Assert.isTrue(idDoneArchive(actId), "复制redis数据到pika未完成:" + actId);

        log.info("delRedisData begin,actId:{}", actId);
        //fixSourceGroup==固定返回热库分组
        Set<String> redisKeys = scanActRedisKey(actId, true);

        String sourceGroup = redisConfigManager.getGroupCode(actId, true);
        //将原来老库数据删除
        unlinkRedisKey(sourceGroup, redisKeys);

        //插入一个删除完成的key
        actRedisGroupDao.hset(sourceGroup, DEL_DONE_HASH_KEY, String.valueOf(actId), DateUtil.getNowYyyyMMddHHmmss());

        //如流通知
        sendDelDoneMsg(actId, redisKeys.size(), sourceGroup, clock);

        log.info("delRedisData done,actId:{},key size:{},cost:{}", actId, redisKeys.size(), clock.tag());
    }


    public Set<String> scanActRedisKey(long actId, boolean fixSourceGroup) {
        Clock clock = new Clock();
        String group = redisConfigManager.getGroupCode(actId, fixSourceGroup);
        Set<String> redisKeys = Sets.newHashSet();
        String scanRankingKeyPattern = Const.GEPM.getParamValue("scan_ranking_key_pattern", "act:%s:*");
        String[] patterns = scanRankingKeyPattern.split(",");
        for (String pattern : patterns) {
            Set<String> curKeys = actRedisGroupDao.scanKey(group, String.format(pattern, actId), 1000);
            if (CollectionUtils.isNotEmpty(curKeys)) {
                redisKeys.addAll(curKeys);
            }
            clock.tag();
        }
        log.info("scanActRedisKey actId:{},size:{},cost:{}", actId, redisKeys.size(), clock.tag());
        return redisKeys;
    }

    /**
     * 校验目标库redis key是否完整
     */
    private void checkKeys(long actId, Set<String> sourceKey) {
        Map<String, Boolean> targetKeys = scanActArchiveRedisKey(actId);
        boolean copyNotComplete = false;
        long notCompleteCount = 0;
        for (String key : sourceKey) {
            if (!targetKeys.containsKey(key)) {
                log.warn("checkKeys,key target not exist,actId:{},key:{}", actId, key);
                copyNotComplete = true;
                notCompleteCount++;
            }
        }
        if (copyNotComplete) {
            String msg = String.format("冷化复制redis异常,actId:%s,key缺失数量:%s", actId, notCompleteCount);
            log.error(msg);
            throw new RuntimeException(msg);
        }
    }

    private Map<String, Boolean> scanActArchiveRedisKey(long actId) {
        Clock clock = new Clock();
        long totalKey = 0;
        String archiveRedisGroup = redisConfigManager.getArchiveGroupCode(actId);
        Map<String, Boolean> redisKeys = Maps.newHashMap();
        String scanRankingKeyPattern = Const.GEPM.getParamValue("scan_ranking_key_pattern", "act:%s*");
        String[] patterns = scanRankingKeyPattern.split(",");
        for (String pattern : patterns) {
            Set<String> curKeys = actRedisGroupDao.scanKey(archiveRedisGroup, String.format(pattern, actId), 1000);
            curKeys.forEach(p -> redisKeys.put(p, true));
            clock.tag();
        }
        log.info("scanActRedisKey actId:{},size:{},cost:{}", actId, totalKey, clock.tag());
        return redisKeys;
    }

    private List<KeyCopyResult> copyRedisKey(long actId, String sourceGroup, String actGroupArchive, Set<String> redisKeys) {
        List<KeyCopyResult> keyCopyResults = Lists.newArrayList();
        for (String key : redisKeys) {
            KeyCopyResult keyCopyResult = actRedisGroupDao.copy(sourceGroup, actGroupArchive, key);
            if (keyCopyResult != null) {
                keyCopyResults.add(keyCopyResult);
            }
        }

        return keyCopyResults;
    }

    private void unlinkRedisKey(String group, Set<String> redisKeys) {
        Clock clock = new Clock();
        List<List<String>> keyArray = Lists.partition(Lists.newArrayList(redisKeys), 1000);
        for (List<String> batchKey : keyArray) {
            actRedisGroupDao.unlink(group, batchKey);
            log.info("unlinkRedisKey,group:{},key:{}", group, JSON.toJSON(batchKey));
            clock.tag();
        }
        log.info("unlinkRedisKey done,total:{},cost:{}", redisKeys.size(), clock.tag());
    }

    /**
     * 根据策略过滤需要迁移的key
     */
    private Set<String> filterNeedCopyKey(long actId, Set<String> keys) {
        Set<String> result = Sets.newHashSet();

        for (String key : keys) {
            //全局黑名单策略
            if (isBlackList(actId, key)) {
                log.info("filterNeedCopyKey is black key,actId:{},key:{}", actId, key);
                continue;
            }
            //组件级别 白名单策略,没配置默认通过
            long cmptId = filterCmptId(actId, key);
            long cmptIndex = filterCmptIndex(actId, key);
            if (cmptId > 0) {
                ComponentStoragePolicy policy = cacheService.getComponentStoragePolicy(actId, cmptId, cmptIndex);
                if (policy != null && policy.getPolicy() == 0) {
                    log.info("filterNeedCopyKey cmpt key,not storage,actId:{},key:{}", actId, key);
                    continue;
                }
                if (policy != null && policy.getPolicy() == 1 && !inCmptWhiteList(policy, key)) {
                    log.info("filterNeedCopyKey cmpt key,not in white list,actId:{},key:{}", actId, key);
                    continue;
                }
            }

            result.add(key);
        }


        return result;
    }

    /**
     * 从 key提取组件id
     */
    private long filterCmptId(long actId, String key) {
        //非组件key
        String keySuffix = Const.addActivityPrefix(actId, Const.COMPONENT_KEY_SUFFIX);
        if (!key.startsWith(keySuffix)) {
            return 0;
        }
        return Convert.toLong(key.split(":")[KEY_CMPT_INDEX],0);
    }

    /**
     * 从key提取组件索引
     */
    private long filterCmptIndex(long actId, String key) {
        //非组件key
        String keySuffix = Const.addActivityPrefix(actId, Const.COMPONENT_KEY_SUFFIX);
        if (!key.startsWith(keySuffix)) {
            return 0;
        }
        return Convert.toLong(key.split(":")[KEY_CMPT_INDEX_INDEX],0);
    }

    /**
     * 是否在组件白名单内
     */
    private boolean inCmptWhiteList(ComponentStoragePolicy policy, String key) {
        //没设置白名单，代表全部都要存储
        if (StringUtils.isEmpty(policy.getWhiteList())) {
            return true;
        }
        String[] patternArray = policy.getWhiteList().split(",");
        Set<String> patterns = Sets.newHashSet();
        for (String item : patternArray) {
            patterns.add(String.format(item, policy.getActId(), policy.getCmptId(), policy.getCmptUseInx()));
        }
        return matches(patterns, key);
    }

    /**
     * 是否是迁移黑名单key
     */
    private boolean isBlackList(long actId, String key) {
        Set<String> blackList = getBlackList();
        Set<String> blackListPatterns = Sets.newHashSet();
        for (String item : blackList) {
            blackListPatterns.add(String.format(item, actId));
        }
        return matches(blackListPatterns, key);
    }

    private boolean matches(Set<String> patterns, String key) {
        for (String pattern : patterns) {
            if (Pattern.matches(pattern, key)) {
                return true;
            }
        }
        return false;
    }

    private Set<String> getBlackList() {
        String[] blackConfig = Const.GEPM.getParamValue("ge_redis_copy_black_list_key", "act:%s:MatchResultNotifyComponent:.*").split(",");
        return Sets.newHashSet(blackConfig);
    }


    /**
     * 复制完成通知
     */
    private void sendCopyDoneMsg(long actId, long totalKey, long blackListKey, String sourceGroup, String targetGroup, List<KeyCopyResult> keyCopyResults, Clock clock) {
        try {
            ActivityInfoVo hdztActivity = hdztRankingThriftClient.queryActivityInfo(actId);
            long noExpireTime = keyCopyResults
                    .stream().filter(p -> Convert.toLong(p.getExpireSeconds(), 0) <= 0).count();
            String expireTimeColor = noExpireTime > 0 ? "<font color=\"red\">请看日志排查，" : "<font color=\"green\">";
            String buf = "### <font color=\"green\">【中控->redis数据冷化复制完成】</font>\n" + "#### [" + actId + "]" + hdztActivity.getActName() + "\n" +
                         "总key数：" + totalKey + "\n" +
                         "黑名单key数：" + blackListKey + "\n" +
                         "来源redis分组：" + sourceGroup + "\n" +
                         "目标redis分组：" + targetGroup + "\n" +
                         expireTimeColor + "没有过期时间key数量：" + noExpireTime + "</font>\n" +
                         "耗时：" + clock + "\n" +
                         "<font color=\"green\">(已进行目标库redis key复制完整性校验ok)</font>\n";
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, buf, null);
        } catch (Throwable e) {
            log.error("sendCopyDoneMsg exception@actId:{}, err:{}", actId, e.getMessage(), e);
        }
    }

    /**
     * 删除
     */
    private void sendDelDoneMsg(long actId, long totalKey, String sourceGroup, Clock clock) {
        try {
            ActivityInfoVo hdztActivity = hdztRankingThriftClient.queryActivityInfo(actId);
            String buf = "### <font color=\"green\">【中控->redis热库数据清理完成】</font>\n" + "#### [" + actId + "]" + hdztActivity.getActName() + "\n" +
                    "总key数：" + totalKey + "\n" +
                    "redis分组：" + sourceGroup + "\n" +
                    "耗时：" + clock.tag() + "\n";
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, buf, null);
        } catch (Throwable e) {
            log.error("sendDelDoneMsg exception@actId:{}, err:{}", actId, e.getMessage(), e);
        }
    }

}
