package com.yy.gameecology.hdzj.element.history.attr;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.yy.apphistory.y2022.act2022jyjfs.bean.RoleConfig;
import com.yy.apphistory.y2022.act2022jyjfs.bean.ShieldPeriod;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption: 交友积分赛组件
 * @Date: 2022/02/22 19:19
 * @Modified:
 */
@Data
public class ActJYJFSComponentAttr extends ComponentAttr {
    /**
     * 交友分组分组系统规则id
     */
    private int roleGroupRuleId;

    /**
     * 灰度周星模式，0=正常模式，查询营收接口,1= 查询中台榜单 ,2=从营收查找数据并且只显示白名单内的主播
     */
    private int greyWeekStartMode;

    /**
     * 年度资格积分阈值
     */
    private long integralThreshold;
    /**
     * 角色相关配置
     */
    private List<RoleConfig> roleConfigs = Lists.newArrayList();

    /**
     * 年度资格上报Item
     */
    private String ZGItem;
    /**
     * 积分上报Item
     */
    private String integralItem;

    /**
     * 碎片上报Item
     */
    private String debrisItem;

    /**
     * 积分倍数上报Item
     */
    private String integralMaxItem;

    /**
     * 积分倍数上报Item -积分榜单外显（特殊处理，需要按荣耀值排序）
     */
    private String integralMaxItem2 = "JY_JF_MAX2";

    /**
     * 积分倍数
     */
    private long integralMaxMultiple = 1000000000L;
    /**
     * 一个礼物累计的榜单值
     **/
    private int giftScore = 52000;

    /**
     * 积分倍数上报限制时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date integralMaxLastTime;

    /**
     * 赏金pk延迟结算的时间，秒,最低不能小于5分钟
     */
    private int pkDelaySettleSec;
    /**
     * 赏金pk延迟结算的榜单
     */
    private List<Long> pkRankId;

    /**
     * 赏金pk延迟结算的标识
     */
    private String pkDelaySettleTag;


    /**
     * 兑换1积分需要的碎片
     */
    private int exchangeRate;
    /**
     * 个人限制兑换积分数量
     */
    private int personalLimit;
    /**
     * 总限制兑换积分数量
     */
    private int totalLimit;
    /**
     * 积分商城组件索引
     */
    private long integralShopIndex;

    /**
     * 积分商城货币
     */
    private String integralShopCurrency;

    /**
     * 碎片商城组件索引
     */
    private long debrisShopIndex;

    /**
     * 碎片商城货币
     */
    private String debrisShopCurrency;


    /**
     * 乱斗高光横幅svga
     **/
    private String ldHighlightSvgaUrl = "https://lxcode.bs2cdn.yy.com/98af85a3-5567-4522-adc8-c80f0c6a391c.svga";
    /**
     * 周星高光横幅svga
     **/
    private String weekStarHighlightSvgaUrl = "https://lxcode.bs2cdn.yy.com/d281c3bf-5454-422a-8807-23c0bc9b8c9f.svga";
    /**
     * 主持获得年度资格横幅svga
     **/
    private String anchorYearQualificationSvgaUrl = "";
    /**
     * 获得积分/碎片弹窗消息模板
     * 三个占位符：{typeName} 、{taskName}、{count}
     **/
    private String obtainPointNotifyMsgTemplate = "<p>恭喜您在{taskName}玩法中获得了{count}{typeName}</p><p>前往商店兑换</p>";
    /**
     * 碎片自动合成积分弹窗消息模板
     * 两个占位符：{debrisCount} 、{pointCount}
     **/
    private String pointCompositeNotifyTemplate = "<p>恭喜您</p><p>获得了{debrisCount}碎片,自动合成了{pointCount}积分</p><p>前往商城兑换</p>";

    // 重构后的配置
//    /**
//     * 限定的角色id,超主=50004,天团=50014
//     **/
//    private List<Long> roleIds;
//
//    /**
//     * 角色与碎片结算榜单映射,key=角色id(50004/50014),value=${rankId}_${phaseId},榜单id与阶段id通过下划线组合
//     **/
//    private Map<Long, String> roleIdDebrisRankMap;
//
//    /**
//     * 碎片奖励配置
//     * key = ${rankId}_${phaseId},榜单id与阶段id通过下划线组合
//     * value = 礼物结算碎片的阈值,<礼物荣耀值，奖励碎片数量>
//     **/
//    private Map<String, Map<Long, Long>> debrisThresholdAwardMap;
//    /**
//     * 积分查询配置
//     **/
//    private List<IntegralQueryConfig> integralQueryConfigs;
//    /**
//     * 乱斗结算配置
//     **/
//    private List<LDSettlementConfig> ldSettlementConfigs;

    /**
     * 积分赛期间,可能有其他活动在跑,有可能需要屏蔽掉重叠的时间段
     * 屏蔽时间段列表
     **/
    private List<ShieldPeriod> shieldPeriods;

    /**
     * 礼物图标
     **/
    private String giftIcon;

    /**
     * 礼物名称
     **/
    private String giftName;

    private Map<String, List<Long>> milestoneScoreMap;

    private String weekStarTransMondayV2 = "20220620";

    private long prevActId;

    private long prevCmptUseInx;

    public List<Long> getAnchorRoles() {
        return roleConfigs.stream().map(RoleConfig::getRoleId).collect(Collectors.toList());
    }


    public RoleConfig getRoleConfig(long roleId) {
        return roleConfigs.stream().filter(item -> item.getRoleId() == roleId).findFirst().orElse(null);
    }

    public Map<Long, Integer> getRankToTypeMap() {
        return roleConfigs.stream().collect(Collectors.toMap(RoleConfig::getWeekStartRankId, RoleConfig::getWeekStartType));
    }

    public long getShieldStartTime() {
        if (CollectionUtils.isEmpty(shieldPeriods)) {
            return 0;
        }
        shieldPeriods = shieldPeriods.stream()
                .sorted(Comparator.comparingLong(ShieldPeriod::getShieldStartTime))
                .collect(Collectors.toList());

        return shieldPeriods.get(0).getShieldStartTime();
    }

    public long getShieldEndTime() {
        if (CollectionUtils.isEmpty(shieldPeriods)) {
            return 0;
        }
        shieldPeriods = shieldPeriods.stream()
                .sorted(Comparator.comparingLong(ShieldPeriod::getShieldEndTime).reversed())
                .collect(Collectors.toList());

        return shieldPeriods.get(0).getShieldEndTime();
    }

    public ShieldPeriod getShieldPeriod(long now) {
        if (CollectionUtils.isEmpty(shieldPeriods)) {
            return null;
        }

        for (ShieldPeriod shieldPeriod : shieldPeriods) {
            if (shieldPeriod.getShieldStartTime() <= now && now <= shieldPeriod.getShieldEndTime()) {
                return shieldPeriod;
            }
        }

        return null;
    }
}
