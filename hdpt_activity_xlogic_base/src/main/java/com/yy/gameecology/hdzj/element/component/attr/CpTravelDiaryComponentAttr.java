package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;

import java.util.List;

@Data
public class CpTravelDiaryComponentAttr extends ComponentAttr  {

    @ComponentAttrField(labelText = "业务ID",remark = "交友500 聊天室 810")
    private long busiId;

    @ComponentAttrField(labelText = "全程阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "cp日榜单id")
    private long cpDailyRankId;

    @ComponentAttrField(labelText = "cp总榜单id")
    private long cpRankId;

    @ComponentAttrField(labelText = "cp总榜-用户对主持贡献榜单id")
    private long cpContributeRankId;

    @ComponentAttrField(labelText = "cp总榜-主持对用户贡献榜单id")
    private long cpAntiContributeRankId;


    @ComponentAttrField(labelText = "cp 用户对主持日贡献榜单id")
    private long cpContributeDailyRankId;

    @ComponentAttrField(labelText = "cp榜单拉取数量")
    private long rankLimit;

    @ComponentAttrField(labelText = "累计任务配置", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TravelDiaryMissionConfig.class)
    })
    private List<TravelDiaryMissionConfig> missions;

    @ComponentAttrField(labelText = "每日任务配置", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TravelDiaryMissionConfig.class)
    })
    private List<TravelDiaryMissionConfig> dailyMissions;


    @ComponentAttrField(labelText = "日榜奖池映射",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TravelDiaryAwardMap.class)})
    private List<TravelDiaryAwardMap> dailyAwardMap;


    @ComponentAttrField(labelText = "总榜奖池映射",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = TravelDiaryAwardMap.class)})
    private List<TravelDiaryAwardMap> awardMap;

    @ComponentAttrField(labelText = "广播模板", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broTemplate;

    @ComponentAttrField(labelText = "累计任务奖池不足时，发奖奖池ID")
    private long travelTaskId;

    @ComponentAttrField(labelText = "累计任务奖池不足时，发奖奖包ID")
    private long travelPackageId;

    @ComponentAttrField(labelText = "累计任务奖池不足时，进场秀名称")
    private String travelShowName;

    @ComponentAttrField(labelText = "累计任务奖池不足时，进场秀图标")
    private String travelShowIcon;

    @ComponentAttrField(labelText = "旅行日记累计任务总奖池",remark = "单位厘")
    private long totalPool;


    @ComponentAttrField(labelText = "获取webdb信息的templateType",remark = "交友 1 聊天室 810")
    private int templateType = 810;



}
