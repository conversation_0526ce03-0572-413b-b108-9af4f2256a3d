package com.yy.gameecology.hdzj.element.doc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sun.javadoc.AnnotationDesc;
import com.sun.javadoc.ClassDoc;
import com.sun.javadoc.MethodDoc;
import com.sun.javadoc.RootDoc;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.*;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.DropDownOption;
import com.yy.gameecology.hdzj.element.history.KingLiftComponent;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组件文档生成器
 *
 * <AUTHOR>
 * @date 2022/7/15 10:33
 **/
@Component
public class ComponentDocGenerator extends DocGenerator {
    private static String PackageRoot;

    @Autowired
    private GameecologyDao gameecologyDao;

    public void init() {
        initPackageRoot();
        // 手动初始化:测试环境和线上一般不需要这段代码,自动初始化会浪费资源
        // initDoc();
    }

    private void initPackageRoot() {
        try {
            String projectRoot = new File(".").getCanonicalPath();
            final boolean match = projectRoot.endsWith("hdpt_activity_xlogic_app");
            if (!match) {
                projectRoot += "/hdpt_activity_xlogic_app";
            }
            String srcRoot = projectRoot + "/src/main/java";
            PackageRoot = srcRoot + "/com/yy/gameecology";
        } catch (IOException e) {
            logger.error("init error", e);
        }
    }

    public static final String TEST_COMPONENT = "UserPaidGrowthComponent";
    private static final List<String> FILTER_METHOD_LIST = Arrays.asList("getComponentId", "isUniq1UseIndex");

    public void initDoc() {
        List<String> allFiles = this.listAllFile(PackageRoot, false);

        ArrayList<String> list = new ArrayList<>();
        list.add("-doclet");
        list.add(ComponentDocGenerator.class.getName());
        list.addAll(allFiles);
        com.sun.tools.javadoc.Main.execute(list.toArray(new String[0]));

        ClassDoc[] classDocs = ComponentDocGenerator.root.classes();
        for (ClassDoc classDoc : classDocs) {
            classDocMap.put(classDoc.name(), classDoc);
        }
    }

    public ComponentDocBean buildComponentDoc(String classSimpleName) {
        ClassDoc classDoc = classDocMap.get(classSimpleName);

        return this.buildComponentDoc(classDoc);
    }

    public ComponentDocBean buildComponentDoc(ClassDoc classDoc) {
        if (classDoc == null) {
            return null;
        }
        ComponentDocBean docBean = new ComponentDocBean();
        docBean.setName(classDoc.name());
        docBean.setCommentText(classDoc.commentText());
        docBean.setAuthor(getAuthor(classDoc));

        // 字段及注释
        docBean.setFieldCommentBeanList(buildFieldCommentBeans(classDoc));

        // 方法及注释
        // methods()只获取public方法
        // methods(false) 获取所有方法
        docBean.setMethodCommentBeanList(buildMethodCommentBeans(classDoc));

        setReflectValue(classDoc, docBean);
        setEventHandlerAttrParam(docBean);
        attachComponentName(docBean);

        return docBean;
    }

    public void attachAttrVo(ComponentDocBean componentDocBean) {
        // 属性定义
        HdzjComponentAttrDefine query = new HdzjComponentAttrDefine();
        query.setCmptId((int) componentDocBean.getComponentId());
        List<HdzjComponentAttrDefine> attrDefines = gameecologyDao.select(HdzjComponentAttrDefine.class, query, "");

        List<HdzjComponentAttrDefine> parentList = attrDefines.stream()
                .filter(attrDefine -> Objects.equals("#", attrDefine.getPid()))
                .collect(Collectors.toList());

        Map<String, List<HdzjComponentAttrDefine>> subAttrDefineMap = attrDefines.stream()
                .collect(Collectors.toMap(
                        HdzjComponentAttrDefine::getPid,
                        attrDefine -> {
                            List<HdzjComponentAttrDefine> list = new ArrayList<>();
                            list.add(attrDefine);
                            return list;
                        },
                        (newList, oldList) -> {
                            newList.addAll(oldList);
                            return newList;
                        })
                );

        // 配置示例
        Map<String, String> configMap = queryConfigExample(componentDocBean.getComponentId());

        List<ComponentAttrVo> attrVoList = new ArrayList<>();
        for (HdzjComponentAttrDefine attrDefine : parentList) {
            ComponentAttrVo attrVo = buildComponentAttrVo(attrDefine);
            String pid = attrDefine.getCmptId() + attrDefine.getPid() + attrDefine.getPropName();
            List<HdzjComponentAttrDefine> subAttrDefines = subAttrDefineMap.get(pid);
            if (CollectionUtils.isNotEmpty(subAttrDefines)) {
                subAttrDefines.forEach(subAttrDefine -> attrVo.addSubAttr(buildComponentAttrVo(subAttrDefine)));
            }

            if (CollectionUtils.isNotEmpty(subAttrDefines) && !Objects.equals("java.util.Map", attrDefine.getValueType())) {
                try {
                    JSONArray array = JSON.parseArray(configMap.get(attrDefine.getPropName()));
                    attrVo.setConfigExample(array.getJSONObject(0).toJSONString());
                } catch (Exception ex) {

                }
            } else {
                attrVo.setConfigExample(configMap.get(attrDefine.getPropName()));
            }

            attrVoList.add(attrVo);
        }
        componentDocBean.setAttrVoList(attrVoList);

        replaceNumberType(componentDocBean);
        updateMapSubAttr(componentDocBean);
    }

    public List<ComponentDocBean> buildAllComponent(List<Class<?>> targetComponents) {
        List<ComponentDocBean> allComponentDoc = new ArrayList<>();
        List<ClassDoc> classDocList = findWithSuperClass(BaseActComponent.class);

        if (CollectionUtils.isNotEmpty(targetComponents)) {
            List<String> componentNames = targetComponents.stream().map(Class::getName).collect(Collectors.toList());
            classDocList = classDocList.stream().filter(doc -> componentNames.contains(doc.toString())).collect(Collectors.toList());
        }

        for (ClassDoc classDoc : classDocList) {
            ComponentDocBean componentDocBean = buildComponentDoc(classDoc);
            attachAttrVo(componentDocBean);
            allComponentDoc.add(componentDocBean);
        }

        return allComponentDoc;
    }

    public void synAllComponentDocToDB(List<Class<?>> targetComponents) {
        List<ComponentDocBean> allComponentDoc = buildAllComponent(targetComponents);
        for (ComponentDocBean componentDocBean : allComponentDoc) {
            HdzjComponentDoc doc = new HdzjComponentDoc();
            doc.setComponentId(componentDocBean.getComponentId());
            doc.setUpdateTime(new Date());
            doc.setDocJson(JSON.toJSONString(componentDocBean));

            gameecologyDao.insertOrUpdate(HdzjComponentDoc.class, doc, HdzjComponentDoc.TABLE_NAME);
        }
    }

    public ComponentDocBean getComponentDocBean(long componentId) {
        HdzjComponentDoc query = new HdzjComponentDoc();
        query.setComponentId(componentId);

        HdzjComponentDoc doc = gameecologyDao.selectOne(HdzjComponentDoc.class, query, "");
        if (doc == null) {
            return null;
        }

        return JSON.parseObject(doc.getDocJson(), ComponentDocBean.class);
    }

    private void attachComponentName(ComponentDocBean componentDocBean) {
        if (StringUtils.isNotEmpty(componentDocBean.getCommentText())) {
            return;
        }

        HdzjComponentDefine query = new HdzjComponentDefine();
        query.setCmptId(componentDocBean.getComponentId());

        HdzjComponentDefine define = gameecologyDao.selectOne(HdzjComponentDefine.class, query, "");
        if (define == null) {
            return;
        }

        componentDocBean.setCommentText(define.getCmptTitle() + "\r\n" + define.getRemark());

        if (StringUtils.isEmpty(componentDocBean.getAuthor())) {
            componentDocBean.setAuthor(define.getAuthor());
        }
    }

    private Map<String, String> queryConfigExample(long componentId) {
        Map<String, String> configMap = Maps.newHashMap();

        HdzjComponent query = new HdzjComponent();
        query.setCmptId(componentId);

        List<HdzjComponent> components = gameecologyDao.select(HdzjComponent.class, query, "order by ctime desc limit 1");
        if (CollectionUtils.isEmpty(components)) {
            return configMap;
        }

        HdzjComponent first = components.get(0);

        HdzjComponentAttr attrQuery = new HdzjComponentAttr();
        attrQuery.setActId(first.getActId());
        attrQuery.setCmptId(componentId);
        attrQuery.setCmptUseInx(first.getCmptUseInx());

        List<HdzjComponentAttr> attrConfigList = gameecologyDao.select(HdzjComponentAttr.class, attrQuery);
        attrConfigList.forEach(attrConfig -> configMap.put(attrConfig.getName(), attrConfig.getValue()));

        return configMap;
    }

    private void replaceNumberType(ComponentDocBean componentDocBean) {
        Map<String, String> typeNameMap = componentDocBean.getAttr().getFieldList()
                .stream()
                .collect(Collectors.toMap(FieldCommentBean::getName, FieldCommentBean::getTypeName));

        for (ComponentAttrVo attrVo : componentDocBean.getAttrVoList()) {
            if (Objects.equals("Number", attrVo.getType())) {
                attrVo.setType(typeNameMap.getOrDefault(attrVo.getName(), attrVo.getType()));
            }
        }

        componentDocBean.setAttr(null);
    }

    private void updateMapSubAttr(ComponentDocBean componentDocBean) {
        try {
            for (ComponentAttrVo attrVo : componentDocBean.getAttrVoList()) {
                if (!attrVo.getType().startsWith("Map") || CollectionUtils.isEmpty(attrVo.getSubAttrList())) {
                    continue;
                }

                String desc = attrVo.getDesc();
                for (ComponentAttrVo componentAttrVo : attrVo.getSubAttrList()) {
                    desc += "\r\n" + componentAttrVo.getName() + ":" + componentAttrVo.getDesc();
                }
                attrVo.setDesc(desc);
                attrVo.setSubAttrList(null);
            }
        } catch (Exception ex) {
            logger.error("updateMapSubAttr error,doc={}", JSON.toJSONString(componentDocBean), ex);
        }
    }

    private void setEventHandlerAttrParam(ComponentDocBean docBean) {
        if (CollectionUtils.isEmpty(docBean.getMethodCommentBeanList())) {
            return;
        }
        for (MethodCommentBean methodCommentBean : docBean.getMethodCommentBeanList()) {
            if (!methodCommentBean.isEventHandler()) {
                continue;
            }
            for (ParamCommentBean inputParam : methodCommentBean.getInputParams()) {
                if (Objects.equals(inputParam.getTypeName(), docBean.getAttr().getTypeName())
                        && Objects.equals(inputParam.getFullTypeName(), docBean.getAttr().getFullTypeName())) {
                    inputParam.setFieldList(null);
                }
            }
        }
    }

    private boolean hasAnnotation(AnnotationDesc[] annotationDescs, String annotationName) {
        if (annotationDescs == null || annotationDescs.length == 0) {
            return false;
        }
        List<String> annotationTypeNames = Arrays.stream(annotationDescs)
                .map(annotationDesc -> annotationDesc.annotationType().name())
                .collect(Collectors.toList());

        return annotationTypeNames.contains(annotationName);
    }

    private void setReflectValue(ClassDoc classDoc, ComponentDocBean docBean) {
        try {
            Class<?> clz = this.getClass().getClassLoader().loadClass(classDoc.toString());
            Object instance = clz.newInstance();

            // 获取组件id
            Method method = clz.getMethod("getComponentId");
            long componentId = Convert.toLong(method.invoke(instance));
            docBean.setComponentId(componentId);

            for (FieldCommentBean fieldCommentBean : docBean.getFieldCommentBeanList()) {
                String fieldName = fieldCommentBean.getName();
                Field field = clz.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(instance);
                fieldCommentBean.setValue(value);
            }

            method = clz.getMethod("getMyAttrClass");
            Class<?> attrClz = (Class<?>) method.invoke(instance);
            ParamCommentBean attr = new ParamCommentBean();
            attr.setName(attrClz.getSimpleName());
            attr.setTypeName(attrClz.getSimpleName());
            attr.setFullTypeName(attrClz.getName());
            setParamField(attr);
            docBean.setAttr(attr);

        } catch (Exception ex) {
            logger.error("setReflectValue error,docBean={}", JSON.toJSONString(docBean), ex);
        }
    }

    @Override
    protected boolean filterMethod(MethodDoc method) {
        return FILTER_METHOD_LIST.contains(method.name()) || hasAnnotation(method.annotations(), "Override");
    }

    @Override
    protected void afterBuildMethodComment(MethodCommentBean methodCommentBean, MethodDoc method) {
        methodCommentBean.setEventHandler(hasAnnotation(method.annotations(), "HdzjEventHandler"));
    }

    private List<String> listAllFile(String dir, boolean skipSubDir) {
        List<String> filePathList = new ArrayList<>();
        File file = new File(dir);
        File[] files = file.listFiles();
        if (files == null) {
            return filePathList;
        }
        for (File componentFile : files) {
            String subFilePath = dir + File.separator + componentFile.getName();
            if (componentFile.isDirectory()) {
                if (!skipSubDir) {
                    filePathList.addAll(listAllFile(subFilePath, false));
                }
            } else {
                if (!subFilePath.endsWith(".java")) {
                    continue;
                }
                filePathList.add(subFilePath);
            }
        }

        return filePathList;
    }

    public static void main(String[] args) {
        ComponentDocGenerator docGenerator = new ComponentDocGenerator();
        docGenerator.initPackageRoot();
        docGenerator.initDoc();

        ClassDoc classDoc = classDocMap.get(KingLiftComponent.class.getSimpleName());
        ComponentDocBean componentDocBean = docGenerator.buildComponentDoc(classDoc);

        System.out.println(JSON.toJSONString(componentDocBean));

        // receiveDelayAward
    }

    private List<ClassDoc> findWithSuperClass(Class<?> superClass) {
        if (superClass == null) {
            return new ArrayList<>(classDocMap.values());
        }

        return classDocMap.values().stream()
                .filter(classDoc -> classDoc.superclass() != null && Objects.equals(classDoc.superclass().toString(), superClass.getName()))
                .collect(Collectors.toList());
    }

    private ComponentAttrVo buildComponentAttrVo(HdzjComponentAttrDefine attrDefine) {
        ComponentAttrVo attrVo = new ComponentAttrVo();
        attrVo.setName(attrDefine.getPropName());
        attrVo.setDefaultValue(attrDefine.getDefaultValue());
        attrVo.setConfigExample("");
        attrVo.setDesc(getDesc(attrDefine));
        attrVo.setType(getValueType(attrDefine));

        return attrVo;
    }

    private String getDesc(HdzjComponentAttrDefine attrDefine) {
        StringBuilder desc = new StringBuilder(attrDefine.getLabelText());
        boolean eq = Objects.equals("DropDown", attrDefine.getPropType());
        if (eq) {
            String extJson = attrDefine.getExtJson();

            HdzjComponentAttrDic dic = new HdzjComponentAttrDic();
            dic.setDropDownSource(JSON.parseObject(extJson).getString("dropDownSource"));

            dic = gameecologyDao.selectOne(HdzjComponentAttrDic.class, dic, null);

            List<DropDownOption> options = JSON.parseArray(dic.getDropDownList(), DropDownOption.class);
            options.forEach(
                    option -> desc.append("\r\n").append(option.getCode()).append(":").append(option.getDesc())
            );
        }
        return desc.toString();
    }

    private String getValueType(HdzjComponentAttrDefine attrDefine) {
        try {
            final boolean eqMap = Objects.equals("java.util.Map", attrDefine.getValueType());
            if (eqMap) {
                // {"key1":"java.lang.String","value":"java.util.List","listValue":"java.lang.Object"}
                // Map<{key},{value}>
                JSONObject extJson = JSON.parseObject(attrDefine.getExtJson());
                String listValue = extJson.getString("listValue");
                String value = extJson.getString("value");
                value = getLastPart(value, ".");

                int level = extJson.size() - 1;
                boolean isListValue = StringUtils.isNotEmpty(listValue);
                if (isListValue) {
                    level = level - 1;
                    value = "List<" + getLastPart(listValue, ".") + ">";
                }

                while (level > 0) {
                    value = "Map<{key},{value}>"
                            .replace("{key}", getLastPart(extJson.getString("key" + level), "."))
                            .replace("{value}", value);
                    level--;
                }

                return value;
            }

            final boolean eqArrayList = Objects.equals("java.util.List", attrDefine.getValueType());
            if (eqArrayList) {
                // {"valueType":"java.lang.String"}
                JSONObject extJson = JSON.parseObject(attrDefine.getExtJson());

                return "List<" + getLastPart(extJson.getString("valueType"), ".") + ">";
            }
        } catch (Exception ex) {
            logger.error("getValueType error,define={}", JSON.toJSONString(attrDefine), ex);
        }

        return getLastPart(attrDefine.getValueType(), ".");
    }


    /**
     * 文档根节点
     */
    private static RootDoc root;

    /**
     * javadoc调用入口
     *
     * @param root
     * @return
     */
    public static boolean start(RootDoc root) {
        ComponentDocGenerator.root = root;
        return true;
    }
}
