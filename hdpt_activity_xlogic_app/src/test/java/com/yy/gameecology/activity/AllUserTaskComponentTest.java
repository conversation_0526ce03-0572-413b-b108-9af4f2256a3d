package com.yy.gameecology.activity;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.common.consts.ThreadPoolNames;
import com.yy.gameecology.hdzj.bean.AwardRoll;
import com.yy.gameecology.hdzj.element.redis.AllUserTaskComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-08-07 17:03
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=5"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-5.properties"
})
public class AllUserTaskComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "5");
    }

    @Autowired
    private AllUserTaskComponent allUserTaskComponent;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Test
    public void test() {
        String eventContent = "{\"actId\":2023085001,\"actors\":{50003:\"1640778529\",50004:\"87814665\",50055:\"87814665_2805640284\",51003:\"WX_2023051001\",50060:\"87814665_2805640284\",50014:\"1640778529\"},\"busiId\":500,\"currRound\":366,\"currTaskIndex\":0,\"ekey\":\"\",\"index\":0,\"itemCurrNumMap\":{\"HDLW\":406210},\"itemLevelPassMap\":{\"HDLW\":[666600]},\"member\":\"WX_2023051001\",\"occurTime\":\"2023-08-19 20:00:20\",\"phaseId\":101,\"phaseScore\":243715210,\"rankId\":51,\"rankScore\":243715210,\"roundComplete\":10,\"seq\":\"6e89ebe6-5778-4159-aadf-f1277e6bb10f\",\"startTaskIndex\":0,\"timeKey\":0,\"timestamp\":\"2023-08-19 20:00:20\",\"uri\":2001}";
        TaskProgressChanged event = JSON.parseObject(eventContent, TaskProgressChanged.class);
        List<AwardRoll> awardRolls = Lists.newArrayList();
        AwardRoll awardRoll = new AwardRoll();
        awardRoll.setPrize("大礼物");
        awardRoll.setType(1);
        awardRolls.add(awardRoll);
        allUserTaskComponent.bottomBroadcastApp(event, allUserTaskComponent.getUniqueComponentAttr(2023085001L), 50042952L, 50042952L, awardRolls);
    }

    @Test
    public void scheduleTest() throws Exception{
        log.info("scheduleTest-------begin");
        threadPoolManager.getScheduledThreadPool(ThreadPoolNames.BRO_LAYER_SCHEDULE).schedule(() -> {
                    log.info("scheduleTest-------invoke");
                }
                , 5, TimeUnit.SECONDS);

        Thread.sleep(10000);
    }

}
