package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.actlayer.BabyMissionItem;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerMp4Config;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.activity.service.actresult.builderimpl.FtsHallActResultBuilder;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.consts.LayerViewSupportName;
import com.yy.gameecology.common.consts.ThreadPoolNames;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5123SparkRecord;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5123UserCard;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.LotteryUtils;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SparkPromiseComponentAttr;
import com.yy.gameecology.hdzj.element.component.service.SparkPromiseService;
import com.yy.gameecology.hdzj.element.history.FireworkComponent;
import com.yy.gameecology.hdzj.utils.BusinessUtils;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@UseRedisStore
@RestController
@RequestMapping("5123")
public class SparkPromiseComponent extends BaseActComponent<SparkPromiseComponentAttr> implements LayerSupport {

    private static final String FIREWORK_EXCHANGED_BRO = "firework_exchanged:%d";

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private SignedService signedService;

    @Autowired
    private SparkPromiseService sparkPromiseService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    @Autowired
    private FtsHallActResultBuilder ftsHallActResultBuilder;

    @Override
    public Long getComponentId() {
        return ComponentId.SPARK_PROMISE;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, SparkPromiseComponentAttr attr) {
        if (attr.getTaskRankId() != event.getRankId() || attr.getTaskPhaseId() != event.getPhaseId()) {
            log.info("onTaskProgressChanged rankId or phaseId not fit event:{}", JSON.toJSONString(event));
            return;
        }

        log.info("onTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
        long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
        if (startIndex == currIndex) {
            return;
        }

        Map<Long, String> actors = event.getActors();
        if (MapUtils.isEmpty(actors)) {
            log.error("onTaskProgressChanged actors is empty");
            return;
        }

        String hallMemberId = null;
        for (long hallRoleId : attr.getHallRoleIds()) {
            if (StringUtils.isEmpty(actors.get(hallRoleId))) {
                continue;
            }

            hallMemberId = actors.get(hallRoleId);
        }

        if (!StringUtils.contains(hallMemberId, StringUtil.UNDERSCORE)) {
            log.error("onTaskProgressChanged cannot get hall member: {}", hallMemberId);
            return;
        }

        String[] arr = hallMemberId.split(StringUtil.UNDERSCORE);
        long sid = Long.parseLong(arr[0]), ssid = Long.parseLong(arr[1]);

        Date occurTime = DateUtil.getDate(event.getOccurTime());
        String memberId = event.getMember();
        String dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), occurTime);
        Pair<Long, Long> completeResult = null;
        for (long i = startIndex + 1; i <= currIndex; i++) {
            completeResult = sparkPromiseService.taskComplete(attr, memberId, dateCode, (int) i, (int) currIndex, sid, ssid, occurTime);
            log.info("taskComplete with memberId:{} taskIndex:{} result:{}", memberId, i, completeResult);
        }

        // 发广播、单播
        if (completeResult == null) {
            log.warn("onTaskProgressChanged completeResult is null");
            return;
        }

        final long recordId = completeResult.getLeft(), packageId = completeResult.getRight();
        SparkPromiseComponentAttr.Spark spark = attr.fetchSparkByPackageId(packageId);
        if (spark == null) {
            log.error("onTaskProgressChanged could not find spark with packageId:{}", packageId);
            return;
        }

        // 挂件广播
        long anchorUid = Long.parseLong(memberId);
        Integer broType = attr.getLayerBroTypes().get(currIndex);
        MemberInfo memberInfo = memberInfoService.getMemberInfo(attr.getBusiId(), RoleType.ANCHOR, memberId);
        // 挂件广播
        if (broType != null && memberInfo != null) {
            String text = String.format(attr.getScreenText(), spark.getName(), memberInfo.getName());
            JSONObject json = new JSONObject(4);
            json.put("fireTp", spark.getPackageId());
            json.put("text", text);
            json.put("nick", memberInfo.getName());
            json.put("logo", memberInfo.getHdLogo());
            json.put("sid", sid);
            json.put("ssid", ssid);

            Template template = InterstellarTreasureComponent.getTemplateByBusiId((int) attr.getBusiId());
            commonBroadCastService.commonBannerBroadcast(sid, ssid, 0, template, broType, attr.getActId(), anchorUid, event.getRankScore(), 5123001, 0, json);
        }

        // 单播给拍照用的
        if (attr.isTaskPhoto() && memberInfo != null) {
            JSONObject json = new JSONObject(6);
            json.put("fireId", recordId);
            json.put("fireTp", spark.getPackageId());
            json.put("giftIcon", spark.getLogo());
            json.put("nick", memberInfo.getName());
            json.put("logo", memberInfo.getHdLogo());
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), "fireworkPhoto", json.toJSONString(), StringUtils.EMPTY, anchorUid);
        }

        long playTime = attr.getSparkDuration().toSeconds();
        // 过滤弹幕游戏频道则不发特效
        if (attr.isExcludeDanmaku()) {
            List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
            boolean danmakuCh = danmakuChannel.stream().anyMatch(ch -> ch.getSid() == sid && ch.getSsid() == ssid);
            if (danmakuCh) {
                playTime = 0;
            }
        }

        // app 广播特效
        if (playTime > 0 && StringUtils.isNotEmpty(spark.getMp4Url())) {
            int business = BusinessUtils.getAppBroBizByBusiId((int) attr.getBusiId());
            AppBannerEvent2 bannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), event.getSeq(), business, AppBannerEvent2.BC_TYPE_SUB, sid, ssid, StringUtils.EMPTY, Collections.emptyList());
            bannerEvent.setContentType(5); //mp4 特效
            AppBannerMp4Config mp4Config = new AppBannerMp4Config();
            mp4Config.setUrl(spark.getMp4Url());
            mp4Config.setLevel(spark.getBroLevel());
            bannerEvent.setMp4Config(mp4Config);
            kafkaService.sendAppBannerKafka(bannerEvent);
            log.info("onTaskProgressChanged send app banner done");
        }

        // 广播给当前子频道抽奖 + 烟花 - 延迟发：为了等移动端特效播完后
        final long pt = playTime;
        threadPoolManager.getScheduledThreadPool(ThreadPoolNames.BRO_LAYER_SCHEDULE).schedule(() -> {
            JSONObject json = new JSONObject(8);
            json.put("fireId", recordId);
            json.put("word", spark.getName());
            json.put("playTime", pt);
            json.put("fireTp", spark.getPackageId());
            json.put("canDraw", "1");

            Template template = BusinessUtils.getTemplateByBusiId((int) attr.getBusiId());
            commonBroadCastService.commonBannerBroadcast(sid, ssid, 0, template, BroadcastType.SUB_CHANNEL, attr.getActId(), anchorUid, event.getRankScore(), 5123002, 0, json);
        }, 6, TimeUnit.SECONDS);
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, SparkPromiseComponentAttr attr) {
        long busiId = event.getBusiId();
        if (busiId != attr.getBusiId()) {
            return;
        }

        long actId = attr.getActId(), sid = event.getSid(), ssid = event.getSsid(), uid = event.getUid();
        String extJson = event.getExtJson();
        boolean app = StringUtils.contains(extJson, "app");
        Date now = commonService.getNow(actId);
        Cmpt5123SparkRecord sparkRecord = sparkPromiseService.getLatestSparkRecord(attr, sid, ssid, now);
        if (sparkRecord == null) {
            return;
        }

        Cmpt5123UserCard userCard = sparkPromiseService.getSparkUserCard(actId, sparkRecord.getId(), uid);
        if (userCard != null && userCard.getEffectState() == EffectState.CLOSED) {
            return;
        }

        SparkPromiseComponentAttr.Spark spark = attr.fetchSparkByPackageId(sparkRecord.getPackageId());
        Assert.notNull(spark, "package's spark cannot be null");

        long playTime = (sparkRecord.getCreateTime().getTime() + attr.getSparkDuration().toMillis() - now.getTime()) / 1000;

        // 是否过滤弹幕游戏
        if (attr.isExcludeDanmaku()) {
            List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
            boolean danmakuCh = danmakuChannel.stream().anyMatch(ch -> ch.getSid() == sid && ch.getSsid() == ssid);
            if (danmakuCh) {
                playTime = 0;
            }
        }

        boolean canDraw = userCard == null || userCard.getCardState() == CardState.INIT;

        if (playTime <= 0 && !canDraw) {
            return;
        }

        JSONObject json = new JSONObject(8);
        json.put("fireId", sparkRecord.getId());
        json.put("word", spark.getName());
        json.put("playTime", playTime);
        json.put("fireTp", spark.getPackageId());
        json.put("canDraw", canDraw ? 1 : 0);

        // PC 端直接发特效 + 翻卡 单播
        if (!app) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), "firework", json.toJSONString(), StringUtils.EMPTY, uid);
            return;
        }

        // 移动端先发特效
        if (playTime > 0 && StringUtils.isNotEmpty(spark.getMp4Url()) && userCard == null) {
            boolean added = sparkPromiseService.tryAddUserCard(actId, sparkRecord.getId(), uid, CardState.INIT, EffectState.NORMAL, now);
            if (added) {
                threadPoolManager.getScheduledThreadPool(ThreadPoolNames.BRO_LAYER_SCHEDULE).schedule(() -> {
                    int business = BusinessUtils.getAppBroBizByBusiId((int) attr.getBusiId());
                    AppBannerEvent2 bannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), event.getSeq(), business, AppBannerEvent2.BC_TYPE_USER, 0, 0, StringUtils.EMPTY, Collections.emptyList());
                    bannerEvent.setPushUidlist(Collections.singletonList(uid));
                    bannerEvent.setContentType(5); //mp4 特效
                    AppBannerMp4Config mp4Config = new AppBannerMp4Config();
                    mp4Config.setUrl(spark.getMp4Url());
                    mp4Config.setLevel(spark.getBroLevel());
                    bannerEvent.setMp4Config(mp4Config);

                    kafkaService.sendAppBannerKafka(bannerEvent);
                    log.info("onUserEnterTemplate send app banner to uid:{} done", uid);
                }, 2, TimeUnit.SECONDS);
            }
        }

        // 移动端等特效播完延迟发翻卡单播
        if (canDraw) {
            threadPoolManager.getScheduledThreadPool(ThreadPoolNames.BRO_LAYER_SCHEDULE).schedule(() -> commonBroadCastService.commonNoticeUnicast(attr.getActId(), "firework", json.toJSONString(), StringUtils.EMPTY, uid), 7, TimeUnit.SECONDS);
        }
    }

    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = true)
    public void onAwardLotteryMsg(HdztAwardLotteryMsg event, SparkPromiseComponentAttr attr) {
        if (event.getTaskId() != attr.getFireworkTaskId()) {
            return;
        }

        if (CollectionUtils.isEmpty(event.getData())) {
            return;
        }

        for (HdztAwardLotteryMsg.Award award : event.getData()) {
            if (award.getPackageId() != attr.getFireworkPackageId()) {
                continue;
            }

            UserCurrentChannel channel = commonService.getUserCurrentChannel(event.getUid());
            if (channel == null) {
                return;
            }

            if (attr.isExcludeDanmaku()) {
                List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
                boolean danmakuCh = danmakuChannel.stream().anyMatch(ch -> ch.getSid() == channel.getTopsid() && ch.getSsid() == channel.getSubsid());
                if (danmakuCh) {
                    return;
                }
            }

            StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
            String key = makeKey(attr, String.format(FIREWORK_EXCHANGED_BRO, event.getUid()));
            boolean set = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, "1", attr.getFireworkSilent()));
            if (set) {
                Template template = BusinessUtils.getTemplateByBusiId((int) attr.getBusiId());
                JSONObject json = new JSONObject(4);
                commonBroadCastService.commonBannerBroadcast(channel.getTopsid(), channel.getSubsid(), 0, template, BroadcastType.SUB_CHANNEL, attr.getActId(), event.getUid(), 0, 5123003, 0, json);
                log.info("onAwardLotteryMsg send banner broadcast to uid:{} done", event.getUid());

                if (StringUtils.isNotEmpty(attr.getFireworkMp4Url())) {
                    int business = BusinessUtils.getAppBroBizByBusiId((int) attr.getBusiId());
                    AppBannerEvent2 bannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), event.getSeq(), business, AppBannerEvent2.BC_TYPE_SUB, channel.getTopsid(), channel.getSubsid(), StringUtils.EMPTY, Collections.emptyList());
                    bannerEvent.setContentType(5); //mp4 特效
                    AppBannerMp4Config mp4Config = new AppBannerMp4Config();
                    mp4Config.setUrl(attr.getFireworkMp4Url());
                    mp4Config.setLevel(attr.getFireworkBroLevel());
                    bannerEvent.setMp4Config(mp4Config);
                    kafkaService.sendAppBannerKafka(bannerEvent);
                    log.info("onAwardLotteryMsg send app banner done");
                }
            }

            return;
        }
    }

    /**
     * 神豪翻转卡片
     * @param actId
     * @param cmptIndex
     * @param fireId
     * @return
     */
    @RequestMapping("lottery")
    public Response<FireworkComponent.LotteryResp> lottery(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam(name = "fireId") long fireId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }

        log.info("lottery uid:{},fireId:{}", uid, fireId);

        SparkPromiseComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(403, "不在活动时间内");
        }

        Cmpt5123UserCard userCard = sparkPromiseService.getSparkUserCard(actId, fireId, uid);
        if (userCard != null && userCard.getCardState() != CardState.INIT) {
            return Response.fail(400, "已经抽过了");
        }

        Date now = commonService.getNow(actId);
        Cmpt5123SparkRecord sparkRecord = sparkPromiseService.getSparkRecordByRecordId(fireId);
        if (sparkRecord == null) {
            return Response.fail(400, "花火不存在");
        }

        Date endTime = DateUtils.addSeconds(sparkRecord.getCreateTime(), (int) (attr.getSparkDuration().toSeconds() + 5));
        if (now.after(endTime)) {
            return Response.fail(400, "不在兑换时间内");
        }

        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, 0, 0, StringUtils.EMPTY, String.valueOf(uid), Collections.emptyMap());
        boolean rewarded = rank != null && rank.getScore() > 0;
        int cent = rewarded ? attr.getRewardedCent() : attr.getUnrewardedCent();
        boolean hit = LotteryUtils.lottery(cent, 10000);
        int cardState = hit ? CardState.REWARDED : CardState.UNREWARDED;

        if (userCard == null) {
            userCard = new Cmpt5123UserCard();
            userCard.setActId(actId);
            userCard.setRecordId(fireId);
            userCard.setUid(uid);
            userCard.setEffectState(EffectState.NORMAL);
            userCard.setCreateTime(now);
        }
        userCard.setCardState(cardState);

        Pair<Long, Long> lotteryResult;
        try {
            lotteryResult = sparkPromiseService.userLottery(attr, userCard);
        } catch (BusinessException e) {
            log.error("lottery exception: ", e);
            return Response.fail(e.getCode(), e.getMessage());
        }

        FireworkComponent.LotteryResp data = new FireworkComponent.LotteryResp();
        if (lotteryResult != null) {
            AwardModelInfo awardModelInfo = getAwardModelInfo(lotteryResult.getLeft(), lotteryResult.getRight());
            data.setCardTp(sparkRecord.getPackageId());
            data.setAward(true);
            if (awardModelInfo != null) {
                data.setItems(List.of(awardModelInfo.packageName));
            }

            log.info("lottery done uid:{},fireId:{},data:{}", uid, fireId,JSON.toJSONString(data));
            return Response.success(data);
        }

        data.setAward(false);
        SparkPromiseComponentAttr.Spark spark = getExcludeSpark(attr, sparkRecord.getPackageId());
        data.setCardTp(spark.getPackageId());
        log.info("lottery done uid:{},fireId:{},data:{}", uid, fireId,JSON.toJSONString(data));
        return Response.success(data);
    }

    public AwardModelInfo getAwardModelInfo(long taskId, long packageId) {
        try {
            Map<Long, AwardModelInfo> awardModelInfoMap = hdztAwardServiceClient.queryAwardTasks(taskId);
            return awardModelInfoMap == null ? null : awardModelInfoMap.get(packageId);
        } catch (Exception e) {
            log.info("getAwardModelInfo fail:", e);
        }

        return null;
    }

    /**
     * 查询当前用户是否签约主持（是否需要展示商城兑换模块）
     * @param actId
     * @param cmptIndex
     * @return
     */
    @RequestMapping("show")
    public Response<Boolean> show(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        SparkPromiseComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        long sid = signedService.getSignedSidByBusiId(uid, attr.getBusiId());
        return Response.success(sid > 0);
    }

    @RequestMapping("close")
    public Response<?> closeSpark(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam(name = "fireId") long fireId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }

        SparkPromiseComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        Cmpt5123SparkRecord record = sparkPromiseService.getSparkRecordByRecordId(fireId);
        if (record == null) {
            return Response.fail(400, "spark not exist");
        }

        sparkPromiseService.closeSparkEffect(actId, record.getId(), uid, commonService.getNow(actId));

        return Response.ok();
    }

    /**
     * 查询正在播放花火秀的频道
     * @param actId
     * @param cmptIndex
     * @param size
     * @return
     */
    @GetMapping("listSparkChannel")
    public Response<FireworkComponent.FireChannelResp> listSparkChannel(@RequestParam("actId") long actId,
                                                                        @RequestParam("cmptIndex") long cmptIndex,
                                                                        @RequestParam(name = "size", required = false, defaultValue = "30") int size) {
        SparkPromiseComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        FireworkComponent.FireChannelResp resp = new FireworkComponent.FireChannelResp();
        Date now = commonService.getNow(actId);
        List<Cmpt5123SparkRecord> sparkRecords = sparkPromiseService.getSparkRecordList(attr, size, now);
        if (CollectionUtils.isEmpty(sparkRecords)) {
            resp.setChannels(Collections.emptyList());
            return Response.success(resp);
        }

        List<FireworkComponent.ChannelVo> channels = new ArrayList<>(sparkRecords.size());
        for (Cmpt5123SparkRecord record : sparkRecords) {
            FireworkComponent.ChannelVo item = new FireworkComponent.ChannelVo();
            item.setSid(record.getSid());
            item.setSsid(record.getSsid());
            channels.add(item);
        }

        setSparkChannelInfo(attr, channels);

        resp.setChannels(channels);

        return Response.success(resp);
    }

    /**
     * 填充频道信息：<br/>
     * 交友-厅管那套 <br/>
     * 聊天室：房间信息
     * @param attr
     * @param channels
     */
    private void setSparkChannelInfo(SparkPromiseComponentAttr attr, List<FireworkComponent.ChannelVo> channels) {
        if (attr.getBusiId() == BusiId.MAKE_FRIEND.getValue()) {
            List<String> memberIds = channels.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).toList();
            Map<String, Map<String, MemberInfo>> memberInfos = ftsHallActResultBuilder.loadMemberInfo(attr.getActId(), 1, RoleType.HALL.getValue(), memberIds);
            String key = "1_" + RoleType.HALL.getValue();
            if (MapUtils.isEmpty(memberInfos) || MapUtils.isEmpty(memberInfos.get(key))) {
                return;
            }

            Map<String, MemberInfo> memberInfoMap = memberInfos.get(key);
            for (FireworkComponent.ChannelVo item : channels) {
                String memberId = item.getSid() + StringUtil.UNDERSCORE + item.getSsid();
                MemberInfo memberInfo = memberInfoMap.get(memberId);
                if (memberInfo != null) {
                    item.setNick(memberInfo.getName());
                    item.setAvatar(memberInfo.getLogo());
                }
            }
        } else if (attr.getBusiId() == BusiId.SKILL_CARD.getValue()) {
            List<Long> ssids = channels.stream().map(FireworkComponent.ChannelVo::getSsid).toList();
            List<RoomInfo> roomInfos = commonService.batchGetRoomInfoBySsids(ssids);
            Map<Long, RoomInfo> roomInfoMap = roomInfos.stream().collect(Collectors.toMap(RoomInfo::getSsid, Function.identity(), (k1, k2) -> k1));
            for (FireworkComponent.ChannelVo item : channels) {
                RoomInfo roomInfo = roomInfoMap.get(item.getSsid());
                if (roomInfo != null) {
                    item.setNick(roomInfo.getTitle());
                    item.setAvatar(roomInfo.getCover());
                }
            }
        }
    }

    public SparkPromiseComponentAttr.Spark getExcludeSpark(SparkPromiseComponentAttr attr, long packageId) {
        List<SparkPromiseComponentAttr.Spark> sparks = attr.getSparks();
        List<SparkPromiseComponentAttr.Spark> excludeSparks = sparks.stream().filter(spark -> spark.getPackageId() != packageId).toList();
        if (excludeSparks.isEmpty()) {
            return null;
        }

        int index = RandomUtils.nextInt(0, excludeSparks.size());
        return excludeSparks.get(index);
    }

    @Override
    public long getActId() {
        return ComponentId.SPARK_PROMISE;
    }

    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        if (!StringUtils.equals(layerMemberItem.getItemType(), LayerItemTypeKey.ANCHOR)) {
            return ext;
        }

        Date now = commonService.getNow(actId);
        SparkPromiseComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return ext;
        }

        if (ext == null) {
            ext = new HashMap<>(5);
        }

        boolean missionCompleted = MapUtils.getBoolean(layerMemberItem.getViewSupport(), LayerViewSupportName.MISSION_COMPLETE, false);
        if (missionCompleted) {
            ext.put("taskDesc", "已达成最高任务");
        } else {
            List<BabyMissionItem> missions = layerMemberItem.getMissions();
            if (CollectionUtils.isNotEmpty(missions)) {
                long gap = missions.get(0).getTotalCount() - missions.get(0).getCompletedCount();
                ext.put("taskDesc", "<p style=\"color: #fff6df\">差<span style=\"color: #ffd077\">" + gap +"</span>荣耀值</p>");
            }
        }

        Cmpt5123SparkRecord sparkRecord = sparkPromiseService.getLatestSparkRecord(attr, layerMemberItem.getMemberId(), now);
        if (sparkRecord == null) {
            return ext;
        }

        SparkPromiseComponentAttr.Spark spark = attr.fetchSparkByPackageId(sparkRecord.getPackageId());

        ext.put("fireTp", spark.getPackageId());
        ext.put("word", spark.getName());

        return ext;
    }

    public static interface CardState {
        int INIT = 0;

        int REWARDED = 10;

        int UNREWARDED = 20;

        int CLOSED = 30;
    }

    public static interface EffectState {
        int NORMAL = 0;

        int CLOSED = 10;
    }
}
