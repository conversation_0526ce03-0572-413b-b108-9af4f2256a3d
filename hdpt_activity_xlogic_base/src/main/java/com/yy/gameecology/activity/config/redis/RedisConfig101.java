package com.yy.gameecology.activity.config.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.Serializable;

/**
 * redis归档热备数据
 */
@Configuration
public class RedisConfig101 extends BaseRedisConfig {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /* ##############################  gameecology redis  ############################# */

    public RedisConfig101(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        super(builderCustomizers);
    }

    @Bean
    @ConfigurationProperties("gameecology.group101.redis")
    public RedisProperties redisGroup101Properties() {
        return new RedisProperties();
    }

    @Bean
    public RedisConnectionFactory redisGroup101ConnectionFactory(RedisProperties redisGroup101Properties) {
        return new LettuceConnectionFactory(getRedisConfig(redisGroup101Properties), getClientConfig(redisGroup101Properties));
    }

    @Bean
    public StringRedisTemplate stringRedisGroup101Template(RedisConnectionFactory redisGroup101ConnectionFactory) {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(redisGroup101ConnectionFactory);
        return stringRedisTemplate;
    }

    @Bean
    public RedissonClient redissonClient101(RedisProperties redisGroup101Properties) {
        Config config = getRedissonConfig(redisGroup101Properties);
        config.setCheckLockSyncedSlaves(false);
        log.info("Creating RedissonClient for group101 with config: host={}, port={}, database={}",
                redisGroup101Properties.getHost(),
                redisGroup101Properties.getPort(),
                redisGroup101Properties.getDatabase());
        return Redisson.create(config);
    }

    @Bean
    public RedisTemplate<Serializable, Serializable> redisGroup101Template(RedisConnectionFactory redisGroup101ConnectionFactory) {
        RedisTemplate<Serializable, Serializable> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisGroup101ConnectionFactory);
        return redisTemplate;
    }

}
