package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.apphistory.y2022.act2022046003.bean.*;
import com.yy.ent.commons.yypclient.exception.BusinessException;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.JiaoyouComboEndEvent;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.BabyMissionItem;
import com.yy.gameecology.activity.bean.actlayer.TaskItem;
import com.yy.gameecology.activity.bean.hdzt.RankPhasePair;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.bean.hdzt.TaskUserInfoVo;
import com.yy.gameecology.activity.service.BigDataService;
import com.yy.gameecology.activity.service.HdztTaskService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.UserPaidGrowthComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/28 15:57
 **/
@UseRedisStore
@Component
public class UserPaidGrowthComponent extends BaseActComponent<UserPaidGrowthComponentAttr> {
    /**
     * 不展示经验获得气泡提示
     **/
    private static final String NOT_SHOW_BUBBLE_TIPS_FIELD = "NotShowBubbleTips";
    /**
     * 总日总威望值
     **/
    private static final String TOTAL_VALUE_TODAY = "TotalValueToday";

    /**
     * 当日经验值
     **/
    private static final String TODAY_EXPERIENCE = "TodayExperience";

    /**
     * 上一经验值阶段
     **/
    private static final String LAST_INTERVAL = "LastInterval";

    /**
     * 每日信息:(yyyyMMdd):(uid)
     **/
    private static final String DAILY_INFO_KEY = "DailyInfo:%s:%s";

    /**
     * 紫水晶券奖励
     **/
    private static final String DELAY_AWARD_KEY = "DelayAward:";

    /**
     * 升级key
     **/
    private static final String LEVEL_UPGRADE_KEY = "LevelUpgrade:";

    /**
     * 紫水晶领取上限累计额
     */
    private static final String AMETHYST_COUPON_CEILING_KEY = "AmethystCountCeiling";

    private static final String AMETHYST_COUPON_TIPS_KEY = "AmethystCouponTips:";

    private static final String JIAOYOU_COMBO_EVENT_SEQ_KEY = "seq:";

    /**
     * 紫水晶券奖励列表
     **/
    private static final String DELAY_AWARD_LIST_KEY = "DelayAwardList:";

    /**
     * 用户经验记录  （：uid）
     */
    private static final String EXPERIENCE_RECORD_KEY = "experience_record:%s";

    private static final String FREE_GIFT_LIMIT_KEY = "FreeGiftLimit";

    @Autowired
    protected UserInfoService userInfoService;

    @Autowired
    private HdztTaskService hdztTaskService;

    @Autowired
    private BigDataService bigDataService;

    @Override
    public Long getComponentId() {
        return ComponentId.USER_PAID_GROWTH;
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        CommonPBOperateResp resp = new CommonPBOperateResp();

        log.info("commonOperatePbRequest request={}", request);
        long uid = request.getOpUid();
        long actId = request.getActId();
        long componentUseIndex = request.getCmptIndex();
        UserPaidGrowthComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            resp.setCode(1);
            resp.setMsg("活动不存在");
            return resp;
        }
        String tipType = "NotShowBubbleTips";
        if (tipType.equals(request.getOpType())) {
            setNotShowBubbleTips(uid + "", attr);
            resp.setCode(0);
        } else {
            resp.setCode(1);
            resp.setMsg("请求参数错误");
        }

        return resp;
    }

    /**
     * 监听连送结束事件：处理经验获取
     **/
    @HdzjEventHandler(value = JiaoyouComboEndEvent.class, canRetry = false)
    public void onJiaoyouComboEndEvent(JiaoyouComboEndEvent event, UserPaidGrowthComponentAttr attr) {
        Map<String, Object> expand = event.getExpand();
        long propId = MapUtils.getLongValue(expand, "propId");
        long eventTime = event.getTimestamp() * 1000;
        long actId = attr.getActId();
        eventTime = commonService.getTime(eventTime, actId);
        long sendUid = event.getSendUid();
        UserPaidGrowthComponentAttr.GiftConfig giftConfig = attr.getGiftConfig(propId, eventTime);
        if (giftConfig == null) {
            log.info("not act gift or expired@actId={},sendUid={}", actId, sendUid);
            return;
        }

        String seqId = event.getSeqId();
        String groupCode = getRedisGroupCode(actId);
        String key = makeKey(attr, JIAOYOU_COMBO_EVENT_SEQ_KEY + seqId);
        if (!actRedisDao.setNX(groupCode, key, String.valueOf(eventTime), DateUtil.ONE_HOUR_SECONDS)) {
            log.warn("onJiaoyouComboEndEvent handle duplicate seq event:{}", event);
            return;
        }

        doExperienceObtainEvent(giftConfig, expand, sendUid, eventTime, event.getSeqId(), attr);
    }

    /**
     * 监听任务事件：处理升级
     **/
    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onTaskProgressChanged(TaskProgressChanged event, UserPaidGrowthComponentAttr attr) {
        if (event.getActId() != attr.getActId()) {
            log.info("actId is not equal,event actId={},attr actId={}", event.getActId(), attr.getActId());
            return;
        }

        if (isLevelUpgradeEvent(event, attr)) {
            doLevelUpgradeEvent(event, attr);
        }
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnter(UserEnterTemplateEvent event, UserPaidGrowthComponentAttr attr) {
        if (event.getBusiId() != BusiId.MAKE_FRIEND.getValue()) {
            return;
        }

        final long uid = event.getUid(), actId = attr.getActId();
        UserPaidGrowthComponentAttr.PhaseConfig phaseConfig = findCurrentPhase(attr);
        if (phaseConfig == null) {
            return;
        }

        String ceilingKey = makeKey(attr, AMETHYST_COUPON_CEILING_KEY);
        String sendAmountStr = actRedisDao.hget(getRedisGroupCode(attr.getActId()), ceilingKey, "1");
        long sendAmount = 0;
        if (StringUtils.isNumeric(sendAmountStr)) {
            sendAmount = Long.parseLong(sendAmountStr);
        }

        if (sendAmount >= attr.getAmethystCouponCeiling()) {
            log.info("onUserEnter had send amount:{}, ceiling:{}", sendAmount, attr.getAmethystCouponCeiling());
            return;
        }

        Map<Integer, Map<Long, Integer>> awardMap = getRecvableDelayAward(uid, phaseConfig.getPhaseId(), attr);

        if (MapUtils.isEmpty(awardMap)) {
            return;
        }

        String key = makeKey(attr, AMETHYST_COUPON_TIPS_KEY + phaseConfig.getPhaseId());
        String groupCode = getRedisGroupCode(attr.getActId());
        boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
        if (!first) {
            log.info("onUserEnter with uid:{}, actId:{}, sid:{} ignore not first", uid, actId, event.getSid());
            return;
        }

        GameecologyActivity.CommonNoticeResponse.Builder tips = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(attr.getTipsNoticeType())
                .setNoticeValue(attr.getTipsNoticeValue())
                .setExtJson(attr.getTipsNoticeExtJson());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(tips).build();
        svcSDKService.unicastUid(uid, msg);

        log.info("onUserEnter send notice done@uid:{} index:{} msg:{}",
                uid, attr.getCmptUseInx(), JsonFormat.printToString(tips.build()));
    }

    /**
     * 用户设置当日不再进行经验获得气泡提示
     **/
    private void setNotShowBubbleTips(String memberId, UserPaidGrowthComponentAttr attr) {
        String currentDay = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String key = makeKey(attr, String.format(DAILY_INFO_KEY, currentDay, memberId));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        actRedisDao.hset(groupCode, key, NOT_SHOW_BUBBLE_TIPS_FIELD, "1", DateUtil.ONE_MONTH_SECONDS);
    }

    // *************************** 经验获取Start *************************** //

    /**
     * 执行经验获取逻辑
     **/
    private void doExperienceObtainEvent(UserPaidGrowthComponentAttr.GiftConfig giftConfig, Map<String, Object> expand
            , long sendUid, long eventTime, String seqId, UserPaidGrowthComponentAttr attr) {
        long actId = attr.getActId();
        log.info("doExperienceObtainEvent with actId:{}, sendUid:{}, expand:{}, seqId:{}", actId, sendUid, expand, seqId);
        // 单位：紫水晶 (combo*price)
        long priceSum = MapUtils.getLongValue(expand, "priceSum");
        long combo = MapUtils.getLongValue(expand, "combo");
        long countPerCombo = MapUtils.getLongValue(expand, "count");
        long totalGiftCount = combo * countPerCombo;

        // 威望值，紫水晶 / 10
        long score = priceSum / 10;

        long propId = MapUtils.getLongValue(expand, "propId");
        final UserPaidGrowthComponentAttr.FreeGiftConfig freeGiftConfig = attr.getFreeGiftConfig(propId);
        if (freeGiftConfig != null) {
            //限制
            if (freeGiftConfig.getEffectLimit() > 0) {
                String freeGiftLimitKey = makeKey(attr, FREE_GIFT_LIMIT_KEY);
                List<Long> rs = actRedisDao.hIncrWithLimit(getRedisGroupCode(attr.getActId()), freeGiftLimitKey, String.valueOf(sendUid), totalGiftCount, freeGiftConfig.getEffectLimit(), true);
                if (CollectionUtils.isNotEmpty(rs)) {
                    long resultCode = rs.get(0);
                    if (resultCode < 0) {
                        log.warn("doExperienceObtainEvent with free gift effect limit actId:{}, sendUid:{}, propId:{}", actId, sendUid, propId);
                        return;
                    } else if (resultCode == TWO) {
                        totalGiftCount = rs.get(1);
                        log.info("doExperienceObtainEvent with free gift partial add actId:{}, sendUid:{}, propId:{}, totalGiftCount:{}", actId, sendUid, propId, totalGiftCount);
                    }

                }
            }
            score = freeGiftConfig.getPrestige() * totalGiftCount;
        }

        // 更新威望榜单
        String seq = "WW_" + seqId;
        updateRank(sendUid + "", seq, score, eventTime, attr.getRankRoleId(), attr.getActId(), attr.getGiftItemId());
        bigDataService.saveNoRankDataToFile(seq, attr.getActId(), BusiId.MAKE_FRIEND, eventTime, sendUid + ""
                , RoleType.USER, score, BigDataScoreType.PRESTIGE, "", 0, 0);

        // 累计当天的送礼额度
        String key = makeKey(attr, String.format(DAILY_INFO_KEY, DateUtil.format(new Date(eventTime), DateUtil.PATTERN_TYPE2), sendUid));
        String groupCode = getRedisGroupCode(actId);
        long afterScore = actRedisDao.hIncrByKey(groupCode, key, TOTAL_VALUE_TODAY, score);
        if (afterScore == score) {
            actRedisDao.setExpire(groupCode, key, DateUtil.ONE_MONTH_SECONDS);
        }

        // 获取加倍倍数配置
        Map<Long, Double> multipleMap = attr.getDefaultExperienceMultipleMap();
        UserPaidGrowthComponentAttr.PhaseConfig phaseConfig = findCurrentPhase(attr);
        if (phaseConfig != null) {
            multipleMap = attr.getExperienceMultipleMap().getOrDefault(phaseConfig.getPhaseId(), multipleMap);
        }

        ImmutableSortedMap<Long, Double> thresholdMap = ImmutableSortedMap.copyOf(multipleMap);
        double multiple = thresholdMap.floorEntry(afterScore).getValue();
        long threshold = thresholdMap.floorEntry(afterScore).getKey();

        // 经验值要除以100
        long totalExperience = new BigDecimal(multiple + "").multiply(BigDecimal.valueOf(score)).longValue() / 100;
        if (freeGiftConfig != null) {
            totalExperience += (freeGiftConfig.getExp() * totalGiftCount);
        }
        long sourceTotalExperience = totalExperience;

        // 判断限额
        if (attr.getExperienceDailyLimit() > 0) {
            long todayExperience = actRedisDao.hIncrByKey(groupCode, key, TODAY_EXPERIENCE, totalExperience);
            log.info("doExperienceObtainEvent add exp with actId:{}, sendUid:{}, exp:{}", actId, sendUid, totalExperience);
            long todayRemainingExperience = attr.getExperienceDailyLimit() - todayExperience;
            totalExperience = todayRemainingExperience > 0 ? totalExperience : Math.max(0, todayRemainingExperience + totalExperience);
        }
        seq = "Exp_" + seqId;
        bigDataService.saveNoRankDataToFile(seq, attr.getActId(), BusiId.MAKE_FRIEND, eventTime, sendUid + ""
                , RoleType.USER, totalExperience, BigDataScoreType.EXPERIENCE, multiple + "", 0, sourceTotalExperience);

        if (totalExperience <= 0) {
            log.info("ExperienceDailyLimit@actId={},uid={}", actId, sendUid);
            return;
        }

        boolean result = updateRank(sendUid + "", seq, totalExperience, eventTime
                , attr.getRankRoleId(), attr.getActId(), attr.getExperienceRankItemId());
        if (result) {
            // 添加经验获得记录
            addExperienceRecord(attr, sendUid, seqId, eventTime, totalExperience, giftConfig.getGiftName(), totalGiftCount);

            // 经验获得气泡提示
            // 第几个区间
            int index = new LinkedList<>(multipleMap.values()).indexOf(multiple);
            boolean needTips = actRedisDao.hsetnx(groupCode, key, LAST_INTERVAL + index, index + "");
            if (needTips) {
                bubbleTips(totalExperience, sendUid, index, eventTime, multiple, threshold, attr);
            }
        }
    }

    /**
     * 添加经验获得记录
     **/
    private void addExperienceRecord(UserPaidGrowthComponentAttr attr, long uid, String seq, long time, long totalExperience
            , String giftName, Long giftCount) {
        String key = makeKey(attr, String.format(EXPERIENCE_RECORD_KEY, uid));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        ExperienceRecord record = new ExperienceRecord(uid, seq, time, totalExperience, giftName, giftCount);
        log.info("addExperienceRecord add exp record with record:{}", record);
        actRedisDao.getRedisTemplate(groupCode).opsForList().leftPush(key, JSON.toJSONString(record));
        actRedisDao.getRedisTemplate(groupCode).opsForList().trim(key, 0, 50);
    }

    /**
     * 获得经验气泡弹窗
     **/
    private void bubbleTips(long totalExperience, long sendUid, int index, long eventTime, double multiple, long threshold, UserPaidGrowthComponentAttr attr) {
        if (isNotShowBubbleTips(sendUid + "", eventTime, attr) || threshold <= 0) {
            log.info("memberId={} isNotShowBubbleTips threshold={}", sendUid, threshold);
            return;
        }
        boolean isShowClose = index > attr.getBubbleTipsThreshold();

        UserBaseInfo userBaseInfo = broadCastHelpService.getUserBaseInfo(sendUid, BusiId.MAKE_FRIEND);

        // isShowClose参数放到扩展字段中
        JSONObject extJson = new JSONObject();
        extJson.put("isShowClose", isShowClose);
        extJson.put("multiple", multiple);
        extJson.put("threshold", threshold);

        GameecologyActivity.ActAnchorFreeLimitTips tips =
                GameecologyActivity.ActAnchorFreeLimitTips.newBuilder()
                        .setActId(attr.getActId())
                        .setUid(sendUid)
                        .setNick(userBaseInfo.getNick())
                        .setLimit(totalExperience)
                        .setExtjson(extJson.toJSONString())
                        .build();
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kActAnchorFreeLimitTipsUri_VALUE)
                .setActAnchorFreeLimitTips(tips)
                .build();

        svcSDKService.unicastUid(sendUid, msg);
        log.info("bubbleTips done uid={},msg={}", sendUid, JsonFormat.printToString(msg));
    }

    /**
     * 用户当然是否不再进行经验获得气泡提示
     **/
    private boolean isNotShowBubbleTips(String memberId, long eventTime, UserPaidGrowthComponentAttr attr) {
        String key = makeKey(attr, String.format(DAILY_INFO_KEY, DateUtil.format(new Date(eventTime), DateUtil.PATTERN_TYPE2), memberId));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());

        return "1".equals(actRedisDao.hget(groupCode, key, NOT_SHOW_BUBBLE_TIPS_FIELD));
    }

    /**
     * 获取当前
     **/
    private UserPaidGrowthComponentAttr.PhaseConfig findCurrentPhase(UserPaidGrowthComponentAttr attr) {
        List<UserPaidGrowthComponentAttr.PhaseConfig> sortedDoubleTime = sortDoubleTime(attr.getPhaseList());
        checkPhaseTimeOverlap(sortedDoubleTime);

        long now = commonService.getNow(attr.getActId()).getTime();
        for (UserPaidGrowthComponentAttr.PhaseConfig phaseConfig : sortedDoubleTime) {
            if (phaseConfig.getPhaseStartTime() <= now && phaseConfig.getPhaseEndTime() >= now) {
                return phaseConfig;
            }
        }

        return null;
    }

    /**
     * 获取下一阶段
     **/
    private UserPaidGrowthComponentAttr.PhaseConfig findNextPhase(UserPaidGrowthComponentAttr attr, int currentPhaseId) {
        List<UserPaidGrowthComponentAttr.PhaseConfig> sortedDoubleTime = sortDoubleTime(attr.getPhaseList());

        for (UserPaidGrowthComponentAttr.PhaseConfig phaseConfig : sortedDoubleTime) {
            if (phaseConfig.getPhaseId() == currentPhaseId) {
                return phaseConfig;
            }
        }

        return null;
    }

    private static final int TWO = 2;

    /**
     * 校验加倍时间是否存在重叠
     **/
    private void checkPhaseTimeOverlap(List<UserPaidGrowthComponentAttr.PhaseConfig> list) {
        int size = list.size();
        for (int index = 0; index < size - TWO; index++) {
            UserPaidGrowthComponentAttr.PhaseConfig current = list.get(index);
            UserPaidGrowthComponentAttr.PhaseConfig next = list.get(index + 1);
            long totalDuration = next.getPhaseEndTime() - current.getPhaseStartTime();
            long currentDuration = current.getPhaseEndTime() - current.getPhaseStartTime();
            long nextDuration = next.getPhaseEndTime() - next.getPhaseStartTime();
            if (totalDuration < (currentDuration + nextDuration)) {
                throw new IllegalArgumentException("活动阶段时间存在重叠情况,请检查");
            }
        }
    }

    /**
     * 按照开始时间升序排列
     **/
    private List<UserPaidGrowthComponentAttr.PhaseConfig> sortDoubleTime(List<UserPaidGrowthComponentAttr.PhaseConfig> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        list = list.stream()
                .sorted(Comparator.comparingLong(UserPaidGrowthComponentAttr.PhaseConfig::getPhaseStartTime))
                .collect(Collectors.toList());

        return list;
    }

    /**
     * 更新经验榜单
     **/
    private boolean updateRank(String memberId, String seq, long score, long time, long rankRoleId, long actId, String itemId) {
        Map<Long, String> actors = new HashMap<>(Collections.singletonMap(rankRoleId, memberId));
        UpdateRankingRequest request = new UpdateRankingRequest();
        request.setBusiId(BusiId.MAKE_FRIEND.getValue());
        request.setActId(actId);
        request.setSeq(seq);
        request.setActors(actors);
        request.setItemId(itemId);
        request.setCount(1);
        request.setScore(score);
        request.setRankScores(Maps.newHashMap());
        request.setTimestamp(time);
        request.setExtData(Maps.newHashMap());
        try {
            UpdateRankingResult result = hdztRankingThriftClient.getProxy().updateRanking(request);
            log.info("updateExperienceRank success,request={},result={}", request, result);

            return true;
        } catch (Exception ex) {
            log.error("updateExperienceRank error,request={}", request, ex);
            return false;
        }
    }

    // *************************** 等级升级Start *************************** //

    /**
     * 是否等级升级事件
     **/
    private boolean isLevelUpgradeEvent(TaskProgressChanged event, UserPaidGrowthComponentAttr attr) {
        return event.getRankId() == attr.getLevelUpgradeRankId() && event.getPhaseId() == attr.getLevelUpgradePhaseId();
    }

    private static final String LEVEL_PREFIX = "Level";

    /**
     * 执行等级升级逻辑
     **/
    private void doLevelUpgradeEvent(TaskProgressChanged event, UserPaidGrowthComponentAttr attr) {
        log.info("doLevelUpgradeEvent event={}", event);
        String key = makeKey(attr, LEVEL_UPGRADE_KEY + event.getMember());
        String group = redisConfigManager.getGroupCode(attr.getActId());
        if (!actRedisDao.hsetnx(group, key, LEVEL_PREFIX + event.getCurrTaskIndex(), event.getOccurTime())) {
            log.info("duplicate upgrade event for level:{},uid:{}", event.getCurrTaskIndex(), event.getMember());
            return;
        }

        // 发送勋章 + 进场秀 + 紫水晶券 奖励
        boolean awardResult = doLevelUpgradeAward(event, attr);
        if (!awardResult) {
            log.error("doLevelUpgradeAward fail");
            return;
        }

        // 发送升级弹窗
        doLevelUpgradePopup(event, attr);

        // 发放横幅
        doUpgradeBanner(event, attr);
    }

    /**
     * 发放等级升级奖励
     **/
    private boolean doLevelUpgradeAward(TaskProgressChanged event, UserPaidGrowthComponentAttr attr) {
        long uid = Convert.toLong(event.getMember(), 0);

        long currTaskIndex = event.getCurrTaskIndex();
        long lastTaskId = event.getStartTaskIndex() + 1;

        long eventTime = DateUtil.getDate(event.getOccurTime()).getTime();

        // 实时发放奖励
        Map<Long, Integer> packageIdCountMap = Maps.newHashMap();
        // 延迟发放奖励
        Map<Long, Integer> delayAwardCountMap = Maps.newHashMap();
        // 延迟发放的奖励列表
        List<Map.Entry<Long, Integer>> delayAwards = Lists.newArrayList();
        for (long index = lastTaskId; index <= currTaskIndex; index++) {
            bigDataService.saveNoRankDataToFile(event.getSeq(), attr.getActId(), BusiId.MAKE_FRIEND, eventTime, event.getMember()
                    , RoleType.USER, index, BigDataScoreType.ACHIEVE_LEVEL, "", 0, 0);

            if (!attr.getLevelUpgradePackageMap().containsKey(index)) {
                log.info("LevelUpgradePackageMap not contains level:{}", index);
                continue;
            }
            packageIdCountMap.put(attr.getLevelUpgradePackageMap().get(index), 1);
            Map<Long, Integer> delayAwardMap = attr.getDelayAwardMap().get(index);
            if (MapUtils.isNotEmpty(delayAwardMap)) {
                for (Map.Entry<Long, Integer> entry : delayAwardMap.entrySet()) {
                    long packageId = entry.getKey();
                    int amount = entry.getValue();
                    delayAwardCountMap.compute(packageId, (k, v) -> (v == null) ? amount : v + amount);
                    delayAwards.add(Pair.of(packageId, amount));

                    // 上报海度
                    int sourceType = packageId == attr.getAmethystCouponAwardPackageId() ? BigDataScoreType.GET_AMETHYST : BigDataScoreType.GET_CHAOJIHAOBIAO;
                    bigDataService.saveNoRankDataToFile(event.getSeq(), attr.getActId(), BusiId.MAKE_FRIEND, eventTime, event.getMember()
                            , RoleType.USER, index, sourceType, "", 0, 0);
                }
            }
        }
        if (MapUtils.isEmpty(packageIdCountMap) && MapUtils.isEmpty(delayAwardCountMap)) {
            log.info("packageIdCountMap and delayAwardCountMap is empty");
            return false;
        }

        // 实时发放的奖励
        if (MapUtils.isNotEmpty(packageIdCountMap)) {
            Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
            taskPackageIds.put(attr.getLevelUpgradeAwardTaskId(), packageIdCountMap);
            hdztAwardServiceClient.doBatchWelfare(uid, taskPackageIds, event.getOccurTime(), 2, Collections.emptyMap());
        }

        //延时礼物
        if (MapUtils.isNotEmpty(delayAwardCountMap)) {
            UserPaidGrowthComponentAttr.PhaseConfig currentPhase = findCurrentPhase(attr);
            if (currentPhase == null) {
                log.error("award delay award error,currentPhase is empty,uid={},delayAwardCountMap={}", uid, delayAwardCountMap);
                return false;
            }

            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String key = makeKey(attr, DELAY_AWARD_KEY + uid);
            for (Map.Entry<Long, Integer> entry : delayAwardCountMap.entrySet()) {
                // hashKey是phaseId + packageId，value是对应的数量
                String hashKey = currentPhase.getNextPhaseId() + ":" + entry.getKey();
                long afterValue = actRedisDao.hIncrByKey(groupCode, key, hashKey, entry.getValue());
                log.info("offer delay award done,key={},hashKey={}, incr:{}, afterValue={}",
                        key, hashKey, entry.getValue(), afterValue);
            }

            String delayAwardListKey = makeKey(attr, DELAY_AWARD_LIST_KEY + uid + ":" + currentPhase.getNextPhaseId());
            for (Map.Entry<Long, Integer> value : delayAwards) {
                actRedisDao.getRedisTemplate(groupCode).opsForList().leftPush(delayAwardListKey, value.getKey() + ":" + value.getValue());
            }
        }

        return true;
    }

    /**
     * 发放升级弹窗
     **/
    private void doLevelUpgradePopup(TaskProgressChanged event, UserPaidGrowthComponentAttr attr) {
        long currentTaskIndex = event.getCurrTaskIndex();
        long uid = Convert.toLong(event.getMember(), 0);

        String noticeValue = "恭喜升级至LV" + currentTaskIndex;

        JSONObject extJson = new JSONObject();
        extJson.put("medalUrl", attr.getLevelMedalMap().get(currentTaskIndex));

        GameecologyActivity.CommonNoticeResponse.Builder tips = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType("level_upgrade_popup")
                .setNoticeValue(noticeValue)
                .setExtJson(extJson.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(tips)
                .build();
        svcSDKService.unicastUid(uid, msg);
        log.info("doLevelUpgradePopup done,uid={},msg={}", uid, JsonFormat.printToString(msg));
    }

    /**
     * 发放升级横幅
     **/
    private void doUpgradeBanner(TaskProgressChanged event, UserPaidGrowthComponentAttr attr) {
        long currentTaskIndex = event.getCurrTaskIndex();
        long uid = Convert.toLong(event.getMember(), 0);

        // 默认子频道广播
        int broType = attr.getLevelUpgradeBroadcastRangeMap().getOrDefault(currentTaskIndex, 2);

        UserCurrentChannel channel = commonService.getUserCurrentChannel(uid);
        if (channel == null) {
            log.warn("doUpgradeBanner error,user not in channel uid:{} ", uid);
            return;
        }
        UserInfoVo userInfoVo = getUserInfoVo(uid);

        String svga = attr.getLevelSvgaMap().getOrDefault(currentTaskIndex, attr.getLevelUpgradeSvgaUrl());
        String jsonData = JSON.toJSONString(Collections.singletonMap("svgaUrl", svga));
        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(1)
                .setAnchorUid(uid)
                .setAnchorNick(userInfoVo.getNick())
                .setAnchorLogo(userInfoVo.getAvatarUrl())
                .setAnchorScore(currentTaskIndex)
                .setJsonData(jsonData)
                .setSid(channel.getTopsid())
                .setSsid(channel.getSubsid())
                .setAsid(commonService.getAsid(channel.getTopsid()));

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast)
                .build();
        broadCastHelpService.broadcast(attr.getActId(), BusiId.MAKE_FRIEND, broType, channel.getTopsid(), channel.getSubsid(), bannerBroMsg);

        log.info("doUpgradeBanner done msg={}", JsonFormat.printToString(bannerBroMsg));
    }

    // *************************** H5接口 Start *************************** //

    /**
     * 领取紫水晶券奖励
     **/
    public List<DelayAwardInfo> receiveDelayAward(long uid, long actId, long componentUseIndex) throws BusinessException {
        log.info("receiveDelayAward,uid={},actId={},componentUseIndex={}", uid, actId, componentUseIndex);
        UserPaidGrowthComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            throw new BusinessException("attr config is null", 500);
        }

        UserPaidGrowthComponentAttr.PhaseConfig currentPhase = findCurrentPhase(attr);
        if (currentPhase == null) {
            throw new BusinessException("current phase is null", 500);
        }

        final int phaseId = currentPhase.getPhaseId();
        boolean recvablePhase = false;
        for (UserPaidGrowthComponentAttr.PhaseConfig phaseConfig : attr.getPhaseList()) {
            if (phaseConfig.getNextPhaseId() == phaseId) {
                recvablePhase = true;
                break;
            }
        }

        if (!recvablePhase) {
            throw new BusinessException("当前不在可领取时段", 500);
        }

        Map<Integer, Map<Long, Integer>> delayAwardMap = getRecvableDelayAward(uid, phaseId, attr);
        if (MapUtils.isEmpty(delayAwardMap)) {
            throw new BusinessException("无可领取奖励", 500);
        }

        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String key = makeKey(attr, DELAY_AWARD_KEY + uid);
        Map<Long, Integer> delayAwardCountMap = Maps.newHashMap();

        Map<String, Integer> rollbackMap = Maps.newHashMap();
        Map<String, Integer> amethystCouponRollbackMap = Maps.newHashMap();
        for (Map.Entry<Integer, Map<Long, Integer>> entry : delayAwardMap.entrySet()) {
            int thePhaseId = entry.getKey();
            for (Map.Entry<Long, Integer> subEntry : entry.getValue().entrySet()) {
                long packageId = subEntry.getKey();
                int count = subEntry.getValue();
                String hashKey = thePhaseId + ":" + packageId;
                rollbackMap.put(hashKey, count);
                if (packageId == attr.getAmethystCouponAwardPackageId()) {
                    amethystCouponRollbackMap.put(hashKey, count);
                }
                long afterValue = actRedisDao.hIncrByKey(groupCode, key, hashKey, -count);
                if (afterValue < 0) {
                    for (Map.Entry<String, Integer> rollbackEntry : rollbackMap.entrySet()) {
                        actRedisDao.hIncrByKey(groupCode, key, rollbackEntry.getKey(), rollbackEntry.getValue());
                    }
                    throw new BusinessException("领取失败,请刷新页面重试", 400);
                }

                delayAwardCountMap.compute(packageId, (k, v) -> (v == null) ? count : v + count);
            }
        }

        if (MapUtils.isEmpty(delayAwardCountMap)) {
            throw new BusinessException("暂时没有无可领取奖励", 500);
        }

        int amethystCouponAmount = delayAwardCountMap.getOrDefault(attr.getAmethystCouponAwardPackageId(), 0);

        if (amethystCouponAmount > 0) {
            long amethystCouponCeiling = attr.getAmethystCouponCeiling();
            if (amethystCouponCeiling > 0) {
                String ceilingKey = makeKey(attr, AMETHYST_COUPON_CEILING_KEY);
                List<Long> result = actRedisDao.hIncrWithLimit(groupCode, ceilingKey, "1", amethystCouponAmount, amethystCouponCeiling);
                if (result != null && result.size() > 0) {
                    if (result.get(0) <= 0) {
                        for (Map.Entry<String, Integer> rollbackEntry : amethystCouponRollbackMap.entrySet()) {
                            actRedisDao.hIncrByKey(groupCode, key, rollbackEntry.getKey(), rollbackEntry.getValue());
                            rollbackMap.remove(rollbackEntry.getKey());
                        }
                        delayAwardCountMap.remove(attr.getAmethystCouponAwardPackageId());
                    }
                }
            }
        }

        if (MapUtils.isEmpty(delayAwardCountMap)) {
            throw new BusinessException("手慢了~紫水晶券剩余数量不足，无法领取", 500);
        }

        String seq = "RAC_" + uid + "_" + System.currentTimeMillis();
        Date now = commonService.getNow(actId);
        String currentTime = DateUtil.format(now);
        BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(seq, uid, ImmutableMap.of(attr.getLevelUpgradeAwardTaskId(), delayAwardCountMap), currentTime, 2, Collections.emptyMap());
        log.info("receiveDelayAward done,seq={},uid={},amount={}, result={}", seq, uid, amethystCouponAmount, result);

        // 数据上报
        for (Map.Entry<String, Integer> entry : rollbackMap.entrySet()) {
            String hashKey = entry.getKey();
            String[] array = StringUtils.split(hashKey, ':');
            String amethystCouponListKey = makeKey(attr, DELAY_AWARD_LIST_KEY + uid + ":" + array[0]);
            List<String> values = actRedisDao.getRedisTemplate(groupCode).opsForList().range(amethystCouponListKey, 0, -1);
            values.forEach(value -> {
                long pkgId = Convert.toLong(value.split(":")[0]);
                int amount = Convert.toInt(value.split(":")[1]);
                int dataSourceType = pkgId == attr.getAmethystCouponAwardPackageId() ? BigDataScoreType.RECEIVE_AMETHYST : BigDataScoreType.RECEIVE_CHAOJIHAOBIAO;
                bigDataService.saveNoRankDataToFile(seq, attr.getActId(), BusiId.MAKE_FRIEND, now.getTime(), uid + ""
                        , RoleType.USER, amount, dataSourceType, "", 0, 0);
            });
        }

        //组织返回内容
        List<UserPaidGrowthComponentAttr.AwardAttrConfig> awardConfigs = attr.getAwardList();
        Map<Long, UserPaidGrowthComponentAttr.AwardAttrConfig> awardConfigMap = awardConfigs.stream().
                collect(Collectors.toMap(UserPaidGrowthComponentAttr.AwardAttrConfig::getPackageId, Function.identity(), (k1, k2) -> k1));
        List<DelayAwardInfo> rs = new ArrayList<>(delayAwardCountMap.size());
        for (Map.Entry<Long, Integer> entry : delayAwardCountMap.entrySet()) {
            DelayAwardInfo awardInfo = new DelayAwardInfo();
            long packageId = entry.getKey();
            int amount = entry.getValue();
            awardInfo.setPackageId(packageId);
            awardInfo.setAmount(amount);
            UserPaidGrowthComponentAttr.AwardAttrConfig awardAttr = awardConfigMap.get(packageId);
            if (awardAttr != null) {
                awardInfo.setAwardName(awardAttr.getAwardName());
                awardInfo.setAwardIcon(awardAttr.getAwardIcon());
                awardInfo.setUnit(awardAttr.getUnit());
            }

            rs.add(awardInfo);
        }

        return rs;
    }

    /**
     * 获取可领取的延迟奖励
     * key --> 阶段
     * value --> key:packageId，value:数量
     **/
    private Map<Integer, Map<Long, Integer>> getRecvableDelayAward(long uid, Integer phaseId, UserPaidGrowthComponentAttr attr) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String key = makeKey(attr, DELAY_AWARD_KEY + uid);
        Map<Object, Object> dbMap = actRedisDao.hGetAll(groupCode, key);
        Map<Integer, Map<Long, Integer>> result = Maps.newHashMap();
        for (Map.Entry<Object, Object> entry : dbMap.entrySet()) {
            String hashKey = (String) entry.getKey();
            String[] array = StringUtils.split(hashKey, ':');
            if (array != null && array.length > 1 && StringUtils.isNumeric(array[0])) {
                int thePhaseId = Integer.parseInt(array[0]);
                if (thePhaseId <= phaseId && thePhaseId > 0) {
                    long packageId = Long.parseLong(array[1]);
                    int amount = Convert.toInt(entry.getValue());
                    if (amount <= 0) {
                        continue;
                    }
                    result.compute(thePhaseId, (k, v) -> {
                        if (v == null) {
                            Map<Long, Integer> delayAwardCount = Maps.newHashMap();
                            delayAwardCount.put(packageId, Convert.toInt(entry.getValue()));
                            return delayAwardCount;
                        } else {
                            v.put(packageId, Convert.toInt(entry.getValue()));
                            return v;
                        }
                    });
                }
            }
        }

        return result;
    }

    /**
     * 查询经验获得记录
     **/
    public List<ExperienceRecord> queryExperienceRecordList(long uid, Long actId, Long componentUseIndex) {
        UserPaidGrowthComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            throw new IllegalArgumentException("param is error");
        }
        String key = makeKey(attr, String.format(EXPERIENCE_RECORD_KEY, uid));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        int queryCount = 50;
        List<String> recordStringList = actRedisDao.getRedisTemplate(groupCode).opsForList().range(key, 0, queryCount);
        if (CollectionUtils.isEmpty(recordStringList)) {
            return Collections.emptyList();
        }
        String json = "[" + StringUtils.join(recordStringList, ",") + "]";
        List<ExperienceRecord> records = JSONObject.parseArray(json, ExperienceRecord.class);

        return records;
    }

    /**
     * 我的等级信息
     **/
    public MyLevelInfo getMyLevelInfo(long uid, Long actId, Long componentUseIndex) {
        UserPaidGrowthComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            throw new IllegalArgumentException("param is error");
        }
        MyLevelInfo myLevelInfo = new MyLevelInfo();

        // 基本信息
        fillUserInfo(uid, myLevelInfo);

        // 等级信息
        fillTaskInfo(uid, myLevelInfo, attr, false);

        // 可领取的紫水晶
        fillDelayAwardInfo(uid, myLevelInfo, attr);

        return myLevelInfo;
    }

    private UserInfoVo getUserInfoVo(long uid) {
        UserInfoVo userInfoVo = userInfoService.getUserInfo(Lists.newArrayList(uid), Template.makefriend).get(uid);
        if (userInfoVo == null) {
            UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
            userInfoVo = new UserInfoVo();
            userInfoVo.setNick(userInfo.getNick());
            userInfoVo.setUid(uid);
            userInfoVo.setAvatarUrl(userInfo.getLogo());
        }

        return userInfoVo;
    }

    private void fillUserInfo(long uid, MyLevelInfo myLevelInfo) {
        UserInfoVo userInfoVo = getUserInfoVo(uid);
        myLevelInfo.setUid(uid);
        myLevelInfo.setNickname(userInfoVo.getNick());
        myLevelInfo.setLogo(userInfoVo.getAvatarUrl());
    }

    private void fillTaskInfo(long uid, MyLevelInfo myLevelInfo, UserPaidGrowthComponentAttr attr, boolean isMedalHover) {
        if (uid <= 0) {
            return;
        }
        myLevelInfo.setMaxLevel(attr.getMaxLevel());

        List<RankPhasePair> rankPhasePairList = new ArrayList<>();
        RankPhasePair rankPhasePair = new RankPhasePair();
        rankPhasePair.setRankId(attr.getLevelUpgradeRankId());
        rankPhasePair.setPhaseId(attr.getLevelUpgradePhaseId());
        rankPhasePairList.add(rankPhasePair);

        TaskUserInfoVo taskUserInfoVo = hdztTaskService.getUserTaskInfo(uid, attr.getActId(), attr.getLevelUpgradeRankId()
                , attr.getLevelUpgradePhaseId(), "");
        if (taskUserInfoVo == null || taskUserInfoVo.getMissions() == null) {
            return;
        }
        List<BabyMissionItem> missionItems = taskUserInfoVo.getMissions();
        BabyMissionItem missionItem = missionItems.get(0);
        List<TaskItem> taskItems = missionItem.getTaskItems();

        // 已完成等级
        Long curTaskId = taskUserInfoVo.getCurTaskId();
        Long currentLevel = Math.max(curTaskId - 1, 0);

        Long currentValue = missionItem.getCompletedCount();

        // 已完成最高等级
        Long maxTaskId = missionItem.getAllTaskCount();
        long totalPassValue = taskItems.stream().mapToLong(TaskItem::getPassValue).sum();
        if (currentValue >= totalPassValue) {
            currentLevel = maxTaskId;
        }

        myLevelInfo.setCurrentLevel(currentLevel.intValue());
        myLevelInfo.setCurrentMedalUrl(attr.getLevelMedalMap().getOrDefault(currentLevel, ""));

        // 当前完成的进度
        myLevelInfo.setCurrentScore(currentValue);


        if (!isMedalHover) {
            // 下一等级差
            long passValue = 0L;
            for (TaskItem taskItem : taskItems) {
                if (taskItem.getTaskId() <= curTaskId) {
                    passValue += taskItem.getPassValue();
                }
            }
            myLevelInfo.setNextLevelGap(Math.max(passValue - currentValue, 0));

            // 等级配置
            List<LevelConfig> levelConfigs = new ArrayList<>();
            passValue = 0;
            for (TaskItem taskItem : taskItems) {
                passValue += taskItem.getPassValue();
                LevelConfig levelConfig = new LevelConfig();
                levelConfig.setScore(passValue);
                levelConfig.setLevel(taskItem.getTaskId().intValue());
                levelConfigs.add(levelConfig);
            }
            myLevelInfo.setLevelConfigs(levelConfigs);
        }
    }

    private void fillDelayAwardInfo(long uid, MyLevelInfo myLevelInfo, UserPaidGrowthComponentAttr attr) {
        UserPaidGrowthComponentAttr.PhaseConfig currentPhase = findCurrentPhase(attr);
        if (currentPhase == null) {
            return;
        }

        long sendAmount = 0;
        final int phaseId = currentPhase.getPhaseId();
        String ceilingKey = makeKey(attr, AMETHYST_COUPON_CEILING_KEY);
        String sendAmountStr = actRedisDao.hget(getRedisGroupCode(attr.getActId()), ceilingKey, "1");
        if (StringUtils.isNumeric(sendAmountStr)) {
            sendAmount = Long.parseLong(sendAmountStr);
        }

        myLevelInfo.setLeftAmethystCouponAmount(Math.max(0, attr.getAmethystCouponCeiling() - sendAmount));
        Map<Integer, Map<Long, Integer>> delayAwardMap = getRecvableDelayAward(uid, 1000, attr);
        if (MapUtils.isEmpty(delayAwardMap)) {
            myLevelInfo.setDelayAwardInfos(Collections.emptyList());
            myLevelInfo.setAmethystCouponAmount(0);
            myLevelInfo.setCanReceive(0);
            myLevelInfo.setHoverDesc(StringUtils.EMPTY);
            return;
        }

        List<UserPaidGrowthComponentAttr.AwardAttrConfig> awardAttrConfigs = attr.getAwardList();
        Map<Long, UserPaidGrowthComponentAttr.AwardAttrConfig> attrConfigMap = awardAttrConfigs.stream().
                collect(Collectors.toMap(UserPaidGrowthComponentAttr.AwardAttrConfig::getPackageId, Function.identity(), (k1, k2) -> k1));
        Map<Long, Integer> delayAwardCountMap = Maps.newHashMap();
        for (Map<Long, Integer> countMap : delayAwardMap.values()) {
            for (Map.Entry<Long, Integer> entry : countMap.entrySet()) {
                int amount = entry.getValue();
                long packageId = entry.getKey();
                delayAwardCountMap.compute(packageId, (k, v) -> (v == null) ? amount : v + amount);
            }
        }

        long amethystCouponAmount = delayAwardCountMap.getOrDefault(attr.getAmethystCouponAwardPackageId(), 0);
        myLevelInfo.setAmethystCouponAmount(amethystCouponAmount);

        List<DelayAwardInfo> delayAwardInfos = new ArrayList<>(delayAwardCountMap.size());
        List<String> tips = new ArrayList<>(delayAwardCountMap.size());
        for (Map.Entry<Long, Integer> entry : delayAwardCountMap.entrySet()) {
            long packageId = entry.getKey();
            int amount = entry.getValue();
            DelayAwardInfo awardInfo = new DelayAwardInfo();
            awardInfo.setPackageId(packageId);
            awardInfo.setAmount(amount);
            UserPaidGrowthComponentAttr.AwardAttrConfig attrConfig = attrConfigMap.get(packageId);
            if (attrConfig != null) {
                awardInfo.setUnit(attrConfig.getUnit());
                awardInfo.setAwardName(attrConfig.getAwardName());
                awardInfo.setAwardIcon(attrConfig.getAwardIcon());
                tips.add(String.format(attrConfig.getTipTemplate(), amount));
            }

            delayAwardInfos.add(awardInfo);
        }

        myLevelInfo.setDelayAwardInfos(delayAwardInfos);

        boolean recvablePhase = false;
        List<UserPaidGrowthComponentAttr.PhaseConfig> phaseList = attr.getPhaseList();
        for (UserPaidGrowthComponentAttr.PhaseConfig phaseConfig : phaseList) {
            if (Objects.equals(phaseConfig.getNextPhaseId(), phaseId)) {
                recvablePhase = true;
                break;
            }
        }

        final boolean isRecvablePhase = recvablePhase;

        String tipsPrefix = StringUtils.EMPTY;
        if (tips.size() > 0) {
            tipsPrefix = "已获得" + StringUtils.join(tips, "和") + "，";
        }
        int minPhaseId = delayAwardMap.keySet().stream().filter(key -> ((key <= phaseId) && isRecvablePhase)).findFirst().orElse(0);
        if (minPhaseId > 0) {
            // 当前阶段可领取
            myLevelInfo.setCanReceive(1);
            myLevelInfo.setHoverDesc(tipsPrefix + "将在 "
                    + currentPhase.getCurrentActName()
                    + " 开放领取");
        } else {
            // 在下一阶段可领取
            myLevelInfo.setCanReceive(0);
            UserPaidGrowthComponentAttr.PhaseConfig nextPhase = findNextPhase(attr, currentPhase.getNextPhaseId());
            myLevelInfo.setHoverDesc(tipsPrefix + "将在 "
                    + nextPhase.getCurrentActName()
                    + " 开放领取");
        }
    }

    //缓存五分钟
    @Cached(timeToLiveMillis = 5 * 60 * 1000)
    public List<JSONObject> batchMedalInfos(List<String> uids, Long actId, Long componentUseIndex) {
        List<JSONObject> levelInfos = new ArrayList<>();
        UserPaidGrowthComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        Map<String, Rank> ranking = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getLevelUpgradeRankId(), attr.getLevelUpgradePhaseId(), "", uids, Maps.newHashMap());
        Map<Long, Long> levelExperienceMap = attr.getLevelExperienceMap();
        ImmutableSortedMap<Long, Long> thresholdMap = ImmutableSortedMap.copyOf(levelExperienceMap);
        Map<Long, String> levelMedalMap = attr.getLevelMedalMap();
        ranking.values().forEach(it -> {
            JSONObject info = new JSONObject();
            long score = it.getScore();
            if (thresholdMap.floorEntry(score) == null) {
                return;
            }
            Long level = thresholdMap.floorEntry(score).getValue();
            String medalUrl = levelMedalMap.get(level);
            info.put("member", Convert.toLong(it.getMember()));
            info.put("growMedalUrl", medalUrl);
            info.put("growMedalLevel", level);
            levelInfos.add(info);
        });
        return levelInfos;
    }

    /**
     * 勋章hover信息
     **/
    public HoverLevelInfo medalHover(long uid, Long actId, Long componentUseIndex) {
        UserPaidGrowthComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            throw new IllegalArgumentException("param is error");
        }
        HoverLevelInfo hoverLevelInfo = new HoverLevelInfo();
        if (uid <= 0) {
            return hoverLevelInfo;
        }
        fillUserInfo(uid, hoverLevelInfo);
        fillTaskInfo(uid, hoverLevelInfo, attr, true);
        fillH5Info(uid, hoverLevelInfo, attr);

        return hoverLevelInfo;
    }

    private void fillH5Info(long uid, HoverLevelInfo hoverLevelInfo, UserPaidGrowthComponentAttr attr) {
        hoverLevelInfo.setHeight(attr.getH5WindowHeight());
        hoverLevelInfo.setWidth(attr.getH5WindowWidth());
        hoverLevelInfo.setRankValue(0);
        hoverLevelInfo.setSkipUrl(attr.getH5Url());

        // 查询威望总榜
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getRankId()
                , attr.getPhaseId(), "", uid + "", Maps.newHashMap());

        if (rank != null) {
            hoverLevelInfo.setRankValue(rank.getScore());
        }
    }

    /**
     * 每日经验信息
     **/
    public DailyExperienceInfo dailyExperienceInfo(long uid, Long actId, Long componentUseIndex) {
        UserPaidGrowthComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            throw new IllegalArgumentException("param is error");
        }
        UserPaidGrowthComponentAttr.PhaseConfig currentPhase = findCurrentPhase(attr);
        if (currentPhase == null) {
            throw new IllegalArgumentException("currentPhase is nulls");
        }

        DailyExperienceInfo dailyExperienceInfo = new DailyExperienceInfo();
        dailyExperienceInfo.setActUrl(currentPhase.getCurrentActUrl());
        dailyExperienceInfo.setCurrentActId(currentPhase.getCurrentActId());
        dailyExperienceInfo.setCurrentActName(currentPhase.getCurrentActName());

        List<String> giftIds = Arrays.asList(currentPhase.getGiftIds().split(","));
        List<String> giftIcons = attr.getGiftList().stream().filter(gift -> giftIds.contains(gift.getGiftId() + "")).map(UserPaidGrowthComponentAttr.GiftConfig::getGiftIcon).collect(Collectors.toList());

        dailyExperienceInfo.setGiftIcons(giftIcons);

        // 倍数
        List<ExperienceTaskInfo> taskInfos = new ArrayList<>();
        Map<Long, Double> multipleMap = attr.getDefaultExperienceMultipleMap();
        UserPaidGrowthComponentAttr.PhaseConfig phaseConfig = findCurrentPhase(attr);
        if (phaseConfig != null) {
            multipleMap = attr.getExperienceMultipleMap().getOrDefault(phaseConfig.getPhaseId(), multipleMap);
        }
        for (Map.Entry<Long, Double> entry : multipleMap.entrySet()) {
            ExperienceTaskInfo taskInfo = new ExperienceTaskInfo();
            taskInfo.setThreshold(entry.getKey());
            taskInfo.setMultiple(entry.getValue());
            taskInfos.add(taskInfo);
        }
        taskInfos = taskInfos.stream().sorted(Comparator.comparingLong(ExperienceTaskInfo::getThreshold)).collect(Collectors.toList());

        dailyExperienceInfo.setTaskInfos(taskInfos);

        // 是否加倍
        long firstKey = multipleMap.entrySet().stream().findFirst().get().getKey();
        if (!Objects.equals(multipleMap.get(firstKey), attr.getDefaultExperienceMultipleMap().get(firstKey))) {
            dailyExperienceInfo.setShowDoubleInfo(true);
        }

        // 当日已获得经验值
        dailyExperienceInfo.setExperienceLimit(attr.getExperienceDailyLimit());
        String groupCode = getRedisGroupCode(actId);
        String key = makeKey(attr, String.format(DAILY_INFO_KEY, DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2), uid));

        Map<Object, Object> todayMap = actRedisDao.hGetAll(groupCode, key);

        String todayExperienceStr = todayMap.getOrDefault(TODAY_EXPERIENCE, "0").toString();
        long todayExperience = Convert.toLong(todayExperienceStr, 0);
        todayExperience = Math.min(attr.getExperienceDailyLimit(), todayExperience);
        dailyExperienceInfo.setExperience(todayExperience);

        String prestigeValue = todayMap.getOrDefault(TOTAL_VALUE_TODAY, "0").toString();
        dailyExperienceInfo.setPrestigeValue(Convert.toLong(prestigeValue, 0));

        return dailyExperienceInfo;
    }
}

