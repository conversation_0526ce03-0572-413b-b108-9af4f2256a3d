<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="stringCasScript" class="org.springframework.data.redis.core.script.DefaultRedisScript">
        <property name="resultType" value="java.lang.Long" />
        <property name="location">
            <value>classpath:lua/string_cas.lua</value>
        </property>
    </bean>

    <bean id="hashCasScript" class="org.springframework.data.redis.core.script.DefaultRedisScript">
        <property name="resultType" value="java.lang.Long" />
        <property name="location">
            <value>classpath:lua/hash_cas.lua</value>
        </property>
    </bean>

    <bean id="hsetGrownValueScript" class="org.springframework.data.redis.core.script.DefaultRedisScript">
        <property name="resultType" value="java.lang.Long" />
        <property name="location">
            <value>classpath:lua/hset_grown_value.lua</value>
        </property>
    </bean>

    <bean id="zpopByScoreScript" class="org.springframework.data.redis.core.script.DefaultRedisScript">
        <property name="resultType" value="java.util.List" />
        <property name="location">
            <value>classpath:lua/zpop_by_score.lua</value>
        </property>
    </bean>

    <bean id="zpopByScoreWithScoresScript" class="org.springframework.data.redis.core.script.DefaultRedisScript">
        <property name="resultType" value="java.util.List" />
        <property name="location" value="classpath:lua/zpop_by_score_withscores.lua"/>
    </bean>
</beans>