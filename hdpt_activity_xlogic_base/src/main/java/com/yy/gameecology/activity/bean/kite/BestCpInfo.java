package com.yy.gameecology.activity.bean.kite;


import lombok.Data;

@Data
public class BestCpInfo {
    private long userUid;
    private String userLogo;
    private String userNick;
    private long babyUid;
    private String babyLogo;
    private String babyNick;

    /**
     * 开播频道
     */
    private Long sid;
    private Long ssid;

    /**
     * 增加的公里
     */
    private long addStep;


    private String awardText;

    /**
     * 统计时展示，礼物个数*2
     */
    private String staticAwardText;
}
