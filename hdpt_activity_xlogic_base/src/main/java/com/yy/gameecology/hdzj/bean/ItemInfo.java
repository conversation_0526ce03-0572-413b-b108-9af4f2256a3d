package com.yy.gameecology.hdzj.bean;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> 2024/3/21
 */
@Data
public class ItemInfo {
    
    /**
     * 商品id
     */
    private String itemId;
    
    /**
     * 商品名
     */
    private String itemName;
    
    /**
     * 商品图片
     */
    private String itemImage;
    
    /**
     * 商品价格
     */
    private long price;
    
    /**
     * 库存剩余 -1代表不限
     */
    private long remain;
    
    /**
     * 用户日限制 -1代表不限
     */
    private long userDayLimit;

    /**
     * 用户总限制-1代表不限
     */
    private long userLimit;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 显示金额(元)
     */
    private String money;
    
    /**
     * 0-库存充足 1-库存不足 2-用户已兑换 3-用户已兑换未填写信息 4-用户已兑换未发放成功
     */
    private int status;

    /**
     * 数量
     */
    private long num;

    /**
     * key 显示分区 ;  value 排序
     */
    private Map<String,Long> areaSort = Maps.newHashMap();

    /**
     * itemtype
     */
    private int rewardType;

    private long taskId;

    private long packageId;

    private String extJson;

    private String msg;
}

