package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSONObject;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.TurnoverServiceClient;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.common.AwardConfig;
import com.yy.gameecology.activity.service.common.AwardService;
import com.yy.gameecology.activity.service.common.PrizeVO;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.AwardAttrByComponent;
import com.yy.gameecology.hdzj.bean.AwardVO;
import com.yy.gameecology.hdzj.bean.ConsumeBO;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.GiftPackageV2ComponentAttr;
import com.yy.gameecology.hdzj.utils.ResponseUtils;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.turnover.TAppId;
import com.yy.thrift.turnover.TConsumeProductRequest;
import com.yy.thrift.turnover.TConsumeProductResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 功能：
 *
 * 该组件已废弃，有需要，请使用 2021组件
 * @see GiftPackageQRCodePayComponent
 * <AUTHOR> on 2021/7/6.
 */
@Component
@Slf4j
@Deprecated
public class GiftPackageV2Component extends BaseActComponent<GiftPackageV2ComponentAttr> {

  @Autowired
  private TurnoverServiceClient turnoverServiceClient;

  @Autowired
  private HdztAwardServiceClient hdztAwardServiceClient;

  @Autowired
  private ActInfoService actInfoService;

  @Autowired
  private Locker locker;

  @Autowired
  private AwardService awardService;

  private static final String BUY_GIFT_PACKAGE_IP_LIMIT_KEY = "buyGiftPackageIpLimit:%s";

  private static final String BUY_RECORD = "buyRecord";

  private static final String BUY_LOCK = "GiftPackage_lock_%s";

  private static final String AWARD_COUNT = "gp_ac";

  @Override
  public Long getComponentId() {
    return ComponentId.GIFT_PACKAGE_BUY_V2;
  }

  public Response<JSONObject> giftPackageStatus(GiftPackageV2ComponentAttr attr, long uid) {
    boolean hasQualification = true;
    String groupCode = getRedisGroupCode(attr.getActId());
    if (attr.isLimitQualification()) {
      boolean contains = actRedisDao.sIsMember(groupCode, attr.getLimitUidRedisKey(), String.valueOf(uid));
      hasQualification = (contains && attr.isAllow()) || (!contains && !attr.isAllow());
    }
    JSONObject data = new JSONObject();
    if (hasQualification) {
      String redisKey = getRedisKey(attr, BUY_RECORD);
      boolean canBuy =
          Convert.toInt(actRedisDao.zscore(groupCode, redisKey, String.valueOf(uid)), 0)
              < attr.getLimitBuyCount();
      data.put("canBuy", canBuy ? 1 : 0);
    } else {
      data.put("canBuy", -1);
    }

    return Response.success(data);
  }

  Response<String> buyGiftPackage(GiftPackageV2ComponentAttr attr, long uid, String ip) {
    Clock clock = new Clock();
    String groupCode = getRedisGroupCode(attr.getActId());
    Response<String> response = check(attr, uid, ip);
    if (ResponseUtils.isFail(response)) {
      return response;
    }
    Response<ConsumeBO> consumeBOResponse = prepareConsumeBO(attr, uid, ip);
    if (ResponseUtils.isFail(consumeBOResponse)) {
      return Response.fail(consumeBOResponse.getResult(), consumeBOResponse.getReason());
    }
    clock.tag();
    String lockName = getRedisKey(attr, String.format(BUY_LOCK, uid));
    Secret lock = null;
    try {
      lock = locker.lock(lockName, 30);
      if (lock != null) {
        String userGiftPackageKey = getRedisKey(attr, BUY_RECORD);
        // 达到限制购买次数
        if (attr.getLimitBuyCount() > 0
            && actRedisDao
            .zIncrWithLimit(groupCode, userGiftPackageKey, String.valueOf(uid), 1, attr.getLimitBuyCount())
            .get(0) != 1L) {
          return Response.fail(2, attr.getLimitBuyCountTip());
        }
        response = consume(consumeBOResponse.getData());
        if (ResponseUtils.isFail(response)) {
          // 购买失败
          actRedisDao.zIncr(groupCode, userGiftPackageKey, String.valueOf(uid), -1L);
          return response;
        }
        clock.tag();
        // 抽奖
        Response<List<AwardVO>> awardResp = award(attr, uid);
        if (ResponseUtils.isFail(awardResp)) {
          log.warn("buy gift package award fail,uid:{},seq:{},code:{}", uid, response.getResult(),
              awardResp.getResult());
          return Response.fail(awardResp.getResult(), awardResp.getReason());
        }
        clock.tag();
        // 发奖
        response = delivery(uid, awardResp.getData());
        if (response.success()) {
          addIpLimit(attr, ip, uid);
          return buildResp(attr, awardResp);
        }
      }
    } catch (Exception e) {
      log.warn("buyGiftPackage error {}", e.getMessage(), e);
    } finally {
      if (lock != null) {
        locker.unlock(lockName, lock);
      }
    }
    return Response.fail(2, "当前购买礼包人数较多，请稍后再试");
  }

  private void addIpLimit(GiftPackageV2ComponentAttr attr, String ip, long uid) {
    String ipKey = getRedisKey(attr, String.format(BUY_GIFT_PACKAGE_IP_LIMIT_KEY, ip));
    actRedisDao.set(getRedisGroupCode(attr.getActId()), ipKey, String.valueOf(uid), attr.getIpLimitSec());
  }

  private Response<String> buildResp(
      GiftPackageV2ComponentAttr attr, Response<List<AwardVO>> awardResp) {
    return Response.ok(attr.getSuccessPromotion());
  }

  private Response<String> delivery(long uid, List<AwardVO> data) {
    Map<Long, Map<Long, Integer>> taskPackageIds =
        data.stream()
            .map(vo -> {
              Table<Long, Long, Integer> table = HashBasedTable.create();
              table.put(vo.getTaskId(), vo.getPackageId(), vo.getCount());
              return table;
            })
            .reduce(HashBasedTable.create(),
                (t1, t2) -> {
                  t1.putAll(t2);
                  return t1;
                })
            .rowMap();
    hdztAwardServiceClient.doBatchWelfare(uid, taskPackageIds, DateUtil.getNowYyyyMMddHHmmss(), 3, Maps.newHashMap());
    log.info("buyGiftPackage doWelfare uid:{}", uid);
    return Response.ok();
  }

  private Response<List<AwardVO>> award(GiftPackageV2ComponentAttr attr, long uid) {
    List<AwardAttrByComponent> awards = attr.getAwards();
    List<AwardConfig> configs =
        awards.stream()
            .map(
                ac -> {
                  List<PrizeVO> prizeVOS =
                      ac.getPrizes().stream()
                          .map(
                              prize ->
                                  PrizeVO.builder()
                                      .prizeId(prize.getPrizeId())
                                      .taskId(prize.getTaskId())
                                      .packageId(prize.getPackageId())
                                      .limit(prize.getLimit())
                                      .min(prize.getMin())
                                      .max(prize.getMax())
                                      .probability(prize.getProbability())
                                      .build())
                          .collect(Collectors.toList());
                  return AwardConfig.builder()
                      .awardType(ac.getAwardType())
                      .prizes(prizeVOS)
                      .awardKey(StringUtils.EMPTY)
                      .build();
                })
            .collect(Collectors.toList());
    return awardService.award(configs, getRedisKey(attr, AWARD_COUNT), uid);
  }

  private TAppId findTAppId(int appId) {
    TAppId id = TAppId.findByValue(appId);
    if (id == null) {
      if (TAppId.Baby.getValue() == appId) {
        return TAppId.Baby;
      }
      return Arrays.stream(TAppId.values()).filter(tAppId -> tAppId.getValue() == appId)
          .findFirst()
          .orElse(null);
    }
    return id;
  }

  private Response<String> consume(ConsumeBO bo) {
    TConsumeProductRequest req = new TConsumeProductRequest();
    req.setUid(bo.getUid());
    req.setAppid(findTAppId(bo.getAppId()));
    req.setProductType(bo.getProductType());
    // productId随便，没有填0
    req.setProductId(bo.getProductId());
    // amount
    req.setAmount(bo.getAmount());
    req.setSeqId(bo.getSeqId());
    req.setDescription(bo.getDescription());
    TConsumeProductResult result = turnoverServiceClient.consumeProductNew(req);
    if (log.isInfoEnabled()) {
      log.info(
          "buyGiftPackage consumeProductNew uid:{} pType:{} amount:{} seq:{} result:{}",
          bo.getUid(),
          bo.getProductType(),
          bo.getAmount(),
          bo.getSeqId(),
          result);
    }
    if (result != null && result.getCode() == 1) {
      return Response.ok(bo.getSeqId());
    }
    final int codem23 = -23;
    if (result != null && result.getCode() == codem23) {
      return Response.fail(1, "Y币余额不足，请及时充值");
    }
    return Response.fail(2, "当前购买礼包人数较多，请稍后再试");
  }

  private Response<ConsumeBO> prepareConsumeBO(
      GiftPackageV2ComponentAttr attr, long uid, String ip) {
    int appId = getConsumeAppId(attr);
    if (findTAppId(appId) == null) {
      return Response.fail(2, "当前购买礼包人数较多，请稍后再试");
    }

    String seq = UUID.randomUUID().toString();
    ConsumeBO consumeBO =
        ConsumeBO.builder()
            .appId(appId)
            .uid(uid)
            .productType(attr.getProductType())
            .productId(attr.getProductId())
            .amount(attr.getPrice())
            .seqId(seq)
            .description(attr.getGiftDes())
            .build();
    return ResponseUtils.success(consumeBO);
  }

  private int getConsumeAppId(GiftPackageV2ComponentAttr attr) {
    return changeToAppId(attr);
  }

  private Response<String> check(GiftPackageV2ComponentAttr attr, long uid, String ip) {
    Response<String> response = checkActivityIsEnabled(attr);
    if (ResponseUtils.isFail(response)) {
      return response;
    }
    response = checkLimitQualification(attr, uid);
    if (ResponseUtils.isFail(response)) {
      return response;
    }
    response = checkLimitIp(attr, ip);
    return response;
  }

  private Response<String> checkLimitIp(GiftPackageV2ComponentAttr attr, String ip) {
    String ipKey = getRedisKey(attr, String.format(BUY_GIFT_PACKAGE_IP_LIMIT_KEY, ip));
    if (StringUtils.isNotBlank(actRedisDao.get(getRedisGroupCode(attr.getActId()), ipKey))) {
      log.warn("buyGiftPackage ip limit,key：{}", ipKey);
      return Response.fail(2, "当前购买礼包人数较多，请稍后再试");
    }
    return Response.success(StringUtils.EMPTY);
  }

  @VisibleForTesting
  public String getRedisKey(GiftPackageV2ComponentAttr attr, String keySuffix) {
    if (attr.isGlobalLimitCount()) {
      return makeKey(attr.getActId(), attr.getCmptId(), 0L, keySuffix);
    }
    return makeKey(attr, keySuffix);
  }

  private Response<String> checkLimitQualification(GiftPackageV2ComponentAttr attr, long uid) {
    if (!commonService.checkWhiteList(attr.getActId(), RoleType.USER, String.valueOf(uid))) {
      return Response.fail(2, "活动暂未开始");
    }
    if (!attr.isLimitQualification()) {
      return Response.success(StringUtils.EMPTY);
    }
    try {
      boolean contains = actRedisDao.sIsMember(getRedisGroupCode(attr.getActId()), attr.getLimitUidRedisKey(), String.valueOf(uid));
      boolean hasQualification = (contains && attr.isAllow()) || (!contains && !attr.isAllow());
      if (!hasQualification) {
        return Response.fail(2, attr.getLimitQualificationTip());
      }
    } catch (Exception e) {
      // ignore redis exception
    }
    return Response.success(StringUtils.EMPTY);
  }

  private Response<String> checkActivityIsEnabled(GiftPackageV2ComponentAttr attr) {
    if (!actInfoService.inActTime(attr.getActId())) {
      return Response.fail(2, "活动已结束");
    }
    return Response.success("");
  }

  public Response<JSONObject> giftPackageStatus(long actId, long index, long uid) {
    if (!isMyDuty(actId)) {
      throw new BadRequestException("无效活动礼包");
    }

    GiftPackageV2ComponentAttr attr = getComponentAttr(actId, index);
    if (attr != null) {
      return giftPackageStatus(attr, uid);
    }
    throw new ParameterException("index error.");
  }

  public Response<String> buyGiftPackage(long actId, long index, long uid, String ip) {
    if (!isMyDuty(actId)) {
      throw new BadRequestException("无效活动礼包");
    }
    GiftPackageV2ComponentAttr attr = getComponentAttr(actId, index);
    if (attr != null) {
      return buyGiftPackage(attr, uid, ip);
    }
    throw new ParameterException("index error.");
  }

  private int changeToAppId(GiftPackageV2ComponentAttr attr) {
    BusiId busiId = BusiId.findByValue((int) attr.getBusiId());
    if (busiId == null) {
      return attr.getConsumeAppId();
    }
    TAppId tAppId = changeToAppId(busiId);
    if (tAppId == null) {
      return attr.getConsumeAppId();
    }
    return tAppId.getValue();
  }

  private TAppId changeToAppId(BusiId busiId) {
    switch (busiId) {
      case YUE_ZHAN:
        return TAppId.VipPk;
      case GAME_BABY:
        return TAppId.Baby;
      case MAKE_FRIEND:
        return TAppId.Dating;
      default:
        return null;
    }
  }
}
