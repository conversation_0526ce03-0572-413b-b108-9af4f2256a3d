
package com.yy.gameecology.common.utils;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Title: Description: Create Time: 2014-04-01 下午4:20 author: wangyan version: 1.0
 */
public class HttpClientHelper {
    private static final Logger log = LoggerFactory.getLogger(HttpClientHelper.class);

    private HttpClient httpClient;

    private Charset charset;

    public HttpClientHelper(Charset charset) {
        httpClient = HttpClients.createDefault();
        this.charset = charset;
    }

    public String excutePostString(String targetUrl,String mimeType, String param) throws IOException {
        HttpPost httpost = new HttpPost(targetUrl);

        if (StringUtil.notBlank(param)) {
            StringEntity requestEntity = new StringEntity(param, mimeType, "utf-8");
            httpost.setEntity(requestEntity);
        }

        HttpResponse response = httpClient.execute(httpost);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }

    public String excutePost(String targetUrl, Map<String, String> params) throws IOException {
        HttpPost httpost = new HttpPost(targetUrl);

        if (params != null && !params.isEmpty()) {
            List<NameValuePair> data = new ArrayList<NameValuePair>();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                NameValuePair pair = new BasicNameValuePair(entry.getKey(), entry.getValue());
                data.add(pair);
            }
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(data, charset.name());
            httpost.setEntity(entity);
        }

        HttpResponse response = httpClient.execute(httpost);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }


    /**
     *
     * <p>Description: post 报文使用string</p>
     * <AUTHOR>
     * @date 2019年1月3日
     *
     *  @param targetUrl
     *  @param param
     *  @return
     *  @throws IOException
     *  String
     * @throws
     */
    public String excutePostString(String targetUrl, String param) throws IOException {
        HttpPost httpost = new HttpPost(targetUrl);

        if (StringUtil.notBlank(param)) {
            StringEntity requestEntity = new StringEntity(param, "utf-8");
            httpost.setEntity(requestEntity);
        }

        HttpResponse response = httpClient.execute(httpost);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }

    public String excuteGetWithHeader(String targetUrl, Map<String, String> headers) throws IOException {
        HttpGet httpGet = new HttpGet(targetUrl);
        if(!CollectionUtils.isEmpty(headers)) {
            for(String key : headers.keySet()) {
                httpGet.addHeader(key, headers.get(key));
            }
        }
        HttpResponse response = httpClient.execute(httpGet);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }

    public String excuteGet(String targetUrl) throws IOException {
        HttpGet httpGet = new HttpGet(targetUrl);
        HttpResponse response = httpClient.execute(httpGet);
        String responseString = EntityUtils.toString(response.getEntity());
        return responseString;
    }

    public static void main(String[] args) {
        HttpClientHelper httpClientHelper = new HttpClientHelper(MyCharsets.UTF_8);
        try {
            /*
             * Map<String, String> map = Maps.newHashMap(); map.put("type", "3"); map.put("expect",
             * "20140401"); map.put("status", "2"); map.put("vsid", "426766");
             */
            String rep = httpClientHelper.excutePost("http://zjgh.g.yy.com/zijinrank/zijinow", null);
            System.out.println(rep);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }
}
