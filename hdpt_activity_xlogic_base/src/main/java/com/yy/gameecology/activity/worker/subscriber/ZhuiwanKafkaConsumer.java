package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.event.ActRiskReCheckEvent;
import com.yy.gameecology.activity.bean.event.SkillCardSeatChgKafkaEvent;
import com.yy.gameecology.activity.bean.mq.*;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.mq.DanmakuChannelGameEvent;
import com.yy.gameecology.activity.bean.mq.GameTeamActionEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiWanGiftEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanGiftComboKafkaEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.UdbUtils;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.turnover.TAppId;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-03-06 13:00
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
public class ZhuiwanKafkaConsumer {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SendGiftConsumer sendGiftConsumer;
    @Autowired
    private RedisConfigManager redisConfigManager;
    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;

    @Autowired
    private BroActLayerService broActLayerService;

    /**
     * 追玩登录事件
     */
    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-login-hdzk",
            topics = "zhuiwan_login_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanUserLoginEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("zhuiwanUserLoginEvent msg:{}", value);
            ZhuiwanLoginEvent event = JSON.parseObject(value, ZhuiwanLoginEvent.class);
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("zhuiwanUserLoginEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    /**
     * 交友语音联运房连送事件
     * @param consumerRecord
     */
    @KafkaListener(containerFactory = "turnoverKafkaContainerFactory", id = "kafka-zhuiwan-gift-combo-jy",
            topics = "common_prop_combo_end_2",
            groupId = "${kafka.hdpt.wx.group}")
    public void onJyComboEvent(ConsumerRecord<String, String> consumerRecord) {
        log.info("jy combo {} ,msg:{}", this.getClass().getSimpleName(), consumerRecord.value());
        onZhuiwanGiftComboEvent(consumerRecord);
    }

    /**
     * 追玩的连送事件 没办法 取代追玩的送礼事件
     * 因为追玩有个“全麦送礼”动作，只有送礼事件是有的。
     * 连送事件的元数据 只是 追玩送礼事件的 子集。
     * @param consumerRecord
     */
    @KafkaListener(containerFactory = "turnoverKafkaContainerFactory", id = "kafka-zhuiwan-gift-combo",
            topics = "common_prop_combo_end_34",
            groupId = "${kafka.hdpt.wx.group}")
    public void onZhuiwanGiftComboEvent(ConsumerRecord<String, String> consumerRecord) {
        log.info("combo{} ,msg:{}", this.getClass().getSimpleName(), consumerRecord.value());
        ZhuiwanGiftComboKafkaEvent event = JSON.parseObject(consumerRecord.value(), ZhuiwanGiftComboKafkaEvent.class);
        String seq = event.getSequenceId();
        boolean suc = actRedisDao.zSetNX(redisConfigManager.temp_act_group, "Zhuiwan_Gift_combo_seq_" + SysEvHelper.getGroup(), seq, DateUtil.DAY_SEC * 2);
        if (!suc) {
            log.warn("ZhuiWanGiftComboListener ignore. seq double ,seq:{}", seq);
            return;
        }
        Template template = Template.ZhuiWan;
        if (event.getAppid() == TAppId.PeopleGame.getValue()) {
            template = Template.SkillCard;
        }

        int userChannel = event.getUsedChannel();
        /**
         * 追玩/yo 有个全麦打赏逻辑，区分是否全麦打赏 需要关注expand
         */
        JSONObject expandJo = new JSONObject();
        try {
            expandJo = JSONObject.parseObject(event.getExpand());
        }  catch (Exception e){
            log.error("onZhuiwanGiftComboEvent parse expand error:{}", e.getMessage());
            return;
        }

        long usedTimestamp = 0L;
        if(!expandJo.containsKey("usedTime")) {
            log.error("onZhuiwanGiftComboEvent lack timestamp, pls check");
            return;
        }
        usedTimestamp = expandJo.getLong("usedTime");
        SendGiftComboEvent sendGiftComboEvent = new  SendGiftComboEvent();
        sendGiftComboEvent.setTemplate(template);
        sendGiftComboEvent.setSeq(event.getSequenceId());
        sendGiftComboEvent.setSendUid(event.getUid());
        sendGiftComboEvent.setRecvUid(event.getAnchorUid());
        sendGiftComboEvent.setGiftId(event.getMeta().getId() + "");
        sendGiftComboEvent.setGiftNum((long)event.getCount());
        sendGiftComboEvent.setSid(event.getSid());
        sendGiftComboEvent.setSsid(event.getSsid());
        sendGiftComboEvent.setEventTime(new Date(usedTimestamp));
        sendGiftComboEvent.setSourceChannel(GiftSourceChannel.findByTurnover(userChannel));
        sendGiftComboEvent.setComboHits(event.getComboHits());
        sendGiftComboEvent.setJsonMap(expandJo);
        sendGiftConsumer.onMessage(sendGiftComboEvent);
        log.info("ZhuiWanGiftComboListener ok. seq:{}", seq);
    }


    /**
     * 追玩送礼事件
     */
    @KafkaListener(containerFactory = "turnoverKafkaContainerFactory", id = "kafka-zhuiwan-gift",
            topics = "zhuiwan_prop_used",
            groupId = "${kafka.hdpt.wx.group}")
    public void onZhuiwanGiftEvent(ConsumerRecord<String, String> consumerRecord) {
        log.info("{} ,msg:{}", this.getClass().getSimpleName(), consumerRecord.value());
        ZhuiWanGiftEvent event = JSON.parseObject(consumerRecord.value(), ZhuiWanGiftEvent.class);

        if (event == null || CollectionUtils.isEmpty(event.getUsedInfos())) {
            log.info("ZhuiWanGiftListener ignore. event:{}", JSON.toJSONString(event));
            return;
        }

        String seq = event.getSeq();
        boolean suc = actRedisDao.zSetNX(redisConfigManager.temp_act_group, "Zhuiwan_Gift_seq_" + SysEvHelper.getGroup(), seq, DateUtil.DAY_SEC * 2);
        if (!suc) {
            log.warn("ZhuiWanGiftListener ignore. seq double ,seq:{}", seq);
            return;
        }

        Template template = Template.ZhuiWan;
        if (event.getAppid() == TAppId.PeopleGame.getValue()) {
            template = Template.SkillCard;
        }

        int userChannel = event.getUsedChannel();

        /**
         * 追玩/yo 有个全麦打赏逻辑，区分是否全麦打赏 需要关注expand
         */
        JSONObject expandJo = new JSONObject();
        try {
            expandJo = JSONObject.parseObject(event.getExpand());
        }  catch (Exception e){
            log.error("onZhuiwanGiftEvent parse expand error:{}", e.getMessage());
            return;
        }

        for (int i = 0; i < event.getUsedInfos().size(); i++) {
            ZhuiWanGiftEvent.UsedInfo giftInfo = event.getUsedInfos().get(i);
            SendGiftEvent sendGiftEvent = new SendGiftEvent();
            sendGiftEvent.setTemplate(template);
            sendGiftEvent.setSeq(event.getSeq() + "_" + i);
            sendGiftEvent.setSendUid(event.getUsedUid());
            sendGiftEvent.setRecvUid(event.getAnchorUid());
            sendGiftEvent.setGiftId(giftInfo.getPropId() + "");
            sendGiftEvent.setGiftNum(giftInfo.getPropCount());
            sendGiftEvent.setGiftAmount(giftInfo.getAmount());
            sendGiftEvent.setSid(event.getSid());
            sendGiftEvent.setSsid(event.getSsid());
            sendGiftEvent.setEventTime(new Date(event.getUsedTimestamp()));
            sendGiftEvent.setSourceChannel(GiftSourceChannel.findByTurnover(userChannel));
            sendGiftEvent.setJsonMap(expandJo);
            sendGiftConsumer.onMessage(sendGiftEvent);
        }

        log.info("ZhuiWanGiftListener ok. seq:{}", seq);
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-yokaihei-login-hdzk",
            topics = "zhuiwan_game_team_action_event",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void yoKaiheiGameTeamActionEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("yoKaiheiGameTeamActionEvent msg:{}", value);
            GameTeamActionEvent event = JSON.parseObject(value, GameTeamActionEvent.class);
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("yoKaiheiGameTeamActionEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }
    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-sign-in-hdzk",
                  topics = "zy_sign_in_notify",
                  groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanSignInEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("zhuiwanSignInEvent msg:{}", value);
            SignInEvent event = JSON.parseObject(value, SignInEvent.class);
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeqId());
        } catch (Exception e) {
            log.error("zhuiwanSignInEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-gameway-notify",
            topics = "game_gateway_notify",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanGameNotifyEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("zhuiwanGameNotifyEvent msg:{}", value);
            ZhuiwanGameNotify event = JSON.parseObject(value, ZhuiwanGameNotify.class);
            //TODO 这个seq可能会随事件类型增加而变化
            String seq = "wzry_event_" + event.getSignalType() + "_" + event.getRoomId();
            hdzjEventDispatcher.notify(Const.NONE_UID, RoleType.USER, event, seq);
        } catch (Exception e) {
            log.error("zhuiwanSignInEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }




    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-app-enterchannel-hdzk",
            topics = "app_user_enter_template",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void appEnterChannelEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("appEnterChannelEvent msg:{}", value);
            UserEnterTemplateEvent event = JSON.parseObject(value, UserEnterTemplateEvent.class);
            if (!UdbUtils.isLoginUid(event.getUid())) {
                log.warn("appEnterChannelEvent msg skip for anonymous uid:{}", event.getUid());
                return;
            }
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("appEnterChannelEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-danmaku-game-hdzk",
            topics = "danmaku_channel_game_event",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void danmakuGameEvent(ConsumerRecord<String, String> consumerRecord) {

        String value = consumerRecord.value();
        try {
            log.info("danmakuGameEvent msg:{}", value);
            DanmakuChannelGameEvent event = JSON.parseObject(value, DanmakuChannelGameEvent.class);
            hdzjEventDispatcher.notify(event.getSid(), RoleType.GUILD, event, event.getSeqId());
        } catch (Exception e) {
            log.error("danmakuGameEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-seat-on-hdzk",
            topics = "zhuiwan_skillcard_on_seat_topic", groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onSeatEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        log.info("onSeatEvent msg:{}", value);
        try {
            SkillcardOnSeatEvent event = JSON.parseObject(value, SkillcardOnSeatEvent.class);
            // 过滤下是否是主持
            long familyId = turnoverFamilyThriftClient.queryContractFamilyId(event.getUid());
            if (familyId <= 0) {
                log.warn("onSeatEvent uid is not contract family:{}", event.getUid());
                return;
            }

            StartShowEvent startShowEvent = new StartShowEvent(event.getSeq(), BusiId.SKILL_CARD, event.getUid(), new Date(event.getTimestamp() * 1000));
            hdzjEventDispatcher.notify(startShowEvent);
        } catch (Exception e) {
            log.error("onSeatEvent exception:", e);
        }
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-seat-order-hdzk",
            topics = "skillcard_online_seat_order", groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onSeatOrderEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        log.info("onSeatOrderEvent msg:{}", value);
        try {
            SkillCardSeatOrderEvent event = JSON.parseObject(value, SkillCardSeatOrderEvent.class);

            // 更新浮层挂件
            Const.EXECUTOR_DELAY_GENERAL.schedule(() -> broActLayerService.invokeAllEffectActRefresh(event.getSid(), event.getSsid()), 8, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("onSeatOrderEvent exception:", e);
        }
    }


    /**
     * 语音房上下座
     */
    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-skillcard-seat-chg-hdzk",
            topics = "skillcard_seat_chg", groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onSeatChgEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        log.info("onSeatChgEvent msg:{}", value);
        try {
            SkillCardSeatChgKafkaEvent event = JSON.parseObject(value, SkillCardSeatChgKafkaEvent.class);
            String seq = StringUtil.isEmpty(event.getSeq()) ? UUID.randomUUID().toString() : event.getSeq();
            hdzjEventDispatcher.notify(event.getUid(), RoleType.ANCHOR, event, seq);
        } catch (Exception e) {
            log.error("onSeatEvent exception:", e);
        }
    }


    /**
     * yomi普通签到事件
     * @param consumerRecord
     */
    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-normal-sign-in-hdzk",
            topics = "zhuiwan_sign_in_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanNoramlSignInEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {

            ZhuiwanSignInEvent event = JSON.parseObject(value, ZhuiwanSignInEvent.class);

            //解决追玩签到每天只有一个seq，测试和灰度会完成不了任务
            String seq = event.getSeq() + ":" + DateUtil.getSeconds();
            event.setSeq(seq);
            log.info("zhuiwanNoramlSignInEvent msg:{},seq:{}", value,seq);
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("zhuiwanNoramlSignInEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    /**
     * 追玩动态
     * @param consumerRecord
     */
    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-post-hdzk",
            topics = "zhuiwan_issue_post_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanPostEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("zhuiwanPostEvent msg:{}", value);
            ZhuiwanPostEvent event = JSON.parseObject(value, ZhuiwanPostEvent.class);
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("zhuiwanPostEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-photo-hdzk",
            topics = "zhuiwan_upload_photos_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanPhotoEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("zhuiwanPhotoEvent msg:{}", value);
            ZhuiwanPhotoEvent event = JSON.parseObject(value, ZhuiwanPhotoEvent.class);
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("zhuiwanPhotoEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-audio-hdzk",
            topics = "zhuiwan_upload_audio_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanAudioEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("zhuiwanAudioEvent msg:{}", value);
            ZhuiwanAudioEvent event = JSON.parseObject(value, ZhuiwanAudioEvent.class);
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("zhuiwanAudioEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-natsk-hdzk",
            topics = "natask_finished_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanNaTaskEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {
            log.info("zhuiwanNaTaskEvent msg:{}", value);
            NaTaskFinishedEvent event = JSON.parseObject(value, NaTaskFinishedEvent.class);
            String seq = event.getTaskId() + "_" + event.getStage() + "_" + event.getUid() + "_" + event.getFinishedTime();
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, seq);
        } catch (Exception e) {
            log.error("zhuiwanNaTaskEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    /**
     * 口令上报通知
     */
    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-share-act-success-hdzk",
            topics = "zhuiwan_share_activity_success_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void zhuiwanShareActivitySuccessEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {

            ActShareSuccessEvent event = JSON.parseObject(value, ActShareSuccessEvent.class);

            //解决追玩签到每天只有一个seq，测试和灰度会完成不了任务
            String seq = event.getSeq() + ":" + DateUtil.getSeconds();
            event.setSeq(seq);
            log.info("zhuiwanShareActivitySuccessEvent msg:{},seq:{}", value,seq);
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("zhuiwanShareActivitySuccessEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }


    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-issue-post-detail-hdzk",
            topics = "zhuiwan_issue_post_detail_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onUserPostDetailKafkaEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        try {

            UserPostDetailKafkaEvent event = JSON.parseObject(value, UserPostDetailKafkaEvent.class);

            log.info("onUserPostDetailKafkaEvent msg:{},seq:{}", value, event.getSeq());
            hdzjEventDispatcher.notify(event.getUid(), RoleType.USER, event, event.getSeq());
        } catch (Exception e) {
            log.error("onUserPostDetailKafkaEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

    @KafkaListener(containerFactory = "zhuiwanContainerFactory", id = "kafka-zhuiwan-act-risk-re-check-hdzk",
            topics = "zhuiwan_act_risk_re_check_topic",
            groupId = "${kafka.hdzt.geact.consumer.group}")
    public void onZhuiwanActRiskReCheckKafkaEvent(ConsumerRecord<String, String> consumerRecord) {
        String value = consumerRecord.value();
        log.info("onZhuiwanActRiskReCheckKafkaEvent msg:{}", value);

        try {
            ActRiskReCheckEvent event = JSON.parseObject(value, ActRiskReCheckEvent.class);
            if (event.getMsgTp() != 1) {
                log.info("onZhuiwanActRiskReCheckKafkaEvent not rsp,value:{}", value);
                return;
            }
            String seq = "risk_recheck:" + event.getAppChallengeRsp().getUid() + ":" + event.getAppChallengeRsp().getRecordId();
            hdzjEventDispatcher.notify(event.getAppChallengeRsp().getUid(), RoleType.USER, event, seq);
        } catch (Exception e) {
            log.error("onZhuiwanActRiskReCheckKafkaEvent error,e:{},value:{}", e.getMessage(), value, e);
        }
    }

}
