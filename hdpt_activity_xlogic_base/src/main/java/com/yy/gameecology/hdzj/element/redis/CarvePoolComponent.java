package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.UserAwardInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CarvePoolComponentAttr;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * desc:根据榜单瓜分奖池
 *
 * @createBy 曾文帜
 * @create 2021-11-02 21:01
 **/
@UseRedisStore
@Component
public class CarvePoolComponent extends BaseActComponent<CarvePoolComponentAttr> implements ComponentRankingExtHandle<CarvePoolComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;


    @Override
    public Long getComponentId() {
        return ComponentId.CARVE_POOL;
    }

    /***
     * 结算阶段结束瓜分奖池
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void settlePhaseTimeEnd(PhaseTimeEnd event, CarvePoolComponentAttr attr) {

        long phaseId = event.getPhaseId();
        if (!attr.getPhaseId().contains(phaseId) || attr.getRankId() != event.getRankId()) {
            log.info("not my duty,event:{},actId:{},cmptId:{},cmptIndex:{}", JSON.toJSONString(event), attr.getActId(), attr.getCmptId(), attr.getCmptUseInx());
            return;
        }
        String paraStr = JSON.toJSONString(event);
        log.info("CarvePoolInvoke begin,para:{}", paraStr);
        try {
            doCarve(event, attr);
            log.info("CarvePoolInvoke done");
        } catch (Exception e) {
            //考虑失败重试
            String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(),
                    DateUtil.getDate(event.getEndTime()));
            String seq = "CarvePool" + "-" + attr.getCmptUseInx() + "-" + event.getActId() + "-" + event.getPhaseId() + "-" + timeCode;
            log.error("CarvePoolInvoke error,add retry,para:{},seq:{},e:{}", paraStr, seq, e.getMessage(), e);

            String msg = buildActRuliuMsg(event.getActId(), true, "瓜分奖池失败，现在触发自动重试", "错误信息:" + e.getMessage());
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
            throw e;
        }
    }

    public void doCarve(PhaseTimeEnd event, CarvePoolComponentAttr attr) {
        String actInfo = String.format("actId:%s,rankId:%s,phaseId:%s ", event.getActId(), event.getRankId(), event.getPhaseId());

        //总奖池数
        BigDecimal poolTotal = new BigDecimal(attr.getPoolTotal());
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(),
                DateUtil.getDate(event.getEndTime()));
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), event.getPhaseId(), timeCode, attr.getTopN(), null);
        log.info("ranks size:{}", ranks.size());
        long total = ranks.stream().mapToLong(Rank::getScore).sum();
        BigDecimal totalScore = new BigDecimal(total);

        List<UserAwardInfo> userAwardInfos = Lists.newArrayList();
        //计算瓜分金额
        for (Rank rank : ranks) {
            long uid = Convert.toLong(rank.getMember(), 0);
            long memberScore = rank.getScore();
            int amount = calMemberAward(attr, poolTotal, memberScore, totalScore);

            if (amount == 0) {
                log.info("doCarve skip,{},uid:{},memberScore:{},allScore:{}"
                        , actInfo, rank.getMember(),
                        memberScore, total);
                continue;
            }

            EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, RoleType.ANCHOR.getValue(), rank.getMember());
            if (enrollmentInfo == null) {
                log.error("严重错误瓜分奖池失败-CarvePool error,not found EnrollmentInfo,{},memberId:{},amount:{}", actInfo, rank.getMember(), amount);
                throw new RuntimeException("CarvePool error,not found EnrollmentInfo");
            }
            Map<Long, Long> taskIdPackageId = attr.getBusiTaskIdPackageId().get(enrollmentInfo.getRoleBusiId());
            if (MapUtils.isEmpty(taskIdPackageId)) {
                log.error("taskIdPackageId not found,event:{},attr:{},enrollmentInfo:{},amount:{}", JSON.toJSONString(event), JSON.toJSONString(attr), JSON.toJSONString(enrollmentInfo), amount);
                continue;
            }

            UserAwardInfo awardInfo = new UserAwardInfo();
            awardInfo.setUid(uid);
            Map<Long, Map<Long, Integer>> taskPackageIdAmount = Maps.newHashMap();
            for (Long taskId : taskIdPackageId.keySet()) {
                Map<Long, Integer> item = taskPackageIdAmount.getOrDefault(taskId, Maps.newHashMap());
                item.put(taskIdPackageId.get(taskId), amount);
                taskPackageIdAmount.put(taskId, item);

                log.info("doCarve preData,{},uid:{},memberScore:{},allScore:{},taskId:{},packageId:{},amount:{}"
                        , actInfo, rank.getMember(),
                        memberScore, total, taskId, taskIdPackageId.get(taskId), amount);
            }
            awardInfo.setTaskPackageIds(taskPackageIdAmount);
            userAwardInfos.add(awardInfo);

        }

        Date now = commonService.getNow(attr.getActId());
        String time = DateUtil.format(now);
        //发奖
        for (UserAwardInfo userAwardInfo : userAwardInfos) {
            String seq = String.format("CarvePool-%s-%s-%s-%s-%s-%s", attr.getActId(), attr.getRankId(), event.getPhaseId(), timeCode, attr.getCmptUseInx(), userAwardInfo.getUid());
            log.info("doCarve welfare begin,{},seq:{},uid:{},packageInfo:{},time:{}", actInfo, seq, userAwardInfo.getUid(), JSON.toJSONString(userAwardInfo.getTaskPackageIds()), time);
            BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(seq, userAwardInfo.getUid(), userAwardInfo.getTaskPackageIds(), time, 3, Maps.newHashMap());

            if (result == null || result.getCode() != 0) {
                log.error("doCarve welfare error,{},seq:{},uid:{},packageInfo:{},time:{},result:{}", actInfo, seq, userAwardInfo.getUid()
                        , JSON.toJSONString(userAwardInfo.getTaskPackageIds()), time, JSON.toJSONString(result));
                //失败插入重试表重试
                throw new RuntimeException("doCarve welfare error");
            } else {
                log.info("doCarve welfare done,{},seq:{},uid:{},packageInfo:{},time:{},result:{}", actInfo, seq, userAwardInfo.getUid()
                        , JSON.toJSONString(userAwardInfo.getTaskPackageIds()), time, JSON.toJSONString(result));
            }
        }

        //打上标记，给榜单查询区分是否已结算展示用
        String settleKey = makeSettleKey(attr, event.getRankId(), event.getPhaseId(), timeCode);
        actRedisDao.setNX(getRedisGroupCode(event.getActId()), settleKey, DateUtil.format(new Date()));


        String msg = buildActRuliuMsg(event.getActId(), false, "根据榜单结果瓜分奖池成功"
                , "奖池金额:" + attr.getPoolTotal() + "\n" + "rankId:" + attr.getRankId() + "\n time:" + timeCode);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
    }

    private int calMemberAward(CarvePoolComponentAttr attr, BigDecimal poolTotal, long memberScore, BigDecimal allMemberScore) {
        BigDecimal memberScoreVal = new BigDecimal(memberScore);
        int amount = memberScoreVal.multiply(poolTotal).divide(allMemberScore, 8, RoundingMode.DOWN).intValue();

        //舍弃个位，因为营收用厂商联盟扣费，只能精确到人民币-分
        return amount / attr.getRoundingDown() * attr.getRoundingDown();
    }


    /**
     * 榜单展示预计瓜分XXX队友金
     */
    @Override
    public List<Object> handleExt(CarvePoolComponentAttr attr, GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {

        //not my duty
        if (!attr.getPhaseId().contains(rankReq.getPhaseId()) || attr.getRankId() != rankReq.getRankId()) {
            return objectList;
        }
        int topN = Math.min(Convert.toInt(attr.getTopN()), objectList.size());
        long totalScore = 0;
        for (int i = 0; i < topN; i++) {
            RankValueItemBase item = (RankValueItemBase) objectList.get(i);
            totalScore += Math.max(0, item.getValue());
        }
        BigDecimal poolTotal = new BigDecimal(attr.getPoolTotal());
        BigDecimal totalScoreVal = new BigDecimal(totalScore);

        String settleKey = makeSettleKey(attr, rankReq.getRankId(), rankReq.getPhaseId(), rankReq.getDateStr());
        boolean alreadySettle = StringUtil.isNotBlank(actRedisDao.get(getRedisGroupCode(rankReq.getActId()), settleKey));
        for (int i = 0; i < topN; i++) {
            RankValueItemBase item = (RankValueItemBase) objectList.get(i);
            int amount = calMemberAward(attr, poolTotal, item.getValue(), totalScoreVal);
            if (amount > 0) {
                item.getViewExt().put("carvePoolVal", amount + "");
                item.getViewExt().put("alreadyCarve", alreadySettle ? "1" : "0");
            }
        }


        return objectList;
    }

    private String makeSettleKey(CarvePoolComponentAttr attr, long rankId, long phaseId, String timeCode) {
        String key = makeKey(attr, rankId + ":" + phaseId);
        if (!StringUtil.isBlank(timeCode)) {
            key = key + ":" + timeCode;
        }
        return key;
    }

}
