package com.yy.gameecology.hdzj.element.redis;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.rank.RankItemBase;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.PKMembers;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PKPlunderComponentAttr;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@UseRedisStore
@Component
public class PKPlunderComponent extends BaseActComponent<PKPlunderComponentAttr> implements ComponentRankingExtHandle<PKPlunderComponentAttr> {

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.PK_PLUNDER;
    }

    /**
     * 在初始榜单阶段晋级成功后初始化好pk member的初始余额
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = false)
    public void initCurrency(PromotTimeEnd event, PKPlunderComponentAttr attr) {
        if (event.getRankId() != attr.getInitRankId() || event.getPhaseId() != attr.getInitPhaseId()) {
            log.info("PKPlunderComponent init currency rankId:{} or phaseId:{} not match!", event.getRankId(), event.getPhaseId());
            return;
        }

        long actId = attr.getActId(), rankId = attr.getInitRankId(), phaseId = attr.getInitPhaseId();
        Map<String, String> ext = ImmutableMap.of(RankExtParaKey.RANK_TYPE_PROMOT, HdztRankType.PROMOT);

        try {
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, StringUtils.EMPTY, 200, ext);
            if (org.springframework.util.CollectionUtils.isEmpty(ranks)) {
                SysEvHelper.waiting(50);
                ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, StringUtils.EMPTY, 500, ext);
            }

            if (CollectionUtils.isEmpty(ranks)) {
                log.warn("PKPlunderComponent init currency rank is empty.");
                return;
            }


            String groupCode = redisConfigManager.getGroupCode(actId);
            String currencyKey = getGlobalCurrencyKey(attr);
            final String initCurrency = String.valueOf(attr.getInitCurrency());
            Map<String, String> m = ranks.stream().collect(Collectors.toMap(Rank::getMember, (rank) -> initCurrency, (k1, k2) -> k1));
            actRedisDao.getRedisTemplate(groupCode).opsForHash().putAll(currencyKey, m);
            log.info("PKPlunderComponent init currency success with m:{}", m);
            String msg = buildInitInfoflowMsg(attr, m);
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
        } catch (Throwable e) {
            log.error("initCurrency exception:", e);
            String msg = "###<font color='red'>PK掠夺玩法初始余额失败</font>###\nmessage:" + e.getMessage();
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
        }

    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void plunderSettle(PhaseTimeEnd event, PKPlunderComponentAttr attr) {
        String key = event.getRankId() + "_" + event.getPhaseId();
        if (!attr.getSettleConfigs().containsKey(key)) {
            log.info("PKPlunderComponent plunderSettle not match:{}", key);
            return;
        }

        final long actId = attr.getActId(), rankId = event.getRankId(), phaseId = event.getPhaseId();
        final String groupCode = getRedisGroupCode(actId);

        String deduplicateKey = getDeduplicateKey(attr, rankId, phaseId);
        if (!actRedisDao.setNX(groupCode, deduplicateKey, StringUtil.ONE)) {
            log.info("PKPlunderComponent plunderSettle handle duplicated!");
            return;
        }

        PKPlunderComponentAttr.SettleConfig config = attr.getSettleConfigs().get(key);

        try {
            List<PKMembers<GroupMemberItem>> pkMembers = getPKMembers(actId, rankId, phaseId, event.getTimeKey(), DateUtil.getDate(event.getEndTime()));
            String currencyKey = getGlobalCurrencyKey(attr);
            Map<String, String> currencyMap = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().entries(currencyKey);
            Map<String, Long> plunderScore = getPlunderAmounts(config, pkMembers, currencyMap);

            //执行更新
            List<Object> result = actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisCallback<Object>) connection -> {
                StringRedisConnection stringConnection = (StringRedisConnection) connection;
                for (String memberId : plunderScore.keySet()) {
                    stringConnection.hIncrBy(currencyKey, memberId, plunderScore.get(memberId));
                }

                return null;
            });

            log.info("PKPlunderComponent plunderSettle with plunderAmount:{}, result:{}", plunderScore, result);
            if (result.size() != plunderScore.size()) {
                log.error("PKPlunderComponent incr currency result not match");
                String msg = buildPlunderFailMsg(actId, rankId, phaseId, "添加数量不一致");
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
                return;
            }

            //校验分值
            Map<String, String> newCurrencyMap = new LinkedHashMap<>(plunderScore.size());
            int i = 0;
            for (String memberId : plunderScore.keySet()) {
                long score = Convert.toLong(result.get(i++));
                newCurrencyMap.put(memberId, String.valueOf(score));
                long initCurrency = Optional.ofNullable(currencyMap.get(memberId))
                        .filter(StringUtils::isNumeric)
                        .map(Long::parseLong)
                        .orElse(0L);

                if (initCurrency + plunderScore.get(memberId) != score) {
                    log.error("PKPlunderComponent incr currency result value not expected!");
                    String failMsg = String.format("掠夺结果不一致：m:%s, s:%d, i:%d, p:%d", memberId, score, initCurrency, plunderScore.get(memberId));
                    String msg = buildPlunderFailMsg(actId, rankId, phaseId, failMsg);
                    baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
                }
            }

            //保存当前阶段的分值
            String phaseCurrencyKey = getPhaseCurrencyKey(attr, event.getRankId(), event.getPhaseId());
            actRedisDao.getRedisTemplate(groupCode).opsForHash().putAll(phaseCurrencyKey, newCurrencyMap);
            log.info("PKPlunderComponent plunderSettle rankId:{}, phaseId:{} currency:{}", event.getRankId(), event.getPhaseId(), newCurrencyMap);

            String msg = buildPlunderSucMsg(actId, rankId, phaseId, newCurrencyMap, plunderScore);
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
        } catch (Throwable e) {
            log.info("plunderSettle fail:", e);
            String msg = buildPlunderFailMsg(actId, rankId, phaseId, "运行出错：" + e.getMessage());
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
        }
    }

    public LinkedHashMap<String, Long> getPlunderAmounts(PKPlunderComponentAttr.SettleConfig config, List<PKMembers<GroupMemberItem>> pkMembers, Map<String, String> currencyMap) {
        LinkedHashMap<String, Long> plunderAmounts = new LinkedHashMap<>(pkMembers.size() * 2);
        for (PKMembers<GroupMemberItem> pkMember : pkMembers) {
            String winnerMember = pkMember.getWinner().memberId;
            String loserMember = pkMember.getLoser().memberId;
            if (pkMember.isTie()) {
                plunderAmounts.put(winnerMember, 0L);
                plunderAmounts.put(loserMember, 0L);
                continue;
            }

            long loserCurrency = Optional.ofNullable(currencyMap.get(loserMember))
                    .filter(StringUtils::isNumeric)
                    .map(Long::parseLong)
                    .orElse(0L);

            long plunderAmount = getPlunderAmount(config, loserCurrency);
            plunderAmounts.put(winnerMember, plunderAmount);
            plunderAmounts.put(loserMember, -plunderAmount);
        }

        return plunderAmounts;
    }

    public Pair<Long, Long> getExpectPlunderInfo(long actId, long rankId, long phaseId, String memberId, String opponent, int pkState) {
        List<PKPlunderComponentAttr> attrs = getAllComponentAttrs(actId);
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }

        String key = rankId + "_" + phaseId;
        for (PKPlunderComponentAttr attr : attrs) {
            if (!attr.getSettleConfigs().containsKey(key)) {
                continue;
            }

            final String groupCode = getRedisGroupCode(actId);
            PKPlunderComponentAttr.SettleConfig config = attr.getSettleConfigs().get(key);
            String currencyKey = getGlobalCurrencyKey(attr);
            List<String> currencyList = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().multiGet(currencyKey, ImmutableList.of(memberId, opponent));

            long currency = Optional.ofNullable(currencyList.get(0)).filter(StringUtils::isNumeric).map(Long::parseLong).orElse(0L);
            if (pkState == 0) {
                return ImmutablePair.of(currency, 0L);
            }
            long opponentCurrency = Optional.ofNullable(currencyList.get(1)).filter(StringUtils::isNumeric).map(Long::parseLong).orElse(0L);
            long loserCurrency = pkState > 0 ? opponentCurrency : currency;
            long plunderAmount = getPlunderAmount(config, loserCurrency);
            return ImmutablePair.of(currency, plunderAmount);
        }

        return null;
    }

    public String getDeduplicateKey(PKPlunderComponentAttr attr, long rankId, long phaseId) {
        return makeKey(attr, String.format("deduplicate:%d:%d", rankId, phaseId));
    }

    public String getGlobalCurrencyKey(PKPlunderComponentAttr attr) {
        return makeKey(attr, "global_currency");
    }

    public String getPhaseCurrencyKey(PKPlunderComponentAttr attr, long rankId, long phaseId) {
        return makeKey(attr, String.format("phase_currency:%d:%d", rankId, phaseId));
    }

    private long getPlunderAmount(PKPlunderComponentAttr.SettleConfig config, long loserCurrency) {
        final int two = 2;
        if (config.getPlunderType() == two) {
            return config.getPlunderValue();
        }

        return loserCurrency * config.getPlunderValue() / 10000;
    }

    private String getPKConfigDateStr(Date date, long timeKey) {
        String dateStr = StringUtils.EMPTY;
        if (TimeKeyHelper.isSplitByHour(timeKey)) {
            dateStr = DateUtil.format(date, DateUtil.PATTERN_TYPE7);

        } else if (TimeKeyHelper.isSplitByDay(timeKey)) {
            dateStr = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
        }
        return dateStr;
    }

    private List<PKMembers<GroupMemberItem>> getPKMembers(long actId, long rankId, long phaseId, long timeKey, Date date) {
        String dateStr = getPKConfigDateStr(date, timeKey);
        PkInfo pkInfo = hdztRankingThriftClient
                .queryPhasePkgroup(actId, rankId, phaseId, dateStr, dateStr,
                        false,
                        true, Collections.emptyMap());
        log.info("PKPlunderComponent getPKMembers with actId:{}, rankId:{}, phaseId:{}, dateStr:{}, pkInfo:{}", actId, rankId, phaseId, dateStr, pkInfo);
        return pkInfo.getPkGroupItems().stream().map(PkGroupItem::getMemberPkItems)
                .flatMap(Collection::stream)
                .filter(groupMemberItems -> groupMemberItems.size() >= 2)
                .map(memberPkItems -> new PKMembers<>(memberPkItems.get(0), memberPkItems.get(1), (c1, c2) -> {
                    if (c1.score == c2.score && c1.score == 0) {
                        return 0;
                    }

                    return Comparator.comparingInt(GroupMemberItem::getRank).compare(c1, c2);
                }))
                .collect(Collectors.toList());
    }

    @Override
    public List<Object> handleExt(PKPlunderComponentAttr attr, GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        final long actId = rankReq.getActId(), rankId = rankReq.getRankId(), phaseId = rankReq.getPhaseId();
        String key = rankId + "_" + phaseId;
        if (!attr.getSettleConfigs().containsKey(key)) {
            log.info("rank request key:{} not match", key);
            return objectList;
        }

        log.info("objectList:{}", objectList);

        RankingPhaseInfo currentPhase = rankingInfo.getCurrentPhase();
        RankingPhaseInfo reqPhase = rankingInfo.getPhasesMap().get(phaseId);
        if (reqPhase == null) {
            return objectList;
        }

        final String groupCode = getRedisGroupCode(actId);
        Date now = commonService.getNow(actId);
        if (currentPhase != null && now.getTime() >= currentPhase.getBeginTime() && now.getTime() < currentPhase.getEndTime()) {
            PKPlunderComponentAttr.SettleConfig config = attr.getSettleConfigs().get(key);
            List<PKMembers<GroupMemberItem>> pkMembers = getPKMembers(actId, rankId, phaseId, rankingInfo.getTimeKey(), now);
            String currencyKey = getGlobalCurrencyKey(attr);
            Map<String, String> currencyMap = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().entries(currencyKey);
            Map<String, Long> plunderScore = getPlunderAmounts(config, pkMembers, currencyMap);

            for (Object obj : objectList) {
                RankItemBase itemBase = (RankItemBase) obj;
                Map<String, String> extView = itemBase.getViewExt();
                if (MapUtils.isEmpty(extView)) {
                    extView = new HashMap<>(5);
                }

                String memberId = itemBase.getKey();
                extView.put("current_jackpot_amount", Optional.ofNullable(currencyMap.get(memberId)).filter(StringUtils::isNumeric).orElse("0"));

                long score = plunderScore.getOrDefault(memberId, 0L);
                extView.put("expected_plunder_amount", String.valueOf(Math.abs(score)));
                if (score > 0) {
                    extView.put("expected_pk_state", "1");
                } else if (score == 0) {
                    extView.put("expected_pk_state", "0");
                } else {
                    extView.put("expected_pk_state", "-1");
                }

                itemBase.setViewExt(extView);
            }
        } else {
            String phaseCurrencyKey = getPhaseCurrencyKey(attr, rankId, phaseId);
            Map<String, String> phaseCurrencyMap = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().entries(phaseCurrencyKey);
            for (Object obj : objectList) {
                RankItemBase itemBase = (RankItemBase) obj;
                Map<String, String> extView = itemBase.getViewExt();
                if (MapUtils.isEmpty(extView)) {
                    extView = new HashMap<>(5);
                }

                extView.put("current_jackpot_amount", Optional.ofNullable(phaseCurrencyMap.get(itemBase.getKey())).filter(StringUtils::isNumeric).orElse("0"));
                itemBase.setViewExt(extView);
            }
        }

        return objectList;
    }

    public static String buildInitInfoflowMsg(PKPlunderComponentAttr attr, Map<String, String> m) {
        StringBuilder sb = new StringBuilder("###<font color='green'>PK掠夺玩法初始余额成功</font>###").append('\n');
        sb.append("活动ID：").append(attr.getActId()).append('\n');
        sb.append("榜单ID：").append(attr.getInitRankId()).append('\n');
        sb.append("阶段ID：").append(attr.getInitPhaseId()).append('\n');
        sb.append("初始详情：").append('\n');
        for (Map.Entry<String, String> entry : m.entrySet()) {
            sb.append(entry.getKey()).append('\t').append(entry.getValue()).append('\n');
        }

        return sb.toString();
    }

    public static String buildPlunderFailMsg(long actId, long rankId, long phaseId, String failMsg) {
        String rs = "###<font color='red'>PK掠夺出错了</font>###\n";
        rs += "活动ID:" + actId + '\n';
        rs += "结算榜单：" + rankId + '\n';
        rs += "结算阶段：" + phaseId + '\n';
        rs += "错误信息：" + failMsg;

        return rs;
    }


    public static String buildPlunderSucMsg(long actId, long rankId, long phaseId, Map<String, String> newCurrencyMap, Map<String, Long> plunderScore) {
        StringBuilder sb = new StringBuilder("###<font color='green'>PK掠夺结算完成</font>###").append('\n');
        sb.append("活动ID：").append(actId).append('\n');
        sb.append("结算榜单：").append(rankId).append('\n');
        sb.append("结算阶段：").append(phaseId).append('\n');
        sb.append("结算详情：").append('\n');
        boolean endl = false;
        for (Map.Entry<String, String> entry : newCurrencyMap.entrySet()) {
            sb.append(entry.getKey()).append(": ").append(entry.getValue()).append('(').append(plunderScore.get(entry.getKey())).append(')');
            if (endl) {
                sb.append('\n');
            } else {
                sb.append(" VS. ");
            }

            endl = !endl;
        }

        return sb.toString();
    }
}
