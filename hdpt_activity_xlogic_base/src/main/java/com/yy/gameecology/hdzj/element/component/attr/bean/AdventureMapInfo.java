package com.yy.gameecology.hdzj.element.component.attr.bean;

import lombok.Data;
import java.util.List;

/**
 * 探险地图信息
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class AdventureMapInfo {
    
    /**
     * 地图格子总数
     */
    private int totalGrids;
    
    /**
     * 当前位置
     */
    private int currentPosition;
    
    /**
     * 地图格子奖励列表
     */
    private List<GridReward> gridRewards;
    
    /**
     * 地图格子奖励
     */
    @Data
    public static class GridReward {
        /**
         * 格子位置
         */
        private int position;
        
        /**
         * 奖励类型
         */
        private int rewardType;
        
        /**
         * 奖励名称
         */
        private String rewardName;
        
        /**
         * 奖励图标
         */
        private String rewardIcon;
        
        /**
         * 奖励数量
         */
        private int rewardCount;
        
        /**
         * 是否有库存
         */
        private boolean hasStock;
    }
}
