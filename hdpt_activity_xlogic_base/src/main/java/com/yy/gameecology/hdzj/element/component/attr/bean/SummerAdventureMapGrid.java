package com.yy.gameecology.hdzj.element.component.attr.bean;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

/**
 * 夏日探险地图格子配置
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class SummerAdventureMapGrid {
    
    @ComponentAttrField(labelText = "格子位置", remark = "地图上的格子位置，从1开始")
    private int gridPosition;
    
    @ComponentAttrField(labelText = "奖励类型", remark = "1:头像框 2:气泡 3:礼物 4:其他")
    private int rewardType;
    
    @ComponentAttrField(labelText = "奖励ID", remark = "对应奖励的ID")
    private long rewardId;
    
    @ComponentAttrField(labelText = "奖励名称", remark = "奖励的显示名称")
    private String rewardName;
    
    @ComponentAttrField(labelText = "奖励数量", remark = "奖励的数量")
    private int rewardCount = 1;
    
    @ComponentAttrField(labelText = "奖励图标", remark = "奖励的图标URL")
    private String rewardIcon;
    
    @ComponentAttrField(labelText = "奖励描述", remark = "奖励的详细描述")
    private String rewardDesc;
    
    @ComponentAttrField(labelText = "是否稀有奖励", remark = "是否为稀有奖励，影响广播")
    private boolean isRareReward = false;
    
    @ComponentAttrField(labelText = "库存数量", remark = "奖励的库存数量，-1表示无限")
    private int stockCount = -1;
    
    @ComponentAttrField(labelText = "权重", remark = "奖励的权重，用于随机分配")
    private int weight = 1;
}
