package com.yy.gameecology.activity.bean.hdzt;

import lombok.Data;

/**
 * @Author: CXZ
 * @Desciption: 奖品记录
 * @Date: 2022/4/25 14:22
 * @Modified:
 */
@Data
public class AwardRecordVo {

    /**
     * 时间
     */
    private String time;
    /**
     * 奖品名称，packageName
     */
    private String prize;
    /**
     * 数量
     */
    private long amount;
    /**
     * 用户id
     **/
    private long uid;
    /**
     * 用户昵称
     **/
    private String nick;

    /**
     * packageid
     */
    private long packageId;

    /**
     * 按需装配
     */
    private String avatar;

    /**
     * 奖包单位，展示用
     */
    private String unit;

    /**
     * 奖品图片
     */
    private String packageImage;

    /**
     * 选中时的图片
     */
    private String choiceImage;

    /**
     * 鼠标移上去的tips
     */
    private String mouseoverTips;

    /**
     * 前端展示扩展信息
     */
    private String viewExtjson;

    private long cpUid;

    private String cpNick;

    private String cpAvatar;

    private String nickExtUsers;

    private String cpMember;

    private String cpUniqSeq;

    private String dupShowSeq;

    private long sid;

    private long ssid;

    private long giftNum;

    public AwardRecordVo() {
    }

    public AwardRecordVo(String time, String prize, long amount) {
        this.time = time;
        this.prize = prize;
        this.amount = amount;
    }
}
