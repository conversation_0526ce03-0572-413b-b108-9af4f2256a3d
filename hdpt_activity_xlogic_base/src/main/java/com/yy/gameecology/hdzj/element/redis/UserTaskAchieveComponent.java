package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.event.AppPopUpEvent;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.TaskItemVo;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.rankext.RankExtHandler;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.RankingTaskBannerComponent;
import com.yy.gameecology.hdzj.element.component.attr.ChannelChatLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.UserTaskAchieveComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardReplaceConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.TaskAttrConfig;
import com.yy.gameecology.hdzj.element.history.ChannelChatLotteryComponent;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.AwardPackageItemInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.*;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-10-30 16:18
 **/
@UseRedisStore
@RequestMapping("/cmpt/UserTaskAchieveComponent")
@RestController
@Component
public class UserTaskAchieveComponent extends BaseActComponent<UserTaskAchieveComponentAttr> implements RankExtHandler {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private ChannelChatLotteryComponent channelChatLotteryComponent;

    @Autowired
    private RankingTaskBannerComponent rankingTaskBannerComponent;

    @Autowired
    private UserInfoService userInfoService;

    private final static String DELAY_HANDLE_BIG_BANNER = "DELAY_HANDLE_BIG_BANNER";


    private final static String TASK_KEY_NAME_PREFIX = "task";

    private final static String NOTICE_RULES_KEY = "notice:";

    /**
     * 用户每级任务送礼最多的主播
     */
    private final static String ACHIEVE_ANCHOR_LIST = "achieve_anchor_list:%s";

    /**
     * 宝箱内容
     */
    private final static String TREASURE_INFO = "treasure_info";

    /**
     * 用户是否已领取
     */
    private final static String TREASURE_INFO_USER = "treasure_info_user:%s";

    /**
     * 各等级任务完成人数统计
     */
    private final static String TASK_LEVEL_USER = "task_level_user:%s";

    /**
     * 任务发奖已发奖励
     */
    private final static String TASK_AWARD_AMOUNT = "task_award_amount";

    /**
     * 打开宝箱
     */
    private final static String OP_TYPE_OPEN_BOX = "openBox";

    /**
     * 进入频道，加载宝箱
     */
    private final static String OP_TYPE_LIST_BOX = "listBox";

    /**
     * 进入频道，APP加载宝箱
     */
    private final static String OP_TYPE_APP_LIST_BOX = "appListBox";


    /**
     * 宝箱根据用户去重 点击次数
     */
    private final static String BOX_TRY_OPEN_AMOUNT = "box_try_open_amount";

    /**
     * 打开宝箱允许过期时间，防止因为点击时延用户报障
     */
    private final static long OPEN_BOX_OFFSET_MILL = 3000;

    /**
     * 引导模式下，用户点击宝箱后跳转到频道，弹窗引导
     */
    private final static String JUMP_CHANNEL_BOX_ID = "jump_channel_box_id:%s";


    @Autowired
    private HdzkTaskService hdzkTaskService;


    @Autowired
    private HdztActorInfoService hdztActorInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;


    @Override
    public Long getComponentId() {
        return ComponentId.USER_TASK_ACHIEVE;
    }

    @PostConstruct
    @NeedRecycle(notRecycle = true, author = "zengwenzhi")
    public void init() throws Exception {
        registerDelayQueue(DELAY_HANDLE_BIG_BANNER, this::delayHandleBigBanner);
    }

    @RequestMapping("/invokeDealPassTaskTest")
    public Response<String> invokeDealPassTaskTest(Long actId, long index, long score, long passTaskIndex, long uid, long anchorUid, String subChannel) throws Exception {
        if (!SysEvHelper.isDev()) {
            return Response.fail(-1, "not dev , reject");
        }
        String seq = UUID.randomUUID().toString();
        var attr = getComponentAttr(actId, index);
        AwardReplaceConfig taskAward = attr.getTaskAward2().get(passTaskIndex);
        RankingScoreChanged event = new RankingScoreChanged();
        event.setActId(actId);
        event.setMember(Convert.toString(uid));
        event.setRankScore(score);
        event.setActors(ImmutableMap.of(81050L, subChannel, 81070L, "10051"));
        dealPassTask(event, attr, seq, passTaskIndex, Lists.newArrayList(passTaskIndex), uid, anchorUid, taskAward);

        return Response.ok("done seq:" + seq);
    }


    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, UserTaskAchieveComponentAttr attr) throws Exception {
        //not my duty
        if (!(attr.getRankId() == event.getRankId() && attr.getPhaseId() == event.getPhaseId())) {
            return;
        }

        log.info("onRankingScoreChanged,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        //---和过任务无关的规则提醒弹窗
        noticeRule(event, attr);

        //---组织过任务请求参数
        long actId = event.getActId();
        String group = getRedisGroupCode(actId);
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        UpdateTaskReq req = new UpdateTaskReq();
        String keyPrefix = makeKey(attr, TASK_KEY_NAME_PREFIX);
        req.setKeyPrefix(keyPrefix);
        req.setSeqKeyPrefix(keyPrefix + ":seq");
        req.setSeq(seq);
        req.setRepeatedCheckExpire(attr.getRepeatedCheckExpire());
        req.setSaveCurResult(Const.ONE);
        req.setScore(event.getItemScore());
        req.setMember(event.getMember());
        Map<String, String> contributeRole = calContributeRole(event, attr);
        req.setContributeMember(contributeRole);
        req.setTaskConfig(attr.getTaskConfigValues());
        req.setRecycleCount(Const.ZERO);
        req.setLogCntlFlag(Const.ONESTR);

        //---累任务数据
        Clock clock = new Clock();
        UpdateTaskResult updateTaskResult = hdzkTaskService.updateTask(group, req);
        clock.tag();
        List<Long> passIndex = resolvePassIndex(updateTaskResult);
        if (CollectionUtils.isEmpty(passIndex)) {
            log.info("not pass task");
            return;
        }
        log.info("pass index:{}", JSON.toJSONString(passIndex));

        List<String> anchors = calTopAwardAnchor(passIndex, updateTaskResult, attr);
        long userUid = Convert.toLong(event.getMember());
        //处理每一级任务
        for (int i = 0; i < passIndex.size(); i++) {
            Long index = passIndex.get(i);
            long anchorUid = Convert.toLong(anchors.get(i), 0);
            var taskAward = attr.getTaskAward2().get(index);

            dealPassTask(event, attr, seq, index, passIndex, userUid, anchorUid, taskAward);
        }

    }


    /**
     * 处理每一级任务
     *
     * @param passTaskIndex 过任务的索引
     * @param userUid       过任务用户uid
     * @param anchorUid     过这级任务是，收礼最多的主播
     * @param awardReplaceConfig 任务奖励配置
     */
    private void dealPassTask(RankingScoreChanged event, UserTaskAchieveComponentAttr attr
            , String seq, long passTaskIndex, List<Long> allPassIndex, long userUid, long anchorUid, AwardReplaceConfig awardReplaceConfig) throws Exception {
        log.info("dealPassTask,actId:{},seq:{},passTaskIndex:{},userUid:{},anchorUid:{},taskPackageId:{}"
                , event.getActId(), seq, passTaskIndex, userUid, anchorUid, JSON.toJSONString(awardReplaceConfig));

        long actId = event.getActId();
        List<String> packageNames = Lists.newArrayList();

        //---发奖
        CommonAwardInfo awardInfo = calAwardInfo(attr, passTaskIndex, seq, awardReplaceConfig, userUid, anchorUid);
        //超出限额，不发奖
        if (awardInfo != null) {
            String time = DateUtil.format(commonService.getNow(event.getActId()));
            Map<Long, Map<Long, Integer>> taskPacakgeNum = ImmutableMap.of(awardInfo.getTaskId(), ImmutableMap.of(awardInfo.getPackageId(), (int) awardInfo.getAmount()));
            // 用户发奖 异步发奖，内部已经有重试了，无异常调用成功就当发成功了
            String playerSeq = MD5SHAUtil.getMD5("award|p|" + awardInfo.getTaskId() + "|" + passTaskIndex + "|" + seq);
            hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), userUid, awardInfo.getTaskId(), taskPacakgeNum, playerSeq, Maps.newHashMap());
            //主播发奖
            if (anchorUid > 0) {
                String anchorSeq = MD5SHAUtil.getMD5("award|a|" + awardInfo.getTaskId() + "|" + passTaskIndex + "|" + seq);
                hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), anchorUid, awardInfo.getTaskId(), taskPacakgeNum, anchorSeq, Maps.newHashMap());
            }

            Map<Long, AwardModelInfo> awardModelInfoMap = hdztAwardServiceClient.queryAwardTasks(awardInfo.getTaskId());
            AwardModelInfo packageInfo = awardModelInfoMap.get(awardInfo.getPackageId());
            if (packageInfo != null) {
                String packageName = packageInfo.getPackageName();
                if (awardInfo.getAmount() > 1) {
                    packageName = packageName + "*" + awardInfo.getAmount();
                }
                packageNames.add(packageName);
            }
        }


        //---收礼物最多的主播记录
        String key = buildAchieveAnchorListKey(attr, userUid);
        String dupSeq = key + ":dup:" + passTaskIndex + ":" + seq;
        //跟等级展示有关，要按顺序放，后面来的放尾巴
        actRedisDao.rPushWithSeq(getRedisGroupCode(actId), dupSeq, key, Convert.toString(anchorUid), attr.getRepeatedCheckExpire());

        //数据统计
        String taskUserKey = buildTaskLevelKey(attr, passTaskIndex);
        String taskUserDup = taskUserKey + ":dup:" + passTaskIndex + ":" + seq;
        actRedisDao.lPushWithSeq(getRedisGroupCode(actId), taskUserDup, taskUserKey, Convert.toString(userUid), attr.getRepeatedCheckExpire());


        TaskAttrConfig config = attr.getTaskConfig().get(passTaskIndex);
        //---宝箱霸屏(一次触发多个，只触发最后一级别)
        if (allPassIndex.get(allPassIndex.size() - 1) == passTaskIndex) {
            String giftName = StringUtils.join(packageNames, ",");
            dealPassTaskSendTreasureInfo(event, attr, seq, passTaskIndex, allPassIndex, userUid, anchorUid, config, giftName);
        } else {
            log.warn("pass more than one task,skip,actId:{},uid:{},passTaskIndex:{}", actId, userUid, passTaskIndex);
        }
        //---神豪弹窗提醒
        String noticeSeq = makeKey(attr, "notice:" + passTaskIndex + ":" + seq);
        RetryTool.withRetryCheck(actId, noticeSeq, DateUtil.ONE_HOUR_SECONDS, () -> dealPassTaskSendNotice(event, attr, packageNames, userUid, config));

    }


    private CommonAwardInfo calAwardInfo(UserTaskAchieveComponentAttr attr, long passTaskIndex, String seq, AwardReplaceConfig awardReplaceConfig, long uid, long anchorUid) {
        long actId = attr.getActId();
        long awardPrice = 0;
        long oriTaskId = awardReplaceConfig.getTaskId();
        long oriPackageId = awardReplaceConfig.getPackageId();
        long replaceTaskId = awardReplaceConfig.getReplaceTaskId();
        long replacePackageId = awardReplaceConfig.getReplacePackageId();
        if (attr.getTaskAwardPrice().containsKey(oriTaskId) && attr.getTaskAwardPrice().get(oriTaskId).containsKey(oriPackageId)) {
            awardPrice = attr.getTaskAwardPrice().get(oriTaskId).get(oriPackageId);
        }

        //原发奖配置，如果设置了限额，且超过限额，则替换成触发限额的发奖配置
        CommonAwardInfo awardInfo = new CommonAwardInfo();
        awardInfo.setTaskId(oriTaskId);
        awardInfo.setPackageId(oriPackageId);
        awardInfo.setAmount(Const.ONE);
        //奖池不足时发放进场秀(最后一笔允许超发，所以可以先读出来判断)
        boolean awardPoolOut = getAwardPoolLeft(attr) <= 0;
        if (awardPoolOut && awardPrice > 0) {
            awardInfo.setTaskId(replaceTaskId);
            awardInfo.setPackageId(replacePackageId);
            log.info("calAwardInfo awardPoolOut,seq:{},uid:{},targetTaskId:{},targetPackageId:{}", seq, uid, replaceTaskId, replacePackageId);
            return awardInfo;
        }


        if (awardPrice > 0) {
            String redisGroup = getRedisGroupCode(actId);
            boolean sendTowUser = anchorUid > 0;
            long inc = sendTowUser ? awardPrice * 2 : awardPrice;
            String amountKey = buildAwardPoolAlreadySend(attr);
            String incSeq = makeKey(attr, "award_limit:" + seq + ":" + passTaskIndex);
            List<Long> incResult = actRedisDao.incrValueWithLimitSeq(redisGroup, incSeq, amountKey, inc, attr.getTaskAwardPoolLimit(), true, attr.getBoxExpireSeconds());
            log.info("calAwardInfo result,actId:{},seq:{},inc:{},incResult:{}", actId, incSeq, inc, JSON.toJSONString(incResult));
            if (incResult.get(0) < 0) {
                //超了限额，发代替礼物
                awardInfo.setTaskId(replaceTaskId);
                awardInfo.setPackageId(replacePackageId);
                log.info("calAwardInfo more than limit,actId:{},uid:{},anchorId:{}", actId, uid, anchorUid);
                return awardInfo;
            } else {
                log.info("calAwardInfo send,actId:{},uid:{},anchorId:{},new taskId:{},new PackageId:{},new amount:{}", actId, uid, anchorUid, awardInfo.getTaskId(), awardInfo.getPackageId(), awardInfo.getAmount());
                return awardInfo;
            }
        }


        return awardInfo;
    }

    private long getAwardPoolLeft(UserTaskAchieveComponentAttr attr) {
        long send = getAwardPoolConsume(attr);
        return Math.max(attr.getTaskAwardPoolLimit() - send, 0);
    }

    private long getAwardPoolConsume(UserTaskAchieveComponentAttr attr) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String amountKey = buildAwardPoolAlreadySend(attr);
        return Convert.toLong(actRedisDao.get(redisCode, amountKey), 0);
    }

    /**
     * 发送完成任务宝箱+霸屏
     */
    private void dealPassTaskSendTreasureInfo(RankingScoreChanged event, UserTaskAchieveComponentAttr attr
            , String seq, long passTaskIndex, List<Long> allPassIndex, long userUid, long anchorUid, TaskAttrConfig config, String giftName) {
        long actId = attr.getActId();
        //当前任务等级没配置宝箱
        if (!attr.getTaskBox().containsKey(passTaskIndex)) {
            log.info("current task not config treasure,actId:{},passTaskIndex:{}", actId, passTaskIndex);
            return;
        }
        //---宝箱霸屏
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(Const.ONE);
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(userUid), multiNickUsers, true, com.yy.gameecology.common.bean.Template.unknown.getCode());
        UserInfoVo userBaseInfo = userInfoVoMap.get(userUid);

        ChannelInfo channelInfo = resolveUserChannel(userUid, event);
        long familyId = resolveBroFamilyId(event, anchorUid, attr.getBroTemplate());

        Map<String, Object> ext = Maps.newHashMap();
        String boxId = UUID.randomUUID().toString();
        long broType = attr.getTaskBox().get(passTaskIndex).keySet().stream().findFirst().get();

        //---宝箱（宝箱看上去比较常用，可独立封装成1个方法组件？）
        TreasureInfo box = new TreasureInfo();
        box.setId(boxId);
        box.setTaskId(passTaskIndex);
        box.setTaskName(config.getName());
        long current = System.currentTimeMillis();
        box.setCreateTime(current);
        box.setCurrentTime(current);
        box.setExpireTime(current + attr.getBoxExpireSeconds() * 1000);
        box.setAppExpireTime(current + attr.getAppBoxExpireSeconds() * 1000);
        box.setBroType(broType);
        box.setUid(userUid);
        box.setSid(channelInfo.getSid());
        box.setSsid(channelInfo.getSsid());
        box.setFamilyId(familyId);
        box.setUserName(userBaseInfo.getNick());
        ext.put("nickExtUsers", multiNickUsers);
        ext.put("boxInfo", box);

        ChannelChatLotteryComponentAttr chatAttr = channelChatLotteryComponent.getComponentAttr(attr.getActId(), attr.getChatLotteryIndex());
        if (chatAttr != null) {
            ext.put("boxChatText", chatAttr.getChatText());
        }

        //--保存宝箱信息
        String boxKey = buildTreasureInfoKey(attr);
        String boxContent = JSON.toJSONString(box);
        actRedisDao.hset(getRedisGroupCode(actId), boxKey, boxId, boxContent);
        log.info("save box,actId:{},boxKey:{},box:{}", actId, boxKey, boxContent);

        if (commonService.isGrey(actId)) {
            broType = BroadcastType.TOP_CHANNEL;
            log.info("act is grey,change broType,actId:{}", actId);
        }

        //--广播宝箱信息
        commonBroadCastService.commonBannerBroadcast(channelInfo.getSid(), channelInfo.getSsid(), familyId, Template.findByValue(attr.getBroTemplate()), broType
                , actId, userUid, event.getRankScore(), PBCommonBannerId.BX_BANNER_ID, 0L, ext);

        //触发祝福抽奖
        if (attr.isChannelChatLotteryEnable()) {
            addChatLotteryBox(attr, boxId, userUid, anchorUid, familyId, channelInfo, broType);
        }

        //app横幅
        if (attr.getAppBigBannerComponent() > 0) {
            String appBannerSeq = makeKey(attr, seq + ":appbigbanner");
            String subChannel = channelInfo.getSid() + "_" + channelInfo.getSsid();
            Map<String, String> dynamic = Maps.newHashMap();
            dynamic.put("{award}", giftName);
            //延迟播放(和宝箱有冲突)
            InvokeBroAppBannerPara para = new InvokeBroAppBannerPara();
            para.setSeq(appBannerSeq);
            para.setLevel(passTaskIndex);
            para.setMember(Convert.toString(userUid));
            para.setSubChannel(subChannel);
            para.setDynamicReplace(dynamic);
            publishDelayEvent(attr, DELAY_HANDLE_BIG_BANNER, para, System.currentTimeMillis() + attr.getAppBigBannerDelay());
        }


        //---霸屏(触发条件和宝箱一致，暂时用同1个配置!!!)
        Map<String, Object> bpBannerExt = Maps.newHashMap();
        bpBannerExt.put("taskId", passTaskIndex);
        bpBannerExt.put("level", passTaskIndex + 1);
        bpBannerExt.put("taskName", config.getName());
        bpBannerExt.put("userNick", userBaseInfo.getNick());
        bpBannerExt.put("userLogo", userBaseInfo.getAvatarUrl());
        bpBannerExt.put("giftName", giftName);
        bpBannerExt.put("userUid", userUid);
        bpBannerExt.put("babyUid", anchorUid);
        bpBannerExt.put("sid",channelInfo.getSid());
        bpBannerExt.put("ssid",channelInfo.getSsid());

        commonBroadCastService.commonBannerBroadcast(channelInfo.getSid(), channelInfo.getSsid(), familyId, Template.findByValue(attr.getBroTemplate()), broType
                , actId, userUid, event.getRankScore(), PBCommonBannerId.BP_BANNER_ID, 0L, bpBannerExt);


        try {
            //左左家族赛要求放开限制
            if (broType >= BroadcastType.SUB_CHANNEL) {
                //通知如流
                String msg = "神豪uid：${uid}\n 神豪昵称：${nick}\n 成就等级：${taskName} \n 成就分值：${score} \n 霸屏范围：${broType}(2-子频道 3-顶级频道/家族 4-全模板)\n 飞机票：${fly}"
                        .replace("${uid}", userUid + "")
                        .replace("${nick}", commonService.getNickName(userUid, false))
                        .replace("${taskName}", config.getName())
                        .replace("${score}", config.getScore() + "")
                        .replace("${broType}", broType + "")
                        .replace("${fly}", String.format("yy://pd-[sid=%s&subid=%s]", channelInfo.getSid(), channelInfo.getSsid()));
                String ruliuMsg = buildActRuliuMsg(actId, false, "注意：天降神豪触发宝箱", msg);
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, ruliuMsg, Lists.newArrayList());
            }
        } catch (Exception e) {
            log.error("send ruliu error,e:{}", e.getMessage(), e);
        }
    }

    private void delayHandleBigBanner(UserTaskAchieveComponentAttr attr, Object object) {
        if (!(object instanceof UserTaskAchieveComponent.InvokeBroAppBannerPara info)) {
            log.error("delayHandleBigBanner type error,object:{}", JSON.toJSONString(object));
            return;
        }
        var bannerAttr = rankingTaskBannerComponent.getComponentAttr(attr.getActId(), attr.getAppBigBannerComponent());
        rankingTaskBannerComponent.invokeBroBanner(bannerAttr, info.getSeq(), info.getLevel(), info.getMember(), info.getSubChannel(), info.getDynamicReplace());
    }


    private void addChatLotteryBox(UserTaskAchieveComponentAttr attr, String boxId, long uid, long anchorUid, long familyId, ChannelInfo channelInfo, long broType) {
        ChannelChatLotteryComponentAttr chatAttr = channelChatLotteryComponent.getComponentAttr(attr.getActId(), attr.getChatLotteryIndex());
        if (chatAttr == null) {
            log.info("sendChatLottery return,not config");
            return;
        }
        ChannelChatLotteryComponent.ChatLotteryBox box = new ChannelChatLotteryComponent.ChatLotteryBox();
        box.setSeq(boxId);
        box.setBabyUid(anchorUid);
        box.setUserUid(uid);
        box.setFamilyId(familyId);
        box.setBroType(broType);

        //用户跳转的频道
        box.setSid(channelInfo.getSid());
        box.setSsid(channelInfo.getSsid());
        channelChatLotteryComponent.addChatLotteryBox(chatAttr, box);
    }

    private void dealPassTaskSendNotice(RankingScoreChanged event, UserTaskAchieveComponentAttr attr, List<String> packageNames, long userUid, TaskAttrConfig config) {
        if (CollectionUtils.isEmpty(packageNames)) {
            log.info("empty packageNames dealPassTaskSendNotice return,actId:{},uid:{}", attr.getActId(), userUid);
            return;
        }
        long actId = event.getActId();
        Map<String, Object> noticeExt = ImmutableMap.of(
                "award", StringUtils.join(packageNames, ",")
                , "taskName", config.getName());
        commonBroadCastService.commonNoticeUnicast(actId, "5074_noticeCompleteTask", StringUtil.EMPTY, JSON.toJSONString(noticeExt), userUid);

        //--神豪app弹窗提醒
        if (StringUtil.isNotBlank(attr.getTaskCompleteAppPopUrl())) {
            AppPopUpEvent appPopUpEvent = new AppPopUpEvent();
            appPopUpEvent.setUid(userUid);
            String url = attr.getTaskCompleteAppPopUrl()
                    .replace("{$taskName}", config.getName())
                    .replace("{$award}", StringUtils.join(packageNames, ","));
            appPopUpEvent.setPopUrl(url);
            appPopUpEvent.setSeq(event.getSeq());
            appPopUpEvent.setProductTime(System.currentTimeMillis());
            kafkaService.sendAppPopUp(appPopUpEvent);
        }
    }

    /**
     * 宝箱抽奖
     * 进频道拉取宝箱列表
     */
    @Report
    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        UserTaskAchieveComponentAttr attr = getComponentAttr(request.getActId(), request.getCmptIndex());
        String lockName = makeKey(attr, "commonOperatePbRequest:" + request.getOpUid());
        Secret secret = locker.lock(lockName, 5, request.getSeq(), 5);
        if (secret == null) {
            return new CommonPBOperateResp(-1, "", "手速太快了！");
        }
        try {
            //opType=openBox
            if (OP_TYPE_OPEN_BOX.equals(request.getOpType())) {
                openBox(attr, request.getActId(), request.getOpUid(), request.getOpId());
            } else if (OP_TYPE_LIST_BOX.equals(request.getOpType()) || OP_TYPE_APP_LIST_BOX.equals(request.getOpType())) {
                Map<String, Object> result = listBox(attr, request.getOpType(), request.getActId(), request.getOpUid(), request.getOpSid(), request.getOpSsid());
                return new CommonPBOperateResp(0, JSON.toJSONString(result), "");
            }
            return new CommonPBOperateResp(0, "", "");
        } catch (SuperException e) {
            log.warn("commonOperatePbRequest error,result:{},e:{}", JSON.toJSONString(request), e.getMessage(), e);
            return new CommonPBOperateResp(e.getCode(), request.getOpId(), e.getMessage());
        } catch (Exception e) {
            log.error("commonOperatePbRequest error,result:{},e:{}", JSON.toJSONString(request), e.getMessage(), e);
            return new CommonPBOperateResp(-2, request.getOpId(), "网络超时，请重试！");

        } finally {
            locker.unlock(lockName, secret, 5);
        }

    }

    public void openBox(UserTaskAchieveComponentAttr attr, long actId, long uid, String boxId) throws Exception {
        if (attr.isChannelChatLotteryEnable()) {
            //公屏口令抽奖模式，这个时候宝箱只是引导，不直接抽奖
            String userInfoKey = buildTreasureUserInfoKey(attr, uid);
            actRedisDao.hset(getRedisGroupCode(actId), userInfoKey, boxId, boxId);

            //频道跳转引导标识
            String fromBoxKey = buildJumpChannelBoxId(attr, uid);
            actRedisDao.set(getRedisGroupCode(actId), fromBoxKey, boxId, Const.THIRTY);
            log.info("openBox done,uid:{},boxId:{}", uid, boxId);
        } else {
            //宝箱抽奖模式
            openRealBox(attr, actId, uid, boxId);
        }
    }

    /**
     * 打开宝箱抽奖
     */
    public void openRealBox(UserTaskAchieveComponentAttr attr, long actId, long uid, String boxId) throws Exception {
        log.info("openBoxAward begin,actId:{},uid:{},boxId:{}", actId, uid, boxId);
        //用活动展示时间是为了避免活动结束有领奖时间临界问题
        if (!actInfoService.inActShowTime(actId)) {
            throw new SuperException("未在活动时间内", SuperException.E_CONF_ILLEGAL);
        }
        String group = getRedisGroupCode(attr.getActId());

        //宝箱ID是否存在
        String key = buildTreasureInfoKey(attr);
        String content = actRedisDao.hget(group, key, boxId);
        if (StringUtil.isBlank(content)) {
            throw new SuperException("宝箱已过期", SuperException.E_WRONG_PARAM);
        }
        //宝箱是否过期
        TreasureInfo boxInfo = JSON.parseObject(content, TreasureInfo.class);
        if (System.currentTimeMillis() >= (boxInfo.getExpireTime() + OPEN_BOX_OFFSET_MILL)) {
            throw new SuperException("宝箱已过期", SuperException.E_WRONG_PARAM);
        }
        //是否已经领取过
        String userInfoKey = buildTreasureUserInfoKey(attr, uid);
        String userRecord = actRedisDao.hget(group, userInfoKey, boxId);
        if (StringUtil.isNotBlank(userRecord)) {
            throw new SuperException("已经领取", SuperException.E_WRONG_PARAM);
        }

        //单个宝箱点击次数限制
        long hitAmount = actRedisDao.hIncrByKey(getRedisGroupCode(actId), makeKey(attr, BOX_TRY_OPEN_AMOUNT), boxId, 1);
        if (attr.getOpenBoxLimit() > 0 && hitAmount > attr.getOpenBoxLimit()) {
            log.warn("open box hit more than limit,actId:{},boxId:{},uid:{},limit:{},hitAmount:{}", actId, boxId, uid, attr.getOpenBoxLimit(), hitAmount);
            //用户领取记录
            actRedisDao.hset(getRedisGroupCode(actId), userInfoKey, boxId, boxId);

            List<Map<String, Object>> awardInfoResult = Lists.newArrayList();
            awardInfoResult.add(ImmutableMap.of("name", "手慢了，谢谢参与"));
            Map<String, Object> noticeExt = ImmutableMap.of("userAward", awardInfoResult, "awardLimit", 1);
            commonBroadCastService.commonNoticeUnicast(actId, "5074_boxPop", StringUtil.EMPTY, JSON.toJSONString(noticeExt), uid);
            return;
        }

        List<Map<String, Object>> awardInfoResult = Lists.newArrayList();
        final boolean sendResult;
        if (attr.isBoxSenderAward() && CollectionUtils.isNotEmpty(attr.getBoxSenderAwardConfig()) && boxInfo.getUid() == uid) {
            //发奖
            log.info("open box send award,actId:{},uid:{},boxId:{}", actId, uid, boxId);
            openBoxAward(awardInfoResult, attr, actId, boxId, uid);
            sendResult = true;
        } else {
            //抽奖
            sendResult = openBoxLottery(awardInfoResult, attr, actId, boxId, uid);
        }

        if (!sendResult) {
            log.info("send award faild,actId:{},uid:{},boxId:{}", actId, uid, boxId);
            return;
        }

        //用户领取记录
        actRedisDao.hset(getRedisGroupCode(actId), userInfoKey, boxId, boxId);

        //抽奖结果用户单播
        Map<String, Object> noticeExt = ImmutableMap.of("userAward", awardInfoResult);
        commonBroadCastService.commonNoticeUnicast(actId, "5074_boxPop", StringUtil.EMPTY, JSON.toJSONString(noticeExt), uid);

        //抽奖结果宝箱广播
        Map<String, Object> bpBannerExt = Maps.newHashMap();
        bpBannerExt.put("boxId", boxInfo.getId());
        bpBannerExt.put("userAward", awardInfoResult);
        bpBannerExt.put("userNick", commonService.getNickName(uid, true));
        commonBroadCastService
                .commonBannerBroadcast(boxInfo.getSid(), boxInfo.getSsid(), boxInfo.getFamilyId()
                        , Template.findByValue(attr.getBroTemplate()), boxInfo.getBroType(), actId
                        , boxInfo.getUid(), 0, PBCommonBannerId.BX_ZJ_BANNER_ID, 0L, bpBannerExt);

    }

    private boolean openBoxLottery(List<Map<String, Object>> awardInfoResult, UserTaskAchieveComponentAttr attr, long actId, String boxId, long uid) throws Exception {
        Map<Integer, Long> assignHit = Maps.newHashMap();
        String seq = boxId + "_" + uid;
        BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(seq, BusiId.GAME_ECOLOGY.getValue(), uid, attr.getBoxTaskId(), 1
                , assignHit
                , ImmutableMap.of(LotteryExtParaName.NOT_FLOW_CONTROL, "1", LotteryExtParaName.LOAD_RECORD_SEQ_DUP, "1")
                , 3);

        boolean lotteryError = result == null || (result.getCode() != 0 && result.getCode() != LotteryException.E_LOTTERY_COMPLETED);
        if (lotteryError) {
            log.error("openBox doBatchLottery error,actId:{},uid:{},result:{}", actId, uid, result);
            return false;
        }

        //用于提取抽中的礼物列表
        Map<Long, AwardModelInfo> awardModelMap = hdztAwardServiceClient.queryAwardTasks(attr.getBoxTaskId());
        //---发奖（根据抽奖结果，同时给主播和用户发奖）
        Map<Long, Long> recordPackageMap = result.getRecordPackages();
        for (Long recordId : recordPackageMap.keySet()) {
            long lPackageId = recordPackageMap.get(recordId);
            awardInfoResult.add(ImmutableMap.of("name", awardModelMap.get(lPackageId).getPackageName(),
                    "image", awardModelMap.get(lPackageId).getPackageImage(),
                    "ext", Convert.toString(awardModelMap.get(lPackageId).getViewExtjson())));
        }

        return true;
    }

    /**
     * 随机发一个
     *
     * @param awardInfoResult
     * @param attr
     * @param actId
     * @param boxId
     * @param uid
     * @throws Exception
     */
    private void openBoxAward(List<Map<String, Object>> awardInfoResult, UserTaskAchieveComponentAttr attr, long actId, String boxId, long uid) throws Exception {
        String time = DateUtil.format(commonService.getNow(actId));
        if (CollectionUtils.isEmpty(attr.getBoxSenderAwardConfig())) {
            return;
        }
        List<AwardAttrConfig> configs = Lists.newArrayList(attr.getBoxSenderAwardConfig());
        Collections.shuffle(configs);
        AwardAttrConfig config = configs.get(0);
        Map<Long, Map<Long, Integer>> taskPacakgeNum = ImmutableMap.of(config.getTAwardTskId(), ImmutableMap.of(config.getTAwardPkgId(), config.getNum()));
        // 用户发奖 异步发奖，内部已经有重试了，无异常调用成功就当发成功了
        String awardSeq = MD5SHAUtil.getMD5("award|box|0|" + boxId + "|" + uid);
        hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), uid, config.getTAwardTskId(), taskPacakgeNum, awardSeq, Maps.newHashMap());
        log.info("openBoxAward with uid:{}, boxId:{}, config:{}", uid, boxId, config);

        //用于提取抽中的礼物列表
        Map<Long, AwardModelInfo> awardModelMap = hdztAwardServiceClient.queryAwardTasks(config.getTAwardTskId());
        awardInfoResult.add(ImmutableMap.of("name", awardModelMap.get(config.getTAwardPkgId()).getPackageName(),
                "image", awardModelMap.get(config.getTAwardPkgId()).packageImage,
                "ext", Convert.toString(awardModelMap.get(config.getTAwardPkgId()).getViewExtjson())));
    }

    public Map<String, Object> listBox(UserTaskAchieveComponentAttr attr, String opeType, long actId, long uid, long sid, long ssid) throws Exception {
        Map<String, Object> result = Maps.newHashMap();
        List<TreasureInfo> boxList = Lists.newArrayList();
        String boxKey = buildTreasureInfoKey(attr);
        Map<Object, Object> boxInfos = actRedisDao.hGetAll(getRedisGroupCode(actId), boxKey);
        if (MapUtils.isNotEmpty(boxInfos)) {
            for (Object key : boxInfos.keySet()) {
                String boxInfo = Convert.toString(boxInfos.get(key));
                TreasureInfo box = JSON.parseObject(boxInfo, TreasureInfo.class);
                //不要用缓存的当前时间
                box.setCurrentTime(System.currentTimeMillis());
                //过时一分钟，清除宝箱
                boolean expire = tryRemoveExpireBox(attr, box);
                if (expire) {
                    continue;
                }
                if (BroadcastType.ALL_TEMPLATE == box.getBroType()) {
                    boxList.add(box);
                } else if (BroadcastType.SUB_CHANNEL == box.getBroType() && box.getSsid() == ssid && box.getSid() == sid) {
                    boxList.add(box);
                } else if (BroadcastType.TOP_CHANNEL == box.getBroType() && box.getSid() == sid && box.getFamilyId() <= 0) {
                    boxList.add(box);
                } else if (BroadcastType.TOP_CHANNEL == box.getBroType() && box.getFamilyId() > 0) {
                    RoomInfo roomInfo = commonService.getRoomInfoBySsid(ssid);
                    if (roomInfo != null && roomInfo.getFamilyId() == box.getFamilyId()) {
                        boxList.add(box);
                    }
                }
            }
        }

        List<String> userBoxId = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(boxList)) {
            List<Object> boxId = boxList.stream().map(p -> (Object) p.getId()).toList();
            List<Object> records = actRedisDao.hmGet(getRedisGroupCode(actId), buildTreasureUserInfoKey(attr, uid), boxId);
            if (records != null) {
                records.forEach(p -> {
                    userBoxId.add(Convert.toString(p));
                });
            }
        }

        //频道跳转引导标识
        String fromBoxKey = buildJumpChannelBoxId(attr, uid);
        String fromBoxId = actRedisDao.get(getRedisGroupCode(actId), fromBoxKey);
        long fromBoxUid = 0;
        if (StringUtil.isNotBlank(fromBoxId)) {
            log.info("jump fromBox,uid:{},fromBoxId:{}", uid, fromBoxId);
            TreasureInfo fromTreasureInfo = boxList.stream().filter(p -> p.getId().equals(fromBoxId)).findFirst().orElse(null);
            ChannelChatLotteryComponentAttr chatAttr = channelChatLotteryComponent.getComponentAttr(attr.getActId(), attr.getChatLotteryIndex());
            if (fromTreasureInfo != null && fromTreasureInfo.getSsid() == ssid && chatAttr != null) {
                fromBoxUid = fromTreasureInfo.getUid();
                actRedisDao.del(getRedisGroupCode(actId), fromBoxKey);
                log.info("jump fromBox remove tag,uid:{},fromBoxId:{}", uid, fromBoxId);
            }
        }

        //多昵称
        List<Long> uids = boxList.stream().map(TreasureInfo::getUid).collect(Collectors.toList());
        if (fromBoxUid > 0 && !uids.contains(fromBoxUid)) {
            uids.add(fromBoxUid);
        }
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers, true, com.yy.gameecology.common.bean.Template.unknown.getCode());

        //跳转引导
        if (fromBoxUid > 0) {
            result.put("fromBoxUid", fromBoxUid);
        }
        ChannelChatLotteryComponentAttr chatAttr = channelChatLotteryComponent.getComponentAttr(attr.getActId(), attr.getChatLotteryIndex());
        if (chatAttr != null) {
            result.put("boxChatText", chatAttr.getChatText());
        }

        //app端拉取，过滤app显示过期宝箱。这里要放到(频道跳转引导标识)后面，防止app显示过期了，丢掉了引导。
        if (OP_TYPE_APP_LIST_BOX.equals(opeType)) {
            boxList = boxList.stream().filter(p -> p.getAppExpireTime() > System.currentTimeMillis()).collect(Collectors.toList());
        }
        boxList = filterClickedBox(attr, boxList, userBoxId);
        result.put("userBoxList", userBoxId);
        result.put("boxList", boxList);
        result.put("nickExtUsers", multiNickUsers);

        return result;
    }

    private List<TreasureInfo> filterClickedBox(UserTaskAchieveComponentAttr attr, List<TreasureInfo> sourceBox, List<String> userBoxId) {
        if (!attr.isChannelChatLotteryEnable() || CollectionUtils.isEmpty(sourceBox)) {
            return sourceBox;
        }
        //口令抽奖引导模式下，过滤已点击过的宝箱
        return sourceBox.stream().filter(p -> !userBoxId.contains(p.getId())).collect(Collectors.toList());
    }


    @RequestMapping("/queryTaskInfo")
    public Response<Map<String, Object>> queryTaskInfo(HttpServletRequest req, HttpServletResponse resp, long actId, long index) throws Exception {
        long uid = getLoginYYUid(req, resp);
        return queryTaskInfo(uid, actId, index);
    }


    @RequestMapping("/queryCompleteTaskInfo")
    public Response<Map<Object, Object>> queryCompleteTaskInfo(HttpServletRequest req, HttpServletResponse resp, long actId, long index) throws Exception {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        Map<Object, Object> levelUser = Maps.newLinkedHashMap();
        UserTaskAchieveComponentAttr attr = getComponentAttr(actId, index);
        if (!attr.getQueryTaskInfoUid().contains(uid)) {
            return Response.fail(999, "to add queryTaskInfoUid");
        }
        List<Long> levels = attr.getTaskConfig().keySet().stream().toList();
        for (int i = levels.size() - 1; i >= 0; i--) {
            long level = levels.get(i);
            String key = buildTaskLevelKey(attr, level);
            List<String> uids = actRedisDao.lrange(getRedisGroupCode(actId), key, 0, 5000);
            TaskAttrConfig config = attr.getTaskConfig().get(level);
            levelUser.put(config.getScore() + "_" + config.getName(), ImmutableMap.of("uids", uids, "size", uids.size()));
        }

        return Response.success(levelUser);
    }

    public Response<Map<String, Object>> queryTaskInfo(long uid, long actId, long index) throws Exception {
        Map<String, Object> result = Maps.newHashMap();
        UserTaskAchieveComponentAttr attr = getComponentAttr(actId, index);

        if (uid > 0) {
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(Const.ONE);
            Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uid), multiNickUsers, false, com.yy.gameecology.common.bean.Template.unknown.getCode());
            UserInfoVo userBaseInfo = userInfoVoMap.get(uid);
            result.put("nick", userBaseInfo.getNick());
            result.put("userAvatarUrl", userBaseInfo.getAvatarUrl());
            result.put("nickExtUsers", multiNickUsers);
        }

        //任务分值
        long score = 0;
        List<Rank> list =
                hdztRankingThriftClient.queryRankingCache(actId, attr.getRankId(), attr.getPhaseId(), "", 0, Convert.toString(uid), null);
        if (list != null && !list.isEmpty() && list.get(0).getMember().equals(Convert.toString(uid))) {
            score = list.get(0).getScore();
        }
        result.put("score", score);

        //任务分值配置
        result.put("taskConfig", attr.getTaskConfigValues());

        int level = calScoreLevel(score, attr.getTaskConfigValues());
        result.put("level", level);

        //任务等级
        String key = buildAchieveAnchorListKey(attr, uid);
        List<String> anchorUidConent = actRedisDao.lrange(getRedisGroupCode(actId), key, 0, -1);
        List<Long> anchorUids = anchorUidConent.stream().map(p -> Convert.toLong(p, 0)).collect(Collectors.toList());
        Map<Long, UserBaseInfo> userBaseInfoMap = commonService.batchGetUserInfos(anchorUids, true);
        List<TaskItemVo> taskItemVos = Lists.newArrayList();
        Map<Long, Map<Long, AwardModelInfo>> awadInfoMap = Maps.newHashMap();
        for (long taskIndex : attr.getTaskConfig().keySet()) {
            TaskItemVo taskItemVo = new TaskItemVo();
            taskItemVo.setTaskId(taskIndex);
            taskItemVo.setTaskName(attr.getTaskConfig().get(taskIndex).getName());

            Map<Long, Long> taskIdPackageId = attr.getTaskAward().get(taskIndex);
            if (MapUtils.isNotEmpty(taskIdPackageId)) {
                List<AwardRoll> awardRolls = Lists.newArrayList();
                for (Long awardTaskId : taskIdPackageId.keySet()) {
                    Map<Long, AwardModelInfo> awardInfo = null;
                    if (!awadInfoMap.containsKey(awardTaskId)) {
                        awardInfo = hdztAwardServiceClient.queryAwardTasks(awardTaskId);
                        if (awardInfo != null) {
                            awadInfoMap.put(awardTaskId, awardInfo);
                        }
                    } else {
                        awardInfo = awadInfoMap.get(awardTaskId);
                    }
                    if (awardInfo != null && awardInfo.containsKey(taskIdPackageId.get(awardTaskId))) {
                        AwardModelInfo awardModelInfo = awardInfo.get(taskIdPackageId.get(awardTaskId));
                        List<AwardPackageItemInfo> awardPackageItemInfos = awardModelInfo.getAwardPackageItemInfos();
                        for (AwardPackageItemInfo itemInfo : awardPackageItemInfos) {
                            AwardRoll awardRoll = new AwardRoll();
                            awardRoll.setPrize(itemInfo.getGiftName());
                            awardRoll.setLogo(itemInfo.getGiftImage());
                            awardRoll.setExtJson(itemInfo.getViewExtjson());
                            awardRolls.add(awardRoll);
                        }
                    }
                }
                taskItemVo.setAwards(awardRolls);
            }

            //获奖主播
            if (anchorUids.size() > taskIndex) {
                long anchorUid = anchorUids.get((int) taskIndex);
                if (anchorUid > 0 && userBaseInfoMap.containsKey(anchorUid)) {
                    taskItemVo.setNick(userBaseInfoMap.get(anchorUid).getNick());
                    taskItemVo.setAvatarUrl(userBaseInfoMap.get(anchorUid).getLogo());
                }
            }

            taskItemVos.add(taskItemVo);
        }

        result.put("taskInfo", taskItemVos);

        //奖池总限额
        result.put("totalAwardLimit", attr.getTaskAwardPoolLimit());
        long totalAwardLeft = 0;
        if (attr.getTaskAwardPoolLimit() > 0) {
            long alreadySend = Convert.toLong(actRedisDao.get(getRedisGroupCode(actId), buildAwardPoolAlreadySend(attr)), 0);
            totalAwardLeft = Math.max(0, attr.getTaskAwardPoolLimit() - alreadySend);
        }
        //奖池剩余金额
        result.put("totalAwardLeft", totalAwardLeft);

        return Response.success(result);
    }


    /**
     * 宝箱是否过期
     *
     * @return true==宝箱已过期
     */
    private boolean tryRemoveExpireBox(UserTaskAchieveComponentAttr attr, TreasureInfo box) {
        if (System.currentTimeMillis() > box.getExpireTime() + DateUtil.ONE_MIN_MILL_SECONDS) {
            actRedisDao.hdel(getRedisGroupCode(attr.getActId()), buildTreasureInfoKey(attr), box.getId());
            log.info("removeExpireBox,actId:{},boxInfo:{}", attr.getActId(), JSON.toJSONString(box));
        }

        return System.currentTimeMillis() > box.getExpireTime();
    }

    private long resolveBroFamilyId(RankingScoreChanged event, long anchorUid, long broTemplate) {
        //语音房模板才需要解析
        if (broTemplate != Template.SkillCard.getValue()) {
            return 0;
        }
        long familyId = 0;
        if (MapUtils.isNotEmpty(event.getActors())) {
            for (Long roleId : event.getActors().keySet()) {
                HdztActorInfo hdztActorInfo = hdztActorInfoService.getHdztActorInfo(roleId);
                if (hdztActorInfo == null) {
                    log.error("can not found role config,roleId:{}", roleId);
                    continue;
                }
                if (hdztActorInfo.getType() == RoleType.FAMILY.getValue()) {
                    familyId = Convert.toLong(event.getActors().get(roleId), 0);
                    log.info("resolveBroFamilyId,actId:{},anchorUid:{},familyId:{}", event.getActId(), anchorUid, familyId);
                    break;
                }
            }
        }
        if (familyId > 0) {
            return familyId;
        }

        Map<Long, Long> result = turnoverFamilyThriftClient.batchQueryContractFamilyIds(Lists.newArrayList(anchorUid));
        return result.getOrDefault(anchorUid, 0L);
    }


    /**
     * 运算能够参与抽奖的用户
     */
    private List<String> calTopAwardAnchor(List<Long> passIndex, UpdateTaskResult updateTaskResult, UserTaskAchieveComponentAttr attr) {
        List<String> topMembers = Lists.newArrayList();

        for (Long index : passIndex) {
            String topMember = StringUtil.EMPTY;
            Optional<String> contribute = attr.getContributeRoleType().stream().findFirst();
            if (contribute.isPresent()) {
                //memberid@roletype@passCurTask
                String keySuffix = "@" + contribute.get() + "@true";
                String group = getRedisGroupCode(attr.getActId());
                //注意：这里每过一级任务都会有1个贡献key，如果是后续有多级任务需求，注意根据根据变化需求调整此处逻辑！！！！！
                for (String memberInfo : updateTaskResult.getContributeKey().keySet()) {
                    if (!memberInfo.contains(keySuffix)) {
                        continue;
                    }
                    List<String> keys = updateTaskResult.getContributeKey().get(memberInfo);
                    //lua的任务是从1开始
                    String passIndexSuffix = ":" + (index + 1);
                    for (String key : keys) {
                        //对应等级key
                        if (!key.endsWith(passIndexSuffix)) {
                            continue;
                        }
                        Set<ZSetOperations.TypedTuple<String>> score = actRedisDao.zrevRange(group, key, Const.ONE);
                        if (!CollectionUtils.isEmpty(score)) {
                            topMember = score.stream().findFirst().get().getValue();
                        }

                    }
                }
            }

            topMembers.add(topMember);
        }


        return topMembers;
    }

    private List<Long> resolvePassIndex(UpdateTaskResult updateTaskResult) {
        List<Long> passLevel = Lists.newArrayList();
        //---过了任务，发奖、通知 TODO 这种算法只支持非循环任务
        if (updateTaskResult.getCurrTaskIndex() > updateTaskResult.getStartTaskIndex()) {
            long offsetIndex = updateTaskResult.getStartTaskIndex();
            while (offsetIndex < updateTaskResult.getCurrTaskIndex()) {
                //任务配置的索引从0开始
                passLevel.add(offsetIndex - 1);
                offsetIndex++;
            }
        }

        return passLevel;
    }

    /**
     * 用户累计达到5000贡献值，且小于10000贡献值时，弹窗弹出提示还差XXXX贡献值可达到初级新秀成就
     */
    private void noticeRule(RankingScoreChanged event, UserTaskAchieveComponentAttr attr) {
        if (attr.getNoticeRangeMin() < Const.ZERO || attr.getNoticeRangeMax() < Const.ZERO) {
            return;
        }
        if (event.getRankScore() >= attr.getNoticeRangeMin() && event.getRankScore() < attr.getNoticeRangeMax()) {
            String dupKey = makeKey(attr, NOTICE_RULES_KEY + event.getMember());
            if (actRedisDao.setNX(getRedisGroupCode(event.getActId()), dupKey, Const.ONESTR)) {
                long uid = Convert.toLong(event.getMember());
                long offset = attr.getNoticeRangeMax() - event.getRankScore();
                Map<String, Object> ext = Maps.newHashMap();
                ext.put("offsetScore", offset);
                commonBroadCastService.commonNoticeUnicast(event.getActId(), "5074_noticeRule", StringUtil.EMPTY, JSON.toJSONString(ext), uid);

                //--app弹窗
                if (StringUtil.isNotBlank(attr.getNoticeAppPopUrl())) {
                    AppPopUpEvent appPopUpEvent = new AppPopUpEvent();
                    appPopUpEvent.setUid(uid);
                    String url = attr.getNoticeAppPopUrl()
                            .replace("{$offsetScore}", Convert.toString(offset));
                    appPopUpEvent.setPopUrl(url);
                    appPopUpEvent.setSeq(event.getSeq());
                    appPopUpEvent.setProductTime(System.currentTimeMillis());
                    kafkaService.sendAppPopUp(appPopUpEvent);
                }
            }
        }
    }

    /**
     * 计算参与过任务的成员
     *
     * @return key===roleType value==memberid
     */
    private Map<String, String> calContributeRole(RankingScoreChanged event, UserTaskAchieveComponentAttr attr) {
        Map<String, String> contributeRole = Maps.newHashMap();
        if (MapUtils.isNotEmpty(event.getActors())) {
            for (Long roleId : event.getActors().keySet()) {
                HdztActorInfo hdztActorInfo = hdztActorInfoService.getHdztActorInfo(roleId);
                if (hdztActorInfo == null) {
                    log.error("can not found role config,roleId:{}", roleId);
                    continue;
                }
                String roleType = Convert.toString(hdztActorInfo.getType());
                if (!attr.getContributeRoleType().contains(roleType)) {
                    continue;
                }
                contributeRole.put(roleType, event.getActors().get(roleId));
            }
        }

        return contributeRole;
    }

    private String buildAchieveAnchorListKey(UserTaskAchieveComponentAttr attr, long userUid) {
        return makeKey(attr, String.format(ACHIEVE_ANCHOR_LIST, userUid));
    }

    private String buildTreasureInfoKey(UserTaskAchieveComponentAttr attr) {
        return makeKey(attr, TREASURE_INFO);
    }

    private String buildTreasureUserInfoKey(UserTaskAchieveComponentAttr attr, long uid) {
        return makeKey(attr, String.format(TREASURE_INFO_USER, uid));
    }

    private String buildTaskLevelKey(UserTaskAchieveComponentAttr attr, long level) {
        return makeKey(attr, String.format(TASK_LEVEL_USER, level));
    }

    private String buildAwardPoolAlreadySend(UserTaskAchieveComponentAttr attr) {
        return makeKey(attr, TASK_AWARD_AMOUNT);
    }

    private String buildJumpChannelBoxId(UserTaskAchieveComponentAttr attr, long uid) {
        return makeKey(attr, String.format(JUMP_CHANNEL_BOX_ID, uid));
    }


    @Override
    public List<String> supportKeys() {
        return null;
    }

    /**
     * 给神豪榜加上贡献神豪标签
     */
    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        UserTaskAchieveComponentAttr attr = tryGetUniqueComponentAttr(rankReq.getActId());
        if (attr == null || CollectionUtils.isEmpty(objectList)) {
            return objectList;
        }
        if (!attr.getShowRagRankId().contains(rankReq.getRankId())) {
            return objectList;
        }

        List<Long> taskConfig = attr.getTaskConfigValues();
        objectList.stream().map(rank -> ((RankValueItemBase) rank))
                .forEach(rank -> {
                    if (rank.getViewExt() == null) {
                        rank.setViewExt(Maps.newHashMap());
                    }
                    for (int i = taskConfig.size() - 1; i >= 0; i--) {
                        long scoreConfig = taskConfig.get(i);
                        if (rank.getValue() >= scoreConfig) {
                            rank.getViewExt().put("userTaskAchieveLevel", Convert.toString(i));
                            break;
                        }
                    }

                });
        return null;
    }

    private int calScoreLevel(long score, List<Long> configs) {
        int level = 0;
        for (int i = configs.size() - 1; i >= 0; i--) {
            long scoreConfig = configs.get(i);
            if (score >= scoreConfig) {
                return i + 1;
            }
        }
        return level;
    }

    @Data
    public static class InvokeBroAppBannerPara {
        private String seq;
        private long level;
        private String member;
        private String subChannel;
        private Map<String, String> dynamicReplace;
    }
}
