package com.yy.gameecology.activity.dao.mysql;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataHash;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataLimit;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataLimitRecord;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataList;
import com.yy.gameecology.common.utils.Convert;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-02-18 12:07
 **/
@Repository
public class CommonDataDao {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String HASH_INSERT_IGNORE = "insert ignore into component_data_hash_%d (act_id, component_id, cmpt_use_inx, key_name, hash_key_name, hash_value, create_time) values (?, ?, ?, ?, ?, ?, now())";

    @Autowired
    private GameecologyDao gameecologyDao;

    @Lazy
    @Autowired
    private CommonDataDao myself;

    /**
     * 【列表、单行数据操作】
     *
     * @param actId
     * @param cmptId
     * @param cmptIndex
     * @param seq
     * @param key
     * @param value
     * @return
     */
    public boolean listInsertIgnore(long actId, long cmptId, int cmptIndex, String seq, String key, String value) {
        if (seq.length() > 64) {
            throw new IllegalArgumentException("seq too long");
        }
        try {
            ComponentDataList data = new ComponentDataList();
            data.setActId(actId);
            data.setComponentId(cmptId);
            data.setCmptUseInx(cmptIndex);
            data.setSeq(seq);
            data.setKeyName(key);
            data.setValue(value);
            data.setCreateTime(new Date());
            gameecologyDao.insert(ComponentDataList.class, data, ComponentDataList.TABLE_NAME + actId);
        } catch (DuplicateKeyException e) {
            log.warn("listInsertIgnore dup,actId:{},cmptId:{},index:{},seq:{},key:{},value:{},e:{}", actId, cmptId, seq, key, value, e.getMessage(), e);
            return false;

        } catch (Throwable e) {
            log.error("listInsertIgnore error,actId:{},cmptId:{},index:{},seq:{},key:{},value:{},e:{}", actId, cmptId, seq, key, value, e.getMessage(), e);
            throw e;
        }

        return true;
    }

    /**
     * 【列表、单行数据操作】
     *
     * @param actId
     * @param cmptId
     * @param cmptIndex
     * @param page
     * @param pageSize
     * @return
     */
    public List<ComponentDataList> listSelect(long actId, long cmptId, int cmptIndex, int page, int pageSize) {
        ComponentDataList where = new ComponentDataList();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        String limit = String.format(" limit %s,%s", page * pageSize, pageSize);
        return gameecologyDao.select(ComponentDataList.class, where, limit, false, ComponentDataList.getTableName(actId));
    }

    public List<ComponentDataList> listSelect(long actId, long cmptId, int cmptIndex, int page, int pageSize, boolean down) {
        ComponentDataList where = new ComponentDataList();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        String sort = down ? "order by id desc" : "";
        String limit = String.format(sort + " limit %s,%s", page * pageSize, pageSize);
        return gameecologyDao.select(ComponentDataList.class, where, limit, false, ComponentDataList.getTableName(actId));
    }

    public List<ComponentDataList> listSelect(long actId, long cmptId, int cmptIndex, String key, int page, int pageSize) {
        ComponentDataList where = new ComponentDataList();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        where.setKeyName(key);
        String limit = String.format(" order by id desc limit %s,%s", page * pageSize, pageSize);
        return gameecologyDao.select(ComponentDataList.class, where, limit, false, ComponentDataList.getTableName(actId));
    }


    /**
     * 【列表、单行数据操作】
     *
     * @param actId
     * @param cmptId
     * @param cmptIndex
     * @param key
     * @return
     */
    public ComponentDataList listSelectOne(long actId, long cmptId, int cmptIndex, String key) {
        ComponentDataList where = new ComponentDataList();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setComponentId(cmptId);
        where.setKeyName(key);
        return gameecologyDao.selectOne(ComponentDataList.class, where, "order by id desc limit 1 ", ComponentDataList.getTableName(actId), false);
    }

    /**
     * 【列表、单行数据操作】
     *
     * @param actId
     * @param cmptId
     * @param cmptIndex
     * @param key
     * @return
     */
    public String listSelectOneValue(long actId, long cmptId, int cmptIndex, String key) {
        ComponentDataList where = new ComponentDataList();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setComponentId(cmptId);
        where.setKeyName(key);
        ComponentDataList componentDataList = gameecologyDao.selectOne(ComponentDataList.class, where, " limit 1 ", ComponentDataList.getTableName(actId), false);
        return componentDataList == null ? "" : componentDataList.getValue();
    }

    /**
     * 【列表、单行数据操作】
     *
     * @param actId
     * @param cmptId
     * @param cmptIndex
     * @param key
     * @param <T>
     * @return
     */
    public <T> T listSelectOneObject(long actId, long cmptId, int cmptIndex, String key) {
        ComponentDataList where = new ComponentDataList();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setComponentId(cmptId);
        where.setKeyName(key);
        ComponentDataList componentDataList = gameecologyDao.selectOne(ComponentDataList.class, where, " limit 1 ", ComponentDataList.getTableName(actId), false);
        return componentDataList == null ? null : JSON.parseObject(componentDataList.getValue(), new TypeReference<T>() {
        });
    }

    /**
     * 【限额表操作】
     * 同1个key,若有重复seq请求，则会返回第一次更新后的value
     *
     * @param actId     活动id
     * @param cmptId
     * @param cmptIndex
     * @param seq
     * @param key       操作表：component_data_limit_${actId}
     * @param value     增量值
     * @return 更新后的值
     */
    public long valueIncrIgnore(long actId, long cmptId, long cmptIndex, String seq, String key, long value) {
        ValueIncResult result = myself.valueIncrIgnoreWithLimit(actId, cmptId, cmptIndex, seq, key, value, 0);
        return result.getAfterInc();
    }

    @Transactional
    public ComponentDataList listAndDel(long actId, long cmptId, int cmptIndex, String key) {
        ComponentDataList componentDataList = listSelectOne(actId, cmptId, cmptIndex, key);
        if(componentDataList == null) {
            return null;
        }
        var me = new ComponentDataList();
        me.setId(componentDataList.getId());
        gameecologyDao.delete(ComponentDataList.class, me, "", ComponentDataList.getTableName(actId));
        return componentDataList;
    }

    /**
     * 【限额表操作】
     * 同1个key,若有重复seq请求，则会返回第一次更新后的value
     *
     * @param actId     活动id
     * @param cmptId
     * @param cmptIndex
     * @param seq
     * @param key       操作表：component_data_limit_${actId}
     * @param value     增量值
     * @param limit 增加后不超过这个限额，传入0代表不限额
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueIncResult valueIncrIgnoreWithLimit(long actId, long cmptId, long cmptIndex, String seq, String key, long value, long limit) {
        log.info("valueIncrIgnoreWithLimit,actId:{},cmptId:{},cmptIndex:{},seq:{},key:{},value:{},limit:{}", actId, cmptId, cmptIndex, seq, key, value, limit);
        if (seq != null && seq.length() > 127) {
            throw new IllegalArgumentException("seq too long");
        }
        ValueIncResult result = new ValueIncResult();

        //范围最大限制规则
        if (value > limit && limit > 0) {
            result.setViolateLimit(true);
            return result;
        }

        ComponentDataLimitRecord record = getComponentDataLimitRecord(actId, cmptId, cmptIndex, seq, key);
        if (record != null) {
            result.setAfterInc(record.getAfterUpdate());
            return result;
        }

        //-----新增数据的流水记录
        ComponentDataLimit where = new ComponentDataLimit();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        where.setKeyName(key);
        //因为要准确记录更新前后的值，所以这里用select for update 上锁，事务比较小，且为保证上层调用成功概率，这里不用无锁设计
        ComponentDataLimit dataLimit = gameecologyDao.selectOne(ComponentDataLimit.class, where, "", ComponentDataLimit.getTableName(actId), true);

        //变更后超限额
        if (dataLimit != null && limit > 0 && dataLimit.getValue() + value > limit) {
            result.setViolateLimit(true);
            return result;
        }

        ComponentDataLimitRecord insert = new ComponentDataLimitRecord();
        insert.setActId(actId);
        insert.setComponentId(cmptId);
        insert.setCmptUseInx(cmptIndex);
        insert.setKeyName(key);
        insert.setSeq(seq);
        insert.setBeforeUpdate(dataLimit == null ? 0 : dataLimit.getValue());
        insert.setAfterUpdate(dataLimit == null ? value : dataLimit.getValue() + value);
        insert.setCreateTime(new Date());
        gameecologyDao.insert(ComponentDataLimitRecord.class, insert, ComponentDataLimitRecord.getTableName(actId));

        //-----更新限额值
        if (dataLimit == null) {
            dataLimit = new ComponentDataLimit();
            dataLimit.setActId(actId);
            dataLimit.setComponentId(cmptId);
            dataLimit.setCmptUseInx(cmptIndex);
            dataLimit.setKeyName(key);
            dataLimit.setValue(value);
            dataLimit.setCreateTime(new Date());
            gameecologyDao.insert(ComponentDataLimit.class, dataLimit, ComponentDataLimit.getTableName(actId));
        } else {
            ComponentDataLimit update = new ComponentDataLimit();
            update.setValue(dataLimit.getValue() + value);
            gameecologyDao.update(ComponentDataLimit.class, where, update, "","",ComponentDataLimit.getTableName(actId));
        }

        result.setAfterInc(insert.getAfterUpdate());
        return result;
    }

    /**
     * 【限额表操作】
     *
     * @param actId
     * @param cmptId
     * @param cmptIndex
     * @param key       操作表：component_data_limit_${actId}
     * @return
     */
    public long valueGet(long actId, long cmptId, long cmptIndex, String key) {
        ComponentDataLimit where = new ComponentDataLimit();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        where.setKeyName(key);
        ComponentDataLimit dataLimit = gameecologyDao.selectOne(ComponentDataLimit.class, where, "", ComponentDataLimit.getTableName(actId), false);
        return dataLimit == null ? 0 : Convert.toLong(dataLimit.getValue(), 0);
    }

    /**
     * 【限额表操作】
     *
     * @param actId
     * @param cmptId
     * @param cmptIndex
     * @param key
     * @param seq
     * @return result[1]== 1 代表 seq增加过限额，result[0] 返回seq当时更新后的值 result[2] 返回seq当时更新前的值 ; result[1]== 0 代表 seq 未更新过限额，返回当前最新值
     */
    public LimitUpdateState valueGetSnapshot(long actId, long cmptId, long cmptIndex, String key, String seq) {
        LimitUpdateState result = new LimitUpdateState(0,0,0);

        //已更新过
        ComponentDataLimitRecord record = getComponentDataLimitRecord(actId, cmptId, cmptIndex, seq, key);
        if (record != null) {
            result.setAfterValue(record.getAfterUpdate());
            result.setUpdateSate(1L);
            result.setBeforeValue(record.getBeforeUpdate());
            return result;
        }

        //未更新过
        ComponentDataLimit where = new ComponentDataLimit();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        where.setKeyName(key);
        ComponentDataLimit dataLimit = gameecologyDao.selectOne(ComponentDataLimit.class, where, "", ComponentDataLimit.getTableName(actId), false);
        long value = dataLimit == null ? 0 : Convert.toLong(dataLimit.getValue(), 0);
        result.setAfterValue(0);
        result.setUpdateSate(0L);
        result.setBeforeValue(value);
        return result;
    }


    public int valueDel(long actId, long cmptId, long cmptIndex, String key) {
        var me = new ComponentDataLimit();
        me.setActId(actId);
        me.setComponentId(cmptId);
        me.setCmptUseInx(cmptIndex);
        me.setKeyName(key);
        return gameecologyDao.delete(ComponentDataLimit.class, me, "", ComponentDataLimit.getTableName(actId));
    }

    public String hashValueGet(long actId, long cmptId, long cmptIndex, String key, String hashKey) {
        ComponentDataHash where = new ComponentDataHash();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        where.setKeyName(key);
        where.setHashKeyName(hashKey);
        ComponentDataHash result = gameecologyDao.selectOne(ComponentDataHash.class, where, "", ComponentDataHash.getTableName(actId), false);
        if (result == null) {
            return null;
        }
        return result.getHashValue();
    }

    public int hashValueDel(long actId, long cmptId, long cmptIndex, String key, String hashKey) {
        ComponentDataHash where = new ComponentDataHash();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        where.setKeyName(key);
        where.setHashKeyName(hashKey);
        return gameecologyDao.delete(ComponentDataHash.class,where,"",ComponentDataHash.getTableName(actId));
    }

    public long hashValueCount(long actId, long cmptId, long cmptIndex, String key) {
        ComponentDataHash where = new ComponentDataHash();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        where.setKeyName(key);
        Long ret = gameecologyDao.count(ComponentDataHash.class,where,"",ComponentDataHash.getTableName(actId));
        return ret == null ? 0 : Convert.toLong(ret);
    }

    /**
     * 设置哈希值
     *
     * @param actId 活动ID
     * @param cmptId 组件ID
     * @param cmptIndex 组件索引
     * @param key 操作表的键名
     * @param hashKey 哈希表的键名
     * @param value 要设置的哈希值
     */
    public int hashValueSet(long actId, long cmptId, long cmptIndex, String key, String hashKey, String value) {
        String sql = "INSERT INTO component_data_hash_%s (act_id, component_id, cmpt_use_inx, key_name, hash_key_name, hash_value, create_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, NOW()) " +
                "ON DUPLICATE KEY UPDATE  " +
                "    hash_value = VALUES(hash_value)";
        sql = String.format(sql, actId);

        return gameecologyDao.update(sql, actId, cmptId, cmptIndex, key, hashKey, value);
    }

    /**
     * 设置哈希值
     *
     * @param actId 活动ID
     * @param cmptId 组件ID
     * @param cmptIndex 组件索引
     * @param key 操作表的键名
     * @param hashKey 哈希表的键名
     * @param value 要设置的哈希值
     */
    public int hashValueSetOrIncr(long actId, long cmptId, long cmptIndex, String key, String hashKey, long value) {
        String sql = "INSERT INTO component_data_hash_%s (act_id, component_id, cmpt_use_inx, key_name, hash_key_name, hash_value, create_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, NOW()) " +
                "ON DUPLICATE KEY UPDATE  " +
                "    hash_value = hash_value + VALUES(hash_value)";
        sql = String.format(sql, actId);

        return gameecologyDao.update(sql, actId, cmptId, cmptIndex, key, hashKey, value);
    }

    public boolean hashValueSetNX(long actId, long cmptId, long cmptIndex, String key, String hashKey, String value) {
        String sql = String.format(HASH_INSERT_IGNORE, actId);
        return gameecologyDao.getJdbcTemplate().update(sql, actId, cmptId, cmptIndex, key, hashKey, value) >= 1;
    }

    public void hashValueIncIgnore(long actId, long cmptId, long cmptIndex, String seq, String key, String hashKey, long value) {

        ComponentDataLimitRecord record = getComponentDataLimitRecord(actId, cmptId, cmptIndex, seq, key);
        if (record != null) {
            log.info("hashValueInc exist,inc skip,actId:{},cmptId:{},cmptIndex:{},seq:{},key:{},hashKey:{},value:{}", actId, cmptId, cmptIndex, seq, key, hashKey, value);
            return;
        }

        ComponentDataLimitRecord insert = new ComponentDataLimitRecord();
        insert.setActId(actId);
        insert.setComponentId(cmptId);
        insert.setCmptUseInx(cmptIndex);
        insert.setKeyName(key);
        insert.setSeq(seq);
        insert.setBeforeUpdate(0L);
        insert.setAfterUpdate(0L);
        insert.setCreateTime(new Date());
        try {
            gameecologyDao.insert(ComponentDataLimitRecord.class, insert, ComponentDataLimitRecord.getTableName(actId));
        } catch (DuplicateKeyException e) {
            log.warn("hashValueInc duplicateKeyException,inc skip,actId:{},cmptId:{},cmptIndex:{},seq:{},key:{},hashKey:{},value:{}", actId, cmptId, cmptIndex, seq, key, hashKey, value);
            return;
        }


        String sql = "INSERT INTO component_data_hash_%s (act_id, component_id, cmpt_use_inx, key_name, hash_key_name, hash_value, create_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, NOW()) " +
                "ON DUPLICATE KEY UPDATE  " +
                "    hash_value = hash_value + VALUES(hash_value)";
        sql = String.format(sql, actId);

        gameecologyDao.update(sql, actId, cmptId, cmptIndex, key, hashKey, value);
    }

    public ValueIncResult hashValueIncreaseIgnoreWithLimit(long actId, long cmptId, long cmptIndex, String seq, String key, String hashKey, long value, long limit) {
        ComponentDataLimitRecord record = getComponentDataLimitRecord(actId, cmptId, cmptIndex, seq, key);
        ValueIncResult result = new ValueIncResult();
        if (record != null) {
            result.setAfterInc(record.getAfterUpdate());
            return result;
        }

        ComponentDataLimitRecord insert = new ComponentDataLimitRecord();
        insert.setActId(actId);
        insert.setComponentId(cmptId);
        insert.setCmptUseInx(cmptIndex);
        insert.setKeyName(key);
        insert.setSeq(seq);
        insert.setBeforeUpdate(0L);
        insert.setAfterUpdate(0L);
        insert.setCreateTime(new Date());
        try {
            gameecologyDao.insert(ComponentDataLimitRecord.class, insert, ComponentDataLimitRecord.getTableName(actId));
        } catch (DuplicateKeyException e) {
            log.warn("hashValueIncreaseIgnoreWithLimit duplicateKeyException,inc skip,actId:{},cmptId:{},cmptIndex:{},seq:{},key:{},hashKey:{},value:{}", actId, cmptId, cmptIndex, seq, key, hashKey, value);
            record = getComponentDataLimitRecord(actId, cmptId, cmptIndex, seq, key);
            Assert.notNull(record, "duplicate key record is null");
            result.setAfterInc(record.getAfterUpdate());
            return result;
        }

        ComponentDataHash me = new ComponentDataHash();
        me.setActId(actId);
        me.setComponentId(cmptId);
        me.setCmptUseInx(cmptIndex);
        me.setKeyName(key);
        me.setHashKeyName(hashKey);
        var dataHash = gameecologyDao.selectOne(ComponentDataHash.class, me, "", ComponentDataHash.getTableName(actId), true);
        if (dataHash == null) {
            ComponentDataHash insertHash = new ComponentDataHash();
            insertHash.setActId(actId);
            insertHash.setComponentId(cmptId);
            insertHash.setCmptUseInx(cmptIndex);
            insertHash.setKeyName(key);
            insertHash.setHashKeyName(hashKey);
            insertHash.setHashValue(String.valueOf(value));
            insertHash.setCreateTime(new Date());
            int rs = gameecologyDao.insert(ComponentDataHash.class, insertHash, ComponentDataHash.getTableName(actId));
            log.info("hashValueIncreaseIgnoreWithLimit insert with key:{},hashKey:{},value:{} rs:{}", key, hashKey, value, rs);
            result.setAfterInc(value);
            return result;
        }
        if (!StringUtils.isNumeric(dataHash.getHashValue())) {
            throw new UnsupportedOperationException("hash value type is not numeric");
        }

        long current = Long.parseLong(dataHash.getHashValue());
        if (current + value > limit) {
            result.setViolateLimit(true);
            result.setAfterInc(current);
            return result;
        }

        ComponentDataHash updateHash = new ComponentDataHash();
        updateHash.setId(dataHash.getId());
        updateHash.setHashValue(String.valueOf(current + value));

        int rs = gameecologyDao.updateByKey(ComponentDataHash.class, updateHash, ComponentDataHash.getTableName(actId));
        log.info("hashValueIncreaseIgnoreWithLimit update with key:{},hashKey:{},value:{},rs:{}", key, hashKey, current + value, rs);

        result.setAfterInc(current + value);
        return result;

    }

    public int insertIgnoreComponentDataLimitRecord(long actId, long cmptId, long cmptIndex, String key, String seq) {
        String sql = "INSERT IGNORE INTO component_data_limit_record_%s (act_id, component_id, cmpt_use_inx, key_name, seq, before_update, after_update, create_time) " +
                "VALUES (?,?,?,?,?,0,0,now())";
        sql = String.format(sql, actId);

        return gameecologyDao.update(sql, actId, cmptId, cmptIndex, key, seq);
    }


    public Map<String, String> hashGetAll(long actId, long cmptId, long cmptIndex, String key) {
        ComponentDataHash where = new ComponentDataHash();
        where.setActId(actId);
        where.setComponentId(cmptId);
        where.setCmptUseInx(cmptIndex);
        where.setKeyName(key);
        List<ComponentDataHash> result = gameecologyDao.select(ComponentDataHash.class, where, "", false, ComponentDataHash.getTableName(actId));
        if (result == null || result.isEmpty()) {
            return Collections.emptyMap();
        }

        return result.stream().collect(Collectors.toMap(ComponentDataHash::getHashKeyName, ComponentDataHash::getHashValue));
    }


    private ComponentDataLimitRecord getComponentDataLimitRecord(long actId, long cmptId, long cmptIndex, String seq, String key) {
        ComponentDataLimitRecord recordWhere = new ComponentDataLimitRecord();
        recordWhere.setActId(actId);
        recordWhere.setComponentId(cmptId);
        recordWhere.setKeyName(key);
        recordWhere.setCmptUseInx(cmptIndex);
        recordWhere.setSeq(seq);
        return gameecologyDao.selectOne(ComponentDataLimitRecord.class, recordWhere, "", ComponentDataLimitRecord.getTableName(actId), false);
    }

    public ComponentDataLimitRecord getComponentDataLimitRecord(long actId, long cmptId, long cmptIndex, String key) {
        ComponentDataLimitRecord recordWhere = new ComponentDataLimitRecord();
        recordWhere.setActId(actId);
        recordWhere.setComponentId(cmptId);
        recordWhere.setKeyName(key);
        recordWhere.setCmptUseInx(cmptIndex);
        return gameecologyDao.selectOne(ComponentDataLimitRecord.class, recordWhere, "order by id desc limit 1", ComponentDataLimitRecord.getTableName(actId), false);
    }

    @Data
    public static class LimitUpdateState {
        private long beforeValue;
        private long afterValue;
        private long updateSate; // 1 更新过

        public LimitUpdateState(int before, int after, int state) {
            this.beforeValue = before; // 初始化成员变量1
            this.afterValue = after; // 初始化成员变量2
            this.updateSate = state; // 初始化成员变量3
        }
    }

    @Data
    public static class ValueIncResult {
        private long afterInc;
        /**
         * true==新增后违反限额，新增失败
         */
        private boolean violateLimit;
    }

}
