package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.hdzj.element.component.attr.ReceiveAwardComponentAttr;
import com.yy.gameecology.hdzj.element.redis.ReceiveAwardComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-08-12 17:17
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties"})
public class ReceiveAwardComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    long actId = 2021084002L;

    @Autowired
    private ReceiveAwardComponent receiveAwardComponent;

    //任务状态读取

    //奖励读取

    //发奖

    @Test
    public void test() throws Exception {
        ReceiveAwardComponentAttr attr = receiveAwardComponent.getComponentAttr(actId, 1);
        receiveAwardComponent.queryAwardRecordStatus(actId, 789, 1);
        receiveAwardComponent.releaseAward(actId, 1, 789, "send_gift_50", 1);
        receiveAwardComponent.queryAwardRecord(actId, 789, 1);
    }
}
