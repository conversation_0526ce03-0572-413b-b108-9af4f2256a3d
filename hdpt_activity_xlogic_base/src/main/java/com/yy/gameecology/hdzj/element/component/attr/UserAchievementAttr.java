package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class UserAchievementAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "过滤弹幕游戏频道", remark = "是否过滤弹幕游戏频道，1-是，0-否")
    protected int excludeDanmaku = 0;

    @ComponentAttrField(labelText = "任务榜单ID")
    private long taskRankId;

    @ComponentAttrField(labelText = "神豪榜单ID")
    private long contributorRankId;

    @ComponentAttrField(labelText = "神豪阶段ID")
    private long contributorPhaseId;

    @ComponentAttrField(labelText = "强厅角色")
    private long tingActor;

    @ComponentAttrField(labelText = "神豪贡献配置"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "等级"),
            @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "分数")
    })
    private Map<Integer, Long> levelScoreMap;

    @ComponentAttrField(labelText = "神豪贡献广播配置"
            , subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "等级"),
            @SubField(fieldName = Constant.VALUE, type = BroType.class, labelText = "广播范围配置")
    })
    private Map<Integer, BroType> levelBroMap;

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private long busiId = 200;

    @ComponentAttrField(labelText = "抽奖次数")
    private int lotteryCnt;

    @ComponentAttrField(labelText = "抽奖奖池")
    private long lotteryTaskId;

    @ComponentAttrField(labelText = "神豪抽奖奖池")
    private long userLotteryTaskId;

    @ComponentAttrField(labelText = "实物奖包id", remark = "奖包id配置,多个时逗号分隔"
            , subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private List<Long> specialPackageIds = new ArrayList<>();

    @ComponentAttrField(labelText = "是否是svga资源")
    private boolean svga;

    @ComponentAttrField(labelText = "动画配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svgaConfigCode"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvagConfig.class, labelText = "svga/mp4配置")})
    private Map<String, BannerSvagConfig> bannerSvag;

    @ComponentAttrField(labelText = "动画文案配置",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "svga/mp4文案配置编码"),
                    @SubField(fieldName = Constant.VALUE, type = BannerSvgaTextConfig.class, labelText = "svga/mp4文案配置")})
    private Map<String, BannerSvgaTextConfig> svgaText;

    @ComponentAttrField(labelText = "动画动态文案", remark = "可用于替换svga/mp4文案配置-富文本消息 中的占位符",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "文案中的占位符"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "占位符对应的值")})
    private Map<String, String> textDynamicValue;

    @ComponentAttrField(labelText = "动画图片key",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "imgkey"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "图片地址")})
    private Map<String, String> svgaImgLayers;

    @ComponentAttrField(labelText = "循环次数", remark = "循环播放次数, 0-无限循环(勿填0)")
    private int loops = 1;

    @ComponentAttrField(labelText = "布局", remark = " 横幅类型(contentType)==6使用 可选 动画播放位置（左右充满，垂直对齐类型）0：全屏播放；1：居中对齐播放；2：顶部对齐播放；3：底部对齐播放")
    private int layoutType;

    @ComponentAttrField(labelText = "布局边距", remark = "相对父布局的间距 2个元素的数组，分别对应顶部和底部的间距；对应位置为[top, bottom]。通常top、bottom为0。配置例子：{\"android\":[10,0],\"ios\":[0,0]}")
    private String layoutMargin = "{\"android\":[10,0],\"ios\":[0,0]}";

    @ComponentAttrField(labelText = "宽高比", remark = "必填 宽高比 客户端默认宽为全屏，svga高度根据宽高比计算,填写实例：6:9")
    private String whRatio;

    @ComponentAttrField(labelText = "广播业务ID", remark = "1 -语音房 2 -交友房 4 -其他 8 -宝贝 位域表示 支持组合：交友and宝贝：2+8")
    private int broBusiId;

    @Data
    public static class BroType {
        @ComponentAttrField(labelText = "pc频道广播范围")
        private int svcBroType;

        @ComponentAttrField(labelText = "svga广播范围")
        private int svgaBroType;

        @ComponentAttrField(labelText = "mp4广播范围")
        private int mp4BroType;
    }

    @Data
    public static class Award {
        @ComponentAttrField(labelText = "奖池id")
        private int taskId;

        @ComponentAttrField(labelText = "奖包id")
        private int packageId;
    }

}
