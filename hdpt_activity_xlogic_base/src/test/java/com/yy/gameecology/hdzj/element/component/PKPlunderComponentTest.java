package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.BaseEvent;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.hdzj.element.component.attr.PKPlunderComponentAttr;
import com.yy.gameecology.hdzj.element.redis.PKPlunderComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class PKPlunderComponentTest {

    private final static long actId = 2022101001;

    private final static long cmptUseIndex = 500;

    static {
        System.setProperty("group", "1");
    }


    @Autowired
    private PKPlunderComponent pkPlunderComponent;


    @Test
    public void initCurrency() {
        PKPlunderComponentAttr attr = pkPlunderComponent.getComponentAttr(actId, cmptUseIndex);
        PromotTimeEnd event = new PromotTimeEnd();
        event.setActId(actId);
        event.setRankId(71);
        event.setPhaseId(71);
        event.setUri(BaseEvent.PROMOT_TIME_END);
        pkPlunderComponent.initCurrency(event, attr);
    }


    @Test
    public void plunderSettle() {
        PKPlunderComponentAttr attr = pkPlunderComponent.getComponentAttr(actId, cmptUseIndex);
        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setActId(actId);
        event.setRankId(71);
        event.setPhaseId(71);
        event.setEndTime("2022-11-17 23:59:59");
        event.setTimeKey(TimeKeyHelper.TIME_KEY_NO);
        event.setUri(BaseEvent.PHASE_TIME_END);
        pkPlunderComponent.plunderSettle(event, attr);
    }
}
