package com.yy.gameecology.hdzj.element.component.xmodule.pepc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.mms.MmsCmdResult;
import com.yy.gameecology.activity.bean.mms.MmsReportCmdRspVo;
import com.yy.gameecology.activity.bean.wzry.JoinGameResult;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.yrpc.UgcRoomClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.pepc.*;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.mapper.pepc.PepcMatchAwardRecordExtMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamExtMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMemberMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.*;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.pepc.PepcChannelGameVo;
import com.yy.gameecology.hdzj.bean.pepc.PepcTeamCurrentGameVo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.PepcActTeamComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.zhuiwan.BatchGetUserOnlineRsp;
import com.yy.thrift.zhuiwan_newfamily.ChannelKey;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@UseRedisStore(NeedChange = true)
@Component
@RestController
@RequestMapping("/5142")
public class PepcActTeamComponent extends BaseActComponent<PepcActTeamComponentAttr> {
    @Override
    public Long getComponentId() {
        return ComponentId.PEPC_TEAM;
    }

    @Autowired
    private PepcMatchAwardRecordExtMapper pepcMatchAwardRecordExtMapper;

    @Autowired
    private PepcTeamMapper pepcTeamMapper;

    private static final String APPLY_NOTICE = "PEPC_NOTICE_%s";

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    private UgcRoomClient ugcRoomClient;

    @Autowired
    private PepcActTeamService pepcActTeamService;

    @Autowired
    private PepcTeamApplyService pepcTeamApplyService;

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    protected UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private PepcTeamMemberMapper pepcTeamMemberMapper;

    @Autowired
    private PepcTeamExtMapper pepcTeamExtMapper;

    @Autowired
    private PepcEnrollmentService pepcEnrollmentService;

    @Autowired
    private PepcPushComponent pepcPushComponent;

    @Autowired
    private PepcPhaseInfoService pepcPhaseInfoService;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private PepcPhaseComponent pepcPhaseComponent;

    @Autowired
    private PepcGameService pepcGameService;

    @RequestMapping("/teamList")
    public Response<TeamListRsp> teamList(@RequestParam("actId") long actId,
                                          @RequestParam(value = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                          @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
                                          @RequestParam(name = "pageSize", required = false, defaultValue = "30") Integer pageSize) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "请先登录");
        }

        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "活动不存在");
        }

        return getTteamList(actId, cmptInx, uid, page, pageSize);
    }

    public Response<TeamListRsp> getTteamList(long actId,
                                             long cmptInx,
                                             long uid,
                                             Integer page,
                                             Integer pageSize) {

        //防止前端获取过大数据
        pageSize = Math.min(pageSize, 200);

        TeamListRsp teamListRsp = new TeamListRsp();
        List<ChannelKey> channelKeys = zhuiWanPrizeIssueServiceClient.listSkillCardAllOnlineChannel();
        Set<String> channelMap = channelKeys.stream().map(key -> key.getTopSid() + "_" + key.getSubSid()).collect(Collectors.toSet());

        // 无队伍
        List<PepcTeam> teamList = pepcActTeamService.listTeam(actId, page, pageSize);
        if (CollectionUtils.isNotEmpty(teamList)) {
            Map<Long, PepcTeam> teamMap = new HashMap<>(teamList.size());
            List<Long> teamIds = new ArrayList<>(teamList.size());
            List<Team> teams = new ArrayList<>(teamList.size());
            for (PepcTeam pepcTeam : teamList) {
                teamIds.add(pepcTeam.getId());
                teamMap.put(pepcTeam.getId(), pepcTeam);
            }

            List<PepcTeamApply> applies = pepcTeamApplyService.listUnHandleApplyByAct(actId, teamIds, uid);
            Map<Long, PepcTeamApply> applyMap = applies.stream().collect(Collectors.toMap(PepcTeamApply::getTeamId, Function.identity()));

            List<PepcTeamMember> teamMembers = pepcTeamMemberMapper.listTeamMembers(teamIds);
            Map<Long, List<PepcTeamMember>> listMap = new HashMap<>();
            List<Long> teamMemberUid = Lists.newArrayList();
            for (PepcTeamMember pepcTeamMember : teamMembers) {
                List<PepcTeamMember> members =
                        listMap.getOrDefault(pepcTeamMember.getTeamId(), new ArrayList<>());
                members.add(pepcTeamMember);
                teamMemberUid.add(pepcTeamMember.getMemberUid());
                listMap.put(pepcTeamMember.getTeamId(), members);
            }
            Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(teamMemberUid, false);
            BatchGetUserOnlineRsp batchGetUserOnline = zhuiWanPrizeIssueServiceClient.batchGetUserOnline(teamMemberUid);

            for (Long teamId : teamIds) {
                Team team = new Team();
                team.setTeamId(teamId);
                PepcTeam pepcTeam = teamMap.get(teamId);
                team.setName(pepcTeam.getTeamName());
                List<Teamate> teamates = new ArrayList<>();
                for (PepcTeamMember m : listMap.get(teamId)) {
                    log.info("teamId:{} m:{}", teamId, m);
                    Teamate teamate = new Teamate();
                    String nick = userInfos.get(m.getMemberUid()).getNick();
                    teamate.setName(nick);
                    teamate.setUid(m.getMemberUid());
                    teamate.setAvatar(userInfos.get(m.getMemberUid()).getHdLogo());
                    if (batchGetUserOnline != null && batchGetUserOnline.userOnlineMap.containsKey(m.getMemberUid())) {
                        teamate.setLive(batchGetUserOnline.userOnlineMap.get(m.getMemberUid()));
                    }
                    teamates.add(teamate);
                }
                team.setTp(pepcTeam.getNeedApply());
                if (applyMap.containsKey(teamId)) {
                    // 有申请记录就认为可以申请
                    team.setTp(2);
                }
                team.setDeclaration(pepcTeam.getDeclaration());
                team.setTeamates(teamates);
                team.setSid(pepcTeam.getSid());
                team.setSsid(pepcTeam.getSsid());
                team.setInRoom(channelMap.contains(pepcTeam.getSid() + "_" + pepcTeam.getSsid()));
                team.setMemberCnt(pepcTeam.getMemberCnt());
                team.setMemberLimit(pepcTeam.getMemberLimit());
                teams.add(team);
            }


            List<Team> fullTeams = teams.stream().filter(p -> p.getMemberCnt() >= p.getMemberLimit()).toList();
            List<Team> notFullTeams = teams.stream().filter(p -> p.getMemberCnt() < p.getMemberLimit()).toList();

            notFullTeams = notFullTeams.stream()
                    .sorted(Comparator.comparing(Team::getMemberCnt, Comparator.reverseOrder())
                            .thenComparing(Team::isInRoom, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

            List<Team> allTeam = Lists.newLinkedList(notFullTeams);
            allTeam.addAll(fullTeams);
            teamListRsp.setTeamList(allTeam);
        }


        return Response.success(teamListRsp);
    }

    @RequestMapping("/myTeamInfo")
    public Response<MyTeamRsp> myTeamInfo(@RequestParam("actId") long actId,
                                          @RequestParam(value = "cmptInx", required = false, defaultValue = "810") long cmptInx) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "请先登录");
        }

        // 时段检查
        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "活动不存在");
        }

        Date now = commonService.getNow(actId);
//        final AovPhase aovPhase = findAovPhase(actId, phaseId);
//        if (aovPhase == null) {
//            return Response.fail(400, "当前无赛程");
//        }

        MyTeamRsp myTeamRsp = new MyTeamRsp();
        myTeamRsp.setTeamLeadGroupQr(attr.getTeamLeadGroupQr());
        myTeamRsp.setTeamLeadGroupId(attr.getTeamLeadGroupId());
        myTeamRsp.setTeammatesGroupQr(attr.getTeammatesGroupQr());
        myTeamRsp.setWxNum(attr.getWxNum());
        myTeamRsp.setDeclarationDefault(attr.getTeamDeclaration());

        List<ChannelKey> channelKeys = zhuiWanPrizeIssueServiceClient.listSkillCardAllOnlineChannel();
        Set<String> channelMap = channelKeys.stream().map(key -> key.getTopSid() + "_" + key.getSubSid()).collect(Collectors.toSet());

        log.info("uid:{} actId:{}", uid, actId);
        PepcTeamMember currentTeamMember = pepcTeamMemberMapper.selectByUniq(actId, uid);
        // 有队伍
        if (currentTeamMember != null) {
            buildMyTeamInfo(attr,  currentTeamMember, channelMap, now, myTeamRsp);
        }

        MyInfo myInfo = new MyInfo();
        UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, false);
        myInfo.setName(userBaseInfo.getNick());
        myInfo.setAvatar(userBaseInfo.getHdLogo());
        myTeamRsp.setMyInfo(myInfo);

        if (!myTeamRsp.isRedDot()) {
            myTeamRsp.setRedDot(!StringUtil.isEmpty(actRedisDao.hget(redisConfigManager.getGroupCode(actId),
                    String.format(APPLY_NOTICE, actId), String.valueOf(uid))));
        }

        return Response.success(myTeamRsp);
    }


    private void buildMyTeamInfo(PepcActTeamComponentAttr attr, @NotNull PepcTeamMember currentTeamMember, Set<String> channelMap, Date now, MyTeamRsp rsp) {
        final long teamId = currentTeamMember.getTeamId();
        log.info("teamId:{} attr:{} uid:{}", teamId, attr, currentTeamMember.getMemberUid());
        long actId = attr.getActId();
        PepcTeam team = pepcActTeamService.selectById(teamId);
        List<PepcTeamMember> pepcTeamMembers = pepcTeamMemberMapper.selectPepcTeamMember(teamId);
        List<Long> uids = pepcTeamMembers.stream().map(PepcTeamMember::getMemberUid).toList();
        Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(uids, false);
        BatchGetUserOnlineRsp batchGetUserOnline = zhuiWanPrizeIssueServiceClient.batchGetUserOnline(uids);
        rsp.setCaptain(Objects.equals(team.getCreator(), currentTeamMember.getMemberUid()));

        Team currentTeam = new Team();
        currentTeam.setTeamId(teamId);
        currentTeam.setName(team.getTeamName());
        currentTeam.setTeamNameAuditState(team.getTeamNameAuditState());
        currentTeam.setTeamNameAudit(team.getTeamNameAudit());
        currentTeam.setDeclaration(team.getDeclaration());
        if (team.getAuditState().equals(PepcConst.PepcTeamMsgAuditState.INIT)) {
            currentTeam.setDeclarationAudit(team.getDeclarationAudit());
        }
        currentTeam.setSid(team.getSid());
        currentTeam.setSsid(team.getSsid());
        currentTeam.setTp(team.getNeedApply());

        List<Teamate> teammates = new ArrayList<>();
        for (PepcTeamMember m : pepcTeamMembers) {
            Teamate teamate = new Teamate();
            long memberUid = m.getMemberUid();
            teamate.setUid(memberUid);
            UserBaseInfo userBaseInfo = userInfos.get(memberUid);
            if (userBaseInfo != null) {
                teamate.setName(userBaseInfo.getNick());
                teamate.setAvatar(userBaseInfo.getHdLogo());
            }

            if (batchGetUserOnline != null && batchGetUserOnline.userOnlineMap.containsKey(m.getMemberUid())) {
                teamate.setLive(batchGetUserOnline.userOnlineMap.get(m.getMemberUid()));
            }

            teammates.add(teamate);
        }

        currentTeam.setTeamates(teammates);
        currentTeam.setInRoom(channelMap.contains(team.getSid() + "_" + team.getSsid()));

        List<Notify> notifies = getNotifies(attr, now, currentTeamMember, team, currentTeam);
        rsp.setNotifyList(notifies);
        rsp.setCurrentTeam(currentTeam);

        // 队长红点
        if (rsp.isCaptain()) {
            rsp.setRedDot(!StringUtil.isEmpty(actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                    String.format(APPLY_NOTICE, actId), "team_" + currentTeamMember.getTeamId())));
        }
    }


    @NotNull
    private List<Notify> getNotifies(PepcActTeamComponentAttr attr, Date now,@NotNull PepcTeamMember currentTeamMember, PepcTeam team, Team currentTeam) {
        final long teamId = currentTeamMember.getTeamId();

        PepcPhaseInfo SignupPhaseInfo = pepcPhaseInfoService.getSignupPhase(attr.getActId());
        List<Notify> notifies = new ArrayList<>(2);
        log.info("currentTeamMember:{} team:{}", currentTeamMember,team);
        // 报名阶段
        if (team.getState() == PepcConst.PhaseTeamState.INIT) {
            Notify notify = new Notify(String.format("报名截止时间%s，满%s人才能参赛", DateUtil.format(SignupPhaseInfo.getEndTime(), DateUtil.DEFAULT_PATTERN),
                    attr.getTeamMemberMin()), false);
            currentTeam.setState(PepcConst.TeamShowState.SIGNING);
            if (team.getMemberCnt() >= attr.getTeamMemberMax()) {
                currentTeam.setState(PepcConst.TeamShowState.SIGN_SUCC);
            }
            notifies.add(notify);
            return notifies;
        }

        // 赛事取消
        if(team.getState() == PepcConst.PhaseTeamState.FAIL) {
            orderNextGameNotify(attr,notifies);
            currentTeam.setState(PepcConst.TeamShowState.CANCEL);
            return notifies;
        }

        // 人数不足
        if(team.getState() == PepcConst.PhaseTeamState.NO_ENOUGH) {
            orderNextGameNotify(attr,notifies);
            currentTeam.setState(PepcConst.TeamShowState.SIGN_FAIL);
            return notifies;
        }

        if(team.getState() == PepcConst.PhaseTeamState.OVER_LIMIT) {
            orderNextGameNotify(attr,notifies);
            currentTeam.setState(PepcConst.TeamShowState.SIGN_TOOLATE);
            return notifies;
        }

        // 分组成功
        PepcPhaseComponentAttr phaseAttr = pepcPhaseComponent.tryGetUniqueComponentAttr(attr.getActId());
        PepcTeamCurrentGameVo gameVo = pepcGameService.queryTeamCurrentGameStatus(now, phaseAttr, currentTeamMember.getMemberUid());
        log.info("gameVo teamId {} {} {}",teamId,currentTeamMember.getMemberUid(),gameVo);

        if (gameVo == null) {
            log.info("gameVo is null {} {}",currentTeamMember,team);
            currentTeam.setState(PepcConst.TeamShowState.SIGN_SUCC);
            return notifies;
        }

        // 当前没有比赛
        if (gameVo.getState() == PepcConst.TeamCurrentGameState.NOT_BEGIN) {
            currentTeam.setState(PepcConst.TeamShowState.SIGN_SUCC);
            Notify notify = new Notify(String.format("开赛时间%s，记得来参赛哦", DateUtil.getPattenStrFromTime(gameVo.getFirstGameStartTime(), DateUtil.DEFAULT_PATTERN)), false);
            notifies.add(notify);
            addAwardNoticeIfNecessary(attr.getActId(),currentTeamMember,notifies);
            return notifies;
        }

        // 有赛事
        if (gameVo.getState() == PepcConst.TeamCurrentGameState.ENTER_GAME) {
            currentTeam.setState(PepcConst.TeamShowState.GAME_PLAYING);
            Notify notify = new Notify(String.format("开赛时间%s，记得来参赛哦", DateUtil.getPattenStrFromTime(gameVo.getGameStartTime(), DateUtil.DEFAULT_PATTERN)), false);
            notifies.add(notify);
            addAwardNoticeIfNecessary(attr.getActId(),currentTeamMember,notifies);
            return notifies;
        }

        // 有下一轮
        if (gameVo.getState() == PepcConst.TeamCurrentGameState.WAIT_NEXT_GAME) {
            currentTeam.setState(PepcConst.TeamShowState.NEXT_ROUND);
            Notify notify = new Notify(String.format("下一轮开赛时间%s，记得来参赛哦", DateUtil.getPattenStrFromTime(gameVo.getNextGameStartTime(), DateUtil.DEFAULT_PATTERN)), false);
            notifies.add(notify);
            addAwardNoticeIfNecessary(attr.getActId(),currentTeamMember,notifies);
            return notifies;
        }

        // 等待结算
        if (gameVo.getState() == PepcConst.TeamCurrentGameState.SETTLE) {
            currentTeam.setState(PepcConst.TeamShowState.WAIT_RESULT);
            Notify notify = new Notify("等待其他对局完成比赛结算赛果，预计等待30分钟",false);
            notifies.add(notify);
            addAwardNoticeIfNecessary(attr.getActId(),currentTeamMember,notifies);
            return notifies;
        }

        // 结束
        currentTeam.setState(PepcConst.TeamShowState.END);
        orderNextGameNotify(attr,notifies);
        addAwardNoticeIfNecessary(attr.getActId(),currentTeamMember,notifies);
        return notifies;
    }

    private void orderNextGameNotify(PepcActTeamComponentAttr attr, List<Notify> notifies) {
        PepcPhaseComponentAttr phaseAttr = pepcPhaseComponent.getUniqueComponentAttr(attr.getActId());
        if (phaseAttr != null && phaseAttr.getNextActTime() > 0) {
            notifies.add(new Notify(String.format("下一期开启报名时间%s，记得来报名哦", DateUtil.getPattenStrFromTime(phaseAttr.getNextActTime(), DateUtil.DEFAULT_PATTERN)), false));
        }
    }

    private void addAwardNoticeIfNecessary(long actId, @NotNull PepcTeamMember currentTeamMember, List<Notify> notifies) {
        List<PepcAwardRecord> awardRecords = pepcMatchAwardRecordExtMapper.selectUserActAwardList(actId, currentTeamMember.getMemberUid(), PepcConst.AwardState.GRANTED);
        if (CollectionUtils.isNotEmpty(awardRecords)) {
            for (PepcAwardRecord awardRecord : awardRecords) {
                notifies.add(new Notify(String.format("恭喜获得%s奖励！可前往领取", awardRecord.getAwardDesc()), true));
            }
        }
    }

    public Boolean InSignUpTime(long actId, Date now) {
       return pepcPhaseInfoService.InSignUpTime(actId, now);
    }

    @GetMapping("queryLayerInfo")
    public Response<PepcLayerInfo> queryChannelLayerInfo(@RequestParam(name = "actId") long actId,
                                                         @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                         @RequestParam(name = "sid") long sid,
                                                         @RequestParam(name = "ssid") long ssid) {
        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        return queryChannelLayerInfo(attr, uid, sid, ssid, commonService.getNow(actId));
    }

    /**
     * 查询挂件信息
     */
    public Response<PepcLayerInfo> queryChannelLayerInfo(PepcActTeamComponentAttr attr, long uid, long sid, long ssid, Date now) {
        PepcLayerInfo data = new PepcLayerInfo();
        data.setCurrentTime(now);
        if (!actInfoService.inActTime(attr.getActId())) {
            data.setPhaseState(0);
            return Response.success(data);
        }

        PepcTeam team = pepcTeamMapper.selectTeamBySid(attr.getActId(), sid, ssid);
        if (team == null) {
            data.setPhaseState(0);
            return Response.success(data);
        }

        WebdbUserInfo userInfo = webdbUinfoClient.getUserInfo(team.getCreator());
        PepcGameTeamInfo current = new PepcGameTeamInfo();
        current.setTeamId(team.getId());
        current.setUid(team.getCreator());
        if (userInfo != null) {
            current.setNick(team.getTeamName());
            current.setAvatar(WebdbUtils.getLogo(userInfo));
        }


        // 报名阶段
        if (team.getState() == PepcConst.PhaseTeamState.INIT) {
            log.info("queryChannelLayerInfo team state {}",team);
            data.setPhaseState(1);
            data.setNeedApply(team.getNeedApply());
            data.setCurrent(current);
            data.setApplyState(getApplyState(team, uid));
            return Response.success(data);
        } else if (team.getState() == PepcConst.PhaseTeamState.SUCC) {
            // 分组成功 开始赛程
            log.info("queryChannelLayerInfo team state {}",team);
            data.setPhaseState(0);
            PepcChannelGameVo channelVo = pepcGameService.queryChannelCurrentGame(attr.getActId(), sid, ssid);
            if (channelVo != null && channelVo.getGameId() > 0 && now.after(DateUtils.addMinutes(channelVo.getStartTime(),attr.getJumpGameMin())) ) {
                log.info("queryChannelLayerInfo team state {} channelVo {}",team,channelVo);
                data.setPhaseState(2);
                data.setCurrent(current);
                data.setGameId(channelVo.getGameId());
                data.setCurBo(channelVo.getRound());
                data.setRoundName(channelVo.getGroupName());
            }
            return Response.success(data);
        }
        data.setPhaseState(0); // 失败没有比赛
        return Response.success(data);
    }

    // 2 已加入 1 申请中 0 未加入
    private int getApplyState(PepcTeam team, long uid) {
        if (uid == team.getCreator()) {
            return 2;
        }

        PepcTeamMember teamMember = pepcTeamMemberMapper.selectByUniq(team.getActId(), uid);
        if (teamMember != null) {
            if (Objects.equals(teamMember.getTeamId(), team.getId())) {
                return 2;
            }

            return 0;
        }

        if (team.getNeedApply() != 1) {
            return 0;
        }

        List<PepcTeamApply> applies = pepcTeamApplyService.listUnHandleApply(team.getId(), uid);

        if (CollectionUtils.isNotEmpty(applies)) {
            return 1;
        }

        return 0;
    }

    @RequestMapping("/createTeam")
    public Response<?> createTeam(HttpServletRequest request,
                                  @RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx,
                                  @RequestParam(required = false, defaultValue = "") String msg,
                                  @RequestParam("needApply") int needApply,
                                  @RequestParam(name = "verifyCode", required = false) String verifyCode,
                                  @RequestParam(name = "recordId", required = false) String recordId,
                                  @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "创建队伍需要先登录");
        }

        Date now = commonService.getNow(actId);

        Response<?> check = checkParam(uid, actId, cmptInx);
        if (!check.success()) {
            return check;
        }

        if (!InSignUpTime(actId, now)) {
            log.info("out of InSignUpTime,{} {} {}", actId, now, uid);
            return Response.fail(-1, "已到达报名截止时间，无法创建队伍");
        }

        ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);

        //报名
        //绑定
        boolean bind = saiBaoClient.isPassportBindPepc(uid);
        if (!bind) {
            log.info("not isPassportBindPepc,actId:{},uid:{}",actId, uid);
            return Response.fail(-1, "未绑定角色，请前往和平精英巅峰赛活动页绑定");
        }

        PepcTeam nowPepcTeam = pepcActTeamService.selectPepcTeam(uid, actId);
        if (nowPepcTeam != null) {
            log.info("already create team,actId:{},uid:{}",actId, uid);
            return Response.fail(-1, "本阶段已创建队伍");
        }

        PepcTeamMember pepcTeamMember = pepcTeamMemberMapper.selectByUniq(actId, uid);
        if (pepcTeamMember != null) {
            log.info("already join team,actId:{},uid:{}",actId, uid);
            return Response.fail(-1, "本阶段已加入队伍");
        }

        //是否实名
        String idHash = userinfoThriftClient.getIdHash(uid);
        if (StringUtil.isEmpty(idHash)) {
            log.info("getIdHash empty,actId:{},uid:{}",actId, uid);
            return Response.fail(471, "您还没有完成实名认证，请先完成实名认证");
        }
        //风控
        Response<?> riskResponse = checkRisk(uid, actId, cmptInx, request, verifyCode, recordId, verifyToken);
        if (!riskResponse.success()) {
            log.info("riskResponse uid:{} {}", uid,riskResponse);
            return riskResponse;
        }
        return doCreateTeam(actId, msg, needApply, activityInfo.getEndTime(), uid, attr);
    }

    private Response<?> checkRisk(long uid, long actId, long cmptInx, HttpServletRequest request, String verifyCode, String recordId, String verifyToken) {
        String mobileHash = userinfoThriftClient.getMobileHash(uid);
        if (StringUtils.isEmpty(mobileHash)) {
            return Response.fail(433, "未绑定手机号，请绑定后重试");
        }

        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);

        // 判断是否黑名单 todo 是否检查黑名单
        if (attr.getBlacklistComponentIndex() > 0) {
            boolean inBlacklist = whitelistComponent.inWhitelist(actId, attr.getBlacklistComponentIndex(), String.valueOf(uid));
            if (inBlacklist) {
                return Response.fail(466, attr.getBlacklistTips());
            }
        }

        // todo 是否检查黑名单 是否用到白名单
//        if (attr.isUseWhitelist()) {
//            boolean inWhitelist = aovWhitelistService.inWhitelist(actId, uid);
//            if (!inWhitelist) {
//                return Response.fail(467, attr.getWhitelistTips());
//            }
//        }

        try {
            zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);
        } catch (SuperException e) {
            log.info("checkRisk,uid:{},e:{}", uid, e.getMessage(), e);
            JoinGameResult joinGameResult = new JoinGameResult();
            joinGameResult.setRiskRecheck(e.getData());
            return Response.fail(e.getCode(), e.getMessage(), joinGameResult);
        }

        if (attr.getMobileUidLimit() > 0) {
            boolean canMobileSignUp = pepcEnrollmentService.canMobileSignUp(uid, mobileHash, MobileEnrollAppId.AppId.PEPC_APP_ID, attr.getMobileUidLimit(), "参与活动：[" + actId + "]");
            if (canMobileSignUp) {
                return Response.ok();
            }
        }

        return Response.fail(465, "超过单手机号最大参赛账号数(>" + attr.getMobileUidLimit() + ")，无法参赛（手机号以首次参赛绑定的用户为准，禁止注销、换绑）");
    }

    private Response<?> checkParam(long uid, long actId, long cmptInx) {
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "参数错误");
        }
//        if(aovPhase == null){
//            return Response.fail(-1, "活动还没开始");
//        }
        return Response.ok();
    }


    public @NotNull Response<?> doCreateTeam(long actId, String msg, int needApply, Long actEndTime, long uid, PepcActTeamComponentAttr attr) {
        log.info("doCreateTeam actId:{} uid:{} actEndTime:{} msg:{} needApply:{}", actId, uid,
                DateUtil.getPattenStrFromTime(actEndTime,DateUtil.DEFAULT_PATTERN),
                msg,  needApply );
        boolean addUgcWhitelistResult = ugcRoomClient.addUgcWhitelist(uid, actEndTime / 1000, actId + "");
        if (!addUgcWhitelistResult) {
            log.info("{}",addUgcWhitelistResult);
            return Response.fail(-1, "创建队伍失败,请稍后重试");
        }
        // 调用接口创建ugc白名单
        ChannelInfo channelInfo = ugcRoomClient.obtainUgcRoomInfo(uid, actId);
        if (channelInfo == null) {
            return Response.fail(-1, "创建队伍失败,请稍后重试");
        }

        long sid = channelInfo.getSid();
        long ssid = channelInfo.getSsid();
        log.info("doCreateTeam sid:{} ssid:{}",sid,ssid );
        PepcTeam pepcTeam;
        if (!StringUtil.isEmpty(msg) && !msg.equals(attr.getTeamDeclaration())) {
            //送审
            //ugc房间
            pepcTeam = new PepcTeam(sid, ssid, actId, uid, PepcConst.PhaseTeamState.INIT, needApply,
                    attr.getTeamDeclaration(), msg, PepcConst.PepcTeamMsgAuditState.INIT, 1,
                    attr.getTeamMemberMax(), new Date());
        } else {
            pepcTeam = new PepcTeam(sid, ssid, actId, uid, PepcConst.PhaseTeamState.INIT, needApply,
                    attr.getTeamDeclaration(), "", PepcConst.PepcTeamMsgAuditState.PASS, 1,
                    attr.getTeamMemberMax(), new Date());
        }

        // 暂不处理积分逻辑
//        long score = aovRankComponent.getPrePhaseScore(actId, aovPhase.getPrevPhaseId(), uid);
//       aovPhaseTeam.setTeamScore(score);

        String nick = commonService.getNickName(uid, false);
        pepcTeam.setTeamName(nick + "的战队");
        pepcTeam.setTeamNameAuditState(PepcConst.TeamNameStatus.PASS);

        long teamId = 0;
        try {
           teamId = pepcActTeamService.createTeam(attr, pepcTeam);
        } catch (SuperException e) {
            log.warn("fail,uid:{},msg:{},e:{}", uid, msg, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (DuplicateKeyException e) {
            log.warn("add game phase team fail uid duplicate");
            return Response.success(-1, "创建队伍失败,请稍后重试");
        }
        log.info("uid:{} pepcTeam:{} teamId:{}", uid, pepcTeam, teamId);
        return Response.success(null);
    }

    @RequestMapping("/changeTeamName")
    public Response<?> changeTeamName(@RequestParam("actId") long actId,
                                      @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                      @RequestParam(name = "teamId") long teamId,
                                      @RequestParam(name = "teamName") String teamName) {
       long uid = getLoginYYUid();

        Response<?> check = checkParam(uid, actId, cmptInx);
        if (!check.success()) {
            return check;
        }

        if (StringUtil.isBlank(teamName)) {
            return Response.fail(402, "队伍名称不能为空");
        }

        PepcTeam team = pepcActTeamService.selectById(teamId);
        if (team == null || team.getCreator() != uid) {
            return Response.fail(403, "您不是队长，无权执行此操作");
        }

        if (PepcConst.TeamNameStatus.INIT == Convert.toInt(team.getTeamNameAuditState(), 0)) {
            return Response.fail(404, "已存在待审核中队伍名称");
        }

        Date now = commonService.getNow(actId);
        if(!InSignUpTime(actId,now)){
            return Response.fail(-1, "报名结束无法修改队名");
        }

        try {
            boolean res = pepcActTeamService.changeTeamName(getComponentAttr(actId, cmptInx), teamId, uid, teamName);
            if (!res) {
                throw new RuntimeException("changeTeamName failed");
            }
        } catch (SuperException e) {
            log.warn("changeTeamName fail,uid:{},teamName:{},e:{}", uid, teamName, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("changeTeamName fail,uid:{},teamName:{},e:{}", uid, teamName, e.getMessage(), e);
            return Response.fail(500, "网络超时，请重试");
        }
        log.info("changeTeamName teamId:{},uid:{},teamName:{}", teamId, uid, teamName);
        return Response.ok();
    }


    @RequestMapping("/joinTeam")
    public Response<?> joinTeam(HttpServletRequest request,
                                @RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx,
                                @RequestParam(required = false, defaultValue = "") String msg,
                                @RequestParam("teamId") long teamId,
                                @RequestParam(name = "verifyCode", required = false) String verifyCode,
                                @RequestParam(name = "recordId", required = false) String recordId,
                                @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "加入队伍需要先登录");
        }

        log.info("joinTeam request with uid:{} teamId:{}", uid, teamId);
        Date now = commonService.getNow(actId);

        Response<?> check = checkParam(uid, actId, cmptInx);
        if (!check.success()) {
            return check;
        }

        if (!InSignUpTime(actId, now)) {
            return Response.fail(-1, "已到达报名截止时间无法加入队伍");
        }

        //报名
        //绑定
        boolean bind = saiBaoClient.isPassportBindPepc(uid);
        if (!bind) {
            return Response.fail(-1, "未绑定角色，请前往和平精英巅峰赛活动页绑定");
        }
        Response<?> riskResponse = checkRisk(uid, actId, cmptInx, request, verifyCode, recordId, verifyToken);
        if (!riskResponse.success()) {
            return riskResponse;
        }
        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        return doJoinTeam(actId, cmptInx, msg, teamId, uid, attr);
    }

    public @NotNull Response<?> doJoinTeam(long actId, long cmptInx, String msg, long teamId, long uid, PepcActTeamComponentAttr attr) {
        PepcTeam pepcTeam = pepcActTeamService.selectById(teamId);
        if (pepcTeam == null) {
            return Response.fail(-1, "加入队伍失败,队伍可能已被队长解散");
        }

        if (uid == pepcTeam.getCreator()) {
            return Response.ok();
        }

        if (pepcTeam.getMemberCnt() >= attr.getTeamMemberMax()) {
            return Response.fail(-1, "当前队伍已满人,加入失败");
        }

        PepcTeamMember teamMember = pepcTeamMemberMapper.selectByUniq(actId, uid);
        if (teamMember != null) {
            return Response.fail(-1, "请勿重复加入队伍");
        }

        long score = 0;
        if (pepcTeam.getNeedApply() == 0) {
            //直接加入
            PepcTeamMember pepcTeamMember = new PepcTeamMember(teamId, actId, uid,
                    PepcConst.PepcTeamRole.MATE + "", 1, new Date());
            //暂不处理
//            long score = aovRankComponent.getPrePhaseScore(actId, aovPhase.getPrevPhaseId(), uid);
            try {
                pepcActTeamService.addTeammate(teamId, pepcTeamMember, score);
                // todo 推送
                var pushAttr = pepcPushComponent.tryGetUniqueComponentAttr(actId);
                pepcPushComponent.sendRoomNotice(actId, UUID.randomUUID().toString(), pushAttr.getJoinTeamRoomNotice(), uid, pepcTeam.getSid(), pepcTeam.getSsid());
                pepcPushComponent.sendJoinTeamIm(pushAttr, pepcTeam.getCreator());
            } catch (Exception e) {
                return Response.fail(-1, "加入队伍失败,请稍后重试");
            }
        } else {
            List<PepcTeamApply> pepcTeamApply = pepcTeamApplyService.listUnHandleApply(teamId, uid);
            if (!CollectionUtils.isEmpty(pepcTeamApply)) {
                return Response.fail(-1, "请勿重复申请");
            }

            actRedisDao.hset(redisConfigManager.getGroupCode(actId),
                    String.format(APPLY_NOTICE, actId), "team_" + teamId, uid + "", 7 * DateUtil.DAY_SEC);
            PepcTeamApply apply = new PepcTeamApply(actId, teamId,
                    uid, msg, PepcConst.PepcTeamApplyState.INIT, new Date());
            pepcTeamApplyService.addApply(apply);
            // push
            pepcPushComponent.sendNewApplyNotice(actId, pepcTeam.getCreator());
            var pushAttr = pepcPushComponent.tryGetUniqueComponentAttr(actId);
            pepcPushComponent.sendRoomNotice(actId, UUID.randomUUID().toString(), pushAttr.getApplyTeamRoomNotice(), uid, pepcTeam.getSid(), pepcTeam.getSsid());
        }

        return Response.success(null);
    }

    @RequestMapping("/applyList")
    public Response<ApplyListRsp> applyList(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx) {
        long uid = getLoginYYUid();
        Date now = commonService.getNow(actId);
        // todo 判断 报名时间
//        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, null);
        Response check = checkParam(uid, actId, cmptInx);
        if (!check.success()) {
            return check;
        }

        Set<Long> uids = new HashSet<>();
        uids.add(uid);
//        long phaseId = aovPhase.getId();
        PepcTeam pepcTeam = pepcActTeamService.selectPepcTeam(uid, actId);
        //队长
        ApplyListRsp applyListRsp = new ApplyListRsp();
        List<ApplyInfo> applyInfos = new ArrayList<>();
        if (pepcTeam != null) {
            applyListRsp.setCaptain(true);
            long teamId = pepcTeam.getId();
            List<PepcTeamApply> teamApplies = pepcTeamApplyService.listApplyByOwner(teamId);
            for (PepcTeamApply apply : teamApplies) {
                uids.add(apply.getUid());
            }
            Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(uids), false);
            String teamName = userInfos.get(uid).getNick();
            for (PepcTeamApply apply : teamApplies) {
                ApplyInfo applyInfo = new ApplyInfo();
                applyInfo.setApplyId(apply.getId());
                applyInfo.setTeamName(teamName);
                applyInfo.setName(userInfos.get(apply.getUid()).getNick());
                applyInfo.setAvatar(userInfos.get(apply.getUid()).getHdLogo());
                applyInfo.setTp(apply.getState());
                applyInfo.setUid(apply.getUid());
                applyInfo.setApplyTime(DateUtil.format(apply.getCreateTime(), DateUtil.DEFAULT_PATTERN));
                applyInfo.setMsg(apply.getMsg());
                applyInfos.add(applyInfo);
            }

            // todo 删除红点
            actRedisDao.hdel(redisConfigManager.getGroupCode(actId), String.format(APPLY_NOTICE, actId), "team_" + teamId);
            log.info("clear captain red dot uid:{}", uid);
        } else {
            applyListRsp.setCaptain(false);
            List<PepcTeamApply> pepcTeamApplies = pepcTeamApplyService.listApply(actId, uid);
            if (CollectionUtils.isEmpty(pepcTeamApplies)) {
                applyListRsp.setApplyInfos(applyInfos);
                return Response.success(applyListRsp);
            }
            Set<Long> teamIds = new HashSet<>();
            for (PepcTeamApply pepcTeamApply : pepcTeamApplies) {
                teamIds.add(pepcTeamApply.getTeamId());
            }
            Map<Long, Long> teamIdCreatorMap = new HashMap<>();
            List<PepcTeam> teamList = pepcActTeamService.selectByIds(List.copyOf(teamIds));
            for (PepcTeam phaseTeam : teamList) {
                uids.add(phaseTeam.getCreator());
                teamIdCreatorMap.put(phaseTeam.getId(), phaseTeam.getCreator());
            }
            Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(uids), false);
            for (PepcTeamApply apply : pepcTeamApplies) {
                Long creator = teamIdCreatorMap.get(apply.getTeamId());
                if (creator == null) {
                    continue;
                }

                ApplyInfo applyInfo = new ApplyInfo();
                applyInfo.setApplyId(apply.getId());
                String teamName = userInfos.get(creator).getNick();
                applyInfo.setTeamName(teamName);
                applyInfo.setName(userInfos.get(apply.getUid()).getNick());
                applyInfo.setAvatar(userInfos.get(apply.getUid()).getHdLogo());
                applyInfo.setTp(apply.getState());
                applyInfo.setMsg(apply.getMsg());
                applyInfo.setApplyTime(DateUtil.format(apply.getCreateTime(), DateUtil.DEFAULT_PATTERN));
                applyInfos.add(applyInfo);
            }

            // todo 删除红点
            actRedisDao.hdel(redisConfigManager.getGroupCode(actId), String.format(APPLY_NOTICE, actId), String.valueOf(uid));
            log.info("clear captain red dot uid:{}", uid);
        }
        applyListRsp.setApplyInfos(applyInfos);
        return Response.success(applyListRsp);
    }

    @RequestMapping("/changeDeclaration")
    public Response<?> changeDeclaration(@RequestParam("actId") long actId,
                                         @RequestParam("cmptInx") long cmptInx,
                                         @RequestParam("msg") String msg) {
        long uid = getLoginYYUid();
        Date now = commonService.getNow(actId);
        // todo 检查阶段
//        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, null);
//        Response<?> check = checkParam(uid, actId, cmptInx, aovPhase);
//        if(!check.success()) {
//            return check;
//        }

        PepcTeam pepcTeam = pepcActTeamService.selectPepcTeam(uid, actId);
        if (pepcTeam == null) {
            return Response.fail(-1, "参数错误");
        }

        if (pepcTeam.getAuditState().equals(PepcConst.PepcTeamMsgAuditState.INIT)) {
            return Response.fail(-1, "之前的文案审核中, 无法修改");
        }

        //判断字符是不是一样的
        //送审
        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        try {
            pepcActTeamService.updateDeclaration(attr, msg, PepcConst.PepcTeamMsgAuditState.INIT,
                    pepcTeam.getId(), uid);
        }  catch (SuperException e) {
            log.warn("fail,uid:{},msg:{},e:{}", uid, msg, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        }catch (Exception e) {
            log.error("changeDeclaration {}",e.getMessage());
            return Response.fail(-1, "修改文案失败，请稍后重试");
        }

        return Response.ok();
    }

    /**
     * 队长审批入队申请
     *
     * @param actId
     * @param cmptInx
     * @param applyId
     * @param apply
     * @return
     */
    @RequestMapping("/apply")
    public Response<?> apply(@RequestParam("actId") long actId,
                             @RequestParam("cmptInx") long cmptInx,
                             @RequestParam("applyId") long applyId,
                             @RequestParam("apply") int apply) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "请先完成登录");
        }

        Date now = commonService.getNow(actId);

        Response<?> check = checkParam(uid, actId, cmptInx);
        if (!check.success()) {
            return check;
        }

        if (!InSignUpTime(actId, now)) {
            return Response.fail(-1, "本期报名已结束，无法审批");
        }

        PepcTeamApply pepcTeamApply = pepcTeamApplyService.selectById(applyId);
        if (pepcTeamApply == null) {
            return Response.fail(-1, "参数错误,请稍后重试");
        }

        long teamId = pepcTeamApply.getTeamId();
        PepcTeam pepcTeam = pepcActTeamService.selectById(teamId);
        if (pepcTeam == null || uid != pepcTeam.getCreator()) {
            return Response.fail(-1, "您不是本队伍队长，无法执行审批");
        }

        if (apply == 0) {
            pepcTeamApplyService.updateState(applyId, PepcConst.PepcTeamApplyState.REJECT);
            //红点
            actRedisDao.hset(redisConfigManager.getGroupCode(actId),
                    String.format(APPLY_NOTICE, actId), String.valueOf(pepcTeamApply.getUid()),
                    pepcTeam.getCreator() + "", 7 * DateUtil.DAY_SEC);
        } else {
            PepcTeamMember pepcTeamMember = pepcTeamMemberMapper.selectByUniq(actId, pepcTeamApply.getUid());
            if (pepcTeamMember != null) {
                return Response.fail(-1, "该用户已加入其他队伍");
            }

//            long score = aovRankComponent.getPrePhaseScore(actId, aovPhase.getPrevPhaseId(), pepcTeamApply.getUid());
            try {
                pepcTeamApplyService.apply(pepcTeamApply);
            } catch (BusinessException e) {
                log.error("message:{}", e.getMessage(), e);
                return Response.fail(e.getCode(), e.getMessage());
            } catch (Exception e) {
                log.error("message:{}", e);
                return Response.fail(-1, "审批失败,请稍后重试");
            }
            pepcPushComponent.sendApprovedNotice(actId, pepcTeamApply.getUid());
            //todo 红点
            actRedisDao.hset(redisConfigManager.getGroupCode(actId),
                    String.format(APPLY_NOTICE, actId), String.valueOf(pepcTeamApply.getUid()), pepcTeam.getCreator() + "", 7 * DateUtil.DAY_SEC);
        }

        return Response.ok();
    }

    @RequestMapping("/applySwitch")
    public Response<?> turnApplySwitch(@RequestParam("actId") long actId,
                                       @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                       @RequestParam(name = "teamId") long teamId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        PepcTeam team = pepcActTeamService.selectById(teamId);
        if (team == null || team.getCreator() != uid) {
            return Response.fail(403, "您不是队长，无权执行此操作");
        }

        int needApply = team.getNeedApply();
        log.info("turnApplySwitch current:{}", needApply);
        int rs = pepcActTeamService.turnNeedApply(teamId);
        log.info("turnApplySwitch turn with uid:{} needApply:{} rs:{}", uid, needApply, rs);
        return Response.ok();
    }

    @RequestMapping("/kickOutMember")
    public Response<?> kickOutMember(@RequestParam("actId") long actId,
                                     @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                     @RequestParam(name = "teamId") long teamId,
                                     @RequestParam(name = "memberUid") long memberUid) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "踢出队伍需要登录");
        }

        if (uid == memberUid) {
            return Response.fail(400, "不能踢出自己");
        }

        PepcTeam team = pepcActTeamService.selectById(teamId);
        if (team == null || team.getCreator() != uid) {
            return Response.fail(403, "您不是队长，无权执行此操作");
        }

        PepcPhaseInfo info = pepcPhaseInfoService.getSignupPhase(actId);
        Date now = commonService.getNow(actId);
        if (info == null || now.after(DateUtils.addMinutes(info.getEndTime(), -30))) {
            return Response.fail(461, "报名结束前30分钟内不允许踢出队员");
        }

        PepcTeamMember member = pepcTeamMemberMapper.selectByUniq(actId, memberUid);
        if (member == null || member.getTeamId() != teamId) {
            return Response.fail(462, "该队员已经不在您的队伍中");
        }

        long score = 0;
//        long score = aovRankComponent.getPrePhaseScore(actId, phase.getPrevPhaseId(), uid);
        boolean removed = pepcActTeamService.removeTeammate(member);
        if (removed) {
            pepcPushComponent.sendKickedOutNotice(actId, memberUid);
        }
        return Response.ok();
    }

    @RequestMapping("/quitTeam")
    public Response<?> quitTeam(@RequestParam("actId") long actId,
                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                @RequestParam(name = "teamId") long teamId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "需要登录");
        }

        Response<?> check = checkParam(uid, actId, cmptInx);
        if (!check.success()) {
            return check;
        }

        PepcTeam team = pepcActTeamService.selectById(teamId);
        if (team == null) {
            return Response.fail(400, "队伍不存在");
        }

        // 检查报名时间
        PepcPhaseInfo info = pepcPhaseInfoService.getSignupPhase(actId);
        Date now = commonService.getNow(actId);
        if (info == null || now.after(DateUtils.addMinutes(info.getEndTime(), -30))) {
            return Response.fail(461, "报名结束前30分钟内不允许离开队伍");
        }

        PepcTeamMember member = pepcTeamMemberMapper.selectByUniq(actId, uid);
        if (member == null || member.getTeamId() != teamId) {
            return Response.fail(462, "您已不在队伍中");
        }

        // 解散队伍
        long score = 0;
        if (uid == team.getCreator()) {
            List<Long> memberUids = pepcActTeamService.disbandTeam(team);
            if (CollectionUtils.isNotEmpty(memberUids)) {
                pepcPushComponent.sendDisbandTeamNotice(actId, memberUids);

                // 把红点去掉
                actRedisDao.hdel(redisConfigManager.getGroupCode(actId), String.format(APPLY_NOTICE, actId), String.valueOf(uid));
            }
        } else { // 离开队伍
//            long score = aovRankComponent.getPrePhaseScore(actId, phase.getPrevPhaseId(), uid);
            boolean quit = pepcActTeamService.quitTeam(member);
            if (quit) {
                pepcPushComponent.sendQuitTeamNotice(actId, team.getCreator());
            }
        }

        return Response.ok();
    }


    @RequestMapping("/commonCallback")
    public MmsReportCmdRspVo commonCallback(HttpServletRequest request,
                                            @RequestParam("actId") long actId,
                                            String appKey,
                                            String serial,
                                            String cmd,
                                            String reason,
                                            String msg,
                                            @RequestParam(value = "extParUrlEncoder", required = false, defaultValue = "") String extParUrlEncoder,
                                            String sign,
                                            String md5sign,
                                            String status,
                                            String reasonCode) {
        String groups = request.getParameter("groups");
        log.info("commonCallback req, actId:{} appKey:{}, serial:{}, cmd:{}, reason:{}, msg:{}, extParUrlEncoder:{}, sign:{}, md5sign:{}, status:{}, reasonCode:{}, groups:{}",
                actId, appKey, serial, cmd, reason, msg, extParUrlEncoder, sign, md5sign, status, reasonCode, groups);
        long actIdPara = calActId(extParUrlEncoder);
        long teamId = calTeamId(extParUrlEncoder);
        PepcActTeamComponentAttr attr = getUniqueComponentAttr(actIdPara);
        if (attr == null) {
            log.info("wrong appid");
            return MmsReportCmdRspVo.SUCCESS;
        }
        if (!attr.getMmsAppid().equals(appKey)) {
            log.info("wrong appKey");
            return MmsReportCmdRspVo.SUCCESS;
        }
        if (teamId == 0) {
            log.info("wrong teamId");
            return MmsReportCmdRspVo.SUCCESS;
        }
        log.info("commonCallback teamId:{}, actIdPara:{} ",
                teamId, actIdPara);
        MmsReportCmdRspVo mmsReportCmdRspVo = new MmsReportCmdRspVo();
        try {
            String infmd5 = String.format("%s%s%s%s%s%s%s", appKey, attr.getMmsAppSecret(), serial, cmd, reason, msg, extParUrlEncoder);
            String myMd5Sign = MD5SHAUtil.getMD5(infmd5);
            boolean MD5Ok = (String.valueOf(myMd5Sign).equals(md5sign));
            if (!MD5Ok) {
                log.error("commonCallback MD5Ok check error");
                return mmsReportCmdRspVo.error(-2, "");
            }
        } catch (RuntimeException e) {
            log.error("{}", e);
            return mmsReportCmdRspVo.error(-2, "");
        }

        //区分队伍宣言和队伍审批回调
        int mmsType = calMmsType(extParUrlEncoder);
        MmsCmdResult result = MmsCmdResult.fromValue(status);
        PepcTeam pepcTeam = pepcActTeamService.selectById(teamId);
        if (pepcTeam == null) {
            log.warn("commonCallback process error mmsReportCmdReq:{}", teamId);
            return MmsReportCmdRspVo.SUCCESS;
        }
        if (MmsConst.MmsType.PEPC_PHASE_TEAM_DESC == mmsType) {
            if (MmsCmdResult.VIOLATION.equals(result)) {
                int rs = pepcActTeamService.clearAuditDeclaration(PepcConst.PepcTeamMsgAuditState.REJECT, teamId, PepcConst.PepcTeamMsgAuditState.INIT);
                pepcPushComponent.sendRejectedNotice(actIdPara, pepcTeam.getCreator());
            } else if (MmsCmdResult.ACCESS.equals(result)) {
                int rs = pepcActTeamService.acceptDeclaration(PepcConst.PepcTeamMsgAuditState.PASS, teamId, PepcConst.PepcTeamMsgAuditState.INIT);
            }
        } else if (MmsConst.MmsType.PEPC_PHASE_TEAM_NAME == mmsType) {
//            long actIdPara = calActId(extParUrlEncoder);
//            var attr = getUniqueComponentAttr(actIdPara);
            var pushAttr = pepcPushComponent.getUniqueComponentAttr(actIdPara);
            pepcActTeamService.doTeamNameAudit(pushAttr, pepcTeam.getCreator(), teamId, result);
        }
        return MmsReportCmdRspVo.SUCCESS;
    }

    private int calMmsType(String para) {
        if (!StringUtil.isJson(para)) {
            return MmsConst.MmsType.AOV_PHASE_TEAM_DESC;
        }
        JSONObject extObject = JSON.parseObject(para);
        return Convert.toInt(extObject.getInteger("mmsType"), MmsConst.MmsType.PEPC_PHASE_TEAM_DESC);
    }

    private long calActId(String para) {
        if (StringUtil.isBlank(para) || !StringUtil.isJson(para)) {
            return 0;
        }
        JSONObject extObject = JSON.parseObject(para);
        return Convert.toInt(extObject.getInteger("actId"), 0);
    }

    private long calTeamId(String para) {
        if (StringUtil.isBlank(para) || !StringUtil.isJson(para)) {
            return 0;
        }
        JSONObject extObject = JSON.parseObject(para);
        return Convert.toInt(extObject.getInteger("teamId"), 0);
    }

    @RequestMapping("/mockBatchAddTeam")
    public Response mockAddTeam( @RequestParam("actId") long actId, @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                 @RequestParam(name = "captains") String captains) {

        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1,"组件不存在");
        }
        List<Long> uids = Arrays.stream(captains.split(",")).map(Long::parseLong).collect(Collectors.toList());
        ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
        long actEndTime = activityInfo.getEndTime();
        for (int i = 0; i < uids.size(); i++) {
            long uid = uids.get(i);
            if (uid == 0){
                log.info("uid=0");
            }
            doCreateTeam(actId,"",0,actEndTime,uids.get(i),attr);
        }

        return Response.ok();
    }

    @RequestMapping("/mockBatchAddTeamate")
    public Response mockBatchAddTeamate( @RequestParam("actId") long actId, @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                 @RequestParam(name = "jumpTeams") String jumpTeams) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(-1, "act not grey");
        }
        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1,"组件不存在");
        }
        Set<Long> jumpTeamList = new HashSet<>();
        if(!StringUtil.isBlank(jumpTeams)){
            jumpTeamList = Arrays.stream(jumpTeams.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        }
        List<PepcTeam> ret = pepcActTeamService.listTeam(actId,0,1000);
        for (int i = 0; i < ret.size(); i++) {
            PepcTeam t = ret.get(i);
            if (t.getMemberCnt()  == attr.getTeamMemberMax() ||jumpTeamList.contains(t.getId()) ) {
                log.info("jump team:{}",t);
                continue;
            }
            log.info("before team:{}",t);
            for (int j = t.getMemberCnt(); j < attr.getTeamMemberMax(); j++) {
                long addRet = TryToJoinTeam(attr,t.getId(),i);
                if (addRet == -2) {
                    break;
                }
            }
            PepcTeam newTeam = pepcActTeamService.selectById(t.getId());
            log.info("after team:{}",t);
        }

        return Response.ok();
    }


    @RequestMapping("/mockBatchAddTeamNoUgc")
    public Response mockAddTeamNoUgc( @RequestParam("actId") long actId, @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                 @RequestParam(name = "length") long length) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(-1, "act not grey");
        }
        PepcActTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1,"组件不存在");
        }
        long uid = 50020000L;
        for (long i = 0; i < length; ) {
            uid++;
            long teamId = 0;
            PepcTeam pepcTeam = new PepcTeam(1538657792L, 2830358845L, actId, uid, PepcConst.PhaseTeamState.INIT, 0,
                    attr.getTeamDeclaration(), "", PepcConst.PepcTeamMsgAuditState.PASS, 1,
                    attr.getTeamMemberMax(), new Date());
            pepcTeam.setTeamName("模拟战队");
            pepcTeam.setTeamNameAuditState(PepcConst.TeamNameStatus.PASS);
            try {
                teamId = pepcActTeamService.createTeam(attr, pepcTeam);
            } catch (DuplicateKeyException e) {
                log.warn("add game phase team fail uid duplicate");
//                continue;
            }
            if (teamId > 0 ){
                i++;
            }
        }

        return Response.ok();
    }

    private long TryToJoinTeam(PepcActTeamComponentAttr attr, long teamId,int j){
        for (int i = 0; ; i++) {
            long uid =  50010000L + teamId*100 + j+i;
            Response resp = mockJoinTeam(attr,teamId,uid);
            if (resp.getResult() == -2 ||  resp.getResult() == 0) { // 成功或者是 队伍没有了 或者队伍已经满了 就返回ok
                return resp.getResult();
            }
            // 换个uid 试试
            log.info("TryToJoinTeam uid:{},teamId:{} resp:{}", uid, teamId, resp);
        }
    }

    private Response mockJoinTeam(PepcActTeamComponentAttr attr, long teamId, long uid){
        PepcTeam pepcTeam = pepcActTeamService.selectById(teamId);
        if (pepcTeam == null) {
            return Response.fail(-2, "加入队伍失败,队伍可能已被队长解散");
        }

        if (uid == pepcTeam.getCreator()) {
            return Response.ok();
        }

        if (pepcTeam.getMemberCnt() >= attr.getTeamMemberMax()) {
            return Response.fail(-2, "队伍人数已满");
        }

        PepcTeamMember teamMember = pepcTeamMemberMapper.selectByUniq(attr.getActId(), uid);
        if (teamMember != null) {
            return Response.fail(-1, "请勿重复加入队伍");
        }

            //直接加入
        PepcTeamMember pepcTeamMember = new PepcTeamMember(teamId, attr.getActId(), uid,
                PepcConst.PepcTeamRole.MATE + "", 1, new Date());

        try {
            pepcActTeamService.addTeammate(teamId, pepcTeamMember, 0);
        }catch (Exception e) {
            return Response.fail(-1, "加入队伍失败,请稍后重试");
        }
        return Response.ok();
    }


    @Data
    public static class TeamListRsp {
        /**
         * 队伍列表，当前用户还没加入队伍时，返回队伍未满人的队伍列表
         */
        private List<Team> teamList;

    }


    @Data
    public static class Team {
        private Long teamId;
        private String name;
        private String teamNameAudit;
        private String declaration;
        private List<Teamate> teamates;
        private Long sid;
        private Long ssid;
        private Integer state;
        private Integer tp;
        private String declarationAudit;
        private boolean inRoom;
        private Integer teamNameAuditState;
        private Integer memberCnt;
        private Integer memberLimit;
    }

    @Data
    public static class Teamate {
        private boolean live; // 是否在app
        private String avatar;
        private String name;
        private Long uid;
    }

    @Data
    public static class MyTeamRsp {
        private boolean canJoin;

        /**
         * 是否展示红点<br/>
         * 当前用户已在队伍中且是队长：有待查看的入队申请 红点
         * 当前用户未在队伍中：有待查看被审批完成的入队申请 红点
         */
        private boolean redDot;

        /**
         * 当前用户已在队伍中且是队长
         */
        private boolean captain;

        private String teamLeadGroupQr;

        private String teamLeadGroupId;

        private String teammatesGroupQr;

        private String declarationDefault;

        private String wxNum;

        /**
         * 当前队伍信息
         */
        private Team currentTeam;

        /**
         * 当前队伍信息下面的感动文案
         */
        private List<Notify> notifyList;

        /**
         * 创建队伍，拉起半窗的时候展示当前用户信息
         */
        private MyInfo myInfo;

    }

    @Data
    public static class MyInfo {
        private String avatar;

        private String name;
    }

    @Data
    public static class ApplyInfo {
        private long uid;
        private long applyId;
        private String teamName;
        private String name;
        private String avatar;
        private String applyTime;
        private Integer tp;
        private String msg;
    }

    @Data
    public static class ApplyListRsp {
        private boolean captain;

        private List<ApplyInfo> applyInfos;
    }

    @Data
    public static class HomePopupVo {
        protected String popupLink;

        public HomePopupVo() {
        }

        public HomePopupVo(String popupLink) {
            this.popupLink = popupLink;
        }
    }

    @Data
    public static class Notify {
        private String text;

        private boolean award;

        public Notify() {
        }

        public Notify(String text, boolean award) {
            this.text = text;
            this.award = award;
        }
    }

    @Data
    public class PepcLayerInfo {

        // 0-当前频道无队伍，1-报名阶段，2-比赛阶段，可进入游戏 【正常只有1和2，其他状态不展示挂件】
        protected int phaseState;

        protected long phaseId;

        // 进入比赛的必要参数，比赛阶段phaseState == 2 才有
        protected long gameId;

        /**
         * 0 未入队，未申请，or 被拒 1 已申请，2 已入队，3 报名成功，等待比赛开始， 4-报名失败
         */
        protected int applyState;

        // 是否上锁
        protected int needApply;

        protected Date currentTime;

        protected Date startTime;// 比赛开始时间，比赛阶段phaseState == 2 才有

        protected String roundName; // 比赛名称

        protected int curBo;

        private PepcGameTeamInfo current;
    }

    @Data
    public class PepcGameTeamInfo {

        protected long teamId;

        protected int nodeIndex;

        protected long uid;

        protected String nick;

        protected String avatar;

//        protected int score;
    }

}
