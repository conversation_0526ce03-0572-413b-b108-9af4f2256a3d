package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjHelper;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankingScoreBannerComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 按榜单中的分值做特效横幅广播组件
 * <p>
 * 1. 广播协议
 * A. uri = 100008
 * B. bannerId 配置的,区分不同类型
 * C. jsonData对应的格式： {"nickName":"","logo":"","value":"","svgaUrl":"","asid":"","ext":"","busiId":""}
 *
 * <AUTHOR>
 */
@UseRedisStore(NeedChange = true)
@RequestMapping("/2028")
@RestController
@Component
public class RankingScoreBannerComponent extends BaseActComponent<RankingScoreBannerComponentAttr> {


    @Override
    public Long getComponentId() {
        return ComponentId.RANKING_SCORE_BANNER;
    }

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;

    /**
     * 测试环境提供http接口 广播测试
     */
    @RequestMapping("/testBroBanner")
    public Response<String> testBroBanner(long actId, long index, long score, String member, String subChannel) {
        if (SysEvHelper.isDeploy()) {
            if (!commonService.isGrey(actId)) {
                return Response.success("测试接口线上不执行");
            }
            if (!hdztRankingThriftClient.checkWhiteList(actId, RoleType.HALL, subChannel)) {
                return Response.success("测试成员不在白名单");
            }
        }

        RankingScoreChanged event = new RankingScoreChanged();
        event.setRankScore(score);
        RankingScoreBannerComponentAttr attr = getComponentAttr(actId, index);

        invokeBro(event, attr, member, attr.getDefaultScoreBannerConfig().get(score).get(0));

        return Response.success("调用成功:" + DateUtil.format(new Date()));
    }

    /**
     * 响应榜单变化事件，按所达分值区间发放奖励， 本函数做了防重检查，每个分值区间只能执行一次
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, RankingScoreBannerComponentAttr attr) {
        invokeOnRankingScoreChanged(event, attr, true);
    }

    public void invokeOnRankingScoreChanged(RankingScoreChanged event, RankingScoreBannerComponentAttr attr, boolean checkRankId) {
        // 先检查是否要处理的
        long rankId = event.getRankId(), phaseId = event.getPhaseId();
        if (!attr.isMyDuty(rankId, phaseId) && checkRankId) {
            return;
        }

        long actId = event.getActId();
        // 提取奖励接收者
        int receiverInx = attr.getReceiverInx();
        String receiver = event.getMember().split("\\|")[receiverInx];

        long roleId = attr.getRoleType();
        if (attr.getRoleType() != RoleType.USER.getValue()) {
            EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(actId, 0L, attr.getRoleType(), receiver);
            if (enrollmentInfo == null) {
                log.warn("onRankingScoreChanged not found,memberId:{},seq:{}", receiver, event.getSeq());
                return;
            }
            roleId = enrollmentInfo.getDestRoleId();
        }


        Map<Long, List<BroadcastConfig>> broConfig = getBroadcastConfig(roleId, attr);
        if (MapUtils.isEmpty(broConfig)) {
            log.warn("broConfig not found,roleId:{},memberId:{},seq:{}", roleId, receiver, event.getSeq());
            return;
        }

        long newLevelScore = HdzjHelper.findLevelScore(event.getRankScore(), Lists.newArrayList(broConfig.keySet()));
        if (newLevelScore <= 0) {
            return;
        }

        // 若设置操作返回的老值大于或等于新值，则设置失败，直接返回
        String subName = HdzjHelper.getRankingScoreChangedSubKey(event, false);
        String hashKey = makeKey(attr, subName + ":CurrLevelScore");
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        long oldLevelScore = actRedisDao.hSetGrownReturnOld(groupCode, hashKey, String.valueOf(receiver), newLevelScore);
        if (oldLevelScore >= newLevelScore) {
            return;
        }

        // 对在 (oldLevelScore, newLevelScore] 这个区间的所有阶梯奖励进行发放操作
        List<BroadcastConfig> configs = getSubScoreBroConfig(broConfig, oldLevelScore, newLevelScore, attr.isSelectHighestLevel());
        for (BroadcastConfig config : configs) {
            log.info("invokeBro begin,actId:{},receiver:{},score:{},selectHighestLevel:{},config:{}", attr.getActId(), receiver, event.getRankScore(), attr.isSelectHighestLevel(), JSON.toJSONString(config));
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    invokeBro(event, attr, receiver, config);
                }
            });
        }
    }


    private void invokeBro(RankingScoreChanged event, RankingScoreBannerComponentAttr attr, String memberId, BroadcastConfig config) {
        //延迟广播
        if (config.getDelayMillSeconds() > 0) {
            log.info("invokeBro delay,actId:{},memberId:{},score:{},config:{}", attr.getActId(), memberId, event.getRankScore(), JSON.toJSONString(config));
            SysEvHelper.waiting(config.getDelayMillSeconds());
        }

        final int broType = config.getBroType();
        String bannerUrl;
        //优先用任务配置,其次用默认
        if (StringUtil.isNotBlank(config.getBannerUrl())) {
            bannerUrl = config.getBannerUrl();
        } else {
            bannerUrl = attr.getDefaultBannerUrl();
        }


        long actId = attr.getActId();
        long score = event.getRankScore();
        //默认设置为交友
        long busiId = BusiId.MAKE_FRIEND.getValue();
        String asId = StringUtils.EMPTY;
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, attr.getRoleType(), memberId);
        if (enrollmentInfo != null) {
            busiId = enrollmentInfo.getRoleBusiId();
            asId = String.valueOf(enrollmentInfo.getSignAsid());
        } else {
            if (attr.getRoleType() != RoleType.USER.getValue()) {
                log.error("bro can not get enrollmentInfo,actId:{},memberId:{}", actId, memberId);
            }
        }

        MemberInfo memberInfo = memberInfoService.getMemberInfo(busiId, RoleType.findByValue(attr.getRoleType()), memberId);
        String nick = memberInfo.getName();

        Map<String, String> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("memberId", memberId);
        extMap.put("nickName", nick);
        extMap.put("logo", StringUtil.isNotBlank(memberInfo.getHdLogo()) ? memberInfo.getHdLogo() : memberInfo.getLogo());
        extMap.put("value", String.valueOf(score));
        extMap.put("svgaUrl", bannerUrl);
        extMap.put("asid", asId);
        extMap.put("ext", config.getExt());
        extMap.put("busiId", String.valueOf(busiId));
        extMap.put("desc", config.getDesc());
        extMap.put("roleType", String.valueOf(attr.getRoleType()));

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(config.getBannerId()).setUserNick(nick)
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        if (broType == BroadcastConfig.BroType.ACT_BUSI.code) {
            broadCastHelpService.broadcast(actId, BusiId.GAME_ECOLOGY, broType, 0, 0, bannerBroMsg);
        } else if (broType == BroadcastConfig.BroType.BUSI_TEMPLATE.code) {
            if (busiId == BusiId.MAKE_FRIEND.getValue() && config.getExcludeDanmaku() == 1) {
                broadCastHelpService.broadcastExcludeDanmakuChannel(bannerBroMsg);
            } else {
                broadCastHelpService.broadcast(actId, BusiId.findByValue((int) busiId), broType, 0, 0, bannerBroMsg);
            }
        } else if (broType == BroadcastType.FAMILY) {
            long familyId = getFamilyId(attr.getRoleType(), memberId);
            broadCastHelpService.broadcast("", actId, BusiId.findByValue((int) busiId), broType, familyId, 0, 0, bannerBroMsg);
        } else {
            ChannelInfoVo channel = getChannelInfo(attr.getRoleType(), broType, memberId);
            if (channel == null) {
                log.error("log ranking banner bro error,user not in channel memberId:{} config:{}", memberId, JSON.toJSONString(config));
                return;
            }
            broadCastHelpService.broadcast(actId, BusiId.findByValue((int) busiId), broType, channel.getSid(), channel.getSsid(), bannerBroMsg);
        }
        log.info("log ranking banner bro done memberId:{} task:{}", memberId, JSON.toJSONString(config));

        bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue(Convert.toInt(busiId)), System.currentTimeMillis(), memberId
                , RoleType.findByValue(attr.getRoleType()), score, BigDataScoreType.RANK_SCORE_BANNER, config.getDesc() + "|" + config.getBannerId() + "|" + config.getBroType());
    }

    private long getFamilyId(int roleType, String memberId) {
        if (RoleType.ROOM.getValue() == roleType) {
            RoomInfo roomInfo = commonService.getRoomInfoByRoomId(Convert.toInt(memberId));
            if (roomInfo == null) {
                throw new RuntimeException("not found roomInfo,roomId:" + memberId);
            }
            return roomInfo.getFamilyId();
        } else if (RoleType.FAMILY.getValue() == roleType) {
            return Convert.toLong(memberId);
        } else if (RoleType.ANCHOR.getValue() == roleType) {
            long familyId = turnoverFamilyThriftClient.queryContractFamilyId(Convert.toLong(memberId));
            if (familyId == 0) {
                throw new RuntimeException("not found contractFamilyId ,anchorId:" + memberId);
            }
            return familyId;
        }

        throw new RuntimeException("not support roleType:" + roleType);
    }

    private ChannelInfoVo getChannelInfo(int roleType, int broType, String memberId) {
        ChannelInfoVo vo = new ChannelInfoVo();
        if (RoleType.ANCHOR.getValue() == roleType || RoleType.USER.getValue() == roleType) {
            UserCurrentChannel channel = commonService.getUserCurrentChannel(Convert.toLong(memberId));
            if (channel == null) {
                log.error("log ranking banner bro error,user not in channel uid:{}", memberId);
                return null;
            }
            vo.setSid(channel.getTopsid());
            vo.setSsid(channel.getSubsid());
            return vo;
        } else if (RoleType.HALL.getValue() == roleType) {
            String[] array = memberId.split("_");
            vo.setSid(Convert.toLong(array[0]));
            vo.setSsid(Convert.toLong(array[1]));
            return vo;

        } else if (RoleType.GUILD.getValue() == roleType) {
            vo.setSid(Convert.toLong(memberId));
            return vo;
        } else if (RoleType.ROOM.getValue() == roleType) {
            RoomInfo roomInfo = commonService.getRoomInfoByRoomId(Convert.toInt(memberId));
            if (roomInfo == null) {
                log.error("log ranking banner bro error, room info cannot be found rooId:{}", memberId);
                return null;
            }

            vo.setSid(roomInfo.sid);
            vo.setSsid(roomInfo.ssid);

            return vo;
        }

        return null;
    }


    private Map<Long, List<BroadcastConfig>> getBroadcastConfig(long roleId, RankingScoreBannerComponentAttr attr) {
        for (String roleIds : attr.getRoleScoreBannerConfig().keySet()) {
            if ((roleIds + ",").contains(roleId + ",")) {
                return attr.getRoleScoreBannerConfig().get(roleIds);
            }
        }

        return attr.getDefaultScoreBannerConfig();
    }

    /**
     * 提阶梯分值在 (fromScore, toScore] 直接的所有奖励配置
     */
    public List<BroadcastConfig> getSubScoreBroConfig(Map<Long, List<BroadcastConfig>> bannerConfigMap, long fromScore, long toScore, boolean selectHighestLevel) {
        List<BroadcastConfig> configs = Lists.newArrayList();
        Set<Long> scoreSet = bannerConfigMap.keySet();
        //只取最高等级
        if (selectHighestLevel) {
            scoreSet = scoreSet.stream().sorted(Comparator.reverseOrder()).collect(Collectors.toCollection(LinkedHashSet::new));
        }
        for (Long levelScore : scoreSet) {
            if (levelScore > fromScore && levelScore <= toScore) {
                configs.addAll(bannerConfigMap.get(levelScore));
                if (selectHighestLevel) {
                    return configs;
                }
            }
        }
        return configs;
    }

}
