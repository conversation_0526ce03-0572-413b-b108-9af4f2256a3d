package com.yy.gameecology.hdzj.element.component.attr.bean;

import lombok.Data;

/**
 * 夏日探险记录
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class SummerAdventureRecord {
    
    /**
     * 活动ID
     */
    private long actId;
    
    /**
     * 组件使用索引
     */
    private long cmptUseInx;
    
    /**
     * 用户UID
     */
    private long uid;
    
    /**
     * 主持UID
     */
    private long anchorUid;
    
    /**
     * CP成员标识 (格式: uid|anchorUid)
     */
    private String cpMember;
    
    /**
     * 探险序列号
     */
    private String adventureSeq;
    
    /**
     * 使用的骰子数量
     */
    private int diceUsed;
    
    /**
     * 骰子点数序列 (逗号分隔)
     */
    private String dicePoints;
    
    /**
     * 起始位置
     */
    private int startPosition;
    
    /**
     * 结束位置
     */
    private int endPosition;
    
    /**
     * 经过的格子位置 (逗号分隔)
     */
    private String passedGrids;
    
    /**
     * 获得的奖励信息 (JSON格式)
     */
    private String rewardsJson;
    
    /**
     * 探险状态 (1:进行中 2:已完成 3:已跳过)
     */
    private int status;
    
    /**
     * 创建时间
     */
    private long createTime;
    
    /**
     * 完成时间
     */
    private long finishTime;
}
