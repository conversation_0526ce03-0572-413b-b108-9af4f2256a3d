package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.WatchwordStakeComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@UseRedisStore
@RestController
@RequestMapping("/5073")
public class WatchwordStakeComponent extends BaseActComponent<WatchwordStakeComponentAttr> {

    private static final String WATCHWORD_KEY = "watchword:%s:%d";

    private static final String WATCHWORD_MEMBER_KEY = "watchword_member:%s";

    private static final String HIT_WATCHWORD_KEY = "hit_watchword";

    private static final String USER_AWARD_RECORD_KEY = "user_award_record:%d";

    private static final String AWARD_RECORD_KEY = "award_record";

    private static final String AWARD_SEQ = "watchword_prod:%d:%d:%s";

    private static final String CARVE_UP_AWARD_SEQ = "c_w_p:%d:%d:%s:%d";

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Override
    public Long getComponentId() {
        return ComponentId.WATCHWORD_STAKE;
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent event, WatchwordStakeComponentAttr attr) {
        Map<String, WatchwordStakeComponentAttr.PresentConfig> configMap = attr.getPresentConfigMap();
        if (!configMap.containsKey(event.getGiftId())) {
            return;
        }

        WatchwordStakeComponentAttr.PresentConfig config = configMap.get(event.getGiftId());
        if (event.getGiftNum() < config.getTriggerCount()) {
            return;
        }

        final long actId = attr.getActId();
        final long member = event.getSendUid();
        final String groupCode = getRedisGroupCode(actId);
        String seqKey = makeKey(attr, event.getSeq());
        boolean set = actRedisDao.setNX(groupCode, seqKey, DateUtil.getNowYyyyMMddHHmmss(), 600);
        if (!set) {
            return;
        }

        Date eventTime = event.getEventTime();
        if (!SysEvHelper.isDeploy() || commonService.isGrey(actId)) {
            eventTime = commonService.getNow(actId);
        }
        int watchwordCount = (int) (event.getGiftNum() / config.getTriggerCount() * config.getWatchwordCount());
        String dateStr = DateFormatUtils.format(eventTime, DateUtil.PATTERN_TYPE2);
        List<WatchwordStakeComponentAttr.Watchword> watchwords = new ArrayList<>(watchwordCount);
        String watchwordMemberKey = makeKey(attr, String.format(WATCHWORD_MEMBER_KEY, dateStr));
        actRedisDao.sadd(groupCode, watchwordMemberKey, String.valueOf(member));
        for (int i = 0; i < watchwordCount; i++) {
            WatchwordStakeComponentAttr.Watchword watchword = getRandomWatchword(attr.getWatchwords());
            String watchwordKey = makeKey(attr, String.format(WATCHWORD_KEY, dateStr, watchword.getWatchwordId()));
            String seq = event.getSeq() + i;
            long rs = actRedisDao.hIncrByKeyWithSeq(groupCode, seq, watchwordKey, String.valueOf(member), 1, 3600);
            log.info("incr watchword with {} rs: {}", watchword.getText(), rs);

            watchwords.add(watchword);
        }

        //气泡提示
        JSONObject noticeJson = new JSONObject();
        noticeJson.put("watchwords", watchwords);
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(PBCommonNoticeType.WATCHWORD_OBTAIN)
                .setNoticeValue(noticeJson.toString(SerializerFeature.DisableCircularReferenceDetect));

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        RetryTool.withRetryCheck(actId, event.getSeq(), () -> svcSDKService.unicastUid(member, msg));

    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onDailyPhaseTimeEnd(PhaseTimeEnd event, WatchwordStakeComponentAttr attr) {
        if (event.getRankId() != attr.getDayRankId()) {
            return;
        }

        assert event.getTimeKey() == 1;

        final long actId = attr.getActId();
        Date date;
        try {
            date = DateUtils.parseDate(event.getEndTime(), DateUtil.DEFAULT_PATTERN);
        } catch (Exception e) {
            date = DateUtils.addDays(commonService.getNow(actId), -1);
        }

        final String groupCode = getRedisGroupCode(actId);
        final String dateStr = DateFormatUtils.format(date, DateUtil.PATTERN_TYPE2);
        final WatchwordStakeComponentAttr.Watchword hitWatchword;
        String hitKey = makeKey(attr, HIT_WATCHWORD_KEY);
        String value = actRedisDao.hget(groupCode, hitKey, dateStr);
        if (StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            hitWatchword = JSON.parseObject(value, WatchwordStakeComponentAttr.Watchword.class);
        } else {
            WatchwordStakeComponentAttr.Watchword word = getRandomWatchword(attr.getWatchwords());
            boolean set = actRedisDao.hsetnx(groupCode, hitKey, dateStr, JSON.toJSONString(word));
            if (!set) {
                value = actRedisDao.hget(groupCode, hitKey, dateStr);
                word = JSON.parseObject(value, WatchwordStakeComponentAttr.Watchword.class);
            }

            hitWatchword = word;
        }

        String watchwordKey = makeKey(attr, String.format(WATCHWORD_KEY, dateStr, hitWatchword.getWatchwordId()));
        Map<String, String> entries = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().entries(watchwordKey);

        Map<Long, Integer> watchwordCountMap = entries.entrySet().stream().filter(entry -> Integer.parseInt(entry.getValue()) > 0)
                .collect(Collectors.toMap(entry -> Long.parseLong(entry.getKey()), entry -> Integer.parseInt(entry.getValue())));

        if (MapUtils.isEmpty(watchwordCountMap)) {
            log.warn("onDailyPhaseTimeEnd no award to grant date:{}", dateStr);
            return;
        }

        int total = watchwordCountMap.values().stream().reduce(0, Integer::sum);

        // 抽取锦鲤神豪
        List<Long> uids = Lists.newArrayList(watchwordCountMap.keySet());
        Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(uids, false);
        Map<Long, String> userNickMap = new HashMap<>(userInfoMap.size());
        for (Map.Entry<Long, UserBaseInfo> entry : userInfoMap.entrySet()) {
            if (entry.getValue() != null) {
                userNickMap.put(entry.getKey(), entry.getValue().getNick());
            }
        }
        Collections.shuffle(uids);
        long luckyUid = uids.get(0);

        int luckyAwardCount = attr.getLuckAwardCount();
        AwardRecord awardRecord = new AwardRecord();
        awardRecord.setDateStr(dateStr);
        awardRecord.setWatchword(hitWatchword);
        awardRecord.setTotalWatchwordCount(total);
        awardRecord.setAvgAwardCount(attr.getAwardCount() / total);
        awardRecord.setLuckyUid(luckyUid);
        awardRecord.setLuckyNick(userNickMap.get(luckyUid));
        awardRecord.setLuckyAwardCount(luckyAwardCount);
        String recordKey = makeKey(attr, AWARD_RECORD_KEY);
        boolean set = actRedisDao.hsetnx(groupCode, recordKey, dateStr, JSON.toJSONString(awardRecord));
        if (!set) {
            return;
        }

        // 瓜分
        for (Map.Entry<Long, Integer> entry : watchwordCountMap.entrySet()) {
            grantAward(event.getSeq(), attr, awardRecord, userNickMap, entry.getKey(), entry.getValue());
        }

        String seq = String.format(AWARD_SEQ, actId, attr.getCmptUseInx(), dateStr);
        hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), luckyUid, attr.getAwardTaskId(), luckyAwardCount, attr.getAwardPackageId(), seq, Collections.emptyMap());

        //针对有口令，但是没有幸运口令的用户发单播
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            Set<Long> sendUids = new HashSet<>(watchwordCountMap.keySet());
            for (WatchwordStakeComponentAttr.Watchword watchword : attr.getWatchwords()) {
                if (watchword.getWatchwordId() == hitWatchword.getWatchwordId()) {
                    continue;
                }

                String wwKey = makeKey(attr, String.format(WATCHWORD_KEY, dateStr, watchword.getWatchwordId()));
                Set<String> uidStrList = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().keys(wwKey);
                uidStrList.forEach(uidStr -> {
                    long uid = Long.parseLong(uidStr);
                    if (sendUids.add(uid)) {
                        String json = getWatchwordUserRecord(awardRecord, uid);
                        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                                .setActId(attr.getActId())
                                .setNoticeType(PBCommonNoticeType.WATCHWORD_AWARD)
                                .setNoticeValue(json);

                        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                                .setCommonNoticeResponse(panel).build();

                        RetryTool.withRetryCheck(actId, event.getSeq() + StringUtil.VERTICAL_BAR + uid, () -> svcSDKService.unicastUid(uid, msg));
                    }
                });
            }

            log.info("send not lucky watchword uid size:{}", sendUids.size() - watchwordCountMap.size());
        });
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, WatchwordStakeComponentAttr attr) {
        if (SysEvHelper.isDev()) {
            log.info("onUserEnterTemplate with:{}", event);
        }

        final long uid = event.getUid();
        Date now = commonService.getNow(attr.getActId());
        Date yesterday = DateUtils.addDays(now, -1);
        String dateStr = DateFormatUtils.format(yesterday, DateUtil.PATTERN_TYPE2);
        String groupCode = getRedisGroupCode(attr.getActId());
        String watchwordMemberKey = makeKey(attr, String.format(WATCHWORD_MEMBER_KEY, dateStr));
        boolean exist = actRedisDao.sIsMember(groupCode, watchwordMemberKey, String.valueOf(uid));
        if (!exist) {
            return;
        }

        String userRecordKey = makeKey(attr, String.format(USER_AWARD_RECORD_KEY, uid));
        String value = actRedisDao.hget(groupCode, userRecordKey, dateStr);
        if (!StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            String recordKey = makeKey(attr, AWARD_RECORD_KEY);
            String recordJson = actRedisDao.hget(groupCode, recordKey, dateStr);
            if (!StringUtils.startsWith(recordJson, StringUtil.OPEN_BRACE)) {
                return;
            }

            AwardRecord awardRecord = JSON.parseObject(recordJson, AwardRecord.class);

            value = getWatchwordUserRecord(awardRecord, uid);
        }

        // 发单播
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.WATCHWORD_AWARD)
                .setNoticeValue(value);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.unicastUid(uid, msg);
    }

    private WatchwordStakeComponentAttr.Watchword getRandomWatchword(List<WatchwordStakeComponentAttr.Watchword> watchwords) {
        return watchwords.get(RandomUtils.nextInt(0, watchwords.size()));
    }

    public void grantAward(String eventSeq, WatchwordStakeComponentAttr attr, AwardRecord awardRecord, Map<Long, String> userNickMap, long uid, int watchwordCount) {
        String groupCode = getRedisGroupCode(attr.getActId());
        int awardCount = watchwordCount * awardRecord.avgAwardCount;
        // 保存中奖记录
        UserAwardRecord userRecord = new UserAwardRecord();
        userRecord.setUid(uid);
        userRecord.setNick(userNickMap.get(uid));
        userRecord.setDateStr(awardRecord.dateStr);
        userRecord.setWatchword(awardRecord.watchword);
        userRecord.setWatchwordCount(watchwordCount);
        userRecord.setTotalWatchwordCount(awardRecord.totalWatchwordCount);
        userRecord.setAvgAwardCount(awardRecord.avgAwardCount);
        userRecord.setLuckyUid(awardRecord.luckyUid);
        userRecord.setLuckyNick(awardRecord.luckyNick);
        userRecord.setLuckyAwardCount(awardRecord.luckyAwardCount);
        userRecord.setAwardCount(awardCount);
        String userRecordJson = JSON.toJSONString(userRecord);

        String recordKey = makeKey(attr, String.format(USER_AWARD_RECORD_KEY, uid));
        boolean set = actRedisDao.hsetnx(groupCode, recordKey, awardRecord.dateStr, userRecordJson);
        if (!set) {
            return;
        }

        // 发奖
        String seq = String.format(CARVE_UP_AWARD_SEQ, attr.getActId(), attr.getCmptUseInx(), awardRecord.dateStr, uid);
        hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), uid, attr.getAwardTaskId(), awardCount, attr.getAwardPackageId(), seq, Collections.emptyMap());

        // 发单播
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.WATCHWORD_AWARD)
                .setNoticeValue(userRecordJson);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        RetryTool.withRetryCheck(attr.getActId(), eventSeq + StringUtil.VERTICAL_BAR + uid, () -> {
            svcSDKService.unicastUid(uid, msg);
            log.info("grantAward finish with uid:{}, watchword count:{}", uid, watchwordCount);
        });
    }

    public String getWatchwordUserRecord(AwardRecord awardRecord, long uid) {
        UserAwardRecord userAwardRecord = new UserAwardRecord();
        userAwardRecord.setWatchword(awardRecord.watchword);
        userAwardRecord.setTotalWatchwordCount(awardRecord.totalWatchwordCount);
        userAwardRecord.setAvgAwardCount(awardRecord.avgAwardCount);
        userAwardRecord.setLuckyUid(awardRecord.luckyUid);
        userAwardRecord.setLuckyNick(awardRecord.luckyNick);
        userAwardRecord.setLuckyAwardCount(awardRecord.luckyAwardCount);
        userAwardRecord.setUid(uid);
        userAwardRecord.setDateStr(awardRecord.dateStr);
        userAwardRecord.setWatchwordCount(0);
        userAwardRecord.setAwardCount(0);

        return JSON.toJSONString(userAwardRecord);
    }

    @GetMapping("watchwords")
    public Response<JSONObject> queryMyWatchwords(@RequestParam("actId") long actId,
                                                  @RequestParam("cmptIndex") long cmptIndex) {
        WatchwordStakeComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "login is need!");
        }

        final List<WatchwordStakeComponentAttr.Watchword> watchwords = attr.getWatchwords();
        Date now = commonService.getNow(actId);
        final String groupCode = getRedisGroupCode(actId);
        String dateStr = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2);
        final byte[] field = String.valueOf(uid).getBytes(StandardCharsets.UTF_8);
        List<Object> counts = actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisCallback<Object>) connection -> {
            for (WatchwordStakeComponentAttr.Watchword watchword : watchwords) {
                String key = makeKey(attr, String.format(WATCHWORD_KEY, dateStr, watchword.getWatchwordId()));
                connection.hGet(key.getBytes(StandardCharsets.UTF_8), field);
            }
            return null;
        });

        List<MyWatchword> myWatchwords = new ArrayList<>(watchwords.size());
        for (int i = 0; i < watchwords.size(); i++) {
            WatchwordStakeComponentAttr.Watchword watchword = watchwords.get(i);
            int count = Convert.toInt(counts.get(i), 0);

            MyWatchword myWatchword = new MyWatchword(watchword, count);
            myWatchwords.add(myWatchword);
        }

        UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, false);
        JSONObject data = new JSONObject(5);
        data.put("nick", userBaseInfo.getNick());
        data.put("avatar", userBaseInfo.getHdLogo());
        data.put("myWatchwords", myWatchwords);
        data.put("transferStartTime", attr.getStartTime());
        data.put("transferEndTime", attr.getEndTime());
        return Response.success(data);
    }

    @RequestMapping("watchword/effect")
    public Response<Long> watchwordEffect(@RequestParam("actId") long actId,
                                            @RequestParam("cmptIndex") long cmptIndex,
                                            @RequestParam("dateStr") String dateStr) {

        WatchwordStakeComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "login is need!");
        }

        String watchwordMemberKey = makeKey(attr, String.format(WATCHWORD_MEMBER_KEY, dateStr));
        Long rs = actRedisDao.sRem(getRedisGroupCode(actId), watchwordMemberKey, String.valueOf(uid));

        return Response.success(rs);
    }

    @RequestMapping("watchword/transfer")
    public Response<Integer> transferWatchword(@RequestParam("actId") long actId,
                                         @RequestParam("cmptIndex") long cmptIndex,
                                         @RequestParam("fromId") int fromId,
                                         @RequestParam("toId") int toId,
                                         @RequestParam("transferCount") int transferCount) {
        WatchwordStakeComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "login is need!");
        }

        if (transferCount <= 0) {
            return Response.fail(400, "口令卡转换不成功，请检查填写是否有误!");
        }

        Set<Integer> ids = attr.getWatchwords().stream().map(WatchwordStakeComponentAttr.Watchword::getWatchwordId).collect(Collectors.toSet());
        if (!ids.contains(fromId) || !ids.contains(toId)) {
            return Response.fail(400, "口令卡转换不成功，请检查填写是否有误!");
        }

        if (fromId == toId) {
            return Response.fail(463, "相同口令卡不可转换哦，请重新选择口令卡!");
        }

        Date now = commonService.getNow(actId);
        LocalTime curTime = DateUtil.toLocalDateTime(now).toLocalTime();
        if (curTime.isBefore(attr.getStartTime()) || curTime.isAfter(attr.getEndTime())) {
            return Response.fail(461, "当前时间不可转换!");
        }

        final String groupCode = getRedisGroupCode(actId);
        String dateStr = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2);
        String fromKey = makeKey(attr, String.format(WATCHWORD_KEY, dateStr, fromId));
        String value = actRedisDao.hget(groupCode, fromKey, String.valueOf(uid));
        int fromCount = Convert.toInt(value, 0);
        if (fromCount < transferCount) {
            log.info("transferWatchword error,uid:{},transferCount:{},fromCount:{}", uid, transferCount, fromCount);
            return Response.fail(462, "当前拥有的口令卡不足");
        }

        long after = actRedisDao.hIncrByKey(groupCode, fromKey, String.valueOf(uid), -transferCount);
        if (after < 0) {
            long rt = actRedisDao.hIncrByKey(groupCode, fromKey, String.valueOf(uid), transferCount);
            log.warn("not enough watchword to transfer uid:{}, fromId:{}, after:{}, rt:{}", uid, fromId, after, rt);
            return Response.fail(462, "当前拥有的口令卡不足!");
        }

        String toKey = makeKey(attr, String.format(WATCHWORD_KEY, dateStr, toId));
        long incr = actRedisDao.hIncrByKey(groupCode, toKey, String.valueOf(uid), transferCount);
        log.warn("transfer finish with uid:{}, fromId:{}, from result:{}, toId:{}, to result:{}, transfer count:{}", uid, fromId, after, toId, incr, transferCount);
        return Response.success(transferCount);
    }

    @RequestMapping("set/watchword")
    public Response<String> setWatchword(@RequestParam("actId") long actId,
                                    @RequestParam("cmptIndex") long cmptIndex,
                                    @RequestParam("watchwordId") int watchwordId) {
        WatchwordStakeComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need!");
        }

        if (attr.getSetUids() == null || !attr.getSetUids().contains(uid)) {
            return Response.fail(403, "you have no privilege to set watchword!");
        }

        log.info("setWatchword with actId:{}, cmptIndex:{}, watchwordId:{}", actId, cmptIndex, watchwordId);

        WatchwordStakeComponentAttr.Watchword watchword = attr.getWatchwords().stream()
                .filter(w -> w.getWatchwordId() == watchwordId).findFirst().orElse(null);

        if (watchword == null) {
            return Response.fail(400, "watchwordId invalid!");
        }

        Date date = commonService.getNow(actId);
        String dateStr = DateFormatUtils.format(date, DateUtil.PATTERN_TYPE2);
        String hitKey = makeKey(attr, HIT_WATCHWORD_KEY);
        actRedisDao.hset(getRedisGroupCode(actId), hitKey, dateStr, JSON.toJSONString(watchword));

        return Response.success(dateStr);
    }

    @GetMapping("/user/award/record")
    public Response<List<UserAwardRecord>> queryMyAwardRecord(@RequestParam("actId") long actId,
                                          @RequestParam("cmptIndex") long cmptIndex) {
        WatchwordStakeComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "login is need!");
        }

        String recordKey = makeKey(attr, String.format(USER_AWARD_RECORD_KEY, uid));
        Map<String, String> entries = actRedisDao.getRedisTemplate(getRedisGroupCode(actId)).<String, String>opsForHash().entries(recordKey);
        if (MapUtils.isEmpty(entries)) {
            return Response.success(Collections.emptyList());
        }

        List<UserAwardRecord> records = entries.values().stream().map(value -> JSON.parseObject(value, UserAwardRecord.class))
                .sorted(Comparator.comparing(UserAwardRecord::getDateStr).reversed()).toList();
        return Response.success(records);
    }

    @GetMapping("/award/record")
    public Response<List<AwardRecord>> queryAwardRecord(@RequestParam("actId") long actId,
                                        @RequestParam("cmptIndex") long cmptIndex) {
        WatchwordStakeComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        String recordKey = makeKey(attr, AWARD_RECORD_KEY);
        Map<String, String> entries = actRedisDao.getRedisTemplate(getRedisGroupCode(actId)).<String, String>opsForHash().entries(recordKey);
        if (MapUtils.isEmpty(entries)) {
            return Response.success(Collections.emptyList());
        }

        List<AwardRecord> records = entries.values().stream().map(value -> JSON.parseObject(value, AwardRecord.class))
                .sorted(Comparator.comparing(AwardRecord::getDateStr).reversed()).toList();
        return Response.success(records);
    }

    @GetMapping("watchword/statistics")
    public Response<?> queryWatchwordStatistics(@RequestParam("actId") long actId,
                                                @RequestParam("cmptIndex") long cmptIndex,
                                                @RequestParam("dateStr") String dateStr) {
        WatchwordStakeComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        String groupCode = getRedisGroupCode(actId);
        List<WatchwordStakeComponentAttr.Watchword> watchwords = attr.getWatchwords();
        Set<String> watchwordUids = new HashSet<>(3000);
        int watchwordTotalCount = 0, hitUidCount = 0, hitWatchwordCount = 0;
        WatchwordStakeComponentAttr.Watchword hitWatchword = null;
        String hitKey = makeKey(attr, HIT_WATCHWORD_KEY);
        String value = actRedisDao.hget(groupCode, hitKey, dateStr);
        if (StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            hitWatchword = JSON.parseObject(value, WatchwordStakeComponentAttr.Watchword.class);
        }

        List<Map<String, Object>> watchwordStats = new ArrayList<>(watchwords.size());
        for (WatchwordStakeComponentAttr.Watchword watchword : watchwords) {
            String watchwordKey = makeKey(attr, String.format(WATCHWORD_KEY, dateStr, watchword.getWatchwordId()));
            Map<String, String> entries = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().entries(watchwordKey);
            Map<String, Integer> map = entries.entrySet().stream().filter(entry -> !StringUtils.equals(entry.getValue(), StringUtil.ZERO))
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> Integer.parseInt(entry.getValue())));
            int watchwordCount = map.values().stream().reduce(0, Integer::sum);
            watchwordUids.addAll(map.keySet());
            Map<String, Object> watchwordStat = new HashMap<>(10);
            watchwordStat.put("watchwordId", watchword.getWatchwordId());
            watchwordStat.put("watchwordText", watchword.getText());
            watchwordStat.put("uidCount", map.size());
            watchwordStat.put("watchwordCount", watchwordCount);
            watchwordTotalCount += watchwordCount;

            watchwordStats.add(watchwordStat);

            if (hitWatchword != null && hitWatchword.getWatchwordId() == watchword.getWatchwordId()) {
                hitUidCount = map.size();
                hitWatchwordCount = watchwordCount;
            }
        }

        //查询当日奖励记录
        AwardRecord record = null;
        String recordKey = makeKey(attr, AWARD_RECORD_KEY);
        String recordJson = actRedisDao.hget(groupCode, recordKey, dateStr);
        if (StringUtils.startsWith(recordJson, StringUtil.OPEN_BRACE)) {
            record = JSON.parseObject(recordJson, AwardRecord.class);
        }

        Map<String, Object> data = new HashMap<>(10);
        data.put("date", dateStr);
        data.put("record", record);
        data.put("watchwordStats", watchwordStats);
        data.put("watchwordTotalCount", watchwordTotalCount);
        data.put("watchwordTotalUidCount", watchwordUids.size());
        data.put("hitUidCount", hitUidCount);
        data.put("hitWatchwordCount", hitWatchwordCount);

        return Response.success(data);
    }

    @GetMapping("watchword/awardList")
    public Response<?> queryWatchwordUserList(@RequestParam("actId") long actId,
                                              @RequestParam("cmptIndex") long cmptIndex,
                                              @RequestParam("dateStr") String dateStr) {
        WatchwordStakeComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        String groupCode = getRedisGroupCode(actId);
        //查询当日奖励记录
        String recordKey = makeKey(attr, AWARD_RECORD_KEY);
        String recordJson = actRedisDao.hget(groupCode, recordKey, dateStr);
        if (!StringUtils.startsWith(recordJson, StringUtil.OPEN_BRACE)) {
            return Response.fail(400, "no award record!");
        }
        AwardRecord record = JSON.parseObject(recordJson, AwardRecord.class);
        String watchwordKey = makeKey(attr, String.format(WATCHWORD_KEY, dateStr, record.getWatchword().getWatchwordId()));
        Map<String, String> entries = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().entries(watchwordKey);
        Map<Long, Integer> map = entries.entrySet().stream().filter(entry -> !StringUtils.equals(entry.getValue(), StringUtil.ZERO))
                .collect(Collectors.toMap(entry -> Long.parseLong(entry.getKey()), entry -> Integer.parseInt(entry.getValue())));

        List<Object> result = actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisCallback<Object>) connection -> {
            for (long uid : map.keySet()) {
                String userRecordKey = makeKey(attr, String.format(USER_AWARD_RECORD_KEY, uid));
                connection.hGet(userRecordKey.getBytes(StandardCharsets.UTF_8), dateStr.getBytes(StandardCharsets.UTF_8));
            }
            return null;
        });

        List<UserAwardRecord> records = result.stream().map(String::valueOf)
                .filter(str -> StringUtils.startsWith(str, StringUtil.OPEN_BRACE))
                .map(str -> JSON.parseObject(str, UserAwardRecord.class))
                .toList();

        return Response.success(records);
    }

    @Data
    public static class MyWatchword extends WatchwordStakeComponentAttr.Watchword {
        protected int count;

        public MyWatchword() {
        }

        public MyWatchword(WatchwordStakeComponentAttr.Watchword watchword, int count) {
            this.watchwordId = watchword.getWatchwordId();
            this.text = watchword.getText();
            this.count = count;
        }
    }

    @Data
    public static class UserAwardRecord {
        protected long uid;

        protected String nick;

        protected String dateStr;

        protected WatchwordStakeComponentAttr.Watchword watchword;

        protected int watchwordCount;

        protected int totalWatchwordCount;

        protected int awardCount;

        protected int avgAwardCount;

        protected long luckyUid;

        protected String luckyNick;

        protected int luckyAwardCount;
    }

    @Data
    public static class AwardRecord {
        protected String dateStr;

        protected WatchwordStakeComponentAttr.Watchword watchword;

        protected int totalWatchwordCount;

        protected int avgAwardCount;

        protected long luckyUid;

        protected String luckyNick;

        protected int luckyAwardCount;
    }
}
