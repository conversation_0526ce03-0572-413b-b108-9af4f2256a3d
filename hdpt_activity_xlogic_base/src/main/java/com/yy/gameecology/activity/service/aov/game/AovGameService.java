package com.yy.gameecology.activity.service.aov.game;

import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.*;
import com.yy.gameecology.common.db.model.gameecology.aov.*;
import com.yy.gameecology.common.utils.WebdbUtils;
import com.yy.gameecology.hdzj.bean.aov.AovGameInfo;
import com.yy.gameecology.hdzj.bean.aov.AovGameTeamInfo;
import com.yy.gameecology.hdzj.bean.aov.AovLayerInfo;
import com.yy.gameecology.hdzj.element.component.xmodule.aov.AovGameComponent;
import com.yy.gameecology.hdzj.element.component.attr.AovGameComponentAttr;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.saibao.JoinRoomVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AovGameService {

    public static final Set<Integer> GAME_STATE = Set.of(AovConst.GameState.CREATED, AovConst.GameState.SAIBAO_READY);

    @Resource
    private AovPhaseMapper aovPhaseMapper;

    @Resource
    private AovPhaseTeamMapper aovPhaseTeamMapper;

    @Resource
    private AovPhaseTeamMemberMapper aovPhaseTeamMemberMapper;

    @Resource
    private AovPhaseTeamApplyMapper aovPhaseTeamApplyMapper;

    @Resource
    private AovGameExtMapper aovGameExtMapper;

    @Resource
    private AovGameTeamExtMapper aovGameTeamExtMapper;

    @Resource
    private AovGameMemberExtMapper aovGameMemberExtMapper;

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    /**
     * 查询对阵树
     * @param phaseId
     * @param teamId
     * @param now
     * @return
     */
    public List<AovGameInfo> queryMatches(long phaseId, long teamId, Date now) {
        List<Long> gameIds = aovGameTeamExtMapper.selectGameTeamsByTeamId(phaseId, teamId, null);
        if (CollectionUtils.isEmpty(gameIds)) {
            return Collections.emptyList();
        }

        List<AovGame> games = aovGameExtMapper.batchSelectGames(gameIds);
        Map<Long, AovGame> gameMap = games.stream().collect(Collectors.toMap(AovGame::getId, Function.identity()));

        List<AovGameTeam> gameTeams = aovGameTeamExtMapper.batchSelectGameTeamsByGameId(gameIds);

        Assert.isTrue(gameIds.size() * 2 == gameTeams.size(), "team size unexpected");

        Set<Long> uids = new HashSet<>(gameTeams.size());
        Map<Long, MutablePair<AovGameTeam, AovGameTeam>> gameTeamMap = new HashMap<>(games.size());
        for (AovGameTeam gameTeam : gameTeams) {
            gameTeamMap.compute(gameTeam.getGameId(), (k, v) -> {
                if (v == null) {
                    v = new MutablePair<>();
                }

                if (gameTeam.getTeamId() == teamId) {
                    v.setLeft(gameTeam);
                } else {
                    v.setRight(gameTeam);
                }

                return v;
            });

            uids.add(gameTeam.getUid());
        }
        Map<Long, WebdbUserInfo> userInfoMap = webdbUinfoClient.batchGetUserInfo(List.copyOf(uids));

        List<AovGameInfo> result = new ArrayList<>(gameTeamMap.size());

        for (Map.Entry<Long, MutablePair<AovGameTeam, AovGameTeam>> entry : gameTeamMap.entrySet()) {
            long gameId = entry.getKey();
            AovGameTeam currentTeam = entry.getValue().getLeft();
            AovGameTeam opponentTeam = entry.getValue().getRight();
            AovGame game = gameMap.get(gameId);
            Assert.notNull(game, "game cannot be null");
            Assert.notNull(currentTeam, "currentTeam cannot be null");
            Assert.notNull(opponentTeam, "opponentTeam cannot be null");

            AovGameInfo info = new AovGameInfo();
            info.setRoundId(game.getRoundId());
            info.setRoundNum(game.getRoundNum());
            String roundName = game.getRoundName();
            int bo = game.getBo(), curBo = game.getCurBo();
            info.setRoundName(roundName);
            info.setGameId(gameId);
            info.setStartTime(game.getStartTime());
            info.setBo(bo);
            info.setCurBo(curBo);
            info.setState(getMatchState(game.getState(), game.getStartTime(), now));

            AovGameTeamInfo current = getMatchNodeTeamInfo(currentTeam, userInfoMap);
            info.setCurrent(current);

            AovGameTeamInfo opponent = getMatchNodeTeamInfo(opponentTeam, userInfoMap);
            info.setOpponent(opponent);

            result.add(info);
        }

        result.sort(Comparator.comparing(AovGameInfo::getStartTime).reversed());

        return result;
    }

    /**
     * 判断是否展示挂件
     * TODO: 自动更新
     * @param attr
     * @param sid
     * @param ssid
     * @param now
     * @return
     */
    public boolean existMatch(AovGameComponentAttr attr, long sid, long ssid, Date now) {
        AovPhase phase = aovPhaseMapper.selectProcessingPhase(now, attr.getActId(), null);
        if (phase == null) {
            return false;
        }

        if (phase.getState() == AovConst.PhaseState.CANCEL) {
            return false;
        }

        AovPhaseTeam team = aovPhaseTeamMapper.selectPhaseTeamBySid(phase.getId(), sid, ssid);
        if (team == null) {
            return false;
        }

        if (team.getState() == AovConst.PhaseTeamState.INIT) {
            return true;
        }

        if (team.getState() == AovConst.PhaseTeamState.SUCC) {
            // 报名成功判断是否有比赛
            List<Long> gameIds = aovGameTeamExtMapper.selectGameTeamsByTeamId(phase.getId(), team.getId(), AovConst.GameTeamState.INIT);
            if (CollectionUtils.isEmpty(gameIds)) {
                return false;
            }

            Assert.isTrue(gameIds.size() == 1, "only one init state team");

            List<AovGame> games = aovGameExtMapper.batchSelectGames(gameIds);
            return games.stream().anyMatch(game -> now.after(game.getStartTime()) && GAME_STATE.contains(game.getState()));
        }

        return false;
    }

    /**
     * 查询挂件信息
     * @param attr
     * @param uid
     * @param sid
     * @param ssid
     * @param now
     * @return
     */
    public Response<AovLayerInfo> queryChannelLayerInfo(AovGameComponentAttr attr, long uid, long sid, long ssid, Date now) {
        AovLayerInfo data = new AovLayerInfo();
        data.setCurrentTime(now);
        AovPhase phase = aovPhaseMapper.selectProcessingPhase(now, attr.getActId(), null);
        if (phase == null || phase.getState() == AovConst.PhaseState.CANCEL) {
            data.setPhaseState(0);
            return Response.success(data);
        }

        data.setPhaseId(phase.getId());
        AovPhaseTeam team = aovPhaseTeamMapper.selectPhaseTeamBySid(phase.getId(), sid, ssid);
        if (team == null) {
            data.setPhaseState(0);
            return Response.success(data);
        }

        data.setNeedApply(team.getNeedApply());

        // 报名阶段
        if (now.after(phase.getSignupStartTime()) && now.before(phase.getSignupEndTime())) {
            data.setPhaseState(1);
            AovGameTeamInfo current = new AovGameTeamInfo();
            current.setTeamId(team.getId());
            current.setUid(team.getCreator());
            WebdbUserInfo userInfo = webdbUinfoClient.getUserInfo(team.getCreator());
            if (userInfo != null) {
                current.setNick(userInfo.getNick());
                current.setAvatar(WebdbUtils.getLogo(userInfo));
            }
            data.setCurrent(current);
            data.setApplyState(getApplyState(team, uid));

            return Response.success(data);
        } else {
            AovGameTeam gameTeam = aovGameTeamExtMapper.selectPhaseProcessingGameTeam(phase.getId(), team.getId(), AovConst.GameTeamState.INIT);
            if (gameTeam == null) {
                data.setPhaseState(0);
                return Response.success(data);
            }

            AovGame game = aovGameExtMapper.selectByPrimaryKey(gameTeam.getGameId());

            if (now.after(game.getStartTime()) && GAME_STATE.contains(game.getState())) {
                data.setPhaseState(2);
                data.setGameId(game.getId());
                data.setBo(game.getBo());
                data.setCurBo(game.getCurBo());
                data.setRoundName(game.getRoundName());
                data.setRoundNum(game.getRoundNum());
                List<Long> uids = List.of(gameTeam.getUid(), gameTeam.getOpponentUid());
                Map<Long, WebdbUserInfo> userInfoMap = webdbUinfoClient.batchGetUserInfo(uids);
                AovGameTeamInfo current = new AovGameTeamInfo();
                current.setTeamId(gameTeam.getTeamId());
                current.setUid(gameTeam.getUid());
                int nodeIndex = gameTeam.getNodeIndex();
                current.setNodeIndex(nodeIndex);
                if (userInfoMap.containsKey(gameTeam.getUid())) {
                    WebdbUserInfo userInfo = userInfoMap.get(gameTeam.getUid());
                    current.setNick(userInfo.getNick());
                    current.setAvatar(WebdbUtils.getLogo(userInfo));
                }
                data.setCurrent(current);

                AovGameTeamInfo opponent = new AovGameTeamInfo();
                opponent.setTeamId(gameTeam.getOpponentTeamId());
                opponent.setUid(gameTeam.getOpponentUid());
                opponent.setNodeIndex(nodeIndex % 2 == 0 ? nodeIndex + 1 : nodeIndex - 1);

                if (userInfoMap.containsKey(gameTeam.getOpponentUid())) {
                    WebdbUserInfo userInfo = userInfoMap.get(gameTeam.getOpponentUid());
                    opponent.setNick(userInfo.getNick());
                    opponent.setAvatar(WebdbUtils.getLogo(userInfo));
                }

                data.setOpponent(opponent);

                return Response.success(data);
            }
        }

        data.setPhaseState(0);
        return Response.success(data);
    }

    public Response<String> getJumpGameUrl(AovGameComponentAttr attr, long uid, long gameId, Date now) {
        AovGame game = aovGameExtMapper.selectByPrimaryKey(gameId);
        if (game == null) {
            return Response.fail(461, "比赛不存在");
        }

        if (now.before(game.getStartTime())) {
            return Response.fail(462, "比赛尚未开始，请等待比赛开始后再进游戏吧");
        }

        if (game.getState() != AovConst.GameState.CREATED && game.getState() != AovConst.GameState.SAIBAO_READY) {
            return Response.fail(463, "比赛已结束或已被取消，敬请等待下一场比赛开始");
        }

        AovGameMember member = aovGameMemberExtMapper.selectGameMember(gameId, null, null, uid);
        if (member != null) {
            JoinRoomVO joinResult = saiBaoClient.joinRoom(game.getChildId(), uid, 0, member.getCamp(), 0, 0, 0);
            if (joinResult == null || StringUtils.isEmpty(joinResult.getRoomUrl())) {
                return Response.fail(500, "获取进入游戏房间链接失败，请稍后再试");
            }

            return Response.success(SaiBaoClient.buildRoomUrl(joinResult.getRoomUrl(), game.getChildId(), joinResult.getRoomParameters(), uid));
        }

        AovPhaseTeamMember teamMember = aovPhaseTeamMemberMapper.selectByUniq(game.getPhaseId(), uid);
        if (teamMember == null) {
            return Response.fail(465, "您非当场比赛参赛用户，无法进入比赛");
        }

        final int camp;
        if (Objects.equals(teamMember.getTeamId(), game.getCamp1TeamId())) {
            camp = 1;
        } else if (Objects.equals(teamMember.getTeamId(), game.getCamp2TeamId())) {
            camp = 2;
        } else {
            return Response.fail(465, "您非当场比赛参赛用户，无法进入比赛");
        }

        Date jumpDeadTime = DateUtils.addMinutes(game.getStartTime(), attr.getJumpRoomDeadMin());
        if (now.after(jumpDeadTime)) {
            return Response.fail(464, "由于您到场较晚，无法加入比赛。下次对局记得早点到场哦");
        }

        JoinRoomVO joinResult = saiBaoClient.joinRoom(game.getChildId(), uid, 0, camp, 0, 0, 0);
        if (joinResult == null || StringUtils.isEmpty(joinResult.getRoomUrl())) {
            return Response.fail(500, "获取进入游戏房间链接失败，请稍后再试");
        }

        member = new AovGameMember();
        member.setGameId(gameId);
        member.setTeamId(teamMember.getTeamId());
        member.setCamp(camp);
        member.setUid(uid);
        member.setJoinGameTime(now);
        member.setState(AovConst.GameMemberState.GRABBED);
        int rs = aovGameMemberExtMapper.saveGameMember(member);
        log.info("getJumpGameUrl save game member with gameId:{} uid:{} rs:{}", gameId, uid, rs);

        return Response.success(SaiBaoClient.buildRoomUrl(joinResult.getRoomUrl(), game.getChildId(), joinResult.getRoomParameters(), uid));
    }

    public Response<String> getLiveRoomInfo(AovGameComponentAttr attr, long gameId) {
        AovGame game = aovGameExtMapper.selectByPrimaryKey(gameId);
        if (game == null) {
            return Response.fail(400, "game not exist");
        }

        if (game.getState() < AovConst.GameState.CREATED) {
            return Response.fail(400, "game is not started yet");
        }

        String url = saiBaoClient.queryLiveRoomInfo(game.getChildId());
        return Response.success(url);
    }

    private int getApplyState(AovPhaseTeam team, long uid) {
        if (uid == team.getCreator()) {
            return 2;
        }

        AovPhaseTeamMember teamMember = aovPhaseTeamMemberMapper.selectByUniq(team.getPhaseId(), uid);
        if (teamMember != null) {
            if (Objects.equals(teamMember.getTeamId(), team.getId())) {
                return 2;
            }

            return 0;
        }

        if (team.getNeedApply() != 1) {
            return 0;
        }

        List<AovPhaseTeamApply> applies = aovPhaseTeamApplyMapper.listUnHandleApply(team.getId(), uid);

        if (CollectionUtils.isNotEmpty(applies)) {
            return 1;
        }

        return 0;
    }

    @NotNull
    private static AovGameTeamInfo getMatchNodeTeamInfo(AovGameTeam gameTe, Map<Long, WebdbUserInfo> userInfoMap) {
        AovGameTeamInfo teamInfo = new AovGameTeamInfo();
        teamInfo.setUid(gameTe.getUid());
        teamInfo.setNodeIndex(gameTe.getNodeIndex());
        teamInfo.setTeamId(gameTe.getId());
        WebdbUserInfo userInfo1 = userInfoMap.get(gameTe.getUid());
        if (userInfo1 != null) {
            teamInfo.setNick(userInfo1.getNick());
            teamInfo.setAvatar(WebdbUtils.getLogo(userInfo1));
        }
        int score = switch (gameTe.getState()) {
            case AovConst.GameTeamState.CANCEL_READY, AovConst.GameTeamState.RESULTED_WIN -> 1;
            default -> 0;
        };
        teamInfo.setScore(score);
        return teamInfo;
    }

    private static int getMatchState(int gameState, Date startTime, Date now) {
        if (gameState == AovConst.GameState.INIT || gameState == AovConst.GameState.CREATING) {
            return AovGameInfo.PREVIEW;
        }
        if (gameState == AovConst.GameState.CREATED) {
            if (now.before(startTime)) {
                return AovGameInfo.PREVIEW;
            }

            return AovGameInfo.GAMING;
        }

        if (AovGameComponent.CLOSE_GAME_STATES.contains(gameState)) {
            return AovGameInfo.CLOSED;
        }

        return AovGameInfo.GAMING;
    }
}
