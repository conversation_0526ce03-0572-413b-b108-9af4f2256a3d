package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.mq.HdzkWzryGameEvent;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.hdzj.bean.wzry.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.WzryTaskComponentAttr;
import com.yy.gameecology.hdzj.element.history.WzryTaskComponent;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2022/9/6 15:28
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-7.properties",
        "classpath:env/local/application-inner.properties"})
public class WzryTaskComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "7");
    }


    @Autowired
    private WzryTaskComponent wzryTaskComponent;

    @Autowired
    protected CommonService commonService;

    private WzryTaskComponentAttr getWzryTaskComponentAttr() {
        WzryTaskComponentAttr attr = new WzryTaskComponentAttr();
        attr.setActId(2024017001L);
        attr.setCmptId(ComponentId.WZRY_TASK);
        attr.setCmptUseInx(810);
        attr.setSignInId(10);
        attr.setPeriod(7);
        attr.setPiaoCurrentType(57);
        attr.setTurnoverAppId(20004);
        attr.setSignInAward(Map.of(1, 10, 3, 10, 4, 10));
        attr.setNewUserSignInAward(Map.of(1, 10, 3, 10, 4, 10));
        attr.setNewUserDay(10);
        attr.setPiaoTaskId(70057L);
        attr.setPiaoPackageId(15386L);
        attr.setTaskConfigList(Lists.newLinkedList());
        WzryTaskComponentAttr.TaskConfig loginTask = new WzryTaskComponentAttr.TaskConfig();
        loginTask.setAward(3);
        loginTask.setName("登录yo语音");
        loginTask.setType(3);
        loginTask.setNewUserAward(10);
        loginTask.setShow(true);
        loginTask.setType(1);
        loginTask.setGoal(1);
        attr.getTaskConfigList().add(loginTask);
        return attr;
    }

    private ZhuiyaPbCommon.Client getClient() {
        return
                ZhuiyaPbCommon.Client.newBuilder()
                        .setApp(ZhuiyaPbCommon.App.APP_YOMI)
                        .setHdid("d773cf23089636d6fc5c0e41eb90d0be")
                        .setIp("127.0.0.1")
                        .setPlatform(ZhuiyaPbCommon.Platform.PLATFORM_ANDROID)
                        .setVersion("1.24.0")
                        .build();
    }

    @Test
    public void signInTest() {
        long uid = 2212987748L;

        WzryTaskComponentAttr attr = getWzryTaskComponentAttr();
        SignInVo sign = wzryTaskComponent.signIn(attr, uid, new Date(), getClient());
        System.out.println();
    }

    @Test
    public void loginTipTest() {
        long uid = 2212987748L;

        WzryTaskComponentAttr attr = wzryTaskComponent.getComponentAttr(2024017001L, 810);

        LoginTipVo loginTip = wzryTaskComponent.loginTip(attr, uid, new Date(), getClient());
        System.out.println();
    }


    @Test
    public void getSignInDetailTest() {
        long uid = 50048618L;

        long actId = 2024017001L;
        WzryTaskComponentAttr attr = wzryTaskComponent.getComponentAttr(actId, 810);
        Date now = commonService.getNow(actId);

        SignInDetailVo sign = wzryTaskComponent.getSignInDetail(attr, uid, true, now, getClient());
        System.out.println();
    }

    @Test
    public void getJumpChannelTest() {
        long uid = 2212987748L;

        WzryTaskComponentAttr attr = getWzryTaskComponentAttr();

        SidSsidVo sidSsidVo = wzryTaskComponent.getJumpChannel(attr);
        System.out.println();
    }

    @Test
    public void getTaskListTest() {
        long uid = 2212987748L;
        long actId = 2024017001L;
        WzryTaskComponentAttr attr = wzryTaskComponent.getComponentAttr(actId, 810);
        Date now = commonService.getNow(actId);
        //  WzryTaskComponentAttr attr = getWzryTaskComponentAttr();

        TaskDetailVo detailVo = wzryTaskComponent.getTaskDetailVo(attr, uid, new Date(), getClient());
        System.out.println();
    }

    @Test
    public void addProgressTest() {
        long uid = 2212987748L;
        WzryTaskComponentAttr attr = wzryTaskComponent.getComponentAttr(2024017001L, 810);
        //  WzryTaskComponentAttr attr = getWzryTaskComponentAttr();

        wzryTaskComponent.addProgress(attr, uid, 1, UUID.randomUUID().toString(), new Date());
        System.out.println();


    }

    @Test
    public void drawAwardTest() {
        long uid = 2212987748L;
        WzryTaskComponentAttr attr = wzryTaskComponent.getComponentAttr(2024017001L, 810);
        //  WzryTaskComponentAttr attr = getWzryTaskComponentAttr();
        wzryTaskComponent.drawAward(attr, uid, 1, new Date(), getClient());
        System.out.println();

    }
    @Test
    public void onHdzkWzryGameEventTest() {
        long uid = 2212987748L;

        WzryTaskComponentAttr attr = wzryTaskComponent.getComponentAttr(2024017001L, 810);
        HdzkWzryGameEvent event = new HdzkWzryGameEvent();
        event.setSeq("gameover:2024012500003723");
        event.setUser(Map.of(2212987748L,1,50048618L,2));
        event.setGameState(900);
        wzryTaskComponent.onHdzkWzryGameEvent(event, attr);
        System.out.println();

    }


}
