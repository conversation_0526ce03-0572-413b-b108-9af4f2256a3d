package com.yy.gameecology.hdzj.element.component.xmodule.aov;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.PageResponse;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.wzry.JoinGameResult;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.yrpc.UgcRoomClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.aov.enroll.AovEnrollmentService;
import com.yy.gameecology.activity.service.aov.phase.AovPhaseJobService;
import com.yy.gameecology.activity.service.aov.phase.AovPhaseTeamApplyService;
import com.yy.gameecology.activity.service.aov.phase.AovPhaseTeamService;
import com.yy.gameecology.activity.service.aov.phase.AovWhitelistService;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.*;
import com.yy.gameecology.common.db.model.gameecology.aov.*;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.aov.AovWhitelistInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.AovTeamComponentAttr;
import com.yy.thrift.zhuiwan.BatchGetUserOnlineRsp;
import com.yy.thrift.zhuiwan_newfamily.ChannelKey;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@UseRedisStore(NeedChange = true)
@RestController
@RequestMapping("5126")
public class AovTeamComponent extends BaseActComponent<AovTeamComponentAttr> {

    private static final String APPLY_NOTICE = "APPLY_NOTICE_%s";

    private static final int MAX_BATCH_SIZE = 200;

    @Resource
    private AovPhaseMapper aovPhaseMapper;

    @Resource
    private AovGameExtMapper aovGameExtMapper;

    @Resource
    private AovGameTeamExtMapper aovGameTeamExtMapper;

    @Resource
    private AovMatchNodeExtMapper aovMatchNodeExtMapper;

    @Resource
    private AovPhaseRoundExtMapper aovPhaseRoundExtMapper;

    @Resource
    private AovPhaseTeamMemberMapper aovPhaseTeamMemberMapper;

    @Resource
    private AovMatchAwardRecordExtMapper aovMatchAwardRecordExtMapper;

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Autowired
    private UgcRoomClient ugcRoomClient;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private AovPhaseJobService aovPhaseJobService;

    @Autowired
    private AovPhaseTeamService aovPhaseTeamService;

    @Autowired
    private AovWhitelistService aovWhitelistService;

    @Autowired
    private AovEnrollmentService aovEnrollmentService;

    @Autowired
    private AovPhaseTeamApplyService aovPhaseTeamApplyService;

    @Autowired
    private AovRankComponent aovRankComponent;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    private AovPushComponent aovPushComponent;

    @Override
    public Long getComponentId() {
        return ComponentId.AOV_TEAM;
    }

    /**
     * 对报名截止的phase，执行初始化队伍
     */
    @NeedRecycle(author = "guanqihua", notRecycle = true)
    @Scheduled(cron = "0/5 * * * * ? ")
    public void initGameTeam() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }
            AovTeamComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("initGameTeam:" + actId, 60, () -> {
                Date now = commonService.getNow(actId);
                AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, AovConst.PhaseState.INIT);
                if(aovPhase != null && aovPhase.getState() == AovConst.PhaseState.INIT && now.after(aovPhase.getSignupEndTime())) {
                    aovPhaseJobService.initTeam(attr, aovPhase);
                }
            });
        }
    }

    public Response<AovPhaseComponent.MyTeamRsp> myTeamInfo(@RequestParam("actId") long actId,
                                                            @RequestParam(value = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                                            @RequestParam(name = "phaseId", required = false) Long phaseId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "请先登录");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "活动不存在");
        }

        Date now = commonService.getNow(actId);
        final AovPhase aovPhase = findAovPhase(actId, phaseId);
        if (aovPhase == null) {
            return Response.fail(400, "当前无赛程");
        }

        AovPhaseComponent.MyTeamRsp myTeamRsp = new AovPhaseComponent.MyTeamRsp();
        myTeamRsp.setTeamLeadGroupQr(attr.getTeamLeadGroupQr());
        myTeamRsp.setTeamLeadGroupId(attr.getTeamLeadGroupId());
        myTeamRsp.setTeammatesGroupQr(attr.getTeammatesGroupQr());
        myTeamRsp.setWxNum(attr.getWxNum());
        myTeamRsp.setDeclarationDefault(attr.getTeamDeclaration());

        List<ChannelKey> channelKeys = zhuiWanPrizeIssueServiceClient.listSkillCardAllOnlineChannel();
        Set<String> channelMap = channelKeys.stream().map(key -> key.getTopSid() + "_" + key.getSubSid()).collect(Collectors.toSet());

        AovPhaseTeamMember currentTeamMember = aovPhaseTeamMemberMapper.selectByUniq(aovPhase.getId(), uid);
        // 有队伍
        if(currentTeamMember != null) {
            buildMyTeamInfo(attr, aovPhase, currentTeamMember, channelMap, now, myTeamRsp);
        }

        AovPhaseComponent.MyInfo myInfo = new AovPhaseComponent.MyInfo();
        UserBaseInfo userBaseInfo = commonService.getUserInfo(uid,false);
        myInfo.setName(userBaseInfo.getNick());
        myInfo.setAvatar(userBaseInfo.getHdLogo());
        myTeamRsp.setMyInfo(myInfo);

        if (!myTeamRsp.isRedDot()) {
            myTeamRsp.setRedDot(!StringUtil.isEmpty(actRedisDao.hget(redisConfigManager.getGroupCode(actId),
                    String.format(APPLY_NOTICE, aovPhase.getId()), String.valueOf(uid))));
        }

        return Response.success(myTeamRsp);
    }

    private void buildMyTeamInfo(AovTeamComponentAttr attr, AovPhase aovPhase, @NotNull AovPhaseTeamMember currentTeamMember, Set<String> channelMap, Date now, AovPhaseComponent.MyTeamRsp rsp) {
        final long teamId = currentTeamMember.getTeamId();
        AovPhaseTeam team = aovPhaseTeamService.selectById(teamId);
        List<AovPhaseTeamMember> aovPhaseTeamMembers = aovPhaseTeamMemberMapper.selectAovPhaseTeamMember(teamId);
        List<Long> uids = aovPhaseTeamMembers.stream().map(AovPhaseTeamMember::getMemberUid).toList();
        Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(uids, false);
        BatchGetUserOnlineRsp batchGetUserOnline = zhuiWanPrizeIssueServiceClient.batchGetUserOnline(uids);
        rsp.setCaptain(Objects.equals(team.getCreator(), currentTeamMember.getMemberUid()));

        AovPhaseComponent.Team currentTeam = new AovPhaseComponent.Team();
        currentTeam.setTeamId(teamId);
        currentTeam.setName(team.getTeamName());
        currentTeam.setTeamNameAuditState(team.getTeamNameAuditState());
        currentTeam.setTeamNameAudit(team.getTeamNameAudit());
        currentTeam.setDeclaration(team.getDeclaration());
        if(team.getAuditState().equals(AovConst.AovTeamMsgAuditState.INIT)) {
            currentTeam.setDeclarationAudit(team.getDeclarationAudit());
        }
        currentTeam.setSid(team.getSid());
        currentTeam.setSsid(team.getSsid());
        currentTeam.setTp(team.getNeedApply());

        List<AovPhaseComponent.Teamate> teammates = new ArrayList<>();
        for (AovPhaseTeamMember m : aovPhaseTeamMembers) {
            AovPhaseComponent.Teamate teamate = new AovPhaseComponent.Teamate();
            long memberUid = m.getMemberUid();
            teamate.setUid(memberUid);
            UserBaseInfo userBaseInfo = userInfos.get(memberUid);
            if (userBaseInfo != null) {
                teamate.setName(userBaseInfo.getNick());
                teamate.setAvatar(userBaseInfo.getHdLogo());
            }

            if(batchGetUserOnline != null && batchGetUserOnline.userOnlineMap.containsKey(m.getMemberUid())) {
                teamate.setLive(batchGetUserOnline.userOnlineMap.get(m.getMemberUid()));
            }

            teammates.add(teamate);
        }

        currentTeam.setTeamates(teammates);
        currentTeam.setInRoom(channelMap.contains(team.getSid()+"_"+ team.getSsid()));

        List<AovPhaseComponent.Notify> notifies = getNotifies(attr, aovPhase, currentTeamMember, now, team, currentTeam);

        rsp.setNotifyList(notifies);
        rsp.setCurrentTeam(currentTeam);

        // 队长红点
        if (rsp.isCaptain()) {
            rsp.setRedDot(!StringUtil.isEmpty(actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                    String.format(APPLY_NOTICE, aovPhase.getId()), "team_"+currentTeamMember.getTeamId())));
        }
    }

    @NotNull
    private List<AovPhaseComponent.Notify> getNotifies(AovTeamComponentAttr attr, AovPhase aovPhase, @NotNull AovPhaseTeamMember currentTeamMember, Date now, AovPhaseTeam team, AovPhaseComponent.Team currentTeam) {
        final long teamId = currentTeamMember.getTeamId();

        List<AovPhaseRound> rounds = aovPhaseRoundExtMapper.selectRounds(aovPhase.getId());
        List<AovPhaseComponent.Notify> notifies = new ArrayList<>(2);
        if (aovPhase.getState() == AovConst.PhaseState.INIT || aovPhase.getState() == AovConst.PhaseState.SUCC) {
            AovPhaseComponent.Notify notify;
            if(team.getMemberCnt() >= attr.getTeamMemberMax()) {
                notify = new AovPhaseComponent.Notify(String.format("开赛时间%s，记得来参赛哦", DateUtil.format(rounds.getFirst().getStartTime(), DateUtil.DEFAULT_PATTERN)), false);
                currentTeam.setState(AovConst.PhaseTeamShowState.SIGN_SUCC);
            } else {
                notify = new AovPhaseComponent.Notify(String.format("报名截止时间%s，满%s人才能参赛", DateUtil.format(aovPhase.getSignupEndTime(), DateUtil.DEFAULT_PATTERN),
                        attr.getTeamMemberMin()), false);
            }
            
            notifies.add(notify);

            addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);

            return notifies;
        }

        AovPhase nextPhase = aovPhaseMapper.selectNextPhase(aovPhase.getActId(), aovPhase.getId());
        Date nextSignupTime = nextPhase == null ? null : nextPhase.getSignupStartTime();

        if(aovPhase.getState() == AovConst.PhaseState.CANCEL) {
            if (nextSignupTime != null) {
                AovPhaseComponent.Notify notify = new AovPhaseComponent.Notify(String.format("下一期开启报名时间%s，记得来报名哦",
                        DateUtil.format(nextSignupTime, DateUtil.DEFAULT_PATTERN)), false);

                notifies.add(notify);
            }

            currentTeam.setState(AovConst.PhaseTeamShowState.CANCEL);

            addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);

            return notifies;
        }

        Assert.isTrue(aovPhase.getState() == AovConst.PhaseState.INITIALIZED, "phase state is not INITIALIZED");

        if(team.getState() == AovConst.PhaseTeamState.NO_ENOUGH) {
            if (nextSignupTime != null) {
                AovPhaseComponent.Notify notify = new AovPhaseComponent.Notify(String.format("下一期开启报名时间%s，记得来报名哦",
                        DateUtil.format(nextSignupTime, DateUtil.DEFAULT_PATTERN)), false);

                notifies.add(notify);
            }

            currentTeam.setState(AovConst.PhaseTeamShowState.SIGN_FAIL);

            addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);

            return notifies;
        }

        if(team.getState() == AovConst.PhaseTeamState.OVER_LIMIT) {
            if (nextSignupTime != null) {
                AovPhaseComponent.Notify notify = new AovPhaseComponent.Notify(String.format("下一期开启报名时间%s，记得来报名哦",
                        DateUtil.format(nextSignupTime, DateUtil.DEFAULT_PATTERN)), false);

                notifies.add(notify);
            }

            currentTeam.setState(AovConst.PhaseTeamShowState.SIGN_TOOLATE);

            addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);

            return notifies;
        }

        Map<Integer, AovPhaseRound> roundMap = rounds.stream().collect(Collectors.toMap(AovPhaseRound::getRoundNum, Function.identity()));
        final AovPhaseRound firstRound = rounds.stream().filter(round -> round.getState() != 0).findFirst().orElseThrow(() -> new IllegalStateException("cannot find first round"));
        final AovPhaseRound currentRound = rounds.reversed().stream().filter(round -> round.getState() != 0).findFirst().orElseThrow(() -> new IllegalStateException("cannot find current round"));
        final AovPhaseRound nextRound = roundMap.get(currentRound.getRoundNum() - 1);
        final AovPhaseRound endRound = rounds.getLast();

        if(now.before(firstRound.getStartTime())) {
            notifies.add(new AovPhaseComponent.Notify(String.format("开赛时间%s，记得来参赛哦", DateUtil.format(firstRound.getStartTime(), DateUtil.DEFAULT_PATTERN)), false));
            currentTeam.setState(AovConst.PhaseTeamShowState.SIGN_SUCC);
            if (firstRound.getState() == AovConst.RoundState.GAME_CREATED) {
                // 判断是不是轮空的队伍（第一轮没有比赛）
                AovGameTeam aovGameTeam = aovGameTeamExtMapper.selectGameTeamsByRoundTeamId(firstRound.getId(), teamId);
                if (aovGameTeam == null) {
                    notifies.add(new AovPhaseComponent.Notify("您当前为轮空队伍，可直接晋级", false));
                }
            }

            addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);

            return notifies;
        }

        // 当前轮次为空
//        if (currentRound == null) {
//            if (nextRound != null) {
//                currentTeam.setState(AovConst.PhaseTeamShowState.NEXT_ROUND);
//                notifies.add(new AovPhaseComponent.Notify(String.format("下一轮开赛时间%s，记得来参赛哦", DateUtil.format(nextRound.getStartTime())), false));
//            } else {
//                currentTeam.setState(AovConst.PhaseTeamShowState.END);
//                if (nextSignupTime != null) {
//                    notifies.add(new AovPhaseComponent.Notify(String.format("下一期开启报名时间%s，记得来报名哦", DateUtil.format(nextSignupTime, DateUtil.DEFAULT_PATTERN)), false));
//                }
//            }
//
//            addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);
//
//            return notifies;
//        }

        // 当前轮次还在初始化
        if(currentRound.getState() == AovConst.RoundState.INIT || currentRound.getState() == AovConst.RoundState.ADJUSTING) {
            notifies.add(new AovPhaseComponent.Notify(String.format("下一轮开赛时间%s，记得来参赛哦", DateUtil.format(currentRound.getStartTime(), DateUtil.DEFAULT_PATTERN)), false));
            currentTeam.setState(AovConst.PhaseTeamShowState.NEXT_ROUND);

            addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);

            return notifies;
        }

        if (currentRound.getState() == AovConst.RoundState.GAME_CREATABLE || currentRound.getState() == AovConst.RoundState.GAME_CREATED) {
            AovMatchNode node = aovMatchNodeExtMapper.selectRoundTeamNode(currentRound.getPhaseId(), currentRound.getId(), teamId);
            if (node == null) {
                if (nextSignupTime != null) {
                    notifies.add(new AovPhaseComponent.Notify(String.format("下一期开启报名时间%s，记得来报名哦", DateUtil.format(nextSignupTime, DateUtil.DEFAULT_PATTERN)), false));
                }

                currentTeam.setState(AovConst.PhaseTeamShowState.END);

                addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);
                return notifies;
            }

            int advanceNodeIndex = node.getNodeIndex() / 2;
            AovGame aovGame = aovGameExtMapper.selectGamesByPhaseIdNodeId(currentRound.getPhaseId(), advanceNodeIndex);
            if (aovGame != null) {
                if(now.before(aovGame.getStartTime())) {
                    notifies.add(new AovPhaseComponent.Notify(String.format("下一轮开赛时间%s，记得来参赛哦", DateUtil.format(aovGame.getStartTime(), DateUtil.DEFAULT_PATTERN)), false));
                    currentTeam.setState(AovConst.PhaseTeamShowState.NEXT_ROUND);
                } else if (aovGame.getState() == AovConst.GameState.CREATED || aovGame.getState() == AovConst.GameState.SAIBAO_READY) {
                    notifies.add(new AovPhaseComponent.Notify(String.format("开赛时间%s，记得来参赛哦", DateUtil.format(aovGame.getStartTime(), DateUtil.DEFAULT_PATTERN)), false));
                    currentTeam.setState(AovConst.PhaseTeamShowState.GAME_PLAYING);
                } else {
                    notifies.add(new AovPhaseComponent.Notify("等待其他对局完成比赛结算赛果，预计等待1-2小时", false));
                    currentTeam.setState(AovConst.PhaseTeamShowState.WAIT_RESULT);
                }
            } else if (Objects.equals(currentRound.getId(), firstRound.getId())) {
                notifies.add(new AovPhaseComponent.Notify("等待其他对局完成比赛结算赛果，预计等待1-2小时", false));
                notifies.add(new AovPhaseComponent.Notify("您当前为轮空队伍，可直接晋级", false));

                currentTeam.setState(AovConst.PhaseTeamShowState.WAIT_RESULT);
            } else {
                notifies.add(new AovPhaseComponent.Notify("成功晋级等待线下赛开赛，请前往比赛QQ群了解线下赛开启时间", false));
                currentTeam.setState(AovConst.PhaseTeamShowState.NEXT_ROUND);
            }

            addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);

            return notifies;
        }

        // 当前轮次已结算
        if(currentRound.getState() == AovConst.RoundState.ADVANCED) {
            if(Objects.equals(currentRound.getId(), endRound.getId()))  {
                List<AovMatchNode> aovMatchNodes = aovMatchNodeExtMapper.batchSelectByNodes(aovPhase.getId(), List.of(2, 3));
                Assert.isTrue(aovMatchNodes.size() == 2, "node size must be 2");
                AovMatchNode node1 = aovMatchNodes.get(0);
                AovMatchNode node2 = aovMatchNodes.get(1);
                if (node1.getState() == AovConst.MatchNodeState.ELIMINATED && node2.getState() == AovConst.MatchNodeState.ELIMINATED) {
                    notifies.add(new AovPhaseComponent.Notify("由于您的队伍未在规定时间内到齐参与比赛，本场比赛视为弃权，奖励不予发放", false));
                }

                if (nextSignupTime != null) {
                    notifies.add(new AovPhaseComponent.Notify(String.format("下一期开启报名时间%s，记得来报名哦", DateUtil.format(nextSignupTime, DateUtil.DEFAULT_PATTERN)), false));
                }

                currentTeam.setState(AovConst.PhaseTeamShowState.END);

            } else {
                AovGameTeam aovGameTeam = aovGameTeamExtMapper.selectGameTeamsByRoundTeamId(nextRound.getId(), teamId);
                if(aovGameTeam != null) {
                    AovGame aovGame = aovGameExtMapper.selectByPrimaryKey(aovGameTeam.getGameId());
                    notifies.add(new AovPhaseComponent.Notify(String.format("下一轮开赛时间%s，记得来参赛哦", DateUtil.format(aovGame.getStartTime(), DateUtil.DEFAULT_PATTERN)), false));

                    currentTeam.setState(AovConst.PhaseTeamShowState.NEXT_ROUND);
                } else {
                    if (nextSignupTime != null) {
                        notifies.add(new AovPhaseComponent.Notify(String.format("下一期开启报名时间%s，记得来报名哦", DateUtil.format(nextSignupTime, DateUtil.DEFAULT_PATTERN)), false));
                    }

                    currentTeam.setState(AovConst.PhaseTeamShowState.END);
                }
            }
        }

        // 未领取的奖励
        addAwardNoticeIfNecessary(aovPhase, currentTeamMember, notifies);
        return notifies;
    }

    private void addAwardNoticeIfNecessary(AovPhase aovPhase, @NotNull AovPhaseTeamMember currentTeamMember, List<AovPhaseComponent.Notify> notifies) {
        List<AovMatchAwardRecord> awardRecords = aovMatchAwardRecordExtMapper.selectUserPhaseAwardList(currentTeamMember.getMemberUid(), aovPhase.getId(), AovConst.AwardState.GRANTED);
        if(CollectionUtils.isNotEmpty(awardRecords)) {
            for (AovMatchAwardRecord awardRecord : awardRecords) {
                notifies.add(new AovPhaseComponent.Notify(String.format("恭喜获得%s！可前往领取", awardRecord.getAwardDesc()), true));
            }
        }
    }

    public Response<?> createTeam(HttpServletRequest request,
                                  @RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx,
                                  @RequestParam(required = false, defaultValue = "") String msg,
                                  @RequestParam("needApply")int needApply,
                                  @RequestParam(name = "verifyCode", required = false) String verifyCode,
                                  @RequestParam(name = "recordId", required = false) String recordId,
                                  @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "创建队伍需要先登录");
        }

        Date now = commonService.getNow(actId);
        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, AovConst.PhaseState.INIT);
        Response<?> check = checkParam(uid, actId, cmptInx, aovPhase);
        if(!check.success()) {
            return check;
        }

        if (now.after(aovPhase.getSignupEndTime())) {
            return Response.fail(-1, "已到达报名截止时间，无法创建队伍");
        }

        //报名
        //绑定
        boolean bind = saiBaoClient.isPassportBind(uid);
        if(!bind){
            return Response.fail(-1, "未绑定角色，请前往王者巅峰赛活动页绑定");
        }

        long phaseId = aovPhase.getId();
        AovPhaseTeam nowAovPhaseTeam = aovPhaseTeamService.selectAovPhaseTeam(uid, phaseId);
        if(nowAovPhaseTeam != null) {
            return Response.fail(-1, "本阶段已创建队伍");
        }

        AovPhaseTeamMember aovPhaseTeamMember = aovPhaseTeamMemberMapper.selectByUniq(phaseId, uid);
        if(aovPhaseTeamMember != null) {
            return Response.fail(-1, "本阶段已加入队伍");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        //是否实名
        String idHash = userinfoThriftClient.getIdHash(uid);
        if(StringUtil.isEmpty(idHash)) {
            return Response.fail(471, "您还没有完成实名认证，请先完成实名认证");
        }
        //风控
        Response<?> riskResponse = checkRisk(uid, actId, cmptInx, request, verifyCode, recordId, verifyToken);
        if(!riskResponse.success()) {
            return riskResponse;
        }
        return doCreateTeam(actId, msg, needApply, aovPhase, uid, attr, phaseId);
    }

    @RequestMapping("/createTeamTest")
    public Response<?> createTeamTest(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx,
                                      @RequestParam(required = false, defaultValue = "") String msg,
                                      @RequestParam("needApply") int needApply,
                                      @RequestParam("uid") long uid) {

        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(-1, "not test env and not grey");
        }

        Date now = commonService.getNow(actId);
        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, AovConst.PhaseState.INIT);

        long phaseId = aovPhase.getId();
        AovPhaseTeam nowAovPhaseTeam = aovPhaseTeamService.selectAovPhaseTeam(uid, phaseId);
        if (nowAovPhaseTeam != null) {
            return Response.fail(-1, "本阶段已创建队伍");
        }

        AovPhaseTeamMember aovPhaseTeamMember = aovPhaseTeamMemberMapper.selectByUniq(phaseId, uid);
        if (aovPhaseTeamMember != null) {
            return Response.fail(-1, "本阶段已加入队伍");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        return doCreateTeam(actId, msg, needApply, aovPhase, uid, attr, phaseId);
    }

    private @NotNull Response<?> doCreateTeam(long actId, String msg, int needApply, AovPhase aovPhase, long uid, AovTeamComponentAttr attr, long phaseId) {
        Date add = DateUtil.add(aovPhase.getEndTime(), 30);
        boolean addUgcWhitelistResult = ugcRoomClient.addUgcWhitelist(uid, add.getTime()/1000, actId + "");
        if (!addUgcWhitelistResult) {
            return Response.fail(-1, "创建队伍失败,请稍后重试");
        }
        // 调用接口创建ugc白名单
        ChannelInfo channelInfo = ugcRoomClient.obtainUgcRoomInfo(uid, actId);
        if (channelInfo == null) {
            return Response.fail(-1, "创建队伍失败,请稍后重试");
        }

        long sid = channelInfo.getSid();
        long ssid = channelInfo.getSsid();
        AovPhaseTeam aovPhaseTeam;
        if(!StringUtil.isEmpty(msg) && !msg.equals(attr.getTeamDeclaration())) {
            //送审
            //ugc房间
            aovPhaseTeam = new AovPhaseTeam(sid, ssid, phaseId, uid, AovConst.PhaseTeamState.INIT, needApply,
                    attr.getTeamDeclaration(), msg, AovConst.AovTeamMsgAuditState.INIT, 1,
                    attr.getTeamMemberMax(), new Date());
        } else {
            aovPhaseTeam = new AovPhaseTeam(sid, ssid, phaseId, uid, AovConst.PhaseTeamState.INIT, needApply,
                    attr.getTeamDeclaration(), "", AovConst.AovTeamMsgAuditState.PASS, 1,
                    attr.getTeamMemberMax(), new Date());
        }

        long score = aovRankComponent.getPrePhaseScore(actId, aovPhase.getPrevPhaseId(), uid);
        aovPhaseTeam.setTeamScore(score);


        String nick = commonService.getNickName(uid,false);
        aovPhaseTeam.setTeamName(nick + "的战队");
        aovPhaseTeam.setTeamNameAuditState(AovConst.TeamNameStatus.PASS);

        try {
            aovPhaseTeamService.createTeam(attr, aovPhaseTeam);
        } catch (DuplicateKeyException e) {
            log.warn("add game phase team fail uid duplicate");
            return Response.success(-1, "创建队伍失败,请稍后重试");
        }
        return Response.success(null);
    }


    public Response<?> turnApplySwitch(@RequestParam("actId") long actId,
                                       @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                       @RequestParam(name = "teamId") long teamId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        AovPhaseTeam team = aovPhaseTeamService.selectById(teamId);
        if (team == null || team.getCreator() != uid) {
            return Response.fail(403, "您不是队长，无权执行此操作");
        }

        int needApply = team.getNeedApply();
        log.info("turnApplySwitch current:{}", needApply);
        int rs = aovPhaseTeamService.turnNeedApply(teamId);
        log.info("turnApplySwitch turn with uid:{} needApply:{} rs:{}", uid, needApply, rs);
        return Response.ok();
    }

    public Response<AovPhaseComponent.TeamListRsp> teamList(@RequestParam("actId") long actId,
                                                            @RequestParam(value = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                                            @RequestParam(name = "phaseId", required = false) Long phaseId,
                                                            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
                                                            @RequestParam(name = "pageSize", required = false, defaultValue = "30") Integer pageSize) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "请先登录");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "活动不存在");
        }

        final AovPhase aovPhase = findAovPhase(actId,phaseId);
        if (aovPhase == null) {
            return Response.fail(400, "当前无赛程");
        }
        //防止前端获取过大数据
        pageSize = Math.min(pageSize, Const.THREE_HUNDRED);

        AovPhaseComponent.TeamListRsp teamListRsp = new AovPhaseComponent.TeamListRsp();
        List<ChannelKey> channelKeys = zhuiWanPrizeIssueServiceClient.listSkillCardAllOnlineChannel();
        Set<String> channelMap = channelKeys.stream().map(key -> key.getTopSid() + "_" + key.getSubSid()).collect(Collectors.toSet());

        // 无队伍
        List<AovPhaseTeam> aovPhaseTeamList = aovPhaseTeamService.listTeam(aovPhase.getId(), page, pageSize);
        if(CollectionUtils.isNotEmpty(aovPhaseTeamList)) {
            Map<Long, AovPhaseTeam> teamMap = new HashMap<>(aovPhaseTeamList.size());
            List<Long> teamIds = new ArrayList<>(aovPhaseTeamList.size());
            List<AovPhaseComponent.Team> teams = new ArrayList<>(aovPhaseTeamList.size());
            for (AovPhaseTeam aovPhaseTeam : aovPhaseTeamList) {
                teamIds.add(aovPhaseTeam.getId());
                teamMap.put(aovPhaseTeam.getId(), aovPhaseTeam);
            }

            List<AovPhaseTeamApply> applies = aovPhaseTeamApplyService.listUnHandleApplyByPhase(aovPhase.getId(), teamIds, uid);
            Map<Long, AovPhaseTeamApply> applyMap = applies.stream().collect(Collectors.toMap(AovPhaseTeamApply::getTeamId, Function.identity()));

            List<AovPhaseTeamMember> aovPhaseTeamMembers = aovPhaseTeamMemberMapper.listTeamMembers(teamIds);
            Map<Long, List<AovPhaseTeamMember>> listMap = new HashMap<>();
            List<Long> teamMemberUid = Lists.newArrayList();
            for (AovPhaseTeamMember aovPhaseTeamMember : aovPhaseTeamMembers) {
                List<AovPhaseTeamMember> members =
                        listMap.getOrDefault(aovPhaseTeamMember.getTeamId(), new ArrayList<>());
                members.add(aovPhaseTeamMember);
                teamMemberUid.add(aovPhaseTeamMember.getMemberUid());
                listMap.put(aovPhaseTeamMember.getTeamId(), members);
            }
            Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(teamMemberUid, false);
            BatchGetUserOnlineRsp batchGetUserOnline = zhuiWanPrizeIssueServiceClient.batchGetUserOnline(teamMemberUid);

            for (Long teamId : teamIds) {
                AovPhaseComponent.Team team = new AovPhaseComponent.Team();
                team.setTeamId(teamId);
                AovPhaseTeam aovPhaseTeam = teamMap.get(teamId);
                team.setName(aovPhaseTeam.getTeamName());
                List<AovPhaseComponent.Teamate> teamates = new ArrayList<>();
                for (AovPhaseTeamMember m : listMap.get(teamId)) {
                    AovPhaseComponent.Teamate teamate = new AovPhaseComponent.Teamate();
                    String nick = userInfos.get(m.getMemberUid()).getNick();
                    teamate.setName(nick);
                    teamate.setUid(m.getMemberUid());
                    teamate.setAvatar(userInfos.get(m.getMemberUid()).getHdLogo());
                    if(batchGetUserOnline != null && batchGetUserOnline.userOnlineMap.containsKey(m.getMemberUid())) {
                        teamate.setLive(batchGetUserOnline.userOnlineMap.get(m.getMemberUid()));
                    }
                    teamates.add(teamate);
                }
                team.setTp(aovPhaseTeam.getNeedApply());
                if(applyMap.containsKey(teamId)) {
                    team.setTp(PepcConst.TeamApplyShowState.APPLIED);
                }
                team.setDeclaration(aovPhaseTeam.getDeclaration());
                team.setTeamates(teamates);
                team.setSid(aovPhaseTeam.getSid());
                team.setSsid(aovPhaseTeam.getSsid());
                team.setInRoom(channelMap.contains(aovPhaseTeam.getSid()+"_"+aovPhaseTeam.getSsid()));
                team.setMemberCnt(aovPhaseTeam.getMemberCnt());
                team.setMemberLimit(aovPhaseTeam.getMemberLimit());
                teams.add(team);
            }


            List<AovPhaseComponent.Team> fullTeams = teams.stream().filter(p -> p.getMemberCnt() >= p.getMemberLimit()).toList();
            List<AovPhaseComponent.Team> notFullTeams = teams.stream().filter(p -> p.getMemberCnt() < p.getMemberLimit()).toList();

            notFullTeams = notFullTeams.stream()
                    .sorted(Comparator.comparing(AovPhaseComponent.Team::getMemberCnt, Comparator.reverseOrder())
                            .thenComparing(AovPhaseComponent.Team::isInRoom, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

            List<AovPhaseComponent.Team> allTeam = Lists.newLinkedList(notFullTeams);
            allTeam.addAll(fullTeams);
            teamListRsp.setTeamList(allTeam);
        }


        return Response.success(teamListRsp);
    }

    public Response<?> kickOutMember(@RequestParam("actId") long actId,
                                     @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                     @RequestParam(name = "teamId") long teamId,
                                     @RequestParam(name = "memberUid") long memberUid) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "踢出队伍需要登录");
        }

        if (uid == memberUid) {
            return Response.fail(400, "不能踢出自己");
        }

        AovPhaseTeam team = aovPhaseTeamService.selectById(teamId);
        if (team == null || team.getCreator() != uid) {
            return Response.fail(403, "您不是队长，无权执行此操作");
        }

        AovPhase phase = aovPhaseMapper.selectByPhaseId(team.getPhaseId());
        Date now = commonService.getNow(actId);
        if (phase == null || now.after(DateUtils.addMinutes(phase.getSignupEndTime(), -30))) {
            return Response.fail(461, "报名结束前30分钟内不允许踢出队员");
        }

        AovPhaseTeamMember member = aovPhaseTeamMemberMapper.selectByUniq(phase.getId(), memberUid);
        if (member == null || member.getTeamId() != teamId) {
            return Response.fail(462, "该队员已经不在您的队伍中");
        }

        //TODO: 控制频率

        long score = aovRankComponent.getPrePhaseScore(actId, phase.getPrevPhaseId(), uid);
        boolean removed = aovPhaseTeamService.removeTeammate(member, score);
        if (removed) {
            aovPushComponent.sendKickedOutNotice(actId, memberUid);
        }
        return Response.ok();
    }

    public Response<?> quitTeam(@RequestParam("actId") long actId,
                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                @RequestParam(name = "teamId") long teamId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "需要登录");
        }

        AovPhaseTeam team = aovPhaseTeamService.selectById(teamId);
        if (team == null) {
            return Response.fail(400, "队伍不存在");
        }

        AovPhase phase = aovPhaseMapper.selectByPhaseId(team.getPhaseId());
        Date now = commonService.getNow(actId);
        if (phase == null || now.after(DateUtils.addMinutes(phase.getSignupEndTime(), -30))) {
            return Response.fail(461, "报名结束前30分钟内不允许离开队伍");
        }

        AovPhaseTeamMember member = aovPhaseTeamMemberMapper.selectByUniq(phase.getId(), uid);
        if (member == null || member.getTeamId() != teamId) {
            return Response.fail(462, "您已不在队伍中");
        }

        //TODO: 控制频率

        // 解散队伍
        if (uid == team.getCreator()) {
            List<Long> memberUids = aovPhaseTeamService.disbandTeam(team);
            if (CollectionUtils.isNotEmpty(memberUids)) {
                aovPushComponent.sendDisbandTeamNotice(actId, memberUids);

                // 把红点去掉
                actRedisDao.hdel(redisConfigManager.getGroupCode(actId), String.format(APPLY_NOTICE, phase.getId()), String.valueOf(uid));
            }
        } else { // 离开队伍
            long score = aovRankComponent.getPrePhaseScore(actId, phase.getPrevPhaseId(), uid);
            boolean quit = aovPhaseTeamService.quitTeam(member, score);
            if (quit) {
                aovPushComponent.sendQuitTeamNotice(actId, team.getCreator());
            }
        }

        return Response.ok();
    }

    public Response<AovPhaseComponent.InRoomRsp> inRoom(@RequestParam("actId") long actId,
                                                        @RequestParam("cmptInx") long cmptInx,
                                                        @RequestParam("teamId")long teamId) {
        Date now = commonService.getNow(actId);
        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, null);
        AovPhaseComponent.InRoomRsp inRoomRsp = new AovPhaseComponent.InRoomRsp();
        if(aovPhase == null) {
            return Response.success(inRoomRsp);
        }
        AovPhaseTeam aovPhaseTeam = aovPhaseTeamService.selectById(teamId);
        if(aovPhaseTeam != null) {
            long sid = aovPhaseTeam.getSid();
            long ssid = aovPhaseTeam.getSsid();
            List<ChannelKey> channelKeys = zhuiWanPrizeIssueServiceClient.listSkillCardAllOnlineChannel();
            for (ChannelKey channelKey : channelKeys) {
                if (channelKey.getTopSid() == sid && channelKey.getSubSid() == ssid) {
                    inRoomRsp.setInRoom(true);
                    break;
                }
            }
        }

        return Response.success(inRoomRsp);
    }

    public Response<?> joinTeam(HttpServletRequest request,
                                @RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx,
                                @RequestParam(required = false, defaultValue = "") String msg,
                                @RequestParam("teamId")long teamId,
                                @RequestParam(name = "verifyCode", required = false) String verifyCode,
                                @RequestParam(name = "recordId", required = false) String recordId,
                                @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "加入队伍需要先登录");
        }

        log.info("joinTeam request with uid:{} teamId:{}", uid, teamId);
        Date now = commonService.getNow(actId);
        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, AovConst.PhaseState.INIT);
        Response<?> check = checkParam(uid, actId, cmptInx, aovPhase);
        if(!check.success()) {
            return check;
        }

        if (now.after(aovPhase.getSignupEndTime())) {
            return Response.fail(-1, "已到达报名截止时间无法加入队伍");
        }

        //报名
        //绑定
        boolean bind = saiBaoClient.isPassportBind(uid);
        if(!bind){
            return Response.fail(-1, "未绑定角色，请前往王者巅峰赛活动页绑定");
        }
        Response<?> riskResponse = checkRisk(uid, actId, cmptInx, request, verifyCode, recordId, verifyToken);
        if(!riskResponse.success()) {
            return riskResponse;
        }
        return doJoinTeam(actId, cmptInx, msg, teamId, uid, aovPhase);
    }

    @RequestMapping("/joinTeamTest")
    public Response<?> joinTeamTest(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx,
                                    @RequestParam(required = false, defaultValue = "") String msg,
                                    @RequestParam("teamId") long teamId,
                                    @RequestParam("uid") long uid) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(-1, "not test env and not grey");
        }

        log.info("joinTeamTest request with uid:{} teamId:{}", uid, teamId);
        Date now = commonService.getNow(actId);
        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, AovConst.PhaseState.INIT);
        return doJoinTeam(actId, cmptInx, msg, teamId, uid, aovPhase);
    }

    private @NotNull Response<?> doJoinTeam(long actId, long cmptInx, String msg, long teamId, long uid, AovPhase aovPhase) {
        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        AovPhaseTeam aovPhaseTeam = aovPhaseTeamService.selectById(teamId);
        if(aovPhaseTeam == null) {
            return Response.fail(-1, "加入队伍失败,队伍可能已被队长解散");
        }

        if(uid == aovPhaseTeam.getCreator()) {
            return Response.ok();
        }

        if(aovPhaseTeam.getMemberCnt() >= attr.getTeamMemberMax()) {
            return Response.fail(-1, "队伍人数已满");
        }

        AovPhaseTeamMember teamMember = aovPhaseTeamMemberMapper.selectByUniq(aovPhase.getId(), uid);
        if (teamMember != null) {
            return Response.fail(-1, "请勿重复加入队伍");
        }

        if(aovPhaseTeam.getNeedApply() == 0) {
            //直接加入
            AovPhaseTeamMember aovPhaseTeamMember = new AovPhaseTeamMember(teamId, aovPhase.getId(), uid,
                    AovConst.AovTeamRole.MATE+"", 0, new Date());
            long score = aovRankComponent.getPrePhaseScore(actId, aovPhase.getPrevPhaseId(), uid);
            try {
                aovPhaseTeamService.addTeammate(teamId, aovPhaseTeamMember, score);
                var pushAttr = aovPushComponent.tryGetUniqueComponentAttr(actId);
                aovPushComponent.sendRoomNotice(actId, UUID.randomUUID().toString(), pushAttr.getJoinTeamRoomNotice(), uid, aovPhaseTeam.getSid(), aovPhaseTeam.getSsid());
                aovPushComponent.sendJoinTeamIm(pushAttr, aovPhaseTeam.getCreator());
            } catch (Exception e) {
                return Response.fail(-1, "加入队伍失败,请稍后重试");
            }
        } else {
            List<AovPhaseTeamApply> aovPhaseTeamApply = aovPhaseTeamApplyService.listUnHandleApply(teamId, uid);
            if(!CollectionUtils.isEmpty(aovPhaseTeamApply)) {
                return Response.fail(-1, "请勿重复申请");
            }

            AovPhaseTeamApply apply =
                    new AovPhaseTeamApply(aovPhase.getId(), teamId,
                            uid, msg, AovConst.AovTeamApplyState.INIT, new Date());
            aovPhaseTeamApplyService.addApply(apply);
            aovPushComponent.sendNewApplyNotice(actId, aovPhaseTeam.getCreator());
            //红点
            actRedisDao.hset(redisConfigManager.getGroupCode(actId),
                    String.format(APPLY_NOTICE, aovPhase.getId()), "team_"+ teamId, uid + "", 7 * DateUtil.DAY_SEC);
            var pushAttr = aovPushComponent.tryGetUniqueComponentAttr(actId);
            aovPushComponent.sendRoomNotice(actId, UUID.randomUUID().toString(), pushAttr.getApplyTeamRoomNotice(), uid, aovPhaseTeam.getSid(), aovPhaseTeam.getSsid());
        }

        return Response.success(null);
    }

    public Response<AovPhaseComponent.ApplyListRsp> applyList(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptInx) {
        long uid = getLoginYYUid();
        Date now = commonService.getNow(actId);
        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, null);
        Response check = checkParam(uid, actId, cmptInx, aovPhase);
        if(!check.success()) {
            return check;
        }
        Set<Long> uids = new HashSet<>();
        uids.add(uid);
        long phaseId = aovPhase.getId();
        AovPhaseTeam aovPhaseTeam = aovPhaseTeamService.selectAovPhaseTeam(uid, aovPhase.getId());
        //队长
        AovPhaseComponent.ApplyListRsp applyListRsp = new AovPhaseComponent.ApplyListRsp();
        List<AovPhaseComponent.ApplyInfo> applyInfos = new ArrayList<>();
        if(aovPhaseTeam != null) {
            applyListRsp.setCaptain(true);
            long teamId = aovPhaseTeam.getId();
            List<AovPhaseTeamApply> aovPhaseTeamApplies = aovPhaseTeamApplyService.listApplyByOwner(teamId);
            for (AovPhaseTeamApply apply : aovPhaseTeamApplies) {
                uids.add(apply.getUid());
            }
            Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(uids), false);
            String teamName = userInfos.get(uid).getNick();
            for (AovPhaseTeamApply apply : aovPhaseTeamApplies) {
                AovPhaseComponent.ApplyInfo applyInfo = new AovPhaseComponent.ApplyInfo();
                applyInfo.setApplyId(apply.getId());
                applyInfo.setTeamName(teamName);
                applyInfo.setName(userInfos.get(apply.getUid()).getNick());
                applyInfo.setAvatar(userInfos.get(apply.getUid()).getHdLogo());
                applyInfo.setTp(apply.getState());
                applyInfo.setUid(apply.getUid());
                applyInfo.setApplyTime(DateUtil.format(apply.getCreateTime(), DateUtil.DEFAULT_PATTERN));
                applyInfo.setMsg(apply.getMsg());
                applyInfos.add(applyInfo);
            }

            actRedisDao.hdel(redisConfigManager.getGroupCode(actId), String.format(APPLY_NOTICE, aovPhase.getId()), "team_"+teamId);
            log.info("clear captain red dot uid:{}", uid);
        } else {
            applyListRsp.setCaptain(false);
            List<AovPhaseTeamApply> aovPhaseTeamApplies = aovPhaseTeamApplyService.listApply(phaseId, uid);
            if (CollectionUtils.isEmpty(aovPhaseTeamApplies)) {
                applyListRsp.setApplyInfos(applyInfos);
                return Response.success(applyListRsp);
            }
            Set<Long> teamIds = new HashSet<>();
            for (AovPhaseTeamApply aovPhaseTeamApply : aovPhaseTeamApplies) {
                teamIds.add(aovPhaseTeamApply.getTeamId());
            }
            Map<Long, Long> teamIdCreatorMap = new HashMap<>();
            List<AovPhaseTeam> teamList = aovPhaseTeamService.selectByIds(List.copyOf(teamIds));
            for (AovPhaseTeam phaseTeam : teamList) {
                uids.add(phaseTeam.getCreator());
                teamIdCreatorMap.put(phaseTeam.getId(), phaseTeam.getCreator());
            }
            Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(uids), false);
            for (AovPhaseTeamApply apply : aovPhaseTeamApplies) {
                Long creator = teamIdCreatorMap.get(apply.getTeamId());
                if (creator == null) {
                    continue;
                }

                AovPhaseComponent.ApplyInfo applyInfo = new AovPhaseComponent.ApplyInfo();
                applyInfo.setApplyId(apply.getId());
                String teamName = userInfos.get(creator).getNick();
                applyInfo.setTeamName(teamName);
                applyInfo.setName(userInfos.get(apply.getUid()).getNick());
                applyInfo.setAvatar(userInfos.get(apply.getUid()).getHdLogo());
                applyInfo.setTp(apply.getState());
                applyInfo.setMsg(apply.getMsg());
                applyInfo.setApplyTime(DateUtil.format(apply.getCreateTime(), DateUtil.DEFAULT_PATTERN));
                applyInfos.add(applyInfo);
            }

            actRedisDao.hdel(redisConfigManager.getGroupCode(actId), String.format(APPLY_NOTICE, phaseId), String.valueOf(uid));
            log.info("clear captain red dot uid:{}", uid);
        }
        applyListRsp.setApplyInfos(applyInfos);
        return Response.success(applyListRsp);
    }

    /**
     * 队长审批入队申请
     * @param actId
     * @param cmptInx
     * @param applyId
     * @param apply
     * @return
     */
    public Response<?> apply(@RequestParam("actId") long actId,
                             @RequestParam("cmptInx") long cmptInx,
                             @RequestParam("applyId") long applyId,
                             @RequestParam("apply") int apply) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "请先完成登录");
        }

        Date now = commonService.getNow(actId);
        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, AovConst.PhaseState.INIT);
        Response<?> check = checkParam(uid, actId, cmptInx, aovPhase);
        if(!check.success()) {
            return check;
        }

        AovPhaseTeamApply aovPhaseTeamApply = aovPhaseTeamApplyService.selectById(applyId);
        if(aovPhaseTeamApply == null) {
            return Response.fail(-1, "参数错误,请稍后重试");
        }

        long teamId = aovPhaseTeamApply.getTeamId();
        AovPhaseTeam aovPhaseTeam = aovPhaseTeamService.selectById(teamId);
        if(aovPhaseTeam == null || uid != aovPhaseTeam.getCreator()) {
            return Response.fail(-1, "您不是本队伍队长，无法执行审批");
        }

        if(now.after(aovPhase.getSignupEndTime())) {
            return Response.fail(-1, "本期报名已结束，无法审批");
        }

        if(apply == 0) {
            aovPhaseTeamApplyService.updateState(applyId, AovConst.AovTeamApplyState.REJECT);
            //红点
            actRedisDao.hset(redisConfigManager.getGroupCode(actId),
                    String.format(APPLY_NOTICE, aovPhase.getId()), String.valueOf(aovPhaseTeamApply.getUid()), aovPhaseTeam.getCreator() + "", 7 * DateUtil.DAY_SEC);
        } else {
            AovPhaseTeamMember  aovPhaseTeamMember = aovPhaseTeamMemberMapper.selectByUniq(aovPhase.getId(), aovPhaseTeamApply.getUid());
            if(aovPhaseTeamMember != null) {
                return Response.fail(-1, "该用户已加入其他队伍");
            }

            long score = aovRankComponent.getPrePhaseScore(actId, aovPhase.getPrevPhaseId(), aovPhaseTeamApply.getUid());
            try {
                aovPhaseTeamApplyService.apply(aovPhaseTeamApply, score);
            } catch (BusinessException e) {
                return Response.fail(e.getCode(), e.getMessage());
            } catch (Exception e) {
                return Response.fail(-1, "审批失败,请稍后重试");
            }
            aovPushComponent.sendApprovedNotice(actId, aovPhaseTeamApply.getUid());
            //红点
            actRedisDao.hset(redisConfigManager.getGroupCode(actId),
                    String.format(APPLY_NOTICE, aovPhase.getId()), String.valueOf(aovPhaseTeamApply.getUid()), aovPhaseTeam.getCreator() + "",  7 * DateUtil.DAY_SEC);
        }

        return Response.ok();
    }

    public Response<?> changeDeclaration(@RequestParam("actId") long actId,
                                         @RequestParam("cmptInx") long cmptInx,
                                         @RequestParam("msg")String msg) {
        long uid = getLoginYYUid();
        Date now = commonService.getNow(actId);
        AovPhase aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, null);
        Response<?> check = checkParam(uid, actId, cmptInx, aovPhase);
        if(!check.success()) {
            return check;
        }

        AovPhaseTeam aovPhaseTeam = aovPhaseTeamService.selectAovPhaseTeam(uid, aovPhase.getId());
        if(aovPhaseTeam == null) {
            return Response.fail(-1, "参数错误");
        }

        if(aovPhaseTeam.getAuditState().equals(AovConst.AovTeamMsgAuditState.INIT)) {
            return Response.fail(-1, "之前的文案审核中, 无法修改");
        }

        //判断字符是不是一样的
        //送审
        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        try {
            aovPhaseTeamService.updateDeclaration(attr, msg, AovConst.AovTeamMsgAuditState.INIT,
                    aovPhaseTeam.getId(), uid);
        } catch (Exception e) {
            return Response.fail(-1, "修改文案失败，请稍后重试");
        }

        return Response.ok();
    }

    public Response<?> changeTeamName(@RequestParam("actId") long actId,
                                      @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                      @RequestParam(name = "teamId") long teamId,
                                      @RequestParam(name = "teamName") String teamName) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        if (StringUtil.isBlank(teamName)) {
            return Response.fail(402, "队伍名称不能为空");
        }

        AovPhaseTeam team = aovPhaseTeamService.selectById(teamId);
        if (team == null || team.getCreator() != uid) {
            return Response.fail(403, "您不是队长，无权执行此操作");
        }

        if (AovConst.TeamNameStatus.INIT == Convert.toInt(team.getTeamNameAuditState(), 0)) {
            return Response.fail(404, "已存在待审核中队伍名称");
        }

        try {
            boolean res = aovPhaseTeamService.changeTeamName(getComponentAttr(actId, cmptInx), teamId, uid, teamName);
            if (!res) {
                throw new RuntimeException("changeTeamName failed");
            }
        } catch (SuperException e) {
            log.warn("changeTeamName fail,uid:{},teamName:{},e:{}", uid, teamName, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("changeTeamName fail,uid:{},teamName:{},e:{}", uid, teamName, e.getMessage(), e);
            return Response.fail(500, "网络超时，请重试");
        }
        log.info("changeTeamName teamId:{},uid:{},teamName:{}", teamId, uid, teamName);
        return Response.ok();
    }

    @GetMapping("queryPageWhitelist")
    public PageResponse<AovWhitelistInfo> queryPageWhitelist(@RequestParam("actId") long actId,
                                                             @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                                             @RequestParam(name = "phaseId", required = false) Long phaseId,
                                                             @RequestParam(name = "page", required = false, defaultValue = "1") int page,
                                                             @RequestParam(name = "pageSize", required = false, defaultValue = "10") int pageSize) {
        long loginUid = getLoginYYUid();
        if (loginUid <= 0) {
            return PageResponse.fail(401, "login is need");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return PageResponse.fail(400, "activity not exist");
        }

        if (attr.getWhitelistManagerUids() == null || !attr.getWhitelistManagerUids().contains(loginUid)) {
            return PageResponse.fail(403, "您没有权限");
        }

        if (page < 1) {
            page = 1;
        }

        if (pageSize < 1) {
            pageSize = 1;
        }

        if (pageSize >  100) {
            pageSize = 100;
        }

        Long inviter = loginUid;
        boolean isSuper = Const.GEPM.getParamValue("aov_whitelist_super", ",50018033,").contains("," + loginUid + ",");
        if (isSuper) {
            inviter = null;
        }

        return aovWhitelistService.queryPageWhitelist(actId, phaseId, inviter, page, pageSize);
    }

    /**
     * 添加白名单
     * @param actId
     * @param cmptInx
     * @param yys
     * @return
     */
    @PostMapping("addWhitelist")
    public Response<AddWhitelistRsp> addWhitelist(@RequestParam("actId") long actId,
                                    @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                    @RequestParam(name = "phaseId", required = false, defaultValue = "0") long phaseId,
                                    @RequestBody  List<Long> yys) {

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        if (attr.getWhitelistManagerUids() == null || !attr.getWhitelistManagerUids().contains(uid)) {
            return Response.fail(403, "您没有权限");
        }

        if (CollectionUtils.isEmpty(yys)) {
            return Response.success(new AddWhitelistRsp());
        }

        if (yys.size() > MAX_BATCH_SIZE) {
            return Response.fail(400, "每次最多只能添加" + MAX_BATCH_SIZE + "个YY号");
        }

        if (phaseId > 0) {
            AovPhase phase = aovPhaseMapper.selectByPhaseId(phaseId);
            if (phase == null || phase.getActId() != actId) {
                return Response.fail(400, "阶段ID有误");
            }

            Date now =commonService.getNow(actId);
            if (now.after(phase.getSignupEndTime())) {
                return Response.fail(400, "报名已截止，不能再添加白名单");
            }
        }

        Map<Long, Long> uidMap = webdbUinfoClient.getUidByYyno(yys);
        if (MapUtils.isEmpty(uidMap)) {
            return Response.success(new AddWhitelistRsp(0, yys.size(), yys));
        }

        List<Long> failList = new ArrayList<>(yys.size());
        List<Long> addUids = new ArrayList<>(yys.size());
        for (long yy : yys) {
            Long addUid = uidMap.get(yy);
            if (addUid == null || addUid <= 0) {
                failList.add(yy);
                continue;
            }

            addUids.add(addUid);
        }

        if (!addUids.isEmpty()) {
            aovWhitelistService.batchAddWhitelist(actId, phaseId, uid, addUids);
        }

        return Response.success(new AddWhitelistRsp(addUids.size(), failList.size(), failList));
    }

    @DeleteMapping("deleteWhitelist")
    public Response<?> deleteWhitelist(@RequestParam("actId") long actId,
                                       @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx,
                                       @RequestParam(name = "phaseId", required = false) Long phaseId,
                                       @RequestParam("uid") long uid) {
        long loginUid = getLoginYYUid();
        if (loginUid <= 0) {
            return Response.fail(401, "login is need");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        if (attr.getWhitelistManagerUids() == null || !attr.getWhitelistManagerUids().contains(uid)) {
            return Response.fail(403, "您没有权限");
        }

        Long inviter = loginUid;
        boolean isSuper = Const.GEPM.getParamValue("aov_whitelist_super", ",50018033,").contains("," + loginUid + ",");
        if (isSuper) {
            inviter = null;
        }

        aovWhitelistService.deleteWhitelist(actId, phaseId, inviter, uid);

        return Response.ok();
    }

    public AovPhase findAovPhase(long actId, Long phaseId) {
        Date now = commonService.getNow(actId);
        final AovPhase aovPhase;
        if (phaseId != null && phaseId > 0) {
            aovPhase = aovPhaseMapper.selectByPhaseId(phaseId);
        } else {
            aovPhase = aovPhaseMapper.selectProcessingPhase(now, actId, null);
        }
        return aovPhase == null || now.after(aovPhase.getEndTime()) ? null : aovPhase;
    }

    private Response<?> checkParam(long uid, long actId, long cmptInx, AovPhase aovPhase) {
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);
        if(attr == null) {
            return Response.fail(-1, "参数错误");
        }
        if(aovPhase == null){
            return Response.fail(-1, "活动还没开始");
        }
        return Response.ok();
    }

    private Response<?> checkRisk(long uid, long actId, long cmptInx, HttpServletRequest request, String verifyCode, String recordId, String verifyToken) {
        log.info("uid:{} request:{}", uid, JsonUtil.toJson(request));
        String mobileHash = userinfoThriftClient.getMobileHash(uid);
        if (StringUtils.isEmpty(mobileHash)) {
            return Response.fail(433, "未绑定手机号，请绑定后重试");
        }

        AovTeamComponentAttr attr = getComponentAttr(actId, cmptInx);

        // 判断是否黑名单
        if (attr.getBlacklistComponentIndex() > 0) {
            boolean inBlacklist = whitelistComponent.inWhitelist(actId, attr.getBlacklistComponentIndex(), String.valueOf(uid));
            if (inBlacklist) {
                return Response.fail(466, attr.getBlacklistTips());
            }
        }

        if (attr.isUseWhitelist()) {
            boolean inWhitelist = aovWhitelistService.inWhitelist(actId, uid);
            if (!inWhitelist) {
                return Response.fail(467, attr.getWhitelistTips());
            }
        }

        try {
            zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);
        } catch (SuperException e) {
            JoinGameResult joinGameResult = new JoinGameResult();
            joinGameResult.setRiskRecheck(e.getData());
            return Response.fail(e.getCode(), e.getMessage(), joinGameResult);
        }

        if (attr.getMobileUidLimit() > 0) {
            boolean canMobileSignUp = aovEnrollmentService.canMobileSignUp(uid, mobileHash, attr.getMobileUidLimit(), "参与活动：[" + actId + "]");
            if (canMobileSignUp) {
                return Response.ok();
            }
        }

        return Response.fail(465, "超过单手机号最大参赛账号数(>" + attr.getMobileUidLimit() + ")，无法参赛（手机号以首次参赛绑定的用户为准，禁止注销、换绑）");
    }

    @Getter
    @Setter
    public static class AddWhitelistRsp {
        protected int successCnt;

        protected int failCnt;

        protected List<Long> failList;

        public AddWhitelistRsp() {
        }

        public AddWhitelistRsp(int successCnt, int failCnt, List<Long> failList) {
            this.successCnt = successCnt;
            this.failCnt = failCnt;
            this.failList = failList;
        }
    }
}
