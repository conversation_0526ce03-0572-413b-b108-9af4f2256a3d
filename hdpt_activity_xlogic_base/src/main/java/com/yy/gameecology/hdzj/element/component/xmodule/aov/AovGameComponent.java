package com.yy.gameecology.hdzj.element.component.xmodule.aov;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.service.aov.game.AovGameService;
import com.yy.gameecology.activity.service.aov.game.AovGameSettleService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.aov.AovGameInfo;
import com.yy.gameecology.hdzj.bean.aov.AovLayerInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AovGameComponentAttr;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("5118")
public class AovGameComponent extends BaseActComponent<AovGameComponentAttr> {

    public static final Set<Integer> CLOSE_GAME_STATES = Set.of(AovConst.GameState.CANCEL, AovConst.GameState.RESULTED, AovConst.GameState.NO_RESULT);

    @Autowired
    private AovGameService aovGameService;

    @Autowired
    private AovGameSettleService aovGameSettleService;

    @Override
    public Long getComponentId() {
        return ComponentId.AOV_GAME;
    }

    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 2000, fixedDelay = 6000)
    public void createRemoteGames() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovGameComponentAttr attr = tryGetUniqueComponentAttr(actId);

            aovGameSettleService.createRemoteGames(attr, commonService.getNow(actId));
        }
    }

    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 2000, fixedDelay = 30000)
    public void checkSaibaoRoomReady() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovGameComponentAttr attr = tryGetUniqueComponentAttr(actId);

            aovGameSettleService.checkSaibaoRoomReady(attr, commonService.getNow(actId));
        }
    }

    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 2000, fixedDelay = 6000)
    public void checkShuttingGames() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovGameComponentAttr attr = tryGetUniqueComponentAttr(actId);

            aovGameSettleService.checkShuttingGames(attr, commonService.getNow(actId));
        }
    }

    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 2000, fixedDelay = 30000)
    public void queryGamesResult() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovGameComponentAttr attr = tryGetUniqueComponentAttr(actId);

            aovGameSettleService.queryGamesResult(attr, commonService.getNow(actId));
        }
    }

    /**
     * 将已经出了结果的game的结果结算到node
     */
    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 2000, fixedDelay = 6000)
    public void settleResultedGames() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovGameComponentAttr attr = tryGetUniqueComponentAttr(actId);

            Date now = commonService.getNow(actId);
            aovGameSettleService.settleResultedGames(attr, now);
        }
    }

    @GetMapping("queryGames")
    public Response<List<AovGameInfo>> queryMatches(@RequestParam(name = "actId") int actId,
                                                    @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                    @RequestParam(name = "phaseId") long phaseId,
                                                    @RequestParam(name = "teamId") long teamId) {
        AovGameComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        List<AovGameInfo> matchInfos = aovGameService.queryMatches(phaseId, teamId, commonService.getNow(actId));

        return Response.success(matchInfos);
    }

    /**
     * 查询挂件是否展示
     * @param seq
     * @param actId
     * @param sid
     * @param ssid
     * @return
     */
    @GetMapping("queryLayerStatus")
    public Response<Map<String, Object>> queryLayerStatus(@RequestParam("seq") String seq,
                                                          @RequestParam(value = "actId", defaultValue = "0") long actId,
                                                          @RequestParam(value = "sid", defaultValue = "0") long sid,
                                                          @RequestParam(value = "ssid", defaultValue = "0") long ssid) {
        if (actId == 0 || sid == 0 || ssid == 0) {
            return new Response<>(Code.E_DATA_ERROR.getCode(), "para error");
        }

        AovGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(Code.E_NO_ACTIVITY_OR_END.getCode(), "activity not exist");
        }

        boolean existingGame = aovGameService.existMatch(attr, sid, ssid, commonService.getNow(actId));

        Map<String, Object> data = new HashMap<>(3);
        data.put("showStatus", existingGame ? 1 : 0);
        data.put("time", System.currentTimeMillis());
        data.put("grey", commonService.isGrey(actId));

        return Response.success(data);
    }

    @GetMapping("queryLayerInfo")
    public Response<AovLayerInfo> queryChannelLayerInfo(@RequestParam(name = "actId") int actId,
                                                        @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                        @RequestParam(name = "sid") long sid,
                                                        @RequestParam(name = "ssid") long ssid) {
        AovGameComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        return aovGameService.queryChannelLayerInfo(attr, uid, sid, ssid, commonService.getNow(actId));
    }

    @RequestMapping("jumpGame")
    public Response<String> getJumpGameUrl(@RequestParam("actId") long actId, @RequestParam("gameId") long gameId) {
        AovGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        if (!commonService.checkWhiteList(actId, RoleType.USER, String.valueOf(uid))) {
            return Response.fail(403, "暂无资格参与");
        }

        return aovGameService.getJumpGameUrl(attr, uid, gameId, commonService.getNow(actId));
    }

    @GetMapping("getLiveRoomInfo")
    public Response<String> getLiveRoomInfo(@RequestParam("actId") long actId, @RequestParam("gameId") long gameId) {
        var attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid != 50018033) {
            return Response.fail(403, "you have not right to get the live room info");
        }

        return aovGameService.getLiveRoomInfo(attr, gameId);
    }
}
