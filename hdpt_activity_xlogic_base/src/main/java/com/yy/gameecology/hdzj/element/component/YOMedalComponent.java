package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSet;
import com.yy.gameecology.activity.bean.GiftSourceChannel;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt1016UserMedal;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.UserMedalInfo;
import com.yy.gameecology.hdzj.bean.YOAppMedalComponentMedaInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.YOMedalComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.YOMedalDao;
import com.yy.gameecology.hdzj.element.component.service.YOMedalService;
import com.yy.protocol.pb.GameecologyActivity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("1016")
public class YOMedalComponent extends BaseActComponent<YOMedalComponentAttr> {

    @Autowired
    private YOMedalDao yoMedalDao;

    @Autowired
    private YOMedalService yoMedalService;

    @Override
    public Long getComponentId() {
        return ComponentId.YO_MEDAL_MYSQL;
    }

    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLogin(ZhuiwanLoginEvent loginEvent, YOMedalComponentAttr attr) {
        final long actId = attr.getActId(), uid = loginEvent.getUid();
        String app = loginEvent.getApp();

        if (!attr.getApps().contains(app)) {
            log.info("onZhuiwanLogin app:{} not match!", app);
            return;
        }

        boolean result = yoMedalService.tryGrantMedal(attr, uid, attr.getTAwardPkgSeniorId(), 2, commonService.getNow(actId));

        log.info("onZhuiwanLogin success with uid:{}, app:{}, packageId:{} result:{}", uid, app, attr.getTAwardPkgSeniorId(), result);

    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent sendGiftEvent, YOMedalComponentAttr attr) {
        final long actId = attr.getActId(), uid = sendGiftEvent.getSendUid();

        // 只处理PC消息
        if (!GiftSourceChannel.PC.equals(sendGiftEvent.getSourceChannel())) {
            log.info("not pc send gift,uid={}", uid);
            return;
        }

        final String giftId = sendGiftEvent.getGiftId();
        Set<String> giftIds = ImmutableSet.copyOf(attr.getGiftIds());
        if (!giftIds.contains(giftId)) {
            return;
        }

        long packageId = attr.getTAwardPkgPrimaryId();
        boolean result = yoMedalService.tryGrantMedal(attr, uid, packageId, 1, commonService.getNow(actId));

        if (!result) {
            return;
        }

        YOAppMedalComponentMedaInfo medalInfo = attr.getTAwardPkgInfos().get(packageId);
        YOAppMedalComponentMedaInfo highMedalInfo = attr.getTAwardPkgInfos().get(attr.getTAwardPkgSeniorId());
        YOAppMedalComponentMedaInfo expertMedalInfo = attr.getTAwardPkgInfos().get(attr.getTAwardPkgExpertId());
        JSONObject json = new JSONObject(15);
        json.put("medalLevel", "primary");
        json.put("medalName", medalInfo.getMedalName());
        json.put("medalPic", medalInfo.getMedalPic());
        json.put("packageId", packageId);
        json.put("highMedalName", highMedalInfo.getMedalName());
        json.put("highMedalPic", highMedalInfo.getMedalPic());
        json.put("qrCode", medalInfo.getQrCode());

        if (expertMedalInfo != null) {
            json.put("expertMedalName", expertMedalInfo.getMedalName());
            json.put("expertMedalPic", expertMedalInfo.getMedalPic());
            json.put("expertMedalGift", expertMedalInfo.getQrCode());
        }

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType("medal_grant_primary")
                .setExtJson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        String seq = makeKey(attr, "primary_notice:" + uid);
        RetryTool.withRetryCheck(actId, seq, () -> {
            svcSDKService.unicastUid(uid, msg);
            log.info("onSendGiftEvent success with uid:{}, packageId:{}", uid, packageId);
        });
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void handleRankingScoreChanged(RankingScoreChanged event, YOMedalComponentAttr attr) {
        if (attr.getTAwardPkgExpertId() <= 0) {
            return;
        }

        if (event.getRankId() != attr.getExpertRankId() || event.getPhaseId() != attr.getExpertPhaseId()) {
            return;
        }

        if (event.getRankScore() < attr.getThreshold()) {
            return;
        }

        if (event.getRankScore() - event.getItemScore() > attr.getThreshold()) {
            log.warn("handleRankingScoreChanged skip overload score score:{}, itemScore:{}", event.getRankScore(), event.getItemScore());
            return;
        }

        long actId = attr.getActId(), uid = Long.parseLong(event.getMember()), packageId = attr.getTAwardPkgExpertId();
        boolean result = yoMedalService.tryGrantMedal(attr, uid, packageId, 3, commonService.getNow(attr.getActId()));
        if (!result) {
            log.warn("handleRankingScoreChanged tryGrantMedal failed, uid:{}, score:{}", uid, event.getRankScore());
            return;
        }

        YOAppMedalComponentMedaInfo medalInfo = attr.getTAwardPkgInfos().get(packageId);
        JSONObject json = new JSONObject(10);
        json.put("medalLevel", "expert");
        json.put("medalName", medalInfo.getMedalName());
        json.put("medalPic", medalInfo.getMedalPic());

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType("medal_grant_expert")
                .setExtJson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        String seq = makeKey(attr, "expert_notice:" + uid);
        RetryTool.withRetryCheck(actId, seq, () -> {
            svcSDKService.unicastUid(uid, msg);
            log.info("grantExpertMedal success with uid:{}, packageId:{}", uid, packageId);
        });
    }

    @GetMapping("/queryMyMedalInfo")
    public Response<UserMedalInfo> queryMyMedalInfo(@RequestParam(name = "actId") long actId, @RequestParam(name = "cmptInx") long cmptInx) {
        long uid = getLoginYYUid();

        YOMedalComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "Invalid actId!");
        }

        final long primaryPackageId = attr.getTAwardPkgPrimaryId();
        final long seniorPackageId = attr.getTAwardPkgSeniorId();
        final long expertPackageId = attr.getTAwardPkgExpertId();

        UserMedalInfo userMedalInfo = new UserMedalInfo();
        userMedalInfo.setAppName(attr.getAppName());
        //添加礼物icon、勋章资源信息
        userMedalInfo.setPrimaryMedal(attr.getTAwardPkgInfos().get(primaryPackageId));
        userMedalInfo.setSeniorMedal(attr.getTAwardPkgInfos().get(seniorPackageId));
        userMedalInfo.setExpertMedal(attr.getTAwardPkgInfos().get(expertPackageId));
        List<String> giftIcons = attr.getGiftIds().stream()
                .map(giftId -> attr.getGiftIcons().get(giftId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        userMedalInfo.setGiftIcons(giftIcons);

        Cmpt1016UserMedal userMedal = yoMedalDao.selectUserMedal(attr.getActId(), uid);
        if (userMedal != null) {
            switch (userMedal.getLevel()) {
                case 1 -> userMedalInfo.setMedalLevel("primary");
                case 2 -> userMedalInfo.setMedalLevel("senior");
                case 3 -> userMedalInfo.setMedalLevel("expert");
                default -> {}
            }
        }

        return Response.success(userMedalInfo);
    }

    @GetMapping("/clearMyMedalInfo")
    public Response<?> clearMyMedalInfo(@RequestParam(name = "actId") long actId) {
        if (SysEvHelper.isDeploy()) {
            return Response.ok("线上环境不允许操作");
        }
        long uid = getLoginYYUid();
        YOMedalComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            return Response.fail(400, "Invalid actId!");
        }

        int rs = yoMedalDao.deleteUserMedal(attr.getActId(), uid);
        log.info("clearMyMedalInfo success with uid:{}, rs:{}", uid, rs);

        return Response.ok();
    }

}
