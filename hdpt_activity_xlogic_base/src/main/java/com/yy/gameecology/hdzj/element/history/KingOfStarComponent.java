package com.yy.gameecology.hdzj.element.history;

import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.service.BigDataService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.KingOfStarComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2023/6/21 15:21
 **/
@UseRedisStore
@Component
@RequestMapping("/cmpt")
@RestController
public class KingOfStarComponent extends BaseActComponent<KingOfStarComponentAttr> {
    private static final String SUBSCRIBE_KEY = "subscribe";

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    protected BigDataService bigDataService;

    @Override
    public Long getComponentId() {
        return ComponentId.KING_OF_STAR;
    }

    /**
     * 获取赛事列表
     **/
    @RequestMapping("listCompetition")
    public Response<ListCompetitionResp> listCompetition(HttpServletRequest req, HttpServletResponse resp, long actId, long useIndex) {
        // 同时返回预约状态
        long uid = getLoginYYUid(req, resp);
        KingOfStarComponentAttr attr = getComponentAttr(actId, useIndex);
        if (attr == null) {
            return Response.fail(Code.E_DATA_ERROR.getCode(), Code.E_DATA_ERROR.getReason());
        }

        String key = makeKey(attr, SUBSCRIBE_KEY);
        String groupCode = getRedisGroupCode(actId);
        String subscribeTime = actRedisDao.hget(groupCode, key, uid + "");
        boolean hasSubscribe = StringUtil.isNotBlank(subscribeTime);

        ListCompetitionResp listCompetitionResp = new ListCompetitionResp();
        listCompetitionResp.setHasSubscribe(hasSubscribe);
        listCompetitionResp.setCanSubscribe(CollectionUtils.isNotEmpty(attr.listNoStartCompetition()));

        // 列表排序：当前赛事 > 赛事预告 > 精彩回放
        attr.getCompetitionList().sort(Comparator.comparingInt(KingOfStarComponentAttr.CompetitionInfo::getStatus).reversed());
        List<CompetitionVo> competitionVoList = new ArrayList<>();
        long now = commonService.getNow(actId).getTime();
        attr.getCompetitionList().forEach(competition -> {
            CompetitionVo vo = new CompetitionVo();
            vo.setStartTime(DateUtil.getDate(competition.getStartTime(), DateUtil.DEFAULT_PATTERN).getTime());
            vo.setStatus(competition.getStatus());
            //2:进行中;1:待开始;0:已结束，如果配置成-1，则用开始和结束时间自动判断状态
            boolean autoSetStatus = competition.getStatus() == Const.MIN_ONE
                    && StringUtil.isNotBlank(competition.getStartTime())
                    && StringUtil.isNotBlank(competition.getEndTime());
            if (autoSetStatus) {
                long startTime = DateUtil.getDate(competition.getStartTime(), DateUtil.DEFAULT_PATTERN).getTime();
                long endTime = DateUtil.getDate(competition.getEndTime(), DateUtil.DEFAULT_PATTERN).getTime();
                if (now < startTime) {
                    vo.setStatus(1);
                } else if (now <= endTime) {
                    vo.setStatus(2);
                } else {
                    vo.setStatus(0);
                }
            }
            vo.setPlaybackLink(competition.getPlaybackLink());
            vo.setSid(competition.getSid());
            vo.setSsid(competition.getSsid());
            vo.setMember(new Member(competition.getUid(), competition.getNickname(), competition.getAvatar(), competition.getScore()));
            vo.setTargetMember(new Member(competition.getTargetUid(), competition.getTargetNickname(), competition.getTargetAvatar(), competition.getTargetScore()));

            competitionVoList.add(vo);
        });
        Collections.sort(competitionVoList, new Comparator<CompetitionVo>() {
            @Override
            public int compare(CompetitionVo o1, CompetitionVo o2) {
                if (o1.getStatus() != o2.getStatus()) {
                    return o2.getStatus() - o1.getStatus();
                } else {
                    return (int) (o1.getStartTime() - o2.getStartTime());  // 按照字段a进行升序排序
                }
            }
        });
        listCompetitionResp.setCompetitionVoList(competitionVoList);
        boolean show = false;
        if(competitionVoList.size() > 0) {
            for (CompetitionVo competitionVo : competitionVoList) {
                if(competitionVo.getStartTime() < now) {
                    show = true;
                    break;
                }
            }
        }
        listCompetitionResp.setShow(show);
        return Response.success(listCompetitionResp);
    }

    /**
     * 赛事订阅
     **/
    @RequestMapping("subscribeCompetition")
    public Response<String> subscribeCompetition(HttpServletRequest req, HttpServletResponse resp, long actId, long useIndex) {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(Code.E_NOT_LOGIN.getCode(), Code.E_NOT_LOGIN.getReason());
        }

        KingOfStarComponentAttr attr = getComponentAttr(actId, useIndex);
        if (attr == null) {
            return Response.fail(Code.E_DATA_ERROR.getCode(), Code.E_DATA_ERROR.getReason());
        }

        boolean isActStart = commonService.isActStart(actId);
        if (!isActStart) {
            return Response.fail(-1, "不在活动时间内");
        }

        // 记录预约状态
        String key = makeKey(attr, SUBSCRIBE_KEY);
        String groupCode = getRedisGroupCode(actId);

        Date now = commonService.getNow(actId);
        String time = DateUtil.format(now);
        boolean subscribe = actRedisDao.hsetnx(groupCode, key, uid + "", time);
        if (!subscribe) {
            return Response.success("预约成功");
        }

        // 找出所有未开始的赛事
        List<KingOfStarComponentAttr.CompetitionInfo> notStartList = attr.listNoStartCompetition();
        if (CollectionUtils.isEmpty(notStartList)) {
            log.info("there is no competition to start");
            return Response.fail(-1,"没有待开始的赛事");
        }
        // 调用追玩接口插入定时push数据
        notStartList.forEach(competitionInfo -> zhuiWanPrizeIssueServiceClient.sendTimingPush(attr.getFromId(), DateUtil.getDate(competitionInfo.getStartTime())
                , attr.getTitle(), attr.getContent(), attr.getImage(), attr.getLink(), attr.getFromId(), uid + ""));
        bigDataService.saveNoRankDataToFile(actId, BusiId.ZHUI_WAN, now.getTime(), uid + "", RoleType.USER, 1, 99);

        log.info("subscribeCompetition done,actId:{},uid:{}", actId, uid);

        return Response.success("预约成功");
    }

    /**
     * 赛事数据
     **/
    @Data
    private static class CompetitionVo {
        /**
         * 开始时间
         **/
        private long startTime;

        /**
         * 状态 2 进行中 1 待开始 0 已结束
         **/
        private int status;
        /**
         * 回放链接,可能没有,有配置则显示回放按钮
         **/
        private String playbackLink;

        /**
         * 直播时的频道信息
         **/
        private long sid;
        private long ssid;

        private Member member;
        private Member targetMember;
    }

    @Data
    private static class ListCompetitionResp {
        private boolean show;
        private boolean hasSubscribe;
        /**
         * true 可以订阅,当不存在未开始的赛事时,不可订阅
         **/
        private boolean canSubscribe;
        private List<CompetitionVo> competitionVoList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class Member {
        private long uid;
        private String nickname;
        private String avatar;
        private int score;
    }
}
