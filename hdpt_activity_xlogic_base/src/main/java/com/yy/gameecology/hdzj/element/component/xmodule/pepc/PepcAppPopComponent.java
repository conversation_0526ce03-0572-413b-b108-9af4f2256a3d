package com.yy.gameecology.hdzj.element.component.xmodule.pepc;

import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.service.pepc.PepcPhaseInfoService;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMemberMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeamMember;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PepcAppPopComponentAttr;
import com.yy.thrift.hdztranking.RoleType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Component
@RestController
@RequestMapping("/5145")
public class PepcAppPopComponent extends BaseActComponent<PepcAppPopComponentAttr> {
    @Override
    public Long getComponentId() {
        return ComponentId.PEPC_APP_POP;
    }

    @Autowired
    private PepcPhaseInfoService pepcPhaseInfoService;

    @Autowired
    private PepcTeamMemberMapper pepcTeamMemberMapper;

    @GetMapping("/getAppPopupUrl")
    public Response<HomePopupVo> getAppPopupUrl(@RequestParam(name = "actId") long actId,
                                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                @RequestParam(name = "uid") long uid) {
        PepcAppPopComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        log.info("getAppPopupUrl with uid:{}", uid);
        Date now = commonService.getNow(actId);

        Boolean inSignUpTime = pepcPhaseInfoService.InSignUpTime(actId, now);
        if (!inSignUpTime) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        //灰度状态，不在灰度白名单内不弹窗
        if (commonService.isGrey(actId)) {
            boolean inWhiteList = commonService.checkWhiteList(actId, RoleType.USER, Convert.toString(uid));
            if (!inWhiteList) {
                log.info("getAppPopupUrl not in white list,actId:{},uid:{}", actId, uid);
                return Response.success(new HomePopupVo(StringUtils.EMPTY));
            }
        }

        PepcTeamMember member = pepcTeamMemberMapper.selectByUniq(actId, uid);
        if (member != null) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }
        log.info("getAppPopupUrl popup record with uid:{} pop:{}", uid, attr.getAppPopupUrl());

        return Response.success(new HomePopupVo(attr.getAppPopupUrl()));
    }

    @Data
    public static class HomePopupVo {
        protected String popupLink;

        public HomePopupVo() {
        }

        public HomePopupVo(String popupLink) {
            this.popupLink = popupLink;
        }
    }
}
