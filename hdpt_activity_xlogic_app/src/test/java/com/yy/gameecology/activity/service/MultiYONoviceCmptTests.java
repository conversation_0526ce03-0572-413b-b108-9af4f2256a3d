package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.hdzj.element.redis.MultiYONovicePackageComponent;
import com.yy.gameecology.hdzj.element.component.attr.MultiYONovicePackageComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=7"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-7.properties"
})
public class MultiYONoviceCmptTests {

    @Autowired
    private MultiYONovicePackageComponent component;

    static {
        System.setProperty("group", "1");
    }

    @Test
    public void testEnterTemplate() {
        MultiYONovicePackageComponentAttr attr = component.getComponentAttr(2023087001L, 200L);
        UserEnterTemplateEvent event = new UserEnterTemplateEvent();
        event.setUid(50073762);
        event.setSid(87814665L);
        event.setSsid(87814665L);
        event.setTemplate(3);
        event.setBusiId(500);
//        component.onUserEnterTemplate(event, attr);
    }

    @Test
    public void testZhuiwanLogin() {
        MultiYONovicePackageComponentAttr attr = component.getComponentAttr(2023087001L, 200L);
        ZhuiwanLoginEvent event = new ZhuiwanLoginEvent();
        event.setUid(50018033);
        event.setHdid("5d1a6030084bee36f4641dc4fc82d431");
        event.setFirstLogin(true);
        event.setClientType(1);
        event.setApp("zhuiwan");

        component.onZhuiwanLoginEvent(event, attr);
    }
}
