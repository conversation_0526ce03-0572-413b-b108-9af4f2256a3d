package com.yy.gameecology.activity.bean.mq;

import lombok.Data;

/**
 * desc:追玩口令上报事件
 *
 * <AUTHOR>
 * @date 2025-01-07 16:12
 **/
@Data
public class ActShareSuccessEvent {

    /**
     * 事件ID
     */
    private String seq;
    /**
     * 时间戳(秒)
     */
    private long timestamp;


    private String app;

    /**
     * 分享者
     */
    private Long shareUid;

    /**
     * 被邀请者，客户端没登陆就上报，uid可能是0
     */
    private long uid;

    /**
     * 被邀请者设备id
     */
    private String hdid;

    /**
     * 被邀请者IP
     */
    private String ip;

    /**
     * 扩展字段
     */
    private String extJson;


    /**
     * 口令
     */
    private String shareToken;

}
