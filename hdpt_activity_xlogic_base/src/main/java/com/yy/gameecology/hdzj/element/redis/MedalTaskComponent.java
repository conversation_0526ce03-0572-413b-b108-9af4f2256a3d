package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.MedalTaskComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 勋章组件
 *
 * <AUTHOR>
 * @date 2021/15 14:25
 */
@UseRedisStore
@Component
public class MedalTaskComponent extends BaseActComponent<MedalTaskComponentAttr> {


    @Autowired
    private SvcSDKService svcSDKService;
    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private BroadCastHelpService broadCastHelpService;
    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;
    @Autowired
    private CommonService commonService;

    /**
     * 用户勋章等级
     */
    public static final String USER_GIFT_MEDAL = "%s_user_gift_medal";


    @Override
    public Long getComponentId() {
        return ComponentId.MEDAL_TASK_V2;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onTaskProgressChanged(TaskProgressChanged event, MedalTaskComponentAttr attr) {
        if (!ArrayUtils.contains(attr.getBusiIds(), event.getBusiId()) || event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        BusiId busiId = BusiId.findByValue((int) event.getBusiId());
        long uid = Convert.toLong(event.getMember());
        long score = 0;
        for (String giftItem : attr.getGiftItems()) {
            score += Convert.toLong(event.getItemCurrNumMap().get(giftItem));
        }
        score = getScore(attr, score);
        //通过分数获取等级
        Map.Entry<Long, Integer> entry = ImmutableSortedMap.copyOf(reverseMap(attr.getLevelScoreMap())).floorEntry(score);

        Assert.notNull(entry, String.format("config error,not cat level by score,actId:{},cmptId:{},cmptUseInx:{},score:{}"
                , attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), score));

        int curLevel = entry.getValue();

        //查询下一级勋章，null说明本身是最高级了
        Long nextLevelScore = attr.getLevelScoreMap().get(curLevel + 1);
        long diff = nextLevelScore != null ? nextLevelScore - score : 0L;

        tipMedalUpdate(attr, uid, curLevel, diff, busiId);
        log.info("MedalTaskV2Component onTaskProgressChanged done -> event:{}, attr:{}", event, attr);
    }

    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = false)
    public void onHdztAwardLotteryMsg(HdztAwardLotteryMsg event, MedalTaskComponentAttr attr) {
        long uid = event.getUid();
        Map<String, Integer> taskPackageLevelMap = attr.getTaskPackageLevelMap();
        if (CollectionUtils.isEmpty(taskPackageLevelMap) || !ArrayUtils.contains(attr.getBusiIds(), event.getBusiId())) {
            return;
        }
        BusiId busiId = BusiId.findByValue((int) event.getBusiId());
        List<Integer> levels = event.getData().stream()
                .map(award -> taskPackageLevelMap.get(event.getTaskId() + "_" + award.getPackageId()))
                .filter(Objects::nonNull)
                .distinct().sorted().collect(Collectors.toList());

        if (levels.isEmpty()) {
            return;
        }

        int curLevel = MyListUtils.last(levels);

        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), "", uid + "", Maps.newHashMap());

        long score = Math.max(rank.getScore(), 0L);

        score = getScore(attr, score);

        //查询下一级勋章，null说明本身是最高级了
        Map.Entry<Integer, Long> entry = ImmutableSortedMap.copyOf(attr.getLevelScoreMap()).higherEntry(curLevel);
        //这次没有对0分数的进行过滤
        long diff = entry != null ? entry.getValue() - score : 0L;
        tipMedalUpdate(attr, uid, curLevel, diff, busiId);
        log.info("onhdztAwardLotteryMsg done -> event:{}, attr:{}", JSON.toJSONString(event), attr);
    }

    /**
     * 查询用户勋章等级
     *
     * @param actId
     * @param index 200 -> 三端同一个
     * @param uid
     * @return
     */
    public JSONObject queryUserMedalInfo(long actId, int index, long uid) {
        MedalTaskComponentAttr attr = getComponentAttr(actId, index);
        JSONObject medalInfo = getMedalInfo(attr, uid);

        JSONObject data = new JSONObject();
        data.put("level", Convert.toLong(medalInfo.getLong("level")));
        data.put("value", Convert.toLong(medalInfo.getLong("score")));
        data.put("total", Convert.toLong(medalInfo.getLong("lastScore")));
        UserBaseInfo userBaseInfo = broadCastHelpService.getUserBaseInfo(uid, null);
        data.put("avatar", userBaseInfo.getLogo());
        data.put("nick", commonService.getNickName(uid, false));
        return data;
    }


    /**
     * 更新用户勋章等级
     *
     * @param attr
     * @param uid
     * @param level
     * @return
     */
    public long updateUserMedalLevelAndReturnOld(MedalTaskComponentAttr attr, long uid, int level) {
        BusiId busiId = attr.getBusiIds().length == 1 ? BusiId.findByValue((attr.getBusiIds()[0]).intValue()) : BusiId.GAME_ECOLOGY;
        String key = makeKey(attr, String.format(USER_GIFT_MEDAL, busiId));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        return actRedisDao.hSetGrownReturnOld(groupCode, key, String.valueOf(uid), level);
    }

    /**
     * 查询用户勋章等级
     *
     * @param attr
     * @param uid
     * @return
     */
    public int queryUserMedalLevel(MedalTaskComponentAttr attr, long uid) {
        BusiId busiId = attr.getBusiIds().length == 1 ? BusiId.findByValue((attr.getBusiIds()[0]).intValue()) : BusiId.GAME_ECOLOGY;
        String key = makeKey(attr, String.format(USER_GIFT_MEDAL, busiId));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String value = actRedisDao.hget(groupCode, key, String.valueOf(uid));
        return StringUtils.isNumeric(value) ? Integer.parseInt(value) : 0;
    }

    /**
     * 勋章升级单播和广播
     *
     * @param attr
     * @param uid
     * @param curLevel
     * @param diff
     */
    private void tipMedalUpdate(MedalTaskComponentAttr attr, long uid, int curLevel, long diff, BusiId busiId) {

        Long actId = attr.getActId();
        //上次的等级（勋章可能有多个发放途径）
        long lastLevel = updateUserMedalLevelAndReturnOld(attr, uid, curLevel);
        long plus = curLevel - lastLevel;
        //被别的发放渠道广播了
        if (plus <= 0) {
            log.info("tipMedalUpdate ignore@uid:{},curLevel:{},diff:{}", uid, curLevel, diff);
            return;
        }
        MedalTaskComponentAttr.Gift gift = attr.getMedalMaterialMap().get(curLevel);
        if (gift == null) {
            log.error("tipMedalUpdate error! medal material not find actId:{} curLevel {}",
                    actId, curLevel);
            return;
        }

        List<GameecologyActivity.GiftInfo> giftInfos = Lists.newArrayList(getGiftInfo(gift));

        List<MedalTaskComponentAttr.Gift> extGifts = attr.getExtGiftsMap().get(curLevel);
        if (!CollectionUtils.isEmpty(extGifts)) {
            List<GameecologyActivity.GiftInfo> extGiftInfos = extGifts.stream().map(this::getGiftInfo)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            giftInfos.addAll(extGiftInfos);
        }
        //向用户单播升级信息
        GameecologyActivity.ActMedalLevelUpTips medalTips = GameecologyActivity.ActMedalLevelUpTips.newBuilder()
                .setActId(actId).setLevel(curLevel).setPlus((int) plus).setDiff((int) diff)
                .addAllGifts(giftInfos).setExtjson(attr.getExtJsonString()).build();

        GameecologyActivity.GameEcologyMsg medalTipsMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kActMedalLevelUpTipsUri_VALUE)
                .setActMedalLevelUpTips(medalTips).build();
        log.info("tipMedalUpdate medal levelUp actId:{} uid:{} level:{} plus:{} diff:{}",
                actId, uid, curLevel, plus, diff);
        svcSDKService.unicastUid(uid, medalTipsMsg);


        //广播用户勋章升级横幅
        Integer broType = attr.getLevelBroTypeMap().get(curLevel);

        if (broType != null) {
            GameecologyActivity.Act202008_UserHonorBanner.Builder userHonorBanner = GameecologyActivity.Act202008_UserHonorBanner.newBuilder()
                    .setActId(actId).setUser(broadCastHelpService.getUserInfo(uid, busiId)).setLevel(curLevel).setName(gift.getName());
            GameecologyActivity.GameEcologyMsg userHonorBannerMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202008_UserHonorBanner_VALUE)
                    .setAct202008UserHonorBanner(userHonorBanner).build();

            broadCastHelpService.broadcast(actId, busiId, broType, 0L, 0L, userHonorBannerMsg);
        }
    }

    private GameecologyActivity.GiftInfo getGiftInfo(MedalTaskComponentAttr.Gift gift) {

        if (gift == null) {
            return null;
        }
        GameecologyActivity.GiftInfo medalInfo = GameecologyActivity.GiftInfo.newBuilder()
                .setName(gift.getName()).setId(gift.getId()).setUrl(gift.getIcon())
                .build();
        return medalInfo;
    }


    public JSONObject getMedalInfo(MedalTaskComponentAttr attr, long uid) {

        Long actId = attr.getActId();

        JSONObject result = new JSONObject();
        //灰度白名单控制
        if (!commonService.checkWhiteList(actId, RoleType.USER, String.valueOf(uid))) {
            return result;
        }

        int curLevel = queryUserMedalLevel(attr, uid);
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), "", uid + "", Maps.newHashMap());
        long score = Math.max(rank.getScore(), 0L);
        score = getScore(attr, score);

        //查询下一级勋章，null说明本身是最高级了
        Map.Entry<Integer, Long> entry = ImmutableSortedMap.copyOf(attr.getLevelScoreMap()).higherEntry(curLevel);
        //这次没有对0分数的进行过滤
        long lastScore = entry != null ? entry.getValue() : attr.getLevelScoreMap().getOrDefault(curLevel, 0L);

        result.put("score", score);
        result.put("level", curLevel);
        result.put("lastScore", lastScore);
        MedalTaskComponentAttr.Gift gift = attr.getMedalMaterialMap().get(curLevel);
        if (gift != null) {
            result.put("medal", gift);
        }

        return result;
    }


    /**
     * 逆转map的 key-value
     *
     * @param map
     * @param <K>
     * @param <V>
     * @return
     */
    private <K, V> Map<V, K> reverseMap(Map<K, V> map) {
        return map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
    }

    private long getScore(MedalTaskComponentAttr attr, long score) {
        if (score > 0 && attr.getScoreMultiple() != 0 && attr.getScoreMultiple() != 1) {
            score = (long) Math.floor(score * attr.getScoreMultiple());
        }
        return score;
    }

}
