package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjComponentCollaborator;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardBean;
import com.yy.gameecology.hdzj.bean.TiGuanGroupDetail;
import com.yy.gameecology.hdzj.bean.TiGuanMemberInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SeedAnchorWithTiGuanComponentAttr;
import com.yy.thrift.hdztranking.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


/**
 * A.种子选手主播组件,在指定阶段选定几个主播作为种子主播,
 * B.挑战种子主播,或者pk种子主播获胜可获得额外的奖励,种子主播获胜也可活动额外奖励
 * C.选定种子主播规则: 总榜topN以及是晋级的主播
 * D.擂主：晋级过来的用户作为擂主,擂主有积分加成
 * E.奖励：
 * 1. 踢馆阶段,从踢馆组件获取对阵信息,计算胜者
 * 2. PK阶段,从中台获取pk对阵信息,计算胜者
 * 3. 给上面的胜者发放奖励
 *
 * <AUTHOR>
 * @date 2022.07.01 18:06
 */
@UseRedisStore
@Component
@RestController
@RequestMapping("/cmpt/seedAnchor")
public class SeedAnchorWithTiGuanComponent extends BaseActComponent<SeedAnchorWithTiGuanComponentAttr> implements ComponentRankingExtHandle<SeedAnchorWithTiGuanComponentAttr> {

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private HdzjComponentCollaborator componentCollaborator;

    @Override
    public Long getComponentId() {
        return ComponentId.SEED_ANCHOR_WITH_TIGUAN;
    }

    /**
     * 奖励记录:用于在榜单中展示 award_issue_record_rankId_phaseId zset{uid:award}
     */
    public static final String AWARD_ISSUE_RECORD = "award_issue_record_%s_%s";

    //1.确定种子主播,
    // 当晋级结束后就可以知道擂主的信息
    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = false)
    public void choseSeedAnchor(PromotTimeEnd event, SeedAnchorWithTiGuanComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        List<String> leizhuPhaseList = attr.getSeedAnchorRankPhaseList();
        String rankPhaseKey = rankId + StringUtil.UNDERSCORE + phaseId;
        if (!leizhuPhaseList.contains(rankPhaseKey)) {
            return;
        }
        long actId = attr.getActId();

        //晋级的主播
        QueryRankingByScoreResponse response = hdztRankService.queryRankingByScore(actId, rankId, phaseId, null, HdztRankType.PROMOT);
        if (response == null || response.code != 0) {
            throw new SuperException("获取晋级名单失败", SuperException.E_UNKNOWN);
        }

        List<String> promotUids = new ArrayList<>();
        for (RankingByScoreInfo info : response.rankingByScoreInfos) {
            promotUids.add(info.getMemberId());
        }

        Map<String, String> phase2Total = attr.getSeedPhase2Total();

        String total = phase2Total.get(rankPhaseKey);

        int count = attr.getSeedAnchorCount();

        if (StringUtil.isBlank(total)) {
            throw new SuperException("未找到对应的主播总榜配置", SuperException.E_UNKNOWN);
        }
        String[] rankPhase = total.split(StringUtil.UNDERSCORE);

        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, Convert.toLong(rankPhase[0]), Convert.toLong(rankPhase[1]),
                "", 100, Maps.newHashMap());
        int i = 0;

        List<String> seedAnchorUids = new ArrayList<>();
        for (Rank rank : ranks) {
            if (promotUids.contains(rank.getMember())) {
                i++;
                seedAnchorUids.add(rank.getMember());
            }
            if (i >= count) {
                break;
            }
        }

        String seedAnchorRedisKey = makeKey(attr, String.format(attr.getSeedAnchorListRedisKey(), rankId, phaseId));
        actRedisDao.sBatchAdd(getRedisGroupCode(actId), seedAnchorRedisKey, seedAnchorUids.toArray(new String[0]));

        List<String> leizhuRankPhaseList = attr.getLeizhuRankPhaseList();
        if (!leizhuRankPhaseList.contains(rankPhaseKey)) {
            return;
        }
        String leizhuRedisKey = makeKey(attr, String.format(attr.getLeizhuListRedisKey(), rankId, phaseId));
        actRedisDao.sBatchAdd(getRedisGroupCode(actId), leizhuRedisKey, promotUids.toArray(new String[0]));

        log.info("choseSeedAnchor done seq:{} seedAnchorUid:{} leizhuUid:{}", event.getSeq(), seedAnchorUids, promotUids);
    }

    public boolean isSeedAnchor(SeedAnchorWithTiGuanComponentAttr attr, long rankId, long phaseId, String memberId) {
        String seedAnchorRedisKey = makeKey(attr, String.format(attr.getSeedAnchorListRedisKey(), rankId, phaseId));
        return actRedisDao.sIsMember(getRedisGroupCode(attr.getActId()), seedAnchorRedisKey, memberId);
    }

    public boolean isLeizhu(SeedAnchorWithTiGuanComponentAttr attr, long rankId, long phaseId, String memberId) {
        String leizhuRedisKey = makeKey(attr, String.format(attr.getLeizhuListRedisKey(), rankId, phaseId));
        return actRedisDao.sIsMember(getRedisGroupCode(attr.getActId()), leizhuRedisKey, memberId);
    }

    public Set<String> seedAnchorSet(SeedAnchorWithTiGuanComponentAttr attr, long rankId, long phaseId) {
        String seedAnchorRedisKey = makeKey(attr, String.format(attr.getSeedAnchorListRedisKey(), rankId, phaseId));
        return actRedisDao.sMembers(getRedisGroupCode(attr.getActId()), seedAnchorRedisKey);
    }

    public Set<String> seedAnchorSet(long actId, long cmptUseInx, long rankId, long phaseId) {
        SeedAnchorWithTiGuanComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        String seedAnchorRedisKey = makeKey(attr, String.format(attr.getSeedAnchorListRedisKey(), rankId, phaseId));
        return actRedisDao.sMembers(getRedisGroupCode(attr.getActId()), seedAnchorRedisKey);
    }

    /**
     * 擂主积分加成
     **/
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChangedEvent(RankingScoreChanged event, SeedAnchorWithTiGuanComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        List<String> leizhuRankPhaseList = attr.getLeizhuRankPhaseList();
        if (!leizhuRankPhaseList.contains(rankId + StringUtil.UNDERSCORE + phaseId)) {
            return;
        }
        if (event.getItemId().equals(attr.getBonusGiftId())) {
            return;
        }

        if (isLeizhu(attr, rankId, phaseId, event.getMember())) {
            String seq = "BONUS:" + event.getSeq() + ":" + event.getMember();
            UpdateRankingRequest request = new UpdateRankingRequest();
            request.setBusiId(attr.getBusiId());
            request.setActId(attr.getActId());
            request.setSeq(seq);
            request.setActors(event.getActors());
            request.setItemId(attr.getBonusGiftId());
            request.setCount(event.getItemCount());
            request.setScore(event.getItemScore() * attr.getJfBonusRate() / 100);
            request.setTimestamp(DateUtil.getDate(event.getOccurTime()).getTime());

            //todo 改成多次重试机制 done
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> doUpdateRanking(request, 2));

        }
    }

    private void doUpdateRanking(UpdateRankingRequest request, int retry) {
        Throwable le = null;

        Clock clock = new Clock();
        for (int i = 1; i <= retry + 1; i++) {
            try {
                UpdateRankingResult response = hdztRankingThriftClient.getProxy().updateRanking(request);
                log.info("save done@ try:{}, request:{}, response:{} {}", i, request, response, clock.tag());
                return;
            } catch (Throwable t) {
                log.warn("save exception@inx try:{}, request:{}, err:{} {}", i, request, t.getMessage(), clock.tag(), t);
                le = t;
            }
        }

        if (le != null) {
            log.error("save fail@ try:{}, request:{}, err:{} {}", retry + 1, request, le.getMessage(), clock.tag(), le);
        }
    }

    private long awardNum(long score, long rate, long max) {
        long award = score / 100 * rate / 100;
        return Math.min(award, max);
    }

    /**
     * 种子主播奖励, 依赖踢馆组件的结算
     **/
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEnd(PhaseTimeEnd event, SeedAnchorWithTiGuanComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        List<String> leizhuRankPhaseList = attr.getLeizhuRankPhaseList();
        List<String> seedAnchorRankPhaseList = attr.getSeedAnchorRankPhaseList();
        String rankPhase = rankId + StringUtil.UNDERSCORE + phaseId;
        //todo 是否需要处理的事件 done
        if (!seedAnchorRankPhaseList.contains(rankPhase)) {
            return;
        }

        Map<String, Long> awardUid2Award = Maps.newHashMap();
        Set<String> seedAnchorSet = seedAnchorSet(attr, rankId, phaseId);

        long seedAwardMax = attr.getSeedAwardMax();
        long seedAwardRate = attr.getSeedAwardRate();
        if (seedAwardRate <= 0) {
            return;
        }


        //todo 踢馆 done
        long actId = attr.getActId();
        if (leizhuRankPhaseList.contains(rankPhase)) {
            Long cmptUseInx = attr.getSeedPhase2TiguanInx().get(rankPhase);
            try {
                Map<String, List<TiGuanMemberInfo>> winnerGroupSortedList = (Map<String, List<TiGuanMemberInfo>>) componentCollaborator.callComponent(actId, ComponentId.TI_GUAN, "nowWinnerGroupSortedList", actId, cmptUseInx);
                for (Map.Entry<String, List<TiGuanMemberInfo>> entry : winnerGroupSortedList.entrySet()) {
                    List<TiGuanMemberInfo> list = entry.getValue();
                    String lz = entry.getKey();
                    TiGuanMemberInfo newWinner = list.get(0);
                    if (!seedAnchorSet.contains(lz)) {
                        continue;
                    }
                    if (!newWinner.getMemberId().equals(lz)) {
                        Optional<TiGuanMemberInfo> lzInfo = list.stream().filter(item -> item.getMemberId().equals(lz)).findFirst();
                        if (lzInfo.isPresent()) {
                            Long lzScore = lzInfo.map(TiGuanMemberInfo::getScore).orElse(0L);
                            //平分擂主
                            newWinner = newWinner.getScore() == lzScore ? lzInfo.get() : newWinner;
                        } else {
                            throw new RuntimeException(lz + "擂主分数无法找到，" + JSON.toJSONString(entry));
                        }
                    }
                    awardUid2Award.put(newWinner.getMemberId(), awardNum(newWinner.getScore(), seedAwardRate, seedAwardMax));
                }

                log.info("seedAnchor Award tiguan cal award done@ seq:{} rankId :{} phaseId:{} cmptUseInx:{}", event.getSeq(), rankId, phaseId, cmptUseInx);
            } catch (Exception e) {
                log.error("seedAnchor Award cal award by callComponent error seq:{}  rankId :{} phaseId:{} cmptUseInx:{} msg:{}", event.getSeq(),
                        rankId, phaseId, cmptUseInx, e.getMessage(), e);
            }
        }
        // todo pk done
        if (attr.getPkPhaseIds().contains(phaseId)) {
            //1.获取pk对阵信息
            //2.获取种子主播
            PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, phaseId, "00000000", "", false, true, Maps.newHashMap());
            pkInfo.getPkGroupItems().stream().map(PkGroupItem::getMemberPkItems).flatMap(Collection::stream)
                    .forEach(
                            memberPkItems -> {
                                GroupMemberItem memberItem1 = memberPkItems.get(0);
                                GroupMemberItem memberItem2 = memberPkItems.get(1);
                                if (seedAnchorSet.contains(memberItem1.getMemberId()) || seedAnchorSet.contains(memberItem2.getMemberId())) {
                                    GroupMemberItem awardItem = memberItem1.getRank() < memberItem2.getRank() ? memberItem1 : memberItem2;

                                    awardUid2Award.put(awardItem.getMemberId(), awardNum(awardItem.getScore(), seedAwardRate, seedAwardMax));
                                }
                            });
        }
        String eseq = event.getEkey() + "|" + event.getSeq();

        //记录奖励

        //todo 发奖 done
        String awardDuplicateKey = String.format("seed_award_mark_%s_%s", rankId, phaseId);
        if (actRedisDao.setNX(getRedisGroupCode(actId), makeKey(attr, awardDuplicateKey), DateUtil.getNowYyyyMMddHHmmss())) {
            seedAnchorAward(eseq, attr, awardUid2Award);
            seedAwardRecord(attr, rankId, phaseId, awardUid2Award);
        }
        log.info("seedAnchor Award pk cal award done@ seq:{} rankId :{} phaseId:{}", event.getSeq(), rankId, phaseId);
    }

    private void seedAwardRecord(SeedAnchorWithTiGuanComponentAttr attr, long rankId, long phaseId, Map<String, Long> awardUid2Score) {
        List<String> keys = new ArrayList<>();
        List<String> flied = new ArrayList<>();
        List<Long> value = new ArrayList<>();
        String key = makeKey(attr, String.format(AWARD_ISSUE_RECORD, rankId, phaseId));
        for (Map.Entry<String, Long> entry : awardUid2Score.entrySet()) {
            keys.add(key);
            flied.add(entry.getKey());
            value.add(entry.getValue());
        }
        actRedisDao.zBatchIncr(getRedisGroupCode(attr.getActId()), keys, flied, value);
        log.info("seedAwardRecord done: awardUid2Score:{}", JSON.toJSONString(awardUid2Score));

    }

    private void seedAnchorAward(String eseq, SeedAnchorWithTiGuanComponentAttr attr, Map<String, Long> awardUid2Score) {
        List<AwardBean> list = Lists.newArrayList();
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        for (Map.Entry<String, Long> entry : awardUid2Score.entrySet()) {
            int awardNum = Convert.toInt(entry.getValue());
            Map<Long, Integer> packageIdNumMap = ImmutableMap.of(attr.getSeedAwardPackageId(), awardNum);
            Map<Long, Map<Long, Integer>> taskPackageIds = ImmutableMap.of(attr.getSeedAwardTaskId(), packageIdNumMap);
            String myseq = java.util.UUID.randomUUID() + "#SeedAnchor" + (list.size() + 1);
            String receiver = entry.getKey();
            AwardBean awardBean = new AwardBean(myseq, attr.getBusiId(), receiver, time, taskPackageIds);
            list.add(awardBean);
        }
        // 保存奖励
        this.saveAward(attr, eseq, list);
        log.info("seedAnchorAward save award done, list:{}", JSON.toJSONString(list));
    }

    /**
     * 发放奖励， 使用 list 的 pop、push， 避免了定时器锁的需求
     */
    @Scheduled(cron = "*/5 * * * * ? ")
    @NeedRecycle(author = "yulianzhu", notRecycle = true)
    public void giveAwards() {
        this.giveAwards(2, 3600, 60 * 24);
    }

    @Override
    public List<Object> handleExt(SeedAnchorWithTiGuanComponentAttr attr, GetRankReq rankReq,
                                  RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        Long rankId = rankReq.getRankId();
        Long phaseId = rankReq.getPhaseId();
        String rankPhase = rankId + StringUtil.UNDERSCORE + phaseId;
        //todo 添加种子主播信息 done
        if (attr.getActId() != rankReq.getActId() || !attr.getSeedAnchorRankPhaseList().contains(rankPhase)) {
            return objectList;
        }
        Set<String> seedAnchorSet = seedAnchorSet(attr, rankId, phaseId);
        String key = makeKey(attr, String.format(AWARD_ISSUE_RECORD, rankId, phaseId));
        Set<ZSetOperations.TypedTuple<String>> typedTuples = actRedisDao.zrevRange(getRedisGroupCode(attr.getActId()), key, 0, -1);
        Map<String, Long> awardUid2Score = Maps.newHashMap();
        if (typedTuples != null) {
            for (ZSetOperations.TypedTuple<String> typedTuple : typedTuples) {
                awardUid2Score.put(typedTuple.getValue(), Convert.toLong(typedTuple.getScore()));
            }
        }

        for (Object o : objectList) {
            RankValueItemBase item = (RankValueItemBase) o;
            if (item.getViewExt() == null) {
                item.setViewExt(Maps.newHashMap());
            }

            if (seedAnchorSet.contains(item.getKey())) {
                item.getViewExt().put("seedAnchor", "1");
            } else {
                item.getViewExt().put("seedAnchor", "2");
            }
            item.getViewExt().put("extAwardNum", awardUid2Score.getOrDefault(item.getKey(), 0L) + "");
        }
        return objectList;
    }

    /**
     * 获取踢馆信息
     **/
    @RequestMapping("/getGroupDetail")
    public Response getGroupDetail(HttpServletRequest req, HttpServletResponse resp, long actId, long rankId, long phaseId) {
        long myUid = getLoginYYUid(req, resp);
        Clock clock = new Clock();
        try {
            SeedAnchorWithTiGuanComponentAttr attr = getUniqueComponentAttr(actId);
            Set<String> seedAnchorSet = seedAnchorSet(attr, rankId, phaseId);
            long tiguanUseIndex = attr.getSeedPhase2TiguanInx().get(rankId + StringUtil.UNDERSCORE + phaseId);
            Response response = (Response) componentCollaborator.callComponent(actId, ComponentId.TI_GUAN,
                    "getGroupDetailOL", actId, tiguanUseIndex, myUid);
            //出错了,直接返回
            if (response.getResult() != Response.OK) {
                return response;
            }
            long seedAwardRate = attr.getSeedAwardRate();
            long seedAwardMax = attr.getSeedAwardMax();

            ImmutableMap<String, Object> data = (ImmutableMap<String, Object>) response.getData();
            List<TiGuanGroupDetail> groups = (List<TiGuanGroupDetail>) data.get("groups");
            for (TiGuanGroupDetail group : groups) {
                TiGuanMemberInfo leizhu = group.getLeizhu();
                if (seedAnchorSet.contains(leizhu.getMemberId())) {
                    leizhu.setSeedAnchor(1);
                    if (leizhu.getStatus() == 1) {
                        leizhu.setExtAwardNum(awardNum(leizhu.getScore(), seedAwardRate, seedAwardMax));
                    } else if (leizhu.getStatus() == -1) {
                        for (TiGuanMemberInfo challenger : group.getChallengers()) {
                            if (challenger.getStatus() == 1) {
                                challenger.setExtAwardNum(awardNum(challenger.getScore(), seedAwardRate, seedAwardMax));
                            }
                        }
                    }
                }
            }

            Object endTime = data.get("manualChallengeEndTime");
            return Response.success(ImmutableMap.of("manualChallengeEndTime", endTime, "groups", groups));
        } catch (Exception e) {
            log.error("getGroupDetail fail@actId:{},rankId:{},phaseId:{} err:{} {}", actId, rankId, phaseId, e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }
}
