package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.hdzj.element.component.attr.RankingScoreAwardComponentQuaAttr;
import com.yy.gameecology.hdzj.element.redis.RankingScoreAwardQuaComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:累计送礼物，加领取任务资格
 *
 * @createBy 曾文帜
 * @create 2021-08-12 17:19
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties"})
public class RankingScoreAwardQuaComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RankingScoreAwardQuaComponent rankingScoreAwardQuaComponent;

//     * //这个用RankingScoreAwardComponent 实现
//             * //    送出50元约会礼物	约会下单特效*1天	2	200
//             * //    送出188约会礼物	约会下单特效*4天	10	100
//             * //    送出520约会礼物	约会下单特效*15天	20	80
//             * //    送出1314约会礼物	约会下单特效*20天	66	20
//             * //    送出5200约会礼物	约会下单特效*30天	188	10

    @Test
    public void Test() {
        int loops = 100;
        for (int i = 0; i < loops; i++) {
            String eventStr = "{\"actId\":2021084002,\"actors\":{80001:\"123456\",80003:\"789\"},\"busiId\":800,\"ekey\":\"\",\"index\":0,\"itemCount\":1,\"itemId\":\"ZW_LMYH\",\"itemScore\":100,\"member\":\"789\",\"occurTime\":\"2021-08-20 17:30:15\",\"phaseId\":1,\"phaseScore\":20,\"rankId\":1,\"rankScore\":5000,\"seq\":\"569d6151-a636-4837-a136-5dc1730db282\",\"timeKey\":0,\"timestamp\":\"2021-08-20 17:30:15\",\"uri\":2002}";

            RankingScoreChanged event = JSON.parseObject(eventStr, RankingScoreChanged.class);
            RankingScoreAwardComponentQuaAttr attr = rankingScoreAwardQuaComponent.getComponentAttr(2021084002L, 1);
            rankingScoreAwardQuaComponent.onRankingScoreChanged(event, attr);
        }
    }


}
