package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.client.thrift.VerifyCodeClient;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.KingLiftAwardReq;
import com.yy.gameecology.hdzj.bean.KingLiftAwardResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.KingLiftComponentAttr;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.verify_code.SRequestSmsCodeResp;
import com.yy.thrift.verify_code.SVerifySmsCodeResp;
import com.yy.thrift.verify_code.VCRSE;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 王者人生组件,与腾讯合作项目
 *
 * <AUTHOR>
 * @date 2022.03.11 14:12
 */
@UseRedisStore
@Service
public class KingLiftComponent extends BaseActComponent<KingLiftComponentAttr> {

    private Logger logger = LoggerFactory.getLogger(KingLiftComponent.class);

    /**
     * 未绑定yy时,获得的奖励临时记录 mobile_temp_award:mobile hash 注意量的问题 {couponId:count}
     */
    public static final String MOBILE_TEMP_AWARD = "mobile_temp_award:%s";

    /**
     * 绑定yy和手机号的奖励发放记录 hash {taskId_packageId_count:time}
     */
    public static final String YY_MOBILE_AWARD = "yy_mobile_award:%s_%s";

    /**
     * 奖励发放失败记录, 人工介入 or 定时器扫描 hash
     */
    public static final String YY_MOBILE_AWARD_FAIL = "yy_mobile_award_fail:%s_%s";

    /**
     * yy和手机号的映射关系 一对一 绑定后不可修改 hash
     */
    public static final String YY_MOBILE_MAP = "yy_mobile_map";

    /**
     * yy和手机号的映射关系 一对一 绑定后不可修改 hash
     */
    public static final String MOBILE_YY_MAP = "mobile_yy_map";

    /**
     * 用户首次进入页面记录 user_first_time_enter  set {uid}
     */
    public static final String USER_FIRST_TIME_ENTER = "user_first_time_enter";

    @Autowired
    private VerifyCodeClient verifyCodeClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.KING_LIFT;
    }

    //查询奖励记录
    //1.检查是否绑定了手机,
    //2.是-> 查询奖励,否->返回绑定手机提示
    //不需要记录完成了什么任
    public JSONObject queryAwardRecord(long actId, long uid) {
        JSONObject response = new JSONObject();
        response.put("result", 0);
        response.put("msg", "ok");

        KingLiftComponentAttr attr = getUniqueComponentAttr(actId);
        String groupCode = redisConfigManager.getGroupCode(actId);
        String yyMobileMap = makeKey(attr, YY_MOBILE_MAP);
        String mobile = actRedisDao.hget(groupCode, yyMobileMap, uid + "");
        //未绑定手机号,返回提示绑定手机号
        if (StringUtil.isBlank(mobile)) {
            response.put("result", 2);
            response.put("msg", "请绑定手机号码!");
            return response;
        }
        response.put("mobile", mobile);
        String awardMapKey = makeKey(attr, String.format(YY_MOBILE_AWARD, uid, mobile));
        Map<Object, Object> awardMap = actRedisDao.hGetAll(groupCode, awardMapKey);
        List<JSONObject> awardList = new ArrayList<>();
        if (awardMap == null || awardMap.size() == 0) {
            response.put("list", awardList);
            return response;
        }
        for (Map.Entry<Object, Object> entry : awardMap.entrySet()) {
            JSONObject data = new JSONObject();
            String key = String.valueOf(entry.getKey());
            String[] split = key.split("_");
            if (split.length >= 3) {
                data.put("awardCount", split[2]);
                data.put("time", entry.getValue());
                awardList.add(data);
            }
        }
        response.put("list", awardList);
        return response;

    }

    //获取验证码接口

    //绑定手机号
    // 1.验证验证吗
    // 2.记录yy-手机号码映射关系
    // 3.异步发奖已有的奖励 or 定时器触发
    // 4.


    /**
     * 发送短信验证码
     *
     * @param uid    uid
     * @param actId  活动id
     * @param ip     IP地址
     * @param mobile 手机号
     * @return BaseRsp BaseRsp
     */
    public SRequestSmsCodeResp codeSend(long actId, long uid, String mobile, String ip) {
        if (!actInfoService.inActTime(actId)) {
            SRequestSmsCodeResp resp = new SRequestSmsCodeResp();
            resp.setRescode(505);
            resp.setContext("活动未开始或已结束!");
            return resp;
        }
        //todo 验证码频率限制,60秒发一次,单号码24小时建议不超过5条，单IP24小时建议不超过20条 done
        String groupCode = redisConfigManager.getGroupCode(actId);
        KingLiftComponentAttr attr = getUniqueComponentAttr(actId);
        String yyMobile = makeKey(attr, YY_MOBILE_MAP);
        String mobileYY = makeKey(attr, MOBILE_YY_MAP);
        String yy2Mobile = actRedisDao.hget(groupCode, yyMobile, uid + "");
        if (yy2Mobile != null) {
            SRequestSmsCodeResp smsCodeResp = new SRequestSmsCodeResp();
            smsCodeResp.setRescode(1);
            smsCodeResp.setErrinfo("您已绑定其他的手机号码");
            logger.warn("codeVerify uid:{} 已绑定手机号码 mobile:{}", uid, yy2Mobile);
            return smsCodeResp;
        }
        String uidd = actRedisDao.hget(groupCode, mobileYY, mobile);
        if (uidd != null) {
            SRequestSmsCodeResp smsCodeResp = new SRequestSmsCodeResp();
            smsCodeResp.setRescode(2);
            smsCodeResp.setErrinfo("该手机号已绑定其他的yy号");
            logger.warn("codeVerify mobile:{} 已绑定uid uid:{}", mobile, uidd);
            return smsCodeResp;
        }

        String sms60SecondLimit = makeKey(attr, String.format("sms_60_second_limit_%s", mobile));
        SRequestSmsCodeResp resp = new SRequestSmsCodeResp();
        //超上限
        resp.setRescode(VCRSE.VCRSE_RATE_ERR.getValue());
        final boolean rs = actRedisDao.setNX(groupCode, sms60SecondLimit + mobile, mobile, 60);
        if (!rs) {
            logger.warn("codeSend error, sms60SecondLimit mobile:{}, uid:{}", mobile, uid);
            resp.setContext("请求过于频繁,请稍后重试!!");
            return resp;
        }

        Date now = commonService.getNow(actId);
        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String sms24HourLimit = makeKey(attr, String.format("sms_24_hour_limit:%s", day));
        final boolean rs1 = actRedisDao.hIncrByKey(groupCode, sms24HourLimit, mobile, 1) > 5;
        if (rs1) {
            logger.warn("codeSend error, sms24HourLimit mobile:{}, uid:{}", mobile, uid);
            resp.setContext("请求过于频繁,请稍后重试!!");
            return resp;
        }

        String smsIpLimit = makeKey(attr, String.format("sms_ip_limit:%s", day));
        actRedisDao.setExpire(groupCode, sms24HourLimit, 86400);
        final boolean rs2 = actRedisDao.hIncrByKey(groupCode, smsIpLimit, ip, 1) > 20;
        if (rs2) {
            logger.warn("codeSend error, smsIpLimit mobile:{}, uid:{}", mobile, uid);
            resp.setContext("请求过于频繁,请稍后重试!!");
            return resp;
        }
        actRedisDao.setExpire(groupCode, smsIpLimit, 86400);
        String content = "您正在参与王者人生活动，验证码为{code}, 有效期3分钟（请勿泄露，如非本人操作请忽略）";
        return verifyCodeClient.sendSmsVerifyCodeByMobile(mobile, content, 180);
    }

    /**
     * 验证短信验证码
     *
     * @param actId  活动id
     * @param uid    用户id
     * @param mobile 手机号
     * @param code   验证码
     * @return
     */
    public SVerifySmsCodeResp codeVerify(long actId, long uid, String mobile, String code) {
        logger.info("codeVerify uid:{}, mobile:{}, code:{}", uid, mobile, code);
        //todo 判断是否已绑定其他yy号 done
        KingLiftComponentAttr attr = getUniqueComponentAttr(actId);
        String yyMobile = makeKey(attr, YY_MOBILE_MAP);
        String mobileYY = makeKey(attr, MOBILE_YY_MAP);
        String groupCode = redisConfigManager.getGroupCode(actId);
        String yy2Mobile = actRedisDao.hget(groupCode, yyMobile, uid + "");
        if (yy2Mobile != null) {
            SVerifySmsCodeResp smsCodeResp = new SVerifySmsCodeResp();
            smsCodeResp.setRescode(1);
            smsCodeResp.setErrinfo("您已绑定其他的手机号码");
            logger.warn("codeVerify uid:{} 已绑定手机号码 mobile:{}", uid, yy2Mobile);
            return smsCodeResp;
        }
        String uidd = actRedisDao.hget(groupCode, mobileYY, mobile);
        if (uidd != null) {
            SVerifySmsCodeResp smsCodeResp = new SVerifySmsCodeResp();
            smsCodeResp.setRescode(2);
            smsCodeResp.setErrinfo("该手机号已绑定其他的yy号");
            logger.warn("codeVerify mobile:{} 已绑定uid uid:{}", mobile, uidd);
            return smsCodeResp;
        }

        SVerifySmsCodeResp smsCodeResp = verifyCodeClient.verifySmsCodeByMobile(mobile, code);
        //请求接口出错了
        if (smsCodeResp == null) {
            logger.error("verifyCodeClient.verifySmsCodeByUid error,uid:{},mobile:{} response is null", uid, mobile);
            smsCodeResp.setRescode(2);
            smsCodeResp.setErrinfo("系统繁忙,请稍后重试!");
            return smsCodeResp;
        }
        int rescode = smsCodeResp.getRescode();
        //成功了,绑定yy和mobile,异步发奖励
        if (rescode == 0) {
            logger.info("codeVerify success yyuid:{} mobile:{}", uid, mobile);
            actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
                connection.hSet(yyMobile.getBytes(), String.valueOf(uid).getBytes(), mobile.getBytes());
                connection.hSet(mobileYY.getBytes(), mobile.getBytes(), String.valueOf(uid).getBytes());
                return null;
            });
            //异步发奖励,注意需等待5s,待映射关系先完成
            Const.EXECUTOR_DELAY_GENERAL.schedule(() -> exeAward(uid, mobile, attr), 5, TimeUnit.SECONDS);
        }
        return smsCodeResp;
    }

    /**
     * 异步发放绑定手机前的奖励
     *
     * @param uid    用户id
     * @param mobile 手机号
     * @param attr   组件属性
     */
    public void exeAward(long uid, String mobile, KingLiftComponentAttr attr) {
        String tempKey = makeKey(attr, String.format(MOBILE_TEMP_AWARD, mobile));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        Map<Object, Object> tempAward = actRedisDao.hGetAll(groupCode, tempKey);
        //如果是空的,说明未完成任何任务,或者是绑定了手机直接发放了
        if (CollectionUtils.isEmpty(tempAward)) {
            logger.info("exeAward done uid:{} award is empty", uid);
            return;
        }
        Map<String, Map<String, Long>> task2Award = attr.getTask2Award();

        //tempAward {couponId:count} todo taskId 和packageId 相同时处理, 改成逐个发奖 done
        for (Map.Entry<Object, Object> entry : tempAward.entrySet()) {
            //奖池和奖包map
            Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
            Map<String, Long> taskAndPackage = task2Award.get(entry.getKey());
            Long taskId = taskAndPackage.get("taskId");
            Long count = taskAndPackage.get("count");

            Map<Long, Integer> packageMap = taskPackageIds.getOrDefault(taskId, Maps.newHashMap());
            packageMap.put(taskAndPackage.get("packageId"), count.intValue());
            taskPackageIds.put(taskId, packageMap);
            awardExe(uid, mobile, attr, groupCode, taskPackageIds);
        }

    }

    private void awardExe(long uid, String mobile, KingLiftComponentAttr attr, String groupCode, Map<Long, Map<Long, Integer>> taskPackageIds) {
        Date now = commonService.getNow(attr.getActId());
        String time = DateUtil.format(now);

        String seq = String.format("king_life_temp_award_%s_%s_%s", attr.getActId(), uid, System.currentTimeMillis());
        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doBatchWelfare(seq, uid, taskPackageIds, time, attr.getRetry(), Maps.newHashMap());
        logger.info("exeAward done,seq:{}, uid:{}, result:{}, taskPackage:{} ", seq, uid, JSON.toJSONString(batchWelfareResult), taskPackageIds);
        Map<String, String> awardRecord = Maps.newHashMap();
        for (Map.Entry<Long, Map<Long, Integer>> entry : taskPackageIds.entrySet()) {
            Long taskId = entry.getKey();
            for (Map.Entry<Long, Integer> packageEntry : entry.getValue().entrySet()) {
                Long packageId = packageEntry.getKey();
                Integer count = packageEntry.getValue();
                awardRecord.put(taskId + "_" + packageId + "_" + count, time);
            }
        }

        String awardKey;
        //成功了添加记录,失败添加带失败的记录
        if (batchWelfareResult != null && batchWelfareResult.getCode() == 0) {
            awardKey = makeKey(attr, String.format(YY_MOBILE_AWARD, uid, mobile));
        } else {
            awardKey = makeKey(attr, String.format(YY_MOBILE_AWARD_FAIL, uid, mobile));
        }
        actRedisDao.getRedisTemplate(groupCode).executePipelined((RedisConnection connection) -> {
            //awardRecord 格式  {taskId_packageId_count,time}
            for (Map.Entry<String, String> entry : awardRecord.entrySet()) {
                connection.hSet(awardKey.getBytes(), entry.getKey().getBytes(), entry.getValue().getBytes());
            }
            return null;
        });
    }


    //todo 抽离具体业务
    public KingLiftAwardResp sendAward(KingLiftAwardReq kingLiftAwardReq) throws Exception {
        KingLiftAwardResp resp = new KingLiftAwardResp();
        //todo 活动ID问题 done
        long actId = Const.GEPM.getParamValueToLong("king_life_act_id");

        if (!actInfoService.inActTime(actId)) {
            resp.setCode("505");
            resp.setMsg("活动未开始或已结束!");
            return resp;
        }
        KingLiftComponentAttr attr = getUniqueComponentAttr(actId);
        String mobileStr = kingLiftAwardReq.getMobile();
        String mobile = AESUtil.decryptCBC(mobileStr, attr.getAesKey(), attr.getIv());
        resp.setSign(kingLiftAwardReq.getSign());
        //1.验证参数,签名
        try {
            verifyParam(kingLiftAwardReq, attr);
        } catch (Exception e) {
            logger.warn("sendAward verifyParam error req:{}", JSONObject.toJSONString(kingLiftAwardReq));
            resp.setCode("8");
            resp.setMsg("参数错误");
            return resp;
        }

        KingLiftAwardReq.CouponContent couponContent = kingLiftAwardReq.getCouponContent();
        //2.查询是否绑定yy,
        //3.是->发奖并记录,否->记录
        //4.
        String couponId = couponContent.getCouponId();
        long couponNum = Convert.toLong(couponContent.getCouponNum());
        String groupCode = redisConfigManager.getGroupCode(actId);

        String key = makeKey(attr, MOBILE_YY_MAP);
        String yyUid = actRedisDao.hget(groupCode, key, mobile);
        String uniqueMark = kingLiftAwardReq.getUniqueMark();

        KingLiftAwardResp.CouponInfo couponInfo = new KingLiftAwardResp.CouponInfo("YY_" + uniqueMark);
        resp.setCouponInfoList(Collections.singletonList(couponInfo));
        logger.info("sendAward  seq:{} yyuid:{} mobile:{} req:{}", uniqueMark, yyUid, mobile, JSON.toJSONString(kingLiftAwardReq));

        //seq 10天过期, 去重
        final boolean rs = actRedisDao.setNX(groupCode, uniqueMark, mobile, 1123200);
        if (!rs) {
            logger.info("sendAward duplicate key seq:{}, mobile:{}", uniqueMark, mobile);
            return resp;
        }
        //未绑定手机号,记录在手机号临时奖励
        if (StringUtil.isBlank(yyUid)) {
            String mobileTempAward = makeKey(attr, String.format(MOBILE_TEMP_AWARD, mobile));
            long incr = actRedisDao.hIncrByKey(groupCode, mobileTempAward, couponId, couponNum);
            logger.info("sendAward save temp seq:{} yyuid:{} mobile:{} incrAfter:{}", uniqueMark, yyUid, mobile, incr);
            return resp;
        }
        //绑定了手机号码,发奖励
        Date now = commonService.getNow(actId);
        String seq = uniqueMark;

        Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();

        //{"yb_1":{"taskId":50195,"packageId":390,"count":1},"yb_2":{"taskId":50195,"packageId":390,"count":2},
        // "yb_4":{"taskId":50195,"packageId":390,"count":4},"yb_6":{"taskId":50195,"packageId":390,"count":6}}
        Map<String, Map<String, Long>> task2Award = attr.getTask2Award();
        //奖池和奖包map, 只有一个奖池时,直接拿出来,多个则根据ID获取, couponId需确认清楚
        Map<String, Long> taskAndPackage;

        taskAndPackage = task2Award.get(couponId);
        Long count = taskAndPackage.get("count");

        Long taskId = taskAndPackage.get("taskId");
        Map<Long, Integer> packageMap = taskPackageIds.getOrDefault(taskId, Maps.newHashMap());
        Long packageId = taskAndPackage.get("packageId");
        packageMap.put(packageId, count.intValue());
        taskPackageIds.put(taskId, packageMap);

        String nowTime = DateUtil.format(now);
        BatchWelfareResult result = hdztAwardServiceClient.doBatchWelfare(seq, Convert.toLong(yyUid), taskPackageIds, nowTime, attr.getRetry(), Maps.newHashMap());
        //发奖失败,记录到发失败记录
        if (result == null || result.getCode() != 0) {
            String yyMobileAward = makeKey(attr, String.format(YY_MOBILE_AWARD_FAIL, yyUid, mobile));
            actRedisDao.hIncrByKey(groupCode, yyMobileAward, couponId, couponNum);
            sendBaiduInfo(yyUid, seq, actId);
            logger.info("doBatchWelfare fail seq:{} uid:{}, award:{}", seq, yyUid, JSON.toJSONString(taskPackageIds));

            return resp;
        }
        String yyMobileAward = makeKey(attr, String.format(YY_MOBILE_AWARD, yyUid, mobile));
        actRedisDao.hset(groupCode, yyMobileAward, taskId + "_" + packageId + "_" + count, nowTime);
        logger.info("doBatchWelfare done seq:{} uid:{}, award:{}", seq, yyUid, JSON.toJSONString(taskPackageIds));
        return resp;
    }

    private void sendBaiduInfo(String yyUid, String seq, long actId) {
        try {
            //todo 发如流通知 done
            String msgFormat = "请注意,王者人生活动发奖失败,uid:{}, seq:{}";
            String msg = String.format(msgFormat, yyUid, seq);
            //baiduInfoFlowRobotService.asyncSendNotifyByActAttrKey(actId, "act_notice", msg, Lists.newArrayList());
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
        } catch (Exception e) {
            logger.warn("sendBaiduInfo error, uid:{} seq:{}", yyUid, seq, e);
        }
    }

    private void verifyParam(KingLiftAwardReq kingLiftAwardReq, KingLiftComponentAttr attr) {
        //检查不可为空的参数
        Assert.hasText(kingLiftAwardReq.getAppId(), "参数appId不可为空");
        Assert.hasText(kingLiftAwardReq.getMobile(), "参数mobile不可为空");
        Assert.hasText(kingLiftAwardReq.getTimeSpan(), "参数timeSpan不可为空");
        Assert.hasText(kingLiftAwardReq.getSign(), "参数sign不可为空");
        Assert.notNull(kingLiftAwardReq.getCouponContent(), "参数couponContent不可为空");
        String sign = sign(kingLiftAwardReq, attr);
        Assert.isTrue((sign.equals(kingLiftAwardReq.getSign())), "签名验证不通过");
    }

    private String sign(KingLiftAwardReq kingLiftAwardReq, KingLiftComponentAttr attr) {
        StringBuilder sb = new StringBuilder();
        sb.append("appId=").append(kingLiftAwardReq.getAppId()).append("&mobile=").append(kingLiftAwardReq.getMobile())
                .append("&timeSpan=").append(kingLiftAwardReq.getTimeSpan()).append("&uniqueMark=").append(kingLiftAwardReq.getUniqueMark())
                .append("&appKey=").append(attr.getAppKey());
        return MD5SHAUtil.getMD5(sb.toString());
    }

    //抽奖接口
    public JSONObject firstEnter(long actId, long uid) {
        JSONObject data = new JSONObject();
        data.put("result", 0);
        data.put("msg", "Ok");
        KingLiftComponentAttr attr = getUniqueComponentAttr(actId);
        String key = makeKey(attr, USER_FIRST_TIME_ENTER);
        String groupCode = redisConfigManager.getGroupCode(actId);
        if (actRedisDao.sAdd(groupCode, key, uid + "") <= 0) {
            data.put("result", 1);
            data.put("msg", "非首次进入");
        }
        return data;
    }


    public static void main(String[] args) throws Exception {
        StringBuilder sb = new StringBuilder();
        String mobile = "15914496204";
        String mobileStr = AESUtil.encryptCBC(mobile, "6406808F5434469D", "1513D520B9C1459C");
        String seq = UUID.randomUUID().toString().replaceAll("-", "");
        //"2022-03-12 12:12:12";
        String timeSpan = "2022-03-21 15:41:58";
        sb.append("appId=").append("YY").append("&mobile=").append(mobileStr)
                .append("&timeSpan=").append(timeSpan).append("&uniqueMark=").append(seq)
                .append("&appKey=").append("LqPQ2gbF");

        System.out.println("timeSpan ->" + timeSpan);
        System.out.println("seq-> " + seq);
        System.out.println("sign->" + MD5SHAUtil.getMD5(sb.toString()));
        System.out.println("mobile ->" + mobileStr);
    }


}
