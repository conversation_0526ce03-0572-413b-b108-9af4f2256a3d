package com.yy.gameecology.activity.bean.hdzt;

import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class ActShopExchangeResult {
    @ComponentAttrField(labelText = "item_id")
    private String itemId;

    @ComponentAttrField(labelText = "单位")
    private String unit;

    @ComponentAttrField(labelText = "价值")
    private String money;

    @ComponentAttrField(labelText = "数量")
    private long num;

    @ComponentAttrField(labelText = "兑换成功结果", remark = "例如:成功获得【%s】奖励")
    private String result;

    @ComponentAttrField(labelText = "兑换成功tip", remark = "例如:已发送至您的包裹中")
    private String tip;

    @ComponentAttrField(labelText = "兑换分区排序", remark = "{\"兵临城下\":1,\"青云之上\":1}")
    private String areaSort;

    @ComponentAttrField(labelText = "福利中心rewardTyp", remark = "非福利中心礼物可以不用此字段")
    private int rewardType;

    @ComponentAttrField(labelText = "奖池id", remark = "奖池id")
    private long taskId;

    @ComponentAttrField(labelText = "奖包id", remark = "奖包id")
    private long packageId;

    @ComponentAttrField(labelText = "是否是心愿皮肤", remark = "0不是 1是")
    private int special;
}
