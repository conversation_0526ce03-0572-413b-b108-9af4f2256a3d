local key = KEYS[1]
local field = ARGV[1]
local winnerMemberId = ARGV[2]
local scoreGap = ARGV[3]
local scoreGapConfig = ARGV[4]

local winnerKey = field .. "@winnerMemberId"
local scoreGapKey = field .. "@scoreGap"

-- 日志输出相关变量和函数
local loginfo = {}
local function log(key, info)
    if logCntlFlag == '1' then
        loginfo[#loginfo + 1] = { '(' .. #loginfo + 1 .. ') [' .. key .. ']', info }
    end
end

local oldWinnerMemberId = redis.call('HGET', key, winnerKey)
log('HGET', key .. ' ' .. winnerKey .. ' return:' .. oldWinnerMemberId)

local returnData = {}
returnData["overTake"] = 0

local setScore = true
-- 第一次设置
-- 胜者已经改变,并且差值大于配置的值
if not oldWinnerMemberId or empty(oldWinnerMemberId) then
    oldWinnerMemberId = ""
    redis.call('HSET', key, winnerKey, winnerMemberId)
    log('HSET', key .. ' ' .. winnerKey .. ' ' .. winnerMemberId)
elseif oldWinnerMemberId ~= winnerMemberId then
    if scoreGap > scoreGapConfig then
        redis.call('HSET', winnerKey, winnerMemberId)
        log('HSET', key .. ' ' .. winnerKey .. ' ' .. winnerMemberId)
        returnData["overTake"] = 1
    else
        setScore = false
    end
end
if setScore then
    redis.call('HSET', key, scoreGapKey, scoreGap)
    log('HSET', key .. ' ' .. scoreGapKey .. ' ' .. scoreGap)
end

returnData["logInfo"] = loginfo

return cjson.encode(returnData)
