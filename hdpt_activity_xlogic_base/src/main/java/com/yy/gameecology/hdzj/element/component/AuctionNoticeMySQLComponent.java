package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerLayout;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.RoleTypeSource;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AuctionNoticeComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.gameecology.hdzj.element.history.AuctionNoticeComponent;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.ActorInfoItem;
import com.yy.thrift.hdztranking.ActorQueryItem;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;

@Component
public class AuctionNoticeMySQLComponent extends BaseActComponent<AuctionNoticeComponentAttr> {

    private static final String AUCTION_NOTICE = "auction_notice:%s:%d";


    private static final String AUCTION_NOTICE_FIRST = "auction_notice_first";

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    protected UserInfoService userInfoService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    protected HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;
    /**
     * 【目前只支持3和6】组件类型 1.alert 2.toast 3.svga横幅 4. 通用web弹窗 5. mp4特效 6. svga特效
     */
    private final static int SVGA_CONENT_TYPE = 6;

    private final static int RO_BANNER_ID = 5090002;


    /**
     * pc 单播 banner id
     */
    private final static Map<Integer, Integer> TYPE_PC_UNICAST_BANNER_ID_MAP
            = Map.of(1, 5090003, 2, 5090001);

    private final static ImmutableSortedMap<Long, String> SCORE_UNIT_MAP = ImmutableSortedMap.of(10000L, "万", 1000000L, "百万", 10000000L, "千万", 100000000L, "亿");

    /**
     * @param actId
     * @param cmptId
     * @param type     1= 首次进入，2=即将达成，3=达成目标
     * @param uid
     * @param memberId 榜单成员，可能是房间
     * @return
     */
    @RequestMapping("/broTest")
    public Response<?> broTest(long actId, long cmptId, int type, long uid, String memberId, String channel) {
        if (SysEvHelper.isDeploy()) {
            return Response.ok();
        }
        var componentAttr = getComponentAttr(actId, cmptId);
        if (componentAttr == null) {
            return Response.success("组件索引错误");
        }
        long minAwardScore = componentAttr.getAwardMap().keySet().stream().min(Long::compareTo).orElse(0L);
        long diffScore = 100000;

        notice(componentAttr, type, uid, memberId, UUID.randomUUID().toString(), minAwardScore - diffScore, minAwardScore, diffScore, true, channel);
        return Response.ok();
    }

    @Override
    public Long getComponentId() {
        return ComponentId.AUCTION_NOTICE_MYSQL;
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, AuctionNoticeComponentAttr attr) {
        Date now = commonService.getNow(attr.getActId());
        long uid = event.getUid(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        log.info("onUserEnterTemplate uid:{},actId:{},extJson:{},sid:{}", uid, attr.getActId(), extJson, sid);
        boolean actEnterEvent = StringUtil.isNotBlank(extJson) && extJson.contains(String.valueOf(attr.getActId()));
        //协议只发1次，确保是本次活动触发的，才弹窗
        if (!actEnterEvent) {
            log.warn("not this actId UserEnterTemplateEvent uid:{}", uid);
            return;
        }

        boolean app = extJson.contains("\"app\"");
        String date = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2);
        String key = makeKey(attr, String.format(AUCTION_NOTICE, date, event.getUid()));
        Map<String, String> entries = commonDataDao.hashGetAll(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key);
        if (MapUtils.isEmpty(entries)) {
            return;
        }

        for (var entry : entries.entrySet()) {
            String member = entry.getKey();
            if (!satisfyChannel(sid, ssid, member, attr.getRoleType())) {
                log.info("enter channel with member:{} sid:{} ssid:{} not satisfy", member, sid, ssid);
                continue;
            }
            String msg = entry.getValue();
            if (StringUtils.isBlank(msg)) {
                continue;
            }

            int rs = commonDataDao.hashValueDel(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key, member);
            if (rs > 0) {
                AuctionNoticeComponent.BannerMember bannerMember = JsonUtils.deserialize(msg, AuctionNoticeComponent.BannerMember.class);
                if (app) {
                    appUnicast(attr, bannerMember);
                } else {
                    pcUnicast(attr, bannerMember);
                }
                log.info("send auction notice with uid:{} member:{}", event.getUid(), bannerMember);
            }
        }
    }

    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = true)
    public void onPromotTimeEnd(PromotTimeEnd event, AuctionNoticeComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        long minAwardScore = attr.getAwardMap().keySet().stream().min(Long::compareTo).orElse(0L);
        if (minAwardScore == 0) {
            log.info("send  first auction notice ignore");
            return;
        }

        boolean absent = commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), AUCTION_NOTICE_FIRST, AUCTION_NOTICE_FIRST, "1");

        if (!absent) {
            log.info("send  first auction notice repetition");
            return;
        }

        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), commonService.getNow(attr.getActId()));
        //查询榜单当前阶段的成员
        List<Rank> curRanks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), dateStr, Integer.MAX_VALUE, Maps.newHashMap());
        if (CollectionUtils.isEmpty(curRanks)) {
            log.info("send  first auction notice ignore rank empty");
            return;
        }
        for (Rank rank : curRanks) {
            if (rank.getScore() >= minAwardScore) {
                log.info("onPromotTimeEnd ignore notice:{},score:{}", rank.getMember(),rank.getScore());
                continue;
            }

            //uid只影响发单播
            Long uid = getNoticeUid(attr, rank.getMember());
            if (uid == null) {
                log.warn("trying to send notice but cannot get notice uid:{}", rank.getMember());
                continue;
            }
            notice(attr, 1, uid, rank.getMember(), UUID.randomUUID().toString(), 0, minAwardScore, minAwardScore, true,"");
        }
        log.info("send auction first notice done");
    }


    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, AuctionNoticeComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        long score = event.getPhaseScore();
        if (score < attr.getHeraldScore()) {
            return;
        }

        long broScore = 0, minAwardScore = 0;
        int broAward = 0;
        long beforeScore = score - event.getItemScore();
        if (beforeScore < attr.getHeraldScore()) {
            broScore = attr.getHeraldScore();
        }

        for (Map.Entry<Long, Integer> entry : attr.getAwardMap().entrySet()) {
            if (entry.getKey() > beforeScore && entry.getKey() <= score && entry.getKey() > broScore) {
                broScore = entry.getKey();
                broAward = entry.getValue();
            }

            if (minAwardScore == 0 || minAwardScore > entry.getKey()) {
                minAwardScore = entry.getKey();
            }
        }

        if (broScore == 0) {
            log.info("broScore not satisfied member:{} beforeScore:{} score:{}", event.getMember(), beforeScore, score);
            return;
        }


        String member = event.getMember();
        // 判断member是否晋级
        ActorQueryItem item = new ActorQueryItem();
        item.setRankingId(attr.getRankId());
        item.setPhaseId(attr.getPhaseId());
        item.setActorId(member);
        item.setWithStatus(true);
        if (event.getTimeKey() != 0) {
            try {
                String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtils.parseDate(event.getOccurTime(), DateUtil.DEFAULT_PATTERN));
                item.setDateStr(dateStr);
            } catch (ParseException e) {
                log.warn("parse date fail:", e);
            }
        }
        ActorInfoItem actorInfoItem = hdztRankingThriftClient.queryActorRankingInfo(attr.getActId(), item);
        if (actorInfoItem == null || actorInfoItem.status != 0) {
            log.warn("score change event member not promote:{} {} {}", event.getRankId(), event.getPhaseId(), member);
            return;
        }

        //兼容不加白名单
        Long uid = getNoticeUid(attr, member);
        if (uid == null) {
            log.warn("trying to send notice but cannot get notice uid:{}", event.getMember());
            uid = 0L;
        }
        String channel = event.getActors().values().stream().filter(i -> i.contains("_")).findFirst().orElse("");
        boolean checkUidOnChannel = attr.getNoticeUserCmptInx() > 0 && !member.equals(String.valueOf(uid));
        if (broAward == 0) {
            //即将达成通知
            notice(attr, 2, uid, member, event.getSeq(), score, minAwardScore, minAwardScore - attr.getHeraldScore(), checkUidOnChannel, channel);
        } else {
            //达成通知
            notice(attr, 3, uid, member, event.getSeq(), score, broScore, 0, checkUidOnChannel, channel);
        }

        log.info("onRankingScoreChanged auction notice done with uid:{} bannerMember:{}", uid, member);
    }

    private void notice(AuctionNoticeComponentAttr attr, int type, long uid, String memberId, String seq, long score, long goalScore, long diffScore, boolean checkInChannel, String channelString) {

        AuctionNoticeComponent.BannerMember bannerMember = new AuctionNoticeComponent.BannerMember();

        bannerMember.setMemberId(memberId);
        bannerMember.setUid(uid);
        bannerMember.setSeq(seq);
        bannerMember.setType(type);
        bannerMember.setScore(score);
        bannerMember.setDiffScore(diffScore);
        bannerMember.setGoalScore(goalScore);
        bannerMember.setAwardName(attr.getAwardName().get(goalScore));
        bannerMember.setAwardValue(attr.getAwardMap().get(goalScore));

        if (StringUtils.isNotBlank(channelString)) {
            String[] channelArr = channelString.split("_");
            bannerMember.setSid(Long.parseLong(channelArr[0]));
            bannerMember.setSsid(Long.parseLong(channelArr[1]));
        }

        if (attr.getRoleType() == RoleTypeSource.ROOM) {
            RoomInfo roomInfo = commonService.getRoomInfoByRoomId(Integer.parseInt(memberId));
            if (roomInfo != null) {
                bannerMember.setNick(StringUtil.trim(roomInfo.getTitle()));
                bannerMember.setHeader(StringUtil.trim(roomInfo.getCover()));
            }
        } else if (attr.getRoleType() == RoleTypeSource.FAMILY) {
            FamilyBasicInfo familyInfo = commonService.getFamilyBasicInfo(Long.parseLong(memberId));
            if (familyInfo != null) {
                bannerMember.setNick(StringUtil.trim(familyInfo.getFamilyName()));
                bannerMember.setHeader(StringUtil.trim(familyInfo.getCover()));
            }
        } else {
            UserInfoVo userInfoVo = userInfoService.getUserInfo(List.of(uid), Template.unknown).get(uid);
            if (userInfoVo != null) {
                bannerMember.setNick(userInfoVo.getNick());
                bannerMember.setHeader(userInfoVo.getAvatarUrl());
            }
        }


        Date now = commonService.getNow(attr.getActId());
        String date = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2);
        String key = makeKey(attr, String.format(AUCTION_NOTICE, date, uid));
//        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));

        if (!TYPE_PC_UNICAST_BANNER_ID_MAP.containsKey(type)) {
            doFinishPcBanner(attr, bannerMember);
            doFinishAppBanner(attr, bannerMember);
            doStatic(attr, bannerMember);
        } else {
            if(uid>0){
                if (checkInChannel) {
                    UserCurrentChannel channel = commonService.getUserCurrentChannel(uid);
                    // 不在频道内，或不在member对应的频道内，先保存起来
                    if (channel == null || !satisfyChannel(channel.getTopsid(), channel.getSubsid(), memberId, attr.getRoleType())) {
                        String msg = JsonUtils.serialize(bannerMember);
                        commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key, memberId, msg);
//                        redisTemplate.opsForHash().put(key, memberId, msg);
                        log.info("save auction notice with bannerMember:{} uid:{}", bannerMember, uid);
                        return;
                    }
                }
                pcUnicast(attr, bannerMember);
                appUnicast(attr, bannerMember);
                commonDataDao.hashValueDel(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key, memberId);
            }else{
                log.warn("send auction notice ignore uid is 0 ,bannerMember:{}",bannerMember);
            }
        }

        log.info("send auction notice with uid:{} bannerMember:{}", uid, bannerMember);

    }

    /**
     * 横幅全频道广播 pc
     *
     * @param attr
     * @param member
     */
    private void doFinishPcBanner(AuctionNoticeComponentAttr attr, AuctionNoticeComponent.BannerMember member) {
        if (StringUtils.isBlank(attr.getAppBannerSvgaUrl()) || CollectionUtils.isEmpty(attr.getAppBannerTextList())) {
            log.info("doFinishBanner ignore");
            return;
        }

        //pc广播
        Map<String, Object> info = Map.of(
                "roleType", attr.getRoleType(),
                "goalScore", member.getGoalScore(),
                "goalScoreShow", toShowScore(member.getGoalScore()),
                "awardName", member.getAwardName(),
                "awardValue", member.getAwardValue(),
                "logo", member.getHeader(),
                "nick", member.getNick());

        com.yy.thrift.broadcast.Template template = BroadCastHelpService.changeBusiId2BroTemplate(attr.getBusiId());

        RetryTool.withRetryCheck(attr.getActId(), makeKey(attr, "pcBro_" + member.getSeq()), () ->
                commonBroadCastService.commonBannerBroadcast(0, 0, 0, template,
                        4, attr.getActId(), 0, 0, RO_BANNER_ID, 0L, info));


    }

    /**
     * 横幅全频道广播 app
     *
     * @param attr
     * @param member
     */
    private void doFinishAppBanner(AuctionNoticeComponentAttr attr, AuctionNoticeComponent.BannerMember member) {
        if (StringUtils.isBlank(attr.getAppBannerSvgaUrl()) || CollectionUtils.isEmpty(attr.getAppBannerTextList())) {
            log.info("doFinishAppBanner ignore");
            return;
        }
        AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();

        boolean multiNick = attr.getRoleType() != RoleTypeSource.ROOM && attr.getRoleType() != RoleTypeSource.FAMILY;
        //svga内嵌文字
        List<Map<String, AppBannerSvgaText>> contentLayers = attr.getAppBannerTextList()
                .stream().map(item -> {
                    String appText = replaceText(item.getText(), member, multiNick);
                    return toAppBannerSvgaTextMap(item, appText);
                }).toList();

        broSvgaConfig.setContentLayers(contentLayers);

        //svga内嵌图片
        if (StringUtils.isNotBlank(attr.getAppBannerImgKey())) {
            broSvgaConfig.setImgLayers(List.of(Map.of(attr.getAppBannerImgKey(), member.getHeader())));
        }

        broSvgaConfig.setJump(0);
        broSvgaConfig.setHeight(0);
        broSvgaConfig.setDuration(attr.getAppBannerShowSec());
        broSvgaConfig.setLoops(1);

        AppBannerLayout layout = new AppBannerLayout();
        layout.setType(0);
        broSvgaConfig.setLayout(layout);

        broSvgaConfig.setLevel(1);
        broSvgaConfig.setWhRatio("20:13");
        broSvgaConfig.setSvgaURL(attr.getAppBannerSvgaUrl());

        int appBroBusiness = BroadCastHelpService.toAppBroBusiness(attr.getBusiId());

        List<Long> uidList = Lists.newArrayList();
        if (attr.getRoleType() == RoleTypeSource.BABY || attr.getRoleType() == RoleTypeSource.USER) {
            uidList = List.of(member.getUid());
        }
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), member.getSeq(), appBroBusiness,
                FstAppBroadcastType.ALL_TEMPLATE, 0, 0, "", uidList);

        appBannerEvent.setContentType(SVGA_CONENT_TYPE);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(attr.getBusiId()));
        appBannerEvent.setSvgaConfig(broSvgaConfig);

        RetryTool.withRetryCheck(attr.getActId(), makeKey(attr, "appBro_" + member.getSeq()), () -> kafkaService.sendAppBannerKafka(appBannerEvent));
    }

    /**
     * pc 单播
     *
     * @param attr
     * @param member
     */

    public void pcUnicast(AuctionNoticeComponentAttr attr, AuctionNoticeComponent.BannerMember member) {
        String text = attr.getAppUnicastText().get(member.getType());

        text = replaceText(text, member, false);
        Map<String, Object> info = Map.of(
                "roleType", attr.getRoleType(),
                "text", text,
                "logo", member.getHeader(),
                "nick", member.getNick());

        Integer bannerId = TYPE_PC_UNICAST_BANNER_ID_MAP.get(member.getType());

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(String.valueOf(bannerId))
                .setNoticeValue(JsonUtils.serialize(info));

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        RetryTool.withRetryCheck(attr.getActId(), makeKey(attr, "pcUnicast_" + member.getSeq()), () -> {
            svcSDKService.unicastUid(member.getUid(), msg);
            log.info("pcUnicast done@uid:{},msg:{}", member.getUid(), JsonUtils.serialize(msg));
        });


    }

    /**
     * app单播
     *
     * @param attr
     * @param member
     */
    public void appUnicast(AuctionNoticeComponentAttr attr, AuctionNoticeComponent.BannerMember member) {
        //不触发app单播
        if(attr.getSendAppUnicast()==0){
            log.info("appUnicast not sendAppUnicast  member:{}",member);
            return;
        }
        String text = attr.getAppUnicastText().get(member.getType());

        List<Long> nickUidList = Lists.newLinkedList();
        if (text.contains("{nick}")) {
            nickUidList.add(member.getUid());
        }

        boolean multiNick = attr.getRoleType() != RoleTypeSource.ROOM && attr.getRoleType() != RoleTypeSource.FAMILY;
        final String textFinal = replaceText(text, member, multiNick);
        RetryTool.withRetryCheck(attr.getActId(), makeKey(attr, "appUnicast_" + member.getSeq()), () -> {
            commonBroadCastService.appUnicastCommonTopBanner(attr.getActId(), member.getUid(), attr.getBusiId(),
                    attr.getAppUnicastBgUrl(), attr.getAppUnicastIconUrl(), textFinal, null, nickUidList);
            log.info("appUnicast done@uid:{}", member.getUid());
        });


    }


    /**
     * 替换配置的占位符
     *
     * @param text
     * @param member
     * @param multiNick
     * @return
     */
    private String replaceText(String text, AuctionNoticeComponent.BannerMember member, boolean multiNick) {
        return text.replace("{nick}", multiNick ? String.format("{%s:n}", member.getUid()) : member.getNick())
                .replace("{score}", toShowScore(member.getScore()))
                .replace("{diffScore}", toShowScore(member.getDiffScore()))
                .replace("{goalScore}", toShowScore(member.getGoalScore()))
                .replace("{awardValue}", String.valueOf(member.getAwardValue()))
                .replace("{awardName}", member.getAwardName());

    }

    public void doStatic(AuctionNoticeComponentAttr attr, AuctionNoticeComponent.BannerMember member) {
        try {

            String familyName = StringUtils.trimToEmpty(commonService.getRoomInfoBySsid(member.getSsid()).getFamilyName());
            String fly = String.format("yy://pd-[sid=%d&subid=%d]", member.getSid(), member.getSsid());
            long level = attr.getAwardMap().keySet().stream().filter(i -> i <= member.getGoalScore()).count();


            String roleName = switch (attr.getRoleType()) {
                case 100 -> "用户";
                case 200 -> "主持";
                case 400 -> "公会";
                case 401 -> "厅";
                case 404 -> "强厅";
                case 700 -> "家族";
                default -> "";
            };

            String content = "member：" + member.getMemberId() + "\n" +
                    roleName + "昵称：" + member.getNick() + "\n" +
                    "所属家族名称：" + familyName + "\n" +
                    "等级：" + level + "\n" +
                    "获得奖励：" + member.getAwardName() + "\n" +
                    roleName + "头像：" + member.getHeader() + "\n" +
                    "飞机票：" + fly + "\n";

            String msg = buildActRuliuMsg(attr.getActId(), false, "决赛拍卖", content);
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
        } catch (Exception e) {
            log.error("doStatic error@data:{} {}", member, e.getMessage(), e);
        }

    }

    /**
     * svga 内嵌文字配置
     *
     * @param textConfig
     * @param text
     * @return
     */
    private Map<String, AppBannerSvgaText> toAppBannerSvgaTextMap(BannerSvgaTextConfig textConfig, String text) {
        AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();

        appBannerSvgaText.setText(text);
        appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
        appBannerSvgaText.setGravity(textConfig.getGravity());
        if (StringUtil.isNotBlank(textConfig.getImages())) {
            appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
        }
        if (StringUtil.isNotBlank(textConfig.getFontSize())) {
            appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
        }
        return Map.of(textConfig.getKey(), appBannerSvgaText);
    }


    private Long getNoticeUid(AuctionNoticeComponentAttr attr, String member) {
        if (attr.getRoleType() == RoleTypeSource.BABY || attr.getRoleType() == RoleTypeSource.USER) {
            return Long.parseLong(member);
        }

        if (attr.getNoticeUserCmptInx() > 0) {
            return whitelistComponent.getConfigValue(attr.getActId(), attr.getNoticeUserCmptInx(), member, Long.class);
        }

        return null;
    }

    private boolean satisfyChannel(long topSid, long subSid, String member, int roleType) {
        long sid, ssid;
        switch (roleType) {
            case RoleTypeSource.GUILD:
                sid = Long.parseLong(member);
                return topSid == sid;
            case RoleTypeSource.SUB_GUILD:
                String[] arr = StringUtils.split(member, StringUtil.UNDERSCORE);
                sid = Long.parseLong(arr[0]);
                ssid = Long.parseLong(arr[1]);
                return topSid == sid && subSid == ssid;
            case RoleTypeSource.ROOM:
                int roomId = Integer.parseInt(member);
                RoomInfo roomInfo = commonService.getRoomInfoByRoomId(roomId);
                return roomInfo != null && roomInfo.sid == topSid && roomInfo.ssid == subSid;
            case RoleTypeSource.FAMILY:
                long familyId = Long.parseLong(member);
                RoomInfo room = commonService.getRoomInfoBySsid(subSid);
                return room != null && room.familyId == familyId;
            case RoleTypeSource.BABY:
            case RoleTypeSource.USER:
                return true;
            default:
                return false;
        }
    }

    private String toShowScore(long score) {
        Map.Entry<Long, String> unit = SCORE_UNIT_MAP.floorEntry(score);
        if (unit == null) {
            return String.valueOf(score);
        } else {
            DecimalFormat df = new DecimalFormat("#.#");
            return df.format((float)score/unit.getKey())+ unit.getValue();
        }

    }


    @Data
    public static class BannerMember {
        /**
         * 1= 首次进入，2=即将达成，3=达成目标
         */
        private int type;
        private String seq;
        private long sid;
        private long ssid;
        private long uid;
        private String header;
        private String nick;
        private long score;
        private long goalScore;
        private long diffScore;
        private String awardName;
        private long awardValue;
        private String memberId;
    }
}
