package com.yy.gameecology.activity.client.http;

import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.TokenGrant;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-10 16:20
 **/
@Component
public class BigDaHttpClient {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String GAME_USER_SERVICE = "https://services.bigda.com";

    private static final String RECENTLY_VISITED_CHANNELS = "/data-api/bigda/paimon/query/wow_actv_user_sid";

    private static final String APP_KEY = "8AEB760C4FDE4495A15A9DD23513E579";

    private static final String APP_SECRET = "E5407A7576974F76BCC8BEAD0950AB33";

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 用户最近访问频道
     */
    public String queryUserRecentlyVisitedChannels(String uid) {
        String path = RECENTLY_VISITED_CHANNELS + "?uid=" + uid;
        String token = TokenGrant.token(APP_KEY, APP_SECRET, path, 300L);

        HttpHeaders headers = new HttpHeaders();
        headers.add("signToken", token);
        RequestEntity<?> entity = new RequestEntity<>(headers, HttpMethod.GET, URI.create(GAME_USER_SERVICE + path));

        ParameterizedTypeReference<BigDataRsp<List<RecentlyVisitedChannelsData>>> typeRef =
                new ParameterizedTypeReference<>() {
                };
        ResponseEntity<BigDataRsp<List<RecentlyVisitedChannelsData>>> response = restTemplate.exchange(entity, typeRef);


        if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null || response.getBody().getData() == null) {
            log.error("queryUserRecentlyVisitedChannels error, uid={}, code={}", uid, response.getStatusCode());
            return StringUtil.EMPTY;
        }
        if(CollectionUtils.isEmpty(response.getBody().getData())){
            return StringUtil.EMPTY;
        }
        return response.getBody().getData().get(0).getLast_sid_list();

    }

    @Data
    public static class RecentlyVisitedChannelsData{
        private String last_sid_list;
    }


    @Getter
    @Setter
    @ToString
    public static class BigDataRsp<T> {

        protected int code;

        protected String message;

        protected T data;

        protected int pageIndex;

        protected int totalCount;
    }


}
