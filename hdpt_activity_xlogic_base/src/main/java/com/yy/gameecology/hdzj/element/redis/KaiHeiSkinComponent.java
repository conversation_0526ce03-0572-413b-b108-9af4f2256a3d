package com.yy.gameecology.hdzj.element.redis;

import cn.yy.ent.zhuiya.task.noaward.gen.pb.NoAwardTask;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.YoPopupMessage;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.ActivityTimeEnd;
import com.yy.gameecology.activity.bean.mq.NaTaskFinishedEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanSignInEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskCompleteEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskPollOutEvent;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.yrpc.CurrencyClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaLoginClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaNoAwardTaskClient;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.NewUserStatus;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTask;
import com.yy.gameecology.hdzj.bean.daytask.UpdateDayTaskReq;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.KaiHeiQcoinsComponent;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.DayTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.KaiHeiSkinComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AppPopupConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.DayTaskConfig;
import com.yy.gameecology.hdzj.element.history.DayTaskComponent;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.login.LoginRecord;
import com.yy.protocol.pb.zhuiwan.signin.ZhuiwanSign;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-09-19 17:51
 **/
@UseRedisStore
@Component
@RestController
@RequestMapping("/cmpt/kaiHeiSkin")
public class KaiHeiSkinComponent extends BaseActComponent<KaiHeiSkinComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    private DayTaskComponent dayTaskComponent;

    @Autowired
    private ZhuiyaLoginClient loginClient;

    @Autowired
    private ZhuiyaNoAwardTaskClient zhuiyaNoAwardTaskClient;

    @Autowired
    private CurrencyClient currencyClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private KaiHeiQcoinsComponent kaiHeiQcoinsComponent;

    @Lazy
    @Autowired
    private KaiHeiSkinPortalComponent kaiHeiSkinPortalComponent;


    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private ZhuiyaLoginClient zhuiyaLoginClient;

    /**
     * 自动填写用户Q币发奖信息延迟任务
     */
    private static final String DELAY_HANDLE_FILL_QCOIN_INFO = "fill_qcion_account_info_task";

    /**
     * 激活任务时候，用于透传到追玩的活动任务id
     */
    private static final String ACTIVE_TASK_EXT_FIELD_NAME = "hdzkTaskId";

    /**
     * 已完成登录任务的标记
     */
    private static final String LOGIN_TASK_AWARD_TAG = "login_task_award:%s";

    /**
     * 最后1次点击去福利中心签到按钮时间 yyyyMMdd
     */
    private static final String LAST_JUMP_SIGN_DATE ="last_jump_sign_date:%s";

    /**
     * 需要手工激活的追玩任务id
     */
    private static final String MANUAL_ZHUI_YA_TASK_ID = "manual_zhuiya_task_id";

    /**
     * 需要自动激活的追玩任务id
     */
    private static final String AUTO_ACTIVE_ZHUI_YA_TASK_ID = "auto_active_zhuiya_task_id";

    /**
     * 完成了所有签到任务发奖成功标记
     */
    private static final String SIGN_AWARD_TAG = "sign_award_tag:%s";

    /**
     * 老用户领取奖励成功时间，用于入口隐藏
     */
    private static final String OLD_USER_FIRST_LOGIN_TIME = "old_user_first_login_time:%s";

    /**
     * 用户完成任务的截止时间
     */
    private static final String USER_SIGN_TASK_END_TIME = "user_sign_task_end_time:%s";

    /**
     * 设备首次登陆绑定的uid
     */
    private static final String DEVICE_USE_RECORD = "device_use_record:%s";

    /**
     * 完成所有签到任务标记
     */
    private static final String COMPLETE_ALL_TASK_TAG = "complete_all_task:%s";

    /**
     * 奖池消耗完的时间
     */
    private static final String POLL_OUT_TIME = "poll_out_time";


    //明确用户Q币发放成功时间
    private static final String QCOIN_AWARD_SUCCEED_TIME = "qcoin_award_succeed_time:%s";

    private static final String DAY_TASK_FINISH_STATISTIC = "day_task_finish_statistics";
    private static final String LOGIN_TASK_FINISH_STATISTIC = "login_task_finish_statistics";

    private static final String LOGIN_TASK_FINISH_DAY_STATISTIC = "login_task_finish_day_statistics:%s";




    @Override
    public Long getComponentId() {
        return ComponentId.KAI_HEI_SKIN;
    }

    /**
     * 线上验证用的签到验证接口
     */
    @RequestMapping("/testOnSignInEvent")
    public Response<String> testOnSignInEvent(long actId, long uid) {
        if (!commonService.isGrey(actId)) {
            return Response.fail(-1, "not grey");
        }
        var attr = tryGetUniqueComponentAttr(actId);
        ZhuiwanSignInEvent event = new ZhuiwanSignInEvent();
        event.setSignDate(new Date());
        event.setUid(uid);
        event.setApp(attr.getApp());
        onSignInEvent(event, attr);

        return Response.ok(Convert.toString(System.currentTimeMillis()));
    }


    /**
     * 签到任务-----打卡任务数值累加
     * 注意：因为都是新用户，所以不存在上线当天，活动开始前已经完成签到任务，需要同步数据的情况！！！
     */
    @HdzjEventHandler(value = ZhuiwanSignInEvent.class, canRetry = true)
    public void onSignInEvent(ZhuiwanSignInEvent event, KaiHeiSkinComponentAttr attr) {
        if (!event.getApp().equals(attr.getApp())) {
            return;
        }

        //当日没有点击过签到按钮的需要拦截
        String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String key = buildLastJumpSignDate(attr, event.getUid());
        String jumpDateCode = actRedisDao.get(getRedisGroupCode(attr.getActId()), key);
        if (!dateCode.equals(jumpDateCode)) {
            log.info("onSignInEvent not jump,uid:{},nowDateCode:{},jumpDateCode:{}", event.getUid(), dateCode, jumpDateCode);
            return;
        }

        //更新任务进度
        boolean updateResult = updateSignTask(attr, event.getSignDate(), event.getUid());

        log.info("onSignInEvent done,uid:{},updateResult:{},event:{},attr:{}", event.getUid(), updateResult, JSON.toJSONString(event), JSON.toJSONString(attr));
    }


    /**
     * 登陆事件，锁定新老用户、发奖
     */
    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLoginEvent(ZhuiwanLoginEvent event, KaiHeiSkinComponentAttr attr) {
        log.info("onZhuiwanLoginEvent event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        if (!event.getApp().equals(attr.getApp())) {
            return;
        }
        String memberId = Convert.toString(event.getUid());

        //没资格的用户也要占用设备使用
        isDeviceFirstUse(attr, event.getUid(), event.getHdid());

        String inPreWhiteList = whitelistComponent.getConfigValue(attr.getActId(), attr.getPreWhiteListCmptIndex(), memberId, String.class);
        if (StringUtil.isBlank(inPreWhiteList)) {
            log.info("not in white list");
            return;
        }

        var actInfo = actInfoService.queryActivityInfo(attr.getActId());

        int newUserState = getOrSaveUserNewState(actInfo, attr, event.getUid(), event.getHdid());
        log.info("onZhuiwanLoginEvent done,uid:{},newUserState:{}", event.getUid(), newUserState);
    }

    /**
     * 获取用户状态，如果不存在的时候，会根据当前给的uid和hdid写入用户状态再返回
     */
    public int getOrSaveUserNewState(ActivityInfoVo actInfo, KaiHeiSkinComponentAttr attr, long uid, String hdId) {

        String memberId = Convert.toString(uid);
        int userStatus = queryUserStatus(attr, uid);
        if (userStatus >= 0) {
            log.info("already in login white list");
            return userStatus;
        }

        if (StringUtil.isBlank(hdId)) {
            log.warn("hdid not find,uid:{}, hdid:{}", uid, hdId);
            return NewUserStatus.NOT_IN_LIST;
        }

        //活动结束前15天，停止资格名单写入
        if (inAddWhiteListEndTime(attr, actInfo, uid)) {
            log.info("getOrSaveUserNewState time up,uid:{}", uid);
            return NewUserStatus.NOT_IN_LIST;
        }



        //---写新老用户：登陆后才能判断设备，最终判断新老用户
        LoginRecord.LoginRecordReq req = LoginRecord.LoginRecordReq.newBuilder().setUid(uid).setHdid(hdId)
                .setApp(ZhuiyaPbCommon.App.APP_YOMI).build();
        LoginRecord.LoginRecordRsp loginRecordRsp = loginClient.queryLoginRecord(req);
        log.info("isNewUser info @req:{},rsp:{}", JsonFormat.printToString(req), JsonFormat.printToString(loginRecordRsp));
        if (loginRecordRsp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
            throw new BusinessException(500, loginRecordRsp.getMessage());
        }
        var loginInfo = loginRecordRsp.getResult();
        long uidFirstLoginTime = loginInfo.getUidFirstLoginTime();
        long deviceFirstLoginTime = loginInfo.getDeviceFirstLoginTime();
        if (uidFirstLoginTime == 0) {
            log.warn("getOrSaveUserNewState user login time is zero,uid:{},uidFirstLoginTime:{}", uid, uidFirstLoginTime);
            uidFirstLoginTime = System.currentTimeMillis();
        }
        if (deviceFirstLoginTime == 0) {
            log.warn("getOrSaveUserNewState device login time is zero,uid:{},deviceFirstLoginTime:{}", uid, deviceFirstLoginTime);
            deviceFirstLoginTime = System.currentTimeMillis();
        }
        long firstLoginTime = Math.min(uidFirstLoginTime, deviceFirstLoginTime);
        long timeOffset = System.currentTimeMillis() - DateUtil.ONE_HOUR_MILL_SECONDS;
        // firstLoginTime > timeOffset 是为了活动开始前，测试环境测试方便
        boolean newLogin = firstLoginTime > timeOffset || firstLoginTime > actInfo.getBeginTime();

        //设备是否首次使用
        boolean deviceFirstUse = isDeviceFirstUse(attr, uid, hdId);

        boolean newUser = newLogin && deviceFirstUse;
        String newUserStr = newUser ? "1" : "0";
        whitelistComponent.ignoreAdd(attr.getActId(), attr.getLoginUserWhiteListCmptIndex(), memberId, newUserStr);
        return Convert.toInt(newUserStr);
    }

    private boolean isDeviceFirstUse(KaiHeiSkinComponentAttr attr, long uid, String hdId) {
        if (StringUtil.isBlank(hdId)) {
            return false;
        }
        //设备是否首次使用
        String deviceKey = makeKey(attr, String.format(DEVICE_USE_RECORD, hdId));
        String redisCode = getRedisGroupCode(attr.getActId());
        String uidStr = Convert.toString(uid);
        boolean deviceFirstUse = actRedisDao.setNX(redisCode, deviceKey, uidStr);
        if (!deviceFirstUse) {
            String deviceUid = actRedisDao.get(redisCode, deviceKey);
            deviceFirstUse = uidStr.equals(deviceUid);
            log.info("not new device，actId:{},deviceKey:{},curUid:{},deviceUid:{}", attr.getActId(), deviceKey, uidStr, deviceUid);
        }

        return deviceFirstUse;
    }

    /**
     * 任务完成事件： 完成当天任务，激活下一天任务
     */
    @HdzjEventHandler(value = NaTaskFinishedEvent.class, canRetry = true)
    public void onNaTaskFinishedEvent(NaTaskFinishedEvent event, KaiHeiSkinComponentAttr attr) {
        log.info("onNaTaskFinishedEvent event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        String ext = event.getExpand();
        if (StringUtil.isBlank(ext)) {
            return;
        }
        JSONObject extObj = JSON.parseObject(ext);
        Long hdzkTaskId = extObj.getLong(ACTIVE_TASK_EXT_FIELD_NAME);
        if (hdzkTaskId == null) {
            return;
        }

        DayTaskComponentAttr dayTaskComponentAttr = dayTaskComponent.getComponentAttr(attr.getActId(), attr.getDayTaskCmptIndex());
        Optional<DayTaskConfig> dayTaskConfig = dayTaskComponentAttr.getDayTaskConfig().stream().filter(p -> p.getTaskId() == hdzkTaskId).findFirst();
        if (dayTaskConfig.isEmpty()) {
            return;
        }
        DayTaskConfig taskConfig = dayTaskConfig.get();

        //更新任务进度
        String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String seq = event.getTaskId() + "_" + event.getStage() + "_" + event.getUid() + "_" + event.getFinishedTime();
        if (updateTask(attr, seq, event.getUid(), dateCode, taskConfig.getPassItem())) {
            return;
        }

        log.info("onNaTaskFinishedEvent done,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
    }


    /**
     * 活动结束后发放奖励
     */
    @HdzjEventHandler(value = ActivityTimeEnd.class, canRetry = true)
    public void onActivityTimeEnd(ActivityTimeEnd event, KaiHeiSkinComponentAttr attr) {
        log.info("onActivityTimeEnd begin,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        String msg = buildActRuliuMsg(attr.getActId(), false, "活动结束通知", "活动结束，现在触发存量用户发奖");
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());

        releaseAllSignAward(attr);
    }


    /**
     * 奖池耗光触发发放奖励
     */
    @HdzjEventHandler(value = DayTaskPollOutEvent.class, canRetry = true)
    public void onDayTaskPollOutEvent(DayTaskPollOutEvent event, KaiHeiSkinComponentAttr attr) {
        log.info("onDayTaskPollOutEvent begin,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        String msg = buildActRuliuMsg(attr.getActId(), false, "奖池消耗完成通知", "奖池消耗完，现在触发存量用户发奖");
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());

        if (event.getPackageId() > 0 && event.getPackageId() == attr.getTaskPollOutPackageId()) {
            //奖池消耗完成时间
            String key = buildPoolOutTimeKey(attr);
            long actId = attr.getActId();
            actRedisDao.setNX(getRedisGroupCode(actId), key, Convert.toString(commonService.getNow(actId).getTime()));
            //全局发奖
            releaseAllSignAward(attr);
        }
    }


    /**
     * 用户完成所有任务，发放奖励
     */
    @HdzjEventHandler(value = DayTaskCompleteEvent.class, canRetry = true)
    public void onDayTaskCompleteEvent(DayTaskCompleteEvent event, KaiHeiSkinComponentAttr attr) {
        log.info("onDayTaskCompleteEvent begin,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        if (event.getDayIndex() == attr.getLastTaskDayIndex()) {
            log.info("onAllTaskCompleteEvent begin award,uid:{}",event.getMemberId());
            //打上完成任务标记
            String key = buildCompleteAllTaskKey(attr, Convert.toLong(event.getMemberId()));
            actRedisDao.set(getRedisGroupCode(attr.getActId()), key, "1");

            //发放奖励
            releaseSignAward(Convert.toLong(event.getMemberId()),attr);
            log.info("onAllTaskCompleteEvent done award,uid:{}",event.getMemberId());
        }


        //获奖顶部提醒
        if (attr.getAwardNotice().containsKey(event.getDayIndex())) {
            AppPopupConfig config = attr.getAwardNotice().get(event.getDayIndex());
            String seq = makeKey(attr, String.format("notice:%s:%s", event.getDayIndex(), event.getMemberId()));
            doSendNotice(seq, Convert.toLong(event.getMemberId()), attr.getAwardPopUpTitle(), config.getText(), config.getLink());
        }

        //更新任务完成统计
        updateDayTaskStatic(attr, event.getDayIndex(), event.getSeq());

        //看下一级任务是否需要自动激活 TODO 待测试
        var dayAttr = dayTaskComponent.getComponentAttr(attr.getActId(), attr.getDayTaskCmptIndex());
        int nextIndex = event.getDayIndex() + 1;
        List<DayTaskConfig> nextDayTaskConfigList = dayAttr.getDayTaskConfig()
                .stream()
                .filter(p -> p.getTaskDayIndex() == nextIndex)
                .toList();
        if (!CollectionUtils.isEmpty(nextDayTaskConfigList)) {
            for (DayTaskConfig config : nextDayTaskConfigList) {
                autoActiveNextTask(Convert.toLong(event.getMemberId()), attr, config);
            }
        }
    }

    private void updateDayTaskStatic(KaiHeiSkinComponentAttr attr, int dayIndex, String eventSeq) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr, DAY_TASK_FINISH_STATISTIC);
        String seq = makeKey(attr,"seq:daytaskStatic:" + eventSeq);
        actRedisDao.hIncrByKeyWithSeq(groupCode, seq, key, String.valueOf(dayIndex), 1, DateUtil.ONE_WEEK_SECONDS);
    }

    @PostConstruct
    @NeedRecycle(notRecycle = true, author = "zengwenzhi")
    public void init() throws Exception {
        registerDelayQueue(DELAY_HANDLE_FILL_QCOIN_INFO, this::fillInAccountInfo);
    }

    @Scheduled(cron = "0 0/1 * * * ?")
    @PostConstruct
    @NeedRecycle(notRecycle = true, author = "zengwenzhi")
    public void releaseTimeUpAward() throws Exception {
        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(activityInfoVos)) {
            for (ActivityInfoVo actInfo : activityInfoVos) {
                long actId = actInfo.getActId();
                Date now = commonService.getNow(actId);
                if (!actInfoService.inActTime(now, actInfo)) {
                    continue;
                }
                var attr = tryGetUniqueComponentAttr(actId);
                if (attr == null) {
                    continue;
                }
                String runKey = makeKey(attr, "releaseTimeUpAward:" + DateUtil.format(now, DateUtil.PATTERN_TYPE2));
                boolean run = actRedisDao.setNX(getRedisGroupCode(actId), runKey, Convert.toString(System.currentTimeMillis()));
                if (run) {
                    releaseTimeUpAward(attr);
                }
            }
        }
    }


    /**
     * 登陆任务---打开登陆页面发奖
     */
    @RequestMapping("/login")
    public Response<Map<String, Object>> login(long actId, long cmptIndex, Integer draw,
                                  HttpServletRequest request, HttpServletResponse response,String verifyCode, String recordId, String verifyToken) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "not login");
        }
        log.info("login,actId:{},uid:{},draw:{}", actId, uid, draw);
        String redisCode = getRedisGroupCode(actId);
        var attr = getComponentAttr(actId, cmptIndex);
        String sendKey = makeKey(attr, String.format(LOGIN_TASK_AWARD_TAG, uid));
        String alreadySend = actRedisDao.get(redisCode, sendKey);
        //发奖接口幂等，所以这里可以先读标记， 不用setNX也无接口并发调用，奖品多发问题
        if (StringUtil.isNotBlank(alreadySend)) {
            log.info("login already execute,actId:{},uid:{},draw:{}", actId, uid, draw);
            return Response.fail(999, "");
        }

        int userState = queryUserStatus(attr, uid);
        if (userState == NewUserStatus.NOT_IN_LIST) {
            log.info("not in white list,actId:{},uid:{}", actId, uid);
            return Response.fail(1, "没有活动参与资格");
        }

        if (Convert.toInt(draw, 0) == 0) {
            log.info("login not draw,actId:{},uid:{}", actId, uid);
            return Response.ok("not auto award");
        }

        //活动已结束
        if (!actInfoService.inActTime(attr.getActId())) {
            return Response.fail(3, "活动已结束");
        }

        //风控
        if (StringUtil.isNotBlank(attr.getRiskStrategyKey())) {
            try {
                zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);
            } catch (SuperException e) {
                log.warn("login doRiskCheck warn,actId:{},uid:{},e:{},e", actId, uid, e.getMessage(), e);
                if (e.getData() != null) {
                    return Response.fail(e.getCode(), e.getMessage(), Map.of("riskRecheck", e.getData()));
                } else {
                    return Response.fail(e.getCode(), e.getMessage());
                }
            } catch (Exception e) {
                log.error("login doRiskCheck error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
                return Response.fail(SuperException.E_FAIL, "网络超时");
            }
        }


        //新用户奖池扣减
        if (userState == NewUserStatus.NEW_USER) {
            if (isPoolOut(attr)) {
                return Response.fail(3, "奖池已发完，活动结束");
            }
            var dayTaskAttr = dayTaskComponent.getComponentAttr(actId, attr.getDayTaskCmptIndex());
            String seq = makeKey(attr, "logintask:" + uid);
            List<Long> addResult = dayTaskComponent.reduceAwardPool(dayTaskAttr, Convert.toString(uid), attr.getQCoinAwardPoolTaskId(), attr.getQCoinAwardPoolPackageId(), seq, attr.getLoginAwardNewUserQCoinValue());
            if (addResult.get(0) <= 0) {
                return Response.fail(3, "奖池已发完，活动结束");
            }
        }

        //发奖
        long packageId = userState == NewUserStatus.NEW_USER ? attr.getLoginAwardNewUserPackageId() : attr.getLoginAwardOldUserPackageId();
        String awardSeq = makeKey(attr, "newUserAward:" + uid);
        String awardSeqMd5 = MD5SHAUtil.getMD5(awardSeq);
        Date now = commonService.getNow(attr.getActId());
        String time = DateUtil.format(now);
        Map<Long, Integer> packageIdAmount = ImmutableMap.of(packageId, 1);
        long taskId = attr.getLoginAwardTaskId();
        Map<String, String> ext = Maps.newHashMap();

        hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), uid, taskId, ImmutableMap.of(taskId, packageIdAmount), awardSeqMd5, ext);
        log.info("login award done,actId:{},uid:{},awardSeq:{},awardSeqMd5:{},packageId:{}", attr.getActId(), uid, awardSeq, awardSeqMd5, packageId);

        //发奖顶部提醒
        if (userState == NewUserStatus.OLD_USER) {
            if (attr.getAwardNotice().containsKey(0)) {
                AppPopupConfig config = attr.getAwardNotice().get(0);
                String seq = makeKey(attr, String.format("notice:%s:%s", 0, uid));
                doSendNotice(seq, uid, attr.getAwardPopUpTitle(), config.getText(), config.getLink());
            }
            //写老用户入口1天后消失逻辑标记
            log.info("not new user,actId:{},uid:{}", attr.getActId(), uid);
            Date firstLogin = commonService.getNow(attr.getActId());
            setOldUserFirstLoginTime(attr, uid, firstLogin.getTime());
        }


        //所有写操作完成后再写完成发奖标记，确保发奖流程能执行到
        actRedisDao.set(redisCode, sendKey, DateUtil.format(new Date()));

        //新用户刷新登录任务统计
        if (userState == NewUserStatus.NEW_USER) {
            updateLoginTaskStatic(attr, uid,now);
        }
        return Response.ok();
    }


    @RequestMapping("/taskList")
    public Response<Map<String, Object>> taskList(long actId, long cmptIndex, Long userId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "not login");
        }
        if (userId != null) {
            uid = userId;
        }
        Map<String, Object> result = Maps.newHashMap();

        //任务读取
        KaiHeiSkinComponentAttr attr = getComponentAttr(actId, cmptIndex);
        CurDayTask curDayTask = dayTaskComponent.queryCurDayTask(actId, attr.getDayTaskCmptIndex(), Convert.toString(uid));
        result.put("dayIndex", curDayTask.getDayShowIndex());
        result.put("curTaskItem", curDayTask.getCurTaskItem());
        result.put("signTaskEndTime", calSignTaskEndTime(attr, uid));
        //已完成打卡的天数
        result.put("signedAmount", curDayTask.getSignedDay());
        //总15天任务领奖状态
        result.put("awardState", calAwardState(attr, uid));
        //奖池消耗完
        result.put("poolOut", isPoolOut(attr));
        //奖池余额(单位，分)
        var dayTaskAttr = dayTaskComponent.getComponentAttr(attr.getActId(), attr.getDayTaskCmptIndex());
        var balance =dayTaskComponent.queryPoolBalance(dayTaskAttr, attr.getQCoinAwardPoolPackageId());
        result.put("poolBalance", balance / 100);
        //任务是否已结束
        result.put("signTaskTimeUp", signTaskTimeUp(attr, uid));

        return Response.success(result);
    }

    /**
     * 点击去完成，激活任务
     */
    @RequestMapping("/jumpTask")
    public Response<Map<String, Object>> jumpTask(long actId, long cmptIndex, String curTaskItem) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "not login");
        }
        log.info("jumpTask begin,actId:{},cmptIndex:{},uid:{},curTaskItem:{}", actId, cmptIndex, uid, curTaskItem);

        var attr = getComponentAttr(actId, cmptIndex);
        boolean isPoolOut = isPoolOut(attr);
        if (isPoolOut) {
            return Response.fail(1, "奖池已抢完，活动已结束");
        }

        if (signTaskTimeUp(attr, uid)) {
            return Response.fail(2, "任务时间已截止");
        }

        //活动已结束
        if (!actInfoService.inActTime(attr.getActId())) {
            return Response.fail(3, "活动已结束");
        }

        try {
            Map<String, Object> result = Maps.newHashMap();
            String jumpLink = manualActiveUserTask(attr, uid, curTaskItem);
            result.put("jumpLink", jumpLink);
            log.info("jumpTask done,actId:{},cmptIndex:{},uid:{},curTaskItem:{},result:{}", actId, cmptIndex, uid, curTaskItem, JSON.toJSONString(result));
            return Response.success(result);

        } catch (SuperException e) {
            log.warn("jumpTask warn,actId:{},cmptIndex:{},uid:{},curTaskItem:{},e:{}", actId, cmptIndex, uid, curTaskItem, e.getMessage(), e);
            return Response.fail(2, e.getMessage());
        } catch (Exception e) {
            log.warn("jumpTask error,actId:{},cmptIndex:{},uid:{},curTaskItem:{},e:{}", actId, cmptIndex, uid, curTaskItem, e.getMessage(), e);
            return Response.fail(500, "网络超时");
        }

    }


    /**
     * 更新福利大厅签到任务
     */
    private boolean updateSignTask(KaiHeiSkinComponentAttr attr, Date taskDate, long uid) {
        //更新任务进度
        Date signDate = attr.isSignTaskUseTestTime() ? commonService.getNow(attr.getActId()) : taskDate;
        String dateCode = DateUtil.format(signDate, DateUtil.PATTERN_TYPE2);
        String seq = "sign:" + uid + ":" + dateCode;
        if (!updateTask(attr, seq, uid, dateCode, attr.getSignTaskItemId())) {
            return false;
        }

        //写任务截止时间
        setSignTaskEndTime(attr, uid);

        return true;
    }


    private boolean updateTask(KaiHeiSkinComponentAttr attr, String seq, long uid, String dateCode, String item) {
        log.info("updateTask seq:{},uid:{},dayCode:{},item:{}", seq, uid, dateCode, item);
        //新用户任务资格名单判断
        if (!isNewUser(attr, uid)) {
            log.info("updateTask return,not new user,uid:{}", uid);
            return false;
        }

        //任务截止时间
        if (signTaskTimeUp(attr, uid)) {
            log.info("updateTask return,time up,uid:{}", uid);
            return false;
        }

        //奖池消耗完
        if (isPoolOut(attr)) {
            log.info("updateTask pool out");
            return false;
        }

        //不在活动时间范围内
        if(!actInfoService.inActTime(attr.getActId())){
            log.info("updateTask act end");
            return false;
        }

        //更新任务进度
        UpdateDayTaskReq req = new UpdateDayTaskReq();
        req.setActId(attr.getActId());
        req.setCmptIndex(attr.getDayTaskCmptIndex());
        req.setSeq(seq);
        req.setMember(Convert.toString(uid));
        req.setDayCode(dateCode);
        req.setItem(item);
        req.setValue(1);
        dayTaskComponent.updateTask(req);

        return true;
    }


    /**
     * 设置任务截止时间
     */
    private void setSignTaskEndTime(KaiHeiSkinComponentAttr attr, long uid) {
        String signEndKey = buildUserSignTaskEndTimeKey(attr, uid);
        long effectEndTime = DateUtil.getDayEndTime(DateUtil.add(commonService.getNow(attr.getActId()), attr.getSignDayEffectDay())).getTime();
        boolean firstSet = actRedisDao.setNX(getRedisGroupCode(attr.getActId()), signEndKey, Convert.toString(effectEndTime));
        if (firstSet) {
            log.info("onSignInEvent set use signTaskEndTime,actId:{},uid:{},time:{}", attr.getActId(), uid, effectEndTime);
        }
    }

    /**
     * 任务截止时间，取min(任务截止时间,活动结束时间)
     */
    private long calSignTaskEndTime(KaiHeiSkinComponentAttr attr, long uid) {
        String signEndKey = buildUserSignTaskEndTimeKey(attr, uid);
        String endTimeStr = actRedisDao.get(getRedisGroupCode(attr.getActId()), signEndKey);
        if (StringUtil.isBlank(endTimeStr)) {
            return 0;
        }
        var actInfo = actInfoService.queryActivityInfo(attr.getActId());
        long actEndTime = actInfo.getEndTime();
        return Math.min(Convert.toLong(endTimeStr), actEndTime);
    }

    /**
     * 是否任务截止
     */
    private boolean signTaskTimeUp(KaiHeiSkinComponentAttr attr, long uid) {
        String signEndKey = buildUserSignTaskEndTimeKey(attr, uid);
        String endTimeStr = actRedisDao.get(getRedisGroupCode(attr.getActId()), signEndKey);
        if (StringUtil.isBlank(endTimeStr)) {
            return false;
        }
        long now = commonService.getNow(attr.getActId()).getTime();

        return now > Convert.toLong(endTimeStr);
    }


    /**
     * 触发需要手工点击跳转任务后才需要激活的任务
     */
    public String manualActiveUserTask(KaiHeiSkinComponentAttr attr, long uid, String curTaskItem) {

        if (attr.getSignTaskItemId().equals(curTaskItem)) {
            return manualActiveSignTask(attr, uid, curTaskItem);
        } else {
            return manualActiveZhuiyaTask(attr, uid, curTaskItem);
        }
    }

    public String manualActiveSignTask(KaiHeiSkinComponentAttr attr, long uid, String curTaskItem) {

        //记录最近一次点击事件，用于判断签到事件是否可以自动完成任务
        String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String key = buildLastJumpSignDate(attr, uid);
        actRedisDao.set(getRedisGroupCode(attr.getActId()), key, dateCode);

        ZhuiwanSign.LastSignInRecordReq req = ZhuiwanSign.LastSignInRecordReq.newBuilder().setApp(ZhuiyaPbCommon.App.APP_YOMI).setUid(uid).build();
        ZhuiwanSign.LastSignInRecordRsp rsp = zhuiyaLoginClient.queryLastSignInRecord(req);
        if (rsp == null || rsp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
            log.error("queryLastSignInRecord error,req:{},rsp:{}", JsonFormat.printToString(req), rsp == null ? null : JsonFormat.printToString(rsp));
            return attr.getSignTaskJumpUrl();
        }
        String lastSignDate = rsp.getResult().getLastSignDate();
        String today = DateUtil.format(new Date(), DateUtil.PATTERN_TYPE2);
        if (today.equals(lastSignDate)) {
            boolean result = updateSignTask(attr, commonService.getNow(attr.getActId()), uid);
            log.info("syncCurDaySign done,uid:{},result:{}", uid, result);
            throw new SuperException("您今日已在福利专区签到，任务已完成", Code.E_DUP_DATA.getCode());
        }

        return attr.getSignTaskJumpUrl();
    }

    public String manualActiveZhuiyaTask(KaiHeiSkinComponentAttr attr, long uid, String curTaskItem) {
        long actId = attr.getActId();
        String jumpUrl = StringUtil.EMPTY;
        List<DayTaskConfig> dayTaskConfigs = dayTaskComponent.queryDayTaskConfig(actId, attr.getDayTaskCmptIndex(), Convert.toString(uid));
        Optional<DayTaskConfig> dayTaskConfig = dayTaskConfigs
                .stream()
                .filter(p -> p.getPassItem().equals(curTaskItem) && p.getTaskExt().contains(MANUAL_ZHUI_YA_TASK_ID))
                .findFirst();
        if (dayTaskConfig.isEmpty()) {
            return jumpUrl;
        }
        DayTaskConfig taskConfig = dayTaskConfig.get();
        JSONObject extData = JSON.parseObject(taskConfig.getTaskExt());
        JSONArray zhuiyaTaskIds = extData.getJSONArray(MANUAL_ZHUI_YA_TASK_ID);
        long taskStartTime = DateUtil.getDayBeginTime(new Date()).getTime();
        var activityInfo = actInfoService.queryActivityInfo(actId);
        long endTime = activityInfo.getEndTime();

        for (Object taskIdObj : zhuiyaTaskIds) {
            long taskId = Convert.toLong(taskIdObj);
            long activeTime = attr.getActiveTimeDayFirstZhuiyaTaskId().contains(taskId)
                    ? DateUtil.getDayBeginTime(new Date()).getTime()
                    : System.currentTimeMillis();

            boolean reset = !attr.getNotResetZhuiyaTaskId().contains(taskId);
            String expand = JSON.toJSONString(ImmutableMap.of(ACTIVE_TASK_EXT_FIELD_NAME, taskConfig.getTaskId()));
            NoAwardTask.ActiveUserTaskReq req = NoAwardTask.ActiveUserTaskReq
                    .newBuilder().setUid(uid)
                    .setTaskId(taskId)
                    .setStage(Convert.toString(taskConfig.getTaskDayIndex()))
                    .setStartTime(taskStartTime)
                    .setEndTime(endTime)
                    .setActiveTime(activeTime)
                    .setResetProgress(reset)
                    .setExpand(expand)
                    .build();
            NoAwardTask.ActiveUserTaskRsp rsp = zhuiyaNoAwardTaskClient.activeUserTask(req);
            boolean rspError = rsp == null || rsp.getCode() != 0;
            if (rspError) {
                log.error("activeUserTask error,req:{},rsp:{}", JsonFormat.printToString(req), rsp == null ? null : JsonFormat.printToString(rsp));
                continue;
            }
            //已完成任务
            if (rsp.getUserTask().getState() == 1) {
                String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
                String seq = "synctask:" + taskId + ":" + uid;
                updateTask(attr, seq, uid, dateCode, curTaskItem);
                throw new SuperException("任务已完成", Code.E_DUP_DATA.getCode());
            }
            //取任意1个
            if (StringUtil.isNotBlank(rsp.getUserTask().getJumpUrl())) {
                jumpUrl = rsp.getUserTask().getJumpUrl();
            }

        }
        return jumpUrl;
    }

    private void autoActiveNextTask(long uid, KaiHeiSkinComponentAttr attr, DayTaskConfig taskConfig) {
        log.info("autoActiveNextTask,uid:{},taskConfig:{}", uid, JSON.toJSONString(taskConfig));
        String taskExt = taskConfig.getTaskExt();
        if (StringUtil.isBlank(taskExt)) {
            return;
        }
        JSONObject taskExtObj = JSON.parseObject(taskExt);
        if (!taskExtObj.containsKey(AUTO_ACTIVE_ZHUI_YA_TASK_ID)) {
            return;
        }
        log.info("autoActiveNextTask begin,uid:{},taskConfig:{}", uid, JSON.toJSONString(taskConfig));
        //有配置要激活下一级任务的，才需要激活
        JSONArray zhuiyaTaskIds = taskExtObj.getJSONArray(AUTO_ACTIVE_ZHUI_YA_TASK_ID);
        long taskStartTime = DateUtil.getDayBeginTime(new Date()).getTime();
        var activityInfo = actInfoService.queryActivityInfo(attr.getActId());
        long endTime = activityInfo.getEndTime();

        for (Object taskIdObj : zhuiyaTaskIds) {
            long taskId = Convert.toLong(taskIdObj);
            //第15天自动激活的任务，要次日0点才能开始做
            long activeTime = attr.getActiveTimeDayFirstZhuiyaTaskId().contains(taskId)
                    ? DateUtil.getDayBeginTime(DateUtil.add(new Date(), Const.ONE)).getTime()
                    : System.currentTimeMillis();

            boolean reset = !attr.getNotResetZhuiyaTaskId().contains(taskId);
            String expand = JSON.toJSONString(ImmutableMap.of(ACTIVE_TASK_EXT_FIELD_NAME, taskConfig.getTaskId()));
            NoAwardTask.ActiveUserTaskReq taskReq = NoAwardTask.ActiveUserTaskReq
                    .newBuilder().setUid(uid)
                    .setTaskId(taskId)
                    .setStage(Convert.toString(taskConfig.getTaskDayIndex()))
                    .setStartTime(taskStartTime)
                    .setEndTime(endTime)
                    .setActiveTime(activeTime)
                    .setResetProgress(reset)
                    .setExpand(expand)
                    .build();
            NoAwardTask.ActiveUserTaskRsp rsp = zhuiyaNoAwardTaskClient.activeUserTask(taskReq);
            log.info("autoActiveNextTask done,uid:{},taskId:{},stage:{}", uid, taskId, taskReq.getStage());
            if (rsp == null || rsp.getCode() != 0) {
                log.error("autoActiveNextTask error,req:{},rsp:{}", JsonFormat.printToString(taskReq), rsp == null ? null : JsonFormat.printToString(rsp));
            }
        }
    }

    private int calAwardState(KaiHeiSkinComponentAttr attr, long uid) {

        String redisCode = getRedisGroupCode(attr.getActId());
        int state = AwardStatus.NOT_COMPLETE;
        //任务截止、任务完成、奖池耗尽
        boolean completeTask = isCompleteAllTask(attr, uid);
        boolean signTaskTimeUp = signTaskTimeUp(attr, uid);
        boolean poolOut = isPoolOut(attr);
        long balance = -1;
        if (!completeTask && !signTaskTimeUp && !poolOut) {
            state = AwardStatus.NOT_COMPLETE;
        } else {
            List<KaiHeiQcoinsComponent.UserReleaseInfo> records = kaiHeiQcoinsComponent
                    .getTaskAwardState(attr.getActId(), uid, (int) attr.getSignTaskAwardTaskId(), Lists.newArrayList(attr.getSignTaskAwardPackageMap().values()));
            balance = queryUserBalance(attr, uid);
            if (!CollectionUtils.isEmpty(records)) {
                //追玩status订单状态,1=有效，4=发放中，6=等待完善信息
                boolean firstSet = false;
                if (records.stream().allMatch(p -> p.getStatus() == 1)) {
                    state = AwardStatus.AWARDED;
                    //用于计算入口隐藏时间
                    String awardSucceedTimeKey = buildQCoinAwardSucceedTimeKey(attr, uid);
                    firstSet = actRedisDao.setNX(redisCode, awardSucceedTimeKey, Convert.toString(commonService.getNow(attr.getActId()).getTime()));

                } else if (records.stream().anyMatch(p -> p.getStatus() == 4)) {
                    state = AwardStatus.AWARD_ING;
                } else if (records.stream().anyMatch(p -> p.getStatus() == 6)) {
                    state = AwardStatus.TO_SUMMIT;
                }
                log.info("getTaskAwardState uid:{},state:{},firstSet:{},records:{}", uid, state, firstSet, JSON.toJSONString(records));
            } else if (balance > 0) {
                //异步发的奖还没到账
                state = AwardStatus.NOT_AWARDED_OR_NOT_SEND;
                log.warn("not found award record,uid:{},balance:{}", uid, balance);
            } else if (balance == 0) {
                //如果没Q币发
                state = AwardStatus.NOT_AWARDED_OR_NOT_SEND;
            }
        }
        log.info("calAwardState uid:{},completeTask:{},signTaskTimeUp：{},poolOut:{},balance:{},state:{}", uid, completeTask, signTaskTimeUp, poolOut, balance, state);
        return state;
    }

    private boolean isCompleteAllTask(KaiHeiSkinComponentAttr attr, long uid) {
        String key = buildCompleteAllTaskKey(attr, uid);
        String complete = actRedisDao.get(getRedisGroupCode(attr.getActId()), key);
        return StringUtil.isNotBlank(complete);
    }


    /**
     * 奖池消耗完发放奖励
     * 活动结束后发放奖励
     */
    public void releaseAllSignAward(KaiHeiSkinComponentAttr attr) {
        Clock clock = new Clock();
        List<Long> memberIds = whitelistComponent.getMemberList(attr.getActId(), attr.getLoginUserWhiteListCmptIndex());
        log.info("releaseAllSignAward memberSize:{},clock:{}", memberIds.size(), clock.tag());
        for (Long uid : memberIds) {
            releaseSignAward(uid, attr);
            SysEvHelper.waiting(10);
        }
        log.info("releaseAllSignAward done memberSize:{},clock:{}", memberIds.size(), clock.tag());
    }

    /**
     * 任务截止发奖
     */
    public void releaseTimeUpAward(KaiHeiSkinComponentAttr attr) {
        Clock clock = new Clock();
        List<Long> memberIds = whitelistComponent.getMemberList(attr.getActId(), attr.getLoginUserWhiteListCmptIndex());
        log.info("releaseTimeUpAward memberSize:{},clock:{}", memberIds.size(), clock.tag());
        int realTimeUpSize = 0;
        for (Long uid : memberIds) {
            boolean timeUp = signTaskTimeUp(attr, uid);
            if (!timeUp) {
                continue;
            }
            realTimeUpSize++;
            log.info("releaseTimeUpAward begin uid:{}", uid);
            releaseSignAward(uid, attr);
            SysEvHelper.waiting(10);
        }
        log.info("releaseTimeUpAward done memberSize:{},realTimeUpSize:{},clock:{}", memberIds.size(), realTimeUpSize, clock.tag());
    }

    /**
     * 1、奖池消耗完发放奖励
     * 2、15天完成任务后发发放奖励
     * 3、活动结束后发放奖励
     */
    public void releaseSignAward(long uid, KaiHeiSkinComponentAttr attr) {
        log.info("releaseSignAward begin,uid:{}", uid);


        String redisCode = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr, String.format(SIGN_AWARD_TAG, uid));
        //发奖已经有seq去重，这里读出来判断不怕有并发问题
        String alreadySend = actRedisDao.get(redisCode, key);
        if (StringUtil.isNotBlank(alreadySend)) {
            log.info("already releaseSignAward,uid:{}", uid);
            return;
        }

        CurDayTask curDayTask = dayTaskComponent.queryCurDayTask(attr.getActId(), attr.getDayTaskCmptIndex(), Convert.toString(uid));
        if (curDayTask == null) {
            log.info("get empty task,uid:{}", uid);
            return;
        }

        long awardTaskId = attr.getSignTaskAwardTaskId();
        long signTaskAwardPackageId = findReleasePackageId(attr, curDayTask.getSignedDay());
        if (signTaskAwardPackageId == 0) {
            log.warn("releaseSignAward amount,not found packageId,uid:{},signDate:{}", uid, curDayTask.getSignedDay());
            return;
        }

        Map<Long, Integer> packageIdAmount = ImmutableMap.of(signTaskAwardPackageId, 1);
        //异步发奖
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        String awardSeq = makeKey(attr, "settleReleaseAward:" + uid);
        String awardSeqMd5 = MD5SHAUtil.getMD5(awardSeq);
        hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), uid, awardTaskId, ImmutableMap.of(awardTaskId, packageIdAmount), awardSeqMd5, Maps.newHashMap());

        //用登录任务填写的Q币信息填入 签到发奖信息，减少用户填写次数; 需要延迟已发放，否则记录可能不存在
        KaiHeiQcoinsComponent.FillQCoinAccountInfo accountInfo = new KaiHeiQcoinsComponent.FillQCoinAccountInfo();
        accountInfo.setActId(attr.getActId());
        accountInfo.setUid(uid);
        accountInfo.setTaskId(attr.getSignTaskAwardTaskId());
        accountInfo.setPackageIds(Lists.newArrayList(attr.getSignTaskAwardPackageMap().values()));
        publishDelayEvent(attr, DELAY_HANDLE_FILL_QCOIN_INFO, accountInfo, System.currentTimeMillis() + DateUtils.MILLIS_PER_MINUTE);



        actRedisDao.set(redisCode, key, "1");
        log.info("award done,actId:{},uid:{},awardSeq:{},awardSeqMd5:{},signTaskAwardPackageId:{},signDate:{}", attr.getActId(), uid, awardSeq, awardSeqMd5, signTaskAwardPackageId, curDayTask.getSignedDay());
    }

    /**
     * 找到完成任务的最近的发奖配置
     */
    private long findReleasePackageId(KaiHeiSkinComponentAttr attr, int signDay) {
        long targetDay = 0;
        //顺序排列
        List<Long> dayConfig = Lists.newArrayList(attr.getSignTaskAwardPackageMap().keySet()).stream()
                .sorted()
                .toList();
        for (long targetDayConfig : dayConfig) {
            if (signDay >= targetDayConfig) {
                targetDay = targetDayConfig;
            }
        }
        return attr.getSignTaskAwardPackageMap().getOrDefault(targetDay, 0L);
    }

    private long queryUserBalance(KaiHeiSkinComponentAttr attr, long uid) {
        int finalBusinessId = 1;
        Map<String, Long> balance = currencyClient.balance(uid, finalBusinessId, Lists.newArrayList(attr.getSignTaskAwardCId()));
        if (balance == null) {
            log.error("queryUserBalance balance error,uid:{}", uid);
            return -1;
        }
        long amount = balance.getOrDefault(attr.getSignTaskAwardCId(), 0L);
        return amount / 100;
    }


    public void fillInAccountInfo(KaiHeiSkinComponentAttr attr, Object object) {
        if (! (object instanceof KaiHeiQcoinsComponent.FillQCoinAccountInfo info)) {
            log.error("fillInAccountInfo type error,object:{}",JSON.toJSONString(object));
            return;
        }
        log.info("fillInAccountInfo,info:{}", JSON.toJSONString(info));
        kaiHeiQcoinsComponent.fillInAccountInfo(info);
    }

    private boolean isNewUser(KaiHeiSkinComponentAttr attr, long uid) {
        return NewUserStatus.NEW_USER == queryUserStatus(attr, uid);
    }

    /**
     * -1未进入资格名单
     * 1 新用户
     * 0 老用户
     */
    public int queryUserStatus(KaiHeiSkinComponentAttr attr, long uid, String hdid) {
        var actInfo = actInfoService.queryActivityInfo(attr.getActId());
        return getOrSaveUserNewState(actInfo, attr, uid, hdid);
    }

    /**
     * -1未进入资格名单
     * 1 新用户
     * 0 老用户
     */
    public int queryUserStatus(KaiHeiSkinComponentAttr attr, long uid) {
        Integer state = whitelistComponent.getConfigValue(attr.getActId(), attr.getLoginUserWhiteListCmptIndex(), Convert.toString(uid), Integer.class);
        if (state == null) {
            return NewUserStatus.NOT_IN_LIST;
        }

        return Convert.toInt(state, NewUserStatus.NOT_IN_LIST);
    }


    public int queryUserStatus(long actId, long uid) {
        KaiHeiSkinComponentAttr attr = tryGetUniqueComponentAttr(actId);
        Integer state = whitelistComponent.getConfigValue(attr.getActId(), attr.getLoginUserWhiteListCmptIndex(), Convert.toString(uid), Integer.class);
        if (state == null) {
            return NewUserStatus.NOT_IN_LIST;
        }

        return Convert.toInt(state, NewUserStatus.NOT_IN_LIST);
    }

    /**
     * 气泡提醒文案
     */
    public String getNoticeTips(KaiHeiSkinComponentAttr attr, long uid) {
        long actId = attr.getActId();
        Date currentDate = commonService.getNow(actId);

        String tips = null;

        //奖池已消耗完，不弹出气泡
        if(isPoolOut(attr)){
            return tips;
        }

        //任务已截止，不弹出气泡
        long taskEndTime = calSignTaskEndTime(attr,uid);
        if(currentDate.getTime()>taskEndTime){
            return tips;
        }

        CurDayTask curDayTask = dayTaskComponent.queryCurDayTask(actId, attr.getDayTaskCmptIndex(), Convert.toString(uid));
        if (curDayTask != null && StringUtil.isNotBlank(curDayTask.getCompleteDayCode())) {
            Date lastCompleteDay = DateUtil.getDate(curDayTask.getCompleteDayCode(), DateUtil.PATTERN_TYPE2);
            long offsetDay = DateUtil.getDays(lastCompleteDay, currentDate);
            //超过N天未打开
            if (offsetDay > attr.getSignTaskTipsDay()) {
                long left = attr.getLastTaskDayIndex() - curDayTask.getSignedDay();
                if (left > 0) {
                    var skinConfig = kaiHeiSkinPortalComponent.queryUserSkin(actId, uid);
                    tips = attr.getSignTaskTips()
                            .replace("$notSign", Convert.toString(offsetDay))
                            .replace("$leftDay", Convert.toString(left))
                            .replace("$award", skinConfig == null ? "奖励" : skinConfig.getSkinName());
                }
            }
        }
        log.info("getNoticeTips,actId:{},uid:{},tips:{}", actId, uid, tips);
        return tips;
    }


    /**
     * 入口隐藏时间
     */
    public long getUserCloseEntryTime(KaiHeiSkinComponentAttr attr, long uid) {
        String redisCode = getRedisGroupCode(attr.getActId());
        var actInfo = actInfoService.queryActivityInfo(attr.getActId());
        long hindOffset = DateUtil.ONE_DAY_MILL_SECONDS;

        //老用户领取奖励后，次日23.59.59点关闭入口
        String key = makeKey(attr, String.format(OLD_USER_FIRST_LOGIN_TIME, uid));
        String oldUserCloseTime = actRedisDao.get(redisCode, key);
        if (StringUtil.isNotBlank(oldUserCloseTime)) {
            log.info("getUserCloseEntryTime old user,uid:{},oldUserCloseTime:{}", uid, oldUserCloseTime);
            return DateUtil.getDayEndTime(Convert.toLong(oldUserCloseTime)) + hindOffset;
        }

        //发奖成功了，次日23.59.59点关闭入口
        String qCoinSucceedTimeKey = buildQCoinAwardSucceedTimeKey(attr, uid);
        String qCoinSucceedTime = actRedisDao.get(redisCode, qCoinSucceedTimeKey);
        if (StringUtil.isNotBlank(qCoinSucceedTime)) {
            log.info("getUserCloseEntryTime qCoinSucceedTime,uid:{},qCoinSucceedTime:{}", uid, qCoinSucceedTime);
            return DateUtil.getDayEndTime(Convert.toLong(qCoinSucceedTime)) + hindOffset;
        }

        //是否存在未发放完成记录
        List<KaiHeiQcoinsComponent.UserReleaseInfo> records = kaiHeiQcoinsComponent
                .getTaskAwardState(attr.getActId(), uid, (int) attr.getSignTaskAwardTaskId(), Lists.newArrayList(attr.getSignTaskAwardPackageMap().values()));
        boolean existNotSendSuccessRecord = !CollectionUtils.isEmpty(records) && records.stream().anyMatch(p -> p.getStatus() != 1);

        //奖池消耗完，次日23.59.59点关闭入口
        String poolOutTimeKey = buildPoolOutTimeKey(attr);
        String poolOutTime = actRedisDao.get(redisCode, poolOutTimeKey);
        if (!existNotSendSuccessRecord && StringUtil.isNotBlank(poolOutTime)) {
            log.info("getUserCloseEntryTime poolOutTime,uid:{},poolOutTime:{}", uid, poolOutTime);
            return DateUtil.getDayEndTime(Convert.toLong(poolOutTime)) + hindOffset;
        }

        //任务截止
        String signEndKey = buildUserSignTaskEndTimeKey(attr, uid);
        String endTimeStr = actRedisDao.get(getRedisGroupCode(attr.getActId()), signEndKey);
        if (!existNotSendSuccessRecord && StringUtil.isNotBlank(endTimeStr)) {
            log.info("getUserCloseEntryTime time up,uid:{},endTimeStr:{}", uid, endTimeStr);
            return DateUtil.getDayEndTime(Convert.toLong(endTimeStr)) + hindOffset;
        }

        return actInfo.getEndTime();
    }

    /**
     * 是否到了 停止写入最终资格名单时间
     */
    public boolean inAddWhiteListEndTime(KaiHeiSkinComponentAttr attr, ActivityInfoVo actInfo, long uid) {
        long endTime = DateUtil.getDayBeginTime(DateUtil.add(new Date(actInfo.getEndTime()), -(attr.getAddWhiteListEndDay() - 1))).getTime();
        long now = commonService.getNow(attr.getActId()).getTime();
        log.info("inAddWhiteListEndTime uid:{},now:{}, endTime:{}", uid, now, endTime);
        return now > endTime;
    }

    /**
     * 顶部提醒
     */
    public void doSendNotice(String seq, long uid, String title, String message, String link) {
        Map<String, String> extend = Maps.newHashMapWithExpectedSize(2);
        //客户端不用等待首页弹窗完成也能显示
        extend.put("ignoreMainPopup", "1");

        YoPopupMessage yoMessage = YoPopupMessage.builder()
                .app("yomi")
                //默认android,-1 安卓+ios
                .platform(-1)
                .title(title)
                .content(Base64Utils.encodeToString(message.getBytes()))
                .innerContent(message)
                .icon("https://gamebaby.bs2dl.yy.com/adminweb/ezzxpsxnzcfggprmtdtwimcttrac2wft.png")
                .extend(extend)
                .link(link).build();
        zhuiWanPrizeIssueServiceClient.sendPopupMessage(seq, uid, yoMessage);
    }


    @GetMapping("/taskStatic")
    public Response<String> taskStatic(HttpServletRequest request, HttpServletResponse response, Long actId) {
        Date now = commonService.getNow(actId);
        doStaticReport(actId, now, getUniqueComponentAttr(actId));
        return Response.ok();
    }

    /**
     * 如流小时播报
     */
    @NeedRecycle(author = "chengaojie", notRecycle = true)
    @Scheduled(cron = "11 0 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}", actId);
                return;
            }

            KaiHeiSkinComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}", actId);
                continue;
            }

            Date now = commonService.getNow(attr.getActId());
            //使用物理时间过滤是否已发送过如流
            String timeCode = DateUtil.format(new Date(), DateUtil.PATTERN_TYPE7);
            String groupCode = getRedisGroupCode(actId);
            String execKey = makeKey(attr, "execStatic:" + timeCode);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}", execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId, now);

            doStaticReport(actId, now, attr);
        }
    }

    public void doStaticReport(long actId, Date now, KaiHeiSkinComponentAttr attr) {
        StringBuilder content = new StringBuilder();
        content.append("【奖池跟踪】\n");

        DayTaskComponentAttr dayTaskComponentAttr = dayTaskComponent.getComponentAttr(actId, attr.getDayTaskCmptIndex());
        long awardDaySend = dayTaskComponent.getAwardDaySend(dayTaskComponentAttr,now, attr.getAwardPackageId());
        content.append("今日发放总金额（元）：").append(awardDaySend/100).append("\n");

        long  loginDayAmount = getLoginTaskDayAmount(attr,now);
        content.append("今日登录任务发奖总金额（元）：").append(loginDayAmount/100).append("\n");

        long left = dayTaskComponent.queryPoolBalance(dayTaskComponentAttr, attr.getAwardPackageId());
        content.append("总奖池剩余金额（元）：").append(left/100).append("\n");


        content.append("【累计完成情况跟踪】\n");
        String groupCode = getRedisGroupCode(attr.getActId());
        String loginKey = makeKey(attr, LOGIN_TASK_FINISH_STATISTIC);
        String loginValue = actRedisDao.get(groupCode, loginKey);
        long loginCount = Convert.toLong(loginValue, 0);
        content.append("累计完成登录任务人数：").append(loginCount).append("\n");

        String dayTaskKey = makeKey(attr, DAY_TASK_FINISH_STATISTIC);
        Map<Object, Object> task = actRedisDao.hGetAll(groupCode, dayTaskKey);
        for (int dayIndex = 1; dayIndex <= attr.getLastTaskDayIndex(); dayIndex++) {
            long count = Convert.toLong(task.getOrDefault(dayIndex + "", 0));
            content.append("累计完成第").append(dayIndex).append("天任务人数：").append(count).append("\n");
        }

        content.append("【白名单监控】\n");
        long total = whitelistComponent.whitelistTotal(actId, (int) attr.getPreWhiteListCmptIndex());
        content.append("当前白名单总人数：").append(total).append("\n");

        long weekAdd = kaiHeiSkinPortalComponent.getWeekAddWhitelist(actId, now);
        content.append("本周新增的白名单人数：").append(weekAdd).append("\n");

        String msg = buildActRuliuMsg(actId, false, "开黑领皮肤如流播报", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
    }

    private long getLoginTaskDayAmount(KaiHeiSkinComponentAttr attr,Date now) {
        String groupCode = getRedisGroupCode(attr.getActId());

        String ymd = DateUtil.format(now,DateUtil.PATTERN_TYPE2);
        String dayKey = makeKey(attr, String.format(LOGIN_TASK_FINISH_DAY_STATISTIC,ymd));
        String loginValue = actRedisDao.get(groupCode, dayKey);
        long loginCount = Convert.toLong(loginValue, 0);
        return loginCount*attr.getLoginAwardNewUserQCoinValue();

    }

    private void setOldUserFirstLoginTime(KaiHeiSkinComponentAttr attr, long uid, long millSeconds) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr, String.format(OLD_USER_FIRST_LOGIN_TIME, uid));
        actRedisDao.setNX(redisCode, key, Convert.toString(millSeconds));
    }

    private boolean isPoolOut(KaiHeiSkinComponentAttr attr) {
        String key = buildPoolOutTimeKey(attr);
        boolean poolOut = StringUtil.isNotBlank(actRedisDao.get(getRedisGroupCode(attr.getActId()), key));
        var dayTaskAttr = dayTaskComponent.getComponentAttr(attr.getActId(), attr.getDayTaskCmptIndex());
        long balance = dayTaskComponent.queryPoolBalance(dayTaskAttr, attr.getQCoinAwardPoolPackageId());
        return poolOut || balance <= 0;
    }

    private void updateLoginTaskStatic(KaiHeiSkinComponentAttr attr, long uid,Date now) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String key = makeKey(attr, LOGIN_TASK_FINISH_STATISTIC);
        String seq = makeKey(attr,"seq:logintaskStatic:" + uid);
        actRedisDao.incrValueWithSeq(groupCode, seq, key, 1, DateUtil.ONE_WEEK_SECONDS);

        String ymd = DateUtil.format(now,DateUtil.PATTERN_TYPE2);
        String dayKey = makeKey(attr, String.format(LOGIN_TASK_FINISH_DAY_STATISTIC,ymd));
        String daySeq = makeKey(attr,"seq:logintaskDayStatic:" + uid);
        actRedisDao.incrValueWithSeq(groupCode, daySeq, dayKey, 1, DateUtil.ONE_WEEK_SECONDS);

    }


    private String buildUserSignTaskEndTimeKey(KaiHeiSkinComponentAttr attr, long uid) {
        return makeKey(attr, String.format(USER_SIGN_TASK_END_TIME, uid));
    }

    private String buildCompleteAllTaskKey(KaiHeiSkinComponentAttr attr, long uid) {
        return makeKey(attr, String.format(COMPLETE_ALL_TASK_TAG, uid));
    }

    private String buildPoolOutTimeKey(KaiHeiSkinComponentAttr attr) {
        return makeKey(attr, POLL_OUT_TIME);
    }


    private String buildQCoinAwardSucceedTimeKey(KaiHeiSkinComponentAttr attr, long uid) {
        return makeKey(attr, String.format(QCOIN_AWARD_SUCCEED_TIME, uid));
    }

    private String buildLastJumpSignDate(KaiHeiSkinComponentAttr attr, long uid) {
        return makeKey(attr, String.format(LAST_JUMP_SIGN_DATE, uid));
    }

    /**
     * 0-未完成任务; 1-已完成任务，未填写领奖信息; 2-已完成任务，奖励发放中；3-已完成任务，奖励发放成功；4-任务已结束，没Q币发放;或者奖励未到账
     */
    private static class AwardStatus {
        public static int NOT_COMPLETE = 0;
        public static int TO_SUMMIT = 1;
        public static int AWARD_ING = 2;
        public static int AWARDED = 3;
        public static int NOT_AWARDED_OR_NOT_SEND = 4;
    }

}
