package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjHelper;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PhaseRankAwardComponentAttr;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.AwardTaskInfo;
import com.yy.thrift.hdztaward.QueryAwardTaskResult;
import com.yy.thrift.hdztranking.Rank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 按阶段所在名次做奖励的组件
 *
 * <AUTHOR>
 * @date 2021/4/14 17:23
 */
@UseRedisStore(NeedChange = true)
@Component
public class PhaseRankAwardComponent extends BaseActComponent<PhaseRankAwardComponentAttr> {

    @Override
    public Long getComponentId() {
        return ComponentId.PHASE_RANK_AWARD;
    }

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    public HdztRankService hdztRankService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    /**
     * 响应阶段结束事件，按配置发放奖励， 本函数做了防重检查，只能执行一次
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onPhaseTimeEnd(PhaseTimeEnd event, PhaseRankAwardComponentAttr attr) {
        // 先检查是否要处理的
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        //log.info("PhaseRankAwardComponent event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        if (!attr.isMyDuty(rankId, phaseId)) {
            return;
        }

        log.info("onPhaseTimeEnd done@event:{}, attr:{}", event, attr);
        String checkName = "onPhaseTimeEnd:" + event.getEkey();
        String checkValue = event.getTimestamp() + ":" + event.getSeq();
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        if (actRedisDao.setNX(groupCode, makeKey(attr, checkName), checkValue)) {
            doAward(event, attr);
            doMysql(event, attr);
        }
    }


    private void doAward(PhaseTimeEnd event, PhaseRankAwardComponentAttr attr) {
        Clock clock = new Clock();
        try {
            // 1. 先提取所有的排名奖励，若配置为空，则结束返回
            Map<Integer, Map<Long, Map<Long, Integer>>> awardConfig = HdzjHelper.parseRankAwardConfig(attr.getRankAwardConfig());
            if (CollectionUtils.isEmpty(awardConfig)) {
                log.warn("doAward ignore@invalid award config, ekey:{}, seq:{}, attr:{}", event.getEkey(), event.getSeq(), attr);
                return;
            }

            // 2. 取主榜的成员（用于形成贡献榜子key中的 成员 部分）
            String primaryRankMember = getPrimaryRankMember(event, attr);
            if (primaryRankMember == null) {
                log.error("doAward skip@not found primaryRankMember, event:{}, attr:{}", event, attr);
                return;
            }

            // 3. 找出从 1 ~ 最大排名位置 的所有排名清单
            int maxRank = Collections.max(Lists.newArrayList(awardConfig.keySet()));
            Map<Integer, Rank> rankReceivers = hdztRankService.readyRankReceivers(event.getActId(), event.getRankId(),
                    event.getPhaseId(), event.getTimeKey(), event.getEndTime(), maxRank, attr.getRankType(), primaryRankMember);
            clock.tag();
            if (rankReceivers == null) {
                log.error("doAward wrong@fail get rankReceivers, event:{}, attr:{}", event, attr);
                return;
            }

            // 4. 遍历位置奖励配置，为每个位置上的接收者发放奖励
            int retry = attr.getRetry();
            int receiverInx = attr.getReceiverInx();
            Date now = commonService.getNow(attr.getActId());
            String time = DateUtil.format(now);
            Map<Long, Map<Long, Map<Long, Integer>>> record = Maps.newHashMap();
            for (Integer rank : awardConfig.keySet()) {
                Rank bean = rankReceivers.get(rank);
                if (bean != null) {
                    String seq = event.getSeq() + StringUtil.UNDERSCORE + rank;
                    Map<Long, Map<Long, Integer>> taskPackageIds = awardConfig.get(rank);
                    long receiver = Long.parseLong(bean.getMember().split("\\|")[receiverInx]);
                    hdztAwardServiceClient.doBatchWelfare(seq, receiver, taskPackageIds, time, retry, Maps.newHashMap());
                    record.put(receiver, taskPackageIds);
                }
            }
            if (attr.getSendFlowNotice() == 1) {
                threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendInfo2Flow(attr.getActId(), record));
            }

            log.info("doAward done@seq:{}, ekey:{}, attr:{} {}", event.getSeq(), event.getEkey(), attr, clock.tag());
        } catch (Throwable t) {
            log.error("doAward exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }

    private void sendInfo2Flow(long actId, Map<Long, Map<Long, Map<Long, Integer>>> records) {
        try {
            Map<String, Long> userAward = Maps.newHashMap();
            List<Long> taskIds = new ArrayList<>();

            for (Map.Entry<Long, Map<Long, Map<Long, Integer>>> entry : records.entrySet()) {
                for (Map.Entry<Long, Map<Long, Integer>> taskEntry : entry.getValue().entrySet()) {
                    for (Map.Entry<Long, Integer> packageEntry : taskEntry.getValue().entrySet()) {
                        Long taskId = taskEntry.getKey();
                        Long packageId = packageEntry.getKey();
                        taskIds.add(taskId);
                        userAward.put(taskId + "_" + packageId, entry.getKey());
                    }
                }
            }
            QueryAwardTaskResult taskResult = hdztAwardServiceClient.getProxy(false).queryAwardTasks(taskIds, false);
            Map<AwardTaskInfo, List<AwardModelInfo>> infoListMap = taskResult.getTaskAwardModelInfoMap();
            Map<String, String> awardInfo = Maps.newHashMap();
            for (Map.Entry<AwardTaskInfo, List<AwardModelInfo>> listEntry : infoListMap.entrySet()) {
                long taskId = listEntry.getKey().getTaskId();
                for (AwardModelInfo info : listEntry.getValue()) {
                    awardInfo.put(taskId + "_" + info.getPackageId(), info.getPackageName());
                }
            }
            String format = "活动: %s 阶段结算奖励: uid:%s 奖励:%s \n";
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, Long> entry : userAward.entrySet()) {
                String award = awardInfo.get(entry.getKey());
                sb.append(String.format(format, actId, entry.getValue(), award));
            }

            //baiduInfoFlowRobotService.asyncSendNotifyByActAttrKey(actId, "act_notice", sb.toString(), Lists.newArrayList());
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, sb.toString(), Lists.newArrayList());
        } catch (Exception e) {
            log.error("sendInfo2Flow error, ");
        }
    }

    private void doMysql(PhaseTimeEnd event, PhaseRankAwardComponentAttr attr) {
        Clock clock = new Clock();
        long actId = event.getActId();
        try {
            // 1. 准备sql操作配置
            Map<String, Set<Integer>> mysqlOperConfig = HdzjHelper.parseRankMysqlConfig(attr.getRankMysqlConfig());
            if (CollectionUtils.isEmpty(mysqlOperConfig)) {
                return;
            }

            // 2. 找出从 1 ~ 最大排名位置 的所有排名清单
            List<Integer> allRanks = Lists.newArrayList();
            for (String sql : mysqlOperConfig.keySet()) {
                allRanks.addAll(mysqlOperConfig.get(sql));
            }
            int maxRank = Collections.max(allRanks);
            Map<Integer, Rank> rankReceivers = hdztRankService.readyRankReceivers(actId, event.getRankId(),
                    event.getPhaseId(), event.getTimeKey(), event.getEndTime(), maxRank, attr.getRankType());
            clock.tag();
            if (rankReceivers == null) {
                log.error("doMysql wrong@event:{}, attr:{}", event, attr);
                return;
            }

            // 3. 遍历位置mysql配置，为每个位置执行该 sql
            for (String mysql : mysqlOperConfig.keySet()) {
                Set<Integer> set = mysqlOperConfig.get(mysql);
                if (CollectionUtils.isEmpty(set)) {
                    continue;
                }
                for (int rank : set) {
                    Rank bean = rankReceivers.get(rank);
                    if (bean != null) {
                        Map<String, String> replaceMap = HdzjHelper.makeReplaceMap(event, bean);
                        String sql = HdzjHelper.replace(mysql, replaceMap);
                        int ret = this.gameecologyDao.getJdbcTemplate().update(sql);
                        log.info("doMysql one ok@actId:{}, seq:{}, ekey:{}, sql:{}, ret:{}", actId, event.getSeq(), event.getEkey(), sql, ret);
                    }
                }
            }
            log.info("doMysql done@seq:{}, ekey:{}, attr:{} {}", event.getSeq(), event.getEkey(), attr, clock.tag());
        } catch (Throwable t) {
            log.error("doMysql exception@event:{}, attr:{}, err:{} {}", event, attr, t.getMessage(), clock.tag(), t);
        }
    }


    /**
     * 查主榜指定位置的成员， 若返回 null 表明出现某种异常，调用者应该中断处理
     */
    private String getPrimaryRankMember(PhaseTimeEnd event, PhaseRankAwardComponentAttr attr) {
        long primaryRankId = attr.getPrimaryRankId();
        if (primaryRankId <= 0) {
            return "";
        }
        int primaryRankPos = attr.getPrimaryRankPos();
        String primaryRankType = attr.getPrimaryRankType();
        Map<Integer, Rank> primaryRankMembers = hdztRankService.readyRankReceivers(event.getActId(), primaryRankId,
                event.getPhaseId(), event.getTimeKey(), event.getEndTime(), primaryRankPos, primaryRankType);
        if (primaryRankMembers == null || !primaryRankMembers.containsKey(primaryRankPos)) {
            return null;
        }
        return primaryRankMembers.get(primaryRankPos).getMember();
    }
}
