package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.hdzj.element.redis.WhiteListPromoteComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2022-07-08 17:59
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class WhiteListPromoteComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private WhiteListPromoteComponent whiteListPromoteComponent;

    @Test
    public void test() {

        for (int i = 0; i <= 0; i++) {
            PromotTimeEnd promotTimeEnd = new PromotTimeEnd();
            promotTimeEnd.setEndTime("2022-11-23 23:59:59");
            promotTimeEnd.setPhaseId(22);
            promotTimeEnd.setRankId(22);
            promotTimeEnd.setActId(2022101001);
            whiteListPromoteComponent.doSettle(promotTimeEnd, whiteListPromoteComponent.getComponentAttr(2022101001, 502));
        }
    }

    @Test
    public void querySettingTest() {
        for (int i = 0; i <= 10; i++) {
            whiteListPromoteComponent.querySetting(2022091001,500);
        }
    }
}
