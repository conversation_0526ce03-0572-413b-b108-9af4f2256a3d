package com.yy.gameecology.common.db.mapper.gameecology.cmpt;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;

public interface Cmpt5156UserInviteMapper {
    @Update("""
                UPDATE cmpt_5156_user_invite
                SET award_receive_state = #{newAwardReceiveState}
                WHERE id = #{id}
                  AND act_id = #{actId}
                  AND uid = #{uid}
                  AND cmpt_use_inx = #{cmptUseInx}
                  AND award_receive_state = #{currentAwardReceiveState}
            """)
    long updateAwardReceiveState(
            @Param("id") Long id,
            @Param("actId") Long actId,
            @Param("cmptUseInx") Long cmptUseInx,
            @Param("uid") Long uid,
            @Param("currentAwardReceiveState") Integer currentAwardReceiveState,
            @Param("newAwardReceiveState") Integer newAwardReceiveState);

    @Select("""
        SELECT SUM(award_amount)
        FROM cmpt_5156_user_invite
        WHERE act_id = #{actId}
          AND cmpt_use_inx = #{cmptUseInx}
          AND uid = #{uid}
    """)
    Long sumAwardAmountByActIdCmptUseInxAndUid(
            @Param("actId") Long actId,
            @Param("cmptUseInx") Long cmptUseInx,
            @Param("uid") Long uid);

    @Select("""
      <script>
      select count(distinct uid) 
      from 
      cmpt_5156_user_invite
      WHERE act_id = #{actId}
        AND cmpt_use_inx = #{cmptUseInx}
        AND state = #{state}
        <if test="startTime != null">
        AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
        AND create_time &lt;= #{endTime}
         </if>
         </script>
    """)
    Long sumUserAmount(@Param("actId") Long actId,
                       @Param("cmptUseInx") Long cmptUseInx,
                       @Param("startTime") Date startTime,
                       @Param("endTime") Date endTime,
                       @Param("state") Integer state);

    @Select("""
       <script>
      select count(distinct invited_uid) 
      from 
      cmpt_5156_user_invite
      WHERE act_id = #{actId}
        AND cmpt_use_inx = #{cmptUseInx}
        AND state = #{state}
          <if test="startTime != null">
        AND create_time &gt;= #{startTime}
         </if>
          <if test="endTime != null">
        AND create_time &lt;= #{endTime}
         </if>
       </script>
    """)
    Long sumInvitedUserAmount(@Param("actId") Long actId,
                       @Param("cmptUseInx") Long cmptUseInx,
                       @Param("startTime") Date startTime,
                       @Param("endTime") Date endTime,
                       @Param("state") Integer state);


    @Select("""
       <script>
      select sum(award_amount) 
      from 
      cmpt_5156_user_invite
      WHERE act_id = #{actId}
        AND cmpt_use_inx = #{cmptUseInx}
        AND award_receive_state = #{receiveState}
          <if test="startTime != null">
        AND create_time  &gt;=  #{startTime}
         </if>
          <if test="endTime != null">
        AND create_time &lt;= #{endTime}
         </if>
       </script>
    """)
    Long sumAwardAmount(@Param("actId") Long actId,
                              @Param("cmptUseInx") Long cmptUseInx,
                              @Param("startTime") Date startTime,
                              @Param("endTime") Date endTime,
                              @Param("receiveState") Integer receiveState);

}
