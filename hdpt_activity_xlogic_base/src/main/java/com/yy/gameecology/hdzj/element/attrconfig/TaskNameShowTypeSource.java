package com.yy.gameecology.hdzj.element.attrconfig;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/23 17:05
 **/
@Component
public class TaskNameShowTypeSource implements DropDownSource {
    @Override
    public List<DropDownVo> listDropDown() {
        // [{"code":"1","desc":"展示最高级(1)"},{"code":"2","desc":"合并展示(2)"}]
        return Arrays.asList(
                new DropDownVo("1", "展示最高级(1)"),
                new DropDownVo("2", "合并展示(2)")
        );
    }
}
