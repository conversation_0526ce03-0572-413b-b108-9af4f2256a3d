package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerLayout;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpPuzzleComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.*;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@UseRedisStore
@RequestMapping("/cpPuzzle")
@RestController
@Component
public class CpPuzzleComponent extends BaseActComponent<CpPuzzleComponentAttr> {

    private static final String PUZZLE_CP_BALANCE = "puzzle_cp_balance:%s";

    private static final String TASK_LOCK = "puzzle_task_lock";

    private static final String PUZZLE_ROW_AWARD_RECORD = "puzzle_row_award_record:%s";  //1 2 3

    private static final String PUZZLE_COL_AWARD_RECORD = "puzzle_col_award_record:%s"; //1 2 3

    private static final String PUZZLE_ALL_AWARD_RECORD = "puzzle_all_award_record";

    private static final String PUZZLE_CP_DAILY_SCORE = "puzzle_cp_daily_score:%s";

    private static final String PUZZLE_ROW_AWARD_RECORD_CNT = "puzzle_row_award_record_cnt:%s";

    private static final String PUZZLE_COL_AWARD_RECORD_CNT = "puzzle_col_award_record_cnt:%s";

    private static final String PUZZLE_ALL_AWARD_RECORD_CNT = "puzzle_all_award_record_cnt:%s";

    private static final String TASK_LEVEL_LOG = "task_level_%s_%s";

    private static final String PUZZLE_ALL_CNT = "puzzle_all_%s";

    private static final String PUZZLE_DAILY_CNT = "puzzle_daily_%s_%s";

    @Autowired
    private Locker locker;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private SignedService signedService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.CP_PUZZLE;
    }

    @RequestMapping("/mockTest")
    public Response mockTest(HttpServletRequest req, HttpServletResponse resp, long actId, long sid, long ssid) {
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setUserUid(1).setUserNick("1").setUserLogo("1").setBannerId(1).setBannerType(1)
                .setUserScore(1).setJsonData(JSON.toJSONString(new HashMap<>()))
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        BusiId busiId = BusiId.findByValue(810);
        broadCastHelpService.broadcast(actId, busiId, 2, sid, ssid, bannerBroMsg);
        return Response.success("succ");
    }

    @HdzjEventHandler(value = RankingScoreChanged.class,canRetry = true)
    public void onRankScoreChange(RankingScoreChanged event, CpPuzzleComponentAttr attr) {
        if(attr.getCpRankId() == event.getRankId()) {
            log.info("onRankScoreChange start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
            String seq = event.getSeq();
            String date = DateUtil.format(DateUtil.getDate(event.getTimestamp(),
                    DateUtil.DEFAULT_PATTERN), DateUtil.PATTERN_TYPE2);
            actRedisDao.zIncrWithSeq(redisConfigManager.getGroupCode(attr.getActId()),
                    seq, makeKey(attr, String.format(PUZZLE_CP_DAILY_SCORE, date)),
                    event.getMember(), event.getItemScore());
        }
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, CpPuzzleComponentAttr attr) {
        if(attr.getCpTaskRankId() == event.getRankId() && attr.getCpTaskPhaseId() == event.getPhaseId()) {
            log.info("processTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
            String eKey = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
            eKey = MD5SHAUtil.getMD5(eKey);
            long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
            if (startIndex == currIndex) {
                return;
            }
            String member = event.getMember();
            String[] members = member.split("\\|");
            long userId = Convert.toLong(members[0]);
            long anchorUid = Convert.toLong(members[1]);

            String sidSsidStr = event.getActors().get(attr.getTingActor());
            String[] sidSsidArray = sidSsidStr.split("_");
            long sid = Convert.toLong(sidSsidArray[0]);
            long ssid = Convert.toLong(sidSsidArray[1]);

            Map<Object, Object> puzzleMap = actRedisDao.hGetAll(
                    redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(PUZZLE_CP_BALANCE, member)));
            Map<String, Long> puzzleBalaceMap = Maps.newHashMap();
            if (puzzleMap != null) {
                puzzleMap.forEach((k, v) -> puzzleBalaceMap.put(Convert.toString(k), Convert.toLong(v)));
            }
            //是否集齐
            if (puzzleBalaceMap.keySet().size() == attr.getCidRowColMap().keySet().size()) {
                log.info("all puzzle has already collected cp:{}, puzzleBalance:{}", members, JsonUtil.toJson(puzzleMap));
                return;
            }

            Secret secret = null;
            String lockName = makeKey(attr, TASK_LOCK);
            try {
                secret = locker.lock(lockName, 5, eKey, 10);
                if (secret == null) {
                    throw new SuperException("网络超时，请重试", SuperException.E_DATA_ERROR);
                }
                for (long i = startIndex + 1; i <= currIndex; i++) {
                    String seq = eKey +"_" + i;
                    puzzleMap = actRedisDao.hGetAll(
                            redisConfigManager.getGroupCode(attr.getActId()),
                            makeKey(attr, String.format(PUZZLE_CP_BALANCE, member)));
                    Map<String, Long> puzzleBalaceMapT = Maps.newHashMap();
                    if (puzzleMap != null) {
                        puzzleMap.forEach((k, v) -> puzzleBalaceMapT.put(Convert.toString(k), Convert.toLong(v)));
                    }
                    //是否集齐
                    if (puzzleBalaceMapT.keySet().size() == attr.getCidRowColMap().keySet().size()) {
                        continue;
                    }

                    Date now = commonService.getNow(attr.getActId());
                    String date = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
                    actRedisDao.incrValue(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, String.format(TASK_LEVEL_LOG, i, date)), 1);
                    int[][] board = new int[attr.getRowAwardMap().size()][attr.getColAwardMap().size()];
                    if (commonService.getNow(attr.getActId()).getTime() > attr.getJackPointTimestamp()
                            && i >= attr.getJackPointTaskIndex()) {
                        //发万能碎片
                        String sendCid = null;
                        for (String cid : attr.getCidRowColMap().keySet()) {
                            if(puzzleBalaceMapT.containsKey(cid)) {
                                int r = attr.getCidRowColMap().get(cid).getRow();
                                int c = attr.getCidRowColMap().get(cid).getCol();
                                board[r-1][c-1] = 1;
                            }
                            if (puzzleBalaceMapT.containsKey(cid)) {
                                continue;
                            }
                            sendCid = cid;
                        }
                        //寻找优先能连线的拼图
                        int[] winMove = findWinningMove(board);
                        if(winMove != null) {
                            int row = winMove[0] + 1;
                            int col = winMove[1] + 1;
                            for (String c : attr.getCidRowColMap().keySet()) {
                                if(row == attr.getCidRowColMap().get(c).getRow() && col == attr.getCidRowColMap().get(c).getCol()){
                                    sendCid = c;
                                    break;
                                }
                            }
                        }
                        handleTaskPuzzle(attr, puzzleBalaceMapT, sendCid, member, userId, anchorUid, seq, sid, ssid, i);
                    } else {
                        handleTaskPuzzle(attr, puzzleBalaceMapT, null, member, userId, anchorUid, seq, sid, ssid, i);
                    }
                }
                locker.unlock(lockName, secret);
            } catch (Exception e) {
                log.error("onTaskProgressChanged error:{}", e.getMessage(), e);
                throw e;
            } finally {
                if (secret != null) {
                    locker.unlock(lockName, secret);
                }
            }

        }
    }

    private static int[] findWinningMove(int[][] board) {
        int r = board.length;
        int c = board[0].length;
        // 检查每一行
        for (int i = 0; i < r; i++) {
            int[] row = board[i];
            int[] result = checkLine(row, c);
            if (result != null) {
                return new int[]{i, result[1]};
            }
        }

        // 检查每一列
        for (int j = 0; j < c; j++) {
            int[] column = new int[r];
            for (int i = 0; i < r; i++) {
                column[i] = board[i][j];
            }
            int[] result = checkLine(column, r);
            if (result != null) {
                return new int[]{result[0], j};
            }
        }
        return null;
    }

    private static int[] checkLine(int[] line, int size) {
        for (int i = 0; i < size - 1; i++) {
            int countPlayer = 0;
            int emptyIndex = -1;
            for (int j = 0; j < size; j++) {
                if (line[j] == 1) {
                    countPlayer++;
                }
                if (line[j] == 0) {
                    emptyIndex = j;
                }
            }
            if (countPlayer == size - 1 && emptyIndex != -1) {
                return new int[]{emptyIndex, emptyIndex};
            }
        }
        return null;
    }



    public void handleTaskPuzzle(CpPuzzleComponentAttr attr,
                                 Map<String, Long> puzzleBalaceMap, String sendCid,
                                 String cp, long uid, long anchorUid, String seq, long sid, long ssid, long level) {

        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String currency = "";
        if(StringUtil.isEmpty(sendCid)) {
            //发随机碎片
            BatchLotteryResult result = hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(),
                    BusiId.GAME_ECOLOGY.getValue(), anchorUid, attr.getLotteryTaskId(), 1, 0, seq);
            log.info("uid:{} lottery result:{}", anchorUid, JsonUtil.toJson(result));
            String pic = "";
            //记录中奖记录
            if (result.getCode() == 0) {
                long recordId = 0L;
                for (Long value : result.getRecordPackages().values()) {
                    recordId = value;
                }
                currency = attr.getLotteryPuzzleMap().get(recordId);
                String subText = puzzleBalaceMap.containsKey(currency) ? "(重复获得，只能点亮一次！)" : null;
                //随机
                broUser(attr.getActId(), anchorUid, "【恭喜获得随机拼图碎片】", subText, attr.getCidRowColMap().get(currency).getPic(), cp);
                broUser(attr.getActId(), uid, "【恭喜获得随机拼图碎片】", subText, attr.getCidRowColMap().get(currency).getPic(), cp);
            }
        } else {
            currency = sendCid;
            //万能˚
            String text ="【恭喜获得万能拼图，将优先点亮空缺的拼图哦~】";
            String pic=  attr.getJackPointPic();
            String subText = null;
            if(level < attr.getJackPointShowTaskIndex()) {
                text =  "【恭喜获得随机拼图碎片】";
                pic = attr.getCidRowColMap().get(currency).getPic();
                subText = puzzleBalaceMap.containsKey(currency) ? "(重复获得，只能点亮一次！)" : null;
                log.info("cp:{}, get jackpoint but no show", cp);
            }
            broUser(attr.getActId(), anchorUid, text, subText, pic, cp);
            broUser(attr.getActId(), uid, text, subText, pic, cp);
        }
        actRedisDao.hIncrByKey(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_CP_BALANCE, cp)), currency, 1L);

        Date now = commonService.getNow(attr.getActId());
        String date = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        actRedisDao.incrValue(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_DAILY_CNT, date, currency)), 1L);
        actRedisDao.incrValue(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_ALL_CNT, currency)), 1L);

        puzzleBalaceMap.put(currency, puzzleBalaceMap.getOrDefault(currency, 0L) + 1);
        CpPuzzleComponentAttr.RowCol rowCol = attr.getCidRowColMap().get(currency);
        int row = rowCol.getRow();
        int col = rowCol.getCol();
        List<String> rowCids = attr.getRowPuzzleCids().get(row);
        List<String> colCids = attr.getColPuzzleCids().get(col);
        int rowCount = 0;
        for (String rowCid : rowCids) {
            if(puzzleBalaceMap.containsKey(rowCid)) {
                rowCount++;
            }
        }
        int colCount = 0;
        for (String colCid : colCids) {
            if(puzzleBalaceMap.containsKey(colCid)) {
                colCount++;
            }
        }
        if(rowCount == rowCids.size()) {
            boolean hset = actRedisDao.hsetnx(groupCode, makeKey(attr, String.format(PUZZLE_ROW_AWARD_RECORD, cp)),
                    row+"", "1");
            if(hset) {
                CpPuzzleComponentAttr.Award award = attr.getRowAwardMap().get(row);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        uid, award.getTaskId(), 1, award.getPackageId(), seq + "_r_" + uid, null);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        anchorUid, award.getTaskId(), 1, award.getPackageId(), seq + "_r_" + anchorUid, null);
                actRedisDao.incrValue(groupCode, makeKey(attr, String.format(PUZZLE_ROW_AWARD_RECORD_CNT, award.getPackageId())), 1);
                //broUser 三连线
                broUser(attr.getActId(), anchorUid, "【恭喜解锁拼图3连线，获得"+ award.getName()+"奖励】", null, award.getPic(), cp);
                broUser(attr.getActId(), uid, "【恭喜解锁拼图3连线，获得"+ award.getName()+"奖励】", null, award.getPic(), cp);
            }
        }
        if(colCount == colCids.size()) {
            boolean hset = actRedisDao.hsetnx(groupCode, makeKey(attr, String.format(PUZZLE_COL_AWARD_RECORD, cp)),
                    col+"", "1");
            if(hset) {
                CpPuzzleComponentAttr.Award award = attr.getColAwardMap().get(col);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        uid, award.getTaskId(), 1, award.getPackageId(), seq + "_c_" + uid, null);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        anchorUid, award.getTaskId(), 1, award.getPackageId(), seq + "_c_" + anchorUid, null);
                actRedisDao.incrValue(groupCode, makeKey(attr, String.format(PUZZLE_COL_AWARD_RECORD_CNT, award.getPackageId())), 1);
                //broUser 三连线
                broUser(attr.getActId(), anchorUid, "【恭喜解锁拼图3连线，获得"+ award.getName()+"奖励】", null, award.getPic(), cp);
                broUser(attr.getActId(), uid, "【恭喜解锁拼图3连线，获得"+ award.getName()+"奖励】", null, award.getPic(), cp);
          }
        }
        if(puzzleBalaceMap.size() == attr.getCidRowColMap().size()) {
            boolean hset = actRedisDao.hsetnx(groupCode, makeKey(attr, PUZZLE_ALL_AWARD_RECORD),
                    cp, "1");
            if(hset) {
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        uid, attr.getJackPointTaskId(), 1, attr.getJackPointPackageId(), seq + "_a_" + uid, null);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        anchorUid, attr.getJackPointTaskId(), 1, attr.getJackPointPackageId(), seq + "_a_" + anchorUid, null);
                List<Long> uids = Lists.newArrayList(uid, anchorUid);
                Map<Long, WebdbUserInfo> userInfos = webdbUinfoClient.batchGetUserInfo(uids);
                WebdbUserInfo anchorUserInfo = userInfos.get(anchorUid);
                WebdbUserInfo userInfo = userInfos.get(uid);
                actRedisDao.incrValue(groupCode, makeKey(attr, String.format(PUZZLE_ALL_AWARD_RECORD_CNT, attr.getJackPointPackageId())), 1);
                //全服告白
                broBanner(attr, userInfo, anchorUserInfo, seq, sid, ssid, cp);
                //broUser 解锁全部拼图
                broUser(attr.getActId(), anchorUid, "【恭喜解锁全部拼图，获得"+ attr.getJackPointAwardName() + "奖励】", null, attr.getJackPointAwardPic(), cp);
                broUser(attr.getActId(), uid, "【恭喜解锁全部拼图，获得"+ attr.getJackPointAwardName() + "奖励】", null, attr.getJackPointAwardPic(), cp);

            }
        }
        long currencyTaskId = rowCol.getTaskId();
        long currencyPackageId = rowCol.getPackageId();
        hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                anchorUid, currencyTaskId, 1, currencyPackageId, seq + "_cur_" + anchorUid, null);
    }

    @RequestMapping("/getCpList")
    public Response getCpList(HttpServletRequest request, HttpServletResponse response, long actId, long cmptId) {
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            return Response.fail(400, "未登陆");
        }
        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }
        List<CpMember> cpMembers = new ArrayList<>();
        CpPuzzleComponentAttr attr = getComponentAttr(actId, cmptId);
        Map<String, QueryRankingRequest> reqMap = Maps.newHashMap();
        Map<String, QueryRankingRequest> antiReqMap = Maps.newHashMap();
        QueryRankingRequest contributeReq = new QueryRankingRequest();
        contributeReq.setActId(actId);
        contributeReq.setRankingId(attr.getCpContributeRankId());
        contributeReq.setPhaseId(attr.getPhaseId());
        contributeReq.setFindSrcMember(Convert.toString(uid));
        contributeReq.setRankingCount(attr.getRankLimit());
        reqMap.put(Convert.toString(uid), contributeReq);
        List<Long> uids = new ArrayList<>();
        Map<String, BatchRankingItem> conTop = hdztRankingThriftClient.queryBatchRanking(reqMap, null);
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(Convert.toLong(rank.getMember()));
            }
        }
        uids.add(uid);
        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        List<String> memberIds = new ArrayList<>();
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
        WebdbUserInfo loginUserInfo =  batched.getUserInfoMap().get(Convert.toString(uid));
        MemInfo loginMemInfo = new MemInfo();
        loginMemInfo.setUid(uid);
        loginMemInfo.setName(loginUserInfo.getNick());
        loginMemInfo.setAvatar(WebdbUtils.getLogo(loginUserInfo));
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                CpMember cpMember = new CpMember();
                long toUid = Convert.toLong(rank.getMember());
                WebdbUserInfo toUserInfo = batched.getUserInfoMap().get(Convert.toString(toUid));
                String cpMemberStr = toUid + "|" + uid;
                MemInfo toMemInfo = new MemInfo();
                toMemInfo.setUid(toUid);
                toMemInfo.setName(toUserInfo.getNick());
                toMemInfo.setAvatar(WebdbUtils.getLogo(toUserInfo));
                cpMember.setCpMember(cpMemberStr);
                cpMember.setAnchor(loginMemInfo);
                cpMember.setUser(toMemInfo);
                cpMembers.add(cpMember);
                memberIds.add(cpMemberStr);
            }
        }

        uids = new ArrayList<>();
        QueryRankingRequest antiContributeReq = new QueryRankingRequest();
        antiContributeReq.setActId(actId);
        antiContributeReq.setRankingId(attr.getCpAntiContributeRankId());
        antiContributeReq.setPhaseId(attr.getPhaseId());
        antiContributeReq.setFindSrcMember(Convert.toString(uid));
        antiContributeReq.setRankingCount(attr.getRankLimit());
        antiReqMap.put(Convert.toString(uid), antiContributeReq);
        Map<String, BatchRankingItem> conAntiTop = hdztRankingThriftClient.queryBatchRanking(antiReqMap, null);
        for (BatchRankingItem value : conAntiTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(Convert.toLong(rank.getMember()));
            }
        }
       batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
        for (BatchRankingItem value : conAntiTop.values()) {
            for (Rank rank : value.getData()) {
                CpMember cpMember = new CpMember();
                long toUid = Convert.toLong(rank.getMember());
                WebdbUserInfo toUserInfo = batched.getUserInfoMap().get(Convert.toString(toUid));
                String cpMemberStr = uid + "|" + toUid;
                MemInfo toMemInfo = new MemInfo();
                toMemInfo.setUid(toUid);
                toMemInfo.setName(toUserInfo.getNick());
                toMemInfo.setAvatar(WebdbUtils.getLogo(toUserInfo));
                cpMember.setCpMember(cpMemberStr);
                cpMember.setAnchor(toMemInfo);
                cpMember.setUser(loginMemInfo);
                cpMembers.add(cpMember);
                memberIds.add(cpMemberStr);
            }
        }
        for (String memberId : memberIds) {
            ActorQueryItem queryItem = new ActorQueryItem();
            queryItem.setActorId(memberId);
            queryItem.setRankingId(attr.getCpRankId());
            queryItem.setPhaseId(attr.getPhaseId());
            queryItem.setWithStatus(false);
            queryCpScorePara.add(queryItem);
        }
        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
        Map<String, Long> scoreMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(actorInfoItems)) {
            for (ActorInfoItem actorInfoItem : actorInfoItems) {
                scoreMap.put(actorInfoItem.getActorId(), actorInfoItem.getScore());
            }
        }
        uids = new ArrayList<>();
        for (CpMember cpMember : cpMembers) {
            String cp = cpMember.getCpMember();
            Date now = commonService.getNow(attr.getActId());
            String date = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            long score = Convert.toLong(actRedisDao.zscore(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(PUZZLE_CP_DAILY_SCORE, date)),
                    cp));
            cpMember.setScore(score);
            uids.add(cpMember.getUser().getUid());
            uids.add(cpMember.getAnchor().getUid());
        }
        cpMembers.sort(new Comparator<CpMember>() {
            @Override
            public int compare(CpMember o1, CpMember o2) {
                return (int)(o2.getScore()-o1.getScore());
            }
        });
        uids.add(uid);
        batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
        CpListRsp rsp = new CpListRsp();
        rsp.setCpMembers(cpMembers);
        NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
        rsp.setNickExtUsers(nickExt.getUsers());
        rsp.setUid(uid);
        rsp.setAvatar(WebdbUtils.getLogo(batched.getUserInfoMap().get(uid+"")));
        rsp.setSign(signedService.getSignedSidByBusiId(uid, attr.getBusiId()) > 0);
        return Response.success(rsp);
    }

    @RequestMapping("/getCpMissions")
    public Response getCpMissions(HttpServletRequest request, HttpServletResponse response, String cpMember, long actId, long cmptId) {
        long uid = getLoginYYUid(request, response);
        CpPuzzleComponentAttr attr = getComponentAttr(actId, cmptId);
        CpMission cpMission = new CpMission();
        UserBaseInfo loginUserInfo = null;
        if(StringUtil.isEmpty(cpMember) && uid > 0) {
            cpMember = "1001|"+uid;
            List<Long> uids = new ArrayList<>();
            uids.add(Convert.toLong(uid));
            Map<Long, UserBaseInfo> userMap = commonService.batchGetUserInfos(uids, false);
            loginUserInfo = userMap.get(uid);
        }
        Date now = commonService.getNow(attr.getActId());
        String date = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        cpMember = cpMember == null ? "" : cpMember;
        long score = Convert.toLong(actRedisDao.zscore(redisConfigManager.getGroupCode(attr.getActId()),
               makeKey(attr, String.format(PUZZLE_CP_DAILY_SCORE, date)),
                cpMember));
        cpMission.setScore(score);
        List<Mission> missions = new ArrayList<>();
        int i = 0;
        for (Long missionVal : attr.getMissions()) {
            Mission mission = new Mission();
            mission.setTaskId(++i);
            mission.setScore(missionVal);
            mission.setFinish(missionVal <= cpMission.getScore());
            missions.add(mission);
        }
        cpMission.setMissions(missions);

        cpMission.setRowAwards(attr.getRowAwardMap());
        cpMission.setColAwards(attr.getColAwardMap());
        Map<Object, Object> puzzleMap = actRedisDao.hGetAll(
                redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_CP_BALANCE, cpMember)));
        Map<String, Long> puzzleBalaceMap = Maps.newHashMap();
        if (puzzleMap != null) {
            puzzleMap.forEach((k, v) -> puzzleBalaceMap.put(Convert.toString(k), Convert.toLong(v)));
        }
        List<CpPuzzleComponentAttr.RowCol> rowCols = new ArrayList<>();
        for (String s : puzzleBalaceMap.keySet()) {
            rowCols.add(attr.getCidRowColMap().get(s));
        }
        cpMission.setRowColLight(rowCols);

        if(loginUserInfo != null) {
            cpMission.setAvatar(loginUserInfo.getHdLogo());
            cpMission.setNick(loginUserInfo.getNick());
        }
        return Response.success(cpMission);
    }

    private void broUser(long actId, long uid, String text, String subText, String img, String cp) {
        JSONObject json = new JSONObject(2);
        json.put("text", text);
        json.put("subText", subText);
        json.put("img", img);
        json.put("cpMember", cp);

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType("cp_puzzle")
                .setExtJson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.unicastUid(uid, msg);
        log.info("broUser success with uid:{}, ", uid);
    }

    private void broBanner(CpPuzzleComponentAttr attr, WebdbUserInfo userInfo,
                           WebdbUserInfo anchorUserInfo, String seq, long sid, long ssid, String cpMember) {
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        String logo = WebdbUtils.getLogo(userInfo);

        String anchorNick = anchorUserInfo.getNick();
        String anchorLogo = WebdbUtils.getLogo(anchorUserInfo);

        long bannerId = attr.getBannerId();
        long bannerType = attr.getBannerType();
        Map<String, Object> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("userNick", org.apache.commons.codec.binary.Base64.encodeBase64String(nick.getBytes()));
        extMap.put("babyNick", org.apache.commons.codec.binary.Base64.encodeBase64String(anchorNick.getBytes()));
        extMap.put("userLogo", logo);
        extMap.put("babyLogo", anchorLogo);
        extMap.put("sid", sid);
        extMap.put("ssid", ssid);
        extMap.put("cpMember", cpMember);

        long uid = Convert.toLong(userInfo.getUid());
        long anchorUid = Convert.toLong(anchorUserInfo.getUid());

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setUserUid(uid).setUserNick(nick).setUserLogo(logo)
                .setBannerId(bannerId).setBannerType(bannerType)
                .setUserScore(0).setJsonData(JSON.toJSONString(extMap))
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        BusiId busiId = BusiId.findByValue((int) attr.getBusiId());
        broadCastHelpService.broadcast(attr.getActId(), busiId, attr.getBannerBroType(), 0, 0, bannerBroMsg);
        if (attr.getBannerSvag() != null && attr.getBannerSvag().size() > 0) {
            List<BannerSvagConfig> list = new ArrayList<>(attr.getBannerSvag().values());
            BannerSvagConfig svagConfig = list.get(0);
            AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();
            UserBaseInfo userInfoT = new UserBaseInfo();
            userInfoT.setUid(Convert.toLong(userInfo.getUid()));
            userInfoT.setNick(userInfo.getNick());
            UserBaseInfo anchorUserInfoT = new UserBaseInfo();
            anchorUserInfoT.setUid(Convert.toLong(anchorUserInfo.getUid()));
            anchorUserInfoT.setNick(anchorUserInfo.getNick());
            //svga内嵌文字
            Set<Long> uids = new HashSet<>();
            uids.add(anchorUid);
            uids.add(uid);
            List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, svagConfig, userInfoT, anchorUserInfoT);
            broSvgaConfig.setContentLayers(broContentLayers);
            //svga内嵌图片
            MemberInfo memberInfo = new MemberInfo();
            memberInfo.setHdLogo(logo);
            MemberInfo anchorMemberInfo = new MemberInfo();
            anchorMemberInfo.setHdLogo(anchorLogo);
            List<Map<String, String>> broImgLayers = getSvgaImageConfig(attr, memberInfo, anchorMemberInfo);
            broSvgaConfig.setImgLayers(broImgLayers);

            broSvgaConfig.setLoops(attr.getLoops());

            AppBannerLayout layout = new AppBannerLayout();
            layout.setType(attr.getLayoutType());
            if (StringUtil.isNotBlank(attr.getLayoutMargin())) {
                layout.setMargin(JSON.parseObject(attr.getLayoutMargin(), new TypeReference<Map<String, List<Integer>>>() {
                }));
            }
            broSvgaConfig.setLayout(layout);
            broSvgaConfig.setWhRatio(attr.getWhRatio());
            broSvgaConfig.setClickLayerName(svagConfig.getClickLayerName());
            broSvgaConfig.setSvgaURL(svagConfig.getSvgaURL());
            broSvgaConfig.setJumpSvgaURL(svagConfig.getJumpSvgaURL());
            broSvgaConfig.setMiniURL(svagConfig.getMiniURL());
            broSvgaConfig.setJumpMiniURL(svagConfig.getJumpMiniURL());

            AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getBroBusiId(),
                    3, 0, 0, "",
                    Lists.newArrayList());
            appBannerEvent.setUid(uid);
            appBannerEvent.setUidList(List.copyOf(uids));
            appBannerEvent.setContentType(6);
            appBannerEvent.setAppId(getTurnoverAppId((int) attr.getBusiId()));
            appBannerEvent.setSvgaConfig(broSvgaConfig);
            kafkaService.sendAppBannerKafka(appBannerEvent);
            log.info("app bro done seq:{}, member:{} event:{}", seq, uid, JSON.toJSONString(appBannerEvent));
        }
    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(CpPuzzleComponentAttr attr, BannerSvagConfig svagConfig,
                                                                   UserBaseInfo userInfo, UserBaseInfo anchorUserInfo) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        String contentLayerCodes = svagConfig.getContentLayerCodes();
        if (StringUtil.isNotBlank(contentLayerCodes)) {
            String[] contentLayerCodeArr = contentLayerCodes.split(",");
            for (String contentLayerCode : contentLayerCodeArr) {
                Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
                BannerSvgaTextConfig textConfig = attr.getSvgaText().get(contentLayerCode);
                if (textConfig == null) {
                    continue;
                }
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
                //配置动态替换文本
                String text = contextReplace(textConfig.getText(), userInfo, anchorUserInfo);
                if (attr.getTextDynamicValue() != null) {
                    Map<String, String> replaceValue = attr.getTextDynamicValue();
                    for (String key : replaceValue.keySet()) {
                        text = text.replace(key, replaceValue.get(key));
                    }
                }
                appBannerSvgaText.setText(text);
                appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
                appBannerSvgaText.setGravity(textConfig.getGravity());
                if (StringUtil.isNotBlank(textConfig.getImages())) {
                    appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
                }
                if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                    appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
                }
                broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

                if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                    broContentLayers.add(broSvgaTextLayer);
                }
            }
        }
        return broContentLayers;
    }

    private List<Map<String, String>> getSvgaImageConfig(CpPuzzleComponentAttr attr, MemberInfo memberInfo, MemberInfo anchorMemberInfo) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }
        Map<String, String> imageMap = attr.getSvgaImgLayers();
        for (String imageKey : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String image = imageMap.get(imageKey);
            String value = replaceImage(image, memberInfo, anchorMemberInfo);
            broImgLayer.put(imageKey, value);
            broImgLayers.add(broImgLayer);
        }
        return broImgLayers;
    }

    private String replaceImage(String context, MemberInfo memberInfo, MemberInfo anchorMemberInfo) {
        return context.replace("{header}", Convert.toString(memberInfo.getHdLogo()))
                .replace("{anchorHeader}", Convert.toString(anchorMemberInfo.getHdLogo()));
    }

    private String contextReplace(String context, UserBaseInfo userInfo, UserBaseInfo anchorUserInfo) {
        context = context.replace("{nick}", String.format("{%s:n}", userInfo.getUid()));
        context = context.replace("{anchorNick}", String.format("{%s:n}", anchorUserInfo.getUid()));
        return context;
    }

    private int getTurnoverAppId(int busiId) {
        BusiId value = BusiId.findByValue(busiId);
        int appId = 0;
        switch (value) {
            case GAME_BABY:
                appId = 36;
                break;
            case MAKE_FRIEND:
                appId = 2;
                break;
            case SKILL_CARD:
                appId = 34;
                break;
            default:break;
        }
        return appId;
    }

    @NeedRecycle(author = "guanqhua", notRecycle = true)
    @Scheduled(cron = "0 0 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }
            CpPuzzleComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            Date now = commonService.getNow(attr.getActId());
            Date noticeDate = DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30));

            String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE9);
            String groupCode = getRedisGroupCode(actId);
            String execKey = makeKey(attr, "execCpPuzzleStatic:"+timeCode);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}",execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId,now);

            doStaticReport(attr);

        }
    }

    public void doStaticReport(CpPuzzleComponentAttr attr) {
        StringBuilder content = new StringBuilder();

        content.append("### 奖品累计发放数量\n");
        for (Integer row : attr.getRowAwardMap().keySet()) {
            long packageId = attr.getRowAwardMap().get(row).getPackageId();
            String key = String.format(PUZZLE_ROW_AWARD_RECORD_CNT, packageId);
            long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, key)));
            content.append("行row:").append(attr.getRowAwardMap().get(row).getName()).append(" 个数 = ").append(value).append("\n");
        }
        for (Integer col : attr.getColAwardMap().keySet()) {
            long packageId = attr.getColAwardMap().get(col).getPackageId();
            String key = String.format(PUZZLE_COL_AWARD_RECORD_CNT, packageId);
            long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, key)));
            content.append("列col:").append(attr.getRowAwardMap().get(col).getName()).append(" 个数 = ").append(value).append("\n");
        }
        long cnt = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_ALL_AWARD_RECORD_CNT, attr.getJackPointPackageId()))));
        content.append("大奖:").append(attr.getJackPointAwardName()).append(" 个数 = ").append(cnt).append("\n");
        content.append("### 每日各等级任务达成CP数\n");
        List<Long> missions = attr.getMissions();
        int len = missions.size();
        Date now = commonService.getNow(attr.getActId());
        String date = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        for(int i=1; i<=len; i++) {
            long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(TASK_LEVEL_LOG, i, date))));
            content.append(date).append("等级:").append(i).append(" 任务达成CP数:").append(value).append("\n");
        }
        content.append("### 每日碎片数\n");
        for (Integer row : attr.getRowPuzzleCids().keySet()) {
            for (String s : attr.getRowPuzzleCids().get(row)) {
                long val = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, String.format(PUZZLE_DAILY_CNT, date, s))));
                content.append(date).append("碎片:").append(s).append(" 数:").append(val).append("\n");
            }
        }
        content.append("### 累计碎片数\n");
        for (Integer row : attr.getRowPuzzleCids().keySet()) {
            for (String s : attr.getRowPuzzleCids().get(row)) {
                long val = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, String.format(PUZZLE_ALL_CNT, s))));
                content.append(date).append("碎片:").append(s).append(" 数:").append(val).append("\n");
            }
        }

        String msg = buildActRuliuMsg(attr.getActId(), false, "夏日婚礼玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
    }

    @Data
    private static class CpListRsp {
        List<CpMember> cpMembers;

        Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private long uid;

        private String avatar;

        private boolean sign;
    }

    @Data
    private static class CpMember {
        private String cpMember;

        private MemInfo anchor;

        private MemInfo user;

        private long score;
    }

    @Data
    private static class MemInfo {
        private long uid;

        private String name;

        private String avatar;
    }

    @Data
    private static class CpMission {
        private List<Mission> missions;

        private List<CpPuzzleComponentAttr.RowCol> rowColLight;

        private Map<Integer, CpPuzzleComponentAttr.Award> rowAwards;

        private Map<Integer, CpPuzzleComponentAttr.Award> colAwards;

        private long score;

        private String nick;

        private String avatar;
    }

    @Data
    private static class Mission {
        private long taskId;

        private boolean finish;

        private long score;
    }

}
