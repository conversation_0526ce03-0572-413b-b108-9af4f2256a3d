package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SkinSelectComponentAttr;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-12 19:50
 **/
@Component
@RestController
@RequestMapping("/5158")
public class SkinSelectComponent extends BaseActComponent<SkinSelectComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommonDataDao commonDataDao;

    @Override
    public Long getComponentId() {
        return ComponentId.SKIN_SELECT;
    }

    private final static String USER_SELECT_SKIN = "user_select_skin";


    /**
     * 活动页面查询用户状态：皮肤领取状态、是否登录app、是否新用户
     *
     * @param actId
     * @param cmptIndex
     * @return
     */
    @GetMapping("skinStatus")
    public Response<SkinInfo> queryUserSkinInfo(@RequestParam("actId") int actId,
                                                @RequestParam("cmptIndex") long cmptIndex) {
        SkinInfo data = new SkinInfo();

        SkinSelectComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }
        long uid = getLoginYYUid();
        //未登录（默认值）
        data.setStatus(-1);
        if (uid > 0) {
            //未选皮肤
            data.setStatus(0);
            String selectSkin = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), USER_SELECT_SKIN, Convert.toString(uid));
            if (StringUtils.isNotBlank(selectSkin)) {
                int skinId = Convert.toInt(selectSkin);
                SkinSelectComponentAttr.SkinConfig skin = attr.getSkinConfigs().stream()
                        .filter(skinConfig -> skinConfig.getSkinId() == skinId)
                        .findFirst()
                        .orElse(null);

                data.setSkin(skin);
                //已选皮肤
                data.setStatus(1);
            }

        }

        data.setSkinList(attr.getSkinConfigs());


        return Response.success(data);
    }


    @GetMapping("skinPick")
    public Response<?> pickSkin(@RequestParam("actId") int actId,
                                @RequestParam("cmptIndex") long cmptIndex,
                                @RequestParam("skinId") int skinId,
                                @RequestHeader(name = "x-fts-host-name", required = false) String app,
                                @RequestHeader(name = "YYHeader-y0", required = false) String hdid,
                                @RequestHeader(value = "YYHeader-Platform", required = false, defaultValue = "3") int clientType) {
        SkinSelectComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }
        long uid = getLoginYYUid();
        if (uid < 0) {
            return Response.fail(401, "未登录");
        }

        SkinSelectComponentAttr.SkinConfig skin = attr.getSkinConfigs().stream()
                .filter(skinConfig -> skinConfig.getSkinId() == skinId)
                .findFirst()
                .orElse(null);

        if (skin == null) {
            return Response.fail(400, "皮肤不存在");
        }
        boolean setRet = commonDataDao
                .hashValueSetNX(actId, attr.getCmptId(), attr.getCmptUseInx(), USER_SELECT_SKIN, Convert.toString(uid), Convert.toString(skinId));
        log.info("pickSkin done,uid:{},skinId:{},setRet:{}", uid, skinId, setRet);
        return Response.ok();
    }


    @Getter
    @Setter
    public static class SkinInfo {

        /**
         * 0-未选皮肤，1-已选皮肤, -1未登录
         */
        protected int status;

        protected SkinSelectComponentAttr.SkinConfig skin;

        protected List<SkinSelectComponentAttr.SkinConfig> skinList;
    }


}
