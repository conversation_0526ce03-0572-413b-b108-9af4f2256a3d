package com.yy.gameecology.activity.config.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.Serializable;


@Configuration
public class RedisConfig2 extends BaseRedisConfig {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /* ##############################  gameecology redis  ############################# */

    public RedisConfig2(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        super(builderCustomizers);
    }

    @Bean
    @ConfigurationProperties("gameecology.group2.redis")
    public RedisProperties redisGroup2Properties() {
        return new RedisProperties();
    }

    @Bean
    public RedisConnectionFactory redisGroup2ConnectionFactory(RedisProperties redisGroup2Properties) {
        return new LettuceConnectionFactory(getRedisConfig(redisGroup2Properties), getClientConfig(redisGroup2Properties));
    }

    @Bean
    public StringRedisTemplate stringRedisGroup2Template(RedisConnectionFactory redisGroup2ConnectionFactory) {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(redisGroup2ConnectionFactory);
        return stringRedisTemplate;
    }

    @Bean
    public RedisTemplate<Serializable, Serializable> redisGroup2Template(RedisConnectionFactory redisGroup2ConnectionFactory) {
        RedisTemplate<Serializable, Serializable> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisGroup2ConnectionFactory);
        return redisTemplate;
    }

    @Bean
    public RedissonClient redissonClient2(RedisProperties redisGroup2Properties) {
        Config config = getRedissonConfig(redisGroup2Properties);
        config.setCheckLockSyncedSlaves(false);
        log.info("Creating RedissonClient for group2 with config: host={}, port={}, database={}",
                redisGroup2Properties.getHost(),
                redisGroup2Properties.getPort(),
                redisGroup2Properties.getDatabase());
        return Redisson.create(config);
    }

}
