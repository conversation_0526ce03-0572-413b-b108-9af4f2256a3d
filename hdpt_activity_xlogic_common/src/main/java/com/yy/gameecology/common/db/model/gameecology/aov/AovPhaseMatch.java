package com.yy.gameecology.common.db.model.gameecology.aov;

import java.util.Date;

public class AovPhaseMatch {
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private Long phaseId;

    /**
     * 所属轮次数
     */
    private Integer roundNum;

    /**
     * 对局名称
     */
    private String roundName;

    /**
     * 节点一索引，必须为偶数
     */
    private Integer nodeIndex1;

    /**
     * 节点二索引，必须为节点一索引值 + 1
     */
    private Integer nodeIndex2;

    /**
     * 
     */
    private Integer bo;

    /**
     * 比赛模式：0-5V5,1-1V1,3-3V3
     */
    private Integer battleMode;

    /**
     * 
     */
    private Date startTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Integer getRoundNum() {
        return roundNum;
    }

    public void setRoundNum(Integer roundNum) {
        this.roundNum = roundNum;
    }

    public String getRoundName() {
        return roundName;
    }

    public void setRoundName(String roundName) {
        this.roundName = roundName == null ? null : roundName.trim();
    }

    public Integer getNodeIndex1() {
        return nodeIndex1;
    }

    public void setNodeIndex1(Integer nodeIndex1) {
        this.nodeIndex1 = nodeIndex1;
    }

    public Integer getNodeIndex2() {
        return nodeIndex2;
    }

    public void setNodeIndex2(Integer nodeIndex2) {
        this.nodeIndex2 = nodeIndex2;
    }

    public Integer getBo() {
        return bo;
    }

    public void setBo(Integer bo) {
        this.bo = bo;
    }

    public Integer getBattleMode() {
        return battleMode;
    }

    public void setBattleMode(Integer battleMode) {
        this.battleMode = battleMode;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
}