package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.YoPopControlComponentAttr;
import com.yy.thrift.hdztranking.RoleType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-12 20:10
 **/
@Component
@RestController
@RequestMapping("/2068")
public class YoPopControlComponent extends BaseActComponent<YoPopControlComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Override
    public Long getComponentId() {
        return ComponentId.YO_POP_CONTROL;
    }

    @GetMapping("/getAppPopupUrl")
    public Response<HomePopupVo> getAppPopupUrl(@RequestParam(name = "actId") long actId,
                                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                @RequestParam(name = "uid") long uid,
                                                @RequestParam(name = "appMarket") String appMarket) {
        log.info("getAppPopupUrl begin,actId:{},uid:{},appMarket:{}", actId, uid, appMarket);
        YoPopControlComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        if (!actInfoService.inActTime(actId)) {
            log.info("getAppPopupUrl not in act time,actId:{},uid:{}", actId, uid);
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        //灰度状态，不在灰度白名单内不弹窗
        if (commonService.isGrey(actId)) {
            boolean inWhiteList = commonService.checkWhiteList(actId, RoleType.USER, Convert.toString(uid));
            if (!inWhiteList) {
                log.info("getAppPopupUrl not in white list,actId:{},uid:{}", actId, uid);
                return Response.success(new HomePopupVo(StringUtils.EMPTY));
            }
        }

        if (uid > 0 && attr.getMarket().contains(appMarket)) {
            log.info("getAppPopupUrl uid:{},appMarket:{}", uid, appMarket);
            return Response.success(new HomePopupVo(attr.getPopupLink()));
        }

        if (uid > 0) {
            String inWhiteList = whitelistComponent.getConfigValue(attr.getActId(), attr.getWhiteListCmptInx(), Convert.toString(uid));
            if (StringUtils.isNotBlank(inWhiteList)) {
                log.info("getAppPopupUrl uid:{},appMarket:{}", uid, appMarket);
                return Response.success(new HomePopupVo(attr.getPopupLink()));
            }
        }

        return Response.success(new HomePopupVo(StringUtils.EMPTY));
    }

    @Data
    public static class HomePopupVo {
        protected String popupLink;

        public HomePopupVo(String popupLink) {
            this.popupLink = popupLink;
        }
    }
}
