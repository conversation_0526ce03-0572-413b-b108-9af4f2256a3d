package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.hdzj.element.redis.KaiHeiSkinComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-09-29 22:38
 **/

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties", "classpath:env/local/application-inner.properties"})
public class KaiHeiSkinComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    static {
        System.setProperty("group", "4");
    }

    @Autowired
    private KaiHeiSkinComponent kaiHeiSkinComponent;

    @Test
    public void test(){
        kaiHeiSkinComponent.releaseSignAward(2959049318L,kaiHeiSkinComponent.getUniqueComponentAttr(2024094001L));
    }
}
