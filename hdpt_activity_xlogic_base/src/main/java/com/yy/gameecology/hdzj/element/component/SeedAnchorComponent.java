package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardBean;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SeedAnchorComponentAttr;
import com.yy.thrift.hdztranking.*;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2022.07.01 18:06
 * A.种子选手主播组件,在指定阶段选定几个主播作为种子主播
 * B.挑战种子主播,或者pk种子主播获胜可获得额外的奖励,种子主播获胜也可活动额外奖励
 * C.选定种子主播规则: 指定榜单阶段的TopN
 * D.奖励：PK阶段,从中台获取pk对阵信息,计算胜者,给胜者发放奖励
 */
@Component
@RestController
@RequestMapping("/cmpt/seedAnchorV2")
public class SeedAnchorComponent extends BaseActComponent<SeedAnchorComponentAttr> implements ComponentRankingExtHandle<SeedAnchorComponentAttr> {

    @Override
    public Long getComponentId() {
        return ComponentId.SEED_ANCHOR;
    }

    /**
     * 种子选手集合 seed_anchor_set_rankId_phaseId
     **/
    private static final String SEED_ANCHOR_SET = "seed_anchor_set_%s_%s";
    /**
     * 奖励记录:用于在榜单中展示 award_issue_record_rankId_phaseId zset{uid:award}
     */
    public static final String AWARD_ISSUE_RECORD = "award_issue_record_%s_%s_%s";

    /**
     * 1.确定种子主播
     **/
    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = false)
    public void choseSeedAnchor(PromotTimeEnd event, SeedAnchorComponentAttr attr) {
        long rankId = event.getRankId();

        long phaseId = event.getPhaseId();
        if (attr.getSeedRankId() != rankId || attr.getSeedPhaseId() != phaseId) {
            return;
        }

        long actId = attr.getActId();

        //获取topN 为种子主播
        List<String> seedAnchorUids = new ArrayList<>();
        for (Map.Entry<Long, Map<Long, Integer>> entry : attr.getSeedSourceCount().entrySet()) {
            for (Map.Entry<Long, Integer> phaseCountMap : entry.getValue().entrySet()) {
                List<Rank> rankList = hdztRankingThriftClient.queryRanking(actId, entry.getKey(), phaseCountMap.getKey(),
                        "", phaseCountMap.getValue(), Maps.newHashMap());
                seedAnchorUids.addAll(rankList.stream().map(Rank::getMember).collect(Collectors.toList()));
            }
        }

        String seedAnchorRedisKey = makeKey(attr, String.format(SEED_ANCHOR_SET, rankId, phaseId));
        actRedisDao.sBatchAdd(getRedisGroupCode(actId), seedAnchorRedisKey, seedAnchorUids.toArray(new String[0]));

        log.info("choseSeedAnchor done seq:{} seedAnchorUid:{} ", event.getSeq(), seedAnchorUids);
    }

    public boolean isSeedAnchor(SeedAnchorComponentAttr attr, long rankId, long phaseId, String memberId) {
        String seedAnchorRedisKey = makeKey(attr, String.format(SEED_ANCHOR_SET, rankId, phaseId));
        return actRedisDao.sIsMember(getRedisGroupCode(attr.getActId()), seedAnchorRedisKey, memberId);
    }


    public Set<String> seedAnchorSet(SeedAnchorComponentAttr attr, long rankId, long phaseId) {
        String seedAnchorRedisKey = makeKey(attr, String.format(SEED_ANCHOR_SET, rankId, phaseId));
        return actRedisDao.sMembers(getRedisGroupCode(attr.getActId()), seedAnchorRedisKey);
    }

    public Set<String> seedAnchorSet(long actId, long cmptUseInx, long rankId, long phaseId) {
        SeedAnchorComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        String seedAnchorRedisKey = makeKey(attr, String.format(SEED_ANCHOR_SET, rankId, phaseId));
        return actRedisDao.sMembers(getRedisGroupCode(attr.getActId()), seedAnchorRedisKey);
    }

    private long awardNum(long score, long rate, long max) {
        long award = score / 100 * rate / 100;
        return Math.min(award, max);
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEnd(PhaseTimeEnd event, SeedAnchorComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();

        Map<Long, Long> awardRankPhase = attr.getAwardRankPhase();
        Long awardPhase = awardRankPhase.get(rankId);
        if (awardPhase == null || awardPhase != phaseId) {
            return;
        }

        Set<String> seedAnchorSet = seedAnchorSet(attr, attr.getSeedRankId(), attr.getSeedPhaseId());

        if (seedAnchorSet.isEmpty()) {
            return;
        }

        Map<String, Long> awardUid2Award = Maps.newHashMap();

        // todo pk done
        String day = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));
        long actId = attr.getActId();
        if (attr.getWinType() == 1) {

            awardUid2Award = handlePkSeedAward(event, attr, seedAnchorSet, day);
        }
        String eseq = event.getEkey() + "|" + event.getSeq();

        //记录奖励
        String awardDuplicateKey = String.format("seed_award_mark_%s_%s_%s", rankId, phaseId, day);
        if (actRedisDao.setNX(getRedisGroupCode(actId), makeKey(attr, awardDuplicateKey), DateUtil.getNowYyyyMMddHHmmss())) {
            seedAnchorAward(eseq, attr, awardUid2Award, day);
            seedAwardRecord(attr, rankId, phaseId, awardUid2Award, day);
        }
        log.info("seedAnchor Award pk cal award done@ seq:{} rankId :{} phaseId:{}", event.getSeq(), rankId, phaseId);
    }

    private Map<String, Long> handlePkSeedAward(PhaseTimeEnd event, SeedAnchorComponentAttr attr, Set<String> seedAnchorSet, String day) {
        Map<String, Long> awardUid2Award = Maps.newHashMap();
        long seedAwardMax = attr.getSeedAwardMax();
        long seedAwardRate = attr.getSeedAwardRate();
        long actId = attr.getActId();
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();

        //1.获取pk对阵信息
        //2.获取种子主播

        PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, phaseId, day, day, false, true, Maps.newHashMap());
        pkInfo.getPkGroupItems().stream().map(PkGroupItem::getMemberPkItems).flatMap(Collection::stream)
                .forEach(
                        memberPkItems -> {
                            GroupMemberItem memberItem1 = memberPkItems.get(0);
                            GroupMemberItem memberItem2 = memberPkItems.get(1);
                            if (seedAnchorSet.contains(memberItem1.getMemberId()) || seedAnchorSet.contains(memberItem2.getMemberId())) {
                                if (memberItem1.getScore() != memberItem2.getScore()) {
                                    GroupMemberItem awardItem = memberItem1.getScore() > memberItem2.getScore() ? memberItem1 : memberItem2;
                                    awardUid2Award.put(awardItem.getMemberId(), awardNum(awardItem.getScore(), seedAwardRate, seedAwardMax));
                                }
                            }
                        });
        return awardUid2Award;
    }

    private void seedAwardRecord(SeedAnchorComponentAttr attr, long rankId, long phaseId, Map<String, Long> awardUid2Score, String day) {
        List<String> keys = new ArrayList<>();
        List<String> flied = new ArrayList<>();
        List<Long> value = new ArrayList<>();
        String key = makeKey(attr, String.format(AWARD_ISSUE_RECORD, rankId, phaseId, day));
        for (Map.Entry<String, Long> entry : awardUid2Score.entrySet()) {
            keys.add(key);
            flied.add(entry.getKey());
            value.add(entry.getValue());
        }
        actRedisDao.zBatchIncr(getRedisGroupCode(attr.getActId()), keys, flied, value);
        log.info("seedAwardRecord done: awardUid2Score:{}", JSON.toJSONString(awardUid2Score));
    }

    private void seedAnchorAward(String eseq, SeedAnchorComponentAttr attr, Map<String, Long> awardUid2Score, String day) {
        List<AwardBean> list = Lists.newArrayList();
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        for (Map.Entry<String, Long> entry : awardUid2Score.entrySet()) {
            int awardNum = Convert.toInt(entry.getValue());
            Map<Long, Integer> packageIdNumMap = ImmutableMap.of(attr.getSeedAwardPackageId(), awardNum);
            Map<Long, Map<Long, Integer>> taskPackageIds = ImmutableMap.of(attr.getSeedAwardTaskId(), packageIdNumMap);
            String myseq = java.util.UUID.randomUUID() + "#SeedAnchor" + (list.size() + 1);
            String receiver = entry.getKey();
            AwardBean awardBean = new AwardBean(myseq, attr.getBusiId(), receiver, time, taskPackageIds);
            list.add(awardBean);
        }
        // 保存奖励
        this.saveAward(attr, eseq, list);
        log.info("seedAnchorAward save award done, list:{}", JSON.toJSONString(list));
    }

    /**
     * 发放奖励， 使用 list 的 pop、push， 避免了定时器锁的需求
     */
    @Scheduled(cron = "*/5 * * * * ? ")
    @NeedRecycle(author = "yulianzhu", notRecycle = true)
    public void giveAwards() {
        this.giveAwards(2, 3600, 60 * 24);
    }

    @Override
    public List<Object> handleExt(SeedAnchorComponentAttr attr, GetRankReq rankReq,
                                  RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        Long rankId = rankReq.getRankId();
        Long phaseId = rankReq.getPhaseId();

        Map<Long, Long> awardRankPhase = attr.getAwardRankPhase();

        Long awardPhase = awardRankPhase.get(rankId);

        if (awardPhase == null || !awardPhase.equals(phaseId)) {
            return objectList;
        }

        String dateStr = "";
        if (rankReq.getDateStr() != null) {
            dateStr = rankReq.getDateStr();
        }

        Set<String> seedAnchorSet = seedAnchorSet(attr, attr.getSeedRankId(), attr.getSeedPhaseId());

        String key = makeKey(attr, String.format(AWARD_ISSUE_RECORD, rankId, phaseId, dateStr));
        Set<ZSetOperations.TypedTuple<String>> typedTuples = actRedisDao.zrevRange(getRedisGroupCode(attr.getActId()), key, 0, -1);
        Map<String, Long> awardUid2Score = Maps.newHashMap();
        if (typedTuples != null) {
            for (ZSetOperations.TypedTuple<String> typedTuple : typedTuples) {
                awardUid2Score.put(typedTuple.getValue(), Convert.toLong(typedTuple.getScore()));
            }
        }

        for (Object o : objectList) {
            RankValueItemBase item = (RankValueItemBase) o;
            if (item.getViewExt() == null) {
                item.setViewExt(Maps.newHashMap());
            }

            if (seedAnchorSet.contains(item.getKey())) {
                item.getViewExt().put("seedAnchor", "1");
            } else {
                item.getViewExt().put("seedAnchor", "2");
            }
            item.getViewExt().put("extAwardNum", awardUid2Score.getOrDefault(item.getKey(), 0L) + "");
        }
        return objectList;
    }

}
