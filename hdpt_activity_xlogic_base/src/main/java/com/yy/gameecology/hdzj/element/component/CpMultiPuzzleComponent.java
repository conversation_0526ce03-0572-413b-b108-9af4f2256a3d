package com.yy.gameecology.hdzj.element.component;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelChatTextInnerEvent;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerLayout;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.locker.redisson.RedissonLocker;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.component.attr.CpMultiPuzzleComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.*;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequestMapping("/5099")
@RestController
@Component
public class CpMultiPuzzleComponent extends BaseActComponent<CpMultiPuzzleComponentAttr> {

    public static final String TARGET_STR = "佳偶天成爱满人间";

    private final String TASK_COMPLETE_ZSET = "task_complete_%s";

    private final String ENTER_ROOM_BRO_SEQ = "enter_room_%s";

    private final String SEND_CHAT_LOTTERY_SEQ = "send_chat_lottery_%s";

    private final String AWARD_LIST = "award_list";

    private final String JACK_POINT_AWARD_LIMIT = "jack_point_award_limit";

    private static final String CP_ROOM_TOAST = "5099_cp_room_toast";

    private static final String CHANNEL_CHAT_AWARD = "5099_chatAward";

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private SignedService signedService;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private RedissonLocker redissonLocker;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    private static final String TASK_LOCK = "puzzle_task_lock";

    private static final String TASK_LEVEL_LOG = "task_level_%s_%s";

    private static final String PUZZLE_ALL_CNT = "puzzle_all_%s";

    private static final String PUZZLE_DAILY_CNT = "puzzle_daily_%s_%s";

    private static final String PUZZLE_CP_BALANCE = "puzzle_cp_balance:%s";

    private static final String PUZZLE_CP_DAILY_SCORE = "puzzle_cp_daily_score:%s";

    private static final String PUZZLE_ROW_AWARD_RECORD = "puzzle_row_award_record:%s_%s";  //1 2 3

    private static final String PUZZLE_COL_AWARD_RECORD = "puzzle_col_award_record:%s_%s"; //1 2 3

    private static final String PUZZLE_ALL_AWARD_RECORD = "puzzle_all_award_record:%s"; // 第几套

    private static final String PUZZLE_ROW_AWARD_RECORD_CNT = "puzzle_row_award_record_cnt:%s";

    private static final String PUZZLE_COL_AWARD_RECORD_CNT = "puzzle_col_award_record_cnt:%s";

    private static final String PUZZLE_ALL_AWARD_RECORD_CNT = "puzzle_all_award_record_cnt:%s";

    static class puzzleBroTp {

        //返回碎片奖励
        private static final int PUZZLE_FRAGMENT_TP_1 = 1;

        //返回连线奖励
        private static final int PUZZLE_FRAGMENT_TP_2 = 2;

        //婚礼殿堂
        private static final int PUZZLE_FRAGMENT_TP_3 = 3;

        //婚礼纪念
        private static final int PUZZLE_FRAGMENT_TP_4 = 4;

    }

    public void lockOld(CpMultiPuzzleComponentAttr attr) {
        Secret secret = null;
        String lockName = makeKey(attr, TASK_LOCK);
        try {
            secret = locker.lock(lockName, 5, "eKey", 10);
            if (secret == null) {
                throw new SuperException("网络超时，请重试", SuperException.E_DATA_ERROR);
            }
            locker.unlock(lockName, secret);
        } catch (Exception e) {
            log.error("onTaskProgressChanged error:{}", e.getMessage(), e);
            throw e;
        } finally {
            if (secret != null) {
                locker.unlock(lockName, secret);
            }
        }
    }
    public void lockNew(CpMultiPuzzleComponentAttr attr) {
        String lockName = makeKey(attr, TASK_LOCK);
        RLock lock = getRedissonClient().getLock(lockName);
        try {
            boolean lockSucc = redissonLocker.lock(lock, 5, 10);
            if(lockSucc) {
                //
            }
        } catch (Exception e) {
            log.error("onTaskProgressChanged error:{}", e.getMessage(), e);
            throw e;
        } finally {
           redissonLocker.unLock(lock);
        }
    }


    @Override
    public Long getComponentId() {
        return ComponentId.CP_MULTI_PUZZLE;
    }

    @NeedRecycle(author = "guanqhua", notRecycle = true)
    @Scheduled(cron = "0 0 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }
            CpMultiPuzzleComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            Date now = commonService.getNow(attr.getActId());
            Date noticeDate = DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30));
            String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE9);
            String groupCode = getRedisGroupCode(actId);
            String execKey = makeKey(attr, "execCpPuzzleStatic:"+timeCode);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}",execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId,now);
            doStaticReport(attr);
        }
    }

    public void doStaticReport(CpMultiPuzzleComponentAttr attr) {
        StringBuilder content = new StringBuilder();
        content.append("### 奖品累计发放数量\n");
        for (Integer no : attr.getRowPuzzles().keySet()) {
            content.append(no).append("套");
            for (CpMultiPuzzleComponentAttr.RowPuzzle rowPuzzle : attr.getRowPuzzles().get(no)) {
                String key = String.format(PUZZLE_ROW_AWARD_RECORD_CNT, rowPuzzle.getPackageId());
                long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, key)));
                content.append("行row:").append(rowPuzzle.getRow()).append(rowPuzzle.getName()).append(" 个数 = ").append(value).append("\n");

            }
        }
        String msg = buildActRuliuMsg(attr.getActId(), false, "拼图玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
        content = new StringBuilder();
        content.append("### 奖品累计发放数量\n");
        for (Integer no : attr.getColPuzzles().keySet()) {
            content.append(no).append("套");
            for (CpMultiPuzzleComponentAttr.ColPuzzle colPuzzle : attr.getColPuzzles().get(no)) {
                String key = String.format(PUZZLE_COL_AWARD_RECORD_CNT, colPuzzle.getPackageId());
                long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, key)));
                content.append("列col:").append(colPuzzle.getRow()).append(colPuzzle.getName()).append(" 个数 = ").append(value).append("\n");
            }
        }
        long cnt = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_ALL_AWARD_RECORD_CNT, attr.getJackPointPackageId()))));
        content.append("大奖:").append(attr.getJackPointAwardName()).append(" 个数 = ").append(cnt).append("\n");
        msg = buildActRuliuMsg(attr.getActId(), false, "拼图玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

        content = new StringBuilder();
        content.append("### 每日各等级任务达成CP数\n");
        List<Long> missions = attr.getMissions();
        int len = missions.size();
        Date now = commonService.getNow(attr.getActId());
        String date = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        for(int i=1; i<=len; i++) {
            long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(TASK_LEVEL_LOG, i, date))));
            content.append(date).append("等级:").append(i).append(" 任务达成CP数:").append(value).append("\n");
        }
        msg = buildActRuliuMsg(attr.getActId(), false, "拼图玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

        content = new StringBuilder();
        content.append("### 每日碎片数\n");
        for (Integer no : attr.getRowPuzzles().keySet()) {
            content.append(no).append("套");
            for (CpMultiPuzzleComponentAttr.RowPuzzle rowPuzzle : attr.getRowPuzzles().get(no)) {
                String[] cids = rowPuzzle.getPuzzles().split(",");
                for (String cid : cids) {
                    long val = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                            makeKey(attr, String.format(PUZZLE_DAILY_CNT, date, cid))));
                    content.append(date).append("碎片:").append(cid).append(" 数:").append(val).append("\n");
                }
            }
        }
        msg = buildActRuliuMsg(attr.getActId(), false, "拼图玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

        content = new StringBuilder();
        content.append("### 累计碎片数\n");
        for (Integer no : attr.getRowPuzzles().keySet()) {
            content.append(no).append("套");
            for (CpMultiPuzzleComponentAttr.RowPuzzle rowPuzzle : attr.getRowPuzzles().get(no)) {
                String[] cids = rowPuzzle.getPuzzles().split(",");
                for (String cid : cids) {
                    long val = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                            makeKey(attr, String.format(PUZZLE_ALL_CNT, cid))));
                    content.append("碎片:").append(cid).append(" 数:").append(val).append("\n");
                }
            }
        }
        msg = buildActRuliuMsg(attr.getActId(), false, "拼图玩法", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
    }

    @HdzjEventHandler(value = RankingScoreChanged.class,canRetry = true)
    public void onRankScoreChange(RankingScoreChanged event, CpMultiPuzzleComponentAttr attr) {
        if(attr.getCpRankId() == event.getRankId()) {
            log.info("onRankScoreChange start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
            String seq = event.getSeq();
            String date = DateUtil.format(DateUtil.getDate(event.getTimestamp(),
                    DateUtil.DEFAULT_PATTERN), DateUtil.PATTERN_TYPE2);
            actRedisDao.zIncrWithSeq(redisConfigManager.getGroupCode(attr.getActId()),
                    seq, makeKey(attr, String.format(PUZZLE_CP_DAILY_SCORE, date)),
                    event.getMember(), event.getItemScore());
        }
    }

    @RequestMapping("/testBroSvga")
    public Response testBroSvga(long actId, long cmptId, long uid, long sid, long ssid, int no) {
        if (SysEvHelper.isDeploy()) {
            return Response.success(null);
        }
        WebdbUserInfo webdbUserInfo = webdbUinfoClient.getUserInfo(uid);
        CpMultiPuzzleComponentAttr attr = getComponentAttr(actId, cmptId);
        broBanner(attr, webdbUserInfo, webdbUserInfo, UUID.randomUUID().toString(), sid, ssid, uid+"|"+uid, no);
        return Response.success(null);
    }

    @RequestMapping("/testBroCpPuzzle")
    public Response testBroCpPuzzle(long actId, long uid, String text, String subText, String img, String cp, int tp) {
        if (SysEvHelper.isDeploy()) {
            return Response.success(null);
        }
        broUser(actId, uid, text, subText, img, cp, tp);
        return Response.success(null);
    }

    @RequestMapping("/getAwardPool")
    public Response getAwardPool(long actId, long cmptId) {
        CpMultiPuzzleComponentAttr attr = getComponentAttr(actId, cmptId);
        if(attr == null) {
            return Response.fail(400, "参数错误");
        }
        long consume = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, JACK_POINT_AWARD_LIMIT)));
        if(attr.getJackPointLimit() - consume < 0) {
            return Response.success(0);
        } else {
            return Response.success((attr.getPriceLimit() -   consume * attr.getJackPointPrice()) < 0
                    ? 0 : attr.getPriceLimit() -   consume * attr.getJackPointPrice());
        }
    }

    @RequestMapping("/listAchievement")
    public Response listAchievement(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptId) {
        CpAchievementRep rsp = new CpAchievementRep();
        CpMultiPuzzleComponentAttr attr = getComponentAttr(actId, cmptId);
        List<String> awardRawList = actRedisDao.lrange(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, AWARD_LIST), 0, 30);
        List<CpAchievementMember> cpAchievementMembers = new ArrayList<>();
        for (String raw : awardRawList) {
            cpAchievementMembers.add(JsonUtils.deserialize(raw, CpAchievementMember.class));
        }
        List<Long> uids = new ArrayList<>();
        for (CpAchievementMember cpAchievementMember : cpAchievementMembers) {
            uids.add(cpAchievementMember.getUser().getUid());
            uids.add(cpAchievementMember.getAnchor().getUid());
        }
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(uids));
        if(batched != null) {
            NickExt nickExt = JsonUtils.deserialize(batched.getNickExt(), NickExt.class);
            rsp.setNickExtUsers(nickExt.getUsers());
            for (CpAchievementMember cpAchievementMember : cpAchievementMembers) {
                MemInfo userMemInfo = cpAchievementMember.getUser();
                Map<String, WebdbUserInfo> userInfoMap = batched.getUserInfoMap();
                userMemInfo.setAvatar(WebdbUtils.getLogo(userInfoMap.get(userMemInfo.getUid()+"")));
                userMemInfo.setName(userInfoMap.get(userMemInfo.getUid()+"").getNick());
                MemInfo anchorMemInfo = cpAchievementMember.getAnchor();
                anchorMemInfo.setAvatar(WebdbUtils.getLogo(userInfoMap.get(anchorMemInfo.getUid()+"")));
                anchorMemInfo.setName(userInfoMap.get(anchorMemInfo.getUid()+"").getNick());
            }
        }
        for (CpAchievementMember cpAchievementMember : cpAchievementMembers) {
            ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(cpAchievementMember.getAnchor().getUid());
            if(channelInfoVo != null) {
                cpAchievementMember.setSid(channelInfoVo.getSid());
                cpAchievementMember.setSsid(channelInfoVo.getSsid());
            }
        }
        rsp.setCpMembers(cpAchievementMembers);
        return Response.success(rsp);
    }

        @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, CpMultiPuzzleComponentAttr attr) {
        if(attr.getCpTaskRankId() == event.getRankId() && attr.getCpTaskPhaseId() == event.getPhaseId()) {
            log.info("processTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
            String eKey = MD5SHAUtil.getMD5(StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey());
            long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
            if (startIndex == currIndex) {
                return;
            }

            for (long i = startIndex + 1; i <= currIndex; i++) {
                incrLogVal(attr, makeKey(attr, String.format(TASK_LEVEL_LOG, i,
                        DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2))), 1);
            }

            //凑齐了就不再处理。
            String member = event.getMember();
            Map<Object, Object> puzzleMap = actRedisDao.hGetAll(
                    redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(PUZZLE_CP_BALANCE, member)));
            Map<String, Long> puzzleBalaceMap = Maps.newHashMap();
            if (puzzleMap != null) {
                puzzleMap.forEach((k, v) -> puzzleBalaceMap.put(Convert.toString(k), Convert.toLong(v)));
            }
            if(collectAll(attr, puzzleBalaceMap)) {
                log.info("task cp:{} puzzle is full balance:{}", member, JsonUtil.toJson(puzzleBalaceMap));
                return;
            }

            String[] members = member.split("\\|");
            long userId = Convert.toLong(members[0]);
            long anchorUid = Convert.toLong(members[1]);
            String sidSsidStr = event.getActors().get(attr.getTingActor());
            String[] sidSsidArray = sidSsidStr.split("_");
            long sid = Convert.toLong(sidSsidArray[0]);
            long ssid = Convert.toLong(sidSsidArray[1]);

            Secret secret = null;
            String lockName = makeKey(attr, TASK_LOCK);
            try {
                secret = locker.lock(lockName, 5, eKey, 10);
                if (secret == null) {
                    throw new SuperException("网络超时，请重试", SuperException.E_DATA_ERROR);
                }
                for (long i = startIndex + 1; i <= currIndex; i++) {
                    String seq = eKey + "_" + i;
                    puzzleMap = actRedisDao.hGetAll(
                            redisConfigManager.getGroupCode(attr.getActId()),
                            makeKey(attr, String.format(PUZZLE_CP_BALANCE, member)));
                    Map<String, Long> puzzleBalaceMapT = Maps.newHashMap();
                    if (puzzleMap != null) {
                        puzzleMap.forEach((k, v) -> puzzleBalaceMapT.put(Convert.toString(k), Convert.toLong(v)));
                    }
                    if(collectAll(attr, puzzleBalaceMapT)) {
                        log.info("task cp:{} puzzle is full balance:{}", member, JsonUtil.toJson(puzzleBalaceMapT));
                        continue;
                    }
                    int nextCollectSet = getNextCollectSet(attr, puzzleBalaceMapT);
                    String nextPuzzleCid = getNextPuzzle(attr, nextCollectSet, (int)i, puzzleBalaceMapT);
                    log.info("task progress member:{}, collectSet:{}, nextPuzzleCid:{}", member, nextCollectSet, nextPuzzleCid);
                    handleTaskPuzzle(attr, puzzleBalaceMapT, nextPuzzleCid, member, userId,
                            anchorUid, seq, sid, ssid, i, nextCollectSet);
                }
                locker.unlock(lockName, secret);
            } catch (Exception e) {
                log.error("onTaskProgressChanged error:{}", e.getMessage(), e);
                throw e;
            } finally {
                if (secret != null) {
                    locker.unlock(lockName, secret);
                }
            }
        }
    }
    public boolean collectAll(CpMultiPuzzleComponentAttr attr, Map<String, Long> puzzleBalaceMap) {
        List<CpMultiPuzzleComponentAttr.RowCol> rowCols = new ArrayList<>();
        for (Integer no : attr.getCidRowColMap().keySet()) {
            rowCols.addAll(attr.getCidRowColMap().get(no));
        }
        return puzzleBalaceMap.keySet().size() == rowCols.size();
    }

    public int getNextCollectSet(CpMultiPuzzleComponentAttr attr, Map<String, Long> puzzleBalaceMap) {
        for (Integer no : attr.getCidRowColMap().keySet()) {
            List<CpMultiPuzzleComponentAttr.RowCol> rowCols = attr.getCidRowColMap().get(no);
            for (CpMultiPuzzleComponentAttr.RowCol rowCol : rowCols) {
                if(!puzzleBalaceMap.containsKey(rowCol.getCid())) {
                    return no;
                }
            }
        }
        return -1;
    }

    public String getNextPuzzle(CpMultiPuzzleComponentAttr attr, int no, int taskLevel, Map<String, Long> puzzleBalaceMap) {
        List<CpMultiPuzzleComponentAttr.RowPuzzle> rows = attr.getRowPuzzles().get(no);
        List<CpMultiPuzzleComponentAttr.ColPuzzle> cols = attr.getColPuzzles().get(no);
        List<CpMultiPuzzleComponentAttr.RowCol> rowCols = attr.getCidRowColMap().get(no);
        Map<String, CpMultiPuzzleComponentAttr.RowCol> cidRowColMap = rowCols.stream().collect(Collectors.toMap(CpMultiPuzzleComponentAttr.RowCol::getCid, rowCol -> rowCol));
        int[][] board = new int[rows.size()][cols.size()];
        List<String> lackSids = new ArrayList<>();
        //发万能碎片
        if (commonService.getNow(attr.getActId()).getTime() > attr.getJackPointTimestamp()
                && taskLevel >= attr.getJackPointTaskIndex()) {
            //发万能碎片
            String sendCid = null;
            for (String cid : cidRowColMap.keySet()) {
                if(puzzleBalaceMap.containsKey(cid)) {
                    int r = cidRowColMap.get(cid).getRow();
                    int c = cidRowColMap.get(cid).getCol();
                    board[r-1][c-1] = 1;
                }
                if (puzzleBalaceMap.containsKey(cid)) {
                    continue;
                }
                lackSids.add(cid);
            }
            if(!lackSids.isEmpty()) {
                int randomIndex = RandomUtils.nextInt(0, lackSids.size());
                sendCid = lackSids.get(randomIndex);
            }
            //寻找优先能连线的拼图
            int[] winMove = findWinningMove(board);
            if(winMove != null) {
                int row = winMove[0] + 1;
                int col = winMove[1] + 1;
                for (String c : cidRowColMap.keySet()) {
                    if(row == cidRowColMap.get(c).getRow() && col == cidRowColMap.get(c).getCol()){
                        sendCid = c;
                        break;
                    }
                }
            }
            return sendCid;
        }
        return null;
    }

    private static int[] findWinningMove(int[][] board) {
        int r = board.length;
        int c = board[0].length;
        // 检查每一行
        for (int i = 0; i < r; i++) {
            int[] row = board[i];
            int[] result = checkLine(row, c);
            if (result != null) {
                return new int[]{i, result[1]};
            }
        }

        // 检查每一列
        for (int j = 0; j < c; j++) {
            int[] column = new int[r];
            for (int i = 0; i < r; i++) {
                column[i] = board[i][j];
            }
            int[] result = checkLine(column, r);
            if (result != null) {
                return new int[]{result[0], j};
            }
        }
        return null;
    }

    private static int[] checkLine(int[] line, int size) {
        for (int i = 0; i < size - 1; i++) {
            int countPlayer = 0;
            int emptyIndex = -1;
            for (int j = 0; j < size; j++) {
                if (line[j] == 1) {
                    countPlayer++;
                }
                if (line[j] == 0) {
                    emptyIndex = j;
                }
            }
            if (countPlayer == size - 1 && emptyIndex != -1) {
                return new int[]{emptyIndex, emptyIndex};
            }
        }
        return null;
    }

    public void handleTaskPuzzle(CpMultiPuzzleComponentAttr attr,
                                 Map<String, Long> puzzleBalaceMap, String sendCid,
                                 String cp, long uid, long anchorUid, String seq, long sid, long ssid, long level, int no) {
        long lotteryTaskId = attr.getLotteryTaskIdMap().get(no);
        List<CpMultiPuzzleComponentAttr.RowCol> rowCols = attr.getCidRowColMap().get(no);
        List<CpMultiPuzzleComponentAttr.RowPuzzle> rowPuzzles = attr.getRowPuzzles().get(no);
        List<CpMultiPuzzleComponentAttr.ColPuzzle> colPuzzles = attr.getColPuzzles().get(no);
        Map<String, CpMultiPuzzleComponentAttr.RowCol> cidRowColMap = rowCols.stream().collect(Collectors.toMap(CpMultiPuzzleComponentAttr.RowCol::getCid, rowCol -> rowCol));
        Map<Integer, CpMultiPuzzleComponentAttr.RowPuzzle> rowPuzzleMap = rowPuzzles.stream().collect(Collectors.toMap(CpMultiPuzzleComponentAttr.RowPuzzle::getRow, rowPuzzle -> rowPuzzle));
        Map<Integer, CpMultiPuzzleComponentAttr.ColPuzzle> colPuzzleMap = colPuzzles.stream().collect(Collectors.toMap(CpMultiPuzzleComponentAttr.ColPuzzle::getRow, colPuzzle -> colPuzzle));

        String currency = "";
        if(StringUtil.isEmpty(sendCid)) {
            //发随机碎片
            BatchLotteryResult result = hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(),
                    BusiId.GAME_ECOLOGY.getValue(), anchorUid,lotteryTaskId, 1, 0, seq);
            log.info("uid:{} lottery result:{}", anchorUid, JsonUtil.toJson(result));
            String pic = "";
            //记录中奖记录
            if (result.getCode() == 0) {
                long recordId = 0L;
                for (Long value : result.getRecordPackages().values()) {
                    recordId = value;
                }
                currency = attr.getLotteryPuzzleMap().get(recordId);
                //String subText = puzzleBalaceMap.containsKey(currency) ? "(重复获得，只能点亮一次！)" : null;
                //随机
                broUser(attr.getActId(), anchorUid, "婚礼拼图碎片1个!", null, cidRowColMap.get(currency).getPic(), cp, puzzleBroTp.PUZZLE_FRAGMENT_TP_1);
                broUser(attr.getActId(), uid, "婚礼拼图碎片1个!", null, cidRowColMap.get(currency).getPic(), cp, puzzleBroTp.PUZZLE_FRAGMENT_TP_1);
            }
        } else {
            currency = sendCid;
            //万能碎片
            String text ="【恭喜获得万能拼图，将优先点亮空缺的拼图哦~】";
            String pic=  attr.getJackPointPic();
            String subText = null;
            if(level < attr.getJackPointShowTaskIndex()) {
                text =  "婚礼拼图碎片1个!";
                pic =cidRowColMap.get(currency).getPic();
                //subText = puzzleBalaceMap.containsKey(currency) ? "(重复获得，只能点亮一次！)" : null;
                log.info("cp:{}, get jackpoint but no show", cp);
            }
            broUser(attr.getActId(), anchorUid, text, null, pic, cp, puzzleBroTp.PUZZLE_FRAGMENT_TP_1);
            broUser(attr.getActId(), uid, text, null, pic, cp, puzzleBroTp.PUZZLE_FRAGMENT_TP_1);
        }
        actRedisDao.hIncrByKey(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_CP_BALANCE, cp)), currency, 1L);

        incrLogVal(attr, makeKey(attr, String.format(PUZZLE_DAILY_CNT, DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2), currency)), 1);
        incrLogVal(attr, makeKey(attr, String.format(PUZZLE_ALL_CNT,  currency)), 1);

        puzzleBalaceMap.put(currency, puzzleBalaceMap.getOrDefault(currency, 0L) + 1);
        CpMultiPuzzleComponentAttr.RowCol rowCol = cidRowColMap.get(currency);
        int row = rowCol.getRow();
        int col = rowCol.getCol();
        CpMultiPuzzleComponentAttr.RowPuzzle rowPuzzle = rowPuzzleMap.get(row);
        CpMultiPuzzleComponentAttr.ColPuzzle colPuzzle = colPuzzleMap.get(col);
        String[] rows = rowPuzzle.getPuzzles().split(",");
        int rowCount = 0;
        for (String rowCid : rows) {
            if(puzzleBalaceMap.containsKey(rowCid)) {
                rowCount++;
            }
        }
        int colCount = 0;
        String[] cols = colPuzzle.getPuzzles().split(",");
        for (String colCid : cols) {
            if(puzzleBalaceMap.containsKey(colCid)) {
                colCount++;
            }
        }
        if(rowCount == rows.length) {
            boolean hset = actRedisDao.hsetnx(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(PUZZLE_ROW_AWARD_RECORD, no, cp)), row+"", "1");
            if(hset) {
                CpMultiPuzzleComponentAttr.RowPuzzle puzzle = rowPuzzleMap.get(row);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        uid, puzzle.getTaskId(), 1, puzzle.getPackageId(), seq + "_r_" + uid, null);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        anchorUid, puzzle.getTaskId(), 1, puzzle.getPackageId(), seq + "_r_" + anchorUid, null);
                incrLogVal(attr, makeKey(attr, String.format(PUZZLE_ROW_AWARD_RECORD_CNT, puzzle.getPackageId())), 1);
                //broUser 三连线
                broUser(attr.getActId(), anchorUid, "获得"+ puzzle.getName(), null, puzzle.getPic(), cp, puzzleBroTp.PUZZLE_FRAGMENT_TP_2);
                broUser(attr.getActId(), uid, "获得"+ puzzle.getName(), null, puzzle.getPic(), cp, puzzleBroTp.PUZZLE_FRAGMENT_TP_2);
                lPushAward(attr, anchorUid, uid, puzzle.getName());
            }
        }
        if(colCount == cols.length) {
            boolean hset = actRedisDao.hsetnx(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(PUZZLE_COL_AWARD_RECORD, no, cp)), col+"", "1");
            if(hset) {
                CpMultiPuzzleComponentAttr.ColPuzzle puzzle = colPuzzleMap.get(col);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        uid, puzzle.getTaskId(), 1, puzzle.getPackageId(), seq + "_c_" + uid, null);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        anchorUid, puzzle.getTaskId(), 1, puzzle.getPackageId(), seq + "_c_" + anchorUid, null);
                incrLogVal(attr, makeKey(attr, String.format(PUZZLE_COL_AWARD_RECORD_CNT, puzzle.getPackageId())), 1);
                //broUser 三连线
                broUser(attr.getActId(), anchorUid, "获得"+ puzzle.getName(), null, puzzle.getPic(), cp, puzzleBroTp.PUZZLE_FRAGMENT_TP_2);
                broUser(attr.getActId(), uid, "获得"+ puzzle.getName(), null, puzzle.getPic(), cp, puzzleBroTp.PUZZLE_FRAGMENT_TP_2);
                lPushAward(attr, anchorUid, uid, puzzle.getName());
            }
        }
        int collectCnt = 0;
        for (CpMultiPuzzleComponentAttr.RowCol rc : rowCols) {
            if(puzzleBalaceMap.containsKey(rc.getCid())) {
                collectCnt++;
            }
        }
        if(collectCnt == (rows.length * cols.length)) {
            boolean hset = actRedisDao.hsetnx(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(PUZZLE_ALL_AWARD_RECORD, no)), cp, "1");
            if(hset) {
                long taskId = attr.getJackPointTaskId();
                long packageId = attr.getJackPointPackageId();
                String awardName = attr.getJackPointAwardName();
                String awardPic = attr.getJackPointAwardPic();
                List<Long> result = actRedisDao
                        .incrValueWithLimitSeq(redisConfigManager.getGroupCode(attr.getActId()),
                                seq, makeKey(attr, JACK_POINT_AWARD_LIMIT), 1, attr.getJackPointLimit(), true, 60 * 60 * 24 * 10);
                if (result.get(0) <= 0) {
                    String[] taskPackage = attr.getJackPointBackUp().split("_");
                    taskId = Convert.toLong(taskPackage[0]);
                    packageId = Convert.toLong(taskPackage[1]);
                    awardName = attr.getJackPointBackUpName();
                    awardPic = attr.getJackPointBackUpPic();
                }
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        uid, taskId, 1, packageId, seq + "_a_" + uid, null);
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                        anchorUid, taskId, 1, packageId, seq + "_a_" + anchorUid, null);
                List<Long> uids = Lists.newArrayList(uid, anchorUid);
                Map<Long, WebdbUserInfo> userInfos = webdbUinfoClient.batchGetUserInfo(uids);
                WebdbUserInfo anchorUserInfo = userInfos.get(anchorUid);
                WebdbUserInfo userInfo = userInfos.get(uid);
                incrLogVal(attr, makeKey(attr, String.format(PUZZLE_ALL_AWARD_RECORD_CNT, packageId)), 1);
                //全服告白
                broBanner(attr, userInfo, anchorUserInfo, seq, sid, ssid, cp, no);
                //broUser 解锁全部拼图
                broUser(attr.getActId(), anchorUid, "获得"+ awardName, null, awardPic, cp, (no == 1 ? puzzleBroTp.PUZZLE_FRAGMENT_TP_3 : puzzleBroTp.PUZZLE_FRAGMENT_TP_4));
                broUser(attr.getActId(), uid, "获得"+ awardName, null, awardPic, cp, (no == 1 ? puzzleBroTp.PUZZLE_FRAGMENT_TP_3 : puzzleBroTp.PUZZLE_FRAGMENT_TP_4));
                zaddPuzzleTaskInfo(attr, no, sid, ssid, anchorUid, uid, seq);
                lPushAward(attr, anchorUid, uid, attr.getJackPointAwardName());
            }
        }
        //发虚拟货币
        long currencyTaskId = rowCol.getTaskId();
        long currencyPackageId = rowCol.getPackageId();
        hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                anchorUid, currencyTaskId, 1, currencyPackageId, seq + "_cur_" + anchorUid, null);
    }

    private void zaddPuzzleTaskInfo(CpMultiPuzzleComponentAttr attr, Integer no, long sid, long ssid, long babyUid, long userUid, String seq) {
        String taskCompletePuzzleKey = String.format(TASK_COMPLETE_ZSET, no);
        Date now = commonService.getNow(attr.getActId());
        double score = now.getTime() + 300000;
        BestCp bestCp = new BestCp();
        bestCp.setSid(sid);
        bestCp.setSsid(ssid);
        bestCp.setBabyUid(babyUid);
        bestCp.setUserUid(userUid);
        bestCp.setStartTime(now.getTime());
        bestCp.setEndTime(now.getTime() + 300000);
        bestCp.setText(attr.getPuzzleCompleteText().get(no));
        bestCp.setSeq(seq);
        actRedisDao.zAdd(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, taskCompletePuzzleKey), JsonUtil.toJson(bestCp), score);
        log.info("add zset puzzle no:{}, zkey:{}, babyCpInfo:{}", no, taskCompletePuzzleKey, JsonUtil.toJson(bestCp));
    }

    private void lPushAward(CpMultiPuzzleComponentAttr attr, long babyUid, long userUid, String awardName) {
        CpAchievementMember cpAchievementMember = new CpAchievementMember();
        cpAchievementMember.setCpMember(userUid + "|" + babyUid);
        MemInfo anchor = new MemInfo();
        anchor.setUid(babyUid);
        cpAchievementMember.setAnchor(anchor);
        MemInfo user = new MemInfo();
        user.setUid(userUid);
        cpAchievementMember.setUser(user);
        cpAchievementMember.setText(awardName);
        actRedisDao.lpush(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, AWARD_LIST), JsonUtil.toJson(cpAchievementMember));
    }



    private void broUser(long actId, long uid, String text, String subText, String img, String cp, int tp) {
        JSONObject json = new JSONObject(4);
        json.put("tp", tp);
        json.put("text", text);
        json.put("subText", subText);
        json.put("img", img);
        json.put("cpMember", cp);
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType("cp_puzzle")
                .setExtJson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.unicastUid(uid, msg);
        log.info("broUser success with uid:{}, ", uid);
    }

    private void broBanner(CpMultiPuzzleComponentAttr attr, WebdbUserInfo userInfo,
                           WebdbUserInfo anchorUserInfo, String seq, long sid, long ssid, String cpMember, int no) {
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        String logo = WebdbUtils.getLogo(userInfo);

        String anchorNick = anchorUserInfo.getNick();
        String anchorLogo = WebdbUtils.getLogo(anchorUserInfo);

        long bannerId = attr.getBannerId();
        long bannerType = attr.getBannerType();
        Map<String, Object> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("userNick", org.apache.commons.codec.binary.Base64.encodeBase64String(nick.getBytes()));
        extMap.put("babyNick", org.apache.commons.codec.binary.Base64.encodeBase64String(anchorNick.getBytes()));
        extMap.put("userLogo", logo);
        extMap.put("babyLogo", anchorLogo);
        extMap.put("sid", sid);
        extMap.put("ssid", ssid);
        extMap.put("cpMember", cpMember);
        extMap.put("no", no);

        long uid = Convert.toLong(userInfo.getUid());
        long anchorUid = Convert.toLong(anchorUserInfo.getUid());

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setUserUid(uid).setUserNick(nick).setUserLogo(logo)
                .setBannerId(bannerId).setBannerType(bannerType)
                .setUserScore(0).setJsonData(JSON.toJSONString(extMap))
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        BusiId busiId = BusiId.findByValue((int) attr.getBusiId());
        broadCastHelpService.broadcast(attr.getActId(), busiId, attr.getBannerBroType(), 0, 0, bannerBroMsg);
        if(attr.getBannerSvag() != null && attr.getBannerSvag().containsKey(no+"")) {
            BannerSvagConfig svagConfig = attr.getBannerSvag().get(no+"");
            AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();
            UserBaseInfo userInfoT = new UserBaseInfo();
            userInfoT.setUid(Convert.toLong(userInfo.getUid()));
            userInfoT.setNick(userInfo.getNick());
            UserBaseInfo anchorUserInfoT = new UserBaseInfo();
            anchorUserInfoT.setUid(Convert.toLong(anchorUserInfo.getUid()));
            anchorUserInfoT.setNick(anchorUserInfo.getNick());
            //svga内嵌文字
            Set<Long> uids = new HashSet<>();
            uids.add(anchorUid);
            uids.add(uid);
            List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, svagConfig, userInfoT, anchorUserInfoT);
            broSvgaConfig.setContentLayers(broContentLayers);
            //svga内嵌图片
            MemberInfo memberInfo = new MemberInfo();
            memberInfo.setHdLogo(logo);
            MemberInfo anchorMemberInfo = new MemberInfo();
            anchorMemberInfo.setHdLogo(anchorLogo);
            List<Map<String, String>> broImgLayers = getSvgaImageConfig(attr, memberInfo, anchorMemberInfo);
            broSvgaConfig.setImgLayers(broImgLayers);

            broSvgaConfig.setLoops(attr.getLoops());

            AppBannerLayout layout = new AppBannerLayout();
            layout.setType(attr.getLayoutType());
            if (StringUtil.isNotBlank(attr.getLayoutMargin())) {
                layout.setMargin(JSON.parseObject(attr.getLayoutMargin(), new TypeReference<Map<String, List<Integer>>>() {
                }));
            }
            broSvgaConfig.setLayout(layout);
            broSvgaConfig.setWhRatio(attr.getWhRatio());
            broSvgaConfig.setClickLayerName(svagConfig.getClickLayerName());
            broSvgaConfig.setSvgaURL(svagConfig.getSvgaURL());
            broSvgaConfig.setJumpSvgaURL(svagConfig.getJumpSvgaURL());
            broSvgaConfig.setMiniURL(svagConfig.getMiniURL());
            broSvgaConfig.setJumpMiniURL(svagConfig.getJumpMiniURL());

            AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getBroBusiId(),
                    3, 0, 0, "",
                    Lists.newArrayList());
            appBannerEvent.setUid(uid);
            appBannerEvent.setUidList(List.copyOf(uids));
            appBannerEvent.setContentType(6);
            appBannerEvent.setAppId(KafkaService.getTurnoverAppId((int) attr.getBusiId()));
            appBannerEvent.setSvgaConfig(broSvgaConfig);
            kafkaService.sendAppBannerKafka(appBannerEvent);
            log.info("app bro done seq:{}, member:{} event:{}", seq, uid, JSON.toJSONString(appBannerEvent));
        }
    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(CpMultiPuzzleComponentAttr attr, BannerSvagConfig svagConfig,
                                                                   UserBaseInfo userInfo, UserBaseInfo anchorUserInfo) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        String contentLayerCodes = svagConfig.getContentLayerCodes();
        if (StringUtil.isNotBlank(contentLayerCodes)) {
            String[] contentLayerCodeArr = contentLayerCodes.split(",");
            for (String contentLayerCode : contentLayerCodeArr) {
                Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
                BannerSvgaTextConfig textConfig = attr.getSvgaText().get(contentLayerCode);
                if (textConfig == null) {
                    continue;
                }
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
                //配置动态替换文本
                String text = contextReplace(textConfig.getText(), userInfo, anchorUserInfo);
                if (attr.getTextDynamicValue() != null) {
                    Map<String, String> replaceValue = attr.getTextDynamicValue();
                    for (String key : replaceValue.keySet()) {
                        text = text.replace(key, replaceValue.get(key));
                    }
                }
                appBannerSvgaText.setText(text);
                appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
                appBannerSvgaText.setGravity(textConfig.getGravity());
                if (StringUtil.isNotBlank(textConfig.getImages())) {
                    appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
                }
                if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                    appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
                }
                broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

                if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                    broContentLayers.add(broSvgaTextLayer);
                }
            }
        }
        return broContentLayers;
    }

    private List<Map<String, String>> getSvgaImageConfig(CpMultiPuzzleComponentAttr attr, MemberInfo memberInfo, MemberInfo anchorMemberInfo) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }
        Map<String, String> imageMap = attr.getSvgaImgLayers();
        for (String imageKey : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String image = imageMap.get(imageKey);
            String value = replaceImage(image, memberInfo, anchorMemberInfo);
            broImgLayer.put(imageKey, value);
            broImgLayers.add(broImgLayer);
        }
        return broImgLayers;
    }

    private String contextReplace(String context, UserBaseInfo userInfo, UserBaseInfo anchorUserInfo) {
        context = context.replace("{nick}", String.format("{%s:n}", userInfo.getUid()));
        context = context.replace("{anchorNick}", String.format("{%s:n}", anchorUserInfo.getUid()));
        return context;
    }

    private String replaceImage(String context, MemberInfo memberInfo, MemberInfo anchorMemberInfo) {
        return context.replace("{header}", Convert.toString(memberInfo.getHdLogo()))
                .replace("{anchorHeader}", Convert.toString(anchorMemberInfo.getHdLogo()));
    }

    @RequestMapping("/getCpList")
    public Response getCpList(HttpServletRequest request, HttpServletResponse response,
                              long actId, long cmptId) {
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            CpListRsp rsp = new CpListRsp();
            return Response.success(rsp);
        }
        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }
        CpMultiPuzzleComponentAttr attr = getComponentAttr(actId, cmptId);
        // 用户是主播 -》 查cp
        List<String> uids = getRankMembers(actId, attr.getCpContributeRankId(),
                attr.getPhaseId(), Convert.toString(uid), attr.getRankLimit());
        List<CpMember> cpMembers = new ArrayList<>();
        List<String> memberIds = new ArrayList<>();
        for (String member : uids) {
            CpMember cpMember = new CpMember();
            long toUid = Convert.toLong(member);
            String cpMemberStr = toUid + "|" + uid;
            MemInfo toMemInfo = new MemInfo();
            toMemInfo.setUid(toUid);
            MemInfo loginMemInfo = new MemInfo();
            loginMemInfo.setUid(uid);
            cpMember.setCpMember(cpMemberStr);
            cpMember.setAnchor(loginMemInfo);
            cpMember.setUser(toMemInfo);
            cpMembers.add(cpMember);
            memberIds.add(cpMemberStr);
        }

        // 用户是土豪 -》 查cp
        uids = getRankMembers(actId, attr.getCpAntiContributeRankId(),
                attr.getPhaseId(), Convert.toString(uid), attr.getRankLimit());
        for (String member : uids) {
            CpMember cpMember = new CpMember();
            long toUid = Convert.toLong(member);
            String cpMemberStr = uid + "|" + toUid;
            MemInfo toMemInfo = new MemInfo();
            toMemInfo.setUid(toUid);
            MemInfo loginMemInfo = new MemInfo();
            loginMemInfo.setUid(uid);
            cpMember.setCpMember(cpMemberStr);
            cpMember.setAnchor(toMemInfo);
            cpMember.setUser(loginMemInfo);
            cpMembers.add(cpMember);
            memberIds.add(cpMemberStr);
        }
        uids = new ArrayList<>();
        for (CpMember cpMember : cpMembers) {
            String cp = cpMember.getCpMember();
            Date now = commonService.getNow(attr.getActId());
            String date = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            long score = Convert.toLong(actRedisDao.zscore(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(PUZZLE_CP_DAILY_SCORE, date)),
                    cp));
            cpMember.setScore(score);
            uids.add(Convert.toString(cpMember.getUser().getUid()));
            uids.add(Convert.toString(cpMember.getAnchor().getUid()));
        }

        List<Long> uidLongs = uids.stream().map(Long::parseLong).collect(Collectors.toList());
        uidLongs.add(uid);
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uidLongs);
        for (CpMember cpMember : cpMembers) {
            MemInfo memInfo = cpMember.getAnchor();
            WebdbUserInfo anchorUserInfo = batched.getUserInfoMap().get(Convert.toString(memInfo.getUid()));
            memInfo.setAvatar(WebdbUtils.getLogo(anchorUserInfo));
            memInfo.setName(anchorUserInfo.getNick());
            MemInfo userMemInfo = cpMember.getUser();
            WebdbUserInfo userInfo = batched.getUserInfoMap().get(Convert.toString(userMemInfo.getUid()));
            userMemInfo.setAvatar(WebdbUtils.getLogo(userInfo));
            userMemInfo.setName(userInfo.getNick());
        }
        cpMembers.sort(new Comparator<CpMember>() {
            @Override
            public int compare(CpMember o1, CpMember o2) {
                return (int)(o2.getScore()-o1.getScore());
            }
        });
        CpListRsp rsp = new CpListRsp();
        rsp.setCpMembers(cpMembers);
        if(batched != null) {
            NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
            rsp.setNickExtUsers(nickExt.getUsers());
            rsp.setAvatar(WebdbUtils.getLogo(batched.getUserInfoMap().get(uid+"")));
        }
        rsp.setUid(uid);
        rsp.setSign(signedService.getSignedSidByBusiId(uid, attr.getBusiId()) > 0);
        return Response.success(rsp);
    }

    public List<String> getRankMembers(long actId, long rankId, long phaseId, String findSrcMember, long count) {
        Map<String, QueryRankingRequest> reqMap = Maps.newHashMap();
        QueryRankingRequest req = new QueryRankingRequest();
        req.setActId(actId);
        req.setRankingId(rankId);
        req.setPhaseId(phaseId);
        req.setFindSrcMember(findSrcMember);
        req.setRankingCount(count);
        reqMap.put(findSrcMember, req);
        List<String> uids = new ArrayList<>();
        Map<String, BatchRankingItem> conTop = hdztRankingThriftClient.queryBatchRanking(reqMap, null);
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(rank.getMember());
            }
        }
        return uids;
    }

    @RequestMapping("/getCpMissions")
    public Response getCpMissions(HttpServletRequest request, HttpServletResponse response, String cpMember, long actId, long cmptId) {
        CpMultiPuzzleComponentAttr attr = getComponentAttr(actId, cmptId);
        CpMission cpMission = new CpMission();
        Map<Integer, Map<Integer, Award>> rowAwards = new HashMap<>();
        Map<Integer, Map<Integer, Award>> colAwards = new HashMap<>();
        for (Integer no : attr.getRowPuzzles().keySet()) {
            Map<Integer, Award> rowAwardMap = new HashMap<>();
            List<CpMultiPuzzleComponentAttr.RowPuzzle> rowPuzzles = attr.getRowPuzzles().get(no);
            for (CpMultiPuzzleComponentAttr.RowPuzzle rowPuzzle : rowPuzzles) {
                Award award = new Award();
                award.setName(rowPuzzle.getName());
                award.setPic(rowPuzzle.getPic());
                rowAwardMap.put(rowPuzzle.getRow(), award);
            }
            rowAwards.put(no, rowAwardMap);
        }
        for (Integer no : attr.getColPuzzles().keySet()) {
            Map<Integer, Award> colAwardMap = new HashMap<>();
            List<CpMultiPuzzleComponentAttr.ColPuzzle> colPuzzles = attr.getColPuzzles().get(no);
            for (CpMultiPuzzleComponentAttr.ColPuzzle colPuzzle : colPuzzles) {
                Award award = new Award();
                award.setName(colPuzzle.getName());
                award.setPic(colPuzzle.getPic());
                colAwardMap.put(colPuzzle.getRow(), award);
            }
            colAwards.put(no, colAwardMap);
        }
        cpMission.setRowAwards(rowAwards);
        cpMission.setColAwards(colAwards);
        Map<Integer, List<RowCol>> rowColLight = new HashMap<>();
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            for (Integer no : attr.getRowPuzzles().keySet()) {
                rowColLight.put(no, new ArrayList<>());
            }
            List<Mission> missions = new ArrayList<>();
            int i = 0;
            for (Long missionVal : attr.getMissions()) {
                Mission mission = new Mission();
                mission.setTaskId(++i);
                mission.setScore(missionVal);
                missions.add(mission);
            }
            cpMission.setMissions(missions);
            cpMission.setRowColLight(rowColLight);
            return Response.success(cpMission);
        }
        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }
        UserBaseInfo loginUserInfo = null;
        if(StringUtil.isEmpty(cpMember)) {
            List<Long> uids = new ArrayList<>();
            uids.add(Convert.toLong(uid));
            Map<Long, UserBaseInfo> userMap = commonService.batchGetUserInfos(uids, false);
            loginUserInfo = userMap.get(uid);
        }

        //每日任务
        Date now = commonService.getNow(attr.getActId());
        String date = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        cpMember = cpMember == null ? "" : cpMember;
        long score = Convert.toLong(actRedisDao.zscore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_CP_DAILY_SCORE, date)),
                cpMember));
        cpMission.setScore(score);
        List<Mission> missions = new ArrayList<>();
        int i = 0;
        for (Long missionVal : attr.getMissions()) {
            Mission mission = new Mission();
            mission.setTaskId(++i);
            mission.setScore(missionVal);
            mission.setFinish(missionVal <= cpMission.getScore());
            missions.add(mission);
        }
        cpMission.setMissions(missions);

        //获取点亮图片
        Map<Integer, List<CpMultiPuzzleComponentAttr.RowCol>> cidRowColMap = attr.getCidRowColMap();
        Map<Object, Object> puzzleMap = actRedisDao.hGetAll(
                redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(PUZZLE_CP_BALANCE, cpMember)));
        Map<String, Long> puzzleBalaceMap = Maps.newHashMap();
        if (puzzleMap != null) {
            puzzleMap.forEach((k, v) -> puzzleBalaceMap.put(Convert.toString(k), Convert.toLong(v)));
        }
        for (Integer no : cidRowColMap.keySet()) {
            List<RowCol> rowCols = new ArrayList<>();
            List<CpMultiPuzzleComponentAttr.RowCol> rowColAttrs = cidRowColMap.get(no);
            Map<String, CpMultiPuzzleComponentAttr.RowCol> cidMap = rowColAttrs.stream()
                    .collect(Collectors.toMap(
                    CpMultiPuzzleComponentAttr.RowCol::getCid,  // 将 `Cid` 作为键
                    Function.identity()                          // 将 `RowCol` 对象本身作为值
            ));
            for (String cid : cidMap.keySet()) {
                if(puzzleBalaceMap.containsKey(cid)) {
                    CpMultiPuzzleComponentAttr.RowCol val = cidMap.get(cid);
                    rowCols.add(new RowCol(val.getRow(), val.getCol(), val.getPic()));
                }
            }
            rowColLight.put(no, rowCols);
        }
        cpMission.setRowColLight(rowColLight);

        if(loginUserInfo != null) {
            cpMission.setAvatar(loginUserInfo.getHdLogo());
            cpMission.setNick(loginUserInfo.getNick());
        }
        return Response.success(cpMission);
    }

    public void incrLogVal(CpMultiPuzzleComponentAttr attr, String key, int val) {
        actRedisDao.incrValue(redisConfigManager.getGroupCode(attr.getActId()), key, val);
    }

    @RequestMapping("/getBestCp")
    public Response<BestCpListRsp> getBestCp(Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx)  {
        CpMultiPuzzleComponentAttr attr = getComponentAttr(actId, cmptInx);
        List<BestCp> bestCps = new ArrayList<>();
        Date now = commonService.getNow(attr.getActId());
        for (Integer no : attr.getPuzzleCompleteText().keySet()) {
            String taskCompletePuzzleKey = String.format(TASK_COMPLETE_ZSET, no);
            Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, taskCompletePuzzleKey), now.getTime(), Long.MAX_VALUE);
            for (String raw : rawSet) {
                BestCp bestCp = JsonUtils.deserialize(raw, BestCp.class);
                bestCps.add(bestCp);
            }
        }
        bestCps.sort(new Comparator<BestCp>() {
            @Override
            public int compare(BestCp o1, BestCp o2) {
                return (int) (o2.getEndTime() - o1.getEndTime());
            }
        });
        List<Long> uids = new ArrayList<>();
        for (BestCp bestCp : bestCps) {
            uids.add(bestCp.getBabyUid());
            uids.add(bestCp.getUserUid());
        }
        BestCpListRsp rsp = new BestCpListRsp();
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(uids));
        if(batched == null){
            return Response.success(rsp);
        }
        Map<String, MemInfo> userInfoMap = batched.getUserInfoMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, i -> toMemInfo(i.getValue())));
        List<CpTaskMember> cpMembers = bestCps.stream()
                .map(bestCp -> {
                    CpTaskMember cpTaskMember = new CpTaskMember();
                    cpTaskMember.setAnchor(userInfoMap.get(String.valueOf(bestCp.getBabyUid())));
                    cpTaskMember.setUser(userInfoMap.get(String.valueOf(bestCp.getUserUid())));
                    cpTaskMember.setStartTime(bestCp.getStartTime());
                    cpTaskMember.setEndTime(bestCp.getEndTime());
                    cpTaskMember.setSid(bestCp.getSid());
                    cpTaskMember.setSsid(bestCp.getSsid());
                    cpTaskMember.setText(bestCp.getText());
                    return cpTaskMember;
                }).collect(Collectors.toList());

        NickExt nickExt = JsonUtils.deserialize(batched.getNickExt(), NickExt.class);
        rsp.setCpMembers(cpMembers);
        rsp.setNickExtUsers(nickExt.getUsers());
        rsp.setCurrentTime(now.getTime());
        log.info("getBestCp size:{}", cpMembers.size());
        return Response.success(rsp);
    }


    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, CpMultiPuzzleComponentAttr attr) {
        long uid = event.getUid(), actId = attr.getActId(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        boolean actEnterEvent = StringUtil.isNotBlank(extJson) && extJson.contains(String.valueOf(attr.getActId()));
        //协议只发1次，确保是本次活动触发的，才弹窗
        if (!actEnterEvent) {
            log.warn("not this actId UserEnterTemplateEvent uid:{}", uid);
            return;
        }
        log.info("onUserEnterTemplate uid:{},actId:{},extJson:{},sid:{},ssid:{}", uid, attr.getActId(), extJson, sid, ssid);
        Date now = commonService.getNow(attr.getActId());
        //发放进房口令，一个用户只发送一次
        String currentTing = sid + "_" + ssid;
        List<BestCp> bestCps = new ArrayList<>();
        for (Integer no : attr.getPuzzleCompleteText().keySet()) {
            String taskCompletePuzzleKey = String.format(TASK_COMPLETE_ZSET, no);
            Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, taskCompletePuzzleKey), now.getTime(), Long.MAX_VALUE);
            for (String raw : rawSet) {
                BestCp bestCp = JsonUtils.deserialize(raw, BestCp.class);
                bestCps.add(bestCp);
            }
        }
        String seq = "";
        for (BestCp bestCp : bestCps) {
            String sidSsid = bestCp.getSid() + "_"+ bestCp.getSsid();
            if(currentTing.equals(sidSsid)) {
                seq = bestCp.getSeq();
                String groupCode = getRedisGroupCode(attr.getActId());
                String key = makeKey(attr, String.format(ENTER_ROOM_BRO_SEQ, seq));
                boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
                if(first) {
                    JSONObject chatJson = new JSONObject();
                    chatJson.put("text", "公屏发送“佳偶天成，爱满人间”有机会获得婚礼祝福！");
                    chatJson.put("sid", sid);
                    chatJson.put("ssid", ssid);
                    commonBroadCastService.commonNoticeUnicast(attr.getActId(), CP_ROOM_TOAST, JsonUtil.toJson(chatJson) ,StringUtils.EMPTY
                            , event.getUid());
                    break;
                }
            }
        }
    }

    @HdzjEventHandler(value = ChannelChatTextInnerEvent.class, canRetry = true)
    public void onChannelTextChatInnerEvent(ChannelChatTextInnerEvent event, CpMultiPuzzleComponentAttr attr) {
        long sid = event.getTopsid();
        long ssid = event.getSubsid();
        long uid = event.getUid();
        String currentTing = sid + "_" + ssid;
        Date now = commonService.getNow(attr.getActId());

        //发放进房口令，一个用户只发送一次
        List<BestCp> bestCps = new ArrayList<>();
        for (Integer no : attr.getPuzzleCompleteText().keySet()) {
            String taskCompletePuzzleKey = String.format(TASK_COMPLETE_ZSET, no);
            Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, taskCompletePuzzleKey), now.getTime(), Long.MAX_VALUE);
            for (String raw : rawSet) {
                BestCp bestCp = JsonUtils.deserialize(raw, BestCp.class);
                bestCps.add(bestCp);
            }
        }
        String seq ="";
        for (BestCp bestCp : bestCps) {
            String sidSsid = bestCp.getSid() + "_"+ bestCp.getSsid();
            if(currentTing.equals(sidSsid)) {
                seq = bestCp.getSeq();
                if(textMatch(event.getChat())) {
                    String groupCode = redisConfigManager.getGroupCode(attr.getActId());
                    String key = makeKey(attr, String.format(SEND_CHAT_LOTTERY_SEQ, seq));
                    boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
                    if(first){
                        String time = DateUtil.format(now);
                        String lotterySeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_chat_" + uid));
                        BatchLotteryResult result = hdztAwardServiceClient.doLottery(time, attr.getBusiId(),
                                uid, attr.getChatTaskId(), 1, 0, lotterySeq);
                        log.info("onChannelTextChatInnerEvent uid:{} lottery result:{}", uid, JsonUtil.toJson(result));
                        Response<List<LotteryAward>> response = lotteryAward(result, attr);
                        if(response.success()) {
                            JSONObject extJson = new JSONObject();
                            extJson.put("hit", false);
                            if(!response.getData().isEmpty()) {
                                LotteryAward award = response.getData().get(0);
                                extJson.put("name", award.getName());
                                extJson.put("icon", award.getImg());
                                extJson.put("awardCount", 1);
                                extJson.put("hit", true);
                            }
                            commonBroadCastService.commonNoticeUnicast(attr.getActId(), CHANNEL_CHAT_AWARD, extJson.toJSONString()
                                    ,StringUtils.EMPTY , uid);
                            break;
                        }
                    } else {
                        log.warn("onChannelTextChatInnerEvent not first uid:{},sid:{},ssid:{}",uid,sid,ssid);
                    }
                } else {
                    log.info("onChannelTextChatInnerEvent not match uid:{},sid:{},ssid:{},chat:{}",uid,sid,ssid,event.getChat());
                }
            }
        }
    }

    public Response<List<LotteryAward>> lotteryAward(BatchLotteryResult batchLotteryResult, CpMultiPuzzleComponentAttr attr) {
        if (batchLotteryResult.getCode() != 0) {
            return Response.fail(3, batchLotteryResult.getReason());
        }
        List<LotteryAward> awardList = Lists.newArrayList();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Long, Long> recordIds = batchLotteryResult.getRecordPackages();
        Map<Long, Integer> pidCount = Maps.newHashMap();
        for (Long pid : recordIds.values()) {
            pidCount.merge(pid, 1, Integer::sum);
        }
        for (Map.Entry<Long, Integer> entry : pidCount.entrySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(entry.getKey());
            if (awardModelInfo != null) {
                if(awardModelInfo.getPackageName().contains("谢谢参与")) {
                    continue;
                }
                LotteryAward award = new LotteryAward();
                award.setName(awardModelInfo.getPackageName());
                award.setImg(awardModelInfo.getPackageImage());
                award.setNum(entry.getValue() == 1 ? 0 : entry.getValue());
                awardList.add(award);
            }
        }
        return Response.success(awardList);
    }

    public Map<Long, AwardModelInfo> packageInfoMap(CpMultiPuzzleComponentAttr attr) {
        try {
            Map<Long, AwardModelInfo> visit = hdztAwardServiceClient.queryAwardTasks(attr.getChatTaskId());
            return  visit == null ? Collections.emptyMap() : visit;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }

    public boolean textMatch(String chatText) {
        String cleanedInput = chatText.replaceAll("[\\p{P}\\p{S}\\s]+", "");
        return containsTargetSequence(cleanedInput, TARGET_STR);
    }

    public static boolean containsTargetSequence(String input, String target) {
        int targetIndex = 0;
        for (int i = 0; i < input.length(); i++) {
            if (input.charAt(i) == target.charAt(targetIndex)) {
                targetIndex++;
                if (targetIndex == target.length()) {
                    return true;
                }
            }
        }
        return false;
    }

    private MemInfo toMemInfo(WebdbUserInfo userBaseInfo) {
        MemInfo memInfo = new MemInfo();
        memInfo.setUid(Long.parseLong(userBaseInfo.getUid()));
        memInfo.setName(userBaseInfo.getNick());
        memInfo.setAvatar(WebdbUtils.getLogo(userBaseInfo));
        return memInfo;
    }

    @Data
    private static class CpListRsp {
        List<CpMember> cpMembers;

        Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private long uid;

        private String avatar;

        private boolean sign;
    }

    @Data
    private static class BestCpListRsp {
        private long currentTime;
        private List<CpTaskMember> cpMembers;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    private static class CpMember {
        private String cpMember;

        private MemInfo anchor;

        private MemInfo user;

        private long score;
    }

    @Data
    private static class MemInfo {
        private long uid;

        private String name;

        private String avatar;
    }

    @Data
    public static class RowCol {
        @ComponentAttrField(labelText = "行")
        private int row;
        @ComponentAttrField(labelText = "列")
        private int col;
        @ComponentAttrField(labelText = "图片")
        private String pic;
        public RowCol() {

        }
        public RowCol(int row, int col, String pic) {
            this.row = row;
            this.col = col;
            this.pic = pic;
        }
    }

    @Data
    public static class Award {
        @ComponentAttrField(labelText = "图片")
        private String pic;
        @ComponentAttrField(labelText = "名称")
        private String name;
    }

    @Data
    private static class CpMission {
        private List<Mission> missions;

        private Map<Integer, List<RowCol>> rowColLight;

        private Map<Integer, Map<Integer, Award>> rowAwards;

        private Map<Integer, Map<Integer, Award>> colAwards;

        private long score;

        private String nick;

        private String avatar;
    }

    @Data
    private static class Mission {
        private long taskId;

        private boolean finish;

        private long score;
    }

    @Data
    private static class BestCp {
        private long sid;
        private long ssid;
        private long babyUid;
        private long userUid;
        private long startTime;
        private long endTime;
        private String seq;
        private String text;
    }

    @Data
    public static class CpTaskMember {
        private MemInfo anchor;
        private MemInfo user;
        private long sid;
        private long ssid;
        private long startTime;
        private long endTime;
        private String text;
    }

    @Data
    public static class LotteryAward {
        private String name;

        private String img;

        private int num;
    }

    @Data
    public static class CpAchievementMember {
        private String cpMember;
        private MemInfo anchor;
        private MemInfo user;
        private long finishTime;
        private String text;
        private long sid;
        private long ssid;
    }

    @Data
    public static class CpAchievementRep {
        private List<CpAchievementMember> cpMembers;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

}
