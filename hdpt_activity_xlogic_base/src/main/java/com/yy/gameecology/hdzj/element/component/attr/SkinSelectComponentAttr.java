package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrCollector;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-12 19:50
 **/
@Data
public class SkinSelectComponentAttr  extends ComponentAttr {

    @ComponentAttrField(labelText = "皮肤配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = SkinConfig.class))
    protected List<SkinConfig> skinConfigs;

    @Getter
    @Setter
    public static class SkinConfig {

        @ComponentAttrField(labelText = "皮肤ID")
        protected int skinId;

        @ComponentAttrField(labelText = "皮肤名称")
        protected String skinName;

        @ComponentAttrField(labelText = "皮肤图片地址", propType = ComponentAttrCollector.PropType.IMAGE)
        protected String skinIcon;
    }
}
