package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeStart;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.CulClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.LoginService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.TeamDTO;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AnchorTeamComponentAttr;
import com.yy.gameecology.hdzj.element.redis.MatchResultNotifyComponent;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.QueryRankingByScoreResponse;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingByScoreInfo;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.HtmlUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 核心业务逻辑：
 * 多个主播组成一个战队,以战队进行累榜,进行赛程比拼
 * <p>
 * 实现逻辑：
 * 1. 配置一个基础榜单,对主播的收礼进行累榜
 * 2. 当前组件监听基础榜单的分数变化事件,查找事件对应主播映射到的战队信息,构造榜单事件,发往中台,进行战队榜累榜
 * 3. 当前组件监听战队榜单变化事件,通知挂件刷新
 * <p>
 * <p>
 * 边界情况：
 * 1. 晋级结算时,存在卡点,数据还没上报完,就进行了晋级,导致数据异常
 * ==> 监听阶段开始时间,延迟通知中台结算
 * ==> 通知失败了？重试几次
 * ==> 兜底策略:
 *
 * <AUTHOR>
 * @since 2022/9/2 17:01
 **/
@Component
@RestController
@RequestMapping("cmp/anchorTeam/")
public class AnchorTeamComponent extends BaseActComponent<AnchorTeamComponentAttr> {
    @Autowired
    private BroActLayerService broActLayerService;
    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;
    @Autowired
    private CulClient culClient;
    @Autowired
    private MatchResultNotifyComponent matchResultNotifyComponent;
    @Autowired
    private LoginService loginService;

    @Override
    public Long getComponentId() {
        return ComponentId.ANCHOR_TEAM;
    }


    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = true)
    public void onPromotTimeEnd(PromotTimeEnd event, AnchorTeamComponentAttr attr) {
        long rankId = event.getRankId();
        long teamRankId = attr.getTeamRankId();
        long phaseId = event.getPhaseId();
        if (rankId != teamRankId || !attr.getAdjustPKPhaseId().contains(phaseId)) {
            return;
        }
        setTeamPkGroup(event.getSeq(), attr, rankId, phaseId);
    }

    //战队动态pk调整,只限定相同公会的不pk
    public void setTeamPkGroup(String seq, AnchorTeamComponentAttr attr, long rankId, long phaseId) {
        QueryRankingByScoreResponse response = hdztRankService.queryRankingByScore(attr.getActId(), rankId, phaseId, null, HdztRankType.PROMOT);
        if (response == null || response.code != 0) {
            throw new SuperException("获取晋级名单失败", SuperException.E_UNKNOWN);
        }
        List<RankingByScoreInfo> rankingByScoreInfos = response.rankingByScoreInfos;
        List<TeamDTO> allTeam = attr.getAllTeam();
        Map<String, Long> teamId2Sid = Maps.newHashMap();
        for (TeamDTO teamDTO : allTeam) {
            teamId2Sid.put(String.valueOf(teamDTO.getTeamId()), teamDTO.getSid());
        }
        List<String> members = rankingByScoreInfos.stream().map(RankingByScoreInfo::getMemberId).collect(Collectors.toList());

        int min = Integer.MAX_VALUE;
        int maxTime = 10000;
        int halfCount = members.size() / 2;
        List<List<String>> resultList = new ArrayList<>();
        for (int i = 0; i < maxTime; i++) {
            Collections.shuffle(members);
            List<List<String>> memberInfos = new ArrayList<>();
            int score = 0;
            for (int index = 0; index < halfCount; index++) {
                List<String> pkGroup = new ArrayList<>();
                String pk1 = members.get(index);
                pkGroup.add(pk1);
                String pk2 = members.get(halfCount + index);
                pkGroup.add(pk2);
                memberInfos.add(pkGroup);
                // 优先不匹配同公会
                if (teamId2Sid.get(pk1).equals(teamId2Sid.get(pk2))) {
                    score += 1;
                }
            }

            if (score == 0) {
                min = 0;
                resultList = memberInfos;
                break;
            }

            if (min > score) {
                resultList = memberInfos;
                min = score;
            }
        }

        StringBuilder sb = new StringBuilder();
        for (List<String> memberInfos : resultList) {
            for (String member : memberInfos) {
                sb.append(member).append(",");
            }
        }
        seq = "pk-" + seq;
        String pkMembers = sb.substring(0, sb.length() - 1);
        final boolean rs = hdztRankingThriftClient.setDynamicPkMembers(attr.getActId(), rankId, phaseId, pkMembers, attr.getOpUid(), seq, 2);
        if (rs) {
            log.info("hdztRankingThriftClient.setDynamicPkMembers rank:{}, phaseId:{} done, seq:{}", seq, rankId, phaseId);
        } else {
            log.error("hdztRankingThriftClient.setDynamicPkMembers rank:{}, phaseId:{}  error:{}", seq, rankId, phaseId);
        }
        log.info("setupPKInfo done, seq:{}, min:{}, resultList:{}", seq, min, JSON.toJSONString(resultList));
    }

    /**
     * 监听榜单分数变化
     * 1. 如果是基础榜单,则进行战队榜单累榜
     * 2. 如果是战队榜单,则进行挂件更新广播
     *
     * @param event 榜单分数变化事件
     * @param attr  组件属性
     **/
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, AnchorTeamComponentAttr attr) {
        long rankId = event.getRankId();
        log.info("onRankingScoreChanged event={}", JSON.toJSONString(event));
        if (rankId == attr.getBasicRankId()) {
            updateTeamRank(event, attr);
            return;
        }

        if (rankId == attr.getTeamRankId()) {
            updateLayer(event, attr);
            return;
        }
    }

    /**
     * 监听阶段开始事件,延迟通知中台进行晋级结算
     *
     * @param event 阶段开始事件
     * @param attr  组件属性
     **/
    @HdzjEventHandler(value = PhaseTimeStart.class, canRetry = true)
    public void onPhaseTimeStart(PhaseTimeStart event, AnchorTeamComponentAttr attr) {
        long rankId = event.getRankId();
        if (rankId != attr.getTeamRankId()) {
            return;
        }
        log.info("onPhaseTimeStart event={}", JSON.toJSONString(event));
        long phaseId = event.getPhaseId();
        if (!attr.getPromotPhaseIds().contains(phaseId)) {
            return;
        }

        // 60秒后执行
        Const.EXECUTOR_DELAY_GENERAL.schedule(() -> {
            hdztRankingThriftClient.notifySettleWithRetry(event.getActId(), rankId, 1, attr.getPromotControlKey(), 10);
        }, 60, TimeUnit.SECONDS);
    }

    /**
     * 监听阶段结束,进行战队赛果广播
     **/
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void teamResultBro(PhaseTimeEnd event, AnchorTeamComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (rankId != attr.getTeamRankId() || phaseId != attr.getLastPhaseId()) {
            return;
        }

        List<String> titles = attr.getTitles();
        if (CollectionUtils.isEmpty(titles)) {
            titles = Arrays.asList("冠军", "亚军", "季军");
        }
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), rankId, phaseId, "", 3, Maps.newHashMap());
        for (Rank rank : ranks) {
            try {
                int index = rank.getRank();
                int delay = (index - 1) * 5 * 1000;
                TeamDTO teamDTO = queryTeam(attr, rank.getMember(), true);
                if (teamDTO == null) {
                    log.error("not found team info {}", JSON.toJSONString(rank));
                    continue;
                }
                String content = String.format(attr.getResultMsgTemplate(), HtmlUtils.htmlEscape(teamDTO.getTeamName()), titles.get(index - 1));

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("actId", attr.getActId());
                jsonObject.put("content", content);
                jsonObject.put("moduleType", 0);
                jsonObject.put("matchType", 1);
                jsonObject.put("skipFlag", 0);
                jsonObject.put("extjson", "");
                GameecologyActivity.GameEcologyMsg message = matchResultNotifyComponent.createGameEcologyMsg(
                        GameecologyActivity.PacketType.kAct202010_MatchResultBroadcast, jsonObject.toJSONString());

                matchResultNotifyComponent.broadcast(attr.getActId(), Arrays.asList(attr.getBusiId()), message, delay, null);
            } catch (Exception ex) {
                log.error("teamResultBro error,rank={}", JSON.toJSONString(rank), ex);
            }
        }
    }


    /**
     * 战队榜数据上报
     **/
    private void updateTeamRank(RankingScoreChanged event, AnchorTeamComponentAttr attr) {
        TeamDTO teamDTO = queryTeam(attr, event.getMember(), false);
        if (teamDTO == null) {
            log.info("{} not in team", event.getMember());
            return;
        }

        String seq = "Team:" + event.getSeq();
        Map<Long, String> actors = event.getActors();
        actors.put(attr.getTeamMemberActorId(), event.getMember());
        actors.put(attr.getTeamActorId(), teamDTO.getTeamId() + "");

        long time = DateUtil.getDate(event.getOccurTime()).getTime();

        UpdateRankingRequest request = new UpdateRankingRequest();
        request.setBusiId(attr.getBusiId());
        request.setActId(attr.getActId());
        request.setSeq(seq);
        request.setActors(actors);
        request.setItemId(attr.getTeamRankItem());
        request.setCount(event.getItemCount());
        request.setScore(event.getItemScore());
        request.setTimestamp(time);
        request.setExtData(Maps.newHashMap());
        hdztRankingThriftClient.updateRankingWithRetry(request, 3);
    }

    /**
     * 更新挂件
     **/
    private void updateLayer(RankingScoreChanged event, AnchorTeamComponentAttr attr) {
        TeamDTO teamDTO = queryTeam(attr, event.getMember(), true);
        if (teamDTO == null) {
            // 理论上是不会进到这里的
            log.error("{}查询不到战队信息,请核对", event.getMember());
            return;
        }

        List<Long> memberIds = getMemberIds(teamDTO.getMemberIdList());
        for (Long memberId : memberIds) {
            ChannelInfoVo channelInfoVo = culClient.queryUserChannel(memberId);
            if (channelInfoVo != null) {
                broActLayerService.invokeRefresh(event.getActId(), channelInfoVo.getSid(), channelInfoVo.getSsid());
            }
        }
    }

    public List<Long> getMemberIds(String memberIdList) {
        if (StringUtils.isEmpty(memberIdList)) {
            return new ArrayList<>();
        }

        return Arrays.stream(memberIdList.split(",")).map(Long::valueOf).collect(Collectors.toList());
    }

    private TeamDTO queryTeam(AnchorTeamComponentAttr attr, String memberId, boolean isTeamId) {
        long memberIdLong = Convert.toLong(memberId, 0);

        List<TeamDTO> teams = attr.getAllTeam().stream().filter(
                        team -> (getMemberIds(team.getMemberIdList()).contains(memberIdLong) && !isTeamId) || (isTeamId && team.getTeamId() == memberIdLong))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(teams)) {
            return null;
        }

        if (teams.size() > 1) {
            throw new SuperException(memberId + "存在多个战队", 400);
        }

        return teams.get(0);
    }

    /**
     * 获取所有的战队
     *
     * @param actId          活动id
     * @param componentIndex 组件下标
     **/
    public List<TeamDTO> listAllTeam(long actId, long componentIndex) {
        AnchorTeamComponentAttr attr = getComponentAttr(actId, componentIndex);
        if (attr == null) {
            throw new SuperException("没找到属性配置", 404);
        }

        return attr.getAllTeam();
    }

    /**
     * 根据战队id查询战队信息
     *
     * @param actId             活动id
     * @param componentUseIndex 组件下标
     * @param teamId            战队id
     **/
    public TeamDTO queryTeam(long actId, long componentUseIndex, String teamId) {
        AnchorTeamComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            throw new SuperException("没找到属性配置", 404);
        }

        return queryTeam(attr, teamId, true);
    }

    /**
     * 手动触发晋级结算
     * 用于兜底晋级处理
     **/
    @GetMapping("qwerpp")
    public Response<String> manualNotifySettle(HttpServletRequest req, HttpServletResponse resp, long actId, long componentUseIndex) {
        long loginUid = loginService.getLoginYYUid(req, resp);
        final long sepUid = 50048479;
        if (loginUid != sepUid) {
            return Response.ok("权限不足");
        }

        AnchorTeamComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            return Response.ok("没有找到属性配置");
        }
        hdztRankingThriftClient.notifySettleWithRetry(actId, attr.getTeamRankId(), 1, attr.getPromotControlKey(), 3);

        return Response.ok();
    }


    @GetMapping("testTeamResultBro")
    public Response<String> testTeamResultBro(long actId, long componentUseIndex) {
        if (SysEvHelper.isDeploy()) {
            return Response.ok("线上环境不允许触发");
        }

        AnchorTeamComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        if (attr == null) {
            return Response.ok("没有找到属性配置");
        }
        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setRankId(attr.getTeamRankId());
        event.setPhaseId(attr.getLastPhaseId());
        teamResultBro(event, attr);

        return Response.ok();
    }
}
