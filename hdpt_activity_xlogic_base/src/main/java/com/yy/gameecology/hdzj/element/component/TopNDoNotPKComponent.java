package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.client.thrift.TurnoverFamilyThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.TopNDoNotPKComponentAttr;
import com.yy.thrift.hdztranking.*;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * PK调整组件：策略的得分越高,优先级越高
 * <p>
 * 支持3种策略
 * 1. topN不优先匹配
 * 2. 同工会不优先匹配
 * 3. 同ow不优先匹配
 * <p>
 * 后两种策略只有主播角色才支持
 *
 * <AUTHOR>
 * @date 2022.10.18 16:54
 */
@Component
public class TopNDoNotPKComponent extends BaseActComponent<TopNDoNotPKComponentAttr> {

    @Autowired
    private SignedService signedService;

    @Autowired
    private TurnoverFamilyThriftClient turnoverFamilyThriftClient;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;


    @Override
    public Long getComponentId() {
        return ComponentId.TOP_N_DO_NOT_PK;
    }


    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = true)
    public void onPromotTimeEnd(PromotTimeEnd event, TopNDoNotPKComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (rankId != attr.getRankId() || phaseId != attr.getPhaseId()) {
            return;
        }
        Map<Long, Map<Long, Integer>> srcTopN = attr.getSrcTopN();

        List<String> topNMembers = new ArrayList<>();

        for (Map.Entry<Long, Map<Long, Integer>> srcEntry : srcTopN.entrySet()) {
            Long srcRankId = srcEntry.getKey();
            for (Map.Entry<Long, Integer> entry : srcEntry.getValue().entrySet()) {
                List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), srcRankId, entry.getKey(), "", entry.getValue(), Maps.newHashMap());
                topNMembers.addAll(ranks.stream().map(Rank::getMember).collect(Collectors.toList()));
            }
        }

        QueryRankingByScoreResponse response = hdztRankService.queryRankingByScore(attr.getActId(), rankId, phaseId, null, HdztRankType.PROMOT);
        if (response == null || response.code != 0) {
            throw new SuperException("获取晋级名单失败", SuperException.E_UNKNOWN);
        }

        List<RankingByScoreInfo> rankingByScoreInfos = response.rankingByScoreInfos;
        List<String> memberIds = rankingByScoreInfos.stream().map(RankingByScoreInfo::getMemberId).collect(Collectors.toList());
        Map<String, Long> signGuild = Maps.newHashMap();
        Map<String, Long> signFamily = Maps.newHashMap();
        Map<Integer, Long> roomFamily = Maps.newHashMap();
        //如果是主播,次要条件同公会不匹配
        if (attr.getRoleType() == RoleType.ANCHOR.getValue()) {
            List<Long> uids = memberIds.stream().map(Convert::toLong).collect(Collectors.toList());
            Map<Long, ChannelInfoVo> signedInfoMap = signedService.getSignedInfoMapCache(uids, Template.getTemplateByBusi(attr.getBusiId()).getCode());
            for (Map.Entry<Long, ChannelInfoVo> entry : signedInfoMap.entrySet()) {
                signGuild.put(String.valueOf(entry.getKey()), entry.getValue().getSid());
            }
            if(attr.getBusiId() == BusiId.SKILL_CARD.getValue()) {
                Map<Long, Long> signFamilyMap = turnoverFamilyThriftClient.batchQueryContractFamilyIds(uids);
                for (Map.Entry<Long, Long> entry : signFamilyMap.entrySet()) {
                    signFamily.put(String.valueOf(entry.getKey()), entry.getValue());
                }
            }
        }
        if (attr.getRoleType() == RoleType.ROOM.getValue()) {
            List<Integer> roomIds = memberIds.stream().map(Convert::toInt).collect(Collectors.toList());
            if(attr.getBusiId() == BusiId.SKILL_CARD.getValue() && !roomIds.isEmpty()) {
                Map<Integer, RoomInfo> roomInfoMap = zhuiwanRoomInfoClient.roomInfoMapByRoomId(roomIds);
                for (Map.Entry<Integer, RoomInfo> roomInfoEntry : roomInfoMap.entrySet()) {
                    roomFamily.put(roomInfoEntry.getKey(), roomInfoEntry.getValue().getFamilyId());
                }
            }
        }
        setupPKInfo(event.getSeq(), memberIds, topNMembers, signGuild, signFamily, roomFamily, attr);
    }

    private void setupPKInfo(String seq, List<String> memberIds, List<String> topNMember,
                             Map<String, Long> signGuild, Map<String, Long> signFamily, Map<Integer, Long> roomFamily, TopNDoNotPKComponentAttr attr) {
        List<List<String>> resultList = new ArrayList<>();
        int min = Integer.MAX_VALUE;
        final int i100000 = 100000;
        for (int i = 0; i < i100000; i++) {
            List<List<String>> groupList = new ArrayList<>();
            Collections.shuffle(memberIds);
            int score = 0;
            int size = memberIds.size();
            final int half = size / 2;
            for (int j = 0; j < half; j++) {
                String pk1 = memberIds.get(j);
                String pk2 = memberIds.get(size - 1 - j);
                if (topNMember.contains(pk1) && topNMember.contains(pk2)) {
                    score += attr.getTopNScore();
                }
                Long pk1Sign = signGuild.get(pk1);
                Long pk2Sign = signGuild.get(pk2);
                if (!signGuild.isEmpty() && pk1Sign != null && pk2Sign != null) {
                    if (pk1Sign.equals(pk2Sign)) {
                        score += attr.getSameSignScore();
                    } else {
                        if (MapUtils.isNotEmpty(attr.getNewWhiteList())) {
                            for (Map.Entry<Long, List<Long>> entry : attr.getNewWhiteList().entrySet()) {
                                if (entry.getValue().contains(pk1Sign) && entry.getValue().contains(pk2Sign)) {
                                    score += attr.getSameOwScore();
                                    break;
                                }
                            }
                        }
                    }
                }
                if(!CollectionUtils.isEmpty(attr.getAnchorWhiteList()) && attr.getAnchorWhiteList().contains(Long.parseLong(pk1))
                        &&  attr.getAnchorWhiteList().contains(Long.parseLong(pk2))) {
                    score += attr.getAnchorWhiteListScore();
                }
                if(!signFamily.isEmpty() && pk1Sign != null && pk2Sign != null) {
                    if (pk1Sign.equals(pk2Sign)) {
                        score += attr.getSameFamilyScore();
                    }
                }
                if(!roomFamily.isEmpty() && roomFamily.containsKey(Convert.toInt(pk1))
                        && roomFamily.containsKey(Convert.toInt(pk2))) {
                    long familyId1 = roomFamily.get(Convert.toInt(pk1));
                    long familyId2 = roomFamily.get(Convert.toInt(pk2));
                    score = score + (int)(familyId1 == familyId2 ? attr.getSameFamilyScore() : 0);
                }
                groupList.add(Lists.newArrayList(pk1, pk2));
            }
            if (score == 0) {
                min = 0;
                resultList = groupList;
                break;
            }

            if (min > score) {
                resultList = groupList;
                min = score;
            }
        }

        StringBuilder sb = new StringBuilder();
        for (List<String> memberInfos : resultList) {
            for (String member : memberInfos) {
                sb.append(member).append(",");
            }
        }
        seq = "pk-" + seq;
        String pkMembers = sb.substring(0, sb.length() - 1);
        if (hdztRankingThriftClient.setDynamicPkMembers(attr.getActId(), attr.getRankId(), attr.getPhaseId(), pkMembers, attr.getOpUid(), seq, 1)) {
            log.info("hdztRankingThriftClient.setDynamicPkMembers rank:{}, phaseId:{} done, seq:{}", seq, attr.getRankId(), attr.getPhaseId());
        } else {
            log.error("hdztRankingThriftClient.setDynamicPkMembers rank:{}, phaseId:{}  error:{}", seq, attr.getRankId(), attr.getPhaseId());
        }
        log.info("setupPKInfo done, seq:{}, min:{}, resultList:{}", seq, min, JSON.toJSONString(resultList));
    }
}
