package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.event.SkillCardSeatChgKafkaEvent;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.common.consts.AppIconItemType;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.bean.WelfareNoticeInfo;
import com.yy.gameecology.hdzj.bean.appitem.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AnchorWelfareTaskEntryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.WelfareTaskEntryItem;
import com.yy.gameecology.hdzj.element.redis.AnchorWelfareTaskComponet;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-05-22 18:08
 **/
@RestController
@RequestMapping("/cmpt/anchorWelfareTaskentry")
@Component
public class AnchorWelfareTaskEntryComponent extends BaseActComponent<AnchorWelfareTaskEntryComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 入口请求类型 1-pc上座
     */
    private static final int SOURCE_PC_ON = 1;

    /**
     * 入口请求类型 2-pc下座
     */
    private static final int SOURCE_PC_OFF = 2;

    private static final String PC_REQ_OP_TYPE = "getPop";


    /**
     * 入口请求类型 3-app气泡
     */
    private static final int SOURCE_APP_POP = 3;

    private static final int SKILL_CARD_BUSINESS = 34;


    private static final String IOS_BANNER_IMG_OLD = "https://hd-static.yystatic.com/****************.png";
    private static final String IOS_BANNER_IMG_NEW = "https://hd-static.yystatic.com/24524770850589173.png";

    @Autowired
    private CommonService commonService;

    @Autowired
    private AnchorWelfareTaskComponet anchorWelfareTaskComponet;

    @Autowired
    private CommonBroadCastService commonBroadCastService;


    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Autowired
    private SignedService signedService;

    @Override
    public Long getComponentId() {
        return ComponentId.ANCHOR_WELFARE_TASK_ENTRY;
    }

    /**
     * APP气泡入口控制
     */
    @RequestMapping("/queryLayerStatus")
    public Response<Map<String, Object>> queryLayerStatus(Long actId, Long uid, String seq, Long sid, Long ssid, String platform, String hostName) {
        Map<String, Object> result = Maps.newHashMap();
        AnchorWelfareTaskEntryComponentAttr attr = getUniqueComponentAttr(actId);

        //判断活动资格
        WelfareNoticeInfo welfareNoticeInfo = null;
        if(actId==null || uid==null){
            result.put("showStatus", 0);
            log.info("queryLayerStatus actId or uid is null,actId:{},seq:{},uid:{},platform:{},hostName:{} not showStatus", actId, seq, uid, platform, hostName);
            return Response.success(result);
        }
        boolean test = false;
        //客户端联调用
        if (attr.getTestUids().contains(uid)) {
            test = true;
            welfareNoticeInfo = new WelfareNoticeInfo();
            welfareNoticeInfo.setText("测试UID");
            welfareNoticeInfo.setQualification(true);
        } else {
            welfareNoticeInfo = queryWelfareNoticeInfo(actId, SOURCE_APP_POP, uid);
        }

        if (welfareNoticeInfo == null || !welfareNoticeInfo.isQualification()) {
            result.put("showStatus", 0);
            log.info("queryLayerStatus actId:{},seq:{},uid:{},platform:{},hostName:{} not showStatus", actId, seq, uid, platform, hostName);
            return Response.success(result);
        }

        List<AppIconItemVo> items = Lists.newArrayList();
        for (WelfareTaskEntryItem configItem : attr.getItemList()) {
            AppIconItemVo iconItem = buildIconItem(actId, configItem, platform, hostName);
            if (iconItem == null) {
                continue;
            }
            if (configItem.getType() == AppIconItemType.WEBVIEW) {
                setIconWeb(configItem, iconItem);
            } else if (configItem.getType() == AppIconItemType.NATIVE && StringUtil.isNotBlank(welfareNoticeInfo.getText())) {
                setIconNative(test, actId, uid, configItem, welfareNoticeInfo, iconItem);
            } else if (configItem.getType() == AppIconItemType.BANNER) {

                setIconBanner(configItem, iconItem,welfareNoticeInfo);
            }
            if (iconItem.getNativeContent() == null && iconItem.getWeb() == null && iconItem.getBanner() == null) {
                continue;
            }
            items.add(iconItem);
        }


        AppIconVo appIconVo = new AppIconVo();
        appIconVo.setData(items);

        String payload = JSON.toJSONString(appIconVo);
        result.put("payload", payload);
        result.put("showStatus", 1);
        result.put("time", System.currentTimeMillis());
        result.put("grey", commonService.isGrey(actId));
        log.info("queryLayerStatus actId:{},seq:{},uid:{},platform:{},hostName:{},payload:{}", actId, seq, uid, platform, hostName, payload);
        return Response.success(result);
    }

    private AppIconItemVo buildIconItem(long actId, WelfareTaskEntryItem configItem, String platform, String hostName) {
        AppIconItemVo iconItem = new AppIconItemVo();
        iconItem.setType(configItem.getType());
        iconItem.setId(configItem.getId());
        iconItem.setWidth(configItem.getWidth());
        iconItem.setHeight(configItem.getHeight());
        iconItem.setDuration(configItem.getDuration());
        iconItem.setShowRed(configItem.isShowRed());
        iconItem.setPosition(configItem.getPosition());
        //显示去重控制
        String timeKey = StringUtil.isNotBlank(configItem.getDupTimeKey())
                ? DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2)
                : "-1";
        iconItem.setDupKey(configItem.getId() + "_" + timeKey);

        if (StringUtil.isNotBlank(configItem.getHostName())) {
            if (Arrays.stream(configItem.getHostName().split(",")).noneMatch(p -> p.equals(hostName))) {
                log.info("not match host,configItem:{},hostName:{}", configItem.getHostName(), hostName);
                return null;
            }
        }

        if (StringUtil.isNotBlank(configItem.getPlatform())) {
            if (Arrays.stream(configItem.getPlatform().split(",")).noneMatch(p -> p.equals(platform))) {
                log.info("not match platform,configItem:{},platform:{}", configItem.getPlatform(), platform);
                return null;
            }
        }

        return iconItem;
    }

    /**
     * 原生气泡红点入口
     */
    private void setIconNative(boolean test, long actId, long uid
            , WelfareTaskEntryItem configItem, WelfareNoticeInfo welfareNoticeInfo
            , AppIconItemVo setIconItem) {

        if(!anchorWelfareTaskComponet.taskAwardPoolValide(actId)) {
            log.info("setIconNative award pool not enough,uid:{},actId:{}", uid, actId);
            return;
        }

        if (anchorWelfareTaskComponet.pcInsertActivity(actId, uid)) {
            IconNative iconNative = new IconNative();
            iconNative.setText(test ? configItem.getNativeText() : welfareNoticeInfo.getText());
            iconNative.setBgColor(configItem.getNativeBgColor());
            iconNative.setTextColor(configItem.getNativeTextColor());
            iconNative.setTextSize(configItem.getNativeTextSize());
            if (StringUtil.isNotBlank(welfareNoticeInfo.getDupKey())) {
                setIconItem.setDupKey(configItem.getId() + "_" + welfareNoticeInfo.getDupKey());
            }
            if (StringUtil.isNotBlank(configItem.getWelfareNoticeInfoBusiness())) {
                String userBusiness = Convert.toString(welfareNoticeInfo.getBusiness());
                if (Arrays.stream(configItem.getWelfareNoticeInfoBusiness().split(",")).noneMatch(p -> p.equals(userBusiness))) {
                    log.info("not match host,configItem:{},hostName:{}", configItem.getWelfareNoticeInfoBusiness(), userBusiness);
                    return;
                }
            }

            setIconItem.setNativeContent(iconNative);
        }
    }

    /**
     * IOS banner入口
     */
    private void setIconBanner(WelfareTaskEntryItem configItem, AppIconItemVo setIconItem,WelfareNoticeInfo welfareNoticeInfo) {
        IconBanner iconBanner = new IconBanner();
        if(welfareNoticeInfo.isNew()){
            iconBanner.setImg(IOS_BANNER_IMG_NEW);
        }else{
            iconBanner.setImg(IOS_BANNER_IMG_OLD);
        }

        iconBanner.setLink(configItem.getBannerLink());
        setIconItem.setBanner(iconBanner);
    }

    /**
     * web view 气泡，预留
     */
    private void setIconWeb(WelfareTaskEntryItem configItem, AppIconItemVo setIconItem) {
        IconWeb iconWeb = new IconWeb();
        iconWeb.setSrc(configItem.getWebSrc());

        setIconItem.setWeb(iconWeb);
    }

    /**
     * APP底部红点
     */
    @RequestMapping("/queryRedPoint")
    public Response<Map<String, Object>> queryRedPoint(Long actId, Long uid, String hdid, String app) {
        Map<String, Object> result = Maps.newHashMap();
        WelfareNoticeInfo welfareNoticeInfo = queryWelfareNoticeInfo(actId, SOURCE_APP_POP, uid);
        boolean show = welfareNoticeInfo != null && StringUtil.isNotBlank(welfareNoticeInfo.getText());
        result.put("show", show);
        log.info("queryRedPoint actId:{},uid:{},show:{}", actId, uid, show);
        return Response.success(result);

    }

    /**
     * 安卓 我的收益h5 显示活动入口
     *
     * @param business 语音房我的收益（黄钻） business== 34    交友黄水晶收益(黄水晶)  business== 2
     */
    @RequestMapping("/showBanner")
    public Response<Map<String, Object>> showBanner(HttpServletRequest req, HttpServletResponse resp, Long actId, Long sid, Long ssid, Integer business) {
        long uid = getLoginYYUid(req, resp);
        boolean show = false;
        boolean isNew = false;
        if (uid > 0) {
            WelfareNoticeInfo welfareNoticeInfo = queryWelfareNoticeInfo(actId, SOURCE_APP_POP, uid);
            show = welfareNoticeInfo != null && welfareNoticeInfo.isQualification() && welfareNoticeInfo.getBusiness().equals(business);
            isNew = welfareNoticeInfo != null && welfareNoticeInfo.isNew();
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("show", show);
        result.put("isNew", isNew);
        log.info("showBanner actId:{},uid:{},show:{},isNew:{},business:{}", actId, uid, show, isNew, business);
        return Response.success(result);
    }

    /**
     * 语音房上、下座气泡提醒
     */
    @HdzjEventHandler(value = SkillCardSeatChgKafkaEvent.class, canRetry = false)
    public void onSkillCardSeatChgEvent(SkillCardSeatChgKafkaEvent event, AnchorWelfareTaskEntryComponentAttr attr) {
        //交友的有时序问题，客户端自己调用commonOperatePbRequest 来拉取，这里值处理语音房
        int type = SkillCardSeatChgKafkaEvent.ChangeType.ON_SEAT.equals(event.getChangeType()) ? SOURCE_PC_ON : SOURCE_PC_OFF;
        log.info("onSkillCardSeatChgEvent,actid:{},uid:{},type:{}", attr.getActId(), event.getUid(), type);

        //---需要在签约频道内上座才显示（6位房间号）
        long family = signedService.getSignedSidByBusiId(event.getUid(), BusiId.SKILL_CARD.getValue());

        RoomInfo roomInfo = zhuiwanRoomInfoClient.roomInfoBySsid(event.getSsid());
        if (roomInfo == null || roomInfo.getFamilyId() != family) {
            log.info("onSkillCardSeatChgEvent sign room,actId:{},uid:{},roomInfo:{},family:{}", attr.getActId(), event.getUid(), roomInfo, family);
            return;
        }

        try {
            Thread.sleep(1000L);
        } catch (InterruptedException e) {
            log.error("onSkillCardSeatChgEvent send unicast interrupted:", e);
        }
        unicastPopText(attr.getActId(), SKILL_CARD_BUSINESS, type, event.getUid());
    }


    /**
     * 交友 pc客户端主动拉取气泡、点击图标主动拉取数据
     */
    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        log.info("commonOperatePbRequest:{}", JSON.toJSONString(request));
        if (!PC_REQ_OP_TYPE.equals(request.getOpType())) {
            return null;
        }
        int business = Convert.toInt(request.getOpId(), 0);
        int source = Convert.toInt(request.getOpTarget(), 0);
        unicastPopText(request.getActId(), business, source, request.getOpUid());
        return new CommonPBOperateResp(1, "response ok", "success");
    }

    public void unicastPopText(long actId, int business, int noticeSource, long uid) {
        Map<String, Object> noticeValue = Maps.newHashMap();
        noticeValue.put("source", noticeSource);
        WelfareNoticeInfo welfareNoticeInfo = queryWelfareNoticeInfo(actId, noticeSource, uid);
        if (welfareNoticeInfo == null || !welfareNoticeInfo.isQualification()) {
            noticeValue.put("hide", true);
            commonBroadCastService.commonNoticeUnicast(actId, "5093_pop", JSON.toJSONString(noticeValue), "", uid);
            return;
        }
        if (business > 0 && !welfareNoticeInfo.getBusiness().equals(business)) {
            noticeValue.put("hide", true);
            commonBroadCastService.commonNoticeUnicast(actId, "5093_pop", JSON.toJSONString(noticeValue), "", uid);
            log.info("unicastPopText return,uid:{},business:{},welfareNoticeInfo business:{}", uid, business, welfareNoticeInfo.getBusiness());
            return;
        }


        noticeValue.put("rollText", welfareNoticeInfo.getRollText());
        noticeValue.put("popText", welfareNoticeInfo.getText());
        //完成了所有任务隐藏入口
        noticeValue.put("hide", welfareNoticeInfo.isCompleteAllTask());
        if(!anchorWelfareTaskComponet.taskAwardPoolValide(actId)) {
            noticeValue.put("hide", true);
            log.info("unicastPopText award pool not enough,uid:{},business:{},welfareNoticeInfo business:{}", uid, business, welfareNoticeInfo.getBusiness());
        }

        commonBroadCastService.commonNoticeUnicast(actId, "5093_pop", JSON.toJSONString(noticeValue), "", uid);

    }

    /**
     * @param noticeSource       入口请求类型  1-pc上座 2-pc下座 3-app气泡
     * @param uid                uid
     */
    private WelfareNoticeInfo queryWelfareNoticeInfo(long actId, int noticeSource, long uid) {

        WelfareNoticeInfo welfareNoticeInfo = anchorWelfareTaskComponet.queryWelfareNoticeInfo(actId, noticeSource, uid);
        log.info("queryWelfareNoticeInfo actId:{},noticeSource:{},uid:{},info:{}", actId, noticeSource, uid, JSON.toJSONString(welfareNoticeInfo));
        return welfareNoticeInfo;

    }


}
