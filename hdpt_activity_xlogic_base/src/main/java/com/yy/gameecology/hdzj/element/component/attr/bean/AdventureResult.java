package com.yy.gameecology.hdzj.element.component.attr.bean;

import lombok.Data;
import java.util.List;

/**
 * 探险结果
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class AdventureResult {
    
    /**
     * 探险记录ID
     */
    private String recordId;
    
    /**
     * 使用的骰子数量
     */
    private int usedDiceCount;
    
    /**
     * 剩余骰子数量
     */
    private int remainingDiceCount;
    
    /**
     * 探险步骤列表
     */
    private List<AdventureStep> steps;
    
    /**
     * 获得的奖励汇总
     */
    private List<RewardSummary> rewardSummary;
    
    /**
     * 探险是否完成
     */
    private boolean completed;
    
    /**
     * 探险开始时间
     */
    private long startTime;
    
    /**
     * 探险结束时间
     */
    private long endTime;
    
    /**
     * 探险步骤
     */
    @Data
    public static class AdventureStep {
        /**
         * 步骤序号
         */
        private int stepNumber;
        
        /**
         * 骰子点数
         */
        private int diceValue;
        
        /**
         * 起始位置
         */
        private int fromPosition;
        
        /**
         * 目标位置
         */
        private int toPosition;
        
        /**
         * 获得的奖励
         */
        private GridReward reward;
    }
    
    /**
     * 奖励汇总
     */
    @Data
    public static class RewardSummary {
        /**
         * 奖励类型
         */
        private int rewardType;
        
        /**
         * 奖励名称
         */
        private String rewardName;
        
        /**
         * 奖励图标
         */
        private String rewardIcon;
        
        /**
         * 获得数量
         */
        private int totalCount;
    }
    
    /**
     * 格子奖励
     */
    @Data
    public static class GridReward {
        /**
         * 格子位置
         */
        private int position;
        
        /**
         * 奖励类型
         */
        private int rewardType;
        
        /**
         * 奖励名称
         */
        private String rewardName;
        
        /**
         * 奖励图标
         */
        private String rewardIcon;
        
        /**
         * 奖励数量
         */
        private int rewardCount;
    }
}
