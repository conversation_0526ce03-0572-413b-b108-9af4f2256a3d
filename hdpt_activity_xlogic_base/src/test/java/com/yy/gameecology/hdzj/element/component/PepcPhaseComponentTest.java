package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.mq.hdzk.PepcGameEndEvent;
import com.yy.gameecology.activity.client.yrpc.HdptAovClient;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.activity.service.pepc.*;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamGroupMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcAwardRecord;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import com.yy.gameecology.hdzj.element.component.xmodule.pepc.PepcActAwardComponent;
import com.yy.gameecology.hdzj.element.component.xmodule.pepc.PepcPhaseComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-10 21:38
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-6.properties", "classpath:env/local/application-inner.properties"})

public class PepcPhaseComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "6");
    }

    @Autowired
    private PepcInitGamePhaseDataService pepcInitGamePhaseDataService;

    @Autowired
    private PepcPhaseComponent pepcPhaseComponent;

    @Autowired
    private PepcActAwardComponent pepcActAwardComponent;

    @Autowired
    private PepcScheduleService pepcScheduleService;

    @Autowired
    private PepcAwardService pepcAwardService;

    @Autowired
    private PepcTeamGroupMapper pepcTeamGroupMapper;

    @Autowired
    private PepcTeamMapper pepcTeamMapper;

    @Autowired
    private PepcTeamGroupService pepcTeamGroupService;

    @Autowired
    private PepcGameTestService pepcGameTestService;

    @Autowired
    private HdptAovClient hdptAovClient;

    @Autowired
    private PepcDao pepcDao;

    final long actId = 2025046015;

    @Test
    public void initDateTest() {
        PepcPhaseComponentAttr attr = pepcPhaseComponent.getUniqueComponentAttr(actId);
        pepcInitGamePhaseDataService.doInitGamePhaseData(attr, actId);
    }

    @Test
    public void gameEndEvent() {
        PepcPhaseComponentAttr attr = pepcPhaseComponent.getUniqueComponentAttr(actId);
        PepcGameEndEvent event = new PepcGameEndEvent();
        event.setGameId(2025041800000805L);
        event.setActId(actId);
        event.setState(PepcConst.GameState.CLOSE);
        pepcScheduleService.settleGameResult(attr, event);

    }

    @Test
    public void awardTest(){
        pepcAwardService.doSettleAward(pepcPhaseComponent.getUniqueComponentAttr(actId),actId,2);
    }

    @Test
    public void selectTest(){
        pepcTeamGroupMapper.selectByActId(actId);
        pepcTeamGroupMapper.selectDistinctGroupPhase(actId);
        pepcTeamMapper.selectById(15);
    }


    @Test
    public void  transTest(){
        pepcGameTestService.transTest();
    }

    @Test
    public void hdptAovClientTset(){
        hdptAovClient.tryAddFirstAward(1,10086L,"test123","test");
    }


    @Test
    public void insertWithAutoIdTest(){
        PepcAwardRecord record = new PepcAwardRecord();
        record.setActId(actId);
        record.setAwardType(PepcConst.AwardType.FIRST_GAME);
        record.setUid(10086L);
        record.setAmount(1L);
        record.setAwardState(PepcConst.AwardState.GRANTED);
        record.setAwardDesc("测试插入");
        record.setAwardTime(new Date());
        record.setRank(-1);

        pepcDao.saveAwardRecord(record);
    }

}
