package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.activity.service.rankext.RankExtHandler;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.HourStarAwardRecord;
import com.yy.gameecology.hdzj.bean.PersonalTask;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.HourStarComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.HourStarAwardConfig;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.thrift.fts_base_info_bridge.CompereSign;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * desc:小时榜星光玩法/24年交友春季赛
 *
 * <AUTHOR>
 * @date 2024-02-22 11:57
 **/
@UseRedisStore
@RestController
@RequestMapping("/cmpt/hourStar")
@Component
public class HourStarComponent extends BaseActComponent<HourStarComponentAttr> implements RankExtHandler {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private EnrollmentNewService enrollmentNewService;


    private static final int TOP_BANNER_CONTENT_TYPE = 3;

    /**
     * 非top1，top2~top5的分值，暂存下来用来计算星光值
     */
    private final static String TOP_SCORE = "top_score:%s";

    /**
     * 厅星星、星光、Y币获奖记录，提供给专题页查看
     */
    private final static String MEMBER_AWARD_RECORD = "member_award_record:%s";

    /**
     * Y币已发放数量
     */
    private final static String Y_COIN_POOL_AMOUNT = "ycoin_pool_amount";

    /**
     * 全局厅Y币获奖记录，导出给产品发奖
     */
    private final static String Y_COIN_RECORD = "y_coin_record";

    private final static int UPDATE_RANK_RETRY = 3;

    @Override
    public Long getComponentId() {
        return ComponentId.HOUR_STAR;
    }


    /**
     * 监听榜单变化 累周榜荣耀值
     * <p>
     * 不直接配置榜单，需要中转累榜背景；
     * 周榜的玩法有点临界问题，小时榜结算的时候星星有可能会累到下一个周去。要分开配3个榜才行，3周时间对应3个榜
     * 每个榜累榜结束时间延迟30s，然后累榜礼物区分开
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void handleScoreChange(RankingScoreChanged event, HourStarComponentAttr attr) {
        if (!attr.getWeekRankEventRankIds().contains(event.getRankId())) {
            return;
        }

        log.info("handleScoreChange event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        String itemId = null;
        Date eventTime = DateUtil.getDate(event.getTimestamp());

        for (String configItem : attr.getWeekRankUpdateItem().keySet()) {
            String time = attr.getWeekRankUpdateItem().get(configItem);
            String[] timeArray = time.split(",");
            Date begin = DateUtil.getDate(timeArray[0].trim());
            Date end = DateUtil.getDate(timeArray[1].trim());

            if (eventTime.getTime() >= begin.getTime() && eventTime.getTime() <= end.getTime()) {
                itemId = configItem;
                log.info("handleScoreChange set item:{},time:{}", itemId, DateUtil.format(eventTime));
            }
        }

        if (StringUtil.isEmpty(itemId)) {
            log.info("handleScoreChange item empty,time:{},event:{}", JSON.toJSONString(event), DateUtil.format(eventTime));
            return;
        }

        String updateRankSeq = "reload-" + (StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq());
        UpdateRankingRequest request = new UpdateRankingRequest();
        request.setBusiId(attr.getUpdateRankBusiId());
        request.setActId(attr.getActId());
        request.setSeq(updateRankSeq);
        request.setActors(event.getActors());
        request.setItemId(itemId);
        request.setCount(1);
        request.setScore(event.getItemScore());
        request.setTimestamp(eventTime.getTime());
        request.setExtData(Maps.newHashMap());

        //上游 分数上限等控制
        request.setRoleCounts(event.getRoleCounts());
        request.setRoleScores(event.getRoleScores());
        request.setRankCounts(event.getRankCounts());
        request.setRankScores(event.getRankScores());


        boolean result = hdztRankingThriftClient
                .updateRankingWithRetry(request, UPDATE_RANK_RETRY);
        if (!result) {
            log.error("handleScoreChange update rank error,invoke retry,seq:{}", updateRankSeq);
            throw new RuntimeException("update rank error,invoke retry");
        }
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void handlePhaseTimeEnd(PhaseTimeEnd event, HourStarComponentAttr attr) {
        if (event.getRankId() != attr.getSettleNotifyRankId()) {
            return;
        }

        for (Long rankId : attr.getSettleRankIds()) {

            String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(),
                    DateUtil.getDate(event.getEndTime()));
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), rankId, event.getPhaseId(), timeCode, attr.getSettleTopN(), null);
            if (CollectionUtils.isEmpty(ranks)) {
                log.warn("load empty rank,actId:{},rankId:{},phaseId:{},timeCode:{},settleTopN:{}", event.getActId(), event.getRankId(), event.getPhaseId(), timeCode, attr.getSettleTopN());
                return;
            }

            log.info("handlePhaseTimeEnd settleRank,actId:{},rankId:{},ranks:{}", event.getActId(), rankId, JSON.toJSONString(ranks));
            String preTimeCode = calPreTimeCode(event.getActId(), rankId, timeCode);
            for (Rank rank : ranks) {
                settleAward(event, attr, timeCode, preTimeCode, rank);
            }
            log.info("handlePhaseTimeEnd settleRank done,actId:{},rankId:{},ranks:{}", event.getActId(), rankId, JSON.toJSONString(ranks));
        }

    }

    private void settleAward(PhaseTimeEnd event, HourStarComponentAttr attr, String timeCode, String preTimeCode, Rank rank) {
        HourStarAwardConfig hourStarAwardConfig = calAwardConfig(attr, rank.score);
        if (hourStarAwardConfig == null) {
            //本次没达到门槛也拿了第一名，获取不到配置
            log.warn("settleAward not config award,actId:{},rank:{},score:{}", attr.getActId(), rank.getRank(), rank.getScore());
        }
        long star = hourStarAwardConfig == null ? 0 : hourStarAwardConfig.getStar();


        log.warn("settleAward begin,actId:{},rank:{},score:{},config:{}", attr.getActId(), rank.getRank(), rank.getScore(), JSON.toJSONString(hourStarAwardConfig));
        String redisCode = getRedisGroupCode(attr.getActId());

        if (rank.getRank() <= attr.getStarAwardTopN()) {
            String preXgKey = buildTopScoreKey(attr, rank.getMember());
            long preTopScore = Convert.toLong(actRedisDao.hget(redisCode, preXgKey, preTimeCode), 0);
            HourStarAwardConfig preAwardConfig = calAwardConfig(attr, preTopScore);
            long preXG = preAwardConfig == null ? 0 : preAwardConfig.getXg();
            long awardXxScore = star + preXG;
            awardXxScore = awardXxScore * attr.getStarUpdateRankWeight();
            String eventMemberSeq = (StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq()) + ":" + rank.getMember();

            //---上报星星数据
            updateStarRank(event, attr, eventMemberSeq, awardXxScore, rank);

            long preYCoinAmount = preAwardConfig == null ? 0 : preAwardConfig.getXgNum();
            long yCoinAmount = (hourStarAwardConfig == null ? 0 : hourStarAwardConfig.getNum()) + preYCoinAmount;

            if (yCoinAmount > 0) {
                //---Y币发放总上限控制
                String totalPoolSeq = makeKey(attr, "seq:pool:" + eventMemberSeq);
                List<Long> addResult = actRedisDao.incrValueWithLimitSeq(redisCode, totalPoolSeq, buildYCoinPoolKey(attr), yCoinAmount, attr.getYcoinTotalPool(), true, DateUtil.ONE_WEEK_SECONDS);
                //0-完整发放 1-奖池不足部分发放 2-奖池抢空了，不发放
                int addType = 0;
                long realYCoinAmount = 0;
                if (addResult.get(0) > 0) {
                    realYCoinAmount = addResult.get(0) == 1 ? yCoinAmount : addResult.get(1);
                }
                if (realYCoinAmount == 0) {
                    log.warn("settleAward pool empty,actId:{},member:{},preYCoinAmount:{},yCoinAmount:{},realYCoinAmount:{}", attr.getActId(), rank.getMember(), preYCoinAmount, yCoinAmount, realYCoinAmount);
                    addType = 2;
                } else if (realYCoinAmount < yCoinAmount) {
                    log.warn("settleAward pool par add,actId:{},member:{},preYCoinAmount:{},yCoinAmount:{},realYCoinAmount:{}", attr.getActId(), rank.getMember(), preYCoinAmount, yCoinAmount, realYCoinAmount);
                    addType = 1;
                }

                log.info("settleAward pool ,actId:{},member:{},preYCoinAmount:{},yCoinAmount:{},realYCoinAmount:{}", attr.getActId(), rank.getMember(), preYCoinAmount, yCoinAmount, realYCoinAmount);
                //---发奖记录
                HourStarAwardRecord record = new HourStarAwardRecord();
                record.setAddType(addType);
                record.setMemberId(rank.getMember());
                record.setAwardAmount(realYCoinAmount);
                record.setStar(star);
                record.setPreXg(preAwardConfig == null ? 0 : preAwardConfig.getXg());
                record.setSettleTime(event.getEndTime());
                record.setCreateTime(DateUtil.format(new Date()));
                String awardRecordKey = buildAwardRecordKey(attr, rank.getMember());
                String awardRecordSeq = makeKey(attr, "seq:record:" + eventMemberSeq);
                actRedisDao.lPushWithSeq(redisCode, awardRecordSeq, awardRecordKey, JSON.toJSONString(record), DateUtil.ONE_WEEK_SECONDS);

                //---Y币奖励，记录厅发奖记录，由于发放对象是厅，这里只记录1条流水数据，导出给产品活动结束后发奖
                String yCoinRecordSeq = makeKey(attr, "seq:yr:" + eventMemberSeq);
                String yCoinRecordSeqRecord = rank.getMember() + "," + realYCoinAmount + "," + DateUtil.format(commonService.getNow(attr.getActId()));
                actRedisDao.lPushWithSeq(redisCode, yCoinRecordSeq, makeKey(attr, Y_COIN_RECORD), yCoinRecordSeqRecord, DateUtil.ONE_WEEK_SECONDS);
            }


            //---app横幅广播
            String bannerSeq = makeKey(attr, "seq:banner:" + eventMemberSeq);
            RetryTool.withRetryCheck(attr.getActId(), bannerSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
                broAppBanner(eventMemberSeq, attr, star, preXG, rank.getMember());
            });

        } else {
            //记录星光数据
            log.info("not star award top n,actId:{},member:{},score:{}", attr.getActId(), rank.getMember(), rank.getScore());
            String topScoreKey = buildTopScoreKey(attr, rank.getMember());
            actRedisDao.hset(redisCode, topScoreKey, timeCode, Convert.toString(rank.getScore()));
        }
    }

    private void updateStarRank(PhaseTimeEnd event, HourStarComponentAttr attr, String eventMemberSeq, long awardXxScore, Rank rank) {

        String updateStarItem = calUpdateStarItem(DateUtil.getDate(event.getEndTime()), attr);
        if (StringUtil.isBlank(updateStarItem)) {
            log.warn("cal empty updateStarItem");
            return;
        }
        if (awardXxScore == 0) {
            log.warn("awardXxScore zero");
            return;
        }
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(event.getActId(), attr.getUpdateRankBusiId(), RoleType.HALL.getValue(), rank.getMember());
        if (enrollmentInfo == null) {
            log.error("updateStarRank not found enrollmentInfo,actId:{},member:{}", event.getActId(), rank.getMember());
            return;
        }

        //---星星累榜
        String updateRankSeq = "start:" + eventMemberSeq;
        UpdateRankingRequest request = new UpdateRankingRequest();
        request.setBusiId(attr.getUpdateRankBusiId());
        request.setActId(attr.getActId());
        request.setSeq(updateRankSeq);
        request.setActors(ImmutableMap.of(enrollmentInfo.getDestRoleId(), rank.getMember()));
        request.setItemId(updateStarItem);
        request.setCount(1);
        request.setScore(awardXxScore);
        Date time = DateUtil.getDate(event.getTimestamp());
        request.setTimestamp(time.getTime());
        request.setExtData(Maps.newHashMap());


        boolean result = hdztRankingThriftClient
                .updateRankingWithRetry(request, UPDATE_RANK_RETRY);
        if (!result) {
            log.error("update rank error,invoke retry,seq:{}", updateRankSeq);
            throw new RuntimeException("update rank error,invoke retry");
        }
    }

    private String calUpdateStarItem(Date eventTime, HourStarComponentAttr attr) {
        for (String configItem : attr.getStartUpdateRankItem().keySet()) {
            String time = attr.getStartUpdateRankItem().get(configItem);
            String[] timeArray = time.split(",");
            Date begin = DateUtil.getDate(timeArray[0].trim());
            Date end = DateUtil.getDate(timeArray[1].trim());

            if (eventTime.getTime() >= begin.getTime() && eventTime.getTime() <= end.getTime()) {
                log.info("calUpdateStarItem set item:{},time:{}", configItem, DateUtil.format(eventTime));
                return configItem;
            }
        }

        return "";
    }


    public void broAppBanner(String seq, HourStarComponentAttr attr, long star, long preXg, String memberId) {

        //礼物名称替换
        String textContent = attr.getAppBannerText();

        String textStar = "";
        if (star > 0 || preXg > 0) {
            textStar = "，获得</font><font color='#FEF390'>星星X{star}{preXg}</font>";
            textStar = textStar.replace("{star}", Convert.toString(star));
            textStar = textStar.replace("{preXg}", preXg > 0 ? "+" + preXg : "");
        }
        textContent = textContent.replace("{textStar}", textStar);

        //用户昵称替换(TODO 这里先暂且写死是交友厅，满足本次活动需求)
        MemberInfo memberInfo = memberInfoService.getMemberInfo(Convert.toLong(BusiId.MAKE_FRIEND.getValue()), RoleType.HALL, memberId);
        textContent = textContent.replace("{nick}", memberInfo == null ? "" : memberInfo.getName());


        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        svgaConfig.setDuration(13);
        svgaConfig.setSvgaURL(attr.getAppBannerSvga());

        Map<String, AppBannerSvgaText> svgaText = Maps.newHashMap();
        AppBannerSvgaText text = new AppBannerSvgaText();
        text.setText(textContent);
        svgaText.put(attr.getAppBannerTextKey(), text);
        svgaConfig.setContentLayers(Collections.singletonList(svgaText));

        //背景图片
        svgaConfig.setImgLayers(buildSvgaImageConfig(attr));

        final int bcType = FstAppBroadcastType.ALL_TEMPLATE;
        String[] memberArray = memberId.split("_");
        long sid = Convert.toLong(memberArray[0]);
        long ssid = memberArray.length == 2 ? Convert.toLong(memberArray[1]) : sid;


        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getAppBannerBusiId(),
                bcType, sid, ssid, "",
                Lists.newArrayList());
        appBannerEvent.setContentType(TOP_BANNER_CONTENT_TYPE);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getUpdateRankBusiId())));
        appBannerEvent.setSvgaConfig(svgaConfig);


        //姑且认为是厅，满足本次活动需求
        String[] hall = memberId.split("_");

        appBannerEvent.setSid(Convert.toLong(hall[0]));
        appBannerEvent.setSsid(Convert.toLong(hall[1]));
        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("processSupperAppBanner app done seq:{}, member:{} event:{}", seq, memberId, JSON.toJSONString(appBannerEvent));
    }

    private List<Map<String, String>> buildSvgaImageConfig(HourStarComponentAttr attr) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }

        Map<String, String> imageMap = attr.getSvgaImgLayers();
        for (String imageKey : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String image = imageMap.get(imageKey);
            broImgLayer.put(imageKey, image);
            broImgLayers.add(broImgLayer);
        }

        return broImgLayers;
    }


    private String calPreTimeCode(long actId, long rankId, String curTimeCode) {
        Date current = DateUtil.getDate(curTimeCode, DateUtil.PATTERN_TYPE7);
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId);

        String currentHour = DateUtil.format(current, DateUtil.PATTERN_TYPE6);
        //小时榜开始的第一个小时，则用前一天的最后1个小时
        if (currentHour.equals(rankingInfo.getTimeKeyBegin())) {
            Date preDay = DateUtil.getAddDay(current, -1);
            String preDayLastHour = DateUtil.format(preDay, DateUtil.PATTERN_TYPE5) + " " + rankingInfo.getTimeKeyEnd();
            return DateUtil.format(DateUtil.getDate(preDayLastHour), DateUtil.PATTERN_TYPE7);

        } else {
            //非开始的第一个小时，则用当前时间上1个小时
            return DateUtil.format(DateUtil.addHours(current, -1), DateUtil.PATTERN_TYPE7);
        }

    }


    private HourStarAwardConfig calAwardConfig(HourStarComponentAttr attr, long score) {
        HourStarAwardConfig config = null;
        //attr.getScoreAward().keySet() 要从小到大排序
        List<Long> scoreAward = attr.getScoreAward().keySet().stream().sorted().toList();
        for (long configScore : scoreAward) {
            if (score >= configScore) {
                config = attr.getScoreAward().get(configScore);
            }
        }
        return config;
    }

    private String buildYCoinPoolKey(HourStarComponentAttr attr) {
        return makeKey(attr, Y_COIN_POOL_AMOUNT);
    }

    private String buildTopScoreKey(HourStarComponentAttr attr, String member) {
        return makeKey(attr, String.format(TOP_SCORE, member));
    }

    private String buildAwardRecordKey(HourStarComponentAttr attr, String member) {
        return makeKey(attr, String.format(MEMBER_AWARD_RECORD, member));
    }

    @Override
    public List<String> supportKeys() {
        return null;
    }

    //榜单扩展信息
    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        HourStarComponentAttr attr = tryGetUniqueComponentAttr(rankReq.getActId());
        if (attr == null || CollectionUtils.isEmpty(objectList)) {
            return objectList;
        }

        //周榜星星、星光显示处理
        if (attr.getShowStarRank().contains(rankReq.getRankId())) {
            Iterator<Object> objectIterator = objectList.iterator();
            while (objectIterator.hasNext()){

                RankValueItemBase rankItem = (RankValueItemBase) objectIterator.next();
                long sourceScore = rankItem.getValue();
                //减去星光放大的倍数
                long xxScore = sourceScore / attr.getStarUpdateRankWeight();
                //星星为0的不显示
                if (xxScore == 0) {
                    objectIterator.remove();
                }
                rankItem.setValue(xxScore);
                Map<String, String> viewExt = rankItem.getViewExt();
                if (viewExt == null) {
                    viewExt = new HashMap<>(3);
                    rankItem.setViewExt(viewExt);
                }
                //余数是荣耀值
                viewExt.put("yzz", Convert.toString(sourceScore % attr.getStarUpdateRankWeight()));
            }
        }

        //小时榜预计得到多少星星、星光处理
        if (attr.getShowSettleRank().contains(rankReq.getRankId())) {
            for (int i = 0; i < objectList.size() && i < attr.getSettleTopN(); i++) {
                RankValueItemBase rankItem = (RankValueItemBase) objectList.get(i);
                long sourceScore = rankItem.getValue();
                Map<String, String> viewExt = rankItem.getViewExt();
                if (viewExt == null) {
                    viewExt = new HashMap<>(3);
                    rankItem.setViewExt(viewExt);
                }
                //星星
                HourStarAwardConfig config = calAwardConfig(attr, sourceScore);
                if (i < attr.getStarAwardTopN()) {
                    //本轮星星
                    viewExt.put("xx", Convert.toString(config == null ? 0 : config.getStar()));

                    //上一轮星光
                    String preTiemCode = calPreTimeCode(rankReq.getActId(), rankReq.getRankId(), rankReq.getDateStr());
                    long preScore = Convert.toLong(actRedisDao.hget(getRedisGroupCode(rankReq.getActId()), buildTopScoreKey(attr, rankItem.getKey()), preTiemCode), 0);
                    HourStarAwardConfig preConfig = calAwardConfig(attr, preScore);
                    viewExt.put("preXg", Convert.toString(preConfig == null ? 0 : preConfig.getXg()));

                } else {
                    //本轮星光
                    viewExt.put("xg", Convert.toString(config == null ? 0 : config.getXg()));
                }
            }

        }


        return objectList;
    }

    /**
     * 查询获奖记录
     */
    @GetMapping("/queryHourStarAwardRecord")
    public Response<Map<String, Object>> queryHourStarAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                                                  @RequestParam(name = "actId") long actId,
                                                                  @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx,
                                                                  @RequestParam(name = "sid") long sid,
                                                                  @RequestParam(name = "ssid") long ssid) {
        long loginUid = getLoginYYUid(request, response);
        if (loginUid <= 0) {
            return Response.fail(401, "未登录！");
        }

        Map<Long, CompereSign> signMap = ftsBaseInfoBridgeClient.getFtsSign(Lists.newArrayList(loginUid));
        log.info("queryHourStarAwardRecord loginUid:{},sid:{},signMap:{}", loginUid,sid,JSONUtils.toJsonString(signMap));
        if (signMap == null || !signMap.containsKey(loginUid) || signMap.get(loginUid).getSid() == 0) {
            return Response.fail(402, "未签约！");
        }
        if (signMap.get(loginUid).getSid() != sid) {
            return Response.fail(403, "未签约本公会！");
        }
        HourStarComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        Map<String, Object> result = Maps.newHashMap();

        List<HourStarAwardRecord> list = Lists.newArrayList();

        String key = buildAwardRecordKey(attr, sid + "_" + ssid);
        String redisCode = getRedisGroupCode(actId);
        List<String> awardRecord = actRedisDao.lrange(redisCode, key, 0, 1000);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            for (String item : awardRecord) {
                HourStarAwardRecord record = JSON.parseObject(item, HourStarAwardRecord.class);
                list.add(record);
            }
        }

        long send = Convert.toLong(actRedisDao.get(redisCode, buildYCoinPoolKey(attr)), 0);
        result.put("left", Math.max(attr.getYcoinTotalPool() - send, 0));
        result.put("awardList", list);

        String nick = memberInfoService.getMemberName(attr.getUpdateRankBusiId(), RoleType.HALL, sid + "_" + ssid);
        result.put("nick", nick);

        return Response.success(result);
    }

    /**
     * 查询获奖记录
     */
    @GetMapping("/queryHourStarAwardPool")
    public Response<Map<String, Object>> queryHourStarAwardPool(HttpServletRequest request, HttpServletResponse response,
                                                                @RequestParam(name = "actId") long actId,
                                                                @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {
        HourStarComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        Map<String, Object> result = Maps.newHashMap();
        String redisCode = getRedisGroupCode(actId);

        long send = Convert.toLong(actRedisDao.get(redisCode, buildYCoinPoolKey(attr)), 0);
        result.put("total",attr.getYcoinTotalPool());
        result.put("send",send);
        result.put("left", Math.max(attr.getYcoinTotalPool() - send, 0));

        return Response.success(result);
    }

    public Map<String, Object> buildItemMemberExtInfo(long actId, long index, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        HourStarComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            return Maps.newHashMap();
        }
        Map<String, Object> extInfo = Maps.newHashMap();

        long score = layerMemberItem.getScore();

        //星星，星光展示
        HourStarAwardConfig hourStarAwardConfig = calAwardConfig(attr, score);
        long xx = hourStarAwardConfig == null ? 0 : hourStarAwardConfig.getStar();
        long xg = hourStarAwardConfig == null ? 0 : hourStarAwardConfig.getXg();
        if (layerMemberItem.getRank() <= attr.getSettleTopN()) {
            if (layerMemberItem.getRank() <= attr.getStarAwardTopN()) {

                //叠加上一轮的星光数来显示
                if (Convert.toLong(layerMemberItem.getCurRankId(), 0) > 0) {
                    String timeCode = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE7);
                    String preTimeCode = calPreTimeCode(actId, layerMemberItem.getCurRankId(), timeCode);
                    String preScoreKey = buildTopScoreKey(attr, layerMemberItem.getMemberId());
                    long preScore = Convert.toLong(actRedisDao.hget(getRedisGroupCode(actId), preScoreKey, preTimeCode), 0);
                    HourStarAwardConfig preConfig = calAwardConfig(attr, preScore);
                    if (preConfig != null && preConfig.getXg()>0) {
                        extInfo.put("preXg", preConfig.getXg());
                    }
                }

                extInfo.put("xx", xx);
            } else {
                extInfo.put("xg", xg);
            }
        }

        extInfo.put("topN", layerMemberItem.getRank() > 0 && layerMemberItem.getRank() <= attr.getSettleTopN() ? 1 : 0);


        //任务进度展示
        boolean isFinish = false;

        score = Math.max(0, score);
        List<Long> scoreConfig = attr.getScoreAward().keySet().stream().sorted().toList();
        long targetScore = scoreConfig.get(0);
        for (int i = 0; i < scoreConfig.size(); i++) {
            long configItem = scoreConfig.get(i);
            if (score >= configItem) {
                if ((i + 1) == scoreConfig.size()) {
                    isFinish = true;
                    targetScore = configItem;

                } else {
                    targetScore = scoreConfig.get(i + 1);
                }
            }
        }

        PersonalTask personalTask = new PersonalTask();
        personalTask.setMyScore(score);
        personalTask.setMyTarget(targetScore);
        personalTask.setFinish(isFinish);
        extInfo.put("personalTask", personalTask);

        return extInfo;
    }
}
