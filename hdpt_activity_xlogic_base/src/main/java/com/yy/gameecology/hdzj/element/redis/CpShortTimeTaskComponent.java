package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerMp4Config;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ChannelChatLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.CpShortTimeTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.history.ChannelChatLotteryComponent;
import com.yy.thrift.hdztranking.BusiId;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-10-18 16:01
 **/
@UseRedisStore
@Component
@RestController
@RequestMapping("/5113")
public class CpShortTimeTaskComponent extends BaseActComponent<CpShortTimeTaskComponentAttr> implements LayerSupport {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private Locker locker;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private ChannelChatLotteryComponent channelChatLotteryComponent;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private AggregationService aggregationService;

    @Autowired
    private BroActLayerService broActLayerService;

    private final static int AUTO_LOAD_DATA_AGGREGATION_SECOND = 5;

    /**
     * hash cp当前飞行数据
     * %s:%s---> yyyyMMddHH:userUid_anchor_uid
     * hashKey:
     * score:飞行距离
     * startTime:开始时间，闭区间
     * endTime:结束时间，闭区间
     * state:0--初始状态 1--已结算,是新记录 2--已结算，未打破纪录
     */
    private final static String CP_CUR_SCORE_INFO = "cp_cur_score_info:%s:%s";

    private final static String CP_CUR_HASH_KEY_ID = "id";
    private final static String CP_CUR_HASH_KEY_SCORE = "score";
    private final static String CP_CUR_HASH_KEY_START_TIME = "startTime";
    private final static String CP_CUR_HASH_KEY_END_TIME = "endTime";
    private final static String CP_CUR_HASH_KEY_STATE = "state";

    /**
     * 用户关联的cp
     * %s:%s---> yyyyMMddHH:userUid_anchor_uid
     */
    private final static String CP_REL_MEMBER = "cp_rel_member:%s:%s";

    private final static List<Object> CP_CUR_ALL_HASH_KEY = Lists.newArrayList(CP_CUR_HASH_KEY_ID, CP_CUR_HASH_KEY_SCORE, CP_CUR_HASH_KEY_START_TIME, CP_CUR_HASH_KEY_END_TIME, CP_CUR_HASH_KEY_STATE);

    private final static String TOTAL_AWARD_CONSUME = "total_award_consume";
    /**
     * 正在起飞的cp
     * 用于结算到全服飞行距离
     */
    private final static String CP_ON_GOING = "cp_on_going";

    /**
     * cp历史记录
     */
    private final static String CP_INFO_HIS = "cp_info_his:%s";

    /**
     * 个人cp小时记录
     * list
     */
    private final static String CP_HOUR_RECORD = "cp_hour_record:%s:%s";

    /**
     * sort set 全服飞行距离榜（同一成员去重）
     * %s ---> yyyyMMddHH
     */
    private final static String CP_RANK = "cp_rank:%s";


    /**
     * sort set 全服飞行距离榜扩展信息（同一成员去重）
     * %s ---> yyyyMMddHH  %s ---> memberId
     */
    private final static String CP_RANK_EXT = "cp_rank:%s:%s";

    /**
     * sort set
     * %s ---> yyyyMMddHH
     */
    private final static String CP_ID_RANK = "cp_id_rank:%s";

    /**
     * 主播最后1次收礼的uid
     */
    private final static String ANCHOR_LAST_CP = "anchor_last_cp";

    /**
     * 主播最后1次收礼的频道
     */
    private final static String ANCHOR_LAST_CHANNEL = "anchor_last_channel";

    /**
     * hash,小时榜 结算标记
     */
    private final static String CP_RANK_SETTLE_RECORD = "cp_rank_settle_record";

    /**
     * 第一名发奖记录
     * hash
     * key yyyyMMddHH
     */
    private final static String TOP1_AWARD_RECORD = "top1_award_record";

    private static final long CP_RANK_BRO_BANNER_ID = 5113001;

    private static final long CP_RANK_QUERY_TOP_AMOUNT = 50;


    @Override
    public long getActId() {
        return ComponentId.CP_SHORT_TIME_TASK;
    }

    @Override
    public Long getComponentId() {
        return ComponentId.CP_SHORT_TIME_TASK;
    }

    @RequestMapping("/settleHourRankAwardBroTest")
    public Response<String> settleHourRankAwardBroTest(long actId, long index, long uid, long anchorUid, long score, String timeCode, long awardIndex) {
        if (!SysEvHelper.isDev()) {
            return Response.fail(-1, "not dev , reject");
        }
        var attr = getComponentAttr(actId, index);
        TopCpInfo topCpInfo = new TopCpInfo();
        topCpInfo.setUserUid(uid);
        topCpInfo.setAnchorUid(anchorUid);
        topCpInfo.setScore(score);

        AwardAttrConfig awardConfig = attr.getHourRankAward().get(awardIndex);

        String finalTimeCode = timeCode + System.currentTimeMillis();
        ChannelInfoVo channelInfoVo = getAnchorChannelInfo(attr, topCpInfo.getAnchorUid());
        settleHourRankAwardBro(attr, topCpInfo, awardConfig, channelInfoVo, finalTimeCode);
        addChatLotteryBox(attr, topCpInfo, channelInfoVo, finalTimeCode);
        return Response.success(finalTimeCode);
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, CpShortTimeTaskComponentAttr attr) throws Exception {
        if (!(attr.getRankId() == event.getRankId() && attr.getPhaseId() == event.getPhaseId())) {
            return;
        }
        Clock clock = new Clock();
        log.info("onRankingScoreChanged event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        String lockKey = makeKey(attr, "rank_change:" + event.getMember());
        Secret secret = null;
        try {
            secret = locker.lock(lockKey, 1, "", 100, 10);
            if (secret == null) {
                throw new Exception("CpShortTimeTaskComponent lock fail,now retry");
            }
            invokeOnRankingScoreChange(event, attr);
        } catch (Exception e) {
            log.info("onRankingScoreChanged error:{}", e.getMessage());
            throw e;
        } finally {
            if (secret != null) {
                locker.unlock(lockKey, secret);
            }
        }
        log.info("onRankingScoreChanged done,seq:{},cost:{}", clock.tag(), event.getSeq());
    }

    private void invokeOnRankingScoreChange(RankingScoreChanged event, CpShortTimeTaskComponentAttr attr) {
        String sourceSeq = StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq();
        String redisCode = getRedisGroupCode(event.getActId());
        String member = event.getMember();
        String timeCode = DateUtil.format(DateUtil.getDate(event.getOccurTime()), DateUtil.PATTERN_TYPE7);

        //先结算老的已过期cp
        CpInfo cpInfo = getCurCpInfo(attr, redisCode, member, timeCode);
        if (cpInfo != null && isCpInfoExpire(attr, cpInfo)) {
            settleExpireCpInfoByLock(attr, cpInfo, redisCode, timeCode);
            cpInfo = null;
        }
        //初始化新cp
        boolean newCp = false;
        if (cpInfo == null && event.getItemScore() >= attr.getInsertCpMinScore()) {
            insertOrReplaceCurCpInfo(attr, redisCode, sourceSeq, member, timeCode);
            newCp = true;
        }

        //给cp加分
        boolean existGoingCp = cpInfo != null && cpInfo.getState() == 0;
        if (existGoingCp || newCp) {
            long addScore = event.getItemScore() / 10;
            addCurCpScore(attr, sourceSeq, redisCode, member, timeCode, addScore);
            addRelMember(attr, member, timeCode);
            addLastReceiveGiftCp(attr, redisCode, member);
        }

        addLastReceiveGiftChannel(event, attr, redisCode, member);

        //单播 通知专题页更新数据
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> noticeUserLoadNewData(attr.getActId(), member, Lists.newArrayList("queryUserStatus")));

        log.info("onRankingScoreChanged done,actId:{},member:{},score:{}", event.getActId(), member, event.getItemScore());
    }


    @PostConstruct
    @Scheduled(cron = "0/1 * * * * ? ")
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    public void settle() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            Date now = commonService.getNow(actId);
            ActivityInfoVo actInfo = hdztRankingThriftClient.queryActivityInfo(actId);
            long startTime = actInfo.getBeginTime();
            long endTime = actInfo.getEndTime() + DateUtil.ONE_MIN_MILL_SECONDS;
            if (now.getTime() < startTime || now.getTime() > endTime) {
                continue;
            }
            CpShortTimeTaskComponentAttr attr = tryGetUniqueComponentAttr(actId);
            timerSupport.work(makeKey(attr, "settleTimer"), Const.TEN, () -> {
                //先计算cp
                settleExpireCp(attr);
                //再结算小时榜
                Date preHour = DateUtil.addHours(commonService.getNow(attr.getActId()), -1);
                timerInvokeSettleHourRank(attr, preHour, now);
            });
        }
    }


    /**
     * 查询我的飞行状态
     */
    @RequestMapping("/queryUserStatus")
    public Response<UserStatusCpMember> queryUserStatus(Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx
            , @RequestParam(name = "timeCode") String timeCode
            , @RequestParam(name = "cpMember", required = false) String cpMember
            , @RequestParam(name = "56637EAD0B6CA194498BFFD27ADBB94A", required = false) Long userId) {
        long uid = getLoginYYUid();
        if (uid <= 0L && userId == null) {
            return Response.fail(400, "未登录");
        }
        if (userId != null && userId > 0) {
            uid = userId;
        }
        CpShortTimeTaskComponentAttr attr = getComponentAttr(actId, cmptInx);
        //如果cpMember传了，则查询精准的成员，如果没有传，则查询uid关联的所有cp
        UserStatusCpMember userStatusCpMember = getUserStatusCpMember(attr, uid, timeCode, cpMember);
        return Response.success(userStatusCpMember);
    }

    /**
     * 查询全服飞行记录（每个cp取最高）
     */
    @RequestMapping("/rank")
    public Response<CpMemberRank> rank(Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx
            , @RequestParam(name = "timeCode") String timeCode) {
        long uid = getLoginYYUid();
        CpShortTimeTaskComponentAttr attr = getComponentAttr(actId, cmptInx);
        CpMemberRank cpMemberRank = queryCpMemberRank(attr, timeCode, uid);
        return Response.success(cpMemberRank);
    }

    @RequestMapping("/myRecord")
    public Response<CpMemberRank> myRecord(Long actId, @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx
            , @RequestParam(name = "timeCode") String timeCode
            , @RequestParam(name = "top", defaultValue = "50") int top
            , @RequestParam(name = "56637EAD0B6CA194498BFFD27ADBB94A", required = false) Long userId) {
        long uid = getLoginYYUid();
        if (uid <= 0L && userId == null) {
            return Response.fail(400, "未登录");
        }
        if (userId != null && userId > 0) {
            uid = userId;
        }
        CpMemberRank result = new CpMemberRank();
        Map<String, Map<String, MultiNickItem>> nickExtUsers = Maps.newHashMap();
        CpShortTimeTaskComponentAttr attr = getComponentAttr(actId, cmptInx);
        List<CpMember> cpMembers = queryMyCpRecordMembers(attr, timeCode, uid, top, nickExtUsers);

        result.setCpMembers(cpMembers);
        result.setNickExtUsers(nickExtUsers);
        return Response.success(result);
    }

    private void timerInvokeSettleHourRank(CpShortTimeTaskComponentAttr attr, Date settleTime, Date now) {
        //结算延迟
        Date hourBegin = DateUtil.getHourBeginTime(now);
        long offset = now.getTime() - hourBegin.getTime();
        if (offset < attr.getHourSettleDelayMill()) {
            log.info("timerInvokeSettleHourRank delay,now:{},offset:{},delay:{}", now, offset, attr.getHourSettleDelayMill());
            return;
        }

        String redisCode = getRedisGroupCode(attr.getActId());
        String preHourTimeCode = DateUtil.format(settleTime, DateUtil.PATTERN_TYPE7);
        //已结算去重,因为以下代码是幂等，所以可以先读出来判断，无并发执行问题，执行完再打上已执行标记，避免执行不成功
        String cpRankSettleKey = makeKey(attr, CP_RANK_SETTLE_RECORD);
        String settleTag = actRedisDao.hget(redisCode, cpRankSettleKey, preHourTimeCode);
        if (StringUtil.isNotBlank(settleTag)) {
            return;
        }
        settleHourRank(attr, preHourTimeCode);
        actRedisDao.hset(redisCode, cpRankSettleKey, preHourTimeCode, DateUtil.format(new Date()));
    }

    private TopCpInfo settleHourRank(CpShortTimeTaskComponentAttr attr, String preHourTimeCode) {
        log.info("settleHourRank begin preHourTimeCode:{}", preHourTimeCode);
        TopCpInfo topCpInfo = queryHourTopCp(attr, preHourTimeCode);
        if (topCpInfo == null) {
            return null;
        }
        AwardAttrConfig awardConfig = settleHourRankAward(attr, topCpInfo, preHourTimeCode);
        ChannelInfoVo channelInfoVo = getAnchorChannelInfo(attr, topCpInfo.getAnchorUid());
        settleHourRankAwardBro(attr, topCpInfo, awardConfig, channelInfoVo, preHourTimeCode);
        addChatLotteryBox(attr, topCpInfo, channelInfoVo, preHourTimeCode);
        log.info("settleHourRank done preHourTimeCode:{}", preHourTimeCode);

        return topCpInfo;
    }

    private TopCpInfo queryHourTopCp(CpShortTimeTaskComponentAttr attr, String preHourTimeCode) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String key = buildCpRankKey(attr, preHourTimeCode);
        Set<ZSetOperations.TypedTuple<String>> topCpInfos = actRedisDao.zrevRange(redisCode, key, Const.ONE);
        if (CollectionUtils.isEmpty(topCpInfos)) {
            return null;
        }
        ZSetOperations.TypedTuple<String> info = topCpInfos.stream().findFirst().get();
        CpUid cpUid = Const.splitCpMember(info.getValue());
        long score = Objects.requireNonNull(info.getScore()).longValue();

        TopCpInfo topCpInfo = new TopCpInfo();
        topCpInfo.setUserUid(cpUid.getUserUid());
        topCpInfo.setAnchorUid(cpUid.getAnchorUid());
        topCpInfo.setScore(score);
        return topCpInfo;
    }

    private AwardAttrConfig settleHourRankAward(CpShortTimeTaskComponentAttr attr, TopCpInfo topCpInfo, String preHourTimeCode) {
        if (topCpInfo == null) {
            log.info("settleHourRankAward cp info empty,actId:{},preHourTimeCode:{}", attr.getActId(), preHourTimeCode);
            return null;
        }
        String redisCode = getRedisGroupCode(attr.getActId());

        long userId = topCpInfo.getUserUid();
        long anchorId = topCpInfo.getAnchorUid();
        long score = topCpInfo.getScore();
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        String awardSourceSeq = "settleHourRank:" + preHourTimeCode;

        //奖池不足时发放进场秀(最后一笔允许超发，所以可以先读出来判断)
        boolean awardPoolOut = getAwardPoolLeft(attr) <= 0;
        AwardAttrConfig awardConfig = calAwardAttrConfig(attr, score);
        if (awardPoolOut) {
            log.info("award pool out release,uid:{},anchorUid:{},awardConfig:{}", userId, anchorId, JSON.toJSONString(awardConfig));

        } else {
            String reduceSeq = makeKey(attr, "seq:reduce_pool:" + awardSourceSeq);
            String consumeKey = buildTotalAwardConsumeKey(attr);
            List<Long> addResult = actRedisDao.incrValueWithSeq(redisCode, reduceSeq, consumeKey, awardConfig.getAwardAmount() * 2, DateUtil.ONE_WEEK_SECONDS);
            log.info("settleHourRank award pool incrValueWithSeq  actId:{},uid:{},anchorUid:{},reduceSeq:{},addResult:{}", attr.getActId(), userId, anchorId, reduceSeq, JSON.toJSONString(addResult));
        }
        Map<Long, Integer> packageIdAmount = ImmutableMap.of(awardConfig.getTAwardPkgId(), awardConfig.getNum());
        String awardUserSeq = makeKey(attr, awardSourceSeq + ":user:" + userId);
        String awardUserSeqMd5 = MD5SHAUtil.getMD5(awardUserSeq);
        hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), userId, awardConfig.getTAwardTskId(), ImmutableMap.of(awardConfig.getTAwardTskId(), packageIdAmount), awardUserSeqMd5, Maps.newHashMap());

        String awardAnchorSeq = makeKey(attr, awardSourceSeq + ":anchor:" + anchorId);
        String awardAnchorSeqMd5 = MD5SHAUtil.getMD5(awardAnchorSeq);
        hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), anchorId, awardConfig.getTAwardTskId(), ImmutableMap.of(awardConfig.getTAwardTskId(), packageIdAmount), awardAnchorSeqMd5, Maps.newHashMap());

        //第一名发奖记录，用于飞行记录王历史发奖显示
        String awardRecordKey = buildTop1AwardRecord(attr);
        actRedisDao.hset(redisCode, awardRecordKey, preHourTimeCode, JSON.toJSONString(awardConfig));

        return awardConfig;
    }

    private void settleHourRankAwardBro(CpShortTimeTaskComponentAttr attr, TopCpInfo topCpInfo, AwardAttrConfig awardConfig, ChannelInfoVo channelInfoVo, String preHourTimeCode) {
        if (topCpInfo == null) {
            log.info("settleHourRankAwardBro cp info empty,actId:{},preHourTimeCode:{}", attr.getActId(), preHourTimeCode);
            return;
        }
        long userUid = topCpInfo.getUserUid();
        long anchorUid = topCpInfo.getAnchorUid();
        //---pc 广播横幅[全频道广播]
        Set<Long> uids = Sets.newHashSet(topCpInfo.getUserUid(), topCpInfo.getAnchorUid());
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers, true, attr.getTemplateType());

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo babyInfoVo = userInfos.get(anchorUid);
        if (channelInfoVo != null) {
            ext.put("sid", channelInfoVo.getSid());
            ext.put("ssid", channelInfoVo.getSsid());
        }
        ext.put("userUid", userUid);
        ext.put("babyUid", anchorUid);
        if (userInfoVo != null) {
            ext.put("userLogo", userInfoVo.getAvatarUrl());
            ext.put("userNick", userInfoVo.getNick());
        }

        if (babyInfoVo != null) {
            ext.put("babyLogo", babyInfoVo.getAvatarUrl());
            ext.put("babyNick", babyInfoVo.getNick());
        }
        ext.put("nickExtUsers", JsonUtil.toJson(multiNickUsers));

        if (awardConfig != null) {
            ext.put("giftName", awardConfig.getAwardName());
            ext.put("giftCount", awardConfig.getNum());
            ext.put("giftIcon", awardConfig.getAwardIcon());
            ext.put("giftUnit", awardConfig.getUnit());
        }

        String broSeq = makeKey(attr, "seq:settleHourRankAwardBro:" + preHourTimeCode);
        RetryTool.withRetryCheck(attr.getActId(), broSeq, () -> {
            commonBroadCastService.commonBannerBroadcast(0, 0, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), BroadcastType.ALL_TEMPLATE
                    , attr.getActId(), 0L, 0L, CP_RANK_BRO_BANNER_ID, 0L, ext);
        });

        //app toast
        if (awardConfig != null) {
            JSONObject noticeExt = new JSONObject();
            noticeExt.put("giftName", awardConfig.getAwardName());
            noticeExt.put("giftCount", awardConfig.getNum());
            noticeExt.put("unit", awardConfig.getUnit());
            String noticeSeq = makeKey(attr, "seq:settleHourRankAwardNotice:" + preHourTimeCode);
            RetryTool.withRetryCheck(attr.getActId(), noticeSeq, () -> {
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), getComponentId() + "_top_award_toast", JSON.toJSONString(noticeExt), StringUtils.EMPTY
                        , userUid);
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), getComponentId() + "_top_award_toast", JSON.toJSONString(noticeExt), StringUtils.EMPTY
                        , anchorUid);
            });
        }

        //---app mp4 特效
        AppBannerMp4Config mp4Config = new AppBannerMp4Config();
        List<Map<String, String>> broContentLayers = getMp4TextConfig(attr, topCpInfo.getUserUid(), topCpInfo.getAnchorUid(), userInfos);
        mp4Config.setLayerExtKeyValues(broContentLayers);
        mp4Config.setUrl(attr.getMp4Url());
        mp4Config.setLevel(attr.getMp4Level());

        String appBannerSeq = makeKey(attr, "seq:appBanner:" + preHourTimeCode);
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), appBannerSeq,
                BroadCastHelpService.toAppBroBusiness(attr.getBusiId()), FstAppBroadcastType.ALL_TEMPLATE, 0, 0, "", Lists.newArrayList());
        appBannerEvent.setMp4Config(mp4Config);
        final int mp4ContentType = 5;
        appBannerEvent.setContentType(mp4ContentType);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setUidList(Lists.newArrayList(topCpInfo.getUserUid(), topCpInfo.getAnchorUid()));
        kafkaService.sendAppBannerKafka(appBannerEvent);
    }

    private void addChatLotteryBox(CpShortTimeTaskComponentAttr attr, TopCpInfo topCpInfo, ChannelInfoVo anchorChannelInfoVo, String preHourTimeCode) {
        ChannelChatLotteryComponentAttr chatAttr = channelChatLotteryComponent.getComponentAttr(attr.getActId(), attr.getChatLotteryIndex());
        if (chatAttr == null) {
            log.info("sendChatLottery return,not config");
            return;
        }
        String seq = makeKey(attr, "seq:sendChatLottery:" + preHourTimeCode);
        ChannelChatLotteryComponent.ChatLotteryBox box = new ChannelChatLotteryComponent.ChatLotteryBox();
        box.setSeq(seq);
        box.setBabyUid(topCpInfo.getAnchorUid());
        box.setUserUid(topCpInfo.getUserUid());
        box.setBroType(BroadcastType.ALL_TEMPLATE);

        //用户跳转的频道
        if (anchorChannelInfoVo != null && anchorChannelInfoVo.getSid() > 0) {
            box.setSid(anchorChannelInfoVo.getSid());
            box.setSsid(anchorChannelInfoVo.getSsid());
        }
        channelChatLotteryComponent.addChatLotteryBox(chatAttr, box);
    }

    private ChannelInfoVo getAnchorChannelInfo(CpShortTimeTaskComponentAttr attr, long anchorUid) {
        ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(anchorUid);
        if (channelInfoVo != null && channelInfoVo.getSid() > 0) {
            return channelInfoVo;
        }
        String redisCode = getRedisGroupCode(attr.getActId());
        String key = buildAnchorLastChannelKey(attr);
        String channel = actRedisDao.hget(redisCode, key, Convert.toString(anchorUid));
        if (StringUtils.isNotBlank(channel)) {
            channelInfoVo = new ChannelInfoVo();
            String[] channelArray = channel.split("_");
            channelInfoVo.setSid(Convert.toLong(channelArray[0]));
            channelInfoVo.setSsid(Convert.toLong(channelArray[1]));
        }


        return channelInfoVo;
    }

    private List<Map<String, String>> getMp4TextConfig(CpShortTimeTaskComponentAttr attr, long userUid, long anchorUid, Map<Long, UserInfoVo> userInfos) {
        List<Map<String, String>> result = Lists.newArrayList();
        for (String key : attr.getMp4LayerExtKeyValues().keySet()) {
            Map<String, String> keyMap = Maps.newHashMap();
            String text = attr.getMp4LayerExtKeyValues().get(key);
            if (StringUtils.isNotBlank(text)) {
                //替换uid
                text = text.replace("{uidNick}", "{" + userUid + ":n}");
                text = text.replace("{anchorNick}", "{" + anchorUid + ":n}");

                UserInfoVo userInfoVo = userInfos.get(userUid);
                if (userInfoVo != null) {
                    text = text.replace("{userLogo}", userInfoVo.getAvatarUrl());
                }
                UserInfoVo babyInfoVo = userInfos.get(anchorUid);
                if (babyInfoVo != null) {
                    text = text.replace("{anchorLogo}", babyInfoVo.getAvatarUrl());
                }
            }

            keyMap.put(key, text);
            result.add(keyMap);
        }
        return result;
    }

    private AwardAttrConfig calAwardAttrConfig(CpShortTimeTaskComponentAttr attr, long score) {
        boolean awardPoolOut = getAwardPoolLeft(attr) <= 0;
        if (awardPoolOut) {
            return attr.getHourRankAward().get(-1L);
        }
        List<Long> scoreConfigs = attr.getHourRankAward().keySet().stream().sorted().toList();
        long targetScoreConfig = 0;
        for (long scoreConfig : scoreConfigs) {
            if (score >= scoreConfig) {
                targetScoreConfig = scoreConfig;
            }
        }
        return attr.getHourRankAward().get(targetScoreConfig);
    }

    private AwardAttrConfig queryHisAwardRecord(String redisCode, CpShortTimeTaskComponentAttr attr, String timeCode) {
        String awardRecordKey = buildTop1AwardRecord(attr);
        String hisRecord = actRedisDao.hget(redisCode, awardRecordKey, timeCode);
        if (StringUtil.isBlank(hisRecord)) {
            return null;
        }
        return JSON.parseObject(hisRecord, AwardAttrConfig.class);
    }


    public void settleExpireCp(CpShortTimeTaskComponentAttr attr) {
        String onGoingKey = buildCpOnGoingKey(attr);
        String redisCode = getRedisGroupCode(attr.getActId());
        //延后1s结算，让礼物都能累到
        long expireTime = calTimeRangeGetNow(attr) + 1000;
        Set<String> expireList = actRedisDao.zrevRangeByScore(redisCode, onGoingKey, 0, expireTime);
        if (!CollectionUtils.isEmpty(expireList)) {
            for (String info : expireList) {
                String[] arrayInfo = info.split(":");
                String member = arrayInfo[0];
                String timeCode = arrayInfo[1];
                String id = arrayInfo[2];
                CpInfo cpInfo = getCurCpInfo(attr, redisCode, member, timeCode);
                if (id.equals(cpInfo.getId())) {
                    settleExpireCpInfoByLock(attr, cpInfo, redisCode, timeCode);
                }
            }
        }
    }


    private long getAwardPoolLeft(CpShortTimeTaskComponentAttr attr) {
        long send = getAwardPoolConsume(attr);
        return Math.max(attr.getTotalPool() - send, 0);
    }

    private long getAwardPoolConsume(CpShortTimeTaskComponentAttr attr) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String totalAwardKey = buildTotalAwardConsumeKey(attr);
        return Convert.toLong(actRedisDao.get(redisCode, totalAwardKey), 0);
    }


    public CpInfo getCurCpInfo(CpShortTimeTaskComponentAttr attr, String redisCode, String member, String timeCode) {
        CpInfo cpInfo = null;

        String key = buildCpCurInfoKey(attr, timeCode, member);
        List<Object> infos = actRedisDao.hmGet(redisCode, key, CP_CUR_ALL_HASH_KEY);
        if (!CollectionUtils.isEmpty(infos) && infos.getFirst() != null) {
            cpInfo = new CpInfo();
            cpInfo.setId(Convert.toString(infos.get(0)));
            cpInfo.setScore(Convert.toLong(infos.get(1)));
            cpInfo.setStartTime(Convert.toLong(infos.get(2)));
            cpInfo.setEndTime(Convert.toLong(infos.get(3)));
            cpInfo.setState(Convert.toInt(infos.get(4)));
            cpInfo.setMember(member);

            CpUid cpUid = Const.splitCpMember(member);
            cpInfo.setUserUid(cpUid.getUserUid());
            cpInfo.setAnchorUid(cpUid.getAnchorUid());
        }

        return cpInfo;
    }


    private UserStatusCpMember getUserStatusCpMember(CpShortTimeTaskComponentAttr attr, long uid, String timeCode, String pointCpMember) {
        UserStatusCpMember cpMemberInfo = new UserStatusCpMember();

        long actId = attr.getActId();
        String relMemberKey = buildUserRelCpMemberKey(attr, timeCode, uid);
        String redisCode = getRedisGroupCode(actId);
        List<String> allCpMembers = StringUtil.isNotBlank(pointCpMember) ?
                Lists.newArrayList(pointCpMember)
                : Lists.newArrayList(actRedisDao.sMembers(redisCode, relMemberKey));
        List<CpInfo> cpInfos = getCurCpInfo(attr, redisCode, allCpMembers, timeCode);
        Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut = Maps.newHashMap();

        //与本人关联的cp用户信息
        List<CpMember> cpMembers = queryCpMember(attr, timeCode, cpInfos, multiNickUsersOutPut);
        cpMemberInfo.setCpMembers(cpMembers);

        //本人用户信息
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uid), multiNickUsersOutPut, attr.getTemplateType());
        if (userInfoVoMap.containsKey(uid)) {
            cpMemberInfo.setAvatarUrl(userInfoVoMap.get(uid).getAvatarUrl());
            cpMemberInfo.setNick(userInfoVoMap.get(uid).getNick());
        }

        cpMemberInfo.setNickExtUsers(multiNickUsersOutPut);
        cpMemberInfo.setServerTime(System.currentTimeMillis());

        Date beginTimeConfig = DateUtil.getDate(timeCode, DateUtil.PATTERN_TYPE7);
        Date endTime = DateUtil.addHours(beginTimeConfig, Const.ONE);
        Date now = commonService.getNow(actId);
        long leftSeconds = Math.max((endTime.getTime() - now.getTime()) / 1000, 0);
        cpMemberInfo.setLeftSeconds(leftSeconds);
        cpMemberInfo.setUid(uid);
        return cpMemberInfo;
    }


    private List<CpInfo> getCurCpInfo(CpShortTimeTaskComponentAttr attr, String redisCode, List<String> members, String timeCode) {
        List<CpInfo> result = Lists.newArrayList();

        if (CollectionUtils.isEmpty(members)) {
            return result;
        }

        byte[][] hasKey = CP_CUR_ALL_HASH_KEY.stream().map(p -> p.toString().getBytes()).toArray(byte[][]::new);
        List<Object> list = actRedisDao.getRedisTemplate(redisCode).executePipelined((RedisConnection connection) -> {
            for (String member : members) {
                String key = buildCpCurInfoKey(attr, timeCode, member);
                connection.hMGet(key.getBytes(), hasKey);
            }
            return null;
        });

        for (int i = 0; i < list.size(); i++) {
            Object obj = list.get(i);
            if (obj == null) {
                continue;
            }
            JSONArray infos = JSON.parseArray(JSON.toJSONString(obj));
            CpInfo cpInfo = new CpInfo();
            String id = Convert.toString(infos.get(0));
            if (StringUtil.isBlank(id)) {
                continue;
            }
            cpInfo.setId(id);
            cpInfo.setScore(Convert.toLong(infos.get(1)));
            cpInfo.setStartTime(Convert.toLong(infos.get(2)));
            cpInfo.setEndTime(Convert.toLong(infos.get(3)));
            cpInfo.setState(Convert.toInt(infos.get(4)));
            String member = members.get(i);
            cpInfo.setMember(member);
            CpUid cpUid = Const.splitCpMember(member);
            cpInfo.setUserUid(cpUid.getUserUid());
            cpInfo.setAnchorUid(cpUid.getAnchorUid());

            result.add(cpInfo);
        }

        return result;
    }


    private CpMemberRank queryCpMemberRank(CpShortTimeTaskComponentAttr attr, String timeCode, long uid) {
        CpMemberRank cpMemberRank = new CpMemberRank();

        String redisCode = getRedisGroupCode(attr.getActId());
        //排行榜
        Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut = Maps.newHashMap();
        List<CpMember> ranks = queryCpRankMember(attr, timeCode, multiNickUsersOutPut);
        cpMemberRank.setCpMembers(ranks);

        //第一名奖励配置
        if (!CollectionUtils.isEmpty(ranks)) {
            AwardAttrConfig award = queryHisAwardRecord(redisCode, attr, timeCode);
            if (award == null) {
                award = calAwardAttrConfig(attr, ranks.getFirst().getScore());
            }
            cpMemberRank.setTop1AwardConfig(award);
        }

        //我的cp信息
        Map<String, Map<String, MultiNickItem>> myMultiNickUsersOutPut = Maps.newHashMap();
        CpMember myData = queryMyCpRankMember(attr, timeCode, uid, myMultiNickUsersOutPut);
        cpMemberRank.setMyTopCpMember(myData);

        //多昵称
        if (MapUtils.isNotEmpty(myMultiNickUsersOutPut)) {
            multiNickUsersOutPut.putAll(myMultiNickUsersOutPut);
        }
        cpMemberRank.setNickExtUsers(multiNickUsersOutPut);

        //奖池
        cpMemberRank.setTotalAwardLimit(attr.getTotalPool() / 1000);
        cpMemberRank.setTotalAwardLeft(getAwardPoolLeft(attr) / 1000);

        return cpMemberRank;
    }

    private List<CpMember> queryCpMember(CpShortTimeTaskComponentAttr attr, String timeCode, List<CpInfo> cpInfos, Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut) {
        List<CpMember> cpMembers = Lists.newArrayList();
        if (CollectionUtils.isEmpty(cpInfos)) {
            return cpMembers;
        }

        //用户信息
        List<Long> userIds = cpInfos.stream().map(CpInfo::getUserUid).toList();
        List<Long> anchorUids = cpInfos.stream().map(CpInfo::getAnchorUid).toList();
        Set<Long> uids = Sets.newHashSet(userIds);
        uids.addAll(anchorUids);
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsersOutPut, attr.getTemplateType());

        //cp排名
        List<String> cpIds = cpInfos.stream().map(CpInfo::getId).toList();
        Map<String, Integer> cpRanks = queryCpIdRank(attr, timeCode, cpIds);


        for (CpInfo cpInfo : cpInfos) {
            CpMember cpMember = new CpMember();
            cpMember.setCpMember(cpInfo.getMember());
            cpMember.setStatus(cpInfo.getState());
            cpMember.setScore(cpInfo.getScore());
            int rank = cpRanks.getOrDefault(cpInfo.getId(), 0);
            cpMember.setRank(rank);
            long now = calTimeRangeGetNow(attr);
            long leftSeconds = Math.max((cpInfo.getEndTime() - now) / 1000, 0L);
            cpMember.setLeftSeconds(leftSeconds);
            ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(cpInfo.getAnchorUid());
            if (channelInfoVo != null) {
                cpMember.setSid(channelInfoVo.getSid());
                cpMember.setSsid(channelInfoVo.getSsid());
            }
            cpMember.setUser(userInfos.get(cpInfo.getUserUid()));
            cpMember.setAnchor(userInfos.get(cpInfo.getAnchorUid()));
            cpMembers.add(cpMember);
        }

        cpMembers = cpMembers.stream().sorted(Comparator.comparing(CpMember::getScore).reversed()).collect(Collectors.toList());
        return cpMembers;
    }

    private Map<String, Integer> queryCpIdRank(CpShortTimeTaskComponentAttr attr, String timeCode, List<String> cpIds) {
        Map<String, Integer> ranks = Maps.newHashMap();

        String redisCode = getRedisGroupCode(attr.getActId());
        String key = buildCpIdRankKey(attr, timeCode);
        List<Object> list = actRedisDao.getRedisTemplate(redisCode).executePipelined((RedisConnection connection) -> {
            for (String member : cpIds) {
                connection.zRevRank(key.getBytes(), member.getBytes());
            }
            return null;
        });

        for (int i = 0; i < list.size(); i++) {
            Object obj = list.get(i);
            if (obj == null) {
                continue;
            }
            ranks.put(cpIds.get(i), Convert.toInt(obj) + 1);
        }

        return ranks;
    }

    private List<CpMember> queryCpRankMember(CpShortTimeTaskComponentAttr attr, String timeCode, Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut) {
        List<CpMember> cpMembers = Lists.newArrayList();

        String rankKey = buildCpRankKey(attr, timeCode);
        String redisCode = getRedisGroupCode(attr.getActId());
        Set<ZSetOperations.TypedTuple<String>> ranks = actRedisDao.zrevRange(redisCode, rankKey, CP_RANK_QUERY_TOP_AMOUNT);
        if (CollectionUtils.isEmpty(ranks)) {
            return cpMembers;
        }
        List<String[]> members = ranks.stream().map(p -> Const.splitCpMemberSimple(p.getValue())).toList();
        List<Long> userIds = members.stream().map(p -> Convert.toLong(p[0])).toList();
        List<Long> anchorUids = members.stream().map(p -> Convert.toLong(p[1])).toList();
        Set<Long> uids = Sets.newHashSet(userIds);
        uids.addAll(anchorUids);
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsersOutPut, attr.getTemplateType());

        int i = Const.ONE;
        for (ZSetOperations.TypedTuple<String> rank : ranks) {
            CpMember cpMember = new CpMember();
            cpMember.setScore(Objects.requireNonNull(rank.getScore()).longValue());
            cpMember.setRank(i++);
            CpUid cpUid = Const.splitCpMember(rank.getValue());
            ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(cpUid.getAnchorUid());
            if (channelInfoVo != null) {
                cpMember.setSid(channelInfoVo.getSid());
                cpMember.setSsid(channelInfoVo.getSsid());
            }
            cpMember.setUser(userInfos.get(cpUid.getUserUid()));
            cpMember.setAnchor(userInfos.get(cpUid.getAnchorUid()));

            cpMembers.add(cpMember);
        }
        return cpMembers;
    }

    private CpMember queryMyCpRankMember(CpShortTimeTaskComponentAttr attr, String timeCode, long uid, Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut) {
        if (uid <= 0) {
            return null;
        }
        List<CpMember> cpMembers = queryMyCpRankMembers(attr, timeCode, uid, multiNickUsersOutPut);

        Optional<CpMember> cpMember = cpMembers.stream().min(Comparator.comparing(CpMember::getRank));
        return cpMember.orElse(null);
    }

    private List<CpMember> queryMyCpRankMembers(CpShortTimeTaskComponentAttr attr, String timeCode, long uid, Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String relMemberKey = buildUserRelCpMemberKey(attr, timeCode, uid);
        List<String> allCpMembers = Lists.newArrayList(actRedisDao.sMembers(redisCode, relMemberKey));

        List<String[]> members = allCpMembers.stream().map(Const::splitCpMemberSimple).toList();
        List<Long> uids = members.stream().map(p -> Convert.toLong(p[0])).collect(Collectors.toList());
        List<Long> anchorIds = members.stream().map(p -> Convert.toLong(p[1])).toList();
        uids.addAll(anchorIds);
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(uids, multiNickUsersOutPut, attr.getTemplateType());


        String rankKey = buildCpRankKey(attr, timeCode);
        List<Object> list = actRedisDao.getRedisTemplate(redisCode).executePipelined((RedisConnection connection) -> {
            for (String member : allCpMembers) {
                connection.zRevRank(rankKey.getBytes(), member.getBytes());
                connection.zScore(rankKey.getBytes(), member.getBytes());
            }
            return null;
        });
        List<CpMember> cpMembers = Lists.newArrayList();
        for (int i = 0; i < list.size() - 1; i = i + 2) {
            Object obj = list.get(i);
            if (obj == null) {
                continue;
            }
            int rank = Convert.toInt(obj) + 1;
            long score = Convert.toLong(list.get(i + 1));

            CpMember cpMember = new CpMember();
            cpMember.setRank(rank);
            cpMember.setScore(score);
            String member = allCpMembers.get(i / 2);
            cpMember.setCpMember(member);

            CpUid cpUid = Const.splitCpMember(member);
            ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(cpUid.getAnchorUid());
            if (channelInfoVo != null) {
                cpMember.setSid(channelInfoVo.getSid());
                cpMember.setSsid(channelInfoVo.getSsid());
            }
            cpMember.setUser(userInfos.get(cpUid.getUserUid()));
            cpMember.setAnchor(userInfos.get(cpUid.getAnchorUid()));

            cpMembers.add(cpMember);
        }

        cpMembers = cpMembers.stream().sorted(Comparator.comparing(CpMember::getRank)).toList();
        return cpMembers;
    }

    private List<CpMember> queryMyCpRecordMembers(CpShortTimeTaskComponentAttr attr, String timeCode, long uid,int top, Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut) {
        String redisCode = getRedisGroupCode(attr.getActId());
        String key = buildCpHourRecord(attr,timeCode,uid);
        List<String> record = actRedisDao.lrange(redisCode,key,Const.ZERO,top);
        if(CollectionUtils.isEmpty(record)) {
            return Lists.newArrayList();
        }
        List<CpInfo> cpInfos = Lists.newArrayList();
        record.forEach(content -> {
            if (StringUtil.isNotBlank(content)) {
                CpInfo cpInfo = JSON.parseObject(content, CpInfo.class);
                cpInfos.add(cpInfo);
            }
        });

        List<Long> uids = cpInfos.stream().map(CpInfo::getUserUid).collect(Collectors.toList());
        List<Long> anchorIds = cpInfos.stream().map(CpInfo::getAnchorUid).toList();
        uids.addAll(anchorIds);
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(uids, multiNickUsersOutPut, attr.getTemplateType());


        List<CpMember> cpMembers = Lists.newArrayList();
        for (CpInfo cpInfo : cpInfos) {
            CpMember cpMember = new CpMember();
            cpMember.setScore(cpInfo.getScore());
            cpMember.setCpMember(cpInfo.getMember());
            ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(cpInfo.getAnchorUid());
            if (channelInfoVo != null) {
                cpMember.setSid(channelInfoVo.getSid());
                cpMember.setSsid(channelInfoVo.getSsid());
            }
            cpMember.setUser(userInfos.get(cpInfo.getUserUid()));
            cpMember.setAnchor(userInfos.get(cpInfo.getAnchorUid()));
            cpMember.setCreateTime(cpInfo.getStartTime());
            cpMembers.add(cpMember);
        }

        return cpMembers;
    }

    private List<CpMember> queryMyCpRecord(CpShortTimeTaskComponentAttr attr, String timeCode, long uid, Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut) {
        List<CpMember> cpMembers = queryMyCpRankMembers(attr, timeCode, uid, multiNickUsersOutPut);
        if (CollectionUtils.isEmpty(cpMembers)) {
            return cpMembers;
        }

        //填充开始时间
        List<String> members = cpMembers.stream().map(CpMember::getCpMember).toList();
        List<Object> list = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId())).executePipelined((RedisConnection connection) -> {
            for (String member : members) {
                String key = buildCpRankExtKey(attr, timeCode, member);
                connection.hGet(key.getBytes(), CP_CUR_HASH_KEY_START_TIME.getBytes());
            }
            return null;
        });
        for (int i = 0; i < list.size(); i++) {
            Object startTIme = list.get(i);
            if (startTIme == null) {
                continue;
            }
            CpMember cpMember = cpMembers.get(i);
            cpMember.setCreateTime(Convert.toLong(startTIme, 0L));
        }
        return cpMembers;
    }


    public void insertOrReplaceCurCpInfo(CpShortTimeTaskComponentAttr attr, String redisCode, String sourceSeq, String member, String timeCode) {
        String key = buildCpCurInfoKey(attr, timeCode, member);
        long startTime = calTimeRangeGetNow(attr);
        long endTime = calCurCpEndTime(attr, startTime, timeCode);

        //活动结束

        Map<String, String> cpInfo = Maps.newHashMap();
        String id = UUID.randomUUID().toString();
        cpInfo.put(CP_CUR_HASH_KEY_ID, id);
        cpInfo.put(CP_CUR_HASH_KEY_SCORE, "0");
        cpInfo.put(CP_CUR_HASH_KEY_START_TIME, Convert.toString(startTime));
        cpInfo.put(CP_CUR_HASH_KEY_END_TIME, Convert.toString(endTime));
        cpInfo.put(CP_CUR_HASH_KEY_STATE, "0");
        actRedisDao.hmset(redisCode, key, cpInfo);

        //增加全局正在累的cp记录，用于结算
        String goIngKey = buildCpOnGoingKey(attr);
        actRedisDao.zAdd(redisCode, goIngKey, buildOnGoingMember(member, timeCode, id), endTime);

        log.info("insertCurCpInfo done,actId:{},member:{},timeCode:{},cpInfo:{}", attr.getActId(), member, timeCode, JSON.toJSONString(cpInfo));
    }

    private long calCurCpEndTime(CpShortTimeTaskComponentAttr attr, long startTime, String timeCode) {
        //用物理时间，方便测试
        long endTime = startTime + attr.getCpDurSeconds() * 1000;
        long curHour = DateUtil.getHours(new Date(startTime));
        long endTimeHour = DateUtil.getHours(new Date(endTime));
        //跨小时了，以本小时结束为准
        if (curHour != endTimeHour) {
            Date startTimeHourBegin = DateUtil.getHourBeginTime(new Date(startTime));
            endTime = startTimeHourBegin.getTime() + DateUtil.ONE_HOUR_MILL_SECONDS;
            log.info("calCurCpEndTime curBeginTime:{},timeCode:{},endTime:{}", startTime, timeCode, endTime);
        }
        return endTime;
    }

    private void addCurCpScore(CpShortTimeTaskComponentAttr attr, String sourceSeq, String redisCode, String member, String timeCode, long score) {
        String key = buildCpCurInfoKey(attr, timeCode, member);
        String seq = makeKey(attr, "seq:add:score:" + sourceSeq);
        actRedisDao.hIncrByKeyWithSeq(redisCode, seq, key, CP_CUR_HASH_KEY_SCORE, score, DateUtil.ONE_DAY_SECONDS);
        log.info("addCurCpScore actId:{},member:{},score:{}", attr.getActId(), member, score);
    }

    private void addRelMember(CpShortTimeTaskComponentAttr attr, String member, String timeCode) {
        CpUid cpUid = Const.splitCpMember(member);

        String uidKey = buildUserRelCpMemberKey(attr, timeCode, cpUid.getUserUid());
        actRedisDao.sAdd(getRedisGroupCode(attr.getActId()), uidKey, member);

        String anchorKey = buildUserRelCpMemberKey(attr, timeCode, cpUid.getAnchorUid());
        actRedisDao.sAdd(getRedisGroupCode(attr.getActId()), anchorKey, member);
    }

    private void addLastReceiveGiftCp(CpShortTimeTaskComponentAttr attr, String redisCode, String member) {
        String key = buildAnchorLastCpKey(attr);
        CpUid cpUid = Const.splitCpMember(member);
        actRedisDao.hset(redisCode, key, Convert.toString(cpUid.getAnchorUid()), member);
    }

    private void addLastReceiveGiftChannel(RankingScoreChanged event, CpShortTimeTaskComponentAttr attr, String redisCode, String member) {
        String subChannelMember = event.getActors().get(attr.getActorSubChannelId());
        if (StringUtil.isNotBlank(subChannelMember)) {
            String key = buildAnchorLastChannelKey(attr);
            CpUid cpUid = Const.splitCpMember(member);
            actRedisDao.hset(redisCode, key, Convert.toString(cpUid.getAnchorUid()), subChannelMember);
        }
    }

    private void noticeUserLoadNewData(long actId, String member, List<String> refreshType) {
        //请求归并，避免客户端收到更新通知后，刷数据太频繁
        aggregationService.invoke(actId, "noticeUserLoadNewData:" + member, AUTO_LOAD_DATA_AGGREGATION_SECOND, () -> {
            CpUid cpUid = Const.splitCpMember(member);
            Map<String, Object> ext = Maps.newHashMap();
            ext.put("refreshType", refreshType);
            commonBroadCastService.commonNoticeUnicast(actId, "5113_refresh_user_status", member, JSON.toJSONString(ext), cpUid.getUserUid());
            commonBroadCastService.commonNoticeUnicast(actId, "5113_refresh_user_status", member, JSON.toJSONString(ext), cpUid.getAnchorUid());
        });
    }

    private boolean isCpInfoExpire(CpShortTimeTaskComponentAttr attr, CpInfo cpInfo) {
        long now = calTimeRangeGetNow(attr);
        return now > cpInfo.getEndTime();
    }

    public void settleExpireCpInfoByLock(CpShortTimeTaskComponentAttr attr, CpInfo cpInfo, String redisCode, String timeCode) {
        String lockKey = makeKey(attr, "settleExpireCpInfoByLock:" + cpInfo.getMember());
        Secret secret = null;
        try {
            secret = locker.lock(lockKey, Const.FIVE, "", Const.THIRTY, 100);
            if (secret != null) {
                settleExpireCpInfo(attr, cpInfo, redisCode, timeCode);
            } else {
                log.info("settleExpireCpInfoByLock failed,cpInfo:{},timeCode:{}", JSON.toJSONString(cpInfo), timeCode);
            }

        } catch (Exception e) {
            log.error("settleExpireCpInfoByLock error,cpInfo:{},e:{}", JSON.toJSONString(cpInfo), e.getMessage(), e);
        } finally {
            if (secret != null) {
                locker.unlock(lockKey, secret);
            }
        }
    }

    private void settleExpireCpInfo(CpShortTimeTaskComponentAttr attr, CpInfo cpInfo, String redisCode, String timeCode) {
        CpInfo curCpInfo = getCurCpInfo(attr, redisCode, cpInfo.getMember(), timeCode);
        //已经被其他人结算过了，上层必须有锁
        if (curCpInfo == null || !curCpInfo.getId().equals(cpInfo.getId())) {
            log.warn("curCpInfo expire or settled,cpInfo:{},curCpInfo:{}", JSON.toJSONString(cpInfo), JSON.toJSONString(curCpInfo));
            return;
        }
        //已结算
        if (curCpInfo.getState() > 0) {
            log.info("settleExpireCpInfo state change,cpInfo:{}", JSON.toJSONString(cpInfo));
            return;
        }


        //送到历史数据
        String cpRankKey = buildCpRankKey(attr, timeCode);
        long score = actRedisDao.zscore(redisCode, cpRankKey, cpInfo.getMember());
        //新记录替换
        int settleStatue = 0;
        if (cpInfo.getScore() > score) {
            //最高排名
            actRedisDao.zAddWithTime(redisCode, cpRankKey, cpInfo.getMember(), cpInfo.getScore(), false);
            settleStatue = 1;

            String cpRankExtKey = buildCpRankExtKey(attr, timeCode, cpInfo.getMember());
            Map<String, String> cpRankExt = ImmutableMap.of(CP_CUR_HASH_KEY_START_TIME, Convert.toString(cpInfo.getStartTime())
                    , CP_CUR_HASH_KEY_ID, cpInfo.getId());
            actRedisDao.hmset(redisCode, cpRankExtKey, cpRankExt);

            log.info("settleCpInfo add new record,actId:{},member:{},oldScore:{},newScore:{}", attr.getActId(), cpInfo.getMember(), score, cpInfo.getScore());
        } else {
            settleStatue = 2;
        }

        //所有数据排名
        String cpAllRank = buildCpIdRankKey(attr, timeCode);
        actRedisDao.zAddWithTime(redisCode, cpAllRank, cpInfo.getId(), cpInfo.getScore(), false);

        //结算标记
        String curInfoKey = buildCpCurInfoKey(attr, timeCode, cpInfo.getMember());
        actRedisDao.hset(redisCode, curInfoKey, CP_CUR_HASH_KEY_STATE, Convert.toString(settleStatue));

        //删除待结算key
        String goIngKey = buildCpOnGoingKey(attr);
        String goIngMember = buildOnGoingMember(cpInfo.getMember(), timeCode, cpInfo.getId());
        actRedisDao.zDel(redisCode, goIngKey, goIngMember);

        //cp历史数据 存档
        String cpInfoJson =JSON.toJSONString(cpInfo);
        String hisKey = makeKey(attr, String.format(CP_INFO_HIS, cpInfo.getMember()));
        actRedisDao.hset(redisCode, hisKey, cpInfo.getId(),cpInfoJson );

        //个人历史记录
        String userRecordSeq = makeKey(attr, "seq:user_record:" + cpInfo.getId());
        String userRecordKey = buildCpHourRecord(attr, timeCode, cpInfo.getUserUid());
        actRedisDao.lPushWithSeq(redisCode, userRecordSeq, userRecordKey, cpInfoJson, DateUtil.ONE_DAY_SECONDS);
        String anchorSeq = makeKey(attr, "seq:anchor_record:" + cpInfo.getId());
        String anchorRecordKey = buildCpHourRecord(attr, timeCode, cpInfo.getAnchorUid());
        actRedisDao.lPushWithSeq(redisCode, anchorSeq, anchorRecordKey, cpInfoJson, DateUtil.ONE_DAY_SECONDS);

        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
                    //更新专题页页面
                    noticeUserLoadNewData(attr.getActId(), cpInfo.getMember(), Lists.newArrayList("queryUserStatus", "rank"));
                    //更新挂件
                    ChannelInfoVo channelInfoVo = getAnchorChannelInfo(attr, cpInfo.getAnchorUid());
                    if (channelInfoVo != null && channelInfoVo.getSid() > 0) {
                        OnlineChannelInfo onlineChannelInfo = new OnlineChannelInfo();
                        onlineChannelInfo.setSid(channelInfoVo.getSid());
                        onlineChannelInfo.setSsid(channelInfoVo.getSsid());
                        broActLayerService.broChannel(attr.getActId(), onlineChannelInfo);
                    }
                }
        );

        log.info("settleExpireCpInfo done,actId:{},member:{},oldScore:{},newScore:{}", attr.getActId(), cpInfo.getMember(), score, cpInfo.getScore());
    }

    private long calTimeRangeGetNow(CpShortTimeTaskComponentAttr attr) {
        if (attr.isTimeRangeUserVirTime()) {
            return commonService.getNow(attr.getActId()).getTime();
        } else {
            return System.currentTimeMillis();
        }
    }


    @Override
    public LayerBroadcastInfo customBroadcastInTheEnd(LayerBroadcastInfo source) {
        long actId = source.getActId();
        CpShortTimeTaskComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return source;
        }
        List<LayerMemberItem> layerMemberItems = source.getExtMemberItem();
        if (CollectionUtils.isEmpty(layerMemberItems)) {
            return source;
        }
        if (source.getCurrentTime() < source.getActBeginTime()) {
            return source;
        }
        if (source.getCurrentTime() > source.getActEndTime()) {
            return source;
        }

        String redisCode = getRedisGroupCode(actId);
        Date now = commonService.getNow(attr.getActId());

        CpMember lastTopCpMember = lastHourTopCp(attr, redisCode, now);
        if (lastTopCpMember != null) {
            fillTopMemberLayerItemInfo(attr, layerMemberItems, lastTopCpMember);
        } else {
            fillCpGoingLayerItemInfo(attr, layerMemberItems, now, redisCode);
        }

        //sort
        List<LayerMemberItem> cpLast = layerMemberItems.stream()
                .filter(p -> LayerItemTypeKey.CP_LATEST.equals(p.getItemType()))
                .sorted(Comparator.comparing(LayerMemberItem::getSort))
                .collect(Collectors.toList());
        List<LayerMemberItem> notCpLast = layerMemberItems.stream()
                .filter(p -> !LayerItemTypeKey.CP_LATEST.equals(p.getItemType())).collect(Collectors.toList());

        List<LayerMemberItem> all = Lists.newArrayList(cpLast);
        all.addAll(notCpLast);
        source.setExtMemberItem(all);

        return source;

    }

    private void fillCpGoingLayerItemInfo(CpShortTimeTaskComponentAttr attr, List<LayerMemberItem> layerMemberItems, Date now, String redisCode) {
        String timeCode = DateUtil.format(now, DateUtil.PATTERN_TYPE7);
        List<Object> anchorMembers = layerMemberItems
                .stream()
                .filter(p -> LayerItemTypeKey.CP_LATEST.equals(p.getItemType()))
                .map(p -> (Object) p.getMemberId()).toList();
        if (anchorMembers.size() <= 0) {
            return;
        }

        String lastReceiveUidKey = buildAnchorLastCpKey(attr);
        List<Object> cpMembersObj = actRedisDao.hmGet(redisCode, lastReceiveUidKey, anchorMembers);
        List<String> cpMembers = cpMembersObj.stream().filter(Objects::nonNull).map(Convert::toString).toList();
        List<CpInfo> cpInfos = getCurCpInfo(attr, redisCode, cpMembers, timeCode);
        Map<String, Map<String, MultiNickItem>> multiNickUsersOutPut = Maps.newHashMap();
        List<CpMember> cpMembersInfo = queryCpMember(attr, timeCode, cpInfos, multiNickUsersOutPut);
        Map<Long, UserInfoVo> anchorUserInfo = null;
        for (LayerMemberItem layerItem : layerMemberItems) {
            if (!LayerItemTypeKey.CP_LATEST.equals(layerItem.getItemType())) {
                continue;
            }
            long babyUid = Convert.toLong(layerItem.getMemberId());
            CpMember curCpInfo = cpMembersInfo
                    .stream()
                    .filter(p -> p.getAnchor() != null && p.getAnchor().getUid().equals(babyUid))
                    .findFirst()
                    .orElse(null);
            //默认 viewStatue == 100 未送礼; viewStatue==101 飞行中 ; viewStatue==102 飞行结束 ;viewStatue==103 小时飞行王
            int viewState = 100;
            if (curCpInfo != null && curCpInfo.getScore() > 0) {
                if (layerItem.getExt() == null) {
                    layerItem.setExt(Maps.newHashMap());
                }
                if (curCpInfo.getUser() != null) {
                    layerItem.getExt().put("userUid", curCpInfo.getUser().getUid());
                    layerItem.getExt().put("userLogo", curCpInfo.getUser().getAvatarUrl());
                    layerItem.getExt().put("userNick", Base64Utils.encodeToString(Convert.toString(curCpInfo.getUser().getNick()).getBytes()));
                }
                if (curCpInfo.getAnchor() != null) {
                    layerItem.getExt().put("babyUid", curCpInfo.getAnchor().getUid());
                    layerItem.getExt().put("babyLogo", curCpInfo.getAnchor().getAvatarUrl());
                    layerItem.getExt().put("babyNick", Base64Utils.encodeToString(Convert.toString(curCpInfo.getAnchor().getNick()).getBytes()));
                }
                layerItem.getExt().put("isNew", curCpInfo.getStatus() == 1);
                layerItem.getExt().put("leftSeconds", curCpInfo.getLeftSeconds());
                layerItem.getExt().put("totalSeconds", attr.getCpDurSeconds());
                layerItem.setLeftSeconds(curCpInfo.getLeftSeconds());
                layerItem.setScore(curCpInfo.getScore());
                layerItem.setRank((int) curCpInfo.getRank());
                viewState = curCpInfo.getStatus() == 0 ? 101 : 102;
            } else {
                //主播头像
                if (anchorUserInfo == null) {
                    List<Long> anchorUid = anchorMembers.stream().map(p -> Convert.toLong(p, 0L)).collect(Collectors.toList());
                    anchorUserInfo = userInfoService.getUserInfo(anchorUid, Template.unknown);
                }
                if (anchorUserInfo != null) {
                    UserInfoVo userInfoVo = anchorUserInfo.get(Convert.toLong(layerItem.getMemberId(), 0L));
                    if (userInfoVo != null) {
                        layerItem.getExt().put("babyLogo", userInfoVo.getAvatarUrl());
                    }
                }
                //没数据的排在后面
                layerItem.setSort(Const.THIRTY);
            }
            layerItem.setViewStatus(viewState);
        }
    }

    private void fillTopMemberLayerItemInfo(CpShortTimeTaskComponentAttr attr, List<LayerMemberItem> layerMemberItems, CpMember lastTopCpMember) {
        for (LayerMemberItem layerItem : layerMemberItems) {
            if (!LayerItemTypeKey.CP_LATEST.equals(layerItem.getItemType())) {
                continue;
            }
            if (layerItem.getExt() == null) {
                layerItem.setExt(Maps.newHashMap());
            }
            if (lastTopCpMember.getUser() != null) {
                layerItem.getExt().put("userUid", lastTopCpMember.getUser().getUid());
                layerItem.getExt().put("userLogo", lastTopCpMember.getUser().getAvatarUrl());
                layerItem.getExt().put("userNick", Base64Utils.encodeToString(Convert.toString(lastTopCpMember.getUser().getNick()).getBytes()));
            }
            if (lastTopCpMember.getAnchor() != null) {
                layerItem.getExt().put("babyUid", lastTopCpMember.getAnchor().getUid());
                layerItem.getExt().put("babyLogo", lastTopCpMember.getAnchor().getAvatarUrl());
                layerItem.getExt().put("babyNick", Base64Utils.encodeToString(Convert.toString(lastTopCpMember.getAnchor().getNick()).getBytes()));
            }
            AwardAttrConfig awardConfig = queryHisAwardRecord(getRedisGroupCode(attr.getActId()), attr, lastTopCpMember.getTimeCode());
            if (awardConfig != null) {
                layerItem.getExt().put("giftLogo", awardConfig.getAwardIcon());
                layerItem.getExt().put("giftCount", awardConfig.getNum());
            }
            layerItem.setScore(lastTopCpMember.getScore());
            //默认 viewStatue == 100 未送礼; viewStatue==101 飞行中 ; viewStatue==102 飞行结束 ;viewStatue==103 小时飞行王
            final int viewState = 103;
            layerItem.setViewStatus(viewState);
        }
    }

    private CpMember lastHourTopCp(CpShortTimeTaskComponentAttr attr, String redisCode, Date now) {
        Date hourBeginTime = DateUtil.getHourBeginTime(now);
        if (now.getTime() > hourBeginTime.getTime() + attr.getCpTop1LayerShowSeconds() * 1000) {
            return null;
        }
        Date preHour = DateUtil.addHours(now, -1);
        String preHourTimeCode = DateUtil.format(preHour, DateUtil.PATTERN_TYPE7);
        String cpRankSettleKey = makeKey(attr, CP_RANK_SETTLE_RECORD);
        String tag = actRedisDao.hget(redisCode, cpRankSettleKey, preHourTimeCode);
        if (StringUtil.isEmpty(tag)) {
            return null;
        }
        TopCpInfo topCpInfo = queryHourTopCp(attr, preHourTimeCode);
        if (topCpInfo == null) {
            return null;
        }
        CpMember cpMember = new CpMember();
        List<Long> uids = Lists.newArrayList(topCpInfo.getAnchorUid(), topCpInfo.getUserUid());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfo(uids, Template.unknown);
        cpMember.setUser(userInfos.get(topCpInfo.getUserUid()));
        cpMember.setAnchor(userInfos.get(topCpInfo.getAnchorUid()));
        cpMember.setScore(topCpInfo.getScore());
        cpMember.setTimeCode(preHourTimeCode);
        return cpMember;
    }


    private String buildCpCurInfoKey(CpShortTimeTaskComponentAttr attr, String timeCode, String member) {
        return makeKey(attr, String.format(CP_CUR_SCORE_INFO, timeCode, member));
    }

    private String buildCpOnGoingKey(CpShortTimeTaskComponentAttr attr) {
        return makeKey(attr, CP_ON_GOING);
    }

    private String buildOnGoingMember(String member, String timeCode, String id) {
        return member + ":" + timeCode + ":" + id;
    }

    private String buildCpRankKey(CpShortTimeTaskComponentAttr attr, String timeCode) {
        return makeKey(attr, String.format(CP_RANK, timeCode));
    }

    private String buildCpRankExtKey(CpShortTimeTaskComponentAttr attr, String timeCode, String member) {
        return makeKey(attr, String.format(CP_RANK_EXT, timeCode, member));
    }

    private String buildCpIdRankKey(CpShortTimeTaskComponentAttr attr, String timeCode) {
        return makeKey(attr, String.format(CP_ID_RANK, timeCode));
    }

    private String buildTotalAwardConsumeKey(CpShortTimeTaskComponentAttr attr) {
        return makeKey(attr, TOTAL_AWARD_CONSUME);
    }

    private String buildUserRelCpMemberKey(CpShortTimeTaskComponentAttr attr, String timeCode, long uid) {
        return makeKey(attr, String.format(CP_REL_MEMBER, timeCode, uid));
    }

    private String buildAnchorLastCpKey(CpShortTimeTaskComponentAttr attr) {
        return makeKey(attr, ANCHOR_LAST_CP);
    }

    private String buildAnchorLastChannelKey(CpShortTimeTaskComponentAttr attr) {
        return makeKey(attr, ANCHOR_LAST_CHANNEL);
    }

    private String buildTop1AwardRecord(CpShortTimeTaskComponentAttr attr) {
        return makeKey(attr, TOP1_AWARD_RECORD);
    }

    private String buildCpHourRecord(CpShortTimeTaskComponentAttr attr, String timeCode, long uid) {
        return makeKey(attr, String.format(CP_HOUR_RECORD, timeCode, uid));
    }

    @Data
    public static class CpInfo {
        private String id;
        private String member;
        private long userUid;
        private long anchorUid;
        private int state;
        private long score;
        private long startTime;
        private long endTime;
    }

    @Data
    public static class TopCpInfo {
        private long userUid;
        private long anchorUid;
        private long score;
    }

    /**
     * 我的飞行状态
     */
    @Data
    public static class UserStatusCpMember {
        private long uid;
        private long serverTime;
        private long leftSeconds;
        private String nick;
        private String avatarUrl;
        private List<CpMember> cpMembers;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    public static class CpMember {
        private String cpMember;
        private int status;
        private long score;
        private long rank;
        private long leftSeconds;
        private long sid;
        private long ssid;
        private long createTime;
        private UserInfoVo user;
        private UserInfoVo anchor;
        private String timeCode;

    }

    /**
     * 全服飞行记录
     */
    @Data
    public static class CpMemberRank {
        private AwardAttrConfig top1AwardConfig;
        private CpMember myTopCpMember;
        private List<CpMember> cpMembers;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private long totalAwardLimit;
        private long totalAwardLeft;
    }


}
