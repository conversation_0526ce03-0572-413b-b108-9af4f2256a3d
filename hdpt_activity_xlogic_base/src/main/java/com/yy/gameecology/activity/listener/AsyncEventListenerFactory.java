package com.yy.gameecology.activity.listener;

import com.yy.gameecology.activity.listener.annotation.AsyncEventListener;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.EventListenerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 异步事件监听器的工厂方法，对AsyncEventListener注解的方法生成异步执行的监听器
 *  非异步的工厂是：DefaultEventListenerFactory
 *  按order顺序执行，order 必须比DefaultEventListenerFactory的小
 *
 * @Author: CXZ
 * @Desciption:
 * @Date: 2021/6/1 19:06
 * @Modified:
 */
@Component
public class AsyncEventListenerFactory implements EventListenerFactory, Ordered {
    private int order = LOWEST_PRECEDENCE - 1;


    public void setOrder(int order) {
        this.order = order;
    }

    @Override
    public int getOrder() {
        return this.order;
    }

    /**
     * 只处理有  AsyncEventListener 注解的方法
     *
     * @param method
     * @return
     */
    @Override
    public boolean supportsMethod(Method method) {
        return AnnotatedElementUtils.findMergedAnnotation(method, AsyncEventListener.class) != null;
    }

    @Override
    public ApplicationListener<?> createApplicationListener(String beanName, Class<?> type, Method method) {
        return new AsyncListenerMethodAdapter(beanName, type, method);
    }
}
