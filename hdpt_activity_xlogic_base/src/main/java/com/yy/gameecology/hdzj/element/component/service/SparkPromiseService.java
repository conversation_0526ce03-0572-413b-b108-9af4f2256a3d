package com.yy.gameecology.hdzj.element.component.service;

import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5123SparkRecord;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5123UserCard;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.redis.SparkPromiseComponent;
import com.yy.gameecology.hdzj.element.component.attr.SparkPromiseComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.SparkPromiseDao;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Slf4j
@Service
public class SparkPromiseService {

    @Autowired
    private SparkPromiseDao sparkPromiseDao;

    @Autowired
    protected HdztAwardServiceClient hdztAwardServiceClient;

    public Cmpt5123SparkRecord getSparkRecordByRecordId(long recordId) {
        return sparkPromiseDao.getSparkRecordByRecordId(recordId);
    }

    @Transactional(rollbackFor = Throwable.class)
    public Pair<Long, Long> taskComplete(SparkPromiseComponentAttr attr, String memberId, String dateCode, int taskIndex, int currentIndex, long sid, long ssid, Date time) {
        long anchorUid = Long.parseLong(memberId);
        // 抽奖
        String seq = String.format("spark_lottery:%s:%s:%d", memberId, dateCode, taskIndex);
        BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(seq, attr.getBusiId(), anchorUid, attr.getSparkLotteryTaskId(), 3);
        if (result.getCode() != 0 || MapUtils.isEmpty(result.getRecordPackages())) {
            throw new RuntimeException("doBatchLottery fail code:" + result.getCode());
        }

        final long packageId = result.getRecordPackages().values().stream().findFirst().orElse(0L);
        Cmpt5123SparkRecord record = new Cmpt5123SparkRecord();
        record.setActId(attr.getActId());
        record.setMemberId(memberId);
        record.setDateCode(dateCode);
        record.setTaskIndex(taskIndex);
        record.setSid(sid);
        record.setSsid(ssid);
        record.setPackageId(packageId);
        record.setEffect(taskIndex == currentIndex ? 1 : 0);
        record.setCreateTime(time);
        int added = sparkPromiseDao.insertIgnore(record);
        if (added <= 0) {
            log.warn("taskComplete insert ignore added:{}", added);
            return null;
        }

        return Pair.of(record.getId(), packageId);
    }

    public Cmpt5123SparkRecord getLatestSparkRecord(SparkPromiseComponentAttr attr, long sid, long ssid, Date now) {
        int seconds = (int) attr.getSparkDuration().toSeconds();
        Date floorTime = DateUtils.addSeconds(now, -seconds);
        String dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);
        return sparkPromiseDao.getLatestEffectSpark(attr.getActId(), sid, ssid, dateCode, floorTime);
    }

    public Cmpt5123SparkRecord getLatestSparkRecord(SparkPromiseComponentAttr attr, String memberId, Date now) {
        int seconds = (int) attr.getSparkDuration().toSeconds();
        Date floorTime = DateUtils.addSeconds(now, -seconds);
        String dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);
        return sparkPromiseDao.getLatestEffectSpark(attr.getActId(), memberId, dateCode, floorTime);
    }

    public List<Cmpt5123SparkRecord> getSparkRecordList(SparkPromiseComponentAttr attr, int size, Date now) {
        int seconds = (int) attr.getSparkDuration().toSeconds();
        Date floorTime = DateUtils.addSeconds(now, -seconds);
        size = Math.min(100, size);
        List<Cmpt5123SparkRecord> sparkRecords = sparkPromiseDao.getEffectSparkList(attr.getActId(), floorTime, 600);
        if (CollectionUtils.isEmpty(sparkRecords)) {
            return sparkRecords;
        }

        Set<String> halls = new HashSet<>(sparkRecords.size());
        List<Cmpt5123SparkRecord> result = new ArrayList<>(size);
        for (Cmpt5123SparkRecord record : sparkRecords) {
            String hall = record.getSid() + StringUtil.UNDERSCORE + record.getSsid();
            if (halls.contains(hall)) {
                continue;
            }

            halls.add(hall);
            result.add(record);

            if (result.size() >= size) {
                break;
            }
        }

        return result;
    }

    public Cmpt5123UserCard getSparkUserCard(long actId, long recordId, long uid) {
        return sparkPromiseDao.getUserCard(actId, recordId, uid);
    }

    public boolean tryAddUserCard(long actId, long recordId, long uid, int cardState, int effectState, Date now) {
        return sparkPromiseDao.insertIgnore(actId, recordId, uid, cardState, effectState, now) > 0;
    }

    public void closeSparkEffect(long actId, long recordId, long uid, Date now) {
        Cmpt5123UserCard userCard = getSparkUserCard(actId, recordId, uid);
        final int rs;
        if (userCard == null) {
            rs = sparkPromiseDao.saveEffectState(actId, recordId, uid, SparkPromiseComponent.EffectState.CLOSED, now);
        } else {
            rs = sparkPromiseDao.updateSetEffectState(userCard.getId(), SparkPromiseComponent.EffectState.CLOSED);
        }
        log.info("closeSparkEffect with recordId:{} uid:{} rs:{}", recordId, uid, rs);
    }

    /**
     * 抽奖
     * @param attr
     * @param userCard
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public Pair<Long, Long> userLottery(SparkPromiseComponentAttr attr, Cmpt5123UserCard userCard) {
        String dateStr = DateFormatUtils.format(userCard.getCreateTime(), DateUtil.PATTERN_TYPE2);
        final Long taskId = attr.getUserLotteryTaskIds().get(dateStr);
        if (taskId == null) {
            log.warn("userLottery could not find taskId of date:{}", dateStr);
            userCard.setCardState(SparkPromiseComponent.CardState.UNREWARDED);
        }

        int incr = 0;
        if (userCard.getCardState() == SparkPromiseComponent.CardState.REWARDED) {
            incr = sparkPromiseDao.updateLotteryCount(userCard.getRecordId(), attr.getLotteryLimit());
            if (incr <= 0) {
                log.warn("userLottery lottery count limit");
                userCard.setCardState(SparkPromiseComponent.CardState.UNREWARDED);
            }
        }

        int rs;
        if (userCard.getId() == null) {
            rs = sparkPromiseDao.insertIgnore(userCard.getActId(), userCard.getRecordId(), userCard.getUid(), userCard.getCardState(), userCard.getEffectState(), userCard.getCreateTime());
        } else {
            rs = sparkPromiseDao.updateSetCardState(userCard.getId(), userCard.getCardState());
        }
        if (rs <= 0) {
            throw new BusinessException(400, "请求太快了，请稍刷新后重试");
        }

        if (userCard.getCardState() == SparkPromiseComponent.CardState.UNREWARDED || taskId == null) {
            return null;
        }

        String seq = String.format("spark_user_lottery:%d:%d", userCard.getRecordId(), userCard.getUid());
        BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(seq, attr.getBusiId(), userCard.getUid(), taskId, 3);
        if (result.getCode() != 0 || MapUtils.isEmpty(result.getRecordPackages())) {
            throw new BusinessException(400, "网络有点忙，请稍后再试试看吧");
        }

        long packageId = result.getRecordPackages().values().stream().findFirst().orElse(0L);
        return Pair.of(taskId, packageId);
    }
}
