package com.yy.gameecology.activity.bean.hdzt;

import java.util.List;
import java.util.Map;

/**
 * 中台抽奖消息
 *
 * <AUTHOR> 2020/7/27
 */
public class HdztAwardLotteryMsg {

    /**
     * 序列号
     **/
    private String seq;

    /**
     * 活动ID
     **/
    private long actId;

    /**
     * 用户ID
     **/
    private long uid;

    /**
     * 奖池ID
     **/
    private long taskId;

    /**
     * 业务ID
     **/
    private long busiId;

    /**
     * 奖项明细
     **/
    private List<Award> data;

    /**
     * 发奖（抽奖）时传过去的extData
     */
    private Map<String, String> extData;

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public long getUid() {
        return uid;
    }

    public void setUid(long uid) {
        this.uid = uid;
    }

    public long getTaskId() {
        return taskId;
    }

    public void setTaskId(long taskId) {
        this.taskId = taskId;
    }

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public List<Award> getData() {
        return data;
    }

    public void setData(List<Award> data) {
        this.data = data;
    }

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public Map<String, String> getExtData() {
        return extData;
    }

    public void setExtData(Map<String, String> extData) {
        this.extData = extData;
    }

    public static class Award {
        /**
         * 通知类型：0-单播，1-广播
         */
        private int noticeType;
        /**
         * 中奖标识
         */
        private long recordId;
        /**
         * 受益人
         */
        private long uid;
        /**
         * 业务标识, 0-未知
         */
        private long busiId;
        /**
         * 奖包标识
         */
        private long packageId;
        /**
         * 奖包发放的数量（动态值，等于发放请求中要求的数量）
         */
        private long packageNum;
        /**
         * 奖项标识
         */
        private long itemId;
        /**
         * 礼物类型
         */
        private long giftType;
        /**
         * 礼物编码，可和业务系统对应
         */
        private String giftCode;
        /**
         * 礼物名称
         */
        private String giftName;
        /**
         * 礼物数量（！！！注意，这个是配置的静态值，表示每个包中的礼物个数， 所以发放请求的全部礼物数量 = packageNum * giftNum）
         */
        private long giftNum;
        /**
         * 任务标识
         */
        private long taskId;

        private String packageName;

        private String packageUnit;

        /**
         *奖包图片
         */
        private String packageImage;

        /**
         *
         */
        private String packageChoiceImage;

        /**
         *
         */
        private String packageMouseoverTips;

        /**
         *
         */
        private String packageSkipUrl;

        /**
         *
         */
        private String packageRemark;

        /**
         *
         */
        private String packageExtjson;

        /**
         * 前端展示用的扩展信息，完全透传给前端，不能放敏感信息
         */
        private String packageViewExtjson;


        public int getNoticeType() {
            return noticeType;
        }

        public void setNoticeType(int noticeType) {
            this.noticeType = noticeType;
        }

        public long getRecordId() {
            return recordId;
        }

        public void setRecordId(long recordId) {
            this.recordId = recordId;
        }

        public long getUid() {
            return uid;
        }

        public void setUid(long uid) {
            this.uid = uid;
        }

        public long getBusiId() {
            return busiId;
        }

        public void setBusiId(long busiId) {
            this.busiId = busiId;
        }

        public long getPackageId() {
            return packageId;
        }

        public void setPackageId(long packageId) {
            this.packageId = packageId;
        }

        public long getPackageNum() {
            return packageNum;
        }

        public void setPackageNum(long packageNum) {
            this.packageNum = packageNum;
        }

        public long getItemId() {
            return itemId;
        }

        public void setItemId(long itemId) {
            this.itemId = itemId;
        }

        public long getGiftType() {
            return giftType;
        }

        public void setGiftType(long giftType) {
            this.giftType = giftType;
        }

        public String getGiftCode() {
            return giftCode;
        }

        public void setGiftCode(String giftCode) {
            this.giftCode = giftCode;
        }

        public String getGiftName() {
            return giftName;
        }

        public void setGiftName(String giftName) {
            this.giftName = giftName;
        }

        public long getGiftNum() {
            return giftNum;
        }

        public void setGiftNum(long giftNum) {
            this.giftNum = giftNum;
        }

        public long getTaskId() {
            return taskId;
        }

        public void setTaskId(long taskId) {
            this.taskId = taskId;
        }

        public String getPackageImage() {
            return packageImage;
        }

        public void setPackageImage(String packageImage) {
            this.packageImage = packageImage;
        }

        public String getPackageChoiceImage() {
            return packageChoiceImage;
        }

        public void setPackageChoiceImage(String packageChoiceImage) {
            this.packageChoiceImage = packageChoiceImage;
        }

        public String getPackageMouseoverTips() {
            return packageMouseoverTips;
        }

        public void setPackageMouseoverTips(String packageMouseoverTips) {
            this.packageMouseoverTips = packageMouseoverTips;
        }

        public String getPackageSkipUrl() {
            return packageSkipUrl;
        }

        public void setPackageSkipUrl(String packageSkipUrl) {
            this.packageSkipUrl = packageSkipUrl;
        }

        public String getPackageRemark() {
            return packageRemark;
        }

        public void setPackageRemark(String packageRemark) {
            this.packageRemark = packageRemark;
        }

        public String getPackageExtjson() {
            return packageExtjson;
        }

        public void setPackageExtjson(String packageExtjson) {
            this.packageExtjson = packageExtjson;
        }

        public String getPackageViewExtjson() {
            return packageViewExtjson;
        }

        public void setPackageViewExtjson(String packageViewExtjson) {
            this.packageViewExtjson = packageViewExtjson;
        }

        public String getPackageName() {
            return packageName;
        }

        public void setPackageName(String packageName) {
            this.packageName = packageName;
        }

        public String getPackageUnit() {
            return packageUnit;
        }

        public void setPackageUnit(String packageUnit) {
            this.packageUnit = packageUnit;
        }
    }

}
