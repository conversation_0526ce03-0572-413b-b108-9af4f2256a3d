package com.yy.gameecology.hdzj.delay;

import com.yy.gameecology.common.utils.MDCUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import io.opentelemetry.context.Context;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.scheduling.config.FixedDelayTask;

import java.util.List;
import java.util.function.BiConsumer;

@Slf4j
public class DelayTask<T extends ComponentAttr> extends FixedDelayTask {

    private static final String POP_SCRIPT = """
            local result = redis.call('ZRANGEBYSCORE', KEYS[1], ARGV[1], ARGV[2], 'LIMIT', 0, ARGV[3])
            for i, member in ipairs(result) do
            	redis.call('ZREM', KEYS[1], member)
            end
                        
            return result
            """;

    private static final RedisScript<List> SCRIPT = new DefaultRedisScript<>(POP_SCRIPT, List.class);

    private static final RedisSerializer SERIALIZER = RedisSerializer.json();

    /**
     * 延迟消费对象
     * @param component 组件对象
     * @param key key
     * @param actId 活动ID
     * @param cmptUseInx 组件索引
     * @param consumer 延迟消息消费者
     */
    public DelayTask(BaseActComponent<T> component, String key, long actId, long cmptUseInx, BiConsumer<T, Object> consumer) {
        super(() -> {
            String origin = String.format("timer=delayTak_%s_%s_%d_%d", component.getClass().getSimpleName(), key, actId, cmptUseInx);
            MDCUtils.putContext(origin);
            try {
                // 这里每次取最新的attr
                T attr = component.getComponentAttr(actId, cmptUseInx);
                if (attr == null) {
                    return;
                }

                String delayKey = component.makeKey(attr, key);
                StringRedisTemplate redisTemplate = component.getFixRedisTemplate(actId);
                try {
                    List<Object> rs = redisTemplate.execute(SCRIPT, RedisSerializer.string(), SERIALIZER, List.of(delayKey), "0", String.valueOf(System.currentTimeMillis()), "10");
                    log.info("zpop with key:{} rs:{}", delayKey, rs);
                    if (CollectionUtils.isEmpty(rs)) {
                        return;
                    }

                    rs.forEach(obj -> consumer.accept(attr, obj));
                } catch (Exception e) {
                    log.error("execute delay consume fail:", e);
                }
            } finally {
                MDCUtils.clearContext();
            }

        }, 3000, 5000);
    }
}
