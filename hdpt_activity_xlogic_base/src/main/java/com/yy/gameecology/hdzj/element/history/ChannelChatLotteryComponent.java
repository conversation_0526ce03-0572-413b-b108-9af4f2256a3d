package com.yy.gameecology.hdzj.element.history;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.bean.ChannelChatTextInnerEvent;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.MD5SHAUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ChannelChatLotteryComponentAttr;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * 使用ChannelWatchwordLotteryComponent替代
 */
@Deprecated
@RequestMapping("/5114")
@RestController
@Component
public class ChannelChatLotteryComponent extends BaseActComponent<ChannelChatLotteryComponentAttr> {

    /**
     * 送祝福、抽大奖
     */
    private final String CHAT_LOTTERY_BOX ="chat_lottery_box";

    private final String SEND_CHAT_LOTTERY_SEQ = "send_chat_lottery_%s";

    private static final String CHANNEL_CHAT_AWARD = "5114_chatAward";

    private static final long LOTTERY_BRO_BANNER_ID = 5114001L;

    private static final long MINUTE_MILLIS = (60*1000);

//    @Autowired
//    protected ActRedisGroupDao actRedisDao;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;



    @Override
    public Long getComponentId() {
        return ComponentId.CHANNEL_CHAT_LOTTERY;
    }

    public void addChatLotteryBox(long actId, long cmptUseInx, ChatLotteryBox lotteryBox) {
        var attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            throw new IllegalArgumentException("component not found");
        }

        addChatLotteryBox(attr, lotteryBox);
    }

    /**
     *
     * @param attr
     * @param lotteryBox
     */
    public void addChatLotteryBox(ChannelChatLotteryComponentAttr attr, ChatLotteryBox lotteryBox) {
        long nowTime = System.currentTimeMillis();
        double score = nowTime + attr.getExpireMinutes()*MINUTE_MILLIS;
        String redisGroup = getRedisGroupCode(attr.getActId());
        String lotteryBoxKey = makeKey(attr, CHAT_LOTTERY_BOX);

        BestCpInfo bestCp = new BestCpInfo();
        bestCp.setSeq(lotteryBox.getSeq());
        bestCp.setUserUid(lotteryBox.getUserUid());
        bestCp.setBabyUid(lotteryBox.getBabyUid());
        bestCp.setSid(lotteryBox.getSid());
        bestCp.setSsid(lotteryBox.getSsid());
        bestCp.setFamilyId(lotteryBox.getFamilyId());
        bestCp.setBoxBroType(lotteryBox.getBroType());
        bestCp.setChatText(attr.getChatText());
        bestCp.setExpireTime(nowTime+attr.getExpireMinutes()*MINUTE_MILLIS);
        bestCp.setScore(lotteryBox.getScore());
        actRedisDao.zAdd(redisGroup, lotteryBoxKey, JsonUtil.toJson(bestCp), score);

        if(lotteryBox.getSid()==null || lotteryBox.getSid()<=0){
            log.warn("addChatLotteryBox sid is illegal,sid:{},ssid:{},seq:{}",lotteryBox.getSid(),lotteryBox.getSsid(),lotteryBox.getSeq());
        }
        log.info("addChatLotteryBox  lotteryBoxKey:{}, babyCpInfo:{}", lotteryBoxKey, JsonUtil.toJson(bestCp));

    }

    public ChatLotteryBoxListRsp getChatLotteryBox(long actId, long cmptUseInx) {

        ChatLotteryBoxListRsp rsp = new ChatLotteryBoxListRsp();
        ChannelChatLotteryComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        String lotteryBoxKey = makeKey(attr, CHAT_LOTTERY_BOX);
        long nowTime = System.currentTimeMillis();
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                lotteryBoxKey, nowTime, Long.MAX_VALUE);

        List<BestCpInfo> cpInfos = Lists.newArrayList();
        Set<Long> uids = new HashSet<>();
        for (String raw : rawSet) {
            BestCpInfo bestCp = JsonUtils.deserialize(raw, BestCpInfo.class);
            if(bestCp.getBabyUid()>0){
                uids.add(bestCp.getBabyUid());
            }
            if(bestCp.getUserUid()>0){
                uids.add(bestCp.getUserUid());
            }
            cpInfos.add(bestCp);
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers,attr.getTemplateType());
        for(BestCpInfo cpInfo: cpInfos) {
            if(cpInfo.getBabyUid()>0){
                UserInfoVo babyInfoVo =  userInfos.get(cpInfo.getBabyUid());
                if (babyInfoVo != null) {
                    cpInfo.setBabyNick(babyInfoVo.getNick());
                    cpInfo.setBabyLogo(babyInfoVo.getAvatarUrl());
                }
            }
            if(cpInfo.getUserUid()>0){
                UserInfoVo userInfoVo = userInfos.get(cpInfo.getUserUid());
                if(userInfoVo!=null){
                    cpInfo.setUserNick(userInfoVo.getNick());
                    cpInfo.setUserLogo(userInfoVo.getAvatarUrl());
                }
            }
        }
        rsp.setCpInfos(cpInfos);
        rsp.setNickExtUsers(multiNickUsers);
        rsp.setCurrentTime(System.currentTimeMillis());
        return rsp;
    }


    public Long getChatLotteryNum(long actId,long  cmptUseInx, String chatLotterySeq) {
        ChannelChatLotteryComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String key = makeKey(attr, String.format(SEND_CHAT_LOTTERY_SEQ, chatLotterySeq));
        Long n = actRedisDao.hlen(groupCode, key);
        return n == null ? 0L : n;
    }


    /**
     * 年度祝福CP口令列表
     */
    @RequestMapping("/getBestCp")
    public Response<ChatLotteryBoxListRsp> getBestCp(Long actId, long cmptInx) {
        ChatLotteryBoxListRsp rsp = new ChatLotteryBoxListRsp();

        ChannelChatLotteryComponentAttr attr = getComponentAttr(actId, cmptInx);
        String lotteryBoxKey = makeKey(attr, CHAT_LOTTERY_BOX);
        long nowTime = System.currentTimeMillis();
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                lotteryBoxKey, nowTime, Long.MAX_VALUE);

        List<BestCpInfo> cpInfos = Lists.newArrayList();
        Set<Long> uids = new HashSet<>();
        for (String raw : rawSet) {
            BestCpInfo bestCp = JsonUtils.deserialize(raw, BestCpInfo.class);
            if(bestCp.getBabyUid()>0){
                uids.add(bestCp.getBabyUid());
            }
            if(bestCp.getUserUid()>0){
                uids.add(bestCp.getUserUid());
            }
            cpInfos.add(bestCp);
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers,810);
        for(BestCpInfo cpInfo: cpInfos) {
            if(cpInfo.getBabyUid()>0){
                UserInfoVo babyInfoVo =  userInfos.get(cpInfo.getBabyUid());
                if (babyInfoVo != null) {
                    cpInfo.setBabyNick(babyInfoVo.getNick());
                    cpInfo.setBabyLogo(babyInfoVo.getAvatarUrl());
                }
            }
            if(cpInfo.getUserUid()>0){
                UserInfoVo userInfoVo = userInfos.get(cpInfo.getUserUid());
                if(userInfoVo!=null){
                    cpInfo.setUserNick(userInfoVo.getNick());
                    cpInfo.setUserLogo(userInfoVo.getAvatarUrl());
                }
            }
        }
        rsp.setCpInfos(cpInfos);
        rsp.setNickExtUsers(multiNickUsers);
        rsp.setCurrentTime(System.currentTimeMillis());
        return Response.success(rsp);
    }


    /**
     * 口令抽奖
     */
    @HdzjEventHandler(value = ChannelChatTextInnerEvent.class, canRetry = false)
    public void onChannelTextChatInnerEvent(ChannelChatTextInnerEvent event, ChannelChatLotteryComponentAttr attr) {
        log.info("onChannelTextChatInnerEvent event:{}", JsonUtil.toJson(event));
        long sid = event.getTopsid();
        long ssid = event.getSubsid();
        long uid = event.getUid();
        String currentTing = sid + "_" + ssid;
        String lotteryBoxKey = makeKey(attr, CHAT_LOTTERY_BOX);
        long nowTime = System.currentTimeMillis();
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                lotteryBoxKey, nowTime, Long.MAX_VALUE);
        List<BestCpInfo> cpInfos = Lists.newArrayList();
        for (String raw : rawSet) {
            BestCpInfo bestCp = JsonUtils.deserialize(raw, BestCpInfo.class);
            cpInfos.add(bestCp);
        }
        log.info("onChannelTextChatInnerEvent uid:{} currentTing:{} cpInfos:{} nowTime:{}", uid, currentTing, cpInfos, nowTime);
        String seq = "";

        Date now = commonService.getNow(attr.getActId());
        for (BestCpInfo bestCp : cpInfos) {
            String sidSsid = bestCp.getSid() + "_" + bestCp.getSsid();
            log.info("onChannelTextChatInnerEvent uid:{} sidSsid:{} bestCp:{} nowTime:{} {} {}", uid, sidSsid, bestCp, nowTime,event.getChat(),attr.getTargetWord());
            if (currentTing.equals(sidSsid)) {
                seq = bestCp.getSeq();
                if (textMatch(event.getChat(), attr.getTargetWord())) {
                    String groupCode = redisConfigManager.getGroupCode(attr.getActId());
                    String key = makeKey(attr, String.format(SEND_CHAT_LOTTERY_SEQ, seq));
                    boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
                    if (first) {
                        String time = DateUtil.format(now);
                        String lotterySeq = MD5SHAUtil.getMD5(makeKey(attr, seq + "_chat_" + uid));
                        BatchLotteryResult result = hdztAwardServiceClient.doLottery(time, attr.getBusiId(),
                                uid, attr.getLotteryTaskId(), 1, 0, lotterySeq);

                        log.info("onChannelTextChatInnerEvent uid:{} lottery result:{}", uid, JsonUtil.toJson(result));
                        Response<List<LotteryAward>> response = lotteryAward(result, attr);
                        if (response.success()) {
                            JSONObject extJson = new JSONObject();
                            boolean hit = false;
                            extJson.put("hit", false);
                            if (!response.getData().isEmpty()) {
                                LotteryAward award = response.getData().get(0);
                                extJson.put("name", award.getName());
                                extJson.put("icon", award.getImg());
                                extJson.put("awardCount", 1);
                                extJson.put("hit", true);
                                hit = true;
                            }
                            commonBroadCastService.commonNoticeUnicast(attr.getActId(), CHANNEL_CHAT_AWARD, extJson.toJSONString()
                                    , StringUtils.EMPTY, uid);

                            if (hit && attr.getBroBoxResult() > 0) {
                                broadcastLotteryResult(attr, bestCp, uid, extJson);
                            }
                            break;
                        }
                    } else {
                        log.warn("onChannelTextChatInnerEvent not first uid:{},sid:{},ssid:{},cmptUseInx:{}", uid, sid, ssid, attr.getCmptUseInx());
                    }
                } else {
                    log.info("onChannelTextChatInnerEvent not match uid:{},sid:{},ssid:{},chat:{},cmptUseInx:{}", uid, sid, ssid, event.getChat(), attr.getCmptUseInx());
                }
            }

        }

    }

    private void broadcastLotteryResult(ChannelChatLotteryComponentAttr attr,BestCpInfo bestCp,long uid, JSONObject awardInfo) {
        String seq = bestCp.getSeq();
        List<Map<String, Object>> awardInfoResult = Lists.newArrayList();
        awardInfoResult.add(ImmutableMap.of("name", awardInfo.getString("name"),
                "image", awardInfo.getString("icon")));
        //抽奖结果宝箱广播
        Map<String, Object> bpBannerExt = Maps.newHashMap();
        bpBannerExt.put("boxId", seq);
        bpBannerExt.put("userUid", uid);
        bpBannerExt.put("userAward", awardInfoResult);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(2);
        Map<Long, UserInfoVo> userInfos = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uid),multiNickUsers,attr.getTemplateType());
        bpBannerExt.put("userNick", commonService.getNickName(uid, true));
        bpBannerExt.put("nickExtUsers",multiNickUsers);

        commonBroadCastService
                .commonBannerBroadcast(bestCp.getSid(), bestCp.getSsid(), bestCp.getFamilyId()
                        , Template.findByValue(attr.getBoxBroTemplate()), bestCp.getBoxBroType(), attr.getActId()
                        , 0, 0,LOTTERY_BRO_BANNER_ID , 0L, bpBannerExt);
    }


    public Response<List<LotteryAward>> lotteryAward(BatchLotteryResult batchLotteryResult, ChannelChatLotteryComponentAttr attr) {
        if(batchLotteryResult==null){
            return Response.fail(3, "网络异常");
        }
        if (batchLotteryResult.getCode() != 0) {
            return Response.fail(3, batchLotteryResult.getReason());
        }
        List<LotteryAward> awardList = Lists.newArrayList();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Long, Long> recordIds = batchLotteryResult.getRecordPackages();
        Map<Long, Integer> pidCount = Maps.newHashMap();
        for (Long pid : recordIds.values()) {
            pidCount.merge(pid, 1, Integer::sum);
        }
        for (Map.Entry<Long, Integer> entry : pidCount.entrySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(entry.getKey());
            if (awardModelInfo != null) {
                if(awardModelInfo.getPackageName().contains("谢谢参与")) {
                    continue;
                }
                LotteryAward award = new LotteryAward();
                award.setName(awardModelInfo.getPackageName());
                award.setImg(awardModelInfo.getPackageImage());
                award.setNum(entry.getValue() == 1 ? 0 : entry.getValue());
                awardList.add(award);
            }
        }
        return Response.success(awardList);
    }

    public Map<Long, AwardModelInfo> packageInfoMap(ChannelChatLotteryComponentAttr attr) {
        try {
            var result = hdztAwardServiceClient.queryAwardTasks(attr.getLotteryTaskId());
            return result == null ? Collections.emptyMap() : result;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }

    public boolean textMatch(String chatText, String targetWord) {
        String cleanedInput = chatText.replaceAll("[\\p{P}\\p{S}\\s]+", "");
        return containsTargetSequence(cleanedInput, targetWord);
    }

    public static boolean containsTargetSequence(String input, String target) {
        int targetIndex = 0;
        for (int i = 0; i < input.length(); i++) {
            if (input.charAt(i) == target.charAt(targetIndex)) {
                targetIndex++;
                if (targetIndex == target.length()) {
                    return true;
                }
            }
        }
        return false;
    }


    @Data
    public static class LotteryAward {
        private String name;

        private String img;

        private int num;
    }

    @Data
    public static class ChatLotteryBoxListRsp {
        private long currentTime;
        private List<BestCpInfo> cpInfos;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    public static class ChatLotteryBox {
        private String seq;
        private long userUid;
        private long babyUid;
        private Long sid;
        private Long ssid;
        private long familyId;
        /**
         * 年度家族赛PK乱斗
         */
        private Long score;
        /**
         * 宝箱广播范围
         */
        private long broType;
    }

    @Data
    public static class BestCpInfo {
        private String seq;
        private long userUid;
        private String userLogo;
        private String userNick;
        private long babyUid;
        private String babyLogo;
        private String babyNick;

        private Long sid;
        private Long ssid;
        private String chatText;
        private Long expireTime;

        private long familyId;
        /**
         * 宝箱广播范围
         */
        private long boxBroType;
        private Long score;
        private Long chatLotteryNum;
    }

}
