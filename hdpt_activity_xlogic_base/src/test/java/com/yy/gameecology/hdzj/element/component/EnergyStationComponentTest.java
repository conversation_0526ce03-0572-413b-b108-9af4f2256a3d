package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.AnchorStartShowEvent;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.component.attr.EnergyStationComponentAttr;
import com.yy.gameecology.hdzj.element.redis.EnergyStationComponent;
import com.yy.thrift.hdztranking.RoleType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class EnergyStationComponentTest {
    static {
        System.setProperty("group", "1");
    }

    public static final long ACT_ID = 2022061001;

    public static final long UID = 248697624L;

    public static final long CMPT_USE_INX = 1;

    @Autowired
    private EnergyStationComponent energyStationComponent;

//    @Autowired
//    private Act2022061001Service act2022061001Service;

    @Test
    public void testEnergyStationComponentAttr() {
        EnergyStationComponentAttr attr = energyStationComponent.getComponentAttr(ACT_ID, CMPT_USE_INX);
        System.out.println("getTotalScore=" + attr.getTotalScore());
        System.out.println("\n");

        System.out.println("isTopScoreLevel(-1)=" + attr.isTopScoreLevel(-1));
        System.out.println("isTopScoreLevel(0)=" + attr.isTopScoreLevel(0));
        System.out.println("isTopScoreLevel(1)=" + attr.isTopScoreLevel(1));
        System.out.println("isTopScoreLevel(50)=" + attr.isTopScoreLevel(50));
        System.out.println("isTopScoreLevel(888)=" + attr.isTopScoreLevel(888));
        System.out.println("isTopScoreLevel(1199)=" + attr.isTopScoreLevel(1199));
        System.out.println("isTopScoreLevel(1200)=" + attr.isTopScoreLevel(1200));
        System.out.println("isTopScoreLevel(1201)=" + attr.isTopScoreLevel(1201));
        System.out.println("\n");

        System.out.println("getTotalAward=" + attr.getTotalAward());
        System.out.println("\n");

        System.out.println("isTopAwardLevel(-1)=" + attr.isTopAwardLevel(-1));
        System.out.println("isTopAwardLevel(0)=" + attr.isTopAwardLevel(0));
        System.out.println("isTopAwardLevel(1)=" + attr.isTopAwardLevel(1));
        System.out.println("isTopAwardLevel(780)=" + attr.isTopAwardLevel(780));
        System.out.println("isTopAwardLevel(34600)=" + attr.isTopAwardLevel(34600));
        System.out.println("isTopAwardLevel(49999)=" + attr.isTopAwardLevel(49999));
        System.out.println("isTopAwardLevel(50000)=" + attr.isTopAwardLevel(50000));
        System.out.println("isTopAwardLevel(50001)=" + attr.isTopAwardLevel(50001));
        System.out.println("\n");

        System.out.println("calcScoreLevel(-1)=" + attr.calcScoreLevel(-1));
        System.out.println("calcScoreLevel(0)=" + attr.calcScoreLevel(0));
        System.out.println("calcScoreLevel(1)=" + attr.calcScoreLevel(1));
        System.out.println("calcScoreLevel(49)=" + attr.calcScoreLevel(49));
        System.out.println("calcScoreLevel(50)=" + attr.calcScoreLevel(50));
        System.out.println("calcScoreLevel(51)=" + attr.calcScoreLevel(51));
        System.out.println("calcScoreLevel(1199)=" + attr.calcScoreLevel(1199));
        System.out.println("calcScoreLevel(1200)=" + attr.calcScoreLevel(1200));
        System.out.println("calcScoreLevel(1201)=" + attr.calcScoreLevel(1201));
        System.out.println("calcScoreLevel(3000)=" + attr.calcScoreLevel(3000));
        System.out.println("\n");

        System.out.println("calcAwardLevel(-1)=" + attr.calcAwardLevel(-1));
        System.out.println("calcAwardLevel(0)=" + attr.calcAwardLevel(0));
        System.out.println("calcAwardLevel(1)=" + attr.calcAwardLevel(1));
        System.out.println("calcAwardLevel(779)=" + attr.calcAwardLevel(779));
        System.out.println("calcAwardLevel(780)=" + attr.calcAwardLevel(780));
        System.out.println("calcAwardLevel(781)=" + attr.calcAwardLevel(781));
        System.out.println("calcAwardLevel(49999)=" + attr.calcAwardLevel(49999));
        System.out.println("calcAwardLevel(50000)=" + attr.calcAwardLevel(50000));
        System.out.println("calcAwardLevel(50001)=" + attr.calcAwardLevel(50001));
        System.out.println("calcAwardLevel(90000)=" + attr.calcAwardLevel(90000));
        System.out.println("\n");

        System.out.println("isZero(0)=" + attr.isZero(0));
        System.out.println("isZero(0.0)=" + attr.isZero(0.0));
        System.out.println("isZero(0.000000000005)=" + attr.isZero(0.000000000005));
        System.out.println("isZero(0.00000000001)=" + attr.isZero(0.00000000001));
        System.out.println("isZero(0.000000000015)=" + attr.isZero(0.000000000015));
        System.out.println("isZero(0.00000000002)=" + attr.isZero(0.00000000002));
        System.out.println("\n");

        System.out.println("calcScoreLevelAward(-1)=" + attr.calcScoreLevelAward(-1));
        System.out.println("calcScoreLevelAward(0)=" + attr.calcScoreLevelAward(0));
        System.out.println("calcScoreLevelAward(1)=" + attr.calcScoreLevelAward(1));
        System.out.println("calcScoreLevelAward(49)=" + attr.calcScoreLevelAward(49));
        System.out.println("calcScoreLevelAward(79)=" + attr.calcScoreLevelAward(79));
        System.out.println("calcScoreLevelAward(119)=" + attr.calcScoreLevelAward(119));
        System.out.println("calcScoreLevelAward(299)=" + attr.calcScoreLevelAward(299));
        System.out.println("calcScoreLevelAward(665)=" + attr.calcScoreLevelAward(665));
        System.out.println("calcScoreLevelAward(887)=" + attr.calcScoreLevelAward(887));
        System.out.println("calcScoreLevelAward(1199)=" + attr.calcScoreLevelAward(1199));
        System.out.println("calcScoreLevelAward(1200)=" + attr.calcScoreLevelAward(1200));
        System.out.println("calcScoreLevelAward(1201)=" + attr.calcScoreLevelAward(1201));
        System.out.println("\n");

        System.out.println("done!");
    }

    @Test
    public void createAnchorRegiment() {
        EnergyStationComponentAttr attr = energyStationComponent.getComponentAttr(ACT_ID, CMPT_USE_INX);
        AnchorStartShowEvent event = new AnchorStartShowEvent();
        event.setUid(UID);
        energyStationComponent.anchorStartShowHint(event, attr);
    }

    @Test
    public void handleRankingScoreChanged() {
        EnergyStationComponentAttr attr = energyStationComponent.getComponentAttr(ACT_ID, CMPT_USE_INX);
        RankingScoreChanged event = new RankingScoreChanged();
        event.setSeq("SEQ-" + DateUtil.today("yyyyMMddHHmmssSSS"));
        event.setRankId(41);
        event.setPhaseId(100);
        event.setMember(String.valueOf(UID));

//        int[] phaseScores = {1, 49, 50, 51, 40, 79, 80, 121, 300, 400, 500, 600, 666, 700, 800, 888, 1000, 1100, 1200, 1300};
//        int[] phaseScores = {80, 700, 1300};
        int[] phaseScores = {50, 80, 121, 300, 600, 666, 700, 888, 1100, 1200, 1300};
        for(int phaseScore : phaseScores) {
            event.setPhaseScore(phaseScore);
            energyStationComponent.handleRankingScoreChanged(event, attr);
        }

        System.out.println("done!");
    }

    @Test
    public void anchorEnergy() {
        energyStationComponent.myStatus(ACT_ID,  1, String.valueOf(UID));
    }

    @Test
    public void energyStationAwardLeft() {
        EnergyStationComponentAttr attr = energyStationComponent.getComponentAttr(ACT_ID, CMPT_USE_INX);
        energyStationComponent.getAwardLeft(ACT_ID, 1);
    }

    @Test
    public void anchorAwardAll() {
        EnergyStationComponentAttr attr = energyStationComponent.getComponentAttr(ACT_ID, CMPT_USE_INX);
        energyStationComponent.isTopAwardLevel(attr, String.valueOf(UID));
    }

    @Test
    public void energyAnchorHasAward() {
        EnergyStationComponentAttr attr = energyStationComponent.getComponentAttr(ACT_ID, CMPT_USE_INX);
        energyStationComponent.getMyAwardCount(attr, String.valueOf(UID));
    }

    @Test
    public void getMemberComponentAttr() {
        EnergyStationComponentAttr attr = energyStationComponent.getMemberComponentAttr(ACT_ID, 1, "248697624");
    }

    @Test
    public void getRoleComponentAttr() {
        EnergyStationComponentAttr attr = energyStationComponent.getRoleComponentAttr(ACT_ID, 1, 50014);
    }

    @Test
    public void buildItemMemberExtInfo() {
        LayerMemberItem layerMemberItem = new LayerMemberItem();
        layerMemberItem.setRoleType(RoleType.ANCHOR.getValue());
        layerMemberItem.setRoleId(50001L);
        layerMemberItem.setMemberId("989536684");
        Map<String, Object> ext = Maps.newHashMap();
//        act2022061001Service.buildItemMemberExtInfo(ACT_ID, layerMemberItem, ext);
    }
}
