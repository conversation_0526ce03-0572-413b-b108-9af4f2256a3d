package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.ActivityTimeStart;
import com.yy.gameecology.activity.service.EnrollmentService;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RankMigrationComponentAttr;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import com.yy.thrift.hdztranking.UpdateRankingResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 该组件将一个榜单的数据累加到另一个榜单
 * <p>
 * 目前支持将活动A的榜单A累加到活动B的榜单B
 * 由活动B的ActivityTimeStart事件驱动
 * 为了防止榜单A还有数据没处理完,可以通过配置delaySeconds延迟迁移
 * <p>
 *
 * <p>
 * 榜单数据迁移与数据验证的时序性需要配置者确认配置的延迟时间
 *
 * <AUTHOR>
 * @date 2022/3/2 10:09
 **/
@UseRedisStore
@Component
public class RankMigrationComponent extends BaseActComponent<RankMigrationComponentAttr> {
    private static Logger logger = LoggerFactory.getLogger(RankMigrationComponent.class);

    public static final String IDEMPOTENT_KEY = "RankMigration";

    /**
     * 默认延迟120秒进行验证
     **/
    private static final Integer DEFAULT_DELAY_VALID_SECOND = 120;

    @Autowired
    private EnrollmentService enrollmentService;

    @Override
    public Long getComponentId() {
        return ComponentId.RANK_MIGRATION;
    }

    @EventListener(ContextRefreshedEvent.class)
    @NeedRecycle(author = "wangdonghong", endTime = "2022-12-31 00:00:00")
    public void onContextRefreshedEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() != null) {
            return;
        }

        if (SysEvHelper.isLocal()) {
            return;
        }
        String delayKey = buildDelayKey();
        registerDelayQueue(delayKey, this::delayHandle);
        log.info("initDelayThread delayKey={}", delayKey);
    }

    @HdzjEventHandler(value = ActivityTimeStart.class, canRetry = false)
    public void onActivityTimeStart(ActivityTimeStart event, RankMigrationComponentAttr attr) {
        if (event.getActId() != attr.getActId()) {
            return;
        }

        logger.info("onActivityTimeStart event={}", JSON.toJSONString(event));

        // 幂等校验
        if (!idempotentCheck(event, attr)) {
            return;
        }

        rankMigration(attr);

        valid(attr);
    }

    private boolean idempotentCheck(ActivityTimeStart event, RankMigrationComponentAttr attr) {
        long actId = event.getActId();
        String key = makeKey(attr, IDEMPOTENT_KEY);
        String groupCode = redisConfigManager.getGroupCode(actId);
        if (!actRedisDao.hsetnx(groupCode, key, event.getEkey(), DateUtil.format(new Date()))) {
            logger.info("idempotent not pass event={},key={}", JSON.toJSONString(event), key);
            return false;
        }

        return true;
    }

    private void rankMigration(RankMigrationComponentAttr attr) {
        List<RankMigrationComponentAttr.MigrationConfig> migrationConfigs = attr.getMigrationConfigs();
        if (CollectionUtils.isEmpty(migrationConfigs)) {
            logger.info("not found migration configs");
            return;
        }
        migrationConfigs.forEach(migrationConfig -> {
            RankMigrationComponentAttr.DelayEvent delayEvent = new RankMigrationComponentAttr.DelayEvent();
            delayEvent.setMigrationConfig(migrationConfig);
            delayEvent.setActId(attr.getActId());
            if (attr.getMigrationDelaySeconds() > 0) {
                publishDelayEvent(attr, buildDelayKey(), delayEvent, System.currentTimeMillis() + (long)attr.getMigrationDelaySeconds() * 1000);
            } else {
                this.doRankMigration(delayEvent);
            }
        });
    }

    /**
     * 执行榜单迁移 RankMigrationComponentAttr.DelayEvent delayEvent
     **/
    private void doRankMigration(RankMigrationComponentAttr.DelayEvent delayEvent) {
        RankMigrationComponentAttr.MigrationConfig migrationConfig = delayEvent.getMigrationConfig();
        long actId = delayEvent.getActId();
        try {
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(migrationConfig.getSourceActId(), migrationConfig.getSourceRankId()
                    , migrationConfig.getSourcePhaseId(), "", Integer.MAX_VALUE, Maps.newHashMap());
            if (CollectionUtils.isEmpty(ranks)) {
                return;
            }
            String seq = "Ranking_QY_" + UUID.randomUUID().toString().replace("-", "");
            ranks.forEach(rank -> updateRanking(actId, migrationConfig, rank, seq));
        } catch (Exception ex) {
            logger.error("doRankMigration error,migrationConfig={}", JSON.toJSONString(migrationConfig), ex);
        }
    }

    private void updateRanking(long actId, RankMigrationComponentAttr.MigrationConfig migrationConfig, Rank rank, String seq) {
        if (StringUtils.isEmpty(seq)) {
            seq = UUID.randomUUID().toString();
        }
        UpdateRankingRequest updateRankingRequest = buildUpdateRankingRequest(actId, migrationConfig, rank, seq);
        try {
            UpdateRankingResult updateRankingResult = hdztRankingThriftClient.getProxy().updateRanking(updateRankingRequest);
            logger.info("updateRanking done,request={},result={}", updateRankingRequest, updateRankingResult);
        } catch (Exception ex) {
            logger.error("doRankMigration error,migrationConfig={},rank={}", JSON.toJSONString(migrationConfig)
                    , JSON.toJSONString(rank), ex);
        }
    }

    private UpdateRankingRequest buildUpdateRankingRequest(long actId, RankMigrationComponentAttr.MigrationConfig migrationConfig
            , Rank rank, String seq) {
        UpdateRankingRequest updateRankingRequest = new UpdateRankingRequest();
        updateRankingRequest.setActId(actId);
        updateRankingRequest.setBusiId(migrationConfig.getTargetBusiId());

        updateRankingRequest.setSeq(seq + "_" + rank.getMember() + "_" + rank.getRank());
        HashMap<Long, String> actors = Maps.newHashMap();
        List<Long> actorIds = listActorIds(migrationConfig, rank.getMember());
        actorIds.forEach(actorId -> actors.put(actorId, rank.getMember()));
        updateRankingRequest.setActors(actors);
        updateRankingRequest.setItemId(migrationConfig.getTargetItemId());
        updateRankingRequest.setCount(1L);
        updateRankingRequest.setScore(rank.getScore() / migrationConfig.getBaseScore());
        updateRankingRequest.setTimestamp(commonService.getNow(actId).getTime());

        return updateRankingRequest;
    }

    private List<Long> listActorIds(RankMigrationComponentAttr.MigrationConfig migrationConfig, String member) {
        if (migrationConfig.getTargetActorId() > 0) {
            return Arrays.asList(migrationConfig.getTargetActorId());
        }

        // 如果是跨环境,这里获取不到报名信息
        List<EnrollmentInfo> enrollmentInfos = enrollmentService.getNormalEntryConfigInfo(migrationConfig.getSourceActId(), Convert.toLong(member, 0));

        return enrollmentInfos.stream().map(EnrollmentInfo::getDestRoleId).collect(Collectors.toList());
    }

    private void valid(RankMigrationComponentAttr attr) {
        if (CollectionUtils.isEmpty(attr.getValidConfigs())) {
            return;
        }

        Map<String, RankMigrationComponentAttr.MigrationConfig> migrationConfigMap = attr.getMigrationConfigs().stream().collect(Collectors.toMap(
                migrationConfig -> buildKey(migrationConfig.getSourceActId(), migrationConfig.getSourceRankId(), migrationConfig.getSourcePhaseId())
                , migrationConfig -> migrationConfig));

        attr.getValidConfigs().forEach(validConfig -> {
            int delaySecond = Math.max(attr.getMigrationDelaySeconds() + DEFAULT_DELAY_VALID_SECOND, attr.getValidDelaySeconds());
            String key = buildKey(validConfig.getSourceActId(), validConfig.getSourceRankId(), validConfig.getSourcePhaseId());

            RankMigrationComponentAttr.DelayEvent delayEvent = new RankMigrationComponentAttr.DelayEvent();
            delayEvent.setActId(attr.getActId());
            delayEvent.setValidConfig(validConfig);
            delayEvent.setMigrationConfig(migrationConfigMap.get(key));

            publishDelayEvent(attr, buildDelayKey(), delayEvent, System.currentTimeMillis() + (long)delaySecond * 1000);
        });
    }

    private String buildKey(long actId, long rankId, long phaseId) {
        return actId + ":" + rankId + ":" + phaseId;
    }

    private void doValid(RankMigrationComponentAttr.DelayEvent delayEvent) {
        RankMigrationComponentAttr.ValidConfig validConfig = delayEvent.getValidConfig();
        RankMigrationComponentAttr.MigrationConfig migrationConfig = delayEvent.getMigrationConfig();
        long actId = delayEvent.getActId();
        try {
            List<Rank> sourceRanks = hdztRankingThriftClient.queryRanking(validConfig.getSourceActId(), validConfig.getSourceRankId()
                    , validConfig.getSourcePhaseId(), "", Integer.MAX_VALUE, Maps.newHashMap());
            if (CollectionUtils.isEmpty(sourceRanks)) {
                return;
            }

            List<Rank> targetRanks = hdztRankingThriftClient.queryRanking(actId, validConfig.getTargetRankId()
                    , validConfig.getTargetPhaseId(), "", Integer.MAX_VALUE, Maps.newHashMap());

            Map<String, Rank> targetRankMap = targetRanks.stream().collect(Collectors.toMap(Rank::getMember, rank -> rank));

            String seq = "Re_Ranking_QY_" + UUID.randomUUID().toString().replace("-", "");
            sourceRanks.forEach(sourceRank -> {
                String member = sourceRank.getMember();
                Rank targetRank = targetRankMap.get(member);

                // 没累加到
                if (targetRank == null) {
                    if (migrationConfig == null) {
                        logger.error("not found target rank and migrationConfig is null,sourceRank={}", JSON.toJSONString(sourceRank));
                    } else {
                        updateRanking(actId, migrationConfig, sourceRank, seq);
                    }
                    return;
                }

                // 恢复回原来的分数,用于后面的对比
                long sourceRankScore = sourceRank.getScore() / migrationConfig.getBaseScore();

                // 因为期间发生了变化,累加少了
                if (sourceRankScore > targetRank.getScore()) {
                    if (migrationConfig == null) {
                        logger.error("target score is less than source score  and migrationConfig is null,sourceRank={}，targetRank={}"
                                , JSON.toJSONString(sourceRank), JSON.toJSONString(targetRank));
                    } else {
                        sourceRank.setScore(sourceRank.getScore() - targetRank.getScore() * migrationConfig.getBaseScore());
                        updateRanking(actId, migrationConfig, sourceRank, seq);
                    }
                    return;
                }

                // 累加多了??? 这种情况应该不会发生,记个error日志,人工介入
                if (sourceRankScore < targetRank.getScore()) {
                    logger.error("member={},source score={},target score={} less", member, sourceRank.getScore(), targetRank.getScore());
                    return;
                }

                logger.info("doValid OK");
            });
        } catch (Exception ex) {
            logger.error("doValid error,validConfig={}", JSON.toJSONString(validConfig), ex);
        }
    }

    private String buildDelayKey() {
        return "RankMigrationComponent:delay_queue";
    }

    private void delayHandle(RankMigrationComponentAttr attr, Object e) {
        if (!(e instanceof RankMigrationComponentAttr.DelayEvent delayEvent)) {
            return;
        }

        if (delayEvent.getValidConfig() != null) {
            this.doValid(delayEvent);
        } else {
            this.doRankMigration(delayEvent);
        }
    }
}
