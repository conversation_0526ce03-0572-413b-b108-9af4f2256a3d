package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.yrpc.CurrencyClient;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.activity.service.OnlineChannelService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.LotteryExtParaName;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.DragonTreasureComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.DragonTreasureInfo;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.*;
import com.yy.thrift.hdztranking.QueryRankingRequest;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 神龙秘宝
 *
 * <AUTHOR>
 * @date 2024/01/15 11:40
 */
@UseRedisStore
@Slf4j
@RestController
@RequestMapping("/5079")
public class DragonTreasureComponent extends BaseActComponent<DragonTreasureComponentAttr> {

    /**
     * service/pb uri:100023 请求类型
     */
    // 请求类型 - 秘宝题目获取
    private final static String OP_TYPE_GET_QUESTION = "getQuestion";
    // 请求类型 - 秘宝题目解秘
    private final static String OP_TYPE_ANWSER_QUESTION = "anwserQuestion";

    // 存放神龙秘宝资格的 key
    private final static String DRAGON_TREASURE_QUALIFICATION_KEY = "dragon_treasure_qualification_%s";

    // 存放神龙秘宝资产交易的 key
    private final static String DRAGON_TREASURE_TRADE_KEY = "dragon_treasure_trade_%s_%s";

    // 虚拟资产系统定的自身业务ID - 具体咨询 关啟华
    private final static int CURRENCY_BUSI_ID = 1;
    private final static int CURRENCY_NOT_ENOUGH = 3;
    private final static int CURRENCY_SUCCESS = 1;
    // 系统异常，调用者应该发起冲正
    private final static int CURRENCY_SYSTEM_ERROR = 500;

    /**
     * 秘宝解秘状态
     */
    // 还未回答
    private final static int ANWSER_STATUS_NOTHING = 0;
    // 回答正确
    private final static int ANWSER_STATUS_RIGHT = 1;
    // 回答错误
    private final static int ANWSER_STATUS_ERROR = 2;

    // 过期时间 - 90 天
    public final long expiredSeconds = 86400 * 90;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private CurrencyClient currencyClient;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private KafkaService kafkaService;

    @Override
    public Long getComponentId() {
        return ComponentId.DRAGON_TREASURE;
    }

    /**
     * 过滤送礼请求，重新发起 赐福 累榜请求, 以解决累榜临界问题（因为秘宝资格结算需要时间，结算后才知道哪些送礼可以累 赐福任务榜）
     * TODO::因 2024012001 活动取消此功能，故未完整实现，若以后又要此功能， 可以继续实现 - by guoliping / 2024-01-17
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void processRankingScoreChanged(RankingScoreChanged event, DragonTreasureComponentAttr attr) {
        // 注意临界问题 - 结合 processPhaseTimeEnd() 处理结果
    }

    /**
     * 处理虚拟货币获取任务 和 赐福任务
     */
    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void processTaskProgressChanged(TaskProgressChanged event, DragonTreasureComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();

        // 记录虚拟货币的发放
        String key = rankId + "|" + phaseId;
        List<String> list = attr.getCurrencyTaskMap().get(key);
        if (!CollectionUtils.isEmpty(list)) {
            saveCurrencyRecord(event, attr, list);
        }

        // 处理 赐福 需求
        long blessingRankId = attr.getBlessingRankId();
        long blessingPhaseId = attr.getBlessingPhaseId();
        boolean flag = blessingPhaseId < 1 || blessingPhaseId == phaseId;
        if (rankId == blessingRankId && flag) {
            processBlessing(event, attr);
        }
    }

    /**
     * 小时榜结束处理
     * 1）记录小时强厅前 X 到本地redis
     * 2）秘宝 单播通知在玩法位主持
     * 3）秘宝 全服广播通知用户
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void processPhaseTimeEnd(PhaseTimeEnd event, DragonTreasureComponentAttr attr) throws IOException {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (rankId != attr.getTreasureQualificationRankId() || phaseId != attr.getTreasureQualificationPhaseId()) {
            return;
        }

        // 时间检查，防止重试可能出现这种情况(30分钟内仍然允许重试）
        Date now = commonService.getNow(attr.getActId());
        Date endTime = DateUtil.getDate(event.getEndTime());
        if (now.getTime() - endTime.getTime() > 1800 * 1000) {
            log.error("processPhaseTimeEnd error@too late for process, event:{}, attr:{}", event, attr);
            return;
        }

        // 1. 取小时强厅前 x 到本地 redis
        String hour = DateUtil.format(endTime, DateUtil.PATTERN_TYPE7);
        List<String> memberIds = queryTreasureQualificationMember(attr, hour);
        if (CollectionUtils.isEmpty(memberIds)) {
            return;
        }
        saveTreasureQualification(event.getSeq(), attr, hour, memberIds);

        // 2. 秘宝 频道单播通知，前端要自己结合是否在座控制显示
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            doDragonTreasureNotice(event.getSeq(), attr, hour, memberIds);
        });

        // 3. 秘宝 全服广播通知用户，只执行1次
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            doDragonTreasureBroadcast(event.getSeq(), attr, hour);
        });


    }

    /**
     * 秘宝单播通知在位uid
     */
    private void doDragonTreasureNotice(String eSeq, DragonTreasureComponentAttr attr, String hour,
                                        List<String> memberIds) {
        for (int i = 0; i < memberIds.size(); i++) {
            String memberId = memberIds.get(i);
            try {
                // 准备单播的内容
                String level = getTreasureLevelFlag(i, attr);
                Map<String, Object> jsonData = ImmutableMap.of("hour", hour, "rank", i + 1,
                        "level", level);

                // 准备单播的房间（子频道）, 每个房间只执行 1 次
                ChannelInfo channelInfo = getChannelInfo(memberId, attr);
                if (channelInfo != null) {
                    String seq = "room-" + (i + 1) + "-" + eSeq;
                    RetryTool.withRetryCheck(attr.getActId(), seq, expiredSeconds, () -> {
                        OnlineChannelInfo online = onlineChannelService.get(channelInfo.getSid(), channelInfo.getSsid());
                        if (online == null) {
                            log.warn("doDragonTreasureNotice skip@hour:{}, seq:{}, memberId:{}, channelInfo:{}", hour,
                                    seq, memberId, JSON.toJSONString(channelInfo));
                        } else {
                            List<Long> guestList = online.getGuestList();
                            List<Long> compereList = online.getCompereList();
                            long compereUid = online.getCompereUid();
                            List<Long> allList = online.getEffectAnchorId();
                            log.info("doDragonTreasureNotice list@hour:{}, seq:{}, memberId:{}, guestList:{}, compereList:{}, compereUid:{}",
                                    hour, seq, memberId, JSON.toJSONString(guestList), JSON.toJSONString(compereList), compereUid);
                            String extJson = JSON.toJSONString(jsonData);
                            for (long uid : allList) {
                                commonBroadCastService.commonNoticeUnicast(attr.getActId(),
                                        "DragonTreasureNotice", "", extJson, uid);
                                log.info("doDragonTreasureNotice one@hour:{}, seq:{}, memberId:{}, ssid:{}, uid:{}",
                                        hour, seq, memberId, channelInfo.getSsid(), uid);
                            }
                        }
                    });
                }
            } catch (Throwable t) {
                log.error("doDragonTreasureNotice exception@eSeq:{}, hour:{}, memberId:{}, err:{}", eSeq, hour,
                        memberId, t.getMessage(), t);
            }
        }
    }

    /**
     * 秘宝全服广播
     */
    private void doDragonTreasureBroadcast(String eSeq, DragonTreasureComponentAttr attr, String hour) {
        try {
            String seq = "all-pc-" + eSeq;
            RetryTool.withRetryCheck(attr.getActId(), seq, expiredSeconds, () -> {
                commonBroadCastService.commonBannerBroadcast(attr.getActId(), 0, 0, "",
                        attr.getBannerId(), attr.getBannerTypeForBlessing(), ImmutableMap.of("hour", hour),
                        Template.findByValue(attr.getTemplate()));
                log.info("doDragonTreasureBroadcast pc ok@seq:{}, hour:{}", seq, hour);
            });
        } catch (Throwable t) {
            log.error("doDragonTreasureBroadcast pc exception1@eSeq:{}, hour:{}, err:{}", eSeq, hour, t.getMessage(), t);
        }

        try {
            String seq = "all-app-" + eSeq;
            RetryTool.withRetryCheck(attr.getActId(), seq, expiredSeconds, () -> {
                AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
                svgaConfig.setDuration(13);
                svgaConfig.setLoops(Const.ONE);
                svgaConfig.setSvgaURL(attr.getAppBannerSvgaUrl());
                svgaConfig.setHeight(120);
                svgaConfig.setJump(0);
                //svgaConfig.setClickLayerName("button");
                //svgaConfig.setMiniURL("https://res.yy.com/fts/mobile/plane/cs_mini_1.svga");

                Map<String, AppBannerSvgaText> svgaText = Maps.newHashMap();
                svgaText.put(attr.getAppBannerSvgaTextKey(), new AppBannerSvgaText(attr.getAppBannerContent()));
                svgaConfig.setContentLayers(Collections.singletonList(svgaText));

                AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), eSeq, attr.getAppBannerBusiId(),
                        3, 0, 0, "", Lists.newArrayList());
                appBannerEvent.setContentType(3);
                appBannerEvent.setAppId(34);
                appBannerEvent.setSvgaConfig(svgaConfig);
                kafkaService.sendAppBannerKafka(appBannerEvent);

                log.info("doDragonTreasureBroadcast app ok@seq:{}, hour:{}", seq, hour);
            });
        } catch (Throwable t) {
            log.error("doDragonTreasureBroadcast app exception2@eSeq:{}, hour:{}, err:{}", eSeq, hour, t.getMessage(), t);
        }

    }

    /**
     * 处理service通道请求
     * 1）秘宝题目获取
     * 2）秘宝题目解秘
     */
    @Report
    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        log.info("commonOperatePbRequest in@request:{}", JSON.toJSONString(request));
        DragonTreasureComponentAttr attr = getComponentAttr(request.getActId(), request.getCmptIndex());
        long opUid = request.getOpUid();
        long opSid = request.getOpSid();
        long opSsid = request.getOpSsid();
        String opType = request.getOpType();
        try {
            if (OP_TYPE_GET_QUESTION.equals(opType)) {
                return getQuestion(attr, request.getActId(), opUid, opSid, opSsid);
            } else if (OP_TYPE_ANWSER_QUESTION.equals(opType)) {
                return anwserQuestion(attr, request.getActId(), request.getSeq(), opUid, opSid, opSsid,
                        request.getExtjson());
            } else {
                return new CommonPBOperateResp(400, "", "不支持的操作");
            }
        } catch (SuperException e) {
            log.warn("commonOperatePbRequest error1,result:{},e:{}", JSON.toJSONString(request), e.getMessage(), e);
            return new CommonPBOperateResp(e.getCode(), request.getOpId(), "答题处理失败，请稍后重试！");
        } catch (Exception e) {
            log.error("commonOperatePbRequest error2,result:{},e:{}", JSON.toJSONString(request), e.getMessage(), e);
            return new CommonPBOperateResp(-2, request.getOpId(), "服务处理失败，请稍后重试！");
        }
    }

    /**
     * 查询小时秘宝资格成员列表
     */
    private List<String> queryTreasureQualificationMember(DragonTreasureComponentAttr attr, String hour) {
        int count = getHourQualificationCount(attr);
        Assert.isTrue(count > 0, "秘宝资格数量必须大于0！");
        QueryRankingRequest request = new QueryRankingRequest();
        request.setActId(attr.getActId());
        request.setRankingId(attr.getTreasureQualificationRankId());
        request.setRankingCount(count);
        request.setPhaseId(attr.getTreasureQualificationPhaseId());
        request.setDateStr(hour);
        request.setRankType("1");
        List<Rank> ranks = hdztRankService.getRanking(request);
        return ranks.stream().map(i -> i.getMember()).collect(Collectors.toList());
    }

    /**
     * 计算小时资格数量
     */
    private static int getHourQualificationCount(DragonTreasureComponentAttr attr) {
        return attr.getTreasureLevelAmounts().stream().mapToInt(i -> Integer.parseInt(i.split("\\|")[1])).sum();
    }

    /**
     * 获取成员所关联的子频道信息
     *
     * @param memberId - 一定是房间号 或者是形如  ${sid}_${ssid} 的子频道
     */
    private ChannelInfo getChannelInfo(String memberId, DragonTreasureComponentAttr attr) {
        ChannelInfo channelInfo = new ChannelInfo();
        if (attr.getRoomFlag() == 1) {
            RoomInfo roomInfo = commonService.getRoomInfoByRoomId(Integer.parseInt(memberId));
            channelInfo.setSid(roomInfo.getSid());
            channelInfo.setSsid(roomInfo.getSsid());
        } else {
            String[] strs = memberId.split("_");
            channelInfo.setSid(Long.parseLong(strs[0]));
            channelInfo.setSsid(Long.parseLong(strs[1]));
        }
        return channelInfo;
    }

    /**
     * 保存小时榜 秘宝 资格到redis中，这里对异常情况会尝试，尽量成功
     */
    private void saveTreasureQualification(String seq, DragonTreasureComponentAttr attr, String hour,
                                           List<String> memberIds) throws IOException {
        if (CollectionUtils.isEmpty(memberIds)) {
            return;
        }

        long actId = attr.getActId();
        String groupCode = getRedisGroupCode(actId);
        String key = this.makeKey(attr, String.format(DRAGON_TREASURE_QUALIFICATION_KEY, hour));

        // 分配题目
        List<DragonTreasureInfo> treasures = allocateTreasureQualification(attr, hour, memberIds);
        List<String> values = treasures.stream().map(i -> JSON.toJSONString(i)).collect(Collectors.toList());

        // 尝试10次，尽量完成题目保存工作
        int times = 10;
        Clock clock = new Clock();
        String ms = JSON.toJSONString(memberIds);
        String ts = JSON.toJSONString(treasures);
        while (true) {
            try {
                List<Object> results = this.actRedisDao.hsetBatchKey(groupCode, key, memberIds, values, expiredSeconds);
                String rs = JSON.toJSONString(results);
                log.info("saveTreasureQualification ok@seq:{}, actId:{}, key:{}, memberIds:{}, questions:{}, " +
                        "times:{}, results:{} {}", seq, actId, key, ms, ts, times, rs, clock.tag());
                return;
            } catch (Throwable t) {
                String err = t.getMessage();
                if (--times < 0) {
                    // 抛出异常让 canRetry = true 重试
                    log.error("saveTreasureQualification fail@seq:{}, actId:{}, key:{}, memberIds:{}, questions:{}, " +
                            "times:{}, err:{} {}", seq, actId, key, ms, ts, 10, err, clock.tag(), t);
                    throw new SuperException("保存秘宝资格失败", Code.E_UNKNOWN.getCode());
                } else {
                    log.warn("saveTreasureQualification exception@seq:{}, actId:{}, key:{}, memberIds:{}, " +
                            "questions:{}, times:{}, err:{}", seq, actId, key, ms, ts, times, err);
                    SysEvHelper.waiting(300);
                }
            }
        }
    }

    /**
     * 分配问题 - 从本地文件读取所有已经排好的问题，按小时 + 成员排序 获得固定问题
     */
    private List<DragonTreasureInfo> allocateTreasureQualification(DragonTreasureComponentAttr attr, String hour,
                                                                   List<String> memberIds) throws IOException {
        List<DragonTreasureInfo> result = Lists.newArrayList();
        Map<String, List<String>> map = loadTreasureQuestion(attr.getFileName(), 6, false);
        ActivityInfoVo activityInfoVo = this.actInfoService.queryActivityInfo(attr.getActId());
        Date begin = new Date(activityInfoVo.getBeginTime());
        Date end = new Date(activityInfoVo.getEndTime());
        Map<String, List<String>> hourQuestionMap = makeHourQuestionMap(attr, map, begin, end);
        for (int i = 0; i < memberIds.size(); i++) {
            String member = memberIds.get(i);
            String level = getTreasureLevelFlag(i, attr);
            String record = hourQuestionMap.get(hour).get(i);
            DragonTreasureInfo dti = DragonTreasureInfo.toDragonTreasureInfo(member, i + 1, level, hour, record);
            Assert.isTrue(dti != null, "房间分配秘宝问题失败！");
            result.add(dti);
        }
        return result;
    }

    /**
     * 制作小时问题
     *
     * @param map   - key：秘宝等级， value：该等级下的所有秘宝问题
     * @param begin - 秘宝开始时间
     * @param end   - 密保结束时间
     * @return 返回Map， key： ${秘宝出的小时（yyyyMMddHH格式）}，
     * value：按秘宝等级资格数量排布好的不重复的秘宝问题（对应排名位置有对应等级的秘宝问题）
     */
    public static Map<String, List<String>> makeHourQuestionMap(DragonTreasureComponentAttr attr,
                                                                Map<String, List<String>> map, Date begin, Date end) {
        // 先算出 begin ~ end 之间所有的小时（格式为 yyyyMMddHH）
        List<String> hours = Lists.newArrayList();
        while (!begin.after(end)) {
            int hour = DateUtil.getHours(begin);
            if (hour >= attr.getQualificationBeginHour() && hour <= attr.getQualificationEndHour()) {
                hours.add(DateUtil.format(begin, DateUtil.PATTERN_TYPE7));
            }
            begin = DateUtil.addHours(begin, 1);
        }

        // 每个小时从 level 等级对应的 questions 问题列表中取 count 个问题，取到 questions 中最后一个又从头开始取
        Map<String, List<String>> result = Maps.newTreeMap();
        for (String item : attr.getTreasureLevelAmounts()) {
            String[] levelCount = item.split("\\|");
            int inx = 0;
            String level = levelCount[0];
            int count = Integer.parseInt(levelCount[1]);
            int qs = map.get(level).size();
            for (int i = 0; i < hours.size(); i++) {
                List<String> list = result.computeIfAbsent(hours.get(i), x -> Lists.newArrayList());
                for (int j = 0; j < count; j++) {
                    String question = map.get(level).get(inx++ % qs);
                    list.add(question);
                }
            }
        }

        return result;
    }

    /**
     * 加载秘宝问题数据
     *
     * @param fileName - 文件名字
     * @param fieldNum - 问题行中 \t 分隔字段的预期数量， 不相等的行会忽略掉
     * @param bAddNo   - 是否给 记录 自动加上行号（方便核对，也作为问题的 ID）
     */
    public static Map<String, List<String>> loadTreasureQuestion(String fileName, int fieldNum, boolean bAddNo)
            throws IOException {
        String dir = "D:\\workspace\\idea\\hdpt\\hdpt_activity_xlogic\\hdpt_activity_xlogic_app\\src\\main\\resources\\";
        List<String> lines = SysEvHelper.isLocal() ? FileUtils.readLines(new File(dir + fileName)) :
                ReadFileUtil.readFromFile(fileName, "UTF-8");
        Map<String, List<String>> map = ImmutableMap.of("S", Lists.newArrayList(), "A", Lists.newArrayList());
        List<String> list = null;
        for (int i = 0; i < lines.size(); i++) {
            String line = StringUtil.trim(lines.get(i));
            if (line.isEmpty()) {
                continue;
            } else if (line.startsWith("S级题目")) {
                list = map.get("S");
                continue;
            } else if (line.startsWith("A级题目")) {
                list = map.get("A");
                continue;
            } else if (list == null) {
                continue;
            }

            String[] strs = line.split("\t");
            if (strs == null || strs.length != fieldNum) {
                log.error("loadTreasureQuestion wrong@fileName:{}, inx:{}, line:{}", fileName, i + 1, lines.get(i));
                continue;
            }

            if (bAddNo) {
                list.add(i + 1 + "\t" + line);
            } else {
                list.add(line);
            }
        }

        return map;
    }

    /**
     * 获取秘宝级别标志值，若没有对应级别，返回 null
     *
     * @param inx - 排行（从0开始）
     */
    private String getTreasureLevelFlag(int inx, DragonTreasureComponentAttr attr) {
        List<String> levelFlags = Lists.newArrayList();
        List<String> levelAmounts = attr.getTreasureLevelAmounts();
        for (int i = 0; i < levelAmounts.size(); i++) {
            String[] strs = levelAmounts.get(i).split("\\|");
            String level = strs[0];
            int amount = Integer.parseInt(strs[1]);
            for (int j = 0; j < amount; j++) {
                levelFlags.add(level);
            }
        }
        // inx + 1 必然小于等于 levelFlags.size()
        return levelFlags.get(inx);
    }

    /**
     * 交易查询
     */
    @GetMapping("/queryTrade")
    public Response<List<Object>> queryTrade(@RequestParam("actId") long actId,
                                             @RequestParam("cmptIndex") long cmptIndex,
                                             @RequestParam("type") String type, @RequestParam("pageSize") int pageSize,
                                             @RequestParam("pageNo") int pageNo) {
        DragonTreasureComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "请求参数错误！");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "未登录");
        }

        if (pageSize <= 0 || pageNo <= 0) {
            return Response.fail(405, "查询页参数必须大于0");
        }

        if (pageSize > 100) {
            return Response.fail(406, "查询页大小不能超过 100");
        }

        int start = (pageNo - 1) * pageSize;
        int end = pageNo * pageSize - 1;
        if (start < 0 || end < 0 || start > end) {
            return Response.fail(407, "查询参数过大，数值溢出！");
        }

        String key = this.makeKey(attr, String.format(DRAGON_TREASURE_TRADE_KEY, uid, type));
        final String groupCode = getRedisGroupCode(actId);
        List<String> list = this.actRedisDao.lrange(groupCode, key, start, end);

        List<Object> collect = list.stream().map(i -> {
            String[] strs = i.split("\\|");
            return ImmutableMap.of("summary", strs[0], "amount", Long.parseLong(strs[1]), "time", strs[2]);
        }).collect(Collectors.toList());

        return Response.success(collect);
    }

    /**
     * 获取用户资产余额
     */
    @GetMapping("/getBalance")
    public Response<Long> getBalance(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex,
                                     @RequestParam("cid") String cid) {
        DragonTreasureComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "请求参数错误！");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "未登录");
        }

        Map<String, Long> balances = currencyClient.balance(uid, CURRENCY_BUSI_ID, Lists.newArrayList(cid));
        if (balances == null) {
            return Response.fail(-1, "余额查询失败，请稍后再试！");
        } else {
            long amount = Convert.toLong(balances.get(cid), 0);
            return Response.success(amount);
        }
    }

    /**
     * 获取商品列表
     */
    @GetMapping("/getGoods")
    public Response<List<JSONObject>> getGoods(@RequestParam("actId") long actId,
                                               @RequestParam("cmptIndex") long cmptIndex) {
        DragonTreasureComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "请求参数错误！");
        }

        // 获取商品，！！！！ 注意下面接口有缓存 1 秒 ！！！！
        Date now = commonService.getNow(actId);
        long exchangeAwardTaskId = attr.getExchangeAwardTaskId();
        Map<Long, PackageLeftQuota> quotas = this.hdztAwardServiceClient.getTaskLeftQuota(exchangeAwardTaskId, now);
        Map<Long, Long> exchangePriceMap = attr.getExchangePriceMap();
        List<JSONObject> list = Lists.newArrayList();
        for (Long goodId : exchangePriceMap.keySet()) {
            PackageLeftQuota quota = quotas.get(goodId);
            JSONObject object = new JSONObject();
            object.put("goodId", goodId);
            object.put("goodName", quota.getPackageName());
            object.put("price", exchangePriceMap.get(goodId));
            object.put("left", quota.getTotalLeft());
            object.put("dailyLeft", quota.getDailyLeft());
            list.add(object);
        }

        return Response.success(list);
    }

    /**
     * 兑换商品
     */
    @GetMapping("/exchangeGood")
    public Response<Long> exchangeGood(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex,
                                       @RequestParam("goodId") long goodId) {
        Clock clock = new Clock();
        DragonTreasureComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "请求参数错误！");
        }

        Date now = commonService.getNow(actId);
        if (now.after(DateUtil.getDate(attr.getExchangeEndTime()))) {
            return Response.fail(402, "活动已结束");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(403, "未登录");
        }

        Long price = attr.getExchangePriceMap().get(goodId);
        if (price == null) {
            return Response.fail(-1, "不存在的商品!");
        }

        // 1. 检查用户是否已经兑换过指定商品
        String userUniqExchKey = makeKey(attr, "good_uniq_exchage");
        String goodField = uid + ":" + goodId;
        String redisGroupCode = getRedisGroupCode(actId);
        if (attr.getUserUniqExchPackageIds().containsKey(goodId)) {
            int count = Convert.toInt(actRedisDao.hget(redisGroupCode, userUniqExchKey, goodField), 0);
            int limit = attr.getUserUniqExchPackageIds().get(goodId);
            if (count >= limit) {
                return Response.fail(-41, "本商品最多只能兑换 " + limit + " 次");
            }
        }

        // 2. 获取商品余额，！！！！ 注意下面接口缓存信息（award进程里面也有） ！！！！
        long exchangeAwardTaskId = attr.getExchangeAwardTaskId();
        Map<Long, PackageLeftQuota> quotas = this.hdztAwardServiceClient.getTaskLeftQuota2(exchangeAwardTaskId, now);
        PackageLeftQuota pq = quotas.get(goodId);
        if (pq == null) {
            return Response.fail(-2, "不存在的商品!");
        }
        if (pq.getTotalLeft() != -1 && pq.getTotalLeft() <= 0) {
            return Response.fail(-3, "商品已无库存");
        }
        if (pq.getDailyLeft() != -1 && pq.getDailyLeft() <= 0) {
            return Response.fail(-31, "商品当天兑换额度已用完");
        }

        // 3. 加锁：有限额的使用 goodid 为key，否则用 uid 为key
        String subKey = (pq.getTotalLeft() != -1 || pq.getDailyLeft() != -1) ? ("gid-" + goodId) : ("uid-" + uid);
        String lockName = makeKey(attr, "exchangeGood:" + subKey);
        String seq = java.util.UUID.randomUUID().toString();
        Secret secret = locker.lock(lockName, 5, seq, 5);
        if (secret == null) {
            return Response.fail(401, "手速太快，请稍后再试！");
        }

        try {
            // 4. 上锁后，再次做余额检查，尽量避免商品余额不足情况
            quotas = this.hdztAwardServiceClient.getTaskLeftQuota2(exchangeAwardTaskId, now);
            pq = quotas.get(goodId);
            if (pq == null) {
                return Response.fail(-2, "不存在的商品!");
            }
            if (pq.getTotalLeft() != -1 && pq.getTotalLeft() <= 0) {
                return Response.fail(-3, "商品已无库存");
            }
            if (pq.getDailyLeft() != -1 && pq.getDailyLeft() <= 0) {
                return Response.fail(-31, "商品当天兑换额度已用完");
            }

            // 5. 先扣费
            String exchangeCurrencyId = attr.getExchangeCurrencyId();
            Map<String, Long> priceMap = ImmutableMap.of(exchangeCurrencyId, price);
            int ret = currencyClient.consume(uid, CURRENCY_BUSI_ID, priceMap, seq);
            log.info("currencyClient.consume done@uid:{}, busiId:{}, priceMap:{}, seq:{}", uid, CURRENCY_BUSI_ID,
                    JSON.toJSONString(priceMap), seq);
            if (ret == CURRENCY_NOT_ENOUGH) {
                return Response.fail(-4, "余额不足!");
            } else if (ret == CURRENCY_SYSTEM_ERROR) {
                currencyClient.rollback(uid, seq, CURRENCY_BUSI_ID);
                return Response.fail(-6, "扣费失败:" + ret);
            } else if (ret != CURRENCY_SUCCESS) {
                return Response.fail(-5, "扣费失败:" + ret);
            }

            // 6. 检查用户是否兑换过指定商品
            if (attr.getUserUniqExchPackageIds().containsKey(goodId)) {
                int limit = attr.getUserUniqExchPackageIds().get(goodId);
                long current = actRedisDao.hIncrByKey(redisGroupCode, userUniqExchKey, goodField, 1, expiredSeconds);
                log.info("exchangeGood hIncrByKey@@seq:{}, actId:{}, cmptIndex:{}, goodId:{}, uid:{}, current:{}, limit:{}",
                        seq, actId, cmptIndex, goodId, uid, current, limit);
                if (current > limit) {
                    currencyClient.rollback(uid, seq, CURRENCY_BUSI_ID);
                    return Response.fail(-61, "本商品兑换不能超过 " + limit + " 次！");
                }
            }

            // 7. 发放商品
            return releaseGood(attr, goodId, now, uid, price, seq, exchangeAwardTaskId, pq, exchangeCurrencyId);
        } catch (Throwable t) {
            log.error("exchangeGood exception@seq:{}, actId:{}, cmptIndex:{}, goodId:{}, uid:{}, err:{} {}", seq, actId,
                    cmptIndex, goodId, uid, t.getMessage(), clock.tag(), t);
            return Response.fail(Code.E_UNKNOWN.getCode(), "兑换操作失败，请稍后再试！");
        } finally {
            locker.unlock(lockName, secret, 5);
        }
    }

    /**
     * 发放兑换的商品
     */
    private Response<Long> releaseGood(DragonTreasureComponentAttr attr, long goodId, Date now, long uid, Long price,
                                       String seq, long exchangeAwardTaskId, PackageLeftQuota pq,
                                       String exchangeCurrencyId) {
        Clock clock = new Clock();
        Map<Long, Map<Long, Integer>> taskPackageIds = ImmutableMap.of(exchangeAwardTaskId,
                ImmutableMap.of(goodId, 1));
        BatchWelfareResult bwr = this.hdztAwardServiceClient.doBatchWelfare(seq, uid, taskPackageIds,
                DateUtil.format(now), attr.getRetry(), null);

        // 发放结果未知，只能打错误日志告警，人工介入！
        long actId = attr.getActId();
        long cmptUseInx = attr.getCmptUseInx();
        if (bwr == null) {
            log.error("releaseGood unknown@seq:{}, actId:{}, cmptIndex:{}, goodId:{}, uid:{} {}", seq, actId,
                    cmptUseInx, goodId, uid, clock.tag());
            return Response.fail(Code.E_UNKNOWN.getCode(), "请求超时，请稍后再试！");
        }

        // 明确发放成功
        int code = bwr.getCode();
        if (code == 0) {
            log.info("releaseGood ok@seq:{}, actId:{}, cmptIndex:{}, goodId:{}, uid:{}, code:{} {}", seq, actId,
                    cmptUseInx, goodId, uid, code, clock.tag());
            saveTradeRecord(seq, attr, uid + "", exchangeCurrencyId, pq.getPackageName(), -1 * price,
                    now);
            return Response.ok();
        } else {
            // 明确发失败，但安全起见，这里不 冲正退款，打告警日志，人工介入
            log.error("releaseGood fail@seq:{}, actId:{}, cmptIndex:{}, goodId:{}, uid:{}, code:{} {}", seq, actId,
                    cmptUseInx, goodId, uid, code, clock.tag());
            return Response.fail(-7, "请求超时，请稍后再试：" + code);
        }
    }

    /**
     * 验证秘宝题目加载正确性
     */
    @GetMapping("/verifyLoadQuestions")
    public Response<Integer> verifyLoadQuestions(@RequestParam("actId") long actId,
                                                 @RequestParam("cmptIndex") long cmptIndex) throws IOException {
        long uid = -1;
        try {
            DragonTreasureComponentAttr attr = getComponentAttr(actId, cmptIndex);
            if (attr == null) {
                return Response.fail(400, "请求参数错误！");
            }

            uid = getLoginYYUid();
            if (uid <= 0) {
                return Response.fail(403, "未登录");
            }

            if (uid != 50001967L) {
                return Response.fail(403, "您无权访问！");
            }

            List<DragonTreasureInfo> result = Lists.newArrayList();
            Map<String, List<String>> map = loadTreasureQuestion(attr.getFileName(), 6, false);
            ActivityInfoVo activityInfoVo = this.actInfoService.queryActivityInfo(attr.getActId());
            Date begin = new Date(activityInfoVo.getBeginTime());
            Date end = new Date(activityInfoVo.getEndTime());
            Map<String, List<String>> hourMap = makeHourQuestionMap(attr, map, begin, end);
            log.info("verifyLoadQuestions ok@uid:{}, hourMap size:{}", uid, hourMap.size());
            return Response.success(hourMap.size());
        } catch (Throwable t) {
            log.warn("verifyLoadQuestions exception@uid:{), err:{}", uid, t.getMessage(), t);
            return Response.fail(-1, t.getMessage());
        }
    }

    /**
     * 获取当前时间上一个小时的秘宝信息， 没有秘宝信息的返回 null
     */
    public DragonTreasureInfo getDragonTreasure(Date now, DragonTreasureComponentAttr attr, String memberId) {
        String hour = DateUtil.format(DateUtil.addHours(now, -1), DateUtil.PATTERN_TYPE7);
        String key = this.makeKey(attr, String.format(DRAGON_TREASURE_QUALIFICATION_KEY, hour));
        String groupCode = getRedisGroupCode(attr.getActId());
        String value = this.actRedisDao.hget(groupCode, key, memberId);
        return StringUtil.isBlank(value) ? null : JSON.parseObject(value, DragonTreasureInfo.class);
    }

    /**
     * 秘宝题目获取
     */
    private CommonPBOperateResp getQuestion(DragonTreasureComponentAttr attr, long actId, long opUid, long opSid,
                                            long opSsid) {
        RoomInfo info = commonService.getRoomInfoBySsid(opSsid);
        if (info == null) {
            return new CommonPBOperateResp(-1, "", "没有找到子频道对应的房间号");
        }

        String groupCode = getRedisGroupCode(actId);
        String hour = DateUtil.format(DateUtil.addHours(commonService.getNow(actId), -1), DateUtil.PATTERN_TYPE7);
        String key = this.makeKey(attr, String.format(DRAGON_TREASURE_QUALIFICATION_KEY, hour));
        String field = String.valueOf(info.getRoomId());
        String value = this.actRedisDao.hget(groupCode, key, field);
        if (StringUtil.isBlank(value)) {
            return new CommonPBOperateResp(-1, "", "没有找到房间的题目");
        }

        String anwserDetail = this.getAnwserDetail(opUid, info, attr, hour);
        String[] strs = anwserDetail.split("\\|");
        int anwserStatus = StringUtil.isBlank(anwserDetail) ? ANWSER_STATUS_NOTHING : Integer.parseInt(strs[0]);

        DragonTreasureInfo dti = JSON.parseObject(value, DragonTreasureInfo.class);
        Map<String, Object> map = Maps.newHashMap();
        map.put("id", dti.getId());
        map.put("hour", hour);
        map.put("question", dti.getQuestion());
        map.put("status", anwserStatus);
        map.put("yourAnwser", anwserStatus == ANWSER_STATUS_NOTHING ? "" : strs[1]);
        map.put("rightAnwser", anwserStatus == ANWSER_STATUS_NOTHING ? "" : dti.getRightAnwser());
        map.put("unlockCurrencyNum", attr.getUnlockCurrencyNum());
        map.put("options", dti.getOptionList());
        return new CommonPBOperateResp(0, JSON.toJSONString(map), "ok");
    }

    /**
     * 秘宝题目回答状态
     */
    private int getAnwserStatus(long uid, RoomInfo info, DragonTreasureComponentAttr attr, String hour) {
        String value = getAnwserDetail(uid, info, attr, hour);
        return StringUtil.isBlank(value) ? ANWSER_STATUS_NOTHING : Integer.parseInt(value.split("\\|")[0]);
    }

    /**
     * 秘宝题目回答状态
     */
    private String getAnwserDetail(long uid, RoomInfo info, DragonTreasureComponentAttr attr, String hour) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String key = this.makeKey(attr, String.format(DRAGON_TREASURE_QUALIFICATION_KEY, hour + "_anwser"));
        String field = uid + "-" + info.getRoomId();
        String value = this.actRedisDao.hget(groupCode, key, field);
        // 1|B|2024-02-07 18:06:09 - 第1个字段是回答状态：正确(1)/错误(2), 第2个字段是用户的回答(A/B/C)，第3个字段是回答时间
        return StringUtil.trim(value);
    }

    /**
     * 秘宝题目解答
     */
    private CommonPBOperateResp anwserQuestion(DragonTreasureComponentAttr attr, long actId, String seq, long opUid,
                                               long opSid, long opSsid, String extjson) {
        Clock clock = new Clock();
        Date now = commonService.getNow(actId);
        String hour = DateUtil.format(DateUtil.addHours(now, -1), DateUtil.PATTERN_TYPE7);
        if (now.after(DateUtil.getDate(attr.getUnlockEndTime()))) {
            return new CommonPBOperateResp(-10, "", "活动已结束");
        }

        String lockName = makeKey(attr, "commonOperatePbRequest:" + opUid + ":anwserQuestion");
        Secret secret = locker.lock(lockName, 5, seq, 5);
        if (secret == null) {
            return new CommonPBOperateResp(-1, "", "手速太快了！");
        }
        clock.tag();

        try {
            // 1. 检查房间是否存在
            RoomInfo info = commonService.getRoomInfoBySsid(opSsid);
            if (info == null) {
                return new CommonPBOperateResp(-2, "", "没有找到子频道对应的房间号！");
            }
            clock.tag();

            // 2. 检查房间是否已经有题目
            JSONObject jsonObject = JSON.parseObject(extjson);
            DragonTreasureInfo dti = getDragonTreasure(now, attr, String.valueOf(info.getRoomId()));
            if (dti == null) {
                return new CommonPBOperateResp(-3, "", "该房间还没有秘宝！");
            }
            clock.tag();

            // 3. 检查是否回答过
            String yourAnwser = StringUtil.trim(jsonObject.getString("anwser"));
            boolean bInAnwsers = "A".equals(yourAnwser) || "B".equals(yourAnwser) || "C".equals(yourAnwser);
            if (!bInAnwsers) {
                return new CommonPBOperateResp(-4, "", "不正确的答案选项：" + yourAnwser);
            }
            if (this.getAnwserStatus(opUid, info, attr, hour) != ANWSER_STATUS_NOTHING) {
                return new CommonPBOperateResp(-4, "", "您已在该房间回答过问题！");
            }
            clock.tag();

            // 4. 核对房间题目和用户提交的题目是否相同
            String id = jsonObject.getString("id");
            if (!dti.getId().equals(id)) {
                return new CommonPBOperateResp(-5, "", "没有找到对应的题目：" + id);
            }

            // 5. 若回答正确，提前获取获取奖包信息
            String rightAnwser = dti.getRightAnwser();
            int status = rightAnwser.equals(yourAnwser) ? ANWSER_STATUS_RIGHT : ANWSER_STATUS_ERROR;
            Map<Long, AwardModelInfo> mis = null;
            long awardTaskId = attr.getTreasureLevelAwardTaskIds().get(dti.getLevel());
            if (status == ANWSER_STATUS_RIGHT) {
                try {
                    mis = this.hdztAwardService.queryAwardTasks(awardTaskId, true);
                } catch (Throwable t) {
                    log.error("anwserQuestion queryAwardTasks fail@seq:{}, tid:{}, uid:{}", seq, awardTaskId, opUid);
                }
            }
            clock.tag();

            // 6. 解秘扣费 + 保存消费记录
            String unlockCurrencyId = attr.getUnlockCurrencyId();
            long unlockCurrencyNum = attr.getUnlockCurrencyNum();
            Assert.isTrue(unlockCurrencyNum > 0, "解秘货币消耗数量必须大于0！");
            Map<String, Long> priceMap = ImmutableMap.of(unlockCurrencyId, unlockCurrencyNum);
            int ret = currencyClient.consume(opUid, CURRENCY_BUSI_ID, priceMap, seq);
            log.info("anwserQuestion consume done@opUid:{}, ssid:{}, hour:{}, priceMap:{}, seq:{}, ret:{} {}",
                    opUid, opSsid, hour, JSON.toJSONString(priceMap), seq, ret, clock.tag());

            String unlockCurrencyName = attr.getUnlockCurrencyName();
            if (ret == CURRENCY_NOT_ENOUGH) {
                return new CommonPBOperateResp(-6, "", unlockCurrencyName + "余额不足");
            } else if (ret == CURRENCY_SUCCESS) {
                saveTradeRecord(seq, attr, String.valueOf(opUid), unlockCurrencyId, attr.getUnlockSummary(),
                        -1 * unlockCurrencyNum, now);
            } else if (ret == CURRENCY_SYSTEM_ERROR) {
                currencyClient.rollback(opUid, seq, CURRENCY_BUSI_ID);
                return new CommonPBOperateResp(-7, "", "请求超时，请稍后再试:" + ret);
            } else {
                return new CommonPBOperateResp(-7, "", unlockCurrencyName + "扣费失败:" + ret);
            }
            clock.tag();

            // 7. 更新用户回答状态，若是重复更新，直接冲正并返回失败
            if (!updateAnwserStatus(seq, status, yourAnwser, now, attr, opUid, info.getRoomId())) {
                currencyClient.rollback(opUid, seq, CURRENCY_BUSI_ID);
                return new CommonPBOperateResp(-8, "", "您已重复作答！");
            }
            clock.tag();

            // 8. 错误回答，返回错误提示
            if (status == ANWSER_STATUS_ERROR) {
                return new CommonPBOperateResp(1, rightAnwser, "回答错误");
            }

            // 9. 否则就是正确回答，龙晶抽奖，抽奖一定要完成，若失败打错误日志，提醒人工介入
            Map<String, String> ext = ImmutableMap.of(LotteryExtParaName.NOT_FLOW_CONTROL, "1",
                    LotteryExtParaName.LOAD_RECORD_SEQ_DUP, "1");
            BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(seq, attr.getBusiId(), opUid, awardTaskId,
                    Const.ONE, null, ext, attr.getRetry());
            boolean errorResult = result == null
                    || (result.getCode() != 0 && result.getCode() != LotteryException.E_LOTTERY_COMPLETED);
            if (errorResult) {
                log.error("anwserQuestion error@uid:{}, ssid:{}, actId:{}, seq:{}, awardTaskId:{}, result:{}", opUid,
                        opSsid, actId, seq, awardTaskId, JSON.toJSONString(result));
            }
            clock.tag();

            // 10. 计算龙晶数量
            long amount = errorResult ? 0 : calcAndSaveAwardRecord(attr, seq, opUid, now, dti, mis,
                    result.getRecordIds());

            // 返回 回答正确提示，龙晶数量为0时，用户可能会报障！！！
            if (amount > 0) {
                log.info("anwserQuestion done@actId:{}, seq:{}, uid:{}, ssid:{}, hour:{}, roomId:{}, amount:{} {}",
                        actId, seq, opUid, opSsid, hour, info.getRoomId(), amount, clock.tag());
            } else {
                log.error("anwserQuestion wrong@actId:{}, seq:{}, uid:{}, ssid:{}, hour:{}, roomId:{}, amount:{} {}",
                        actId, seq, opUid, opSsid, hour, info.getRoomId(), amount, clock.tag());
            }
            return new CommonPBOperateResp(0, String.valueOf(amount), "ok");
        } catch (Throwable t) {
            log.error("anwserQuestion exception@uid:{}, sid:{}, ssid:{}, actId:{}, seq:{}, extjson:{}, err:{} {}",
                    opUid, opSid, opSsid, actId, seq, extjson, t.getMessage(), clock.tag(), t);
            return new CommonPBOperateResp(9999, "", "请求超时，请稍后再试！");
        } finally {
            locker.unlock(lockName, secret, 5);
        }
    }

    /**
     * 计算龙晶数量
     * <p>
     * 1）这里假定奖包里面的奖项全部是龙晶，下面算法才成立
     * 2）若前面抽奖失败，则龙晶数量显示为0（没办法只能这样），具体有没得到视前面抽奖实际成功与否而定
     * 3）另外若奖包信息缺失（如前面因为异常未获取到，则龙晶数量显示为0，具体有没得到视前面抽奖实际成功与否而定）
     */
    private long calcAndSaveAwardRecord(DragonTreasureComponentAttr attr, String seq, long opUid, Date now,
                                        DragonTreasureInfo dti, Map<Long, AwardModelInfo> mis,
                                        Map<Long, Long> recordIds) {
        long packageId = recordIds.keySet().iterator().next();
        AwardModelInfo mi = mis == null ? null : mis.get(packageId);
        long amount = mi == null ? 0 : mi.getAwardPackageItemInfos().get(0).getGiftNum();
        if (amount > 0) {
            String summary = "解答" + dti.getLevel() + "级秘宝";
            AwardPackageItemInfo awardPackageItemInfo = mi.getAwardPackageItemInfos().get(0);
            String giftCode = JSON.parseObject(awardPackageItemInfo.getExtJson()).getString("giftCode");
            saveTradeRecord(seq, attr, String.valueOf(opUid), giftCode, summary, amount, now);
        } else {
            log.error("calcAndSaveAwardRecord fail@seq:{}, uid:{}, mi:{}, packageId:{}", seq, opUid, mi, packageId);
        }
        return amount;
    }

    /**
     * 保存交友记录，不能抛异常
     */
    private void saveTradeRecord(String seq, DragonTreasureComponentAttr attr, String memberId, String type,
                                 String summary, long amount, Date date) {
        Clock clock = new Clock();
        String key = this.makeKey(attr, String.format(DRAGON_TREASURE_TRADE_KEY, memberId, type));
        // 替换掉里面的 | 符号，防止意外影响分割处理
        summary = summary.replace("|", "/");
        String record = summary + "|" + amount + "|" + DateUtil.format(date);
        try {
            String groupCode = this.getRedisGroupCode(attr.getActId());
            long len = this.actRedisDao.lpush(groupCode, key, record);
            log.info("saveTradeRecord ok@seq:{}, key:{}, record:{}, len:{} {}", seq, key, record, len, clock.tag());
            actRedisDao.expire(groupCode, key, expiredSeconds);
        } catch (Throwable t) {
            log.error("saveTradeRecord exception@seq:{}, key:{}, record:{}, err:{} {}", seq, key, record,
                    t.getMessage(), clock.tag());
        }
    }

    /**
     * 更新用户回答状态，若之前已更新过返回失败，否则返回成功
     */
    private boolean updateAnwserStatus(String seq, int status, String yourAnwser, Date now,
                                       DragonTreasureComponentAttr attr, long uid, int roomId) {
        String hour = DateUtil.format(DateUtil.addHours(now, -1), DateUtil.PATTERN_TYPE7);
        String groupCode = getRedisGroupCode(attr.getActId());
        String key = this.makeKey(attr, String.format(DRAGON_TREASURE_QUALIFICATION_KEY, hour + "_anwser"));
        String field = uid + "-" + roomId;
        // 0|B|2024-02-07 18:06:09 - 第1个字段是回答状态：正确(1)/错误(2), 第2个字段是用户的回答(A/B/C)，第3个字段是回答时间
        String value = String.format("%s|%s|%s", status, yourAnwser, DateUtil.format(now));
        boolean flag = this.actRedisDao.hsetnx(groupCode, key, field, value);
        log.info("updateAnwserStatus done@seq:{}, key:{}, field:{}, value:{}, ret:{}", seq, key, field, value, flag);
        return flag;
    }

    /**
     * 保存虚拟货币发放记录到 redis list 中
     */
    private void saveCurrencyRecord(TaskProgressChanged event, DragonTreasureComponentAttr attr, List<String> list) {
        long startIndex = event.getStartTaskIndex();
        int taskLevelCount = event.getTaskLevelCount();
        long total = calcTotalTaskLevel(event);
        String seq = event.getSeq();
        String uid = event.getMember();
        for (int i = 0; i < total; i++) {
            int inx = (int) (startIndex + i) % taskLevelCount;
            String config = list.get(inx);
            if (!StringUtil.isBlank(config)) {
                String[] strs = config.split("\\|");
                String currencyId = strs[0];
                long amount = Long.parseLong(strs[1]);
                String summary = strs[2];
                Date date = DateUtil.getDate(event.getOccurTime());
                this.saveTradeRecord(seq, attr, uid, currencyId, summary, amount, date);
                log.info("saveCurrencyRecord done@seq:{}, uid:{}, i:{}, inx:{}, total:{}, currencyId:{}, amount:{}",
                        seq, uid, i, inx, total, currencyId, amount);
            }
        }
    }

    /**
     * 抽奖 + 赐福广播（业务全频道）
     * TODO::因 2024012001 活动取消此功能，故下面函数没有经过测试严格测试，后面若要复用，请仔细测试 - by guoliping / 2024-01-17
     */
    private void processBlessing(TaskProgressChanged event, DragonTreasureComponentAttr attr) {
        // 直接返回，待以后详细测试完善后再去除
        if (1 == 1) {
            return;
        }

        long blessingActorId = attr.getBlessingActorId();
        String member = (blessingActorId < 1) ? event.getMember() : event.getActors().get(blessingActorId);
        if (StringUtil.isBlank(member)) {
            return;
        }

        // 先对过任务做抽奖，抽中的奖包放到  packageIdNum 中返回
        long uid = Long.parseLong(member);
        long total = calcTotalTaskLevel(event);
        Map<Long, Long> packageIdNum = Maps.newHashMap();
        for (int i = 0; i < total; i++) {
            long lev = (event.getStartTaskIndex() + i) % event.getTaskLevelCount();
            doBatchLottery(event, attr, uid, total, packageIdNum, i, lev);
        }

        // 整理全部抽中的奖品信息
        List<Map<String, Object>> awardDetails = toAwardDetails(event.getSeq(), attr, packageIdNum);
        if (CollectionUtils.isEmpty(awardDetails)) {
            return;
        }

        // 准备赐福广播内容
        MemberInfo mi = memberInfoService.getMemberInfo((long) attr.getBusiId(), RoleType.USER, member);
        Date occurTime = DateUtil.getDate(event.getOccurTime());
        String hour = DateUtil.format(DateUtil.addHours(occurTime, -1), DateUtil.PATTERN_TYPE7);
        Map<String, Object> jsonData = Maps.newHashMap();
        jsonData.put("hour", hour);
        jsonData.put("nick", mi.getName());
        jsonData.put("logo", mi.getLogo());
        jsonData.put("award", awardDetails);

        // 发出赐福广播
        commonBroadCastService.commonBannerBroadcast(attr.getActId(), uid, 0, "", attr.getBannerId(),
                attr.getBannerTypeForBlessing(), jsonData, Template.findByValue(attr.getTemplate()));
    }

    /**
     * 做抽奖， 错误或发生异常就当没有抽中，返回空
     *
     * @param total        - 总计要做的抽奖次数，这里只用于打日志，方便运维
     * @param packageIdNum - 奖包数量，用于返回抽奖结果， key：奖包ID， value：累计的数量
     * @param inx          - 当前处理的抽奖序号，这里只用于打日志，方便运维
     * @param lev          - 当前任务等级，0表示第一级，以此类推
     */
    private void doBatchLottery(TaskProgressChanged event, DragonTreasureComponentAttr attr, long uid, long total,
                                Map<Long, Long> packageIdNum, int inx, long lev) {
        Clock clock = new Clock();
        String lotterySeq = makeKey(attr, event.getSeq() + ":" + (inx + 1) + "-" + lev);
        String hashSeq = MD5SHAUtil.getMD5(lotterySeq);
        try {
            Long taskId = attr.getBlessingAwardTaskIds().get(lev);
            if (taskId == null) {
                return;
            }

            Map<String, String> ext = ImmutableMap.of(LotteryExtParaName.NOT_FLOW_CONTROL, "1",
                    LotteryExtParaName.LOAD_RECORD_SEQ_DUP, "1");
            BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(hashSeq, attr.getBusiId(), uid, taskId,
                    Const.ONE, null, ext, attr.getRetry());
            boolean errorResult = result == null || (result.getCode() != 0
                    && result.getCode() != LotteryException.E_LOTTERY_COMPLETED);
            if (errorResult) {
                log.warn("doBatchLottery fail@uid:{}, hashSeq:{}, lotterySeq:{}, total:{}, result:{} {}", uid, hashSeq,
                        lotterySeq, total, result, clock.tag());
                return;
            }

            result.getRecordPackages().keySet().forEach(pid -> {
                long count = packageIdNum.containsKey(pid) ? packageIdNum.get(pid) : 0;
                packageIdNum.put(pid, count + 1);
            });

            log.info("doBatchLottery ok@uid:{}, hashSeq:{}, lotterySeq:{}, total:{}, result:{} {}", uid, hashSeq,
                    lotterySeq, total, result, clock.tag());
        } catch (Throwable t) {
            log.info("doBatchLottery exception@hashSeq:{}, lotterySeq:{}, event:{}, attr:{}, total:{}, err:{} {}",
                    hashSeq, lotterySeq, event, attr, total, t.getMessage(), clock.tag(), t);
        }
    }

    /**
     * 将奖包ID数量转成奖包的详细信息
     */
    private List<Map<String, Object>> toAwardDetails(String seq, DragonTreasureComponentAttr attr,
                                                     Map<Long, Long> packageIdNum) {
        Clock clock = new Clock();
        try {
            List<Long> awardTaskIds = Lists.newArrayList(attr.getBlessingAwardTaskIds().values());
            QueryAwardTaskResult atr = hdztAwardServiceClient.getProxy(false)
                    .queryAwardTasks(awardTaskIds, false);
            if (atr.getCode() != 0) {
                String atdis = JSON.toJSONString(awardTaskIds);
                log.warn("toAwardDetails fail@seq:{}, awardTaskIds:{}, atr:{} {}", seq, atdis, atr, clock.tag());
                return null;
            }

            Map<Long, AwardModelInfo> packageInfo = Maps.newHashMap();
            atr.getTaskAwardModelInfoMap().values().forEach(i -> {
                i.forEach(j -> {
                    packageInfo.put(j.getPackageId(), j);
                });
            });

            List<Map<String, Object>> result = Lists.newArrayList();
            for (Long packageId : packageIdNum.keySet()) {
                AwardModelInfo ami = packageInfo.get(packageId);
                Map<String, Object> gift = ImmutableMap.of("code", packageId.toString(), "name",
                        ami.getPackageName(), "count", packageIdNum.get(packageId));
                result.add(gift);
            }
            return result;
        } catch (Throwable e) {
            return null;
        }
    }

    /**
     * 计算已通过的总任务级数（这里考虑了循环过任务情况）
     */
    private long calcTotalTaskLevel(TaskProgressChanged event) {
        long startIndex = event.getStartTaskIndex();
        long currentIndex = event.getCurrTaskIndex();
        long roundComplete = event.getRoundComplete();
        int taskLevelCount = event.getTaskLevelCount();
        return (roundComplete * taskLevelCount) + (currentIndex - startIndex);
    }

    public static void main(String[] args) throws IOException {
        String fileName = "DragonTreasureQuestion-001.txt";
        //shuffleFile(fileName);
        readFile(fileName);
    }

    private static void shuffleFile(String fileName) throws IOException {
        Map<String, List<String>> map = loadTreasureQuestion(fileName, 5, true);
        List<String> s = map.get("S");
        List<String> a = map.get("A");
        Collections.shuffle(s);
        Collections.shuffle(a);
        List<String> list = Lists.newArrayList();
        list.add("S级题目\tA\tB\tC\t正确答案");
        list.addAll(s);
        list.add("A级题目\tA\tB\tC\t正确答案");
        list.addAll(a);

        String p = "D:\\workspace\\idea\\hdpt\\hdpt_activity_xlogic\\hdpt_activity_xlogic_app\\src\\main\\resources\\";
        File file = new File(p + fileName + ".shuffle");
        FileUtils.writeLines(file, "UTF-8", list);
    }

    private static void readFile(String fileName) throws IOException {
        Date begin = DateUtil.getDate("2024-02-02 17:00:00");
        Date end = DateUtil.getDate("2024-02-22 23:59:59");
        Map<String, List<String>> map2 = loadTreasureQuestion(fileName + ".shuffle", 6, false);
        DragonTreasureComponentAttr attr = new DragonTreasureComponentAttr();
        attr.setTreasureLevelAmounts(JSON.parseArray("[\"S|10\",\"A|10\"]", String.class));
        Map<String, List<String>> r = makeHourQuestionMap(attr, map2, begin, end);
        System.out.println(r.size());
        for (String hour : r.keySet()) {
            for (int i = 0; i < 20; i++) {
                String level = "S";
                String q = r.get(hour).get(i);
                DragonTreasureInfo d = DragonTreasureInfo.toDragonTreasureInfo("u1", i + 1, level, hour, q);
                Assert.isTrue(d != null, "房间分配秘宝问题失败！");
            }
            System.out.println("hour -> " + hour);
        }
    }
}
