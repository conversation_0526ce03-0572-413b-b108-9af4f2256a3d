package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.service.ActPlayService;
import com.yy.gameecology.hdzj.element.redis.MedalTaskComponent;
import com.yy.gameecology.hdzj.element.component.attr.MedalTaskComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class MedalTaskComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private MedalTaskComponent medalTaskComponent;
    @Autowired
    private ActPlayService actPlayService;



    @Test
    public void onTaskProgressChanged() throws Exception {
        MedalTaskComponentAttr attr = medalTaskComponent.getComponentAttr(2022033001L, 1);
        String estr = "{\"actId\":2022033001,\"actors\":{40003:\"248699782\",40004:\"39626143\",40013:\"248697624\"},\"busiId\":400,\"currRound\":1,\"currTaskIndex\":1,\"ekey\":\"\",\"index\":0,\"itemCurrNumMap\":{\"HDLW\":8800},\"member\":\"248699782\",\"occurTime\":\"2022-05-12 20:00:00\",\"phaseId\":30,\"rankId\":50,\"roundComplete\":0,\"seq\":\"808a907b-7160-408f-88fe-14b5ab40a0db\",\"startTaskIndex\":0,\"timeKey\":0,\"timestamp\":\"2022-05-12 20:00:00\",\"uri\":2001}";
        TaskProgressChanged event = JSON.parseObject(estr, TaskProgressChanged.class);
        medalTaskComponent.onTaskProgressChanged(event, attr);
    }


    @Test
    public void doNotify() throws Exception {

        MedalTaskComponentAttr attr = medalTaskComponent.getComponentAttr(2021041001L, 1);
        HdztAwardLotteryMsg event =  new HdztAwardLotteryMsg();
        event.setActId(2021041001L);
        event.setBusiId(500L);
        event.setSeq("123456");
        event.setUid(3);
        event.setTaskId(20051);
        HdztAwardLotteryMsg.Award award1 = new HdztAwardLotteryMsg.Award();
        award1.setPackageId(37L);
        award1.setGiftName("test1");
        HdztAwardLotteryMsg.Award award2 = new HdztAwardLotteryMsg.Award();
        award2.setPackageId(48L);
        award2.setGiftName("test2");
        HdztAwardLotteryMsg.Award award3 = new HdztAwardLotteryMsg.Award();
        award3.setPackageId(49L);
        award3.setGiftName("test3");
        event.setData(Lists.newArrayList(award1, award2,award3));

        while (true) {
            try {
                medalTaskComponent.onHdztAwardLotteryMsg(event, attr);
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }


    }

    @Test
    public void doNotify2() throws Exception {

        MedalTaskComponentAttr attr = medalTaskComponent.getComponentAttr(2021041001, 2);

        TaskProgressChanged event = JSON.parseObject("{\"actId\":2021041001,\"actors\":{50003:\"1366647420\"},\"busiId\":500,\"currRound\":1,\"currTaskIndex\":3,\"ekey\":\"\",\"index\":0,\"itemCurrNumMap\":{\"SHXZ\":625284},\"member\":\"1366647420\",\"occurTime\":\"2021-06-08 00:02:00\",\"phaseId\":52,\"rankId\":20051,\"roundComplete\":0,\"seq\":\"fea74f1b-0f1b-4399-9102-96dd22daffb8\",\"startTaskIndex\":2,\"timeKey\":0,\"timestamp\":\"2021-06-08 00:02:00\",\"uri\":2001}",TaskProgressChanged.class);



        while (true) {
            try {
                medalTaskComponent.onTaskProgressChanged(event, attr);
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }


    }
}
