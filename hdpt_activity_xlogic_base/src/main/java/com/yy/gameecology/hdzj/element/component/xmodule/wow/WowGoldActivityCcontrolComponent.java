package com.yy.gameecology.hdzj.element.component.xmodule.wow;

import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;

import com.yy.gameecology.activity.client.yrpc.BaymaxServiceClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.TokenGrant;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.WowGoldActivityCcontrolComponentAttr;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import com.yy.gameecology.common.utils.Convert;

import java.net.URI;
import java.util.*;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/5127")
@Component
public class WowGoldActivityCcontrolComponent extends BaseActComponent<WowGoldActivityCcontrolComponentAttr> {

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private BaymaxServiceClient baymaxServiceClient;

    @Override
    public Long getComponentId() {
        return ComponentId.WOW_ACTIVITY_ENTER_CONTROL;
    }

    // 白名单同步key
    public static final String WOW_WHITE_SYNC = "wow_white_sync_%s_%s";

    public static final String WHITELIST_DAY_STATISTIC = "whitelist_day_statistics";

    public static final String WHITELIST_DAY_STATISTIC_LOCK = "whitelist_day_statistics_lock_%s_%s";

    public static final String NEW_USER_DAY_STATISTIC = "new_user_day_statistics";

    public static final String WHITELIST_DAY_STATISTIC_SEQ = "whitelist_users_day_seq_%s_%s";

    public static final String WHITELIST_TOTAL_STATISTIC_SEQ = "whitelist_users_total_seq_%s_%s";

    private static final String GAME_USER_PATH = "/data-api/bigda/trino/query/wow_actv_user";

    private static final String GAME_USER_SERVICE = "https://services.bigda.com";

    private static final String APP_KEY = "8AEB760C4FDE4495A15A9DD23513E579";

    private static final String APP_SECRET = "E5407A7576974F76BCC8BEAD0950AB33";

    private static final String STATICS_TOTAL_KEY = "total";

    private static final String STATICS_DAY_KEY = "wow_white_user_%s";

    private static final String STATICS_TOTAL_COUNT_KEY = "wow_white_user_count";

    private static final int PAGE_SIZE = 500;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private WhitelistComponent whitelistComponent;


    @GetMapping("/sync")
    public Response<?> sync(@RequestParam("actId") int actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam("dateStr") String dateStr) {
        WowGoldActivityCcontrolComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();
        if (!isUIDWorkAccount(uid)) {
            return Response.fail(403,"unsupported operation");
        }
        log.info("start sync actId:{} date:{}", attr.getActId(),dateStr);
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> doSyncGameUser(attr, dateStr));

        return Response.ok();
    }

    @NeedRecycle(author = "gaofei", notRecycle = true)
    @Scheduled(cron = "0 0,30 10 * * *")
    public void syncGameUser() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
            if (activityInfo == null) {
                continue;
            }

            Date now = commonService.getNow(actId);

            if (!actInfoService.inActTime(now, activityInfo)) {
                continue;
            }

            WowGoldActivityCcontrolComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }


            String timeCode = DateUtil.format(DateUtil.addMinutes(now, -60 * 24), DateUtil.PATTERN_TYPE5);

            doSyncGameUser(attr, timeCode);
        }

    }

    public void doSyncGameUser(WowGoldActivityCcontrolComponentAttr attr, String dateStr) {
        if (commonService.isGrey(attr.getActId())) {
            log.warn("act is grey:{}", attr.getActId());
            return;
        }
        log.info("start sync actId:{} date:{}", attr.getActId(),dateStr);
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        int total = 200;
        int exception = 0;
        int maxException = 10;
        for (int pageIndex = 1; pageIndex < total; pageIndex++) {
            long startTimeMillis = System.currentTimeMillis();
            String lockKey = makeKey(attr, String.format(WOW_WHITE_SYNC, dateStr, pageIndex));
            boolean set = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 20, TimeUnit.MINUTES));
            if (!set) {
                continue;
            }
            try {
                List<Long> uids = queryGameUids(dateStr, pageIndex, PAGE_SIZE);
                if (CollectionUtils.isEmpty(uids)) {
                    log.info("doSyncGameUser with uids is empty dateStr:{} pageIndex:{}", dateStr, pageIndex);
                    break;
                }

                List<String> val = new ArrayList<>(uids.size());
                for (long uid : uids) {
                    val.add(String.valueOf(uid));
                }
                int ret = whitelistComponent.batchInsertIgnore(attr.getActId(), attr.getCmptUseInx(), val, DateUtil.getNowYyyyMMddHHmmss());
                if (!val.isEmpty()) {
                    int rs = baymaxServiceClient.batchAdd("yo_wow_act", val);
                    log.info("doSyncGameUser add whitelist with val:{} rs:{}", val, rs);
                }
                updateWhitelistStatic(attr, dateStr, ret, pageIndex);
                long endTimeMillis = System.currentTimeMillis();
                log.info("doSyncGameUser add whitelist with len：{} ret:{} pageIndex:{} ms:{} ", val.size(), ret, pageIndex, endTimeMillis - startTimeMillis);
            } catch (Exception e) {
                log.error("doSyncGameUser pageIndex,{} exception:{} e:{}", pageIndex, exception, e);
                exception = exception + 1;
                redisTemplate.delete(lockKey);
            }
            if (exception >= maxException) {
                log.error("doSyncGameUser reach max exception");
                break;
            }
        }
    }

    public void updateWhitelistStatic(WowGoldActivityCcontrolComponentAttr attr, String dateStr, int ret, int pageIndex) {
        if (ret == 0) {
            return ;
        }
        String totalSeq = String.format(WHITELIST_TOTAL_STATISTIC_SEQ, dateStr, pageIndex);
        commonDataDao.hashValueIncIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), totalSeq, STATICS_TOTAL_COUNT_KEY, STATICS_TOTAL_KEY, ret);
        String dayLockkey = makeKey(attr, String.format(WHITELIST_DAY_STATISTIC_SEQ, dateStr, pageIndex));
        String datakey = String.format(STATICS_DAY_KEY , dateStr);
        commonDataDao.hashValueIncIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), dayLockkey, STATICS_TOTAL_COUNT_KEY, datakey, ret);
    }

    public List<Long> queryGameUids(String dateStr, int pageIndex, int pageSize) {
        WhiteUserResp resp = queryGameUsers(dateStr, pageIndex, pageSize);
        if (resp == null || resp.code != 0) {
            log.error("doSyncGameUser queryGameUsers fail: resp:{}", resp);
            throw new RuntimeException("query game users fail");
        }

        if (CollectionUtils.isEmpty(resp.getData())) {
            return Collections.emptyList();
        }

        return resp.data.stream().map(UidItem::getUid).filter(StringUtils::isNumeric).map(Long::parseLong).toList();
    }

    public WhiteUserResp queryGameUsers(String dateStr, int pageIndex, int pageSize) {
        String path = GAME_USER_PATH + "?dt=" + dateStr + "&pageIndex=" + pageIndex + "&pageSize=" + pageSize;
        String token = TokenGrant.token(APP_KEY, APP_SECRET, path, 300L);

        HttpHeaders headers = new HttpHeaders();
        headers.add("signToken", token);

        RequestEntity<?> entity = new RequestEntity<>(headers, HttpMethod.GET, URI.create(GAME_USER_SERVICE + path));

        ResponseEntity<WhiteUserResp> response = restTemplate.exchange(entity, WhiteUserResp.class);
        if (response.getStatusCode() == HttpStatus.OK) {
            return response.getBody();
        }

        log.error("queryGameUsers fail status pageIndex:{} pageSize:{} response:{} ",pageIndex, pageSize, response);
        return null;
    }

    public Statics getWhiteStatics(long actId, long cmptIndex, String timeCode) {
        Statics ret = new Statics();
        WowGoldActivityCcontrolComponentAttr attr = getComponentAttr(actId, cmptIndex);
        String total = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), STATICS_TOTAL_COUNT_KEY, STATICS_TOTAL_KEY);
        String datakey = String.format(STATICS_DAY_KEY, timeCode);
        String dayCount = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), STATICS_TOTAL_COUNT_KEY, datakey);
        ret.setDayCount(Convert.toLong(dayCount, 0));
        ret.setTotalCount(Convert.toLong(total, 0));
        return ret;
    }

    @Data
    public static class UidItem {
        protected String uid;
    }

    @Data
    public static class WhiteUserResp {

        protected int code;

        protected String message;

        protected List<UidItem> data;

        protected int pageIndex;

        protected int totalCount;
    }

    @Data
    public static class Statics {
        public long totalCount;
        public long dayCount;
    }

}
