package com.yy.gameecology.hdzj.element.component.xmodule.pepc;

import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.bean.mq.hdzk.PepcGameEndEvent;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.activity.service.pepc.PepcRankService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.PepcConst;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGameMember;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PepcGameRankComponentAttr;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-10 15:35
 **/
@Component
public class PepcGameRankComponent extends BaseActComponent<PepcGameRankComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PepcDao pepcDao;

    @Autowired
    private PepcRankService pepcRankService;

    @Autowired
    private PepcPhaseComponent pepcPhaseComponent;

    @Override
    public Long getComponentId() {
        return ComponentId.PEPC_RANK;
    }

    @HdzjEventHandler(value = PepcGameEndEvent.class, canRetry = true)
    public void onPepcGameEndEvent(PepcGameEndEvent event, PepcGameRankComponentAttr attr) {
        log.info("onPepcGameEndEvent UpdateRanking -> event:{}, attr:{}", event, attr);

        List<PepcGameMember> gameMembers = pepcDao.getPepcGameMemberByGameId(event.getActId(), event.getGameId());

        for (PepcGameMember member : gameMembers) {
            //击杀榜单
            long ele = Convert.toLong(member.getElimination(), 0);
            if (ele > 0) {
                String seq = makeKey(attr, "kill|" + event.getGameId() + "|" + member.getUid());
                updateRankData(attr, seq, attr.getKillItemId(), member.getUid(), ele);
            }

            //胜利榜
            if (Const.ONE.equals(member.getRank()) && member.getState().equals(PepcConst.GameMemberState.RESULTED)) {
                String seq = makeKey(attr, "win|" + event.getGameId() + "|" + member.getUid());
                updateRankData(attr, seq, attr.getWinItemId(), member.getUid(), 1);
            }

        }

        long res = pepcRankService.saveRankActId(pepcPhaseComponent.getUniqueComponentAttr(attr.getActId()), attr.getActId());

        log.info("onPepcGameEndEvent UpdateRanking done,actId:{},res:{}", attr.getActId(), res);
    }


    private void updateRankData(PepcGameRankComponentAttr attr, String seq, String itemId, long uid, long score) {
        UpdateRankingRequest rankDataEvent = new UpdateRankingRequest();
        rankDataEvent.setBusiId(attr.getBusiId());
        rankDataEvent.setActId(attr.getActId());
        rankDataEvent.setSeq(seq);
        rankDataEvent.setActors(ImmutableMap.of(attr.getActorId(), Convert.toString(uid)));
        rankDataEvent.setItemId(itemId);
        rankDataEvent.setCount(1);
        rankDataEvent.setScore(score);
        rankDataEvent.setTimestamp(commonService.getNow(attr.getActId()).getTime());
        boolean result = hdztRankingThriftClient.updateRankWithRetry(rankDataEvent, 3);
        if (!result) {
            log.error("update rank error, invoke retry seq:{}", seq);
            throw new RuntimeException("update rank error, invoke retry");
        }
        log.info("updateRankData done: result:{}, request:{}", result, rankDataEvent);
    }


}
