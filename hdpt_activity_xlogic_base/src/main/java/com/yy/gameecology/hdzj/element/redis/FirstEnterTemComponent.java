package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.FirstEnterTemComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.RoleType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021.09.09 11:40
 */
@UseRedisStore
@Component
public class FirstEnterTemComponent extends BaseActComponent<FirstEnterTemComponentAttr> {
    @Override
    public Long getComponentId() {
        return ComponentId.FIRST_ENTER_TEMP;
    }

    public static final String TASK_FIRST_TIME_IN = "task_first_time_in";

    public static final String TASK_FIRST_TIME_LIGHT_ONE_LEVE = "task_first_time_light_one_level";

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void handleUserEnterTemplate(UserEnterTemplateEvent event, FirstEnterTemComponentAttr attr) {
        GameecologyActivity.Act202103_InHouseTemplateEventRsp.Builder builder = GameecologyActivity.Act202103_InHouseTemplateEventRsp.newBuilder();
        long uid = event.getUid();
        try {
            long sid = event.getSid();
            long ssid = event.getSsid();
            long busiId = event.getBusiId();
            long template = event.getTemplate();
            JSONObject ext = inHouseTemplate(uid, attr.getActId(), busiId, template, attr.getBusiIds());

            builder.setIsShow((int) ext.get("isShow"))
                    .setCode(0)
                    .setMessage("success");
            String btnText = (String) ext.get("btnText");
            if (StringUtil.isNotBlank(btnText)) {
                builder.setBtnText(btnText);
            }
            String content = (String) ext.get("content");
            if (StringUtil.isNotBlank(content)) {
                builder.setContent(content);
            }
            String title = (String) ext.get("title");
            if (StringUtil.isNotBlank(title)) {
                builder.setTitle(title);
            }
            String url = (String) ext.get("url");
            if (StringUtil.isNotBlank(url)) {
                builder.setUrl(url);
            }
            String extjon = (String) ext.get("extjon");
            if (StringUtil.isNotBlank(extjon)) {
                builder.setExtjson(extjon);
            }
            log.info("InHouseTemplateEventReq end uid:{} sid:{} ssid:{} ext={}", uid, sid, ssid, ext.toJSONString());
            GameecologyActivity.GameEcologyMsg build = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202103_InHouseTemplateEventRsp_VALUE)
                    .setAct202103InHouseTemplateEventRsp(builder.build())
                    .build();
            //换成单播
            svcSDKService.unicastUid(uid, build);

        } catch (Exception e) {
            log.warn("handleUserEnterTemplate error e", e);
            builder.setCode(-1);
            builder.setMessage("failed");
            builder.setIsShow(0);
            GameecologyActivity.GameEcologyMsg build = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202103_InHouseTemplateEventRsp_VALUE)
                    .setAct202103InHouseTemplateEventRsp(builder.build())
                    .build();
            //换成单播
            svcSDKService.unicastUid(uid, build);
        }
    }

    public JSONObject inHouseTemplate(long uid, long actId, long busiId, long templateId, long[] busiIds) {
        JSONObject json = new JSONObject();
        json.put("isShow", 0);
        int status = actInfoService.actTimeStatus(actId);
        if (status != 0) {
            log.warn("inHouseTemplate not in act time");
            return json;
        }

        //生产环境灰度白名单控制
        if (!commonService.checkWhiteList(actId, RoleType.USER, String.valueOf(uid))) {
            return json;
        }
        String groupCode = redisConfigManager.getGroupCode(actId);

        String firstTimeInKey = Const.addActivityPrefix(actId, TASK_FIRST_TIME_IN);
        String lightOnKey = Const.addActivityPrefix(actId, TASK_FIRST_TIME_LIGHT_ONE_LEVE);
        for (long template : busiIds) {
            if (busiId == template && actRedisDao.hsetnx(groupCode, firstTimeInKey, String.valueOf(uid), "1")) {
                json.put("isShow", 1);
                actRedisDao.hset(groupCode, lightOnKey, String.valueOf(uid), "1");
            }
        }
        return json;
    }

}
