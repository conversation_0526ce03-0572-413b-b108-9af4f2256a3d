package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.client.thrift.TGiftbagStatServiceClient;
import com.yy.gameecology.activity.client.thrift.TurnoverServiceClient;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.yrpc.IMMessageServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.service.BigDataService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.ZhuiwanApp;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.RequestUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.MultiYONovicePackageComponentAttr;
import com.yy.protocol.pb.zhuiwan.risk.ZhuiyaRisk;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.turnover.TActivityPropsDailyStat;
import com.yy.thrift.turnover.TUserProps;
import com.yy.thrift.zhuiwan.DecorationStorage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@UseRedisStore
@Slf4j
@RestController
@RequestMapping("/multi_novice")
public class MultiYONovicePackageComponent extends BaseActComponent<MultiYONovicePackageComponentAttr> {

    private static final String LOGIN_STRATEGY = "LOGIN_STRATEGY";

    private static final String TODAY_AWARD_COUNT_V2 = "awardCountV2:%d:%s";

    private static final String POP_UP_USER_CACHE_KEY = "popUpUser";

    private static final String DRAW_RECORD = "drawRecord";

    private static final String DRAW_USER = "drawUser";

    private static final String ACT_DATA_LOG = "actDataLog:%d";

    private static final String ACT_DATA_DAY_LOG = "actDataDayLog:%d:%s";

    private static final int SCORE_TYPE_PC_POP_UP = 10;
    private static final int SCORE_TYPE_RECEIVE_AWARD = 11;
    private static final int SCORE_TYPE_RECEIVE_AWARD_SUCC = 12;
    private static final int SCORE_TYPE_LOGIN = 13;
    private static final int SCORE_TYPE_RECEIVE_APP_LOGIN_AWARD_SUCC = 14;

    private static final String ENTER_CH_OP_TYPE = "multi_novice_enter_ch";

    private static final String ACT_DATA_POP_UP_TP = "log_data:pop";

    private static final String ACT_DATA_POP_UP_QUIET_TP = "log_data:popQuiet";

    private static final String ACT_DATA_OBTAIN_TP = "log_data:obtain";

    private static final String ACT_DATA_OBTAIN_NA_TP = "log_data:obtainNa";

    private static final String ACT_DATA_OBTAIN_APP_TP = "log_data:obtainAp";

    private static final String ACT_DATA_OBTAIN_PART_APP_TP = "log_data:obtainPartAp";

    private static final String ACT_DATA_OBTAIN_FREE_APP_TP = "log_data:obtainFreeAp";
    private static final String ACT_DATA_UID_LOGIN_ZHUIWAN_TP = "log_data:uidLoginZhuiwan";

    private static final String ACT_DATA_UID_LOGIN_ZHUIWAN_NA_TP = "log_data:uidLoginZhuiwanNa";
    private static final String ACT_DATA_UID_LOGIN_YOMI_TP = "log_data:uidLoginYomi";

    private static final String ACT_DATA_UID_LOGIN_YOMI_NA_TP = "log_data:uidLoginYomiNa";

    private static final String ACT_DATA_HDID_LOGIN_ZHUIWAN_TP = "log_data:hdidLoginZhuiwan";

    private static final String ACT_DATA_HDID_LOGIN_ZHUIWAN_NA_TP = "log_data:hdidLoginZhuiwanNa";

    private static final String ACT_DATA_HDID_LOGIN_YOMI_TP = "log_data:hdidLoginYomi";

    private static final String ACT_DATA_HDID_LOGIN_YOMI_NA_TP = "log_data:hdidLoginYomiNa";

    private static final String DRAW_USER_APP = "drawUserApp";

    private static final String ACT_FIRST_APP_UID_LOGIN = "actAppUidFirstLogin";
    private static final String ACT_FIRST_APP_HDID_LOGIN = "actAppHdidFirstLogin";

    private static final String DATA_LOG_USER_LIST = "dataLongUserList";

    private static final String HDID_DRAW_UID = "hdidDrawUid";

    private static final String APP_POPUP_UID = "appPopupUid";

    private static final List<String> STAT_HASH_KEYS = ImmutableList.of(
            ACT_DATA_POP_UP_TP, ACT_DATA_POP_UP_QUIET_TP,
            ACT_DATA_OBTAIN_TP, ACT_DATA_OBTAIN_NA_TP,
            ACT_DATA_OBTAIN_APP_TP, ACT_DATA_OBTAIN_PART_APP_TP, ACT_DATA_OBTAIN_FREE_APP_TP,
            ACT_DATA_UID_LOGIN_ZHUIWAN_TP, ACT_DATA_UID_LOGIN_ZHUIWAN_NA_TP,
            ACT_DATA_UID_LOGIN_YOMI_TP, ACT_DATA_UID_LOGIN_YOMI_NA_TP,
            ACT_DATA_HDID_LOGIN_ZHUIWAN_TP, ACT_DATA_HDID_LOGIN_ZHUIWAN_NA_TP,
            ACT_DATA_HDID_LOGIN_YOMI_TP, ACT_DATA_HDID_LOGIN_YOMI_NA_TP);

    @Autowired
    private ZhuiwanRiskClient riskClient;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private BigDataService bigDataService;

    @Autowired
    private TGiftbagStatServiceClient tGiftbagStatServiceClient;

    @Autowired
    private TurnoverServiceClient turnoverServiceClient;

    @Autowired
    private IMMessageServiceClient imMessageServiceClient;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Override
    public Long getComponentId() {
        return ComponentId.MULTI_NOVICE_PACKAGE;
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        final long uid = request.getOpUid(), sid = request.getOpSid(), ssid = request.getOpSsid(),
                actId = request.getActId(), cmptIndex = request.getCmptIndex();
        final String ip = request.getIp(), opType = request.getOpType(), opId = request.getOpId();
        log.info("commonOperatePbRequest request with uid:{}, sid:{}, ssid:{}, actId:{}, index:{}, ip:{}, opType:{}, opId:{}",
                uid, sid, ssid, actId, cmptIndex, ip, opType, opId);

        if (!ENTER_CH_OP_TYPE.equals(opType)) {
            return new CommonPBOperateResp(400, StringUtils.EMPTY, "unsupported opType!");
        }

        long busiId = Convert.toLong(opId);

        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return new CommonPBOperateResp(400, StringUtils.EMPTY, "component not exist!");
        }

        MultiYONovicePackageComponentAttr.NoviceConfig config = attr.getNoviceConfigs().get(busiId);
        if (config == null) {
            return new CommonPBOperateResp(400, StringUtils.EMPTY, "business not exist!");
        }

        Date now = commonService.getNow(attr.getActId());
        // 1. 判断是否在业务时间
        if (now.before(config.getStartTime()) || now.after(config.getEndTime())) {
            return new CommonPBOperateResp(403, StringUtils.EMPTY, "not in activity time!");
        }

        // 2. 判断用户是否已经弹过窗
        if (alreadyPopUp(uid, actId, cmptIndex)) {
            return new CommonPBOperateResp(441, StringUtils.EMPTY, "already pop!");
        }

        // 3. 判断风控是否通过
        if (hitRiskOfPc(uid, ip, attr.getRiskStrategyKey())) {
            return new CommonPBOperateResp(441, StringUtils.EMPTY, "hit risk rank!");
        }

        // 4. 判断用户是否在名单中
        boolean notInWhitelist = notInWhitelist(uid, actId, config.getWhitelistCmptInx());
        if (notInWhitelist && config.getFreePackageId() <= 0) {
            return new CommonPBOperateResp(403, StringUtils.EMPTY, "not in whitelist!");
        }

        // 5. 是否达到每日限制的90%
        boolean almostReachDaily = reachDailyLimit(actId, cmptIndex, busiId, config);
        if (almostReachDaily && config.getFreePackageId() <= 0) {
            return new CommonPBOperateResp(500, StringUtils.EMPTY, "already reach daily limit!");
        }

        // 6. 白名单且未达到90%则主动触发弹窗
        if (!notInWhitelist && !almostReachDaily) {
            popUpPc(uid, sid, ssid, busiId, attr, config.getApps());
        }

        // 7. 记录
        if (addToAlreadyPopUpSet(uid, actId, cmptIndex)) {
            addToDrawRecord(uid, sid, ssid, busiId, actId, cmptIndex, config);
            String tp = almostReachDaily || notInWhitelist ? ACT_DATA_POP_UP_QUIET_TP : ACT_DATA_POP_UP_TP;
            logBusinessData(actId, cmptIndex, busiId, tp, 1);
        }

        return super.commonOperatePbRequest(request);
    }

    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLoginEvent(ZhuiwanLoginEvent event, MultiYONovicePackageComponentAttr attr) {
        final long uid = event.getUid(), actId = attr.getActId(), cmptInx = attr.getCmptUseInx();
        NewUserPackageRecord record = getNewUserPackageRecord(uid, actId, cmptInx);

        if (record == null) {
            log.warn("onZhuiwanLoginEvent record null,uid:{},actId:{}",uid,actId);
            return;
        }

        if (!Objects.equals(record.getStatus(), Const.ONE)) {
            log.info("onZhuiwanLoginEvent pc draw status is not completed:{}", uid);
            return;
        }

        final long busiId = record.getBusiId();
        MultiYONovicePackageComponentAttr.NoviceConfig config = attr.getNoviceConfigs().get(busiId);

        if (StringUtils.isNotEmpty(config.getApps()) && !config.getApps().contains(event.getApp())) {
            log.warn("onZhuiwanLoginEvent uid:{},confiGapp:{}",uid,config.getApps());
            return;
        }

        //
        Date now = commonService.getNow(actId);
        if (now.before(config.getStartTime()) || now.after(config.getEndTime())) {
            log.warn("onZhuiwanLoginEvent not in activity time:{},uid:{}", record,uid);
            return;
        }

        if (!event.isRiskPass()) {
            log.warn("onZhuiwanLoginEvent hit risk with uid:{}, hdid:{}, tips:{}", uid, event.getHdid(), event.getRiskTips());
            return;
        }

        bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue((int) busiId), System.currentTimeMillis(), String.valueOf(uid), RoleType.USER, 1, SCORE_TYPE_LOGIN, event.getApp());

        //只第一次登陆打点
        final boolean uidFirstLogin = uidFirstLoginAfterPopup(uid, actId, cmptInx);
        if (uidFirstLogin) {
            log.info("onZhuiwanLoginEvent uidFirstLogin uid:{}",uid);
            String app = event.getApp();
            if (ZhuiwanApp.zhuiwan.name().equals(app)) {
                logBusinessData(actId, cmptInx, busiId, record.getPcPackageId() == 0 ? ACT_DATA_UID_LOGIN_ZHUIWAN_NA_TP : ACT_DATA_UID_LOGIN_ZHUIWAN_TP, 1);
            } else if (ZhuiwanApp.yomi.name().equals(app)) {
                logBusinessData(actId, cmptInx, busiId, record.getPcPackageId() == 0 ? ACT_DATA_UID_LOGIN_YOMI_NA_TP : ACT_DATA_UID_LOGIN_YOMI_TP, 1);
            }
        }

        boolean isNewHdid = isNewHdid(actId, cmptInx, uid, event.getHdid(), config.getStartTime());
        log.info("onZhuiwanLoginEvent uid:{},isNewHdid:{}",uid,isNewHdid);
        if (uidFirstLogin && isNewHdid && hdidFirstLoginAfterPopup(event.getHdid(), actId, cmptInx)) {
            String app = event.getApp();
            if (ZhuiwanApp.zhuiwan.name().equals(app)) {
                logBusinessData(actId, cmptInx, busiId, record.getPcPackageId() == 0 ? ACT_DATA_HDID_LOGIN_ZHUIWAN_NA_TP : ACT_DATA_HDID_LOGIN_ZHUIWAN_TP, 1);
            } else if (ZhuiwanApp.yomi.name().equals(app)) {
                logBusinessData(actId, cmptInx, busiId, record.getPcPackageId() == 0 ? ACT_DATA_HDID_LOGIN_YOMI_NA_TP : ACT_DATA_HDID_LOGIN_YOMI_TP, 1);
            }
        }

        long packageId;
        int appCount = 0;
        if (record.getPcPackageId() == 0) {
            packageId = config.getFreePackageId();
        } else if (isNewHdid) {
            packageId = config.getAppFullPackageId();
            appCount = config.getAppGiftCount();
        } else {
            packageId = config.getAppPartPackageId();
        }

        log.info("onZhuiwanLoginEvent  uid:{},packageId:{}", uid,packageId);
        boolean received = record.getAppStatus() != null && record.getAppStatus() == 1;
        if (received || packageId <= 0) {
            log.info("onZhuiwanLoginEvent user can't get reward from app login uid:{},record:{},packageId:{}", uid,record,packageId);
            return;
        }

        log.info("onZhuiwanLoginEvent get reward from app login uid:{},event:{}, record:{}", uid,event, record);
        boolean sendResult = false;
        String groupCode = getRedisGroupCode(actId);
        boolean nx = actRedisDao.hsetnx(groupCode, makeKey(actId, cmptInx, DRAW_USER_APP), String.valueOf(uid), String.valueOf(packageId));
        if (!nx) {
            log.info("onZhuiwanLoginEvent get reward from app login uid:{} cant get lock", uid);
            return;
        }

        try {
            String today = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2);
            long limit = config.getDayLimit(today);

            if (appCount > 0) {
                List<Long> incResult = actRedisDao.incrValueWithLimit(groupCode, getTodayAwardCountKey(actId, cmptInx, busiId), appCount, limit, false);
                // 新设备超过单日上限，只发进场秀
                if (incResult.get(0) != 1) {
                    log.info("onZhuiwanLoginEvent get reward from app login uid:{} over limit turn to part award", uid);
                    packageId = config.getAppPartPackageId();
                }
            }

            String time = DateUtil.format(commonService.getNow(actId));
            String newSeq = record.getSeq() + "-app";
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, busiId, uid, config.getTaskId(), 1, packageId, newSeq, 2);
            log.info("onZhuiwanLoginEvent get reward from app login doWelfare uid:{},packageId:{},result={}", uid,packageId,batchWelfareResult);
            sendResult = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if (sendResult) {
                record.setAppStatus(1);
                record.setNewHdid(isNewHdid ? 1 : 0);
                record.setAppPackageId(packageId);
                updateDrawRecord(uid, actId, cmptInx, record);
                // 上报数据-- scoreType = 12 领取成功
                bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue((int) busiId)
                        , System.currentTimeMillis(), String.valueOf(uid), RoleType.USER, 1, SCORE_TYPE_RECEIVE_APP_LOGIN_AWARD_SUCC);

                final String tp;
                if (packageId == config.getAppFullPackageId()) {
                    tp = ACT_DATA_OBTAIN_APP_TP;
                } else if (packageId == config.getAppPartPackageId()) {
                    tp = ACT_DATA_OBTAIN_PART_APP_TP;
                } else {
                    tp = ACT_DATA_OBTAIN_FREE_APP_TP;
                }
                logBusinessData(actId, cmptInx, busiId, tp, 1);
            }
        } catch (Exception e) {
            log.info("user cant get reward from app login doWelfare result={}", ExceptionUtils.getStackTrace(e));
        } finally {
            if (!sendResult) {
                actRedisDao.hdel(groupCode, makeKey(actId, cmptInx, DRAW_USER_APP), String.valueOf(uid));
            }
        }
    }


    @GetMapping("queryStatus")
    public Response<NewUserPackageStatusResp> queryStatus(HttpServletRequest request, HttpServletResponse response,
                                                          @RequestParam("actId") long actId,
                                                          @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx,
                                                          @RequestParam(name = "busiId") long busiId) {
        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }

        MultiYONovicePackageComponentAttr.NoviceConfig config = attr.getNoviceConfigs().get(busiId);
        if (config == null) {
            return Response.fail(400, "busiId not support!");
        }

        Date now = commonService.getNow(actId);
        if (now.before(config.getStartTime()) || now.after(config.getEndTime())) {
            return Response.success(new NewUserPackageStatusResp());
        }

        long loginUid = getLoginYYUid(request, response);

        String ip = RequestUtil.getRealIp(request);
        boolean notInWhitelist = notInWhitelist(loginUid, actId, config.getWhitelistCmptInx());
        notInWhitelist = notInWhitelist && config.getFreePackageId() <= 0;
        if (loginUid <= 0 || notInWhitelist || hitRiskOfPc(loginUid, ip, attr.getRiskStrategyKey())) {
            return Response.success(new NewUserPackageStatusResp());
        }

        return Response.success(doQueryStatus(loginUid, actId, busiId, cmptInx));
    }

    /**
     * 接口是给交友那边用的，在交友模板里判断勋章的状态
     * 如果已经获得(交友查内部对应的进场秀是否已获得)：则显示已获得并将进场秀点亮
     * 否则如果 canDraw == true && hasDraw == true：则继续指挥，显示已获得，hover后展示引导App的弹窗
     * 否则：则继续指挥，显示未获得，hover没交互
     * @param actId
     * @param cmptInx
     * @param busiId
     * @param uid
     * @return
     */
    @GetMapping("packageStatus")
    public Response<NewUserPackageStatusResp> queryStatus(@RequestParam("actId") long actId,
                                                          @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx,
                                                          @RequestParam(name = "busiId") long busiId,
                                                          @RequestParam(name = "uid") long uid) {
        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }

        MultiYONovicePackageComponentAttr.NoviceConfig config = attr.getNoviceConfigs().get(busiId);
        if (config == null) {
            return Response.fail(400, "busiId not support!");
        }

        boolean notInWhitelist = notInWhitelist(uid, actId, config.getWhitelistCmptInx());
        notInWhitelist = notInWhitelist && config.getFreePackageId() <= 0;
        if (notInWhitelist) {
            return Response.success(new NewUserPackageStatusResp());
        }

        NewUserPackageRecord record = getNewUserPackageRecord(uid, actId, cmptInx);
        if (record != null && busiId != 0 && record.getBusiId() != busiId) {
            record = null;
        }

        boolean canDraw = record != null;
        boolean hasDraw = canDraw && record.getStatus() == 1 && record.getPcPackageId() != 0;

        return Response.success(new NewUserPackageStatusResp(canDraw, hasDraw));
    }

    /**
     * 领取奖励
     * 返回：data == pcPackageId => PC端领取到的奖励packageId
     * data == 0：pc端没有领取到奖励，app端发放粉丝票
     * data != 0: pc端领取到了奖励，app端发放剩余礼物
     **/
    @PostMapping("receiveAward")
    public Response<Long> receiveAward(HttpServletRequest request, HttpServletResponse response,
                                         @RequestParam("actId") long actId,
                                         @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx,
                                         @RequestParam(name = "busiId") long busiId,
                                         @RequestParam("sid") long sid,
                                         @RequestParam("ssid") long ssid) {

        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }

        MultiYONovicePackageComponentAttr.NoviceConfig config = attr.getNoviceConfigs().get(busiId);
        if (config == null) {
            return Response.fail(400, "busiId not support!");
        }

        Date now = commonService.getNow(actId);
        log.info("receiveAward actId:{},cmptInx:{},config:{},now:{}",actId,cmptInx, JSON.toJSON(config),now);
        if (now.before(config.getStartTime()) || now.after(config.getEndTime())) {
            return Response.fail(400, "活动已结束，无法领取奖励！");
        }

        long uid = getLoginYYUid(request, response);
        String ip = RequestUtil.getRealIp(request);
        log.info("receiveAward uid={}, ip={}, actId={}, cmptInx={}, busiId:{}", uid, ip, actId, cmptInx, busiId);
        boolean notInWhitelist = notInWhitelist(uid, actId, config.getWhitelistCmptInx());
        notInWhitelist = notInWhitelist && config.getFreePackageId() <= 0;
        if (uid <= 0 || notInWhitelist || hitRiskOfPc(uid, ip, attr.getRiskStrategyKey())) {
            return Response.fail(400, "不满足领取条件");
        }

        long pcPackageId;
        try {
            pcPackageId = doReceiveAward(uid, attr, busiId);
        } catch (SuperException e) {
            log.warn("uid:{}, actId:{}, cmptInx:{} receice award e:{}", uid, actId, cmptInx, ExceptionUtils.getStackTrace(e));
            return Response.fail(e.getCode(), e.getMessage());
        }

        if (pcPackageId != 0) {
            popUpMaskPc(uid, sid, ssid, attr, busiId);
        }

        return Response.success(pcPackageId);
    }

    /**
     * 查客户端有无弹窗
     * https://sy.sysop.yy.com/service/overview/3@zhuiya@zhuiya-server/monitor/logger#Cloud
     * getHomepagePopupList 2914048394 208
     *
     */
    @GetMapping("/queryAppRewardStatus")
    public Response<JSONObject> queryAppRewardStatus(HttpServletRequest request, HttpServletResponse response,
                                                     @RequestParam("actId") long actId,
                                                     @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx,
                                                     @RequestHeader("x-fts-host-name") String app,
                                                     @RequestHeader("YYHeader-y0") String hdid,
                                                     @RequestHeader(value = "YYHeader-Platform", defaultValue = "0") int clientType) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(401, "you need login first!");
        }

        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }

        NewUserPackageRecord record = getNewUserPackageRecord(uid, actId, cmptInx);
        if (record == null || !Objects.equals(record.getStatus(), Const.ONE)) {
            return Response.fail(403, "you have not receive award from pc!");
        }

        String ip = RequestUtil.getRealIp(request);

        if (hitRiskOfApp(uid, hdid, ip, app, clientType, LOGIN_STRATEGY)) {
            log.info("queryAppRewardStatus hit risk uid:{}, hdid:{}, ip:{}, app:{}, clientType:{}", uid, hdid, ip, app, clientType);
            return Response.fail(409, "you do not have right to get this!");
        }

        MultiYONovicePackageComponentAttr.NoviceConfig config = attr.getNoviceConfigs().get((long)record.getBusiId());

        final boolean isNewUser;
        final long appPackageId;
        if (record.getAppStatus() == 1) {
            isNewUser = record.getNewHdid() == 1;
            appPackageId = record.getAppPackageId();
        } else if (record.getPcPackageId() == 0) {
            isNewUser = false;
            appPackageId = config.getFreePackageId();
        } else if (StringUtils.isEmpty(hdid)) {
            log.warn("not hdid specified");
            isNewUser = false;
            appPackageId = config.getAppPartPackageId();
        } else {
            //时序问题，可能没有到账，所以允许自行做判断
            isNewUser = isNewHdid(actId, cmptInx, uid, hdid, config.getStartTime());
            appPackageId = config.getAppPartPackageId();
        }

        List<DecorationStorage> decorations = zhuiWanPrizeIssueServiceClient.getUserInUseDecorations(uid, 5);
        final boolean equipped = decorations != null && decorations.stream().anyMatch(decoration -> decoration.decorationId == attr.getDecorationId());

        String key = makeKey(actId, cmptInx, APP_POPUP_UID);
        boolean firstPopup = actRedisDao.hsetnx(getRedisGroupCode(actId), key, String.valueOf(uid), hdid);

        JSONObject data = new JSONObject(5);
        data.put("isNewUser", isNewUser);
        data.put("equipped", equipped);
        data.put("firstPopup", firstPopup);
        final int appState;
        if (appPackageId == config.getAppFullPackageId()) {
            appState = 1;
        } else if (appPackageId == config.getAppPartPackageId()) {
            appState = 2;
        } else if (appPackageId == config.getFreePackageId()) {
            appState = 3;
        } else {
            appState = 2;
        }
        data.put("appPackageId", appPackageId);
        data.put("appState", appState);

        log.info("queryAppRewardStatus request with uid:{}, hdid:{}, app:{}, data:{}", uid, hdid, app, data);

        return Response.success(data);
    }

    @GetMapping("/getReceivedUids")
    public List<Long> getReceivedUids(HttpServletRequest request, HttpServletResponse response,
                                      @RequestParam("actId") long actId,
                                      @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx,
                                      @RequestParam("busiId") int busiId) {
        long uid = getLoginYYUid(request, response);
        if (uid == 0) {
            return Collections.emptyList();
        }

        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        String key = makeKey(attr, DRAW_RECORD), groupCode = getRedisGroupCode(actId);
        List<Long> result = new ArrayList<>(50000);
        try (Cursor<Map.Entry<String, String>> cursor =
                actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().scan(key, ScanOptions.scanOptions().count(1000).match("*").build())) {
            while (cursor.hasNext()) {
                Map.Entry<String, String> entry = cursor.next();
                NewUserPackageRecord record = JSON.parseObject(entry.getValue(), NewUserPackageRecord.class);
                if (!Objects.equals(record.getStatus(), Const.ONE) || record.getBusiId() != busiId) {
                    continue;
                }

                result.add(Long.parseLong(entry.getKey()));
            }
        } catch (Exception e) {
            log.error("getReceivedUids fail:", e);
        }

        return result;
    }

    @Scheduled(cron = "24 * * * * ?")
    @NeedRecycle(author = "liqingyang01", notRecycle = true)
    public void sendGiftExpiredMsg() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }

            MultiYONovicePackageComponentAttr attr = getUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            if (attr.getExpiredMsgCountdown() == 0) {
                continue;
            }

            Date now = commonService.getNow(actId);
            for (Map.Entry<Long, MultiYONovicePackageComponentAttr.NoviceConfig> entry : attr.getNoviceConfigs().entrySet()) {
                if (now.before(entry.getValue().getStartTime()) || now.after(entry.getValue().getEndTime())) {
                    continue;
                }

                if (now.getTime() + attr.getExpiredMsgCountdown() < entry.getValue().getEndTime().getTime()) {
                    continue;
                }
                log.info("doSendGiftExpiredMsg begin");
                doSendGiftExpiredMsg(actId, attr.getCmptUseInx(), entry.getKey(), entry.getValue(), attr.getMsgAppid(), attr.getMsgSenderUid());

            }

        }
    }

    @GetMapping("clearPopUp")
    public Response<String> clearPopUp(@RequestParam("uid") long uid,
                                       @RequestParam("actId") long actId,
                                       @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.success("not accessible!");
        }

        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        String popUpUserCacheKey = makeKey(attr, POP_UP_USER_CACHE_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        actRedisDao.sRem(groupCode, popUpUserCacheKey, String.valueOf(uid));
        String hashKey = makeKey(attr, DRAW_RECORD);
        actRedisDao.hdel(groupCode, hashKey, String.valueOf(uid));
        actRedisDao.hdel(groupCode, makeKey(actId, cmptInx, DRAW_USER), String.valueOf(uid));
        actRedisDao.hdel(groupCode, makeKey(actId, cmptInx, DRAW_USER_APP), String.valueOf(uid));
        actRedisDao.hdel(groupCode, makeKey(actId, cmptInx, APP_POPUP_UID), String.valueOf(uid));
        return Response.success("成功");
    }

    @GetMapping("broadcast")
    public Response<String> broadcast(@RequestParam("uid") long uid,
                                      @RequestParam("sid") long sid,
                                      @RequestParam("ssid") long ssid,
                                      @RequestParam("actId") long actId,
                                      @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx,
                                      @RequestParam("busiId") long busiId) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.success("not accessible!");
        }

        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        if (!attr.getNoviceConfigs().containsKey(busiId)) {
            return Response.fail(400, "busiId invalid!");
        }
        popUpMaskPc(uid, sid, ssid, attr, busiId);
        return Response.success("领取成功");
    }

    @GetMapping("/enterTemplate")
    public Response<String> enterTemplate(@RequestParam("uid") long uid,
                                          @RequestParam("sid") long sid,
                                          @RequestParam("ssid") long ssid,
                                          @RequestParam("actId") long actId,
                                          @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx,
                                          @RequestParam("busiId") long busiId) {
        if (SysEvHelper.isDeploy()) {
            return Response.fail(401, "not allow");
        }

        CommonPBOperateRequest request = new CommonPBOperateRequest();
        request.setActId(actId);
        request.setCmptId(getComponentId());
        request.setCmptIndex(cmptInx);
        request.setOpUid(uid);
        request.setOpSid(sid);
        request.setOpSsid(ssid);
        request.setIp("127.0.0.1");
        request.setOpType(ENTER_CH_OP_TYPE);
        request.setOpId(String.valueOf(busiId));

        CommonPBOperateResp resp = this.commonOperatePbRequest(request);

        return Response.success(JSON.toJSONString(resp));
    }

    @GetMapping("loginZhuiwan")
    public Response<String> loginZhuiwan(@RequestParam("uid") long uid,
                                         @RequestParam("actId") long actId,
                                         @RequestParam(name = "cmptInx", defaultValue = "200") long cmptInx,
                                         @RequestParam("app") String app,
                                         @RequestParam("hdid") String hdid) {
        ZhuiwanLoginEvent event = new ZhuiwanLoginEvent();
        event.setUid(uid);
        event.setHdid(hdid);
        event.setApp(app);
        event.setClientType(1);
        event.setTimestamp(System.currentTimeMillis() / 1000);
        event.setFirstLogin(true);

        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        this.onZhuiwanLoginEvent(event, attr);

        return Response.success("success!");
    }

    /**
     * 判断用户是否不在白名单内
     **/
    private boolean notInWhitelist(long uid, long actId, long whitelistIndex) {
        boolean inWhitelist = whitelistComponent.inWhitelist(actId, whitelistIndex, String.valueOf(uid));
        if (!inWhitelist) {
            log.info("uid={} is not in whitelist", uid);
        }
        return !inWhitelist;
    }

    private String getTodayAwardCountKey(long actId, long cmptInx, long busiId) {
        String today = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2);
        return makeKey(actId, cmptInx, String.format(TODAY_AWARD_COUNT_V2, busiId, today));
    }

    private long getTodayAwardCount(long actId, long cmptInx, long busiId) {
        String key = getTodayAwardCountKey(actId, cmptInx, busiId);
        String groupCode = getRedisGroupCode(actId);

        long todayAwardCount = Convert.toLong(actRedisDao.get(groupCode, key), 0);
        log.info("key={},todayAwardCount={}", key, todayAwardCount);

        return todayAwardCount;
    }

    /**
     * 1、本日上限达到95%时，就不弹弹窗了，预留部分礼物供未领取的人进行领取
     * 2、如果领取时依然超过上限，那加多个提示：当前领取人数过多，请明日再来领取。
     */
    private boolean reachDailyLimit(long actId, long cmptInx, long busiId, MultiYONovicePackageComponentAttr.NoviceConfig config) {
        long todayAwardCount = getTodayAwardCount(actId, cmptInx, busiId);
        String today = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2);
        long dayLimit = config.getDayLimit(today);

        return todayAwardCount >= dayLimit * config.getStopPopupRate();
    }

    private boolean alreadyPopUp(long uid, long actId, long cmptInx) {
        String popUpUserCacheKey = makeKey(actId, cmptInx, POP_UP_USER_CACHE_KEY);
        String groupCode = getRedisGroupCode(actId);
        boolean alreadyPopUp = actRedisDao.sIsMember(groupCode, popUpUserCacheKey, String.valueOf(uid));
        if (alreadyPopUp) {
            log.info("uid={} has already pop up", uid);

        }

        return alreadyPopUp;
    }

    /**
     * 是否命中风控
     **/
    private boolean hitRiskOfPc(long uid, String ip, String riskStrategyKey) {
        ZhuiyaRisk.Client client = ZhuiyaRisk.Client.newBuilder()
                .setApp(ZhuiyaRisk.App.APP_PCYY)
                .setIp(ip)
                .setPlatform(ZhuiyaRisk.Platform.PLATFORM_PC)
                .build();
        ZhuiyaRisk.RiskReq riskReq = ZhuiyaRisk.RiskReq.newBuilder()
                .setUserId(uid)
                .setStrategyKey(riskStrategyKey)
                .setClient(client)
                .build();
        ZhuiyaRisk.RiskRsp riskRsp = riskClient.getProxy().riskCheck(riskReq);

        boolean hitRisk = riskRsp.getRiskResult() != ZhuiyaRisk.RiskResult.RISK_RESULT_VALID;
        if (hitRisk) {
            log.info("uid={} hit pc risk req={},risk resp={}", uid, JsonFormat.printToString(riskReq), JsonFormat.printToString(riskRsp));
        }

        return hitRisk;
    }

    /**
     * 是否命中风控
     **/
    private boolean hitRiskOfApp(long uid, String hdid, String ip, String app, int clientType, String riskStrategyKey) {
        ZhuiyaRisk.Client client = ZhuiyaRisk.Client.newBuilder()
                .setApp(getApp(app))
                .setIp(ip)
                .setHdid(hdid)
                .setPlatform(ZhuiyaRisk.Platform.forNumber(clientType))
                .build();
        ZhuiyaRisk.RiskReq riskReq = ZhuiyaRisk.RiskReq.newBuilder()
                .setUserId(uid)
                .setStrategyKey(riskStrategyKey)
                .setClient(client)
                .build();
        ZhuiyaRisk.RiskRsp riskRsp = riskClient.getProxy().riskCheck(riskReq);

        boolean hitRisk = riskRsp.getRiskResult() != ZhuiyaRisk.RiskResult.RISK_RESULT_VALID;
        if (hitRisk) {
            log.info("uid={} hit app risk req={},risk resp={}", uid, JsonFormat.printToString(riskReq), JsonFormat.printToString(riskRsp));
        }

        return hitRisk;
    }

    private static ZhuiyaRisk.App getApp(String app) {
        if (ZhuiwanApp.dreamer.name().equals(app) || ZhuiwanApp.zhuiwan.name().equals(app)) {
            return ZhuiyaRisk.App.APP_ZHUIWAN;
        } else if (ZhuiwanApp.yomi.name().equals(app)) {
            return ZhuiyaRisk.App.APP_YOMI;
        }

        return ZhuiyaRisk.App.UNRECOGNIZED;
    }

    private static ZhuiyaRisk.Platform getPlatform(int clientType) {
        return ZhuiyaRisk.Platform.forNumber(clientType);
    }

    /**
     * PC弹窗
     **/
    private void popUpPc(long uid, long sid, long ssid, long busiId, MultiYONovicePackageComponentAttr attr, String apps) {
        // 通用弹窗协议,uri = 100004,noticeType=newUserPackage,noticeValue=PcPopUp,extJson={"sid":123,"ssid":222,"busiId":400,"uid":3333,"app":"Yomi/Zhuiwan"}
        JSONObject extJson = new JSONObject();
        extJson.put("sid", sid);
        extJson.put("ssid", ssid);
        extJson.put("busiId", busiId);
        extJson.put("apps", apps);
        if (StringUtils.isNotEmpty(apps)) {
            String app = apps.split(",")[0];
            extJson.put("app", app);
        }
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), attr.getNoticeType(), attr.getNoticeValue()
                , extJson.toJSONString(), uid);
        log.info("popUpPc sid:{}, ssid:{}", sid, ssid);

        // 上报数据 scoreType = 10 pc弹窗推送
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue((int) busiId)
                , System.currentTimeMillis(), String.valueOf(uid), RoleType.USER, 1, SCORE_TYPE_PC_POP_UP);
    }

    /**
     * 添加用户到已弹窗集合
     **/
    private boolean addToAlreadyPopUpSet(long uid, long actId, long cmptInx) {
        String popUpUserCacheKey = makeKey(actId, cmptInx, POP_UP_USER_CACHE_KEY);
        String groupCode = getRedisGroupCode(actId);
        long rs = actRedisDao.sadd(groupCode, popUpUserCacheKey, String.valueOf(uid));
        log.info("addToAlreadyPopUpSet uid={},key={},rs={}", uid, popUpUserCacheKey, rs);
        return rs > 0;
    }

    /**
     * 添加待领取记录
     **/
    private void addToDrawRecord(long uid, long sid, long ssid, long busiId, long actId, long cmptInx, MultiYONovicePackageComponentAttr.NoviceConfig config) {
        String hashKey = makeKey(actId, cmptInx, DRAW_RECORD);
        String groupCode = getRedisGroupCode(actId);

        NewUserPackageRecord record = new NewUserPackageRecord();
        record.setActId(actId);
        record.setSid(sid);
        record.setSsid(ssid);
        record.setBusiId((int) busiId);
        record.setChannelId(config.getChannelId());
        record.setApps(config.getApps());

        actRedisDao.hset(groupCode, hashKey, String.valueOf(uid), JSON.toJSONString(record));
        log.info("addToDrawRecord uid={},hashKey={},record={}", uid, hashKey, JSON.toJSONString(record));
    }

    private void logBusinessData(long actId, long cmptInx, long busiId, String dataType, int count) {
        String hashKey = makeKey(actId, cmptInx, String.format(ACT_DATA_LOG, busiId));
        String today = DateUtil.format(new Date(), DateUtil.PATTERN_TYPE2);
        String hashKeyToday = makeKey(actId, cmptInx, String.format(ACT_DATA_DAY_LOG, busiId, today));
        String groupCode = getRedisGroupCode(actId);
        actRedisDao.hIncrByKey(groupCode, hashKey, dataType, count);
        actRedisDao.hIncrByKey(groupCode, hashKeyToday, dataType, count);
    }

    private NewUserPackageRecord getNewUserPackageRecord(long uid, long actId, long cmptInx) {
        String hashKey = makeKey(actId, cmptInx, DRAW_RECORD);
        String groupCode = getRedisGroupCode(actId);
        String value = actRedisDao.hget(groupCode, hashKey, String.valueOf(uid));
        if (StringUtil.isEmpty(value)) {
            return null;
        }

        return JSON.parseObject(value, NewUserPackageRecord.class);
    }

    private boolean uidFirstLoginAfterPopup(long uid, long actId, long cmptInx) {
        String key = makeKey(actId, cmptInx, ACT_FIRST_APP_UID_LOGIN);
        String groupCode = getRedisGroupCode(actId);
        Long row = actRedisDao.getRedisTemplate(groupCode).opsForSet().add(key, String.valueOf(uid));
        row = row == null ? 0L : row;
        return row > 0;
    }

    private boolean hdidFirstLoginAfterPopup(String hdid, long actId, long cmptInx) {
        String key = makeKey(actId, cmptInx, ACT_FIRST_APP_HDID_LOGIN);
        String groupCode = getRedisGroupCode(actId);
        Long row = actRedisDao.getRedisTemplate(groupCode).opsForSet().add(key, hdid);
        row = row == null ? 0L : row;
        return row > 0;
    }

    private void updateDrawRecord(long uid, long actId, long cmptInx, NewUserPackageRecord record) {
        String hashKey = makeKey(actId, cmptInx, DRAW_RECORD);
        String groupCode = getRedisGroupCode(actId);
        actRedisDao.hset(groupCode, hashKey, String.valueOf(uid), JSON.toJSONString(record));
    }

    public NewUserPackageStatusResp doQueryStatus(long uid, long actId, long busiId, long cmptInx) {
        NewUserPackageStatusResp resp = new NewUserPackageStatusResp();
        NewUserPackageRecord record = getNewUserPackageRecord(uid, actId, cmptInx);
        if (record != null && busiId != 0 && record.getBusiId() != busiId) {
            record = null;
        }
        boolean inActTime = actInfoService.inActTime(actId);
        resp.setCanDraw(record != null && inActTime);
        resp.setHasDraw(record != null && record.getStatus() == 1);

        return resp;
    }

    public long doReceiveAward(long uid, MultiYONovicePackageComponentAttr attr, long busiId) {
        NewUserPackageRecord record = getNewUserPackageRecord(uid, attr.getActId(), attr.getCmptUseInx());
        if (record == null) {
            throw new SuperException("没有待领取记录", 400);
        }

        if (record.getBusiId() != busiId) {
            throw new SuperException("请从对应模板领取", 400);
        }

        if (record.getStatus() == 1) {
            throw new SuperException("奖励已领取", 403);
        }

        // 上报数据-- scoreType = 11 领取成功
        return sendAward(uid, attr, busiId, record);
    }

    /**
     * PC 测点击领取，发放PC测礼物
     * @param uid
     * @param attr
     * @param busiId
     * @param record
     * @return
     *   0：没有发放实际礼物
     *      1、不是白名单用户
     *      2、或者已达到90%
     *   pcPackageId：成功发放PC礼物
     */
    private long sendAward(long uid, MultiYONovicePackageComponentAttr attr, long busiId, NewUserPackageRecord record) {
        final long actId = attr.getActId(), cmptInx = attr.getCmptUseInx();
        MultiYONovicePackageComponentAttr.NoviceConfig config = attr.getNoviceConfigs().get(busiId);
        // 发放奖励
        long pcPackageId = config.getPcPackageId(), freePackageId = config.getFreePackageId();
        if (pcPackageId <= 0 && freePackageId <= 0) {
            throw new SuperException("没有配置奖包", 400);
        }

        // scoreType = 11 点击领取
        bigDataService.saveNoRankDataToFile(actId, BusiId.findByValue((int) busiId)
                , System.currentTimeMillis(), String.valueOf(uid), RoleType.USER, 1, SCORE_TYPE_RECEIVE_AWARD);

        String groupCode = getRedisGroupCode(actId);
        // 限额判断
        // 先设置已领取
        boolean nx = actRedisDao.hsetnx(groupCode, makeKey(actId, cmptInx, DRAW_USER), String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
        if (!nx) {
            throw new SuperException("操作太频繁,请稍后重试", 400);
        }

        boolean sendResult = false;

        boolean notInWhitelist = notInWhitelist(uid, actId, config.getWhitelistCmptInx());
        int count = config.getPcGiftCount();
        String today = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2);
        long limit = config.getDayLimit(today);
        if (notInWhitelist) {
            pcPackageId = 0;
        }

        try {
            if (pcPackageId != 0 && reachDailyLimit(actId, cmptInx, busiId, config)) {
                pcPackageId = 0;
            }

            if (pcPackageId != 0) {
                List<Long> incResult = actRedisDao.incrValueWithLimit(groupCode, getTodayAwardCountKey(actId, cmptInx, busiId), count, limit, false);
                if (incResult.get(0) != 1) {
                    pcPackageId = 0;
                }
            }

            if (pcPackageId <= 0 && freePackageId <= 0) {
                throw new SuperException("当前领取人数过多，请明日再来领取。", 400);
            }

            if (pcPackageId != 0) {
                String time = DateUtil.format(commonService.getNow(actId));
                BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, busiId, uid, config.getTaskId(), 1, pcPackageId, record.getSeq(), 2);
                log.info("doWelfare result={}", batchWelfareResult);

                sendResult = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            } else {
                // 触发发免费礼物，PC侧直接成功
                sendResult = true;
            }

            if (sendResult) {
                record.setStatus(1);
                record.setPcPackageId(pcPackageId);
                updateDrawRecord(uid, actId, cmptInx, record);
                reportUserOrient(actId, uid, config.getApps(), config.getEndTime());

                // 上报数据-- scoreType = 12 领取成功
                bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue((int) busiId)
                        , System.currentTimeMillis(), String.valueOf(uid), RoleType.USER, 1, SCORE_TYPE_RECEIVE_AWARD_SUCC);
                logBusinessData(actId, cmptInx, busiId, pcPackageId == 0 ? ACT_DATA_OBTAIN_NA_TP :ACT_DATA_OBTAIN_TP, 1);
            }
        } finally {
            if (!sendResult) {
                actRedisDao.hdel(groupCode, makeKey(actId, cmptInx, DRAW_USER), String.valueOf(uid));
            }
        }

        if (!sendResult) {
            throw new SuperException("网络异常，请稍后再试！", 500);
        }

        return pcPackageId;
    }

    private void reportUserOrient(long actId, long uid, String apps, final Date endTime) {
        Splitter.on(',').trimResults().omitEmptyStrings().split(apps).forEach(app -> {
            String key = "act:" + actId + ":" + app;
            zhuiWanPrizeIssueServiceClient.reportUserOrient(uid, key, endTime);
        });
    }

    private void popUpMaskPc(long uid, long sid, long ssid, MultiYONovicePackageComponentAttr attr, long busiId) {
        // 通用弹窗协议,uri = 100004,noticeType=newUserPackage,noticeValue=PcPopUp,extJson={"sid":123,"ssid":222,"busiId":400,"uid":3333,"app":"Yomi/Zhuiwan"}
        String apps = attr.getNoviceConfigs().get(busiId).getApps();
        JSONObject extJson = new JSONObject(6);
        extJson.put("sid", sid);
        extJson.put("ssid", ssid);
        extJson.put("busiId", busiId);
        extJson.put("apps", apps);
        if (StringUtils.isNotEmpty(apps)) {
            String app = apps.split(",")[0];
            extJson.put("app", app);
        }
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), attr.getNoticeType(), attr.getMaskNoticeValue(), extJson.toJSONString(), uid);
    }

    private void doSendGiftExpiredMsg(long actId, long cmptInx, long busiId, MultiYONovicePackageComponentAttr.NoviceConfig config, int msgAppId, long msgSenderUid) {
        String groupCode = getRedisGroupCode(actId);

        String execKey = makeKey(actId, cmptInx, "send_gift_expired_msg:" + busiId);
        if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
            return;
        }

        String hashKey = makeKey(actId, cmptInx, DRAW_RECORD);
        long counter = 0;
        try (Cursor<Map.Entry<String, String>> cursor = actRedisDao.getRedisTemplate(groupCode)
                .<String, String>opsForHash()
                .scan(hashKey, ScanOptions.scanOptions().count(1000).match("*").build())) {
            while (cursor.hasNext()) {
                Map.Entry<String, String> entry = cursor.next();
                try {
                    NewUserPackageRecord record = JSON.parseObject(entry.getValue(), NewUserPackageRecord.class);
                    if (record == null) {
                        continue;
                    }
                    long recordBusiId = record.getBusiId();
                    //不是这个业务下领取的礼包不处理
                    if (recordBusiId != busiId) {
                        log.info("doSendGiftExpiredMsg not busiId uid:{}, record:{}",entry.getKey(), entry.getValue());
                        continue;
                    }
                    if (Objects.equals(record.getStatus(), Const.ONE) || Objects.equals(record.getAppStatus(), Const.ONE)) {
                        long uid = Convert.toLong(entry.getKey());
                        List<TUserProps> userProps = turnoverServiceClient.getAllUserPropsByPropId(uid, config.getPropId(), config.getPropAppid());
                        if (CollectionUtils.isEmpty(userProps)) {
                            log.info("doSendGiftExpiredMsg no userProps,uid:{}, record:{}",uid, entry.getValue());
                            continue;
                        }

                        boolean propPresent = userProps.stream().findFirst().filter(prop -> prop.propsCount > 0).isPresent();
                        if (propPresent) {
                            doSendIMMsg(uid, msgAppId, msgSenderUid, config.getGiftExpiredMsg());
                            doSendSms(uid, config.getGiftExpiredSmsMsg());
                            counter++;
                            log.info("doSendGiftExpiredMsg done uid:{}, record:{}",uid, entry.getValue());
                        }
                    }
                } catch (Throwable t) {
                    log.error("handle sending expiring msg with entry:{} fail", entry, t);
                }
            }
            log.info("doSendGiftExpiredMsg ok,actId:{}, counter:{}", actId, counter);
        } catch (Exception e) {
            log.error("doSendGiftExpiredMsg fail:", e);
        }
    }

    private void doSendIMMsg(long uid, int msgAppId, long msgSenderUid, String msg) {
        if(StringUtil.isEmpty(msg)){
            log.warn("send im gift expiring msg empty to uid:{}", uid);
            return;
        }
        imMessageServiceClient.sendMessage(msgAppId, msgSenderUid,
                Collections.singletonList(uid), msg, Collections.emptyMap());
        log.info("send im gift expiring msg to uid:{}", uid);
    }

    private void doSendSms(long uid, String msg) {
        if(StringUtil.isEmpty(msg)){
            log.warn("send sms gift expiring msg empty to uid:{}", uid);
            return;
        }
        userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, msg);
        log.info("send sms gift expiring msg to uid:{}", uid);
    }

    private boolean isNewHdid(long actId, long cmptInx, long uid, String hdid, Date startTime) {
        String hdidKey = makeKey(actId, cmptInx, HDID_DRAW_UID);
        String groupCode = getRedisGroupCode(actId);
        boolean set = actRedisDao.hsetnx(groupCode, hdidKey, hdid, String.valueOf(uid));
        if (!set) {
            String value = actRedisDao.hget(groupCode, hdidKey, hdid);
            if (!StringUtils.equals(value, String.valueOf(uid))) {
                return false;
            }
        }

        Map<String, Long> loginTimeMap = zhuiWanPrizeIssueServiceClient.getUserLoginTime(uid, hdid);
        Long hdidLoginTime = loginTimeMap.get(hdid);
        if (hdidLoginTime == null || hdidLoginTime <= 0) {
            return true;
        }
        final long startMills;
        //测试环境和灰度因为有模拟时间，所以使用当前时间偏移1天来判断是否新用户
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            startMills = startTime.getTime();
        } else {
            startMills = System.currentTimeMillis() - DateUtils.MILLIS_PER_DAY;
        }
        log.info("isNewHdid request with hdid:{}, startMills:{}, hdidLoginTime:{}", hdid, startMills, hdidLoginTime);

        //最早的登录时间必须大于活动开始时间
        return hdidLoginTime > startMills;
    }

    /**********************************************************************************************************************
     *                                                   统计需求接口
     **********************************************************************************************************************/
    @GetMapping(value = "queryDataLog", produces = "text/html;charset=UTF-8")
    public String queryDataLog(HttpServletRequest request, HttpServletResponse response,
                               @RequestParam("actId") long actId,
                               @RequestParam("cmptInx") long cmptInx) {

        MultiYONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return "<p>未登陆!</p>";
        }
        String whiteListKey = makeKey(attr, DATA_LOG_USER_LIST);
        String groupCode = getRedisGroupCode(attr.getActId());
        boolean isMember = actRedisDao.sIsMember(groupCode, whiteListKey, String.valueOf(uid));
        if (!isMember) {
            return "<p>暂无权限访问！</p>";
        }

        Set<Long> busiIds = attr.getNoviceConfigs().keySet();

        StringBuilder html = new StringBuilder();
        prepend(html);
        for (long busiId : busiIds) {
            NewUserPackageDataLog today = getTodayDataLog(actId, cmptInx, busiId, attr.getNoviceConfigs().get(busiId));
            NewUserPackageDataLog total = getTotalDataLog(actId, cmptInx, busiId, attr.getNoviceConfigs().get(busiId));
            renderData(html, "今日[" + toBusiName((int) busiId) + "]", today);
            renderData(html, "总数[" + toBusiName((int) busiId) + "]", total);
        }

        append(html);

        return html.toString();
    }

    private static String toBusiName(int busiId) {
        switch (busiId) {
            case 200:
                return "活动";
            case 400:
                return "宝贝";
            case 500:
                return "交友";
            case 810:
                return "语音房";
            default:
                return "活动";
        }
    }

    private void prepend(StringBuilder html) {
        html.append("<table style='align:center' border>");
        html.append("<thead>").append("<tr>");

        html.append("<th rowspan='2'>").append("类型").append("</th>");
        html.append("<th colspan='2'>").append("PC进频道Uid").append("</th>");
        html.append("<th colspan='2'>").append("PC点击领取Uid").append("</th>");
        html.append("<th colspan='3'>").append("App领取Uid").append("</th>");
        html.append("<th colspan='4'>").append("App登录Uid").append("</th>");
        html.append("<th colspan='4'>").append("App登录新设备").append("</th>");
        html.append("<th colspan='2'>").append("礼物发放").append("</th>");
        html.append("<th colspan='4'>").append("礼物使用数量").append("</th>");
        html.append("<th colspan='3'>").append("礼物使用Uid").append("</th>");
        html.append("<th colspan='2'>").append("点击率").append("</th>");
        html.append("<th colspan='2'>").append("登录率").append("</th>");
        html.append("<th colspan='2'>").append("新设备占比率").append("</th>");
        html.append("<th colspan='2'>").append("送礼率").append("</th>");

        html.append("</tr>").append("<tr>");

        html.append("<th>").append("弹窗").append("</th>");
        html.append("<th>").append("非弹窗").append("</th>");

        html.append("<th>").append("领取礼包").append("</th>");
        html.append("<th>").append("非领取礼包").append("</th>");

        html.append("<th>").append("领取[礼包+进场秀]").append("</th>");
        html.append("<th>").append("领取[进场秀]").append("</th>");
        html.append("<th>").append("领取[免费礼物]").append("</th>");

        html.append("<th>").append("YO语音[白名单]").append("</th>");
        html.append("<th>").append("YO语音[非白名单]").append("</th>");
        html.append("<th>").append("YO交友[白名单]").append("</th>");
        html.append("<th>").append("YO交友[非白名单]").append("</th>");

        html.append("<th>").append("YO语音[白名单]").append("</th>");
        html.append("<th>").append("YO语音[非白名单]").append("</th>");
        html.append("<th>").append("YO交友[白名单]").append("</th>");
        html.append("<th>").append("YO交友[非白名单]").append("</th>");

        html.append("<th>").append("总数量").append("</th>");
        html.append("<th>").append("总金额（厘）").append("</th>");

        html.append("<th>").append("总数量").append("</th>");
        html.append("<th>").append("总金额（厘）").append("</th>");
        html.append("<th>").append("YO语音数").append("</th>");
        html.append("<th>").append("YO交友数").append("</th>");

        html.append("<th>").append("总数").append("</th>");
        html.append("<th>").append("YO语音").append("</th>");
        html.append("<th>").append("YO交友").append("</th>");

        html.append("<th>").append("白名单").append("</th>");
        html.append("<th>").append("非白名单").append("</th>");

        html.append("<th>").append("白名单").append("</th>");
        html.append("<th>").append("非白名单").append("</th>");

        html.append("<th>").append("白名单").append("</th>");
        html.append("<th>").append("非白名单").append("</th>");

        html.append("<th>").append("YO语音").append("</th>");
        html.append("<th>").append("YO交友").append("</th>");

        html.append("</tr></thead>");
        html.append("<tbody>");
    }

    private void renderData(StringBuilder html, String typeName, NewUserPackageDataLog data) {
        html.append("<tr>");
        html.append("<td>").append(typeName).append("</td>");

        html.append("<td>").append(data.getPopUpNum()).append("</td>");
        html.append("<td>").append(data.getQuietPopUpNum()).append("</td>");

        html.append("<td>").append(data.getObtainNum()).append("</td>");
        html.append("<td>").append(data.getObtainNaNum()).append("</td>");

        html.append("<td>").append(data.getAppObtainNum()).append("</td>");
        html.append("<td>").append(data.getAppObtainPartNum()).append("</td>");
        html.append("<td>").append(data.getAppObtainFreeNum()).append("</td>");

        html.append("<td>").append(data.getYomiLoginNum()).append("</td>");
        html.append("<td>").append(data.getYomiLoginNaNum()).append("</td>");
        html.append("<td>").append(data.getZhuiwanLoginNum()).append("</td>");
        html.append("<td>").append(data.getZhuiwanLoginNaNum()).append("</td>");

        html.append("<td>").append(data.getYomiHdidLoginNum()).append("</td>");
        html.append("<td>").append(data.getYomiHdidLoginNaNum()).append("</td>");
        html.append("<td>").append(data.getZhuiwanHdidLoginNum()).append("</td>");
        html.append("<td>").append(data.getZhuiwanHdidLoginNaNum()).append("</td>");

        html.append("<td>").append(data.getTotalIssueCount()).append("</td>");
        html.append("<td>").append(data.getTotalIssueAmount()).append("</td>");

        html.append("<td>").append(data.getTotalUsedCount()).append("</td>");
        //TODO:这里礼物单价是写死的
        html.append("<td>").append(data.getTotalUsedCount() * 100).append("</td>");
        html.append("<td>").append(data.getYomiUsedCount()).append("</td>");
        html.append("<td>").append(data.getZhuiwanUsedCount()).append("</td>");

        html.append("<td>").append(data.getTotalUsedUser()).append("</td>");
        html.append("<td>").append(data.getYomiUsedUser()).append("</td>");
        html.append("<td>").append(data.getZhuiwanUsedUser()).append("</td>");

        html.append("<td>").append(data.getPopUpNum() == 0 ? "0" : String.format("%.2f", (double) data.getObtainNum() / (double) data.getPopUpNum())).append("</td>");
        html.append("<td>").append(data.getQuietPopUpNum() == 0 ? "0" : String.format("%.2f", (double) data.getObtainNaNum() / (double) data.getQuietPopUpNum())).append("</td>");

        html.append("<td>").append(data.getObtainNum() == 0 ? "0" : String.format("%.2f", (double) (data.getYomiLoginNum() + data.getZhuiwanLoginNum()) / (double) (data.getObtainNum()))).append("</td>");
        html.append("<td>").append(data.getObtainNaNum() == 0 ? "0" : String.format("%.2f", (double) (data.getYomiLoginNaNum() + data.getZhuiwanLoginNaNum()) / (double) (data.getObtainNaNum()))).append("</td>");

        html.append("<td>").append(data.getYomiLoginNum() + data.getZhuiwanLoginNum() == 0 ? "0" : String.format("%.2f", (double) (data.getYomiHdidLoginNum() + data.getZhuiwanHdidLoginNum()) / (double) (data.getYomiLoginNum() + data.getZhuiwanLoginNum()))).append("</td>");
        html.append("<td>").append(data.getYomiLoginNaNum() + data.getZhuiwanLoginNaNum() == 0 ? "0" : String.format("%.2f", (double) (data.getYomiHdidLoginNaNum() + data.getZhuiwanHdidLoginNaNum()) / (double) (data.getYomiLoginNaNum() + data.getZhuiwanLoginNaNum()))).append("</td>");

        html.append("<td>").append(data.getYomiLoginNum() == 0 ? "0" : String.format("%.2f", (double) data.getYomiUsedUser() / (double) data.getYomiLoginNum())).append("</td>");
        html.append("<td>").append(data.getZhuiwanLoginNum() == 0 ? "0" : String.format("%.2f", (double) data.getZhuiwanUsedUser() / (double) data.getZhuiwanLoginNum())).append("</td>");

        html.append("</tr>");
    }

    private void append(StringBuilder html) {
        html.append("</tbody>").append("</table>");
    }

    private NewUserPackageDataLog getTodayDataLog(long actId, long cmptInx, long busiId, MultiYONovicePackageComponentAttr.NoviceConfig config) {
        final Date now = new Date();
        long endTime = DateUtils.ceiling(now, Calendar.DAY_OF_MONTH).getTime();
        long startTime = endTime - DateUtils.MILLIS_PER_DAY;

        return doGetDataLog(actId, cmptInx, busiId, config, false, startTime, endTime);
    }

    public NewUserPackageDataLog getTotalDataLog(long actId, long cmptInx, long busiId, MultiYONovicePackageComponentAttr.NoviceConfig config) {
        //只能从这天的00点开始
        long startTime = config.getStartTime().getTime();
        startTime = DateUtils.truncate(new Date(startTime), Calendar.DAY_OF_MONTH).getTime();
        long endTime = config.getEndTime().getTime() + 1000;

        if (!SysEvHelper.isDeploy() || commonService.isGrey(actId)) {
            //2023-07-01 00:00:00
            startTime = 1688140800000L;
            //2056-01-01 00:00:00
            endTime = 2713881600000L;
        }

        return doGetDataLog(actId, cmptInx, busiId, config, true, startTime, endTime);
    }

    private NewUserPackageDataLog doGetDataLog(long actId, long cmptInx, long busiId, MultiYONovicePackageComponentAttr.NoviceConfig config, boolean isTotal, long startTime, long endTime) {
        final Date now = new Date();
        final String key;
        if (isTotal) {
            key = makeKey(actId, cmptInx, String.format(ACT_DATA_LOG, busiId));
        } else {
            String today = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            key = makeKey(actId, cmptInx, String.format(ACT_DATA_DAY_LOG, busiId, today));
        }

        NewUserPackageDataLog result = new NewUserPackageDataLog();

        String groupCode = getRedisGroupCode(actId);

        List<String> tps = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().multiGet(key, STAT_HASH_KEYS);

        result.setPopUpNum(Convert.toInt(tps.get(0), 0));
        result.setQuietPopUpNum(Convert.toInt(tps.get(1), 0));
        result.setObtainNum(Convert.toInt(tps.get(2), 0));
        result.setObtainNaNum(Convert.toInt(tps.get(3), 0));
        result.setAppObtainNum(Convert.toInt(tps.get(4), 0));
        result.setAppObtainPartNum(Convert.toInt(tps.get(5), 0));
        result.setAppObtainFreeNum(Convert.toInt(tps.get(6), 0));
        result.setZhuiwanLoginNum(Convert.toInt(tps.get(7), 0));
        result.setZhuiwanLoginNaNum(Convert.toInt(tps.get(8), 0));
        result.setYomiLoginNum(Convert.toInt(tps.get(9), 0));
        result.setYomiLoginNaNum(Convert.toInt(tps.get(10), 0));
        result.setZhuiwanHdidLoginNum(Convert.toInt(tps.get(11), 0));
        result.setZhuiwanHdidLoginNaNum(Convert.toInt(tps.get(12), 0));
        result.setYomiHdidLoginNum(Convert.toInt(tps.get(13), 0));
        result.setYomiHdidLoginNaNum(Convert.toInt(tps.get(14), 0));

        List<TActivityPropsDailyStat> stats = tGiftbagStatServiceClient.getActivityPropsDailyStat(config.getPropAppid(), config.getPropActivityId(), startTime, endTime);
        if (CollectionUtils.isNotEmpty(stats)) {
            if (isTotal) {
                stats.forEach(stat -> {
                    result.setTotalIssueCount(result.getTotalIssueCount() + stat.totalIssueCount);
                    result.setTotalIssueAmount(result.getTotalIssueAmount() + stat.totalIssueAmount);
                    result.setTotalUsedCount(result.getTotalUsedCount() + stat.totalUseCount);
                    result.setTotalUsedUser(result.getTotalUsedUser() + stat.totalUseUser);
                    if (!StringUtils.startsWith(stat.expand, StringUtil.OPEN_BRACE)) {
                        return;
                    }

                    JSONObject expend = JSON.parseObject(stat.expand);
                    JSONObject usedUserMap = expend.getJSONObject("total_use_user_map");
                    JSONObject usedCountMap = expend.getJSONObject("total_use_count_map");
                    //usedUserMap.getIntValue("157") + usedUserMap.getIntValue("158") + usedUserMap.getIntValue("171") + usedUserMap.getIntValue("172");
                    int yomiUsedUser = getTotalCount(usedUserMap, "157", "158", "171", "172");
                    //usedCountMap.getIntValue("157") + usedCountMap.getIntValue("158") + usedCountMap.getIntValue("171") + usedCountMap.getIntValue("172");
                    int yomiUsedCount = getTotalCount(usedCountMap, "157", "158", "171", "172");

                    //usedUserMap.getIntValue("77") + usedUserMap.getIntValue("78");
                    int zhuiwanUsedUser = getTotalCount(usedUserMap, "77", "78");
                    //usedCountMap.getIntValue("77") + usedCountMap.getIntValue("78");
                    int zhuiwanUsedCount = getTotalCount(usedCountMap, "77", "78");

                    result.setZhuiwanUsedUser(result.getZhuiwanUsedUser() + zhuiwanUsedUser);
                    result.setZhuiwanUsedCount(result.getZhuiwanUsedCount() + zhuiwanUsedCount);
                    result.setYomiUsedUser(result.getYomiUsedUser() + yomiUsedUser);
                    result.setYomiUsedCount(result.getYomiUsedCount() + yomiUsedCount);
                });
            } else {
                stats.stream()
                        .filter(stat -> DateUtils.isSameDay(new Date(stat.getDt()), now))
                        .findFirst()
                        .ifPresent(stat -> {
                            result.setTotalIssueCount(stat.totalIssueCount);
                            result.setTotalIssueAmount(stat.totalIssueAmount);
                            result.setTotalUsedCount(stat.totalUseCount);
                            result.setTotalUsedUser(stat.totalUseUser);

                            if (!StringUtils.startsWith(stat.expand, StringUtil.OPEN_BRACE)) {
                                return;
                            }

                            JSONObject expend = JSON.parseObject(stat.expand);
                            JSONObject usedUserMap = expend.getJSONObject("total_use_user_map");
                            JSONObject usedCountMap = expend.getJSONObject("total_use_count_map");
                            //usedUserMap.getIntValue("157") + usedUserMap.getIntValue("158") + usedUserMap.getIntValue("171") + usedUserMap.getIntValue("172");
                            int yomiUsedUser = getTotalCount(usedUserMap, "157", "158", "171", "172");
                            //usedCountMap.getIntValue("157") + usedCountMap.getIntValue("158") + usedCountMap.getIntValue("171") + usedCountMap.getIntValue("172");
                            int yomiUsedCount = getTotalCount(usedCountMap, "157", "158", "171", "172");

                            //usedUserMap.getIntValue("77") + usedUserMap.getIntValue("78");
                            int zhuiwanUsedUser = getTotalCount(usedUserMap, "77", "78");
                            //usedCountMap.getIntValue("77") + usedCountMap.getIntValue("78");
                            int zhuiwanUsedCount = getTotalCount(usedCountMap, "77", "78");

                            result.setZhuiwanUsedUser(zhuiwanUsedUser);
                            result.setZhuiwanUsedCount(zhuiwanUsedCount);
                            result.setYomiUsedUser(yomiUsedUser);
                            result.setYomiUsedCount(yomiUsedCount);
                        });
            }
        }

        return result;
    }

    private static int getTotalCount(JSONObject json, String... appid) {
        return Arrays.stream(appid).mapToInt(json::getIntValue).reduce(Integer::sum).orElse(0);
    }
}
