package com.yy.gameecology.activity.worker.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.acttask.MemberTaskVo;
import com.yy.gameecology.activity.bean.hdzt.*;
import com.yy.gameecology.activity.bean.rank.KoRankItem;
import com.yy.gameecology.activity.bean.rank.RankItemUserAnchor;
import com.yy.gameecology.activity.client.thrift.FtsSensitiveServiceThriftClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.client.yrpc.CurrencyClient;
import com.yy.gameecology.activity.client.yrpc.OnlineChannelClient;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.worker.web.BaseController;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbServiceClient;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.bean.PKRecord;
import com.yy.gameecology.hdzj.bean.UserTaskInfo;
import com.yy.gameecology.hdzj.bean.UserTaskVo;
import com.yy.gameecology.hdzj.element.redis.ActivityGloryReportComponent;
import com.yy.gameecology.hdzj.element.history.HonorHallPkRecordComponent;
import com.yy.gameecology.hdzj.element.component.RankingShowComponent;
import com.yy.gameecology.hdzj.element.component.RankingTaskQueryComponent;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-20 15:34
 **/
@Validated
@RestController
@CrossOrigin(allowCredentials = "true", originPatterns = {"yy.com", "*.yy.com"})
@RequestMapping("/hdzt_rank")
public class HdztRankController extends BaseController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final int MAX_RANK_COUNT = 100;

    @Autowired
    private WebdbServiceClient webdbServiceClient;

    @Autowired
    private HdztRankService hdztRankService;

    @Autowired
    private HdztRankConfigService hdztRankConfigService;

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private HdztTaskService hdztTaskService;

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private ActTaskService actTaskService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ActKoService actKoService;

    @Autowired
    private ActResultService actResultService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private RankingShowComponent rankingShowComponent;

    @Autowired
    private HonorHallPkRecordComponent hallPKRecordV2Component;
    @Autowired
    private RankingTaskQueryComponent rankingTaskQueryComponent;

    @Autowired
    private ActivityGloryReportComponent activityGloryReportComponent;

    @Autowired
    private FtsSensitiveServiceThriftClient ftsSensitiveService;

    @Autowired
    private TopCPService topCPService;

    @Autowired
    private OnlineChannelClient onlineChannelClient;

    @Autowired
    private CurrencyClient currencyClient;

    @Report
    //所有榜单配置信息
    @RequestMapping(value = "/queryKoRecord")
    public Response<RankInfo> queryKoRecord(HttpServletRequest req, HttpServletResponse resp,
                                            Long actId, long rankId, long phaseId, String dateStr,
                                            String pointedMember, int recordType, Integer rankCount) {
        Clock clock = new Clock();

        try {

            if (rankCount == null || rankCount <= 0 || rankCount > MAX_RANK_COUNT) {
                rankCount = MAX_RANK_COUNT;
            }

            RankInfo rankInfo = actKoService.queryKoInfos(actId, rankId, phaseId, dateStr,
                    recordType, rankCount, pointedMember);

            return new Response<>(Code.OK.getCode(), "ok", rankInfo);
        } catch (Exception e) {
            log.error("queryKoRecord exception,req:{},clock:{},err:{}", actId, clock.tag(), e, e);
            return new Response<>(Code.E_SYS_BUSY);
        }

    }

    @Report
    //所有榜单配置信息
    @RequestMapping(value = "/queryKoRecordNew")
    public Response<RankInfo> queryKoRecordNew(HttpServletRequest req, HttpServletResponse resp,
                                               Long actId, long rankId, long phaseId, String dateStr,
                                               String pointedMember, Integer rankCount) {
        Clock clock = new Clock();
        try {

            if (rankCount == null || rankCount <= 0 || rankCount > MAX_RANK_COUNT) {
                rankCount = MAX_RANK_COUNT;
            }

            RankInfo result = new RankInfo();

            RankInfo koing = actKoService.queryKoInfos(actId, rankId, phaseId, dateStr,
                    2, rankCount, pointedMember);

            RankInfo koDone = actKoService.queryKoInfos(actId, rankId, phaseId, dateStr,
                    1, rankCount, pointedMember);

            List<KoRankItem> koRankItems = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(koing.getList())) {
                koRankItems.addAll(koing.getList());
            }
            if (CollectionUtils.isNotEmpty(koDone.getList())) {
                koRankItems.addAll(koDone.getList());
            }

            result.setTotalCount(koRankItems.size() + 0L);
            result.setList(koRankItems);


            return new Response<>(Code.OK.getCode(), "ok", result);
        } catch (Exception e) {
            log.error("queryKoRecordNew exception,req:{},clock:{},err:{}", actId, clock.tag(), e, e);
            return new Response<>(Code.E_SYS_BUSY);
        }

    }

    @Report
    @RequestMapping(value = "/querySimpleLayerInfo")
    public Response<Map<String, Object>> querySimpleLayerInfo(HttpServletRequest req, Long actId, Long sid, Long ssid) {
        try {
            if (actId == null || sid == null || ssid == null) {
                log.warn("para error,actid:{},sid:{},ssid:{}", actId, sid, ssid);
                return new Response<>(Code.E_DATA_ERROR.getCode(), "para error", null);
            }

            Map<String, Object> result = Maps.newHashMap();

            LayerBroadcastInfo layerBroadcastInfo = actLayerInfoService.getSimpleLayerInfo(actId, sid, ssid);
            result.put("layerInfo", layerBroadcastInfo);

            return new Response<>(Code.OK.getCode(), "ok", result);
        } catch (Throwable e) {
            log.error("buildLayerInfo exception@ip:{}, actId:{}, sid:{}, ssid:{}, err:{} ", RequestUtil.getRealIp(req),
                    actId, sid, ssid, e.getMessage(), e);
            return Response.fail(Code.E_SYS_BUSY.getCode(), "E5136#系统繁忙！");
        }
    }


    /**
     * 查询活动榜单配置信息
     *
     * @param actId
     * @return
     */
    @Report
    @RequestMapping(value = "/queryRankConfig")
    public Response<RankingConfigResponse> queryRankConfig(@NotNull @RequestParam(name = "actId") Long actId) {
        Clock clock = new Clock();
        RankingConfigResponse response = hdztRankConfigService.getRankConfig(actId);
        return new Response<>(Code.OK.getCode(), "ok", response);

    }

    @Report
    //所有榜单配置信息
    @RequestMapping(value = "/querySingleRankConfig")
    public Response<RankingConfigVo> querySingleRankConfig(HttpServletRequest req, HttpServletResponse resp,
                                                           Long actId, Long rankId) {
        Clock clock = new Clock();

        try {
            RankingConfigVo response = hdztRankConfigService.getSingleRankConfig(actId, rankId);
            return new Response<RankingConfigVo>(Code.OK.getCode(), "ok", response);
        } catch (Exception e) {
            log.error("querySingleRankConfig exception,req:{},clock:{},err:{} {}", actId, clock.tag(), e, e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }


    @Report
    //赛程配置信息
    @RequestMapping(value = "/queryPhaseConfig")
    public Response<RankingPhaseResponse> queryPhaseConfig(HttpServletRequest req, Long actId, String groupCodes) {
        List<RankingPhaseGroupVo> vos = Lists.newArrayList();
        final long actId202008001 = 202008001;
        if (actId.equals(actId202008001) && StringUtils.isBlank(groupCodes)) {
            vos = hdztRankConfigService.getQiXiRankingPhase(actId);
        } else if (StringUtils.isNotBlank(groupCodes)) {
            List<String> groupCodeList = Arrays.asList(groupCodes.split("\\|"));
            vos = hdztRankConfigService.getRankingPhaseGroup(actId, groupCodeList);
        }
        RankingPhaseResponse response = new RankingPhaseResponse();
        response.setItems(vos);
        response.setActivityInfoVo(hdztRankingThriftClient.queryActivityInfo(actId));
        response.setSrvTimestamp(System.currentTimeMillis());
        return Response.success(response);
    }


    @Report
    //查询通用榜单信息
    @RequestMapping(value = "/queryRank")
    public Response<RankInfo> queryRank(@Validated QueryRankRequest queryRankRequest) {
        log.info("queryRank actId:{},rankId:{},phaseId:{}", queryRankRequest.getActId(), queryRankRequest.getRankId(), queryRankRequest.getPhaseId());
        Clock clock = new Clock();
        long loginUid = -1;
        try {
            loginUid = getLoginYYUid();
            if(loginUid <= 0) {
                try {
                    loginUid = Convert.toLong(queryRankRequest.getUseLoginUid());
                } catch (Exception e) {
                    log.info("get used login uid error");
                }
            }
            Long actId = queryRankRequest.getActId();

            GetRankReq getRankReq = new GetRankReq();
            getRankReq.setRankId(queryRankRequest.getRankId());
            getRankReq.setPhaseId(Convert.toLong(queryRankRequest.getPhaseId(), 0));
            getRankReq.setDateStr(queryRankRequest.getDateStr());
            getRankReq.setActId(actId);
            getRankReq.setRankType(queryRankRequest.getRankType());
            getRankReq.setFindSrcMember(queryRankRequest.getFindSrcMember());
            getRankReq.setLoginUid(loginUid);
            getRankReq.setGroup(queryRankRequest.getGroup());
            getRankReq.setUseCache(Convert.toInt(queryRankRequest.getUseCache(),0));


            long maxRankCount = Const.GEPM.getParamValueToLong(GeParamName.HDZT_MAX_RANK_COUNT, MAX_RANK_COUNT);
            Long rankCount = queryRankRequest.getRankCount();
            if (rankCount == null) {
                rankCount = (long) MAX_RANK_COUNT;
            } else if (rankCount > maxRankCount) {
                rankCount = maxRankCount;
            }
            getRankReq.setRankCount(rankCount);

            if (Const.isNumberOrSsid(queryRankRequest.getPointedMember())) {
                getRankReq.setPointedMember(queryRankRequest.getPointedMember());
            }
            //不指定不过滤敏感词，都是要过滤的，敏感词目前只有http的榜单接口默认要过滤，其他调用可以不用
            if (!StringUtil.ZERO.equals(queryRankRequest.getIsFilterWord())) {
                String sensitiveWordConfig = "";
                //module,替换内容
                String jySensitiveWordConfig = commonService.getActAttr(actId, "jy_sensitive_word_config");
                if (StringUtils.isNotBlank(jySensitiveWordConfig)) {
                    String[] configA = jySensitiveWordConfig.split(",");
                    String module = configA[0];
                    String replaceWord = configA.length > 1 ? configA[1] : "*";
                    if (StringUtils.isNumeric(module)) {
                        List<String> sensitiveWords = ftsSensitiveService.getSensitiveWordsConfigCache(Integer.valueOf(module));
                        sensitiveWordConfig = StringUtils.join(sensitiveWords, "|") + "," + replaceWord;
                    } else {
                        log.error("queryRank jy_sensitive_word_config config error,{}", jySensitiveWordConfig);
                    }
                } else {
                    sensitiveWordConfig = commonService.getActAttr(actId, "sensitive_word_config");
                }
                getRankReq.setSensitiveWordFilter(sensitiveWordConfig);
            }
            if (StringUtil.ONE.equals(queryRankRequest.getShowZeroItem()) || StringUtil.isEmpty(queryRankRequest.getShowZeroItem())) {
                getRankReq.setShowZeroItem(true);
            } else {
                getRankReq.setShowZeroItem(false);
            }
            List<String> extQueryList = Lists.newArrayList();
            if (StringUtil.isNotBlank(queryRankRequest.getExtQuery())) {
                extQueryList = Lists.newArrayList(queryRankRequest.getExtQuery().split(","));
            }
            getRankReq.setExtQueryList(extQueryList);
            getRankReq.setShowType(queryRankRequest.getShowType() == null ? 1 : queryRankRequest.getShowType());

            RankInfo rankInfo = hdztRankService.getRankInfo(getRankReq);
            return new Response<>(Code.OK.getCode(), "ok", rankInfo);
        } catch (Throwable t) {
            log.error("queryRankException@loginUid:{}, ip:{}, req:{}, err:{} {}", loginUid, RequestUtil.getRealIp(),
                    JSON.toJSONString(queryRankRequest), t.getMessage(), clock.tag(), t);
            return Response.fail(Code.E_SYS_BUSY.getCode(), "E5134#系统繁忙！");
        }
    }

    @Report
    @RequestMapping(value = "/queryRankWithTopCP")
    public Response<RankInfo> queryRankWithTopCP(HttpServletRequest req, HttpServletResponse resp
            , @Validated QueryRankRequest queryRankRequest) {
        GetRankReq getRankReq = buildRankReq(req, resp, queryRankRequest);
        log.info("loginUid={}", getRankReq.getLoginUid());
        RankInfo rankInfo = topCPService.queryRankWithTopCP(getRankReq);

        return new Response<>(Code.OK.getCode(), "ok", rankInfo);
    }

    @Report
    //给运营导出榜单数据
    @RequestMapping(value = "/exportRank")
    public RankInfo exportRank(HttpServletRequest req, HttpServletResponse resp,
                               Long actId,
                               Long rankCount,
                               Long rankId, Long phaseId, String dateStr, String findSrcMember, String showZeroItem) {

        GetRankReq getRankReq = new GetRankReq();
        getRankReq.setRankId(rankId);
        getRankReq.setPhaseId(Convert.toLong(phaseId, 0));
        getRankReq.setDateStr(dateStr);
        getRankReq.setActId(actId);

        long maxRankCount = Const.GEPM.getParamValueToLong(GeParamName.HDZT_MAX_EXPORT_RANK_COUNT, 1000);
        if (rankCount > maxRankCount) {
            rankCount = maxRankCount;
        }

        getRankReq.setRankCount(rankCount);
        getRankReq.setFindSrcMember(findSrcMember);
        getRankReq.setLoginUid(getLoginYYUid(req, resp));
        getRankReq.setShowZeroItem(StringUtil.ONE.equals(showZeroItem));

        return hdztRankService.getRankInfo(getRankReq);
    }


    /**
     * 通用浮层信息查询接口
     */
    @Report
    @RequestMapping(value = "/queryLayerInfo")
    public Response<Map<String, Object>> queryLayerInfo(HttpServletRequest req, HttpServletResponse resp,
                                                        Long actId, Long sid, Long ssid) {
        Clock clock = new Clock();
        try {

             if (actId == null || sid == null || ssid == null) {
                log.warn("para error,actid:{},sid:{},ssid:{}", actId, sid, ssid);
                return new Response<Map<String, Object>>(Code.E_DATA_ERROR.getCode(), "para error", null);
            }
            Map<String, Object> result = Maps.newHashMap();

            LayerBroadcastInfo layerBroadcastInfo = null;
            Date now = commonService.getNow(actId);
            String from = "web";
            if (actLayerInfoService.inSettle(actId, now)) {
                layerBroadcastInfo = actLayerInfoService.buildLayerInfo(actId, sid, ssid, now, from);
            } else {
                layerBroadcastInfo = actLayerInfoService.getLayerInfo(actId, sid, ssid, from);
            }
            result.put("layerInfo", layerBroadcastInfo);

            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
            result.put("actInfo", activityInfoVo);

            log.info("queryLayerInfo done,actId:{},sid:{},ssid:{},uid:{}", actId, sid, ssid, getLoginYYUid());
            return new Response<Map<String, Object>>(Code.OK.getCode(), "ok", result);
        } catch (Throwable e) {
            log.error("buildLayerInfo exception@ip:{}, actId:{}, sid:{}, ssid:{}, err:{} {}", RequestUtil.getRealIp(req),
                    actId, sid, ssid, e.getMessage(), clock.tag(), e);
            return Response.fail(Code.E_SYS_BUSY.getCode(), "E5136#系统繁忙！");
        }
    }

    @Report
    //查询浮层挂件信息
    @RequestMapping(value = "/queryCurItem")
    public Response<RankCurItemVo> queryCurItem(HttpServletRequest req, HttpServletResponse resp,
                                                Long actId, Long rankId, Long sid, Long ssid, Long userId, String dateStr) {
        Clock clock = new Clock();
        try {
            RankCurItemVo vo = hdztRankService.queryCurItem(actId, rankId, sid, ssid, userId, dateStr);
            return new Response<>(Code.OK.getCode(), "ok", vo);
        } catch (Exception e) {
            log.error("queryCurItem exception,actId:{},sid:{},ssid:{},rankId:{},clock:{},err:{} {}", actId, sid, ssid, rankId, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 通用浮层信息查询接口
     */
    @Report
    @RequestMapping(value = "/queryLayerStatus")
    public Response<Map<String, Object>> queryLayerStatus(String seq, Long actId, Long sid, Long ssid) {
        log.info("queryLayerStatus,actId:{},sid:{},ssid:{},seq:{}", actId, sid, ssid, seq);
        Clock clock = new Clock();
        try {
            if (actId == null || sid == null || ssid == null) {
                log.warn("para error,actid:{},sid:{},ssid:{}", actId, sid, ssid);
                return new Response<>(Code.E_DATA_ERROR.getCode(), "para error", null);
            }
            Map<String, Object> result = actLayerInfoService.queryLayerStatus(actId, sid, ssid);
            if (SysEvHelper.isDev()) {
                log.info("queryLayerStatus,actId:{},sid:{},ssid:{},seq:{},result:{}", actId, sid, ssid, seq, JSON.toJSONString(result));
            }
            return new Response<>(Code.OK.getCode(), "ok", result);
        } catch (Exception e) {
            log.error("buildLayerInfo exception,actId:{},seq:{},sid:{},ssid:{},clock:{},err:{} {}", actId, seq, sid, ssid, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }


    @Report
    //查询用户当前任务完成情况
    @RequestMapping(value = "/queryRoomTaskInfo")
    public Response<TaskUserInfoVo> queryRoomTaskInfo(HttpServletRequest req, HttpServletResponse resp, Long ssid,
                                                      Long actId, Long rankId, Long phaseId, String dateStr) {
        Clock clock = new Clock();
        RoomInfo roomInfo = commonService.getRoomInfoBySsid(ssid);
        if (roomInfo == null || roomInfo.getRoomId() <= 0) {
            return Response.ok();
        }

        try {
            TaskUserInfoVo vo = hdztTaskService.getUserTaskInfo(Convert.toLong(roomInfo.getRoomId()), actId, rankId, phaseId == null ? 0L : phaseId, dateStr);
            if(vo == null){
                return Response.ok();
            }
            String title = roomInfo.getTitle();
            String cover = roomInfo.getCover();
            vo.setNick(title == null ? "" : title);
            vo.setAvatar(cover == null ? "" : cover);
            return new Response<>(Code.OK.getCode(), "ok", vo);
        } catch (Exception e) {
            log.error("queryRoomTaskInfo exception,actId:{},rankId:{},phaseId:{},ssid:{} clock:{},err:{}",
                    actId, rankId, phaseId, ssid, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    @Report
    //查询用户当前任务完成情况
    @RequestMapping(value = "/queryTaskUserInfo")
    public Response<TaskUserInfoVo> queryTaskUserInfo(HttpServletRequest req, HttpServletResponse resp, Long uid,
                                                      Long actId, Long rankId, Long phaseId, String dateStr) {

        Clock clock = new Clock();
        try {
            TaskUserInfoVo vo = hdztTaskService.getUserTaskInfo(uid, actId, rankId, phaseId == null ? 0L : phaseId, dateStr);
            return new Response<TaskUserInfoVo>(Code.OK.getCode(), "ok", vo);
        } catch (Exception e) {
            log.error("queryTaskUserInfo exception,actId:{},rankId:{},phaseId:{},clock:{},err:{} {}",
                    actId, rankId, phaseId, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }


    //查询突破任务奖励
    @Report
    @RequestMapping(value = "/queryUserTaskAward")
    public Response<List<ActAwardRecordInfo>> queryUserTaskAward(HttpServletRequest req, HttpServletResponse resp, Long taskId, Long uid) {
        return new Response<List<ActAwardRecordInfo>>(Code.OK.getCode(), "ok", userTaskService.getAwardRecordInfo(taskId, uid));
    }

    //查询用户个性化任务
    @Report
    @RequestMapping(value = "/queryActMemberTask")
    public Response<List<MemberTaskVo>> queryActMemberTask(HttpServletRequest req, HttpServletResponse resp,
                                                           Long actId, String memberIds, Integer roleType, Integer taskStatus) {

        Clock clock = new Clock();
        try {
            //用户看到的是1，产品运营可以个性化传入非正常状态来预览任务
            taskStatus = taskStatus == null ? 1 : taskStatus;
            List<MemberTaskVo> vo = actTaskService.queryActMemberTask(actId, memberIds, roleType, taskStatus);
            return new Response<List<MemberTaskVo>>(Code.OK.getCode(), "ok", vo);
        } catch (Exception e) {
            log.error("queryActMemberTask exception,actId:{},memberIds:{},roleType:{},clock:{},err:{} {}",
                    actId, memberIds, roleType, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }

    }

    /**
     * 活动结果（2021-11-25 前的活动使用这个接口查询） - modified by guoliping/2021-11-25
     */
    @Report
    @RequestMapping(value = "/queryActResult")
    public Response<ActResultInfo> queryActResult(HttpServletRequest req, HttpServletResponse resp,
                                                  Long actId, @RequestParam(defaultValue = StringUtil.ONE) String type) {
        Clock clock = new Clock();
        try {
            List<Integer> types = Arrays.asList(type.split(",")).stream().map((x) -> Integer.parseInt(x))
                    .collect(Collectors.toList());
            ActResultInfo actResultInfo = actResultService.queryActResult(actId, types, 1);
            return new Response<ActResultInfo>(Code.OK.getCode(), "ok", actResultInfo);
        } catch (Exception e) {
            log.error("queryActResult exception,actId:{},type:{},status:{},clock:{},err:{} {}",
                    actId, type, 1, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 活动结果（2021-11-25 后的活动使用这个接口查询） - modified by guoliping/2021-11-25
     */
    @Report
    @RequestMapping(value = "/queryActResultNew")
    public Response<ActResultInfoNew> queryActResultNew(HttpServletRequest req, HttpServletResponse resp,
                                                        Long actId, @RequestParam(defaultValue = StringUtil.ONE) String type) {
        Clock clock = new Clock();
        try {
            List<Integer> types = Arrays.asList(type.split(",")).stream().map((x) -> Integer.parseInt(x))
                    .collect(Collectors.toList());
            ActResultInfoNew actResultInfo = actResultService.queryActResultNew(actId, types, 1);
            return new Response<ActResultInfoNew>(Code.OK.getCode(), "ok", actResultInfo);
        } catch (Exception e) {
            log.error("queryActResult exception,actId:{},type:{},status:{},clock:{},err:{}",
                    actId, type, 1, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    @Report
    @RequestMapping("/queryPkRecords")
    public Response<List<PKRecord>> queryPkRecords(Long actId, @RequestParam(defaultValue = "pk_record") String recordKey,
                                                   @RequestParam(defaultValue = "channel") String type) {
        List<PKRecord> pkRecords = hallPKRecordV2Component.queryPKRecordByKey(actId, recordKey, type);
        return Response.success(pkRecords);
    }

    @Report
    @RequestMapping(value = "/getBusiId")
    public Response<Long> getBusiId(Long sid, Long ssid) {
        return new Response<Long>(Code.OK.getCode(), "ok", webdbThriftClient.getBusiId(sid, ssid));
    }

    @Report
    @RequestMapping(value = "/getAsid")
    public Response<Long> getBusiId(Long sid) {
        return new Response<Long>(Code.OK.getCode(), "ok", commonService.getAsid(sid));
    }


    /**
     * 小时榜倒计时
     */
    @Report
    @RequestMapping(value = "/hourRankLeftSeconds")
    public Response<Map<String, Object>> hourRankLeftSeconds(HttpServletRequest req, HttpServletResponse resp, Long actId, Long rankId) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("leftSeconds", hdztRankService.hourRankLeftSeconds(actId, rankId));
        return Response.success(result);
    }

    /**
     * 半小时榜倒计时
     */
    @Report
    @RequestMapping(value = "/halfHourRankLeftSeconds")
    public Response<Map<String, Object>> halfHourRankLeftSeconds(HttpServletRequest req, HttpServletResponse resp, Long actId, Long rankId) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("leftSeconds", hdztRankService.halfHourRankLeftSeconds(actId, rankId));
        return Response.success(result);
    }


    /**
     * 修改分组信息（危险操作）
     */
    @RequestMapping(value = "/updateEnrollment")
    @Report
    public Response<String> updateEnrollment(HttpServletRequest req, HttpServletResponse resp
            , long actId, String memberId, long roleType, long newRoleId, Long newSignSid) {
        String seq = UUID.randomUUID().toString();
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }

        String whiteList = Const.GEPM.getParamValue("update_enrollment_white_list_" + actId, "");
        String loginUid = "," + uid;
        if (!whiteList.contains(loginUid)) {
            return Response.fail(-1, "未授权");
        }

        long signSid = Convert.toLong(newSignSid, 0);
        String result = enrollmentNewService.updateEnrollInfo(seq, uid, actId, memberId, roleType, newRoleId, signSid);
        return Response.ok(result + ":" + DateUtil.format(new Date()));
    }

    @RequestMapping("queryRankingTaskInfoV2")
    @Report
    public Response queryRankingTaskInfoV2(HttpServletRequest req, HttpServletResponse resp
            , Long actId, Integer componentIndex, @RequestParam(name = "type", defaultValue = StringUtil.ZERO) int type
            , @RequestParam(name = "uid", defaultValue = StringUtil.ZERO) long uid
            , @RequestParam(name = "dateStr", defaultValue = "") String dateStr) {
        if (uid <= 0) {
            uid = getLoginYYUid(req, resp);
        }
        Map<String, UserTaskInfo> data = rankingTaskQueryComponent.queryUserTaskV2(actId, componentIndex, uid, type, dateStr);
        Map<String, Object> result = Maps.newHashMap();
        result.putAll(data);
        result.put("userInfo", getUserInfo(uid));

        return Response.success(result);
    }

    @RequestMapping("queryChannelCurrentTask")
    @Report
    public Response queryChannelCurrentTask(HttpServletRequest req, HttpServletResponse resp
            , Long actId, Integer componentIndex
            , @RequestParam(name = "sid", defaultValue = StringUtil.ZERO) long sid
            , @RequestParam(name = "dateStr", defaultValue = "") String dateStr) {
        UserTaskVo data = rankingTaskQueryComponent.queryChannelCurrentTask(actId, componentIndex, sid, dateStr);
        Map<String, Object> result = Maps.newHashMap();
        result.put("currentTask", data);
        result.put("channelInfo", commonService.getChannelInfo(sid, true));

        return Response.success(result);
    }

    private Map<String, Object> getUserInfo(long uid) {
        UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, false);
        Map<String, Object> userInfo = Maps.newHashMap();
        userInfo.put("uid", uid);
        userInfo.put("nickname", userBaseInfo.getNick());
        userInfo.put("logo", userBaseInfo.getLogo());

        return userInfo;
    }

    /**
     * 查询榜单展示配置，目前用于交友右侧公屏榜单
     *
     * @param actId 活动id
     * @return 展示配置
     */
    @RequestMapping(value = "/queryActShowRankConfig")
    @Report
    public Response<List<RankShowConfigVo>> queryActShowRankConfig(Long actId, Long componentIndex) {
        return Response.success(rankingShowComponent.getEffectShowRankConfig(actId, componentIndex));
    }

    @RequestMapping("/queryActPKRecordShow")
    @Report
    public Response queryActPKRecordShow() {
        return null;
    }

    @RequestMapping("queryRankingTaskInfo")
    @Report
    public Response queryRankingTaskInfo(HttpServletRequest req, HttpServletResponse resp
            , Long actId, Integer componentIndex, @RequestParam(name = "type", defaultValue = StringUtil.ZERO) int type
            , @RequestParam(name = "uid", defaultValue = StringUtil.ZERO) long uid
            , @RequestParam(name = "dateStr", defaultValue = "") String dateStr) {
        if (uid <= 0) {
            uid = getLoginYYUid(req, resp);
        }
        Map<String, List<UserTaskVo>> data = rankingTaskQueryComponent.queryUserTask(actId, componentIndex, uid, type, dateStr);

        return Response.success(data);
    }

    @RequestMapping("queryUserCurrentTask")
    @Report
    public Response queryUserCurrentTask(HttpServletRequest req, HttpServletResponse resp
            , Long actId, Integer componentIndex, @RequestParam(name = "type", defaultValue = StringUtil.ZERO) int type
            , @RequestParam(name = "uid", defaultValue = StringUtil.ZERO) long uid
            , @RequestParam(name = "dateStr", defaultValue = "") String dateStr) {
        if (uid <= 0) {
            uid = getLoginYYUid(req, resp);
        }
        Map<String, UserTaskVo> data = rankingTaskQueryComponent.queryUserCurrentTask(actId, componentIndex, uid, type, dateStr);
        Map<String, Object> result = Maps.newHashMap();
        result.putAll(data);
        result.put("userInfo", getUserInfo(uid));

        return Response.success(result);
    }


    // 查询cp榜top
    @Report
    @RequestMapping(value = "/queryCPRankTop")
    public Response<Object> queryCPRankTop(HttpServletRequest req, HttpServletResponse resp,
                                           @Validated QueryRankRequest queryRankRequest) {
        GetRankReq getRankReq = buildRankReq(req, resp, queryRankRequest);

        RankInfo rankInfo = topCPService.queryCPRankTop(getRankReq);

        Map<String, Object> data = Maps.newHashMap();
        data.put("list", rankInfo.getList());

        return new Response<>(Code.OK.getCode(), "ok", data);
    }

    // 查询cp榜top列表
    @Report
    @RequestMapping(value = "/listCPRankTop")
    public Response<Object> listCPRankTop(HttpServletRequest req, HttpServletResponse resp,
                                          @Validated QueryRankRequest queryRankRequest) throws Exception {
        GetRankReq getRankReq = buildRankReq(req, resp, queryRankRequest);

        Map<Long, RankItemUserAnchor> data = topCPService.listCPRankTop(getRankReq);
        Map<String, Object> result = Maps.newHashMap();
        result.put("list", data);

        return new Response<>(Code.OK.getCode(), "ok", result);
    }

    private GetRankReq buildRankReq(HttpServletRequest req, HttpServletResponse resp,
                                    @Validated QueryRankRequest queryRankRequest) {
        long loginUid = getLoginYYUid(req, resp);
        Long actId = queryRankRequest.getActId();

        GetRankReq getRankReq = new GetRankReq();
        getRankReq.setRankId(queryRankRequest.getRankId());
        getRankReq.setPhaseId(Convert.toLong(queryRankRequest.getPhaseId(), 0));
        getRankReq.setDateStr(queryRankRequest.getDateStr());
        getRankReq.setActId(actId);
        getRankReq.setRankType(queryRankRequest.getRankType());
        getRankReq.setFindSrcMember(queryRankRequest.getFindSrcMember());
        getRankReq.setLoginUid(loginUid);

        if (StringUtil.ONE.equals(queryRankRequest.getShowZeroItem()) || StringUtil.isEmpty(queryRankRequest.getShowZeroItem())) {
            getRankReq.setShowZeroItem(true);
        } else {
            getRankReq.setShowZeroItem(false);
        }

        getRankReq.setPointedMember(queryRankRequest.getPointedMember());

        List<String> extQueryList = Lists.newArrayList();
        if (StringUtil.isNotBlank(queryRankRequest.getExtQuery())) {
            extQueryList = Lists.newArrayList(queryRankRequest.getExtQuery().split(","));
        }
        getRankReq.setExtQueryList(extQueryList);
        getRankReq.setShowType(queryRankRequest.getShowType() == null ? 1 : queryRankRequest.getShowType());

        long maxRankCount = Const.GEPM.getParamValueToLong(GeParamName.HDZT_MAX_RANK_COUNT, MAX_RANK_COUNT);
        Long rankCount = queryRankRequest.getRankCount() == null ? maxRankCount : Math.min(queryRankRequest.getRankCount(), maxRankCount);
        getRankReq.setRankCount(rankCount);

        return getRankReq;
    }

    @RequestMapping("/queryReportData")
    @Report
    public Response<JSONObject> queryReportData(Long actId, String seq) {
        if (actId == null) {
            return Response.fail(-1, "actid is null");
        }
        String value = activityGloryReportComponent.getReportData(actId, seq);
        if (StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            JSONObject data = JSON.parseObject(value);
            return Response.success(data);
        }

        return Response.success(new JSONObject());
    }
}
