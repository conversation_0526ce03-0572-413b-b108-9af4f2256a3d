<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovMatchNodeMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchNode">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phase_id" jdbcType="BIGINT" property="phaseId" />
    <result column="round_id" jdbcType="BIGINT" property="roundId" />
    <result column="node_index" jdbcType="INTEGER" property="nodeIndex" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="team_id" jdbcType="BIGINT" property="teamId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="advance_type" jdbcType="INTEGER" property="advanceType" />
    <result column="team_state" jdbcType="INTEGER" property="teamState" />
    <result column="game_count" jdbcType="INTEGER" property="gameCount" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="alive_score" jdbcType="INTEGER" property="aliveScore" />
  </resultMap>
  <sql id="Base_Column_List">
    id, phase_id, round_id, node_index, state, team_id, uid, advance_type, team_state,
    game_count, score, alive_score
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from aov_match_node
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from aov_match_node
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchNode">
    insert into aov_match_node (id, phase_id, round_id,
    node_index, state, team_id,
    uid, advance_type, team_state,
    game_count, score, alive_score
    )
    values (#{id,jdbcType=BIGINT}, #{phaseId,jdbcType=BIGINT}, #{roundId,jdbcType=BIGINT},
    #{nodeIndex,jdbcType=INTEGER}, #{state,jdbcType=INTEGER}, #{teamId,jdbcType=BIGINT},
    #{uid,jdbcType=BIGINT}, #{advanceType,jdbcType=INTEGER}, #{teamState,jdbcType=INTEGER},
    #{gameCount,jdbcType=INTEGER}, #{score,jdbcType=INTEGER}, #{aliveScore,jdbcType=INTEGER}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchNode">
    insert into aov_match_node
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="phaseId != null">
        phase_id,
      </if>
      <if test="roundId != null">
        round_id,
      </if>
      <if test="nodeIndex != null">
        node_index,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="teamId != null">
        team_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="advanceType != null">
        advance_type,
      </if>
      <if test="teamState != null">
        team_state,
      </if>
      <if test="gameCount != null">
        game_count,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="aliveScore != null">
        alive_score,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="phaseId != null">
        #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="roundId != null">
        #{roundId,jdbcType=BIGINT},
      </if>
      <if test="nodeIndex != null">
        #{nodeIndex,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        #{state,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        #{teamId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="advanceType != null">
        #{advanceType,jdbcType=INTEGER},
      </if>
      <if test="teamState != null">
        #{teamState,jdbcType=INTEGER},
      </if>
      <if test="gameCount != null">
        #{gameCount,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        #{score,jdbcType=INTEGER},
      </if>
      <if test="aliveScore != null">
        #{aliveScore,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchNode">
    update aov_match_node
    <set>
      <if test="phaseId != null">
        phase_id = #{phaseId,jdbcType=BIGINT},
      </if>
      <if test="roundId != null">
        round_id = #{roundId,jdbcType=BIGINT},
      </if>
      <if test="nodeIndex != null">
        node_index = #{nodeIndex,jdbcType=INTEGER},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=INTEGER},
      </if>
      <if test="teamId != null">
        team_id = #{teamId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="advanceType != null">
        advance_type = #{advanceType,jdbcType=INTEGER},
      </if>
      <if test="teamState != null">
        team_state = #{teamState,jdbcType=INTEGER},
      </if>
      <if test="gameCount != null">
        game_count = #{gameCount,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=INTEGER},
      </if>
      <if test="aliveScore != null">
        alive_score = #{aliveScore,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovMatchNode">
    update aov_match_node
    set phase_id = #{phaseId,jdbcType=BIGINT},
    round_id = #{roundId,jdbcType=BIGINT},
    node_index = #{nodeIndex,jdbcType=INTEGER},
    state = #{state,jdbcType=INTEGER},
    team_id = #{teamId,jdbcType=BIGINT},
    uid = #{uid,jdbcType=BIGINT},
    advance_type = #{advanceType,jdbcType=INTEGER},
    team_state = #{teamState,jdbcType=INTEGER},
    game_count = #{gameCount,jdbcType=INTEGER},
    score = #{score,jdbcType=INTEGER},
    alive_score = #{aliveScore,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>