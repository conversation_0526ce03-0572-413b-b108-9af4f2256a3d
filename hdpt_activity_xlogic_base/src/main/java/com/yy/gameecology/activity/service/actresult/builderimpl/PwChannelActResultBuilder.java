package com.yy.gameecology.activity.service.actresult.builderimpl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.service.actresult.ActResultMemberLoader;
import com.yy.gameecology.activity.service.impl.ActPwSupportService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRoleId;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.thrift.act_ext_support.MemberItemInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-11-17 16:12
 **/
@Component
public class PwChannelActResultBuilder extends BuilderBase  implements ActResultMemberLoader {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActPwSupportService actPwSupportService;

    @Override
    public Map<String, Map<String, MemberInfo>> loadMemberInfo(long actId, long type, long roleType, List<String> memberIds) {
        Map<String, MemberInfo> memberInfoMapResult = Maps.newHashMap();

        Map<String, List<String>> memberIdMap = Maps.newHashMap();
        memberIdMap.put(HdztRoleId.PW_CHANNEL + "", memberIds);
        Map<String, Map<String, MemberItemInfo>> pwChannelMap = actPwSupportService.queryMember(0, 0, memberIdMap);
        Map<String, MemberItemInfo> pwChannelInfo = pwChannelMap.getOrDefault(HdztRoleId.PW_CHANNEL + "", Maps.newHashMap());
        for (String memberId : pwChannelInfo.keySet()) {
            MemberItemInfo memberItemInfo = pwChannelInfo.get(memberId);

            MemberInfo memberInfo = new MemberInfo();
            memberInfo.setName(memberItemInfo.getBaseFieldMemberName());
            String memberUrl = memberItemInfo.getBaseFieldMemberUrl();

            String asid = Optional.ofNullable(memberItemInfo.getExt()).map(map -> map.get("asid")).orElse(memberId);
            memberInfo.setAsid(asid);
            memberInfo.setSid(memberId);
            if (StringUtil.isBlank(memberUrl)) {
                memberInfo.setLogo(Const.IMAGE.DEFAULT_CHANNEL_LOGO);
                memberInfo.setHdLogo(Const.IMAGE.DEFAULT_CHANNEL_LOGO);
            } else {
                memberInfo.setLogo(memberUrl);
                memberInfo.setHdLogo(memberUrl);
            }

            memberInfoMapResult.put(memberId, memberInfo);
        }

        return ImmutableMap.of(type + "_" + roleType, memberInfoMapResult);
    }
}
