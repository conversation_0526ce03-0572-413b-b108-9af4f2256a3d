package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.redis.SeedAnchorWithTiGuanComponent;
import com.yy.gameecology.hdzj.element.history.TiGuanComponent;
import com.yy.gameecology.hdzj.element.component.attr.SeedAnchorWithTiGuanComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.TiGuanComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.atomic.AtomicLong;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties"})
public class TiGuanComponentTest {

    static {
        System.setProperty("group", "4");}

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    AtomicLong counter = new AtomicLong(0);

    @Autowired
    private TiGuanComponent tiGuanComponent;

    public static final long ACT_ID = 2022064001L;

    @Test
    public void onPromotTimeEnd() throws Exception {
       TiGuanComponentAttr attr = tiGuanComponent.getComponentAttr(ACT_ID, 1);
         PromotTimeEnd event1 = new PromotTimeEnd();
        event1.setActId(ACT_ID);
        event1.setRankId(5);
        event1.setUri(PromotTimeEnd.URI);
        event1.setTimestamp(DateUtil.today());
        event1.setEkey("PromotTimeEnd_2022064001_5_3");
        event1.setIndex(0);
        event1.setPhaseId(3);
        event1.setTimeKey(0);
        event1.setEndTime("2022-08-09 23:59:59");
        event1.setPromotKey("PromotTimeEnd_2022064001_5_3");
        tiGuanComponent.onPromotTimeEnd(event1, attr);

        PromotTimeEnd event2 = new PromotTimeEnd();
        event2.setActId(ACT_ID);
        event2.setRankId(6);
        event2.setUri(PromotTimeEnd.URI);
        event2.setTimestamp(DateUtil.today());
        event2.setEkey("PromotTimeEnd_2022064001_6_3");
        event2.setIndex(0);
        event2.setPhaseId(3);
        event2.setTimeKey(0);
        event2.setEndTime("2022-08-09 23:59:59");
        event2.setPromotKey("PromotTimeEnd_2022064001_6_3");
        tiGuanComponent.onPromotTimeEnd(event2, attr);

        System.out.println("done!");
    }

    @Test
    public void onPromotTimeEnd2() throws Exception {
        TiGuanComponentAttr attr = tiGuanComponent.getComponentAttr(ACT_ID, 2);
        PromotTimeEnd event1 = new PromotTimeEnd();
        event1.setActId(ACT_ID);
        event1.setRankId(5);
        event1.setUri(PromotTimeEnd.URI);
        event1.setTimestamp(DateUtil.today());
        event1.setEkey("PromotTimeEnd_2022064001_5_4");
        event1.setIndex(0);
        event1.setPhaseId(4);
        event1.setTimeKey(0);
        event1.setEndTime("2022-08-11 23:59:59");
        event1.setPromotKey("PromotTimeEnd_2022064001_5_4");
        tiGuanComponent.onPromotTimeEnd(event1, attr);

        PromotTimeEnd event2 = new PromotTimeEnd();
        event2.setActId(ACT_ID);
        event2.setRankId(6);
        event2.setUri(PromotTimeEnd.URI);
        event2.setTimestamp(DateUtil.today());
        event2.setEkey("PromotTimeEnd_2022064001_6_4");
        event2.setIndex(0);
        event2.setPhaseId(4);
        event2.setTimeKey(0);
        event2.setEndTime("2022-08-11 23:59:59");
        event2.setPromotKey("PromotTimeEnd_2022064001_6_4");
        tiGuanComponent.onPromotTimeEnd(event2, attr);

        System.out.println("done!");
    }

    @Test
    public void onPhaseTimeEnd() throws Exception {
        TiGuanComponentAttr attr = tiGuanComponent.getComponentAttr(ACT_ID, 1);
        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setActId(ACT_ID);
        event.setRankId(101);
        event.setUri(PhaseTimeEnd.URI);
        event.setTimestamp(DateUtil.today());
        event.setEkey("PhaseTimeEnd_2022064001_101_3");
        event.setIndex(0);
        event.setPhaseId(3);
        event.setTimeKey(0);
        event.setEndTime("2022-08-09 23:59:59");
        tiGuanComponent.onPhaseTimeEnd(event, attr);
        System.out.println("done!");
    }

    @Test
    public void sysEnroll() throws Exception {
       // tiGuanComponent.sysEnroll();
        System.out.println("done!");
    }

    @Test
    public void giveAwards() throws Exception {
         tiGuanComponent.giveAwards();
        System.out.println("done!");
    }

    @Autowired
    private SeedAnchorWithTiGuanComponent seedAnchorWithTiGuanComponent;

    @Test
    public void onPhaseTimeEnd1() throws Exception {
        SeedAnchorWithTiGuanComponentAttr attr = seedAnchorWithTiGuanComponent.getComponentAttr(ACT_ID, 1);
        PhaseTimeEnd event = new PhaseTimeEnd();
        event.setActId(ACT_ID);
        event.setRankId(5);
        event.setUri(PhaseTimeEnd.URI);
        event.setTimestamp(DateUtil.today());
        event.setEkey("PhaseTimeEnd_2022064001_5_5");
        event.setIndex(0);
        event.setPhaseId(5);
        event.setTimeKey(0);
        event.setEndTime("2022-08-13 23:59:59");
        seedAnchorWithTiGuanComponent.onPhaseTimeEnd(event, attr);
        System.out.println("done!");
    }

    @Test
    public void challenge() {
        tiGuanComponent.challenge(ACT_ID, 1, 67061130, 1025450810);
    }



}
