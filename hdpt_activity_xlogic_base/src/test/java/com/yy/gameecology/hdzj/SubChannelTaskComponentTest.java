package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.hdzj.element.redis.SubChannelTaskComponent;
import com.yy.gameecology.hdzj.element.component.attr.SubChannelTaskComponentAttr;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.RoleType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-09-27 21:28
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class SubChannelTaskComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private SubChannelTaskComponent subChannelTaskComponent;

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Test
    public void invokeTest() {
        RankingScoreChanged event = JSON.parseObject("{\"actId\":2022091001,\"actors\":{50003:\"50042952\",50004:\"11297016\",50055:\"11297016_11297016\",50014:\"50042952\"},\"busiId\":500,\"ekey\":\"\",\"index\":0,\"itemCount\":1,\"itemId\":\"JY_520\",\"itemScore\":52000,\"member\":\"11297016_11297016\",\"occurTime\":\"2022-10-25 17:00:11\",\"phaseId\":100,\"phaseScore\":5047090,\"rankId\":68,\"rankScore\":5046090,\"seq\":\"54719132-7e07-416a-b6a1-6cd1d8bae4f4\",\"timeKey\":1,\"timestamp\":\"2022-10-25 17:00:11\",\"uri\":2002}", RankingScoreChanged.class);
        subChannelTaskComponent.onRankingScoreChanged(event, subChannelTaskComponent.getComponentAttr(2022091001, 500));
    }

    @Test
    public void queryAwardList() {
        subChannelTaskComponent.queryAwardList(2022091001, 500);
    }

    @Test
    public void getTaskRecordTest() {
        subChannelTaskComponent.getTaskRecord(0, 2022091001, 11297016L, 2434034302L, 500);
    }

    @Test
    public void initMemberTest() {
        PromotTimeEnd event = new PromotTimeEnd();
        event.setActId(2022091001);
        event.setRankId(22);
        event.setPhaseId(21);
        SubChannelTaskComponentAttr attr = subChannelTaskComponent.getComponentAttr(2022091001, 500);
        subChannelTaskComponent.invokePromotTimeEnd(event, attr);
    }

    @Test
    public void memberTest() {
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(2022091001L, 0L, RoleType.HALL.getValue(), "1451119345_2804720979", 3);
        MemberInfo memberInfo = memberInfoService.getMemberInfo(enrollmentInfo.getRoleBusiId(), RoleType.findByValue((int) enrollmentInfo.getRoleType()), "1451119345_2804720979");

    }
}
