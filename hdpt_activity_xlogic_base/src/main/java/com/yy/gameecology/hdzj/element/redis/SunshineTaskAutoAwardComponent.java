package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.BabyMissionItem;
import com.yy.gameecology.activity.bean.actlayer.TaskItem;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BigDataService;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.LoginService;
import com.yy.gameecology.common.bean.PairBean;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.BigDataScoreType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.ActTaskScore;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.ReadFileUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardRecordInfo;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.SunshineTaskConfig;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SunshineTaskAutoAwardComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

/**
 * 主播成就，高光任务<p>
 * 没有突破逻辑了,每天的过任务数值是一样的
 * <p>
 * A. 个人任务<p>
 * 1. 根据主播配置不同的任务分值,不配置则改主播没个人任务,初始化数据由产品提供(根据主播的最近流水进行设置)<p>
 * 2. 主播任务初始化：提供一个文本文件链接,每个用户一行记录,每行的格式为：{uid},{score}<p>
 * 3. 配置管理员白名单和初始化文件链接,调用 {host}/cmpt/SunshineTaskComponentV3/initSingleTaskConfig进行初始化
 * <p>
 * B. 全站任务<p>
 * 1. 根据角色进行配置,一个角色可以对应多个任务
 * <p>
 * C. 任务完成<p>
 * 1. 中台有一个榜单存储用户的过任务数值<p>
 * 2. 监听该榜单的分值改变事件RankingScoreChanged<p>
 * 3. 基于A和B构建该用户的所有任务列表,基于榜单数值判断任务的完成情况(上次的榜单分值小于任务完成值,并且本次的榜单分值大于任务完成值,则认定该任务完成)<p>
 * 4. 对完成的任务进行实时发奖
 * <p>
 * D. 发奖逻辑<p>
 * 1. 每个角色每日有发奖限额,超过该限额,则不处理发奖<p>
 * 2. 超过总奖池限额,也不处理发奖<p>
 * 3. 记录用户的奖励记录到redis list中,每个用户一个key,用于展示<p>
 * 4. 调用中台接口进行发放<p>
 * 5. 根据配置发放横幅
 * <p>
 * E. h5接口<p>
 * 1. 专题页查询h5接口
 *
 * <AUTHOR>
 */
@UseRedisStore
@Slf4j
@Component
@RestController
@RequestMapping("/cmpt/SunshineTaskComponentV3")
public class SunshineTaskAutoAwardComponent extends BaseActComponent<SunshineTaskAutoAwardComponentAttr> {

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private BigDataService bigDataService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Override
    public Long getComponentId() {
        return ComponentId.SUNSHINE_TASK_AUTO_AWARD;
    }

    /**
     * 个人突破任务分值配置
     */
    private static final String MEMBER_SINGLE_TASK = "member_single_task";

    /**
     * 总奖池总已发放
     */
    private static final String AWARD_RELEASE_AMOUNT = "award_release_amount";

    /**
     * :{yyyyMMdd}:{memberId}
     * 个人日已发放
     */
    private static final String DAY_AWARD_RELEASE_AMOUNT = "day_award_release_amount:%s:%s";

    /**
     * :{uid}
     * 奖励记录，给前端展示用
     */
    private static final String AWARD_RECORD_LIST_VIEW = "award_record_list_view:%s";

    /**
     * :{yyyyMMdd}
     * 需要发奖的key
     */
    private static final String AWARD_RECORD_SET_VIEW = "award_record_set_view:%s";


    //个人突破任务
    private static final String SINGLE = "single";

    /**
     * 个人突破任务展示名称
     */
    private static final String SINGLE_NAME = "突破";

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, SunshineTaskAutoAwardComponentAttr attr) {
        invokeOnRankingScoreChanged(event, attr);
    }

    /**
     * 重要：里面的数据操作，都要保证幂等性，可安全重试！！！
     */
    public void invokeOnRankingScoreChanged(RankingScoreChanged event, SunshineTaskAutoAwardComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        log.info("log SunshineTaskAutoAwardComponent handlerTaskEvent,event:{}", JSON.toJSONString(event));

        long actId = event.getActId();
        //榜单更新时间
        Date rankUpdateDate = DateUtil.getDate(event.getOccurTime());
        String member = event.getMember();

        handleTask(event, attr, actId, member, rankUpdateDate);
    }

    /**
     * 个人突破和全站突破任务合并查询(挂件查询用)
     */
    public List<BabyMissionItem> querySingleAndAllTask(Date now, long actId, long uid, long roleId, long index) {
        SunshineTaskAutoAwardComponentAttr attr = getComponentAttr(actId, index);
        BabyMissionItem mission = new BabyMissionItem();

        String taskTime = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);
        long score = queryAnchorTaskScore(taskTime, actId, uid, index);
        mission.setCompletedCount(score);

        List<ActTaskScore> taskScores = queryMemberTaskConfigSortByScore(actId, attr, uid + "", roleId, taskTime);
        List<TaskItem> taskConfig = convertTaskConfigView(attr, taskScores);
        mission.setTaskItems(taskConfig);

        PairBean pairBean = getCurLevelConfig(score, taskConfig);
        long curLevelConfig = pairBean.getSecond();

        mission.setTotalCount(curLevelConfig);
        mission.setLevel(pairBean.getFirst());
        int levelIndex = Convert.toInt(pairBean.getFirst()) - 1;
        if (levelIndex >= 0 && levelIndex < taskConfig.size()) {
            mission.setTaskName(taskConfig.get(levelIndex).getName());
        }

        return Lists.newArrayList(mission);
    }

    @GetMapping("/getTaskRecord")
    public Response<Map<String, Object>> getTaskRecord(HttpServletRequest req, HttpServletResponse resp, long actId, long componentUseIndex) {
        long uid = loginService.getLoginYYUid(req, resp);
        return getTaskRecord(uid, actId, componentUseIndex);
    }

    @GetMapping("/initSingleTaskConfig")
    public Response<String> initSingleTaskConfig(HttpServletRequest req, HttpServletResponse resp, long actId, long index) {
        long uid = loginService.getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(100, "未登录");
        }
        initSingleTaskData(uid, actId, index);
        return Response.success("ok");
    }


    private void handleTask(RankingScoreChanged event, SunshineTaskAutoAwardComponentAttr attr, long actId, String member, Date eventTime) {
        String taskTime = DateUtil.format(eventTime, DateUtil.PATTERN_TYPE2);
        long uid = Convert.toLong(member);
        long roleId = getMemberRoleId(actId, member);
        List<ActTaskScore> memberTaskConfig = queryMemberTaskConfigSortByScore(actId, attr, member, roleId, taskTime);
        if (CollectionUtils.isEmpty(memberTaskConfig)) {
            return;
        }
        long rankScore = event.getRankScore();
        //本次更新分数所过的任务
        List<ActTaskScore> completedList = calculateAnchorTask(memberTaskConfig, rankScore, event.getItemScore());
        if (CollectionUtils.isEmpty(completedList)) {
            return;
        }
        //扣减奖池，保存过任务记录,用于结算发奖
        long realAward = recordCompleteTask(taskTime, attr, memberTaskConfig, completedList, member, roleId);
        if (realAward <= 0) {
            log.warn("award-log-tag|奖池已经用完，本次不发放奖励,uid:{},actId:{},rankScore:{},itemScore:{}", member, attr.getActId(), event.getRankScore(), event.getItemScore());
            return;
        }
        RetryTool.withRetryCheck(actId, event.getSeq(), () -> {
        //发奖广播
        completedList.forEach(actTaskScore -> sendBanner(attr, event, uid, actTaskScore));
        });
    }

    public ActTaskScore queryMemberSingleTaskConfig(SunshineTaskAutoAwardComponentAttr attr, String member) {
        String configScore = actRedisDao.hget(getRedisGroupCode(attr.getActId()), buildSingleTaskKey(attr), member);
        if (StringUtils.isNumeric(configScore)) {
            ActTaskScore score = new ActTaskScore();
            long scoreValue = Convert.toLong(configScore);
            score.setScore(scoreValue);
            score.setLevel(attr.getSingleTaskLevel());
            score.setRemark(attr.getSingleTaskName());
            score.setTaskType(SINGLE);
            score.setAward(String.valueOf(calSingleAwardByScore(attr, scoreValue)));
            return score;
        }

        return null;
    }

    /**
     * 查询用户过任务配置列表
     */
    private List<ActTaskScore> queryMemberTaskConfigSortByScore(long actId, SunshineTaskAutoAwardComponentAttr attr, String member, long roleId, String taskTime) {
        List<ActTaskScore> actTaskScores = Lists.newArrayList();
        //个人任务
        ActTaskScore score = queryMemberSingleTaskConfig(attr, member);
        if (score != null) {
            actTaskScores.add(score);
        }
        //全站任务
        String taskType = attr.getRoleIdCommonTaskType().get(roleId);
        if (StringUtils.isNotEmpty(taskType) && attr.getTaskConfig().containsKey(taskType)) {
            Map<Long, SunshineTaskConfig> levelScore = attr.getTaskConfig().get(taskType);
            for (Long level : levelScore.keySet()) {
                SunshineTaskConfig config = levelScore.get(level);
                ActTaskScore task = new ActTaskScore();
                task.setTaskType(taskType);
                task.setLevel(level);
                task.setRemark(config.getName());
                task.setScore(config.getPassValue());
                task.setAward(config.getAward() + "");
                actTaskScores.add(task);
            }
        }

        //按分值排序
        actTaskScores.sort(Comparator.comparing(ActTaskScore::getScore));
        return actTaskScores;
    }

    private long getMemberRoleId(long actId, String member) {
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(actId, 0L, RoleType.ANCHOR.getValue(), member, 5);
        if (enrollmentInfo == null) {
            throw new RuntimeException("未找到报名信息actId:" + actId + ",memberId:" + member);
        }
        return enrollmentInfo.getDestRoleId();
    }

    private long recordCompleteTask(String taskTime, SunshineTaskAutoAwardComponentAttr attr, List<ActTaskScore> memberTaskConfig, List<ActTaskScore> completedList, String member, long roleId) {
        log.info("recordCompleteTask,taskTime:{},list:{},member:{}", taskTime, JSON.toJSONString(completedList), member);
        //本次送礼触发总发放的奖励
        long totalAward = 0;
        String group = getRedisGroupCode(attr.getActId());
        final long dayAwardLimit = attr.getRoleAwardDayLimit().getOrDefault(roleId, 0L);
        for (ActTaskScore complete : completedList) {
            long award = calCurTaskIncAward(memberTaskConfig, complete);
            long itemRealAward = 0;
            boolean dayLimitPartialAward = false;

            //日限额控制
            if (dayAwardLimit > 0) {
                String dayLimitSeq = makeKey(attr, "seq:daylimit:" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
                String dayLimitKey = buildMemberDayRelease(attr, taskTime, member);
                List<Long> memberResult = actRedisDao.incrValueWithLimitSeq(group, dayLimitSeq, dayLimitKey, award, dayAwardLimit, true, attr.getSeqExpireSeconds());
                if (memberResult.get(0) > 0) {
                    itemRealAward = memberResult.get(0) == 1 ? award : memberResult.get(1);
                }
                if (itemRealAward == 0) {
                    log.warn("award-log-tag|日限额已用完,本次任务关卡不发放奖励,member:{},act:{},award:{},itemRealAward:{},completeTask:{}", member, attr.getActId(), award, itemRealAward, JSON.toJSONString(complete));
                    continue;
                }
                dayLimitPartialAward = itemRealAward > 0 && itemRealAward < award;
                if (dayLimitPartialAward) {
                    award = itemRealAward;
                    log.info("award-log-tag|用户达到日限额，部分发放，actId:{},memberId:{},award:{},itemRealAward:{}", attr.getActId(), member, award, itemRealAward);
                }
            }

            //总扣奖池限额控制
            long poolLimit = attr.getAwardPoolConfig();
            String poolLimitSeq = makeKey(attr, "seq:poollimit::" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
            List<Long> result = actRedisDao.incrValueWithLimitSeq(group, poolLimitSeq, buildAwardReleaseKey(attr), award, poolLimit, true, attr.getSeqExpireSeconds());
            if (result.get(0) > 0) {
                itemRealAward = result.get(0) == 1 ? award : result.get(1);
            } else {
                itemRealAward = 0;
            }

            if (itemRealAward == 0) {
                log.warn("award-log-tag|奖池已用完,本次任务关卡不发放奖励,member:{},act:{},award:{},itemRealAward:{},completeTask:{}", member, attr.getActId(), award, itemRealAward, JSON.toJSONString(complete));
                //尝试发放默认奖励
                if (attr.getDefaultCount() > 0 && MapUtils.isNotEmpty(attr.getDefaultTaskPackageId())) {
                    //发奖记录展示
                    AwardRecordInfo recordInfo = new AwardRecordInfo();
                    recordInfo.setDate(System.currentTimeMillis());
                    recordInfo.setTaskType(complete.getTaskType());
                    recordInfo.setTaskLevel(complete.getLevel());
                    recordInfo.setAwardType(2);
                    recordInfo.setAwardName(attr.getDefaultAwardName());
                    recordInfo.setAward(attr.getDefaultCount());
                    recordInfo.setAwardMode(1);
                    String recordSeq = makeKey(attr, "seq:record:" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
                    actRedisDao.lPushWithSeq(group, recordSeq, buildAwardListViewKey(attr, member), JSON.toJSONString(recordInfo), attr.getSeqExpireSeconds());

                    //发默认奖励
                    releaseDefaultAward(attr, complete, taskTime, member);

                    //发单播
                    sendAwardUnicast(attr, complete, 2, attr.getDefaultCount(), Convert.toLong(member));

                    totalAward += attr.getDefaultCount();
                }

                continue;
            }

            totalAward = totalAward + itemRealAward;
            //是否部分发奖
            boolean poolPartialAward = itemRealAward > 0 && itemRealAward < award;
            if (poolPartialAward) {
                log.info("award-log-tag|总奖池已用完，部分发放，actId:{},memberId:{},award:{},itemRealAward:{}", attr.getActId(), member, award, itemRealAward);
            }

            //发奖记录展示
            AwardRecordInfo recordInfo = new AwardRecordInfo();
            recordInfo.setDate(System.currentTimeMillis());
            recordInfo.setTaskType(complete.getTaskType());
            recordInfo.setTaskLevel(complete.getLevel());
            recordInfo.setAwardType(1);
            recordInfo.setAwardName(attr.getAwardName());
            recordInfo.setAward(itemRealAward);

            recordInfo.setAwardMode(poolPartialAward ? 3 : (dayLimitPartialAward ? 2 : 1));
            String recordSeq = makeKey(attr, "seq:record:" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
            actRedisDao.lPushWithSeq(group, recordSeq, buildAwardListViewKey(attr, member), JSON.toJSONString(recordInfo), attr.getSeqExpireSeconds());

            //待发放记录
            String needReleaseKey = buildAwardRecordSetViewKey(taskTime, attr);
            actRedisDao.hset(group, needReleaseKey, member + "," + complete.getTaskType() + "," + complete.getLevel(), itemRealAward + "");

            //奖励发放
            releaseAward(attr, complete, taskTime, member, itemRealAward);
            log.info("award-log-tag|award ok,actId:{},taskTime:{},taskType:{},member:{},itemRealAward:{}", attr.getActId(), taskTime, complete.getTaskType(), member, itemRealAward);

            //发单播
            sendAwardUnicast(attr, complete, 1, itemRealAward, Convert.toLong(member));
        }

        return totalAward;
    }

    /**
     * 奖励发放
     */
    private void releaseAward(SunshineTaskAutoAwardComponentAttr attr, ActTaskScore score, String taskTime, String member, long amount) {
        Map<Long, Map<Long, Long>> busiTaskIdPackageId = attr.getBusiTaskIdPackageId();
        for (Long busiId : busiTaskIdPackageId.keySet()) {
            for (Long taskId : busiTaskIdPackageId.get(busiId).keySet()) {
                long packageId = busiTaskIdPackageId.get(busiId).get(taskId);
                String seq = attr.getActId() + "_" + attr.getCmptId() + attr.getCmptUseInx() + "_" + taskTime + "_" + member + "_" + score.getTaskType() + "_" + score.getLevel();
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), busiId, Convert.toLong(member), taskId, Convert.toInt(amount), packageId, seq, Maps.newHashMap());
            }
        }
    }

    /**
     * 奖励发放
     */
    private void releaseDefaultAward(SunshineTaskAutoAwardComponentAttr attr, ActTaskScore score, String taskTime, String member) {
        Map<Long, Map<Long, Long>> busiTaskIdPackageId = attr.getDefaultTaskPackageId();
        for (Long busiId : busiTaskIdPackageId.keySet()) {
            for (Long taskId : busiTaskIdPackageId.get(busiId).keySet()) {
                long packageId = busiTaskIdPackageId.get(busiId).get(taskId);
                String seq = attr.getActId() + "_" + attr.getCmptId() + attr.getCmptUseInx() + "_" + taskTime + "_" + member + "_" + score.getTaskType() + "_" + score.getLevel();
                hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), busiId, Convert.toLong(member), taskId, Convert.toInt(attr.getDefaultCount()), packageId, seq, Maps.newHashMap());
            }
        }
    }

    private void sendAwardUnicast(SunshineTaskAutoAwardComponentAttr attr, ActTaskScore score, int awardType, long count, long uid) {
        //任务名字和奖品的展示处理
        GameecologyActivity.Act202008_AnchorTips.Builder anchorTips = GameecologyActivity.Act202008_AnchorTips.newBuilder()
                .setType(2).setActId(attr.getActId());
        anchorTips.addComplete(score.getLevel().intValue());

        String nickName = commonService.getNickName(uid, false);
        String awardName = awardType == 1 ? attr.getAwardName() : attr.getDefaultAwardName();
        String awardIcon = awardType == 1 ? attr.getAwardIcon() : attr.getDefaultAwardIcon();
        String taskAward = awardName + "*" + count;

        JSONObject award = new JSONObject(3);
        award.put("awardName", taskAward);
        award.put("awardIcon", awardIcon);
        award.put("awardCount", count);

        JSONObject extJson = new JSONObject(5);
        extJson.put("nickName", nickName);
        extJson.put("taskName", score.getRemark());
        extJson.put("taskAward", taskAward);
        extJson.put("additionAward", StringUtils.EMPTY);
        extJson.put("awardList", Collections.singletonList(award));

        anchorTips.setExtjson(extJson.toJSONString());

        GameecologyActivity.GameEcologyMsg anchorTipsMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202008_AnchorTips_VALUE)
                .setAct202008AnchorTips(anchorTips).build();
        threadPoolManager.get(Const.GENERAL_POOL).submit(() -> {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.error("delay send unicast interrupted:", e);
            }

            svcSDKService.unicastUid(uid, anchorTipsMsg);

        });
        log.info("sendAwardUnicast unicast done@uid:{},completes:{}", uid, score.getLevel());
    }

    /**
     * 专题页查询任务信息
     */
    public Response<Map<String, Object>> getTaskRecord(long uid, long actId, long componentUseIndex) {
        SunshineTaskAutoAwardComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        String group = getRedisGroupCode(actId);
        Map<String, Object> result = Maps.newHashMap();

        //当前奖池剩余数额
        result.put("highMissionAwardLeft", getPoolLeft(actId, componentUseIndex));

        //奖励信息列表
        List<AwardRecordInfo> awardRecordInfos = Lists.newArrayList();
        List<String> awardRecord = actRedisDao.lrange(group, buildAwardListViewKey(attr, uid + ""), 0, 1000);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            for (String record : awardRecord) {
                AwardRecordInfo info = JSON.parseObject(record, AwardRecordInfo.class);
                awardRecordInfos.add(info);
            }
        }
        result.put("awardinfo", awardRecordInfos);

        //总奖励
        result.put("totalAward", awardRecordInfos.stream().mapToLong(AwardRecordInfo::getAward).sum());

        //查询自己的突破任务数值
        ActTaskScore score = queryMemberSingleTaskConfig(attr, String.valueOf(uid));
        if (score != null) {
            result.put("breakthrough", score.getScore());
            long award = calCurTaskIncAward(Collections.emptyList(), score);
            result.put("breakthroughAward", award);
            result.put("breakthroughAwardIcon", attr.getAwardIcon());
            result.put("breakthroughAwardName", attr.getAwardName());
        }

        return Response.success(result);
    }

    /**
     * 奖池剩余数量
     */
    public long getPoolLeft(long actId, long index) {
        SunshineTaskAutoAwardComponentAttr attr = getComponentAttr(actId, index);
        String group = getRedisGroupCode(attr.getActId());
        long award = Convert.toLong(actRedisDao.get(group, buildAwardReleaseKey(attr)), 0);
        return attr.getAwardPoolConfig() - award;
    }

    /**
     * 返回上一级任务分值
     */
    private long calOldAwardScore(List<ActTaskScore> memberTaskConfig, ActTaskScore complete) {
        for (int i = 0; i < memberTaskConfig.size(); i++) {
            ActTaskScore config = memberTaskConfig.get(i);
            if (config.getLevel().equals(complete.getLevel()) && config.getTaskType().equals(complete.getTaskType())) {
                return i == 0 ? 0 : memberTaskConfig.get(i - 1).getScore();
            }
        }
        return 0;
    }

    /**
     * 根据返奖比例算出突破奖励金额
     */
    private long calSingleAwardByScore(SunshineTaskAutoAwardComponentAttr attr, long score) {
        BigDecimal rate = new BigDecimal(attr.getAwardRate());
        long award = rate.multiply(new BigDecimal(score)).longValue();
        if (attr.getSingleAwardLimit() > 0) {
            return Math.min(award, attr.getSingleAwardLimit());
        }

        return award;
    }

    /**
     * 算出本次完成任务的增量奖励
     */
    private long calCurTaskIncAward(List<ActTaskScore> memberTaskConfig, ActTaskScore completeTask) {
        ActTaskScore preTask = null;
        for (int i = 0; i < memberTaskConfig.size(); i++) {
            ActTaskScore config = memberTaskConfig.get(i);
            if (config.getLevel().equals(completeTask.getLevel()) && config.getTaskType().equals(completeTask.getTaskType()) && i > 0) {
                preTask = memberTaskConfig.get(i - 1);
            }
        }

        long incAward = Convert.toLong(completeTask.getAward()) - Convert.toLong(preTask == null ? 0 : preTask.getAward());
        //避免出现一些变态配置，更高级的，奖励反而更少
        return Math.max(0, incAward);
    }


    private List<ActTaskScore> calculateAnchorTask(List<ActTaskScore> memberTaskConfig, long total, long score) {
        List<ActTaskScore> completedList = new ArrayList<>();

        long src = total - score;
        for (ActTaskScore actTaskScore : memberTaskConfig) {
            Long target = actTaskScore.getScore();
            //完成任务
            if (src < target && total >= target) {
                actTaskScore.setCompleted(1L);
                actTaskScore.setcTime(new Date());
                completedList.add(actTaskScore);
            }
        }
        return completedList;

    }


    /**
     * 当前阶段的总任务数
     */
    private PairBean getCurLevelConfig(long score, List<TaskItem> taskConfig) {
        long curMinScore = 0;
        long curMaxScore = 0;
        for (int i = 0; i < taskConfig.size(); i++) {
            TaskItem item = taskConfig.get(i);
            long curGapScore = item.getPassValue();
            curMaxScore = curMinScore + curGapScore;
            if (score >= curMinScore && score < curMaxScore) {
                return new PairBean(i + 1, curMaxScore);
            }

            curMinScore = curMinScore + curGapScore;
        }

        return new PairBean(1, curMaxScore);
    }

    private List<TaskItem> convertTaskConfigView(SunshineTaskAutoAwardComponentAttr attr, List<ActTaskScore> taskScores) {
        List<TaskItem> result = Lists.newLinkedList();
        for (int i = 0; i < taskScores.size(); i++) {
            ActTaskScore taskScore = taskScores.get(i);
            TaskItem item = new TaskItem();
            long passValue = i == 0 ? taskScore.getScore() : taskScore.getScore() - taskScores.get(i - 1).getScore();
            item.setPassValue(passValue);
            String taskName = SINGLE.equals(taskScore.getTaskType()) ? SINGLE_NAME : taskScore.getRemark();
            item.setName(taskName);
            item.setRemark(taskName + taskScore.getLevel());
            JSONObject ext = null;
            if (StringUtil.isNotBlank(taskScore.getExtJson())) {
                ext = JSON.parseObject(taskScore.getExtJson());
            }
            if (ext == null) {
                ext = new JSONObject();
            }
            //这里是非分段
            String desc = String.format(attr.getAwardDescTips(), taskScore.getScore(), taskName);
            ext.put("desc", desc);
            ext.put("award", taskScore.getAward());
            item.setExtjson(JSON.toJSONString(ext));
            result.add(item);
        }
        return result;
    }

    /**
     * 查询任务榜分数
     */
    public long queryAnchorTaskScore(String taskTime, long actId, long uid, long index) {
        SunshineTaskAutoAwardComponentAttr attr = getComponentAttr(actId, index);
        return queryAnchorTaskScore(attr, taskTime, uid);
    }

    public long queryAnchorTaskScore(SunshineTaskAutoAwardComponentAttr attr, String taskTime, long uid) {
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), taskTime,
                uid + "", Maps.newHashMap());
        return rank == null ? 0 : rank.getScore();
    }


    /**
     * 触发过任务常规高光、霸屏等横幅
     */
    private void sendBanner(SunshineTaskAutoAwardComponentAttr attr, RankingScoreChanged event, long uid, ActTaskScore taskScore) {
        String taskType = taskScore.getTaskType();
        if (!attr.getTaskBanner().containsKey(taskType)) {
            return;
        }
        if (!attr.getTaskBanner().get(taskType).containsKey(taskScore.getLevel())) {
            return;
        }
        List<BroadcastConfig> configs = attr.getTaskBanner().get(taskType).get(taskScore.getLevel());
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        for (BroadcastConfig config : configs) {
            log.info("invokeBro begin,actId:{},uid:{},taskType:{},level:{},config:{}", attr.getActId(), uid, taskType, taskScore.getLevel(), JSON.toJSONString(config));
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    invokeBro(attr, event, uid, taskScore, config);
                }
            });
        }
    }

    private void invokeBro(SunshineTaskAutoAwardComponentAttr attr, RankingScoreChanged event, long uid, ActTaskScore taskScore, BroadcastConfig config) {
        String taskType = taskScore.getTaskType();
        //延迟广播
        if (config.getDelayMillSeconds() > 0) {
            log.info("invokeBro delay,actId:{},uid:{},taskType:{},level:{},config:{}", attr.getActId(), uid, taskType, taskScore.getLevel(), JSON.toJSONString(config));
            SysEvHelper.waiting(config.getDelayMillSeconds());
        }

        int broType = config.getBroType();
        String bannerUrl;
        //优先用任务配置,其次用默认
        if (StringUtil.isNotBlank(config.getBannerUrl())) {
            bannerUrl = config.getBannerUrl();
        } else {
            bannerUrl = attr.getDefaultBannerUrl();
        }
        UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        Long score = taskScore.getScore();

        String busiId = BusiId.MAKE_FRIEND.getValue() + "";
        String asId = "";
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, RoleType.ANCHOR.getValue(), uid + "");
        if (enrollmentInfo != null) {
            busiId = enrollmentInfo.getRoleBusiId() + "";
            asId = enrollmentInfo.getSignAsid() + "";
        } else {
            log.error("bro can not get enrollmentInfo,actId:{},memberId:{}", actId, uid + "");
        }

        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("nickName", nick);
        extMap.put("logo", StringUtil.isNotBlank(userInfo.getHdLogo()) ? userInfo.getHdLogo() : userInfo.getLogo());
        extMap.put("value", score + "");
        extMap.put("svgaUrl", bannerUrl);
        extMap.put("award", taskScore.getAward());
        extMap.put("asid", asId);
        extMap.put("ext", config.getExt());
        extMap.put("busiId", busiId);

        int bannerId = config.getBannerId();
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(bannerId).setUserNick(nick)
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();


        if (broType == BroadcastConfig.BroType.ACT_BUSI.code) {
            broadCastHelpService.broadcast(actId, BusiId.GAME_ECOLOGY, broType, 0L, 0L, bannerBroMsg);
        } else {
            UserCurrentChannel channel = commonService.getUserCurrentChannel(uid);
            if (channel == null) {
                log.info("log SunshineTaskComponent bro done ,user not in channel uid:{} task:{}", uid, JSON.toJSONString(taskScore));
                return;
            }
            broadCastHelpService.broadcast(actId, BusiId.findByValue(Convert.toInt(event.getBusiId())), broType, channel.getTopsid(), channel.getSubsid(), bannerBroMsg);
        }
        log.info("log SunshineTaskComponent bro done uid:{} task:{}", uid, JSON.toJSONString(taskScore));

        bigDataService.saveNoRankDataToFile(actId, BusiId.GAME_ECOLOGY, System.currentTimeMillis(), uid + ""
                , RoleType.ANCHOR, score, BigDataScoreType.SUNSHINE_TASK_BANNER, config.getDesc() + "|" + config.getBannerId() + "|" + config.getBroType());
    }

    public void initSingleTaskData(long opUid, long actId, long index) {
        SunshineTaskAutoAwardComponentAttr attr = getComponentAttr(actId, index);
        String url = attr.getInitSingleTaskUrl();
        if (StringUtil.isEmpty(url)) {
            log.info("url not config,actId:{}", actId);
            return;
        }
        if (!attr.getInitSingleTaskAuthUid().contains(opUid)) {
            throw new RuntimeException("初始化突破任务权限不足,opUid:" + opUid);
        }
        List<String> contents = ReadFileUtil.readFromFile(url, "utf8");
        Assert.notNull(contents);
        //转成long,用作格式校验
        Map<Long, Long> uidScores = Maps.newHashMap();
        for (String content : contents) {
            String[] array = content.split(",");
            uidScores.put(Convert.toLong(array[0]), Convert.toLong(array[1]));
        }
        String key = buildSingleTaskKey(attr);
        actRedisDao.getRedisTemplate(getRedisGroupCode(actId)).executePipelined((RedisConnection connection) -> {
            for (Long uid : uidScores.keySet()) {
                connection.hSet(key.getBytes(), Convert.toString(uid).getBytes(), Convert.toString(uidScores.get(uid)).getBytes());
            }
            return null;
        });
        log.info("initSingleTaskData ok,actId:{},index:{},key:{},size:{}", actId, index, key, uidScores.keySet().size());
    }


    /**
     * 个人突破任务配置分值key
     */
    private String buildSingleTaskKey(SunshineTaskAutoAwardComponentAttr attr) {
        return makeKey(attr, MEMBER_SINGLE_TASK);
    }

    private String buildMemberDayRelease(SunshineTaskAutoAwardComponentAttr attr, String taskTime, String member) {
        return makeKey(attr, String.format(DAY_AWARD_RELEASE_AMOUNT, taskTime, member));
    }

    /**
     * 奖池已发放奖励数量
     */
    private String buildAwardReleaseKey(SunshineTaskAutoAwardComponentAttr attr) {
        return makeKey(attr, AWARD_RELEASE_AMOUNT);
    }

    /**
     * 发奖记录展示
     */
    private String buildAwardListViewKey(SunshineTaskAutoAwardComponentAttr attr, String member) {
        return makeKey(attr, String.format(AWARD_RECORD_LIST_VIEW, member));
    }

    /**
     * 待发奖记录
     */
    private String buildAwardRecordSetViewKey(String taskTime, SunshineTaskAutoAwardComponentAttr attr) {
        return makeKey(attr, String.format(AWARD_RECORD_SET_VIEW, taskTime));
    }


}
