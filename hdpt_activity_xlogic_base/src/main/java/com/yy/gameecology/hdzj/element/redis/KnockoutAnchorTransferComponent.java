package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.CulClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.consts.RoleTypeSource;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.KnockoutAnchorTransferComponentAttr;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import com.yy.thrift.hdztranking.UpdateRankingResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022.10.12 20:48
 * 转移被淘汰的主播至分赛场,记录榜单改变事件至list,直到结算完成,然后重放这些事件
 * 1.主赛场和分赛场的榜单角色配置不可相同
 * 2.
 */
@UseRedisStore
@Component
public class KnockoutAnchorTransferComponent extends BaseActComponent<KnockoutAnchorTransferComponentAttr> {

    @Autowired
    private BroActLayerService broActLayerService;

    @Autowired
    private CulClient culClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    public static final String RANK_PHASE_PROMOT_KEY = "hdzt_ranking:%s:%s:_:_:_|%s.promot";

    public static final String HDZT_RANK_KEY = "hdzt_ranking:%s:%s:_:_:_|%s";

    //所有在主赛场的主播,包含前面阶段未参与的主播,每轮淘汰的主播都会修改其角色ID,所以数量是有限的
    public static final String ALL_ANCHOR = "all_anchor";

    public static final String CURRENT_PROMOT_ANCHOR = "current_promot_anchor";

    //记录榜单改变事件
    public static final String KNOCKOUT_ANCHOR_TRANSFER_LUA = "knockout_anchor_transfer.lua";

    //阶段结算收集的rankId,用于判断是否所有相关的榜单都已结算完成 phase_promot_rank_set:hdzkRankId:phaseId
    public static final String PHASE_PROMOT_RANK_COUNT = "phase_promot_rank_count:%s:%s";

    //角色修改记录,活动中期,新主播需要先修改角色,在上报数据,否则中台会修改其角色,导致未在分赛场累分
    public static final String ROLE_CHANGE_RECORD = "role_change_record";

    @Override
    public Long getComponentId() {
        return ComponentId.KNOCKOUT_ANCHOR_TRANSFER;
    }

    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = false)
    public void onPromotTimeEndEvent(PromotTimeEnd event, KnockoutAnchorTransferComponentAttr attr) {

        long phaseId = event.getPhaseId();
        Map<Long, List<Long>> phaseContainRankId = attr.getPhaseContainRankId();
        List<Long> rankIds = phaseContainRankId.get(phaseId);

        if (CollectionUtils.isEmpty(rankIds) || !rankIds.contains(event.getRankId())) {
            log.info("onPromotTimeEndEvent rankId not match, rankIds:{}, event rankId:{}", rankIds, event.getRankId());
            return;
        }

        long actId = attr.getActId();
        String groupCode = getRedisGroupCode(actId);
        String promotRankCountKey = makeKey(attr, String.format(PHASE_PROMOT_RANK_COUNT, attr.getHdzkRankId(), event.getPhaseId()));
        long rankSize = actRedisDao.incrValue(groupCode, promotRankCountKey, 1);
        String seq = event.getSeq();
        String msg = String.format(
                        "当前榜单数:%s\n" +
                        "要求的榜单数:%s\n" +
                        "当前榜单ID:%s\n" +
                        "seq:%s"
                , rankSize, rankIds.size(), event.getRankId(), seq);
        msg = buildActRuliuMsg(actId, false, "黑马赛场切换开始：1", msg);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
        //如果需要可重试,如何处理????????
        if (rankSize != rankIds.size()) {
            log.info("waiting for other rank promot, current promot:{}", JSON.toJSONString(event));
            return;
        }

        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), commonService.getNow(actId));

        List<Rank> ranks = Lists.newArrayList();
        Map<String, String> ext = Maps.newHashMap();
        ext.put(RankExtParaKey.RANK_TYPE_PROMOT, HdztRankType.PROMOT);
        for (Long rankId : rankIds) {
            ranks.addAll(hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, 1000, ext));
        }

        List<String> newPromotAnchor = ranks.stream().map(Rank::getMember).collect(Collectors.toList());
        String newPromotMembers = StringUtils.join(newPromotAnchor, ",");

        String allAnchorKey = makeKey(attr, ALL_ANCHOR);
        String currentPromotAnchorKey = makeKey(attr, CURRENT_PROMOT_ANCHOR);

        String promotKey = makeKey(attr, String.format(RANK_PHASE_PROMOT_KEY, actId, attr.getHdzkRankId(), phaseId));
        String rankKey = makeKey(attr, String.format(HDZT_RANK_KEY, actId, attr.getHdzkRankId(), phaseId));
        List<String> keys = Lists.newArrayList(promotKey, allAnchorKey, rankKey, currentPromotAnchorKey);

        List<String> values = Lists.newArrayList("1", "", "", newPromotMembers);
        long result = actRedisDao.executeLua(groupCode, KNOCKOUT_ANCHOR_TRANSFER_LUA, Long.class, keys, values);
        String msg2 = String.format("替换新晋级主播结果:%s\n" +
                " 1-> 成功, 其他->失败(需人工介入处理)\n" +
                "seq:%s", result, seq);
        msg2 = buildActRuliuMsg(actId, false, "黑马赛场切换中：保存组件数据完成", msg2);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg2, Lists.newArrayList());

        if (result == 1) {
            log.info("KnockoutAnchorTransfer onPromotTimeEndEvent done, seq:{}, newPromotMembers:{}", seq, newPromotMembers);
        } else {
            log.error("KnockoutAnchorTransfer .onPromotTimeEndEvent error, seq:{}, newPromotMembers:{}", seq, newPromotMembers);
            return;
        }

        Set<String> allAnchors = actRedisDao.sMembers(groupCode, allAnchorKey);

        log.info("all anchors in cur phase:{}", allAnchors);
        //修改角色
        newPromotAnchor.forEach(allAnchors::remove);
        List<String> members = new ArrayList<>(allAnchors);
        knockoutRoleChange(attr, actId, seq, members);
        //重放事件
        reloadRankScoreChange(attr, groupCode, phaseId, members);
        String msg3 = String.format("淘汰主播赛场切换完成\n result:%s\n seq:%s", result, seq);
        msg3 = buildActRuliuMsg(actId, false, "黑马赛场切换成功", msg3);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg3, Lists.newArrayList());

    }

    private void reloadRankScoreChange(KnockoutAnchorTransferComponentAttr attr, String groupCode, long phaseId, List<String> members) {
        long actId = attr.getActId();
        long hdzkRankId = attr.getHdzkRankId();
        String rankKey = makeKey(attr, String.format(HDZT_RANK_KEY, actId, hdzkRankId, phaseId));

        // 记录队列当前长度, 若为0直接结束循环
        long len = actRedisDao.llen(groupCode, rankKey);
        if (len == 0) {
            return;
        }
        long total = len;
        String content = null;
        while ((content = actRedisDao.lpop(groupCode, rankKey)) != null) {
            RankingScoreChanged event = JSON.parseObject(content, RankingScoreChanged.class);
            //只需要重播未晋级的
            if (members.contains(event.getMember())) {
                onRankingScoreChangeEvent(event, attr);
            }

            if (--len < 0) {
                log.warn("reloadRankScoreChange break@key:{}, total:{}, len:{}", rankKey, total, len);
                break;
            }
        }
    }

    private void knockoutRoleChange(KnockoutAnchorTransferComponentAttr attr, long actId, String eventSeq, List<String> members) {
        long roleType = attr.getRoleType();
        List<EnrollmentInfo> enrollmentInfos = hdztRankingThriftClient.queryEnrollmentInfoNocache(actId, roleType, members);
        String roleChangeKey = makeKey(attr, ROLE_CHANGE_RECORD);
        Map<Long, Long> roleChangeMap = attr.getRoleTransferMap();

        List<String> hasChanged = new ArrayList<>();
        log.info("knockoutRoleChange start eventSeq:{} members:{}", eventSeq, members);
        for (EnrollmentInfo enrollmentInfo : enrollmentInfos) {
            String seq = UUID.randomUUID().toString();
            long srcRole = enrollmentInfo.getDestRoleId();
            Long newRole = roleChangeMap.get(srcRole);
            if (roleChangeMap.containsValue(srcRole)) {
                log.warn("knockoutRoleChange srcRole has been change, member:{}, srcRole:{}", enrollmentInfo.getMemberId(), srcRole);
                continue;
            }
            if (newRole == null) {
                log.error("knockoutRoleChange newRole is null,please check, member:{}, srcRole:{}", enrollmentInfo.getMemberId(), srcRole);
                continue;
            }
            EnrollmentInfo newEnrollmentInfo = new EnrollmentInfo(enrollmentInfo);
            newEnrollmentInfo.setSrcRoleId(newRole);
            newEnrollmentInfo.setDestRoleId(newRole);
            String remark = String.format("淘汰角色调整,seq:%s,oldRoleId:%s,cmptId:%s,ip:%s,%s", eventSeq, srcRole, getComponentId(), SystemUtil.getIp(), DateUtil.today());

            int updateCount = 0;
            int ret;
            do {
                updateCount++;
                ret = hdztRankingThriftClient.saveEnrollment(actId, newEnrollmentInfo, true, remark, seq);
                if (ret == 0) {
                    hasChanged.add(enrollmentInfo.getMemberId());
                    break;
                }
                SysEvHelper.waiting(1000);
            } while (updateCount < 2);
            String memberId = enrollmentInfo.getMemberId();
            if (ret == 0) {
                log.info("knockoutRoleChange role change info,seq:{} actId:{}, member:{} oldRoleId:{},newRoleId:{},updateCount:{}", eventSeq, actId, memberId, srcRole, newRole, updateCount);
            } else {
                log.error("knockoutRoleChange role change error,seq:{} actId:{}, member:{} oldRoleId:{},newRoleId:{},updateCount:{}", eventSeq, actId, memberId, srcRole, newRole, updateCount);
            }
        }
        if (!hasChanged.isEmpty()) {
            actRedisDao.sRem(getRedisGroupCode(actId), makeKey(attr, ALL_ANCHOR), hasChanged.toArray(new String[0]));
            actRedisDao.sBatchAdd(getRedisGroupCode(actId), roleChangeKey, hasChanged.toArray(new String[0]));
            log.info("knockoutRoleChange anchors has changed success:{}", hasChanged);
        }
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChangeEvent(RankingScoreChanged event, KnockoutAnchorTransferComponentAttr attr) {
        //监听辅助榜分值改变事件
        long rankId = event.getRankId();

        if (!attr.getScoreRankId().contains(rankId)) {
            return;
        }
        long actId = attr.getActId();
        long phaseId = event.getPhaseId();
        String groupCode = getRedisGroupCode(actId);
        String allInPhaseAnchorKey = makeKey(attr, ALL_ANCHOR);
        //若在第一阶段,只需记录所有主播UID,用于结算时修改角色
        if (attr.getStartSavePhaseId() > phaseId) {
            actRedisDao.sadd(groupCode, allInPhaseAnchorKey, event.getMember());
            return;
        }

        long hdzkRankId = attr.getHdzkRankId();
        String promotKey = makeKey(attr, String.format(RANK_PHASE_PROMOT_KEY, actId, hdzkRankId, phaseId));

        String currentPromotAnchorKey = makeKey(attr, CURRENT_PROMOT_ANCHOR);

        String rankKey = makeKey(attr, String.format(HDZT_RANK_KEY, actId, hdzkRankId, phaseId));
        List<String> keys = Lists.newArrayList(promotKey, allInPhaseAnchorKey, rankKey, currentPromotAnchorKey);

        String eventJson = JSON.toJSONString(event);
        List<String> values = Lists.newArrayList("0", event.getMember(), eventJson, "");

        //1-> 晋级结算完成 2-> 不在晋级名单需上报数据 3->在晋级名单无需处理 4->记录事件完成
        long result = actRedisDao.executeLua(groupCode, KNOCKOUT_ANCHOR_TRANSFER_LUA, Long.class, keys, values);
        log.info("KNOCKOUT_ANCHOR_TRANSFER_LUA result:{}, event:{}", result, eventJson);

        //需上报
        final int two = 2;
        if (result == two) {
            Map<Long, Long> roleTransferMap = attr.getRoleTransferMap();
            Map<Long, String> actors = event.getActors();
            Map<Long, String> newActors = Maps.newHashMap();
            boolean roleChange = false;
            //先修改角色,再上报数据
            String roleChangeRecordKey = makeKey(attr, ROLE_CHANGE_RECORD);
            if (!actRedisDao.sIsMember(groupCode, roleChangeRecordKey, event.getMember())) {
                //等待2秒,等待原角色插入数据后才能更新角色,避免被覆盖
                SysEvHelper.waiting(2000);
                knockoutRoleChange(attr, actId, event.getSeq(), Lists.newArrayList(event.getMember()));
                roleChange = true;
            }
            Map<String, Long> roleScore = Maps.newHashMap();
            for (Map.Entry<Long, String> entry : actors.entrySet()) {
                Long newRole = roleTransferMap.get(entry.getKey());
                if (newRole != null) {
                    newActors.put(newRole, entry.getValue());
                } else {
                    newActors.put(entry.getKey(), entry.getValue());
                    // 注释下面的设置为0：防止对应的贡献榜累不到，要做到其他榜单防止重复加分限制：不要配置 zkGiftId，不配置zkGiftId就不会累到榜
                    //其他角色分值设为0,预防其他榜单重复加分
                    //roleScore.put(String.valueOf(entry.getKey()), 0L);
                }
            }

            updateRankData(actId, attr.getBusiId(), attr.getZkGiftId(), event, newActors, roleScore);
            //如果改变了角色,上报玩数据后主动更新挂件
            if (roleChange) {
                long sid = 0, ssid = 0;
                if (attr.getRoleType() == RoleTypeSource.BABY) {
                    ChannelInfoVo channelInfoVo = culClient.queryUserChannel(Convert.toLong(event.getMember()));
                    if (channelInfoVo != null) {
                        sid = channelInfoVo.getSid();
                        ssid = channelInfoVo.getSsid();
                    }
                } else if (attr.getRoleType() == RoleTypeSource.SUB_GUILD) {
                    if (StringUtils.contains(event.getMember(), StringUtil.UNDERSCORE)) {
                        String[] arr = event.getMember().split(StringUtil.UNDERSCORE);
                        sid = Long.parseLong(arr[0]);
                        ssid = Long.parseLong(arr[1]);
                    }
                } else if (attr.getRoleType() == RoleTypeSource.GUILD) {
                    sid = Long.parseLong(event.getMember());
                }

                if (sid != 0) {
                    broActLayerService.invokeRefresh(event.getActId(), sid, ssid);
                }
            }
            log.info("KnockoutAnchorTransfer onRankingScoreChangeEvent done, seq:{},result:{} event:{}", event.getSeq(), result, event);

        }
    }


    private void updateRankData(long actId, int busiId, String itemId, RankingScoreChanged event, Map<Long, String> actors, Map<String, Long> roleScore) {

        UpdateRankingRequest rankDataEvent = new UpdateRankingRequest();
        rankDataEvent.setBusiId(busiId);
        rankDataEvent.setActId(actId);
        rankDataEvent.setSeq("fsc-" + event.getSeq());
        rankDataEvent.setActors(actors);
        rankDataEvent.setItemId(itemId);
        rankDataEvent.setCount(event.getItemCount());
        rankDataEvent.setScore(event.getItemScore());
        rankDataEvent.setRoleScores(roleScore);
        rankDataEvent.setTimestamp(System.currentTimeMillis());
        UpdateRankingResult result = null;
        try {
            result = hdztRankingThriftClient.getProxy().updateRanking(rankDataEvent);
            log.info("updateRankData done: result:{}, request:{}", result.toString(), rankDataEvent.toString());
        } catch (TException e) {
            log.error("updateRankData error: result:{}, request:{}", result, rankDataEvent.toString(), e);
        }
    }
}
