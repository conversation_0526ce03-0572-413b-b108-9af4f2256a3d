package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2021/7/2
 */
@Getter
@Setter
public class PKRewardComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;
    @ComponentAttrField(labelText = "Lua脚本")
    private String addRewardLua = "pk_reward_add.lua";
    @ComponentAttrField(labelText = "结算key前缀")
    private String settleFenceKeySuffix = "pksf";
    @ComponentAttrField(labelText = "奖励key")
    private String rewardKey = "reward";
    //荣耀值转换赏金比例
    @ComponentAttrField(labelText = "荣耀值转换赏金比例")
    private long rewardRatio = 10000000;
    @ComponentAttrField(labelText = "单位奖励")
    private long unitReward = 1000;
    @ComponentAttrField(labelText = "奖励限制")
    private long rewardLimit = 5000;
    @ComponentAttrField(labelText = "rewardRobRatio")
    private int rewardRobRatio = 2;
}
