package com.yy.gameecology.activity.config.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.Serializable;


@Configuration
public class RedisConfig3 extends BaseRedisConfig {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /* ##############################  gameecology redis  ############################# */

    public RedisConfig3(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        super(builderCustomizers);
    }

    @Bean
    @ConfigurationProperties("gameecology.group3.redis")
    public RedisProperties redisGroup3Properties() {
        return new RedisProperties();
    }

    @Bean
    public RedisConnectionFactory redisGroup3ConnectionFactory(RedisProperties redisGroup3Properties) {
        return new LettuceConnectionFactory(getRedisConfig(redisGroup3Properties), getClientConfig(redisGroup3Properties));
    }

    @Bean
    public StringRedisTemplate stringRedisGroup3Template(RedisConnectionFactory redisGroup3ConnectionFactory) {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(redisGroup3ConnectionFactory);
        return stringRedisTemplate;
    }

    @Bean
    public RedisTemplate<Serializable, Serializable> redisGroup3Template(RedisConnectionFactory redisGroup3ConnectionFactory) {
        RedisTemplate<Serializable, Serializable> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisGroup3ConnectionFactory);
        return redisTemplate;
    }

    @Bean
    public RedissonClient redissonClient3(RedisProperties redisGroup3Properties) {
        Config config = getRedissonConfig(redisGroup3Properties);
        config.setCheckLockSyncedSlaves(false);
        log.info("Creating RedissonClient for group3 with config: host={}, port={}, database={}",
                redisGroup3Properties.getHost(),
                redisGroup3Properties.getPort(),
                redisGroup3Properties.getDatabase());
        return Redisson.create(config);
    }

}
