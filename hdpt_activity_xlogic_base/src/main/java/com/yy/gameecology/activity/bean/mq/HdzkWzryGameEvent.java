package com.yy.gameecology.activity.bean.mq;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-24 17:50
 **/
@Data
public class HdzkWzryGameEvent {
    private String seq;
    private Long actId;
    private Long gameId;
    private String gameCode;
    private Integer battleMode;
    /**
     * 900 完成游戏，正常结束
     */
    private Integer gameState;

    /**
     * key--用户uid  value--用户所在战队
     */
    private Map<Long, Integer> user = Maps.newHashMap();

    /**
     * 胜利战队
     */
    private Integer winner;

    /**
     * 赛事开始时间，毫秒，时间戳
     */
    private Long gameStartTime;

    /**
     * 事件时间，毫秒，当前时间戳
     */
    private Long eventTime;


}
