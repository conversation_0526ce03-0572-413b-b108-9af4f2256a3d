package com.yy.gameecology.activity;


import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.hdzj.element.redis.WatchwordStakeComponent;
import com.yy.gameecology.hdzj.element.component.attr.WatchwordStakeComponentAttr;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=4"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-4.properties"
})
public class WatchwordStakeComponentTests {

    static {
        System.setProperty("env", "local");
        System.setProperty("group", "4");
    }

    @Autowired
    private WatchwordStakeComponent watchwordStakeComponent;

    @Test
    public void testTriggerEnterTemplate() {
        WatchwordStakeComponentAttr attr =  watchwordStakeComponent.getComponentAttr(2023114001, 810);
        //uid=2926671561, sid=87814665, ssid=2813956279, busiId=0, template=0, ip=**************, seq=8e81ab9b-fe67-465c-9a24-1042a195f17e
        UserEnterTemplateEvent event = new UserEnterTemplateEvent();
        event.setTemplate(0);
        event.setBusiId(0);
        event.setUid(2926671561L);
        event.setSid(87814665);
        event.setSsid(2813956279L);
        event.setSeq("8e81ab9b-fe67-465c-9a24-1042a195f17e");

        watchwordStakeComponent.onUserEnterTemplate(event, attr);

    }
}
