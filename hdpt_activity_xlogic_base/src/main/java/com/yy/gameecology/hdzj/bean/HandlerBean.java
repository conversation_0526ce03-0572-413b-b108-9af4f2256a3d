package com.yy.gameecology.hdzj.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yy.gameecology.hdzj.element.ActComponent;

/**
 * 功能描述:处理器管理bean
 *
 * <AUTHOR>
 * @date 2021/4/1 15:08
 */
public class HandlerBean {
    private long actId;

    private long cmptUseInx;

    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    @org.codehaus.jackson.annotate.JsonIgnore
    private ActComponent actComponent = null;

    private String beanName;

    public HandlerBean() {
    }

    public HandlerBean(long actId, ActComponent actComponent, long cmptUseInx) {
        this.actId = actId;
        this.actComponent = actComponent;
        this.cmptUseInx = cmptUseInx;
        this.beanName = actComponent.getBeanName();
    }

    public long getActId() {
        return actId;
    }

    public void setActId(long actId) {
        this.actId = actId;
    }

    public ActComponent getActComponent() {
        return actComponent;
    }

    public void setActComponent(ActComponent actComponent) {
        this.actComponent = actComponent;
    }

    public long getCmptUseInx() {
        return cmptUseInx;
    }

    public void setCmptUseInx(long cmptUseInx) {
        this.cmptUseInx = cmptUseInx;
    }

    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }
}
