package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-31 21:44
 **/
@Data
public class PepcPhaseComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected long busiId;

    @ComponentAttrField(labelText = "上一活动ID", remark = "上一个活动ID")
    private long preActId;

    @ComponentAttrField(labelText = "下期活动开始时间", remark = "外显给用户看")
    private long nextActTime;

    @ComponentAttrField(labelText = "奖励图片素材")
    private String awards;

    @ComponentAttrField(labelText = "默认皮肤", remark = "默认皮肤设置")
    private String defaultSkin = "default";

    @ComponentAttrField(labelText = "皮肤配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = PepcPhaseComponentAttr.ActSkinConfig.class))
    private List<PepcPhaseComponentAttr.ActSkinConfig> actSkinConfigs = Lists.newArrayList();

    @ComponentAttrField(labelText = "分组时间", remark = "报名结束N分钟后进行分组，延迟一下，防止有报名结束时间临界问题")
    private Integer signupEndDelayGroupMin = 10;

    @ComponentAttrField(labelText = "开始队伍最小人数")
    private Integer teamMinMember = 4;

    @ComponentAttrField(labelText = "开赛最小队伍")
    private Integer startGameMinTeam = 20;

    @ComponentAttrField(labelText = "开赛最大队伍")
    private Integer startGameMaxTeam = 400;

    @ComponentAttrField(labelText = "分组的模")
    private Integer groupMode = 20;

    @ComponentAttrField(labelText = "腾讯赛事名称")
    private String gameName;


    @ComponentAttrField(labelText = "腾讯截止报名时间",remark = "距离比赛开始时间间隔（分钟）")
    private int signUpEndMin = 5;

    @ComponentAttrField(labelText = "腾讯游戏时间", remark = "创建腾讯赛事时，开始时间+此时间得出游戏结束时间")
    protected int gameTimeMin = 50;

    @ComponentAttrField(labelText = "赛程配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = PepcPhaseComponentAttr.PepcPhaseConfig.class))
    private List<PepcPhaseConfig> phaseConfig = Lists.newLinkedList();

    @ComponentAttrField(labelText = "赛事配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = PepcPhaseComponentAttr.PepcGameConfig.class))
    private List<PepcGameConfig> gameConfig = Lists.newLinkedList();

    @ComponentAttrField(labelText = "晋级加权分", remark = "晋级的时候，用来保存额外的淘汰分占的位数")
    private long scoreExtraDigits = 100000;

    @ComponentAttrField(labelText = "奖励配置", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = PepcPhaseComponentAttr.PepcAwardInfo.class))
    private List<PepcAwardInfo> awardInfos = Lists.newLinkedList();

    @ComponentAttrField(labelText = "小队排名晋级分",
            subFields = {@SubField(fieldName = Constant.KEY1, type = String.class, labelText = "小队排名"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "晋级分数")})
    private Map<Integer, Integer> teamRankScore = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "显示即将开始比赛时间偏移（分）", remark = "和比赛开始时间比较")
    private int showMyGameStateSoonToStartBegin = -30;

    @ComponentAttrField(labelText = "显示比赛进行中时间偏移（分）", remark = "和比赛开始时间比较")
    private int showMyGameStatePlayingBegin = -10;

    @ComponentAttrField(labelText = "显示比赛结算中时间偏移（分）", remark = "和比赛开始时间比较")
    private int showMyGameStateSettleBegin = 36;

    public Date getSignupStartTime() {
        Optional<PepcPhaseConfig> config = phaseConfig.stream().filter(p -> p.getPhaseId() == 0).findFirst();
        if (config.isEmpty()) {
            throw new RuntimeException("not config signup time");
        }
        return config.get().getStartTime();
    }


    public Date getSignupEndTime() {
        Optional<PepcPhaseConfig> config = phaseConfig.stream().filter(p -> p.getPhaseId() == 0).findFirst();
        if (config.isEmpty()) {
            throw new RuntimeException("not config signup time");
        }
        return config.get().getEndTime();
    }

    public int getFirstPhaseIdExcludeSignUp() {
        return phaseConfig.stream().filter(p -> p.getPhaseId() != 0).mapToInt(PepcPhaseConfig::getPhaseId).min().orElse(0);
    }

    public int getLastPhaseId() {
        return phaseConfig.stream().mapToInt(PepcPhaseConfig::getPhaseId).max().orElse(0);
    }


    @Getter
    @Setter
    public static class PepcPhaseConfig {

        @ComponentAttrField(labelText = "阶段id", remark = "0代表报名阶段，其他递增")
        protected Integer phaseId;

        @ComponentAttrField(labelText = "阶段名称")
        protected String phaseName;

        @ComponentAttrField(labelText = "总轮次")
        protected int totalRound;

        @ComponentAttrField(labelText = "开始时间", remark = "HH:mm:ss")
        protected Date startTime;

        @ComponentAttrField(labelText = "结束时间", remark = "HH:mm:ss")
        protected Date endTime;

        /**
         * 晋级规则
         * 类型1，代表按照比赛队伍数量，决定晋级队伍数，例如   1|{"0":10,"30":20} 代表，队伍>0队的时候晋级10队，对于>30队的时候晋级20队
         */
        @ComponentAttrField(labelText = "晋级规则", remark = "类型1，代表按照比赛队伍数量，决定晋级队伍数，例如   1|{\"0\":10,\"30\":20} 代表，队伍>0队的时候晋级10队，对于>30队的时候晋级20队")
        private String advanceRule;
    }

    @Getter
    @Setter
    public static class PepcGameConfig {

        @ComponentAttrField(labelText = "阶段id", remark = "0代表报名阶段，其他递增")
        protected Integer phaseId;

        @ComponentAttrField(labelText = "轮次序号")
        protected int roundIndex;

        @ComponentAttrField(labelText = "开始时间", remark = "yyyy-MM-dd HH:mm:ss")
        protected Date startTime;


        @ComponentAttrField(labelText = "地图", remark = "0-海岛，1-雨林，2-沙漠，3-雪地，4-山谷，5-度假岛")
        protected int moduleId;
    }

    @Data
    public static class PepcAwardInfo {
        @ComponentAttrField(labelText = "名次",remark = "-1 代表首胜奖励")
        private Integer rank;
        @ComponentAttrField(labelText = "平分人数")
        private Integer memberAmount;
        @ComponentAttrField(labelText = "奖励数量", remark = "点券")
        private Long awardAmount;
        @ComponentAttrField(labelText = "奖池id")
        private Long taskId;
        @ComponentAttrField(labelText = "奖包id")
        private Long packageId;
        @ComponentAttrField(labelText = "奖励说明")
        private String awardDesc;

    }

    @Data
    public static class ActSkinConfig {
        @ComponentAttrField(labelText = "开始时段", remark = "时间戳，毫秒")
        private long startTime;

        @ComponentAttrField(labelText = "结束时段", remark = "时间戳，毫秒")
        private long endTime;

        @ComponentAttrField(labelText = "皮肤编码")
        private String skinCode;

        @ComponentAttrField(labelText = "备注")
        private String remark;
    }

}
