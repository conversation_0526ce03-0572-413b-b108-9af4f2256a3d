
package com.yy.gameecology.activity.worker.web.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.RankInfo;
import com.yy.gameecology.activity.client.thrift.FtsGroupCenterThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanCommonListClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.worker.web.BaseController;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.element.component.*;
import com.yy.gameecology.hdzj.element.history.*;
import com.yy.gameecology.hdzj.element.redis.JySendGiftLotteryComponent;
import com.yy.gameecology.hdzj.element.redis.ShowLoveComponent;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.IntStream;

@RestController
@RequestMapping(value = "/testTool")
public class TestToolController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(TestToolController.class);

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private HdztRankService hdztRankService;

    @Autowired
    private ShopComponent shopComponent;

    @Autowired
    private ShowLoveComponent showLoveComponent;

    @Autowired
    private AwardUnicastComponent awardUnicastComponent;


    @Autowired
    private WarfareRegimentComponent warfareRegiment;

    @Autowired
    private HdztAwardService hdztAwardService;

    @Autowired
    private ZhuiWanCommonListClient zhuiWanCommonListClient;


    @Autowired
    private ActivityPlaneComponent activityPlaneComponent;

    @Autowired
    private ZhuiwanDatingExchangeComponent zhuiwanDatingExchangeComponent;

    @Autowired
    private MemberRoleChangeV1Component memberRoleChangeV1Component;

    @Autowired
    private SendGiftLotteryComponent sendGiftLotteryComponent;

    @Autowired
    private JySendGiftLotteryComponent jySendGiftLotteryComponent;

    @Autowired
    private ChannelArenaComponent channelArenaComponent;

    @Autowired
    private FtsGroupCenterThriftClient ftsGroupCenterThriftClient;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private RedisScanTool redisScanTool;


    /**
     * 抽奖宝箱触发测试
     */
    @GetMapping("channelArena/testBoxBro")
    public Response testBoxBro(HttpServletRequest req, long actId, long index, long sid, long uid, int level) throws Exception {
        checkEnvForGrey(req, actId);
        return Response.success(channelArenaComponent.testBoxBro(actId, index, uid, sid, level));
    }

    /**
     * 设置送礼顺序排名
     */
    @GetMapping("sendGiftLottery/setSendGiftRank")
    public Response setSendGiftRank(HttpServletRequest req, long actId, long index, int mustCount, String day) throws Exception {
        checkEnvForGrey(req, actId);
        return sendGiftLotteryComponent.setSendGiftRank(actId, index, mustCount, day);
    }


    /**
     * 获取送礼顺序排名
     */
    @GetMapping("sendGiftLottery/getSendGiftRank")
    public Response getSendGiftRank(HttpServletRequest req, long actId, long index, String day) throws Exception {
        checkEnvForGrey(req, actId);
        return sendGiftLotteryComponent.getSendGiftRank(actId, index, day);
    }

    /**
     * 设置送礼顺序排名
     */
    @GetMapping("sendGiftLottery/setJYSendGiftRank")
    public Response setJYSendGiftRank(HttpServletRequest req, long actId, long index, int mustCount, String day) throws Exception {
        checkEnvForGrey(req, actId);
        return jySendGiftLotteryComponent.setSendGiftRank(actId, index, mustCount, day);
    }

    /**
     * 获取送礼顺序排名
     */
    @GetMapping("sendGiftLottery/getJYSendGiftRank")
    public Response getJYSendGiftRank(HttpServletRequest req, long actId, long index, String day) throws Exception {
        checkEnvForGrey(req, actId);
        return jySendGiftLotteryComponent.getSendGiftRank(actId, index, day);
    }

    /**
     * 增加用户的兑换额度
     */
    @GetMapping("zw_exchange/addUserQuote")
    public Response addUserShopCurrency(HttpServletRequest req, HttpServletResponse resp, long actId, long index, long uid, int count) throws Exception {
        checkEnvForGrey(req, actId);
        zhuiwanDatingExchangeComponent.addUserQuoteByTest(actId, index, uid, count);
        return Response.success("");
    }


    @GetMapping("zw_exchange/setDailyLimit")
    public Response setDailyLimit(HttpServletRequest req, HttpServletResponse resp, long actId, long index, long dayLimit) {

        long loginUid = getLoginYYUid(req, resp);
        if (loginUid == 0) {
            return Response.fail(1, "未登录");
        }
        return zhuiwanDatingExchangeComponent.setDailyLimit(actId, index, dayLimit, loginUid);

    }

    @GetMapping("zw_exchange/getDailyQuotaLimit")
    public Response getDailyQuotaLimit(HttpServletRequest req, HttpServletResponse resp, long actId, long index) {

        long loginUid = getLoginYYUid(req, resp);
        if (loginUid == 0) {
            return Response.fail(1, "未登录");
        }
        return zhuiwanDatingExchangeComponent.getDailyQuotaLimit(actId, index, loginUid);
    }


    @GetMapping("memberRoleChange/manualAdjust")
    public Response manualAdjust(HttpServletRequest req, HttpServletResponse resp,
                                 long actId, long index, String memberId, long rankId, long phaseId) throws Exception {
        // 非本地要检查办公ip
        String ip = RequestUtil.getRealIp(req);
        if (!SysEvHelper.isLocal()) {
            if (!isOfficeIp(ip)) {
                throw new Exception("只允许办公网IP发起请求， your ip:" + ip);
            }
        }
        long loginUid = getLoginYYUid(req, resp);
        return memberRoleChangeV1Component.manualAdjust(actId, index, memberId, rankId, phaseId, loginUid);
    }

    @GetMapping("releasePackageItem")
    public Response releasePackageItem(HttpServletRequest req, HttpServletResponse resp,
                                       Long packageId, Long itemId, Long uid, String seq) throws Exception {
        // 非本地要检查办公ip
        String ip = RequestUtil.getRealIp(req);
        if (!SysEvHelper.isLocal()) {
            if (!isOfficeIp(ip)) {
                throw new Exception("只允许办公网IP发起请求， your ip:" + ip);
            }
        }
        long loginUid = getLoginYYUid(req, resp);
        if (loginUid <= 0) {
            return Response.fail(1, "未登录");
        }
        return hdztAwardService.releasePackageItem(packageId, itemId, uid, loginUid, seq);
    }

    @GetMapping("releaseTaskItem")
    public Response releaseTaskItem(HttpServletRequest req, HttpServletResponse resp,
                                    Long taskId, Long uid, String seq) throws Exception {
        // 非本地要检查办公ip
        String ip = RequestUtil.getRealIp(req);
        if (!SysEvHelper.isLocal()) {
            if (!isOfficeIp(ip)) {
                throw new Exception("只允许办公网IP发起请求， your ip:" + ip);
            }
        }
        long loginUid = getLoginYYUid(req, resp);
        if (loginUid <= 0) {
            return Response.fail(1, "未登录");
        }
        return hdztAwardService.releaseTaskItem(taskId, uid, loginUid, seq);
    }


    /**
     * 必须用https 要不然会获取不到登录态
     *
     * @param req
     * @param resp
     * @param filePath
     * @return
     * @throws Exception
     */
    @GetMapping("batchAwardByFile")
    public Response batchAwardByFile(HttpServletRequest req, HttpServletResponse resp, String filePath) throws Exception {
        // 非本地要检查办公ip
        String ip = RequestUtil.getRealIp(req);
        if (!SysEvHelper.isLocal()) {
            if (!isOfficeIp(ip)) {
                throw new Exception("只允许办公网IP发起请求， your ip:" + ip);
            }
        }
        if (SysEvHelper.isDeploy()) {
            long uid = getLoginYYUid(req, resp);
            if (uid == 0) {
                return Response.fail(1, "未登录");
            }
            String uids = Const.getGeValue(null, GeParamName.BATCH_AWARD_BY_FILE_UIDS, "");
            if (!ArrayUtils.contains(uids.split(StringUtil.COMMA), uid + "")) {
                return Response.fail(1, "权限不足");
            }
        }
        hdztAwardService.batchAwardByFile(filePath);
        return Response.success("");
    }

    @GetMapping("warfareRegimentAddTest")
    public Response warfareRegimentTest(HttpServletRequest req, long actId, long uid, Long anchorUid, long sid, long ssid) throws Exception {
        checkEnvForGrey(req, actId);
        return warfareRegiment.addAnchorRegimentTest(actId, uid, anchorUid, sid, ssid);
    }

    /**
     * 增加用户的商城货币
     */
    @GetMapping("shop/addUserCurrency")
    public Response addUserShopCurrency(HttpServletRequest req, HttpServletResponse resp, long actId, long index, long uid, String currency, int count) throws Exception {
        checkEnvForGrey(req, actId);
        shopComponent.addUserCurrency(actId, index, uid, currency, count);
        return Response.success("");
    }

    /**
     * 告白动效测试接口
     */
    @GetMapping("showLoveTest")
    public Response showLoveTest(HttpServletRequest req, long actId, int index, long busiId, long uid, long anchorUid, long sid, long ssid, long num) throws Exception {
        checkEnvForGrey(req, actId);
        showLoveComponent.test(actId, index, busiId, uid, anchorUid, sid, ssid, num);
        return Response.success("");
    }

    /**
     * 奖品单播测试
     *
     * @param req
     * @param actId
     * @param index
     * @param uid
     * @return
     * @throws Exception
     */
    @GetMapping("awardUnicastTest")
    public Response awardUnicastTest(HttpServletRequest req, long actId, int index, long uid) throws Exception {
        checkEnvForGrey(req, actId);
        awardUnicastComponent.test(actId, index, uid);
        return Response.success("");
    }

    /**
     * 活动飞机
     *
     * @param req
     * @param resp
     * @param actId
     * @param index
     * @param busiId
     * @param sendUid
     * @param recvUid
     * @param giftId
     * @param giftNum
     * @param count
     * @param sid
     * @param ssid
     * @param seq
     * @return
     * @throws Exception
     */
    @GetMapping("activityPlane/doTakeOffPlane")
    public Response doTakeOffPlane(HttpServletRequest req, HttpServletResponse resp,
                                   long actId, long index, long busiId, long sendUid, long recvUid, String giftId,
                                   long giftNum, int count, long sid, long ssid, String seq) throws Exception {

        // 非本地要检查办公ip
        String ip = RequestUtil.getRealIp(req);
        if (!SysEvHelper.isLocal()) {
            if (!isOfficeIp(ip)) {
                throw new Exception("只允许办公网IP发起请求， your ip:" + ip);
            }
        }
        long opUid = getLoginYYUid(req, resp);
        if (SysEvHelper.isDeploy()) {
            if (opUid == 0) {
                return Response.fail(1, "未登录");
            }
            String uids = Const.getGeValue(null, GeParamName.DO_TASK_OFF_PLANE_UIDS, "");
            if (!ArrayUtils.contains(uids.split(StringUtil.COMMA), opUid + "")) {
                return Response.fail(1, "权限不足");
            }
        }

        Response response = activityPlaneComponent.doTakeOffPlane(actId, index, busiId, sendUid, recvUid, giftId, giftNum, count, sid, ssid, seq);

        log.info("doTakeOffPlane info@opUid:{} queryString:{}", opUid, req.getQueryString());
        return response;
    }

    /**
     * 获取 获取榜单-内部使用不做样式处理
     */
    @RequestMapping(value = "/queryRankInterior")
    @ResponseBody
    public Object queryRank(HttpServletRequest req, HttpServletResponse resp,
                            Long actId, Long rankId, Long phaseId, String dateStr,
                            String findSrcMember, Integer queryRankType, Integer rankCount) {

        try {
            String ip = RequestUtil.getRealIp(req);
            log.info("queryRankInterior ip:{}", ip);
            if (!SysEvHelper.isLocal()) {
                //管理后台ip
                String managementIp = "************,**************";
                if (!isOfficeIp(ip) && !managementIp.contains(ip)) {
                    throw new Exception("只允许办公网IP发起请求， your ip:" + ip);
                }
            }
            if (StringUtils.isBlank(dateStr)) {
                dateStr = "";
            }
            if (rankCount == null) {
                rankCount = 100;
            }
            if (phaseId == null) {
                phaseId = 0L;
            }
            if (StringUtils.isBlank(findSrcMember)) {
                findSrcMember = "";
            }
            if (queryRankType == null || queryRankType < 0) {
                queryRankType = 0;
            }

            RankInfo rankInfo = hdztRankService.getRankInfo(actId, rankId, phaseId, dateStr, findSrcMember, queryRankType, rankCount);
            return new Response<>(Code.OK.getCode(), "ok", rankInfo);
        } catch (Exception e) {
            log.error("queryRankInterior exception,actId:{},rankId:{},phaseId:{},dateStr:{},findSrcMember:{},queryRankType:{} {}"
                    , actId, rankId, phaseId, dateStr, findSrcMember, queryRankType, e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    /**
     * 获取 zset key 下指定范围的排名数据
     */
    @RequestMapping(value = "/zrevRange")
    @ResponseBody
    public Object zrevRange(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        long start = Convert.toLong(request.getParameter("start"), 0);
        long end = Convert.toLong(request.getParameter("end"), -1);
        try {
            checkEnvForGrey(request, actId);
            checkKey(actId, key);
            String groupCode = redisConfigManager.getGroupCode(actId);
            Set<ZSetOperations.TypedTuple<String>> set = actRedisDao.zrevRange(groupCode, key, start, end);
            List<String> result = Lists.newArrayList();
            int inx = 0;
            for (ZSetOperations.TypedTuple<String> tuple : set) {
                inx++;
                String member = tuple.getValue();
                long score = tuple.getScore().longValue();
                result.add("(" + inx + ") " + member + " -> " + score);
            }
            return new Object[]{"zrevRange", key + " " + start + " " + end, result};
        } catch (Throwable t) {
            log.error("zrevRange exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 获取 zset key 下指定 member 的分值 和 排名
     */
    @RequestMapping(value = "/zScoreAndRank")
    @ResponseBody
    public Object zScoreAndRank(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        String member = StringUtil.trim(request.getParameter("member"));
        try {
            checkEnvForGrey(request, actId);
            checkKey(actId, key);
            String groupCode = redisConfigManager.getGroupCode(actId);
            List<Long> result = actRedisDao.zScoreAndRank(groupCode, key, member);
            return new Object[]{"zScoreAndRank", key + " " + member, result};
        } catch (Throwable t) {
            log.error("zScoreAndRank exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 增加 zset key 下指定 member 的分值
     */
    @RequestMapping(value = "/zIncr")
    @ResponseBody
    public Object zIncr(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        String member = StringUtil.trim(request.getParameter("member"));
        long score = Convert.toLong(request.getParameter("score"));
        try {
            checkEnvForGrey(request, actId);
            checkKey(actId, key);
            String groupCode = redisConfigManager.getGroupCode(actId);
            long result = actRedisDao.zIncr(groupCode, key, member, score);
            return new Object[]{"zIncr", key + " " + member + " " + score, result};
        } catch (Throwable t) {
            log.error("zIncr exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 删除 zset key 下指定 member
     */
    @RequestMapping(value = "/zDel")
    @ResponseBody
    public Object zDel(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        String member = StringUtil.trim(request.getParameter("member"));
        try {
            checkEnvForGrey(request, actId);
            checkKey(actId, key);
            String groupCode = redisConfigManager.getGroupCode(actId);
            actRedisDao.zDel(groupCode, key, member);
            return new Object[]{"zDel", key + " " + member, "ok!"};
        } catch (Throwable t) {
            log.error("zDel exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 增加 set key 下指定 member
     */
    @RequestMapping(value = "/sadd")
    @ResponseBody
    public Object sAdd(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        String member = StringUtil.trim(request.getParameter("member"));
        try {
            checkEnvForGrey(request, actId);
            checkKey(actId, key);
            String groupCode = redisConfigManager.getGroupCode(actId);
            Long result = actRedisDao.sAdd(groupCode, key, member);
            return new Object[]{"sAdd", key + " " + member, result.longValue()};
        } catch (Throwable t) {
            log.error("sAdd exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 增加 set key 下指定 member
     */
    @RequestMapping(value = "/saddRand")
    @ResponseBody
    public Object sAddRand(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        Integer start = Convert.toInt(request.getParameter("startMember"));
        Integer num = Convert.toInt(request.getParameter("num"), 1);
        try {
            checkEnvForGrey(request, actId);
            checkKey(actId, key);
            String[] members = IntStream.range(start, start + num).mapToObj(String::valueOf).toArray(String[]::new);
            String groupCode = redisConfigManager.getGroupCode(actId);
            Long result = actRedisDao.getRedisTemplate(groupCode).opsForSet().add(key, members);
            String memberString = StringUtils.join(members, " ");
            return new Object[]{"sAdd", key + " " + memberString, result.longValue()};
        } catch (Throwable t) {
            log.error("sAdd exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 删除 set key 下指定 member
     */
    @RequestMapping(value = "/sDel")
    @ResponseBody
    public Object sDel(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        String member = StringUtil.trim(request.getParameter("member"));
        try {
            checkEnvForGrey(request, actId);
            checkKey(actId, key);

            String groupCode = redisConfigManager.getGroupCode(actId);
            Long result = actRedisDao.sRem(groupCode, key, member);
            return new Object[]{"sDel", key + " " + member, "ok!" + result};
        } catch (Throwable t) {
            log.error("sDel exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 获取 set key 下所有 member
     */
    @RequestMapping(value = "/sMembers")
    @ResponseBody
    public Object sMembers(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        try {
            checkEnvForGrey(request, actId);
            checkKey(actId, key);
            String groupCode = redisConfigManager.getGroupCode(actId);
            Set<String> result = actRedisDao.sMembers(groupCode, key);
            return new Object[]{"sMembers", key, result};
        } catch (Throwable t) {
            log.error("sMembers exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 获取 hash key 所有 field 的内容
     */
    @RequestMapping(value = "/hGetAll")
    @ResponseBody
    public Object hGetAll(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        try {
            checkEnvForGrey(request, actId);
            String groupCode = redisConfigManager.getGroupCode(actId);
            Map<Object, Object> result = actRedisDao.hGetAll(groupCode, key);
            return new Object[]{"hGetAll", key, result};
        } catch (Throwable t) {
            log.error("hGetAll exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 获取 hash key 下指定 field 的内容
     */
    @RequestMapping(value = "/hgetKey")
    @ResponseBody
    public Object hgetKey(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        String field = StringUtil.trim(request.getParameter("field"));
        try {
            checkEnvForGrey(request, actId);
            String groupCode = redisConfigManager.getGroupCode(actId);
            String result = actRedisDao.hget(groupCode, key, field);
            return new Object[]{"hgetKey", key + " " + field, result};
        } catch (Throwable t) {
            log.error("hgetKey exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 设置 hash key 下指定 field 的内容
     */
    @RequestMapping(value = "/hsetKey")
    @ResponseBody
    public Object hsetKey(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        String field = StringUtil.trim(request.getParameter("field"));
        String value = StringUtil.trim(request.getParameter("value"));
        try {
            checkEnvForGrey(request, actId);
            String groupCode = redisConfigManager.getGroupCode(actId);
            actRedisDao.hset(groupCode, key, field, value, -1);
            return new Object[]{"hsetKey", key + " " + field + " " + value, "ok!"};
        } catch (Throwable t) {
            log.error("hsetKey exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 删除 hash key 中的指定 field
     */
    @RequestMapping(value = "/hdelKey")
    @ResponseBody
    public Object hdelKey(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        String field = StringUtil.trim(request.getParameter("field"));
        try {
            checkEnvForGrey(request, actId);

            String groupCode = redisConfigManager.getGroupCode(actId);
            actRedisDao.hdelKey(groupCode, key, Lists.newArrayList(field));
            return new Object[]{"hdelKey", key + " " + field, "ok!"};
        } catch (Throwable t) {
            log.error("hdelKey exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 删除任意key
     */
    @RequestMapping(value = "/del")
    @ResponseBody
    public Object del(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String key = StringUtil.trim(request.getParameter("key"));
        try {
            checkEnvForGrey(request, actId);

            String groupCode = redisConfigManager.getGroupCode(actId);
            actRedisDao.del(groupCode, key);
            return new Object[]{"del", key, "ok!"};
        } catch (Throwable t) {
            log.error("del exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }

    /**
     * 列出指定模式的key
     */
    @RequestMapping(value = "/keys")
    @ResponseBody
    public Object keys(HttpServletRequest request, HttpServletResponse response, ModelMap map) {
        long actId = Convert.toLong(request.getParameter("actId"));
        String pattern = StringUtil.trim(request.getParameter("pattern"));
        try {
            checkEnvForGrey(request, actId);
            String str = pattern.replaceAll("\\*", "");
            int num = 3;
            if (str.length() < num) {
                return "明确的字符数不能少于" + num + "个：" + pattern;
            }

            String groupCode = redisConfigManager.getGroupCode(actId);
            Set<String> keys = actRedisDao.keys(groupCode, pattern);
            return new Object[]{"keys", pattern, keys};
        } catch (Throwable t) {
            log.error("keys exception@actId:{}, err:{}", actId, t.getMessage(), t);
            return t;
        }
    }


    private void checkKey(long actId, String key) throws Exception {
        if (StringUtils.isBlank(key)) {
            throw new Exception("key is black.");
        }

        if (!Const.checkActivityKey(actId, key)) {
            throw new Exception("key = " + key + " 不包含活动id，actId = " + actId);
        }
    }

    @RequestMapping(value = "/getPwChannel")
    @ResponseBody
    public List<Long> getPwChannel() {
        return zhuiWanCommonListClient.queryPeiWanOnlineChannel();
    }


//    @RequestMapping(value = "/queryFtsGroup6666666")
//    @ResponseBody
//    public String queryFtsGroup(HttpServletRequest request, HttpServletResponse response, long actId, boolean isGray, int busiId, String sourceUrl) {
//        long login = getLoginYYUid(request, response);
//        if (login != 50042952 && login != 50044432) {
//            throw new RuntimeException("权限不足");
//        }
//        List<String> contents = ReadFileUtil.readFromFile(sourceUrl, "utf8");
//        StringBuilder result = new StringBuilder();
//        for (String uid : contents) {
//
//            Map<String, String> queryMap = Maps.newHashMap();
//
//            queryMap.put(QueryType.TypeCompere.name(), uid);
//            Map<String, String> groupResult = ftsGroupCenterThriftClient.queryGroupV2((int) actId, queryMap, busiId, actId, isGray);
//            result.append(uid)
//                    .append(",")
//                    .append(Convert.toString(groupResult.getOrDefault(QueryType.TypeCompere.name(), ""), "0"))
//                    .append("\r\n");
//        }
//
//        return result.toString();
//    }


    @RequestMapping(value = "/testPipeline")
    public Response<String> testPipeline(Long actId, int size) {
        if (SysEvHelper.isDeploy()) {
            return Response.fail(-1, "deny");
        }

        String redisCode = redisConfigManager.getGroupCode(actId);

        Clock clock = new Clock();
        actRedisDao.getRedisTemplate(redisCode).executePipelined((RedisConnection connection) -> {
            //pk关系
            for (int i = 0; i < size; i++) {
                connection.hSetNX("hsettest".getBytes(), UUID.randomUUID().toString().getBytes(), DateUtil.format(new Date()).getBytes());
            }
            return null;
        });
        log.info("testPipeline,size:{},clock:{}", size, clock.tag());
        return Response.ok(String.format("size:%s,clock:%s", size, clock));
    }


    @RequestMapping(value = "/testSendJiaoyouLayerKafka")
    public Response<String> testSendJiaoyouLayerKafka(Long actId, long sid, long ssid, boolean showLayer) {
        if (SysEvHelper.isDeploy()) {
            return Response.fail(-1, "deny");
        }

        kafkaService.sendJiaoyouLayerKafka(actId, sid, ssid, showLayer);

        return Response.ok("testSendJiaoyouLayerKafka ok");
    }

    @RequestMapping(value = "/testCommonNoticeUnicast")
    public Response<String> testCommonNoticeUnicast(Long actId, long userUid) {
        if (SysEvHelper.isDeploy()) {
            return Response.fail(-1, "deny");
        }

        Map<String, Object> noticeExt = ImmutableMap.of(
                "award", "测试奖励"
                , "taskName", "单播测试任务");
        commonBroadCastService.commonNoticeUnicast(actId, "5074_noticeCompleteTask", StringUtil.EMPTY, JSON.toJSONString(noticeExt), userUid);

        return Response.ok("testCommonNoticeUnicast ok");
    }

    @RequestMapping(value = "/scanRedisTools159856")
    public Set<String> scanRedisTools159856(HttpServletRequest req, HttpServletResponse resp, Long actId, String keyword) {
        long opUid = getLoginYYUid(req, resp);
        if (opUid != 50042952) {
            return Sets.newHashSet("user deny");
        }

        return redisScanTool.scanActRedisKey(actId, keyword);
    }



}
