package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.Set;

@Getter
@Setter
public class OnlineNoticeComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "单播限定业务ID", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class), remark = "进频道单播限定的busiId，为空则不限定，多个使用逗号分隔开")
    protected Set<Long> businessIds;

    @ComponentAttrField(labelText = "单播限定hostId", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class), remark = "进频道单播限定的hostId，为空则不限定，多个使用逗号分隔开")
    protected Set<Integer> noticeHostIds;

    @ComponentAttrField(labelText = "单播限定host", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class), remark = "进频道单播限定的host，为空则不限定，多个使用逗号分隔开")
    protected Set<String> noticeHosts;

    @ComponentAttrField(labelText = "初始延迟", remark = "进频道后触发的单播的初始延迟时间")
    protected Duration initDelay;

    @ComponentAttrField(labelText = "间隔延迟", remark = "进频道后触发的每两个单播之间的间隔延迟")
    protected Duration intervalDelay;

    @ComponentAttrField(labelText = "保留时长", remark = "在线单播的保留时长")
    protected Duration expireDuration = Duration.ZERO;
}
