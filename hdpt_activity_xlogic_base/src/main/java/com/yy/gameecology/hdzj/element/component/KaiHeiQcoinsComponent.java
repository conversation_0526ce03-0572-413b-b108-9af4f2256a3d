package com.yy.gameecology.hdzj.element.component;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.KaiHeiQcoinsComponentAttr;
import com.yy.thrift.hdztaward.AwardIssueRecordInfo;
import com.yy.thrift.hdztaward.AwardIssueRecordResult;
import com.yy.thrift.zhuiwan.UserBatchReleaseRsp;
import com.yy.thrift.zhuiwan.UserReleaseWithAccountInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/5110")
public class KaiHeiQcoinsComponent extends BaseActComponent<KaiHeiQcoinsComponentAttr> {

    private static final Integer ORDER_NOTEXIST = 404;

    private static final String QQ_ACCOUNT_BIND_UID = "account_bind_uid";

    private static final String ACCOUNT_AUTO_FILL = "account_auto_fill";

    private static final String ACCOUNT_AUTO_FILL_GAME_AREA_ID = "gameAreaId";
    private static final String ACCOUNT_AUTO_FILL_GAME_SERVER_NAME = "gameServerName";
    private static final String ACCOUNT_AUTO_FILL_GAME_SUB_ACCOUNT = "gameSubAccount";

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;


    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;




    @Autowired
    private CommonDataDao commonDataDao;


    @Override
    public Long getComponentId() {
        return ComponentId.KAI_HEI_QCOINS;
    }


    /**
     * 查看登录任务点券填写信息、状态
     */
    @RequestMapping("/getQcoinsAccountInfo")
    public Response<UserReleaseInfo> getQcoinsAccountInfo(HttpServletRequest request, HttpServletResponse response,
                                                          @RequestParam("actId") long actId,
                                                          @RequestParam("cmptIndex") long cmptIndex,
                                                          @RequestParam(name = "type", defaultValue = "0") int type) {

        Long uid = getLoginYYUid(request, response);
        if (uid <= 0L) {
            return Response.fail(400, "未登录");
        }

        KaiHeiQcoinsComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }


        try {
            List<Long> loginPackageId = Lists.newArrayList(attr.getLoginPackageId());
            List<Long> taskPackageId = Lists.newArrayList(attr.getPackageIds());
            taskPackageId.remove(attr.getLoginPackageId());
            List<Long> packageId = type == 0 ? loginPackageId
                    : taskPackageId;
            List<UserReleaseInfo> userReleaseList = getTaskAwardState(actId, uid, attr.getTaskId(), packageId);
            if (CollectionUtils.isEmpty(userReleaseList)) {
                UserReleaseInfo userReleaseInfo = new UserReleaseInfo();
                userReleaseInfo.setStatus(ORDER_NOTEXIST);
                return Response.success(userReleaseInfo);
            } else {
                //有多笔时，状态优先级：发放失败（或未填写） ＞ 发放中 ＞ 发放成功
                UserReleaseInfo userReleaseInfo = userReleaseList.get(0);
                int state = userReleaseInfo.getStatus();
                boolean existFailed = userReleaseList.stream().anyMatch(p -> p.getStatus() == UserReleaseInfoState.FAILED);
                boolean existPending = userReleaseList.stream().anyMatch(p -> p.getStatus() == UserReleaseInfoState.PENDING);
                if (existFailed) {
                    state = UserReleaseInfoState.FAILED;
                } else if (existPending) {
                    state = UserReleaseInfoState.PENDING;
                }
                userReleaseInfo.setStatus(state);

                fillAcountInfoExtInfo(attr, userReleaseInfo.getAccountInfo(), uid);

                return Response.success(userReleaseInfo);
            }
        } catch (BusinessException e) {
            log.error("getQcoinsAccountInfo e:{}", e.getMessage(), e);
            return Response.fail(1, e.getMessage());
        } catch (Exception e) {
            log.error("getQcoinsAccountInfo e:{}", e.getMessage(), e);
            return Response.fail(1, "网络异常");
        }

    }

    /**
     * 填写账号信息
     *
     * @return
     */
    @RequestMapping("/updateQcoinsAccountInfo")
    public Response updateQcoinsAccountInfo(HttpServletRequest request, HttpServletResponse response,
                                            @RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex,
                                            String gameAccount,
                                            String buyerMobile,
                                            String gameServerName,
                                            Integer gameAreaId,
                                            String gameSubAccount) {


        Long uid = getLoginYYUid(request, response);
        if (uid <= 0L) {
            return Response.fail(400, "未登录");
        }

        KaiHeiQcoinsComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        if (StringUtil.isBlank(gameAccount)) {
            return Response.fail(1, "角色昵称不能为空");
        }
        if (StringUtil.isBlank(buyerMobile)) {
            return Response.fail(1, "手机号不能为空");
        }
        if (StringUtil.isBlank(gameServerName) || gameAreaId == null) {
            return Response.fail(1, "游戏区服不能为空");
        }
        if (StringUtil.isBlank(gameSubAccount)) {
            return Response.fail(1, "游戏阵营不能为空");
        }


        //检测QQ号
        if (accountHasBind(uid, gameAccount, attr)) {
            log.warn("getQcoinsAccountInfo accountHasBind,actId:{},uid:{},gameAccount:{}", actId, uid, gameAccount);
            return Response.fail(1, "此账号已被其他YY绑定，请重新填写");
        }
        log.info("updateQcoinsAccountInfo actId:{},uid:{},gameAccount:{},buyerMobile:{}", actId, uid, gameAccount, buyerMobile);
        try {
            AwardIssueRecordResult issueResult = hdztAwardServiceClient.queryUserAwardIssues(attr.getBusiId(), uid, attr.getTaskId(), attr.getPackageIds());
            if (issueResult == null) {
                return Response.fail(1, "网络异常");
            }
            if (issueResult.getRetCode() != 0) {
                return Response.fail(1, "查询获奖记录失败");
            }

            List<AwardIssueRecordInfo> issueRecords = issueResult.getAwardList();
            if (CollectionUtils.isEmpty(issueRecords)) {
                return Response.fail(1, "没有获奖记录需要填写账号信息");
            }

            zhuiWanPrizeIssueServiceClient.updateAccountInfo(uid, gameAccount, gameSubAccount, buyerMobile, gameServerName, Convert.toInt(gameAreaId, 0), issueRecords.stream().map(v -> v.getIssueSeq()).collect(Collectors.toList()));
            //更新QQ绑定的uid信息
            updateAccountBindUid(uid, gameAccount, attr);

            //保存需要下次自动填充的信息
            Map<String, Object> autoFill = ImmutableMap.of(ACCOUNT_AUTO_FILL_GAME_AREA_ID, gameAreaId,
                    ACCOUNT_AUTO_FILL_GAME_SERVER_NAME, gameServerName,
                    ACCOUNT_AUTO_FILL_GAME_SUB_ACCOUNT, gameSubAccount);
            commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), ACCOUNT_AUTO_FILL, Convert.toString(uid), JSON.toJSONString(autoFill));

            return Response.ok("填写成功");
        } catch (BusinessException e) {
            log.error("updateAccountInfo e:{}", e.getMessage(), e);
            return Response.fail(1, e.getMessage());
        } catch (Exception e) {
            log.error("updateAccountInfo e:{}", e.getMessage(), e);
            return Response.fail(1, "网络异常");
        }

    }

    private boolean accountHasBind(long uid, String gameAccount, KaiHeiQcoinsComponentAttr attr) {
        String accountUid = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), QQ_ACCOUNT_BIND_UID, gameAccount);
        return StringUtil.isNotBlank(accountUid) && !Convert.toString(uid).equals(accountUid);
    }

    private void updateAccountBindUid(long uid, String gameAccount, KaiHeiQcoinsComponentAttr attr) {
        log.info("updateAccountBindUid uid:{},gameAccount:{}", uid, gameAccount);
        commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), QQ_ACCOUNT_BIND_UID, gameAccount, Convert.toString(uid));
    }


    /**
     * 同步登录任务填写的账号到打卡任务
     */
    public void fillInAccountInfo(FillQCoinAccountInfo info) {
        long actId = info.getActId();
        long uid = info.getUid();
        int taskId = (int) info.getTaskId();

        KaiHeiQcoinsComponentAttr attr = tryGetUniqueComponentAttr(actId);

        AwardIssueRecordResult issueResult = hdztAwardServiceClient.queryUserAwardIssues(attr.getBusiId(), uid, taskId, info.getPackageIds());
        log.info("fillInAccountInfo uid:{},info:{},issueResult:{}", uid, JSON.toJSONString(info), JSON.toJSONString(issueResult));
        if (issueResult == null) {
            throw new BusinessException(500, "网络异常");
        }

        if (issueResult.getRetCode() != 0) {
            throw new BusinessException(500, "查询发奖记录失败");
        }

        List<AwardIssueRecordInfo> issueRecords = issueResult.getAwardList();
        if (CollectionUtils.isEmpty(issueRecords)) {
            log.warn("fillInAccountInfo issueRecords is empty,uid:{},info:{}", uid, JSON.toJSONString(info));
            return;
        }

        UserReleaseInfo userReleaseInfo = getUserAccountInfo(attr, uid);
        if (userReleaseInfo == null || userReleaseInfo.getStatus() == 6) {
            log.warn("fillInAccountInfo userReleaseInfo is empty,uid:{},info:{}", uid, JSON.toJSONString(userReleaseInfo));
            return;
        }

        UserAccountInfo accountInfo = userReleaseInfo.getAccountInfo();

        fillAcountInfoExtInfo(attr, accountInfo, uid);

        zhuiWanPrizeIssueServiceClient.updateAccountInfo(uid, accountInfo.getGameAccount(), accountInfo.getGameSubAccount(), accountInfo.getBuyerMobile(), accountInfo.getGameServerName(), accountInfo.getGameAreaId(), issueRecords.stream().map(v -> v.getIssueSeq()).collect(Collectors.toList()));
    }

    private void fillAcountInfoExtInfo(KaiHeiQcoinsComponentAttr attr, UserAccountInfo accountInfo, long uid) {
        String extInfo = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), ACCOUNT_AUTO_FILL, Convert.toString(uid));
        if (StringUtil.isBlank(extInfo)) {
            log.warn("fillInAccountInfo extInfo is empty,uid:{},info:{}", uid);
            return;
        }

        JSONObject jsonObject = JSON.parseObject(extInfo);
        if (StringUtil.isBlank(accountInfo.getGameServerName())) {
            accountInfo.setGameServerName(jsonObject.getString(ACCOUNT_AUTO_FILL_GAME_SERVER_NAME));
        }
        if (StringUtil.isBlank(accountInfo.getGameSubAccount())) {
            accountInfo.setGameSubAccount(jsonObject.getString(ACCOUNT_AUTO_FILL_GAME_SUB_ACCOUNT));
        }
        if (accountInfo.getGameAreaId() == 0) {
            accountInfo.setGameAreaId(jsonObject.getIntValue(ACCOUNT_AUTO_FILL_GAME_AREA_ID));
        }

    }


    /**
     * 查询任务奖励发状态
     */
    public List<UserReleaseInfo> getTaskAwardState(long actId, long uid, int awardTaskId, List<Long> awardPackageId) {
        KaiHeiQcoinsComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (CollectionUtils.isEmpty(awardPackageId)) {
            throw new BusinessException(401, "奖包参数不能为空");
        }
        AwardIssueRecordResult issueResult = hdztAwardServiceClient.queryUserAwardIssues(attr.getBusiId(), uid, awardTaskId, awardPackageId);
        if (issueResult == null) {
            throw new BusinessException(500, "网络异常");
        }

        if (issueResult.getRetCode() != 0) {
            throw new BusinessException(500, "查询发奖记录失败");
        }

        List<AwardIssueRecordInfo> issueRecords = issueResult.getAwardList();
        if (CollectionUtils.isEmpty(issueRecords)) {
            log.warn("getTaskAwardState issueRecords is empty,actId:{},uid:{},awardTaskId:{},awardPackageId:{}", actId, uid, awardTaskId, JsonUtil.toJson(awardPackageId));
            return Collections.EMPTY_LIST;
        }

        List<String> orderSeqs = issueRecords.stream().map(v -> v.getIssueSeq()).collect(Collectors.toList());
        UserBatchReleaseRsp releaseResult = zhuiWanPrizeIssueServiceClient.batchGetUserReleaseInfo(uid, orderSeqs);
        if (releaseResult == null) {
            throw new BusinessException(500, "网络异常");
        }
        if (releaseResult.getCode() != 0) {
            throw new BusinessException(500, "查询发放记录失败");
        }

        List<UserReleaseInfo> userReleaseList = getTaskAwardReleaseState(releaseResult.getReleaseInfoList());
        return userReleaseList;
    }

    private List<UserReleaseInfo> getTaskAwardReleaseState(List<UserReleaseWithAccountInfo> releaseInfoList) {

        List<UserReleaseInfo> userReleaseList = Collections.EMPTY_LIST;
        if (CollectionUtils.isNotEmpty(releaseInfoList)) {
            userReleaseList = releaseInfoList.stream().map(v -> {
                UserReleaseInfo item = new UserReleaseInfo();
                item.setStatus(v.getStatus());
                UserAccountInfo accountInfo = new UserAccountInfo();
                if (v.getAccountInfo() != null) {
                    accountInfo.setGameAccount(v.getAccountInfo().getGameAccount());
                    accountInfo.setBuyerMobile(v.getAccountInfo().getBuyerMobile());
                    accountInfo.setGameServerName(v.getAccountInfo().getGameServerName());
                    accountInfo.setGameSubAccount(v.getAccountInfo().getGameSubAccount());
                }
                item.setAccountInfo(accountInfo);
                return item;
            }).collect(Collectors.toList());
        }

        return userReleaseList;
    }


    private UserReleaseInfo getUserAccountInfo(KaiHeiQcoinsComponentAttr attr, long uid) {

        List<UserReleaseInfo> userReleaseList = getTaskAwardState(attr.getActId(), uid, attr.getTaskId(), Lists.newArrayList(attr.getLoginPackageId()));
        if (CollectionUtils.isEmpty(userReleaseList)) {
            log.warn("getUserAccountInfo empty uid:{}", uid);
            return null;
        }

        return userReleaseList.get(0);

    }


    private UserReleaseInfo getUserReleaseInfo(UserReleaseWithAccountInfo releaseInfo) {
        UserReleaseInfo userReleaseInfo = new UserReleaseInfo();
        userReleaseInfo.setStatus(releaseInfo.getStatus());
        UserAccountInfo accountInfo = new UserAccountInfo();
        if (releaseInfo.getAccountInfo() != null) {
            accountInfo.setGameAccount(releaseInfo.getAccountInfo().getGameAccount());
            accountInfo.setGameAccount(releaseInfo.getAccountInfo().getBuyerMobile());

        }
        userReleaseInfo.setAccountInfo(accountInfo);
        return userReleaseInfo;
    }


    @Data
    public static class UserReleaseInfo {
        /**
         * 追玩订单状态,1=有效，4=发放中，6=等待完善信息
         * Q币这里没有2和5，追玩转化成了6
         */
        private int status;
        private UserAccountInfo accountInfo;
    }

    interface UserReleaseInfoState {
        /**
         * 发放成功
         */
        int SUCCEED = 1;
        /**
         * 发放中
         */
        int PENDING = 4;
        /*
         *未填写或发放失败
         */
        int FAILED = 6;
    }

    @Data
    public static class UserAccountInfo {
        private String gameAccount;//充值账号或游戏账号
        private String buyerMobile;//手机号
        /**
         * 区服
         */
        private String gameServerName;

        private String gameSubAccount;

        private int gameAreaId;
    }


    @Data
    public static class FillQCoinAccountInfo {
        private long actId;
        private long uid;
        private long taskId;
        private List<Long> packageIds;
    }


}
