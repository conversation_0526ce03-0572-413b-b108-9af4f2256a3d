package com.yy.gameecology.hdzj.element.component.xmodule.aov;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.event.AppPopUpEvent;
import com.yy.gameecology.activity.bean.event.JyLayerPushEvent;
import com.yy.gameecology.activity.bean.event.PublicScreenNotifyEvent;
import com.yy.gameecology.activity.bean.mq.ActShareSuccessEvent;
import com.yy.gameecology.activity.bean.mq.HdzkAovRoundSettledEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.yrpc.IMMessageServiceClient;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.aov.AovPushService;
import com.yy.gameecology.activity.service.aov.game.AovGameService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.consts.ZhuiwanScreenNotifyType;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.*;
import com.yy.gameecology.common.db.model.gameecology.aov.*;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.aov.AovGameInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AovPushComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("5121")
public class AovPushComponent extends BaseActComponent<AovPushComponentAttr> {
    
    private static final String START_GAME_SMS = "aov_start_sms:%s";

    private static final String START_GAME_NOTICE = "aov_start_game:%d";

    private static final String MANUAL_CREATE_GAME_URL ="https://" + (SysEvHelper.isDeploy() ? "manager-hdzt.yy.com" : "test-manager-hdzt.yy.com") + "/admin-static/wzry/aovMatches?actId=%d&phaseId=%d&roundNum=%d";

    @Resource
    private AovPhaseMapper phaseMapper;

    @Resource
    private AovGameExtMapper aovGameExtMapper;

    @Resource
    private AovPhaseTeamMapper aovPhaseTeamMapper;
    
    @Resource
    private AovPhaseSubscribeMapper aovPhaseSubscribeMapper;

    @Resource
    private AovPhaseTeamMemberMapper aovPhaseTeamMemberMapper;

    @Resource
    private AovPopupRecordMapper aovPopupRecordMapper;

    @Autowired
    private AovGameService aovGameService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private IMMessageServiceClient imMessageServiceClient;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private AovPushService aovPushService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;
    @Autowired
    private AovPhaseRoundExtMapper aovPhaseRoundExtMapper;

    @Override
    public Long getComponentId() {
        return ComponentId.AOV_PUSH;
    }

    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLogin(ZhuiwanLoginEvent event, AovPushComponentAttr attr) {
        Date now = commonService.getNow(attr.getActId());
        AovPhase phase = phaseMapper.selectProcessingPhase(now, attr.getActId(), AovConst.PhaseState.INIT);
        if (phase == null) {
            return;
        }

        if (phase.getPrevPhaseId() <= 0) {
            return;
        }

        if (!DateUtils.isSameDay(now, phase.getSignupStartTime())) {
            return;
        }

        AovPhaseSubscribe subscribe = aovPhaseSubscribeMapper.selectUserSubscribe(phase.getPrevActId(), phase.getPrevPhaseId(), event.getUid());
        if (subscribe == null || subscribe.getState() != 0) {
            return;
        }

        int rs = aovPhaseSubscribeMapper.updateSubscribeState(subscribe.getId(), 0, 1);
        if (rs <= 0) {
            return;
        }

        // 发
        doSendNotice(attr.getImAppId(), attr.getImSenderUid(), event.getUid(), attr.getImMsgTitle(), attr.getSubscribeMsg(), attr.getImMsgLink());
    }

    @HdzjEventHandler(value = HdzkAovRoundSettledEvent.class, canRetry = true)
    public void onAovRoundSettled(HdzkAovRoundSettledEvent event, AovPushComponentAttr attr) {
        int advancedSize = CollectionUtils.isEmpty(event.getAdvancedTeamIds()) ? 0 : event.getAdvancedTeamIds().size();
        int substituteSize = CollectionUtils.isEmpty(event.getSubstituteTeamIds()) ? 0 : event.getSubstituteTeamIds().size();
        int eliminatedSize = CollectionUtils.isEmpty(event.getEliminatedTeamIds()) ? 0 : event.getEliminatedTeamIds().size();
        String msg = "轮次【" + event.getRoundName() + "】结算成功，获胜晋级队伍数：" + advancedSize + "，补位晋级队伍数：" + substituteSize + "，被淘汰队伍数：" + eliminatedSize;
        // 判断下一轮是否为手动创建
        AovPhaseRound nextRound = aovPhaseRoundExtMapper.selectRound(event.getPhaseId(), event.getRoundNum() - 1);
        if (nextRound != null && nextRound.getCreateGame() != 1) {
            msg += "\n下一轮次【" + nextRound.getRoundName() + "】比赛创建模式为“手动创建”。请及时前往创建比赛：" + String.format(MANUAL_CREATE_GAME_URL, event.getActId(), event.getPhaseId(), event.getRoundNum() - 1);
        }
        String message = buildActRuliuMsg(attr.getActId(), false, "轮次结算成功", msg);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_MOBILE_USER_GROWTH, message, Collections.emptyList());
    }

    @HdzjEventHandler(value = ActShareSuccessEvent.class, canRetry = true)
    public void onActShareSuccessEvent(ActShareSuccessEvent event, AovPushComponentAttr attr) {
        log.info("onActShareSuccessEvent,event:{} attrActId:{}", JSON.toJSONString(event), attr.getActId());
        if (event.getUid() < 0) {
            log.info("uid empty");
            return;
        }
        if (event.getUid() == event.getShareUid()) {
            log.info("two users are the same person uid:{}", event.getUid());
            return;
        }
        if (StringUtil.isBlank(attr.getShareInviteUrl())) {
            log.info("not config shareUrl");
            return;
        }
        if (!StringUtil.isJson(event.getExtJson())) {
            log.info("ext is not json,seq:{},uid:{},ext:{}", event.getSeq(), event.getUid(), event.getExtJson());
            return;
        }
        JSONObject extJson = JSON.parseObject(event.getExtJson());
        Long teamId = extJson.getLong("teamId");
        if (teamId == null) {
            log.warn("teamId is null,uid:{}", event.getUid());
            return;
        }
        AovPhaseTeam team = aovPhaseTeamMapper.selectById(teamId);
        if (team == null) {
            log.warn("team is null,uid:{}", event.getUid());
            return;
        }
        AovPhase aovPhase = phaseMapper.selectByPhaseId(team.getPhaseId());
        if (aovPhase == null || !aovPhase.getActId().equals(attr.getActId())) {
            log.warn("act not match,actId:{},uid:{},aovPhase:{}", attr.getActId(), event.getUid(), JSON.toJSONString(aovPhase));
            return;
        }
        //低版本ios有bug，同1口令1s内会重复上报，这里做个去重
        String dupSeq = makeKey(attr, "onActShareSuccessEventPop:" + event.getUid());
        RetryTool.withRetryCheck(attr.getActId(), dupSeq, Const.TEN, () -> {
            String nick = commonService.getNickName(event.getShareUid(), true);
            AppPopUpEvent appPopUpEvent = new AppPopUpEvent();
            appPopUpEvent.setUid(event.getUid());
            String url = attr.getShareInviteUrl()
                    .replace("{shareNick}", nick)
                    .replace("{shareNickUrlEncode}", URLEncoder.encode(nick, StandardCharsets.UTF_8))
                    .replace("{teamId}", Convert.toString(teamId));
            appPopUpEvent.setPopUrl(url);
            appPopUpEvent.setSeq(makeKey(attr, "appSharePop_" + event.getSeq()));
            appPopUpEvent.setProductTime(System.currentTimeMillis());
            kafkaService.sendAppPopUp(appPopUpEvent);
            log.info("onActShareSuccessEvent done,uid:{},url:{}", event.getUid(), url);
        });

    }

    public boolean hasSubscribe(long actId, long uid, long phaseId) {
        return aovPhaseSubscribeMapper.selectUserSubscribe(actId, phaseId, uid) != null;
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(initialDelay = 2000, fixedDelay = 10000)
    public void trySendSubscribePush() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            Date now = commonService.getNow(actId);

            AovPhase phase = phaseMapper.selectProcessingPhase(now, attr.getActId(), AovConst.PhaseState.INIT);
            if (phase == null) {
                continue;
            }

            if (phase.getPrevPhaseId() <= 0) {
                continue;
            }

            doTrySendSubscribePush(attr, phase, now);
        }
    }

    private void doTrySendSubscribePush(AovPushComponentAttr attr, AovPhase phase, Date now) {
        if (!DateUtils.isSameDay(now, phase.getSignupStartTime())) {
            return;
        }

        LocalTime curTime = DateUtil.toLocalDateTime(now).toLocalTime();

        if (curTime.isBefore(attr.getStartTime()) || curTime.isAfter(attr.getEndTime())) {
            return;
        }

        List<AovPhaseSubscribe> subscribes = aovPhaseSubscribeMapper.selectSubscribes(phase.getPrevActId(), phase.getPrevPhaseId(), 0, 50);
        if (CollectionUtils.isEmpty(subscribes)) {
            return;
        }

        for (AovPhaseSubscribe subscribe : subscribes) {
            int rs = aovPhaseSubscribeMapper.updateSubscribeState(subscribe.getId(), 0, 1);

            if (rs > 0) {
                doSendNotice(attr.getImAppId(), attr.getImSenderUid(), subscribe.getUid(), attr.getImMsgTitle(), attr.getSubscribeMsg(), attr.getImMsgLink());
                userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), subscribe.getUid(), attr.getSubscribeSms(), attr.getSmsSecret(), attr.getSmsAppid());
                log.info("doTrySendSubscribePush to uid:{}", subscribe.getUid());
            }
        }
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(initialDelay = 2000, fixedDelay = 10000)
    public void trySendStartGameNotice() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            Date now = commonService.getNow(actId);

            AovPhase phase = phaseMapper.selectProcessingPhase(now, attr.getActId(), AovConst.PhaseState.INITIALIZED);
            if (phase == null) {
                continue;
            }

            List<AovGame> games = aovGameExtMapper.selectGamesByPhaseId(phase.getId(), List.of(AovConst.GameState.CREATED));
            if (CollectionUtils.isEmpty(games)) {
                continue;
            }

            StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
            for (AovGame game : games) {
                doTrySendStartGameNotice(attr, redisTemplate, game, now);
            }
        }
    }

    private void doTrySendStartGameNotice(AovPushComponentAttr attr, StringRedisTemplate redisTemplate, AovGame game, Date now) {
        if (now.before(game.getStartTime())) {
            return;
        }

        List<AovPhaseTeamMember> members = aovPhaseTeamMemberMapper.listTeamMembers(List.of(game.getCamp1TeamId(), game.getCamp2TeamId()));
        if (CollectionUtils.isEmpty(members)) {
            return;
        }


        String key = makeKey(attr, String.format(START_GAME_NOTICE, game.getId()));
        String smsKey = makeKey(attr, String.format(START_GAME_SMS, DateFormatUtils.format(now, com.yy.gameecology.common.utils.DateUtil.PATTERN_TYPE2)));
        boolean set1 = false, set2 = false;
        Map<Long, List<AovGameInfo>> matchMap = new HashMap<>(2);
        for (AovPhaseTeamMember member : members) {
            long uid = member.getMemberUid();
            boolean put = redisTemplate.opsForHash().putIfAbsent(key, String.valueOf(uid), "1");
            if (!put) {
                continue;
            }

            set1 = true;

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getStartGameMsg(), attr.getImMsgLink());
            sendStartGameNotice(attr, uid, game.getId());
            long teamId = member.getTeamId();
            List<AovGameInfo> matches = matchMap.get(teamId);
            matches = sendStartGameGameList(attr, uid, member.getPhaseId(), teamId, now, matches);
            if (!matchMap.containsKey(teamId)) {
                matchMap.put(teamId, matches);
            }

            put = redisTemplate.opsForHash().putIfAbsent(smsKey, String.valueOf(uid), "1");
            if (!put) {
                continue;
            }

            set2 = true;
            userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getStartGameSms(), attr.getSmsSecret(), attr.getSmsAppid());

        }

        if (set1) {
            redisTemplate.expire(key, 3, TimeUnit.HOURS);
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendTeamLayerShow(attr.getActId(), game.getCamp1TeamId()));
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendTeamLayerShow(attr.getActId(), game.getCamp2TeamId()));
        }

        if (set2) {
            redisTemplate.expire(smsKey, 2, TimeUnit.DAYS);
        }

    }

    private void sendTeamLayerShow(long actId, long teamId) {
        AovPhaseTeam team = aovPhaseTeamMapper.selectById(teamId);
        if (team == null) {
            return;
        }

        JyLayerPushEvent jyLayerPushEvent = new JyLayerPushEvent();
        jyLayerPushEvent.setProducerSeqID(UUID.randomUUID().toString());
        jyLayerPushEvent.setProducerTime(System.currentTimeMillis() / 1000);
        jyLayerPushEvent.setEventType(1); //通知类型 1-子频道广播 2-uid单播通知
        jyLayerPushEvent.setFromService(actId + "-bro");
        jyLayerPushEvent.setFromIP(SystemUtil.getIp());
        jyLayerPushEvent.setSid(team.getSid());
        jyLayerPushEvent.setSsid(team.getSsid());
        jyLayerPushEvent.setStatus(1); //1 -打开 2 -关闭
        jyLayerPushEvent.setActivityID(actId); //
        kafkaService.sendJiaoyouLayerKafka(jyLayerPushEvent);
        log.info("sendTeamLayerShow with teamId:{} sid:{} ssid:{}", teamId, team.getSid(), team.getSsid());
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(cron = "35 7 4 * * ?")
    public void cleanAppPopupRecord() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);

            Date now = commonService.getNow(actId);
            LocalDate today = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate expiredDate = today.minusDays(attr.getAppPopupDays() + 10);
            int rs = aovPopupRecordMapper.deleteExpiredRecords(expiredDate);
            log.info("cleanAppPopupRecord with expiredDate:{} rs:{}", expiredDate, rs);
        }
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(cron = "14 0 10 * * ?")
    public void sendStatisticNotice() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            Date now = commonService.getNow(actId);

            timerSupport.work("sendStatisticNotice:" + actId, 60, () -> {
                log.info("sendStatisticNotice with actId:{}", actId);
                aovPushService.sendStatisticNotice(attr, now);
            });
        }
    }

    @GetMapping("subscribe")
    public Response<?> subscribe(@RequestParam(name = "actId") long actId,
                                 @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                 @RequestParam(name = "phaseId", required = false, defaultValue = "0") long phaseId) {
        AovPushComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(500, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        if (phaseId <= 0) {
            Date now = commonService.getNow(actId);
            AovPhase phase = phaseMapper.selectProcessingPhase(now, (long) actId, null);
            if (phase == null) {
                return Response.fail(400, "当前不可以预约");
            }

            phaseId = phase.getId();
        }

        AovPhaseSubscribe subscribe = aovPhaseSubscribeMapper.selectUserSubscribe(actId, phaseId, uid);
        if (subscribe != null) {
            return Response.ok();
        }

        AovPhaseSubscribe record = new AovPhaseSubscribe();
        record.setPrevActId(actId);
        record.setPrevPhaseId(phaseId);
        record.setUid(uid);
        record.setState(0);
        record.setCreateTime(new Date());

        aovPhaseSubscribeMapper.insertSelective(record);

        return Response.ok();
    }

    @GetMapping("getAppPopupUrl")
    public Response<HomePopupVo> getAppPopupUrl(@RequestParam(name = "actId") long actId,
                                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                @RequestParam(name = "uid") long uid) {
        AovPushComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        log.info("getAppPopupUrl with uid:{}", uid);
        Date now = commonService.getNow(actId);
        AovPhase phase = phaseMapper.selectProcessingPhase(now, actId, AovConst.PhaseState.INIT);
        if (phase == null) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        if (now.before(phase.getSignupStartTime()) || now.after(phase.getSignupEndTime())) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        AovPhaseTeamMember member = aovPhaseTeamMemberMapper.selectByUniq(phase.getId(), uid);
        if (member != null) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        LocalDate today = now.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        int popup = aovPopupRecordMapper.countByDate(actId, uid, today);
        if (popup > 0) {
            return Response.success(new HomePopupVo(attr.getAppPopupUrl()));
        }

        LocalDate startDate = today.minusDays(attr.getAppPopupDays());

        int popupTimes = aovPopupRecordMapper.countByDates(actId, uid, startDate, today);
        if (popupTimes >= attr.getAppPopupTimes()) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        AovPopupRecord popupRecord = new AovPopupRecord();
        popupRecord.setUid(uid);
        popupRecord.setPopupDate(today);
        popupRecord.setCreateTime(now);
        int rs = aovPopupRecordMapper.insert(popupRecord);
        log.info("getAppPopupUrl insert popup record with uid:{} today:{} rs:{}", uid, today, rs);

        return Response.success(new HomePopupVo(attr.getAppPopupUrl()));
    }

    @GetMapping("appPopup")
    public Response<?> appPopup(@RequestParam(name = "actId") int actId,
                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {
        return Response.ok();
    }

    @GetMapping("statistic")
    public Response<?> getStatisticData(@RequestParam(name = "actId") long actId,
                                                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {

        AovPushComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid != 50018033) {
            return Response.fail(403, "unsupported");
        }

        aovPushService.sendStatisticNotice(attr, commonService.getNow(actId));
        return Response.ok();
    }

    public void sendRejectedNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendRejectedNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getRejectedMsg(), attr.getImMsgLink());
            log.info("sendRejectedNotice done with uid:{}", uid);
        });
    }

    public void sendNewApplyNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendNewApplyNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getNewApplyMsg(), attr.getApproveLink());
            log.info("sendNewApplyNotice done with uid:{}", uid);
        });
    }

    public void sendApprovedNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendApprovedNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getApprovedMsg(), attr.getImMsgLink());
            userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getApprovedSms(), attr.getSmsSecret(), attr.getSmsAppid());
            log.info("sendApprovedNotice done with uid:{}", uid);
        });
    }

    public void sendKickedOutNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendKickedOutNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getKickedOutMsg(), attr.getImMsgLink());
            if (StringUtils.isNotEmpty(attr.getKickedOutSms())) {
                userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getKickedOutSms(), attr.getSmsSecret(), attr.getSmsAppid());
            }

            log.info("sendKickedOutNotice done with uid:{}", uid);
        });
    }

    public void sendDisbandTeamNotice(long actId, List<Long> memberUids) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendDisbandTeamNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), memberUids, attr.getImMsgTitle(), attr.getDisbandMsg(), attr.getImMsgLink());
            if (StringUtils.isNotEmpty(attr.getDisbandSms())) {
                memberUids.forEach(uid -> userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getKickedOutSms(), attr.getSmsSecret(), attr.getSmsAppid()));
            }

            log.info("sendDisbandTeamNotice done with uid:{}", memberUids);
        });
    }

    public void sendQuitTeamNotice(long actId, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            AovPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.error("sendQuitTeamNotice fail component not exist");
                return;
            }

            doSendNotice(attr.getImAppId(), attr.getImSenderUid(), uid, attr.getImMsgTitle(), attr.getQuitTeamMsg(), attr.getImMsgLink());
            if (StringUtils.isNotEmpty(attr.getQuitTeamSms())) {
                userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getQuitTeamSms(), attr.getSmsSecret(), attr.getSmsAppid());
            }

            log.info("sendQuitTeamNotice done with uid:{}", uid);
        });
    }

    private void doSendNotice(int appid, long senderUid, long receiveUid, String title, String msg, String link) {
        imMessageServiceClient.sendZhuiwanIMPush(appid, senderUid, Collections.singletonList(receiveUid), title, msg, StringUtils.EMPTY, link);
    }

    private void doSendNotice(int appid, long senderUid, List<Long> receiveUids, String title, String msg, String link) {
        imMessageServiceClient.sendZhuiwanIMPush(appid, senderUid, receiveUids, title, msg, StringUtils.EMPTY, link);
    }

    private void sendStartGameNotice(AovPushComponentAttr attr, long uid, long gameId) {
        JSONObject json = new JSONObject(3);
        json.put("gameId", gameId);
        String noticeValue = json.toJSONString();

        // 发单播
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.AOV_START_GAME_NOTICE)
                .setNoticeValue(noticeValue);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.unicastUid(uid, msg);
        log.info("sendStartGameNotice with uid:{} noticeValue:{}", uid, noticeValue);
    }


    /**
     * ugc房间公屏消息通知
     */
    public void sendRoomNotice(long actId, String seq, final String text, long uid, long sid, long ssid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            String textContent = text.replace("{uid}", Convert.toString(uid));

            PublicScreenNotifyEvent event = kafkaService.buildPublicScreenNotifyEvent(actId, seq, textContent, ZhuiwanScreenNotifyType.SUB_CHANNEL, uid, sid, ssid, 0, Lists.newArrayList());
            kafkaService.sendZhuiwanPublicScreenNotifyEvent(event);
        });
    }

    public void sendJoinTeamIm(AovPushComponentAttr pushAttr, long uid) {
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            if (pushAttr == null) {
                log.error("sendJoinTeamIm fail component not exist");
                return;
            }
            imMessageServiceClient.sendZhuiwanActIMPush(pushAttr.getImAppId(), pushAttr.getImSenderUid(), Collections.singletonList(uid), pushAttr.getImMsgTitle(), pushAttr.getJoinTeamIm(), StringUtils.EMPTY, pushAttr.getImMsgLink());
            log.info("sendJoinTeamIm done with uid:{}", uid);
        });
    }

    private List<AovGameInfo> sendStartGameGameList(AovPushComponentAttr attr, long uid, long phaseId, long teamId, Date now, List<AovGameInfo> matches) {
        if (matches == null) {
            matches = aovGameService.queryMatches(phaseId, teamId, now);
        }
        String noticeValue = JSON.toJSONString(matches);

        // 发单播
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.AOV_FRESH_GAME_LIST_NOTICE)
                .setNoticeValue(noticeValue);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.unicastUid(uid, msg);
        log.info("sendStartGameGameList with uid:{} noticeValue:{}", uid, noticeValue);

        return matches;
    }

    @Getter
    @Setter
    public static class HomePopupVo {
        protected String popupLink;

        public HomePopupVo() {
        }

        public HomePopupVo(String popupLink) {
            this.popupLink = popupLink;
        }
    }
}
