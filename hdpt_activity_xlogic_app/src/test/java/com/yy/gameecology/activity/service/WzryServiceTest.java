package com.yy.gameecology.activity.service;

import com.google.common.collect.ImmutableMap;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.activity.dao.mysql.WzryDao;
import com.yy.gameecology.activity.service.wzry.WzryGameInitService;
import com.yy.gameecology.activity.service.wzry.WzryGameService;
import com.yy.gameecology.activity.service.wzry.WzryGameSettleService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.history.WzryGameComponent;
import com.yy.gameecology.hdzj.element.history.WzryTaskComponent;
import com.yy.gameecology.hdzj.element.component.attr.WzryTaskComponentAttr;
import com.yy.gameecology.hdzj.utils.ZhuiyaClientUtils;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import org.eclipse.jetty.util.ajax.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-01-22 9:58
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties",
        "classpath:env/local/group-setting-7.properties",
        "classpath:env/local/application-inner.properties"})
public class WzryServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "7");
    }

    private final static long ACT_ID = 2024017001;

    @Autowired
    private WzryGameComponent wzryGameComponent;

    @Autowired
    private WzryGameService wzryGameService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Autowired
    private WzryGameSettleService wzryGameSettleService;

    @Autowired
    private WzryGameInitService wzryGameInitService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private WzryDao wzryDao;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private WzryTaskComponent wzryTaskComponent;

    //自动创建赛事
    @Test
    public void autoCreateGameTest() {
        wzryGameComponent.autoCreateGame(wzryGameComponent.getUniqueComponentAttr(ACT_ID), "20240201");
    }

    //自动创建赛事
    @Test
    public void autoCreateGameTest2() {
        wzryGameComponent.autoCreateGame(wzryGameComponent.getUniqueComponentAttr(ACT_ID));
    }


    //赛宝自动初始化
    @Test
    public void autoInitSbGame() {
        Date signUpBefore = DateUtil.addMinutes(DateUtil.getDate("2024-02-01 17:00:00"), 10);
        wzryGameInitService.autoInitSbGame(ACT_ID, signUpBefore, Const.TEN);
    }


    //----赛事流程用户操作
    @Test
    public void joinGameTest() {
        var attr = wzryGameComponent.getUniqueComponentAttr(ACT_ID);
        wzryGameService.joinGame(attr, 50042952L, "yomi", "", "", 1, "20240201170000_5V5");
    }

    @Test
    public void mulJoinGameTest() {
        var attr = wzryGameComponent.getUniqueComponentAttr(ACT_ID);


        long uidStart = 50042952L;
        for (int i = 0; i < 50; i++) {
            long uid = uidStart + i;
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(() -> {
                wzryGameService.joinGame(attr, uid, "yomi", "", "", 1, "20240315220000_5V5");
            });
        }
        SysEvHelper.waiting(1000*20);
    }

    @Test
    public void addPiaoTest(){
        WzryTaskComponentAttr attr = wzryTaskComponent.getUniqueComponentAttr(ACT_ID);
        ZhuiyaPbCommon.Client client = ZhuiyaClientUtils.toClient("", "", "", 0, "", "");
        long uidStart = 50042952L;
        for (int i = 0; i < 50; i++) {
            long uid = uidStart + i;
            wzryTaskComponent.doWelfarePiao(attr, uid, 1000, "手工新增", commonService.getNow(ACT_ID), client);
        }
    }

    @Test
    public void queryGameListTest() {
        wzryGameComponent.queryGameList(ACT_ID, 50042952L, 0, 5);
    }

    @Test
    public void myGameTest() {
        Date startTimeStart = DateUtil.addMinutes(commonService.getNow(ACT_ID), -20);
        Date startTimeEnd = DateUtil.addMinutes(commonService.getNow(ACT_ID), 20);
        wzryGameService.getWzryMyGameVo(ACT_ID, wzryGameComponent.getUniqueComponentAttr(ACT_ID), startTimeStart, startTimeEnd, 50042952L);
    }

    @Test
    public void joinGameHistory() {
        var attr = wzryGameComponent.getUniqueComponentAttr(ACT_ID);
        wzryGameService.getWzryMyGameCloseGame(attr, ACT_ID, 50042952L, 0, 0);
    }

    @Test
    public void subscribe() {
        wzryGameComponent.subscribe(ACT_ID, "20240201170000_5V5", 50042952L);
    }

    @Test
    public void queryGameTest() {
        wzryGameService.queryWzryMyGameVo(ACT_ID, "", 1454054224L, 1454054224L, 50042952L);
        wzryGameService.queryWzryMyGameVo(ACT_ID, "20240201170000_5V5", 1454054224L, 1454054224L, 50042952L);
    }

    @Test
    public void jumpGame() {
        wzryGameService.getJumpGame(ACT_ID, "20240201170000_5V5", 50042952L);
    }

    //--赛事结算分配队伍
    @Test
    public void autoAllocationGameTeam() {
        log.info("begin autoInitSbGame game,actId:{}", ACT_ID);
        //报名结束10分钟后，检查赛宝赛事有无开启，如无开启则关闭赛事
        Date now = commonService.getNow(ACT_ID);
        wzryGameInitService.autoAllocationGameTeam(ACT_ID, now, 10);
    }


    //---赛事结算1
    @Test
    public void cancelSbTimeOutGame() {
        log.info("begin cancelSbTimeOutGame game,actId:{}", ACT_ID);
        //报名结束10分钟后，检查赛宝赛事有无开启，如无开启则关闭赛事
        Date now = commonService.getNow(ACT_ID);
        Date signUpEndTimeEnd = DateUtil.addMinutes(now, -10);
        wzryGameSettleService.cancelSbTimeOutGame(wzryGameComponent.getUniqueComponentAttr(ACT_ID), ACT_ID, now, signUpEndTimeEnd, 1000);
    }

    //---赛事结算2
    @Test
    public void cancelWzryGame() {
        wzryGameSettleService.cancelWzryTimeOutGame(wzryGameComponent.getUniqueComponentAttr(ACT_ID), commonService.getNow(ACT_ID), 10, 10);
    }

    //---赛事结算3
    @Test
    public void dealGameOverGame() {
        wzryGameSettleService.closeGameOverList(wzryGameComponent.getUniqueComponentAttr(ACT_ID), 10);
    }


    @Test
    public void querySbRoom() {
        saiBaoClient.queryRoom("2024020600011984", 1023722532);
    }


    @Test
    public void closeGame() {
        saiBaoClient.closeRoom("10084143");
    }

    @Test
    public void sendKakaTest() {

    }

    @Test
    public void querySbRoomTest(){
         saiBaoClient.queryRoom("10128011",1023722532);
    }

    @Test
    public void querySbGameTest(){
        wzryGameSettleService.queryGameResult("10154315");
    }

    @Test
    public void awardTest() {
        String seq = System.currentTimeMillis() + "";
        Map<String, String> ext = ImmutableMap.of(Const.AWARD_ISSUE_EXT, JSON.toString(ImmutableMap.of("desc", "比赛胜利赏金奖励")));
        hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), 200, 50042952, 70057, 3000, 15387, seq, ext);
    }

    @Test
    public void queryTiemTest(){
        wzryDao.getRecentlyStartGame(ACT_ID,new Date());
    }

    @Test
    public void reportStatis(){
        wzryGameComponent.doStaticReport(ACT_ID,commonService.getNow(ACT_ID),wzryGameComponent.getUniqueComponentAttr(ACT_ID));
    }

}
