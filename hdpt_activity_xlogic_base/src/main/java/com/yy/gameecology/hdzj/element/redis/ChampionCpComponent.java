package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.champion.CpShowAwardInfoVo;
import com.yy.gameecology.activity.bean.champion.CpTopRankInfoVo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.rank.RankItemUserAnchor;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.activity.service.rankext.RankExtHandler;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ChampionCpComponentAttr;

import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.ChampionCpShows;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 夏季冠军杯挚爱CP玩法
 */
@UseRedisStore
@RestController
@RequestMapping("/cmpt/championCp")
@Component
public class ChampionCpComponent extends BaseActComponent<ChampionCpComponentAttr> implements RankExtHandler , LayerSupport {



    private final static String CP_TOP_SHOW = "cp_top_show:%d:%d";


    private static final String CP_SHOW_AWARD_LIST_KEY = "cp_show_award:";
    private static final String CP_SHOW_HISTORY_TOPONE_LIST_KEY = "cp_show_history_top1";

    private final static int TOP_ONE = 1;

    private static final long TOPONE_BRO_BANNER_ID = 5087001L;

    private static final int TOP_APP_BANNER_CONTENT_TYPE = 6;


    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private UserInfoService userInfoService;

    @Override
    public List<String> supportKeys() {
        return null;
    }



    @Override
    public Long getComponentId() {
        return ComponentId.CHAMPION_CP;
    }


    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void handlePhaseTimeEnd(PhaseTimeEnd event, ChampionCpComponentAttr attr) {
        log.info("ChampionCpComponent  handlePhaseTimeEnd event:{},attr:{}",JsonUtil.toJson(event),JsonUtil.toJson(attr));
        if (event.getRankId() != attr.getRankId()) {
            return;
        }
        if (event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), timeCode, attr.getShowLotteryTopN(), null);
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("ChampionCpComponent empty rank,actId:{},rankId:{},phaseId:{},timeCode:{}", event.getActId(), event.getRankId(), event.getPhaseId(), timeCode);
            return;
        }


        for (Rank rank : ranks) {
            releaseaward(attr,timeCode,rank,event);
        }

    }


    private void releaseaward(ChampionCpComponentAttr attr,String timeCode,Rank rank,PhaseTimeEnd event) {

        log.info("ChampionCpComponent  releaseaward event:{},attr:{},timeCode:{},Rank:{}",JsonUtil.toJson(event),JsonUtil.toJson(attr),timeCode,JsonUtil.toJson(rank));

        List<ChampionCpShows> showConfigs = attr.getShowConfigs();

        String[] members = rank.getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long babyUid =  Long.parseLong(members[1]);
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        if(rank.getRank()<=attr.getShowLotteryTopN()) {
            String lotteryrHashSeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_" + attr.getRankId() + "_" + attr.getPhaseId() + "_" + timeCode + "_" + userUid + "_" + babyUid + "_" + rank.getRank()));
            BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(lotteryrHashSeq, attr.getBusiId(), userUid, attr.getTopNLotteryTaskId(), 3);
            if (result.getCode() != 0) {
                log.error("ChampionCpComponent lottery error uid:{}, ret:{}",userUid, JsonUtil.toJson(result));
                throw new RuntimeException("抽奖失败 actId:"+attr.getActId());
            }
            Map<Long, Long> recordIds = result.getRecordPackages();
            final long lotteryPackageId = recordIds.values().stream().findFirst().orElse(0L);
            log.info("ChampionCpComponent uid:{},lotteryPackageId:{},actId:{}",userUid,lotteryPackageId,attr.getActId());
            ChampionCpShows show = showConfigs.stream().filter(v->v.getLotteryTaskId()==attr.getTopNLotteryTaskId() && v.getLotteryPackageId()==lotteryPackageId).findFirst().orElse(null);
            if(show==null){
                log.error("ChampionCpComponent releaseaward error show is null,actId:{},userUid:{},babyUid{},timeCode:{}",attr.getActId(),userUid,babyUid,timeCode);
                return;
            }

            String userHashSeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_" + attr.getRankId() + "_" + attr.getPhaseId() + "_" + timeCode + "_" + userUid + "_" + rank.getRank()));
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), userUid, show.getTaskId(), 1, show.getPackageId(), userHashSeq, 2);
            boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if(!suc) {
                // 需要人工介入处理
                log.error("ChampionCpComponent user doWelfare error,  actId:{}, uid:{}, seq:{}  ret:{}", attr.getActId(), userUid, userHashSeq, batchWelfareResult);
            }

            if(userUid!=babyUid){
                String babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_" + attr.getRankId() + "_" + attr.getPhaseId() + "_" + timeCode + "_" + babyUid + "_" + rank.getRank()));
                batchWelfareResult =  hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), babyUid, show.getTaskId(), 1, show.getPackageId(), babyHashSeq, 2);
                suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
                if(!suc) {
                    // 需要人工介入处理
                    log.error("ChampionCpComponent  baby doWelfare error,  actId:{}, uid:{}, seq:{} ret:{}", attr.getActId(), babyUid, babyHashSeq, batchWelfareResult);
                }
            }

            //保存cp榜单对应的入场秀
            saveCpRankTopnShow(attr,userUid,babyUid,timeCode,show.getUrl(),show.getTaskId(),show.getPackageId());

            //插入获奖记录
            addUserAwardRecord(attr,event,Lists.newArrayList(userUid,babyUid),show.getUrl(),rank.getRank(),show.getTaskId(),show.getPackageId());

            if(rank.getRank()<=TOP_ONE){
                //插入历史topone
                addHistoryTopOneList( attr, userUid, babyUid, rank, timeCode);

                //TOP1CP动画
                Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(userUid,babyUid), com.yy.gameecology.common.bean.Template.makefriend);
                UserInfoVo userInfoVo = userInfoVoMap.get(userUid);
                UserInfoVo babyInfoVo = userInfoVoMap.get(babyUid);
                Map<String, String> ext = Maps.newHashMap();
                ext.put("svgaUrl",attr.getTop1SvgaUrl());
                if(userInfoVo!=null){
                    ext.put("userlogo",userInfoVo.getAvatarUrl());
                    ext.put("usernick",userInfoVo.getNick());
                }

                if(babyInfoVo!=null) {
                    ext.put("babyLogo",babyInfoVo.getAvatarUrl());
                    ext.put("babnick",babyInfoVo.getNick());
                }

                log.info("ChampionCpComponent commonBannerBroadcast ext:{}",JsonUtil.toJson(ext));
                commonBroadCastService.commonBannerBroadcast(0, 0, 0, Template.findByValue(attr.getBroTemplate()), 4
                        , attr.getActId(), 0L,0L, TOPONE_BRO_BANNER_ID, 0L, ext);

                //TOP1发送app cp特效
                broTopOneCpApp( userHashSeq, attr, userInfoVoMap, userUid, babyUid);
            }


        }

    }

    private void broTopOneCpApp(String seq,ChampionCpComponentAttr attr,Map<Long, UserInfoVo> userInfoVoMap,long userUid,long babyUid) {

        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        svgaConfig.setDuration(13);
        svgaConfig.setSvgaURL(attr.getAppTop1SvgaUrl());

        List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, userInfoVoMap, userUid,babyUid);
        svgaConfig.setContentLayers(broContentLayers);

        //头像
        svgaConfig.setImgLayers(buildSvgaImageConfig(attr,userInfoVoMap,userUid,babyUid));


        final int bcType = FstAppBroadcastType.ALL_TEMPLATE;
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getAppBannerBusiId(),
                bcType, 0, 0, "",
                Lists.newArrayList());

        appBannerEvent.setContentType(TOP_APP_BANNER_CONTENT_TYPE);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setSvgaConfig(svgaConfig);
        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("broTopOneCpApp app done seq:{}, userUid:{} ,babyUid:{},event:{}",  seq, userUid,babyUid, JSON.toJSONString(appBannerEvent));
    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(ChampionCpComponentAttr attr,Map<Long, UserInfoVo> userInfoVoMap,long userUid,long babyUid) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        Map<Integer, BannerSvgaTextConfig> textMap = attr.getSvgaText();

        for (Integer userType : textMap.keySet()) {
            Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
            BannerSvgaTextConfig textConfig = attr.getSvgaText().get(userType);
            if (textConfig == null) {
                continue;
            }
            AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
            //配置动态替换文本
            UserInfoVo userInfoVo = userType==1? userInfoVoMap.get(userUid) : userInfoVoMap.get(babyUid);
            String text = contextReplace(textConfig.getText(),userInfoVo,textConfig.getNameCountLimit());
            appBannerSvgaText.setText(text);
            appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
            appBannerSvgaText.setGravity(textConfig.getGravity());
            if (StringUtil.isNotBlank(textConfig.getImages())) {
                appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
            }
            if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
            }
            broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

            if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                broContentLayers.add(broSvgaTextLayer);
            }

        }

        return broContentLayers;

    }
    private String contextReplace(String context,UserInfoVo userInfoVo,int nameCountLimit) {
        if(userInfoVo!=null){
            context = context.replace("{nick}",userInfoVo.getNick());
        }else{
            context = StringUtil.EMPTY;
        }

        return context;
    }

    private List<Map<String, String>> buildSvgaImageConfig(ChampionCpComponentAttr attr,Map<Long, UserInfoVo> userInfoVoMap,long userUid,long babyUid) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }

        Map<Integer, String> imageMap = attr.getSvgaImgLayers();
        UserInfoVo userInfoVo = userInfoVoMap.get(userUid);
        UserInfoVo babyInfoVo = userInfoVoMap.get(babyUid);
        for (Integer userType : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String imageKey = imageMap.get(userType);
            String image = StringUtil.EMPTY;
            if(userType==1){
                image = userInfoVo!=null ? userInfoVo.getAvatarUrl() :StringUtil.EMPTY ;
            }else{
                image = babyInfoVo!=null ? babyInfoVo.getAvatarUrl() :StringUtil.EMPTY ;
            }
            broImgLayer.put(imageKey, image);
            broImgLayers.add(broImgLayer);
        }

        return broImgLayers;


    }

    /**
     * 添加历史挚爱cp top1
     */
    private void addHistoryTopOneList(ChampionCpComponentAttr attr,long userUid,long babyUid,Rank rank,String timeCode) {
        String redisCode = getRedisGroupCode(attr.getActId());
        CpTopRankInfoVo item = new CpTopRankInfoVo();
        item.setUserUid(userUid);
        item.setBabyUid(babyUid);
        item.setGiftCount(rank.getScore());
        item.setRankTimeCode(timeCode);

        String finishTaskKey = buildHistoryTopKey(attr);
        String finishTaskSeq = makeKey(attr, "seq:historyTop:" + timeCode);
        actRedisDao.lPushWithSeq(redisCode, finishTaskSeq, finishTaskKey, JSON.toJSONString(item), DateUtil.TWO_MONTH_SECONDS);
    }

    private String buildHistoryTopKey(ChampionCpComponentAttr attr) {
        return makeKey(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), CP_SHOW_HISTORY_TOPONE_LIST_KEY);
    }


    /**
     * 添加获奖记录 主持-神豪uid一致只插入一次
     */
    private void addUserAwardRecord(ChampionCpComponentAttr attr,PhaseTimeEnd event,List<Long> uidList,String showUrl,int rank,long taskId,long packageId) {
        Set<Long> uidSet = new HashSet<>(uidList);
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));
        String redisCode = getRedisGroupCode(attr.getActId());
        for(long uid : uidSet) {
            CpShowAwardInfoVo  item = new CpShowAwardInfoVo();

            long startTime = DateUtil.getDayOf15MinuteInterval(DateUtil.getDate(event.getEndTime())).getTime();
            long endTime =  DateUtil.addMinutes(DateUtil.getDayOf15MinuteInterval(DateUtil.getDate(event.getEndTime())),15).getTime();
            item.setMember(String.valueOf(uid));
            item.setAwardStartTime(startTime);
            item.setAwardEndTime(endTime);
            item.setTaskId(taskId);
            item.setPackageId(packageId);
            String finishTaskKey = buildCpAwardListKey(attr,String.valueOf(uid));
            String finishTaskSeq = makeKey(attr, "seq:cpAward:" + timeCode + ":" + rank + ":"+uid);
            actRedisDao.lPushWithSeq(redisCode, finishTaskSeq, finishTaskKey, JSON.toJSONString(item), DateUtil.TWO_MONTH_SECONDS);
        }

    }

    private String buildCpAwardListKey(ChampionCpComponentAttr attr,String member) {

        return makeKey(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), CP_SHOW_AWARD_LIST_KEY + member);
    }


    /**
     * 保存cp榜单对应的入场秀
     */
    private void saveCpRankTopnShow(ChampionCpComponentAttr attr,long userUid,long babyUid,String timeCode,String showUrl,long taskId,long packageId) {

        String key = buildTopShowKey(attr,userUid,babyUid);
        String value = taskId + "_" + packageId;
        actRedisDao.hset(getRedisGroupCode(attr.getActId()),key, timeCode,value);
    }

    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        ChampionCpComponentAttr attr = tryGetUniqueComponentAttr(rankReq.getActId());

        if (attr == null || CollectionUtils.isEmpty(objectList)) {
            return objectList;
        }

        //挚爱CP榜需展示入场秀图标，礼物图片
        if(attr.getRankId()==rankReq.getRankId()) {

            List<ChampionCpShows> cpShowConfig = attr.getShowConfigs();
            Map<String,ChampionCpShows> cpShowMap = cpShowConfig.stream().collect(Collectors.toMap(v-> v.getTaskId()+"_"+v.getPackageId(), Function.identity()));

            objectList.stream().map(rank -> ((RankItemUserAnchor) rank)).forEach(rank->{
                if (rank.getViewExt() == null) {
                    rank.setViewExt(new HashMap<>(2));
                }
                rank.getViewExt().put("cpGiftUrl",attr.getGiftUlr());
            });

            for (int i = 0; i < objectList.size() && i < attr.getShowLotteryTopN(); i++) {
                RankItemUserAnchor rankItem = (RankItemUserAnchor) objectList.get(i);
                if (rankItem.getViewExt() == null) {
                    rankItem.setViewExt(new HashMap<>(2));
                }
                if(rankItem.getUserRankItem()!=null && rankItem.getBabyRankItem()!=null) {
                    String key = buildTopShowKey(attr,rankItem.getUserRankItem().getUid(),rankItem.getBabyRankItem().getUid());
                    String awardInfo = actRedisDao.hget(getRedisGroupCode(rankReq.getActId()),key, rankReq.getDateStr());
                    String cpShowUrl =  getCpShow(rankItem.getRank(),awardInfo, cpShowMap, attr);
                    rankItem.getViewExt().put("cpShow",cpShowUrl);
                }

            }

        }

        return objectList;
    }

    private String getCpShow(int rank,String awardPackageInfo,Map<String,ChampionCpShows> cpShowMap,ChampionCpComponentAttr attr) {
        if(StringUtil.notEmpty(awardPackageInfo)) {
            ChampionCpShows showConfig = cpShowMap.get(awardPackageInfo);
            return showConfig!=null ? showConfig.getUrl() : StringUtil.EMPTY;
        }else{
            return StringUtil.EMPTY;
        }

    }

    /**
     * 我的获奖记录
     * @return
     */
    @GetMapping("/queryShowAwardRecord")
    public Response<List<CpShowAwardInfoVo>>  queryShowAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                                                   @RequestParam(name = "actId") long actId,
                                                                   @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {
        long loginUid = getLoginYYUid(request, response);
        if (loginUid <= 0) {
            return Response.fail(401, "未登录！");
        }
        ChampionCpComponentAttr attr = getComponentAttr(actId,cmptUseInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }
        String redisCode = getRedisGroupCode(attr.getActId());
        String finishTaskKey = buildCpAwardListKey(attr,String.valueOf(loginUid));
        List<CpShowAwardInfoVo> list = Lists.newArrayList();

        List<String> awardRecord = actRedisDao.lrange(redisCode, finishTaskKey, 0, 1000);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            List<ChampionCpShows> cpShowConfig = attr.getShowConfigs();
            Map<String,ChampionCpShows> cpShowMap = cpShowConfig.stream().collect(Collectors.toMap(v-> v.getTaskId()+"_"+v.getPackageId(), Function.identity()));

            for (String item : awardRecord) {
                CpShowAwardInfoVo record = JSON.parseObject(item, CpShowAwardInfoVo.class);
                String showUrl = getUserAwardShow( cpShowMap, attr,record.getTaskId(),record.getPackageId());
                record.setShowUrl(showUrl);
                list.add(record);
            }
        }

        return Response.success(list);

    }

    private String getUserAwardShow(Map<String,ChampionCpShows> cpShowMap,ChampionCpComponentAttr attr,long taskId,long packageId) {
            String awardPackageInfo = taskId + "_" + packageId;
            ChampionCpShows showConfig = cpShowMap.get(awardPackageInfo);
            return showConfig!=null ? showConfig.getUrl() : StringUtil.EMPTY;

    }

    private String buildTopShowKey(ChampionCpComponentAttr attr, long userUid,long babyUId ) {
        return makeKey(attr, String.format(CP_TOP_SHOW, userUid,babyUId));
    }


    @Override
    public long getActId() {
        return ComponentId.CHAMPION_CP;
    }

    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        // 过滤
        if (!LayerItemTypeKey.CMPT_ITEM2.equals(layerMemberItem.getItemType())) {
            return ext;
        }

        ChampionCpComponentAttr attr = tryGetUniqueComponentAttr(actId);
        String timeCode = DateUtil.format(DateUtil.getDayOf15MinuteInterval(commonService.getNow(actId)), DateUtil.PATTERN_TYPE9);

        //填充定制信息
        if(layerMemberItem.getCurPhaseInfo()==null){
            PhaseInfo phaseInfo = new PhaseInfo();
            layerMemberItem.setCurPhaseInfo(phaseInfo);
        }
        layerMemberItem.getCurPhaseInfo().setNameShow("挚爱CP");
        layerMemberItem.setNickName("当前TOP1");

        Date now = commonService.getNow(attr.getActId());
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId,  attr.getRankId(), now,attr.getPhaseId());
        PhaseInfo curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, attr.getPhaseId());
        long leftSeconds = actLayerInfoService.getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
        layerMemberItem.setLeftSeconds(leftSeconds);


        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, attr.getRankId(), attr.getPhaseId(), timeCode, 1, null);
        if(ranks.isEmpty()){
            return ext;
        }
        Rank rank = ranks.get(0);
        String[] members = rank.getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long babyUid =  Long.parseLong(members[1]);
        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(userUid,babyUid), com.yy.gameecology.common.bean.Template.makefriend);
        UserInfoVo userInfo = userInfoVoMap.get(userUid);
        UserInfoVo babyInfo = userInfoVoMap.get(babyUid);

        Map<String, Object> extInfo = Maps.newHashMap();
        extInfo.put("userUid", userUid);
        extInfo.put("babyUid", babyUid);
        extInfo.put("giftCount", rank.getScore());
        extInfo.put("giftUrl", attr.getLayerGiftUlr());
        if(userInfo!=null){
            extInfo.put("userlogo",userInfo.getAvatarUrl() );
            extInfo.put("userNick",userInfo.getNick() );
        }

        if(babyInfo!=null){
            extInfo.put("babyLogo",babyInfo.getAvatarUrl() );
            extInfo.put("babyNick",babyInfo.getNick() );
        }


        return extInfo;
    }





    /**
     * 历史挚爱CPTOP1
     * @param request
     * @param response
     * @param actId
     * @param cmptUseInx
     * @return
     */
    @GetMapping("/historyCpTopOneRank")
    public Response<List<CpTopRankInfoVo>>  historyCpTopOneRank(HttpServletRequest request, HttpServletResponse response,
                                                                @RequestParam(name = "actId") long actId,
                                                                @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {


        long loginUid = getLoginYYUid(request, response);

        ChampionCpComponentAttr attr = getComponentAttr(actId,cmptUseInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }
        String redisCode = getRedisGroupCode(attr.getActId());
        String finishTaskKey = buildHistoryTopKey(attr);
        List<CpTopRankInfoVo> list = Lists.newArrayList();
        List<String> historyTopOne = actRedisDao.lrange(redisCode, finishTaskKey, 0, 50);
        if (CollectionUtils.isNotEmpty(historyTopOne)) {
            for (String item : historyTopOne) {
                CpTopRankInfoVo record = JSON.parseObject(item, CpTopRankInfoVo.class);
                list.add(record);
            }
            Set<Long> userUids = list.stream().map(v->v.getUserUid()).collect(Collectors.toSet());
            Set<Long> babyUids =  list.stream().map(v->v.getBabyUid()).collect(Collectors.toSet());
            Set<Long> uidList = new HashSet<>();
            uidList.addAll(userUids);
            uidList.addAll(babyUids);
            Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(uidList), com.yy.gameecology.common.bean.Template.yule);

            for (CpTopRankInfoVo item : list) {
                item.setGiftUrl(attr.getGiftUlr());
                UserInfoVo userInfo = userInfoVoMap.get(item.getUserUid());
                UserInfoVo babyInfo = userInfoVoMap.get(item.getBabyUid());
                if(userInfo!=null) {
                    item.setUserlogo(userInfo.getAvatarUrl());
                    item.setUserNick(userInfo.getNick());
                }
                if(babyInfo!=null){
                    item.setBabyLogo(babyInfo.getAvatarUrl());
                    item.setBabyNick(babyInfo.getNick());
                }
            }

        }


        return Response.success(list);
    }


}
