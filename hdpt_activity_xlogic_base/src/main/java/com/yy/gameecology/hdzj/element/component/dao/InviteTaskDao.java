package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5156UserInviteMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5156UserInvite;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5156UserInviteBind;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5156UserInviteLog;
import com.yy.gameecology.common.utils.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-11 15:44
 **/
@Component
public class InviteTaskDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private Cmpt5156UserInviteMapper cmpt5156UserInviteMapper;


    public long addUserInviteLog(long actId,
                                 long cmptUseInx,
                                 long uid,
                                 String userHdid,
                                 long invitedUid,
                                 String invitedHdid,
                                 int invitedState,
                                 String invitedDesc,
                                 String extJson) {
        Cmpt5156UserInviteLog log = new Cmpt5156UserInviteLog();
        log.setActId(actId);
        log.setCmptUseInx(cmptUseInx);
        log.setUid(uid);
        log.setUserHdid(userHdid);
        log.setInvitedUid(invitedUid);
        log.setInvitedHdid(invitedHdid);
        log.setInvitedState(invitedState);
        log.setInvitedDesc(invitedDesc);
        log.setCreateTime(new Date());
        log.setExtJson(extJson);

        return gameecologyDao.insertWithReturnAutoId(Cmpt5156UserInviteLog.class, log);
    }

    public Cmpt5156UserInviteLog getCmptUserInviteLog(long actId, long id) {
        Cmpt5156UserInviteLog where = new Cmpt5156UserInviteLog();
        where.setActId(actId);
        where.setId(id);
        return gameecologyDao.selectOne(Cmpt5156UserInviteLog.class, where, "");
    }

    public long countUserInviteByUid(long actId, long cmptIndex, long uid) {
        Cmpt5156UserInvite where = new Cmpt5156UserInvite();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setUid(uid);
        return gameecologyDao.count(Cmpt5156UserInvite.class, where, "");
    }

    public Cmpt5156UserInvite getUserInviteByInvitedUid(long actId, long cmptIndex, long uid) {
        Cmpt5156UserInvite where = new Cmpt5156UserInvite();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setInvitedUid(uid);
        return gameecologyDao.selectOne(Cmpt5156UserInvite.class, where, "");
    }

    public Cmpt5156UserInvite getUserInviteByInvitedHdid(long actId, long cmptIndex, String hdid) {
        Cmpt5156UserInvite where = new Cmpt5156UserInvite();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setInvitedHdid(hdid);
        return gameecologyDao.selectOne(Cmpt5156UserInvite.class, where, "");
    }

    public Cmpt5156UserInvite getUserInviteById(long actId, long cmptIndex, long uid, long id) {
        Cmpt5156UserInvite where = new Cmpt5156UserInvite();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setUid(uid);
        where.setId(id);
        return gameecologyDao.selectOne(Cmpt5156UserInvite.class, where, "");
    }

    public List<Cmpt5156UserInvite> getCmptUserInvite(long actId, long cmptIndex, long uid, Integer receiveState) {
        Cmpt5156UserInvite where = new Cmpt5156UserInvite();
        where.setActId(actId);
        where.setCmptUseInx(cmptIndex);
        where.setUid(uid);
        where.setAwardReceiveState(receiveState);
        return gameecologyDao.select(Cmpt5156UserInvite.class, where, " order by id desc ");
    }

    public long getUserInviteAwardAmount(long actId, long cmptIndex, long uid) {
        return Convert.toLong(cmpt5156UserInviteMapper.sumAwardAmountByActIdCmptUseInxAndUid(actId, cmptIndex, uid),0);
    }

    public int saveUserInvite(long actId, long cmptIndex, long uid, long invitedUid, String invitedHdid, String invitedDesc,
                              long awardAmount, int receiveState, int state) {
        Cmpt5156UserInvite data = new Cmpt5156UserInvite();
        data.setActId(actId);
        data.setCmptUseInx(cmptIndex);
        data.setUid(uid);
        data.setInvitedUid(invitedUid);
        data.setInvitedHdid(invitedHdid);
        data.setInvitedDesc(invitedDesc);
        data.setAwardAmount(awardAmount);
        data.setState(state);
        data.setAwardReceiveState(receiveState);
        data.setCreateTime(new Date());
        return gameecologyDao.insert(Cmpt5156UserInvite.class, data);
    }

    public Cmpt5156UserInviteBind getCmptUserInviteBind(long actId, long uid) {
        Cmpt5156UserInviteBind where = new Cmpt5156UserInviteBind();
        where.setActId(actId);
        where.setUid(uid);
        return gameecologyDao.selectOne(Cmpt5156UserInviteBind.class, where, "");
    }

    public long inertIgnoreUserInviteBind(long actId, long cmptUseInx, long uid, String mobileHash, String hdid) {
        Cmpt5156UserInviteBind item = new Cmpt5156UserInviteBind();
        item.setActId(actId);
        item.setCmptUseInx(cmptUseInx);
        item.setUid(uid);
        item.setMobileHash(mobileHash);
        item.setHdid(hdid);
        item.setCreateTime(new Date());
        return gameecologyDao.insert(Cmpt5156UserInviteBind.class, item, Cmpt5156UserInviteBind.TABLE_NAME, true);
    }

    public long updateAwardReceiveState(
            Long id,
            Long actId,
            Long cmptUseInx,
            Long uid,
            Integer currentAwardReceiveState,
            Integer newAwardReceiveState) {
        return cmpt5156UserInviteMapper.updateAwardReceiveState(id, actId, cmptUseInx, uid, currentAwardReceiveState, newAwardReceiveState);
    }

    public List<Cmpt5156UserInviteLog> queryInviteLog(long actId, long uid, long invitedUid, int limit) {
        Cmpt5156UserInviteLog where = new Cmpt5156UserInviteLog();
        if (uid > 0) {
            where.setUid(uid);
        }
        if (invitedUid > 0) {
            where.setInvitedUid(invitedUid);
        }
        where.setActId(actId);
        return gameecologyDao.select(Cmpt5156UserInviteLog.class, where, " order by id desc limit " + limit);
    }

}
