package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.mq.FtsCompereOnlineEvent;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.MqConst;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import com.yy.thrift.hdztranking.RoleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Slf4j
@Component
public class FtsCompereOnlineConsumer {

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    @KafkaListener(containerFactory = "jiaoyouWxKafkaContainerFactory", id = "jy_wx_kafka_compereOnline",
            topics = MqConst.JY_COMPERE_ONLINE,
            groupId = "${kafka.jiaoyou.compere-online.wx.group}")
    public void handleWxEvent(ConsumerRecord<String, String> consumerRecord) {
        onCompereOnline("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "jiaoyouSzKafkaContainerFactory", id = "jy_sz_kafka_compereOnline",
            topics = MqConst.JY_COMPERE_ONLINE,
            groupId = "${kafka.jiaoyou.compere-online.sz.group}")
    public void handleSzEvent(ConsumerRecord<String, String> consumerRecord) {
        onCompereOnline("sz", consumerRecord.value());
    }

    private void onCompereOnline(String from, String payload) {
        log.info("onCompereOnline from:{} payload:{}", from, payload);
        FtsCompereOnlineEvent event = JSON.parseObject(payload, FtsCompereOnlineEvent.class);
        hdzjEventDispatcher.notify(event.getUid(), RoleType.ANCHOR, event, event.getProducerSeqID());
    }
}
