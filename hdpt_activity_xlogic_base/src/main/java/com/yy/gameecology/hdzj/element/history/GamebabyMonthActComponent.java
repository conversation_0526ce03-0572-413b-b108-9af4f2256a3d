package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.RankDataEvent;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRoleId;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.GamebabyMonthActComponentAttr;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import java.time.LocalDateTime;
import java.util.*;


/**
 * 宝贝月度活动组件
 *
 * <AUTHOR>
 * @date 2022.02.11 17:42
 */
@Deprecated
@Slf4j
@Component
public class GamebabyMonthActComponent extends BaseActComponent<GamebabyMonthActComponentAttr> {


    @Autowired
    private KafkaService kafkaService;


    /**
     * 荣耀对决-主播水晶余额
     */
    public static final String HONOR_CRY_BABY_BALANCE = "cry_baby_balance";

    /**
     * 荣耀对决-已发放勋章天数
     */
    public static final String HONOR_CRY_AWARD_MEDAL = "cry_award_medal_days";

    /**
     * 神豪总幸运之钥数 hash
     */
    public static final String PLAYER_TOTAL_LUCK_KEY = "player_total_luck_key";

    /**
     * 神豪已使用的幸运之钥 hash
     */
    public static final String PLAYER_USED_LUCK_KEY = "player_used_luck_key";

    /**
     * 神豪抽奖记录 player_lottery_award:uid {item:count}
     */
    public static final String PLAYER_LOTTERY_AWARD = "player_lottery_award:%s";

    /**
     * 荣耀对决-主播每小时累计赠送活动礼物数量
     */
    public static final String HONOR_GIFT_AMOUNT_KEY = "cry_gift_amount";

    /**
     * 荣耀对决-每小时全局发放荣耀水晶数量
     */
    public static final String HONOR_CRY_AWARD_AMOUNT_KEY = "cry_award_amount";

    @Override
    public Long getComponentId() {
        return ComponentId.GAMEBABY_MONTH_ACT;
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = false)
    public void onSendGiftEvent(SendGiftEvent sendGiftEvent, GamebabyMonthActComponentAttr attr) {
        log.info("onSendGiftEvent with event:{}", sendGiftEvent);
        if (sendGiftEvent.getTemplate().getValue() != Template.Gamebaby.getValue()) {
            return;
        }
        //不在活动时间范围内
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            return;
        }

        Long sendUid = sendGiftEvent.getSendUid();
        Long giftNum = sendGiftEvent.getGiftNum();
        LocalDateTime localDateTime = commonService.getNowDateTime(actId);
        String day = localDateTime.format(DateUtil.YYYY_MM_DD);
        Long anchorUid = sendGiftEvent.getRecvUid();
        String playerUid = String.valueOf(sendUid);
        String giftId = sendGiftEvent.getGiftId();

        long sid = sendGiftEvent.getSignSid() == null ? sendGiftEvent.getSid() : sendGiftEvent.getSignSid();
        //非宝贝的送礼行为
        if (anchorUid.equals(sendUid)) {
            return;
        }
        //非活动礼物
        if (!attr.getGiftId().contains(giftId)) {
            return;
        }
        if (!commonService.checkWhiteList(actId, RoleType.ANCHOR, anchorUid + "")) {
            log.warn("not in white list,return,trans:{}", JSON.toJSONString(sendGiftEvent));
            return;
        }
        if (!commonService.checkWhiteList(actId, RoleType.USER, playerUid + "")) {
            log.warn("not in white list,return,trans:{}", JSON.toJSONString(sendGiftEvent));
            return;
        }


        //送礼累水晶，扣总水晶限额
        try {
            handlerGiftSend(anchorUid, sid, giftNum, attr, giftId);
        } catch (Exception e) {
            log.error("handlerGiftSend error,anchorUid:{},giftNum:{}", anchorUid, giftNum);
        }
        String groupCode = redisConfigManager.getGroupCode(actId);

        long luckKey = giftNum / 520;
        if (luckKey > 0) {
            String key = makeKey(attr, PLAYER_TOTAL_LUCK_KEY);
            actRedisDao.hIncrByKey(groupCode, key, playerUid, luckKey);
        }

    }

    private int getCryLimit(Date now, GamebabyMonthActComponentAttr attr) {
        if (!actInfoService.inActTime(attr.getActId())) {
            return -1;
        }
        int hour = DateUtil.getHours(now);
        if (hour >= attr.getHonorNormalStartHour() && hour <= attr.getHonorNormalEndHour()) {
            return attr.getHonorCryLimitNormalHour();
        } else if (hour >= attr.getHonorHotStartHour() && hour <= attr.getHonorHotEndHour()) {
            return attr.getHonorCryLimitHotHour();
        }

        //不在相关时段
        return -1;
    }


    /**
     * 送礼累水晶，扣总水晶限额
     */
    public void handlerGiftSend(long babyUid, long sid, long amount, GamebabyMonthActComponentAttr attr, String giftId) {

        long actId = attr.getActId();
        String groupCode = redisConfigManager.getGroupCode(actId);
        Date now = commonService.getNow(actId);
        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        if (attr.getFreeGiftId().contains(giftId)) {
            String freeGiftLimitRedisKey = makeKey(attr, "free_gift_limit_" + day);
            List<Long> incResult = actRedisDao.hIncrWithLimit(groupCode, freeGiftLimitRedisKey, String.valueOf(babyUid), amount, attr.getFreeGiftLimit(), true);
            final int two = 2;
            if (incResult.get(0) == two) {
                log.warn("免费礼物:超出限额,部分增加:actId:{},babyUid:{},giftAmount:{},limit{},giftId{},add{}", actId, babyUid, amount, attr.getFreeGiftLimit(), giftId, incResult.get(1));
                amount = incResult.get(1);
            } else if (incResult.get(0) == 1) {
                log.info("免费礼物:完整增加:actId:{},babyUid:{},giftAmount:{},limit{}", actId, babyUid, amount, attr.getFreeGiftLimit());
            } else {
                log.warn("免费礼物:超出限额,本次不增加:actId:{},babyUid:{},giftAmount:{},limit{},giftId{}", actId, babyUid, amount, attr.getFreeGiftLimit(), giftId);
                return;
            }

        }


        String hourStr = DateUtil.format(now, DateUtil.PATTERN_YYYYMMDD);
        int limit = getCryLimit(now, attr);
        if (limit <= 0) {
            return;
        }

        //用户收集的礼物
        String key = makeKey(attr, HONOR_GIFT_AMOUNT_KEY);
        long giftHourAfterAmount = actRedisDao.hIncrByKey(groupCode, key, hourStr + "_" + babyUid, amount);
        long giftHourBeforeAmount = giftHourAfterAmount - amount;
        long afterMod = giftHourAfterAmount / attr.getHonorCryGiftAsk();
        long beforeMod = giftHourBeforeAmount / attr.getHonorCryGiftAsk();

        long addCry = afterMod - beforeMod;
        if (addCry <= 0) {
            return;
        }

        String hourAwardAmountKey = makeKey(attr, HONOR_CRY_AWARD_AMOUNT_KEY);
        //初步判断有无超出限额
        long cryCostAmount = Convert.toLong(actRedisDao.hget(groupCode, hourAwardAmountKey, hourStr), 0);
        if (cryCostAmount >= limit) {
            log.info("增加水晶:超出限额,本次不增加:actId:{},babyUid:{},giftAmount:{},giftHourAfterAmount:{},addCry:{}"
                    , actId, babyUid, giftHourAfterAmount, addCry);
            return;
        }

        //全局总奖励的水晶

        List<Long> incResult = actRedisDao.hIncrWithLimit(groupCode, hourAwardAmountKey, hourStr, addCry, limit, true);
        final int two = 2;
        if (incResult.get(0) == two) {
            log.warn("增加水晶:超出限额,部分增加:actId:{},babyUid:{},giftAmount:{},giftHourAfterAmount:{},addCry:{},realAddCry:{}"
                    , actId, babyUid, amount, giftHourAfterAmount, addCry, incResult.get(1));
            addCry(babyUid, sid, incResult.get(1), attr);
        } else if (incResult.get(0) == 1) {
            log.info("增加水晶:完整增加:actId:{},babyUid:{},giftAmount:{},giftHourAfterAmount:{},addCry:{}"
                    , actId, babyUid, amount, giftHourAfterAmount, addCry);
            addCry(babyUid, sid, addCry, attr);
        } else {
            log.warn("增加水晶:超出限额,本次不增加:actId:{},babyUid:{},giftAmount:{},giftHourAfterAmount:{},addCry:{}"
                    , actId, babyUid, amount, giftHourAfterAmount, addCry);
        }

    }


    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onTaskCompleteEvent(TaskProgressChanged event, GamebabyMonthActComponentAttr attr) {
        if (event.getRankId() == attr.getAnchorRankId()) {
            try {
                handlerAwardCry(event, attr);
            } catch (Exception e) {
                log.error("handlerAwardCry error,event:{},e:{}", JSON.toJSONString(event), e.getMessage(), e);
            }
            return;
        }

        if (event.getRankId() == attr.getPlayerRankId()) {
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            final long finishIndexCount = event.getCurrTaskIndex() - event.getStartTaskIndex();
            if (finishIndexCount > 0) {
                actRedisDao.hIncrByKey(groupCode, makeKey(attr, PLAYER_TOTAL_LUCK_KEY), event.getMember(), finishIndexCount);
            }
        }
    }

    private void handlerAwardCry(TaskProgressChanged event, GamebabyMonthActComponentAttr attr) {
        long cryAmount = event.getCurrTaskIndex() - event.getStartTaskIndex();
        Map<Long, String> actors = event.getActors();
        long babyUid = Convert.toLong(actors.get(HdztRoleId.BABY_ANCHOR));
        log.info("handlerAwardCry begin award,baby:{},amount:{},seq:{}", babyUid, cryAmount, event.getSeq());
        long sid = Convert.toLong(actors.get(HdztRoleId.BABY_CHANNEL), 0);
        addCry(babyUid, sid, cryAmount, attr);
        log.info("handlerAwardCry done award,baby:{},amount:{},seq:{}", babyUid, cryAmount, event.getSeq());

        for (long i = event.getStartTaskIndex() + 1; i <= event.getCurrTaskIndex(); i++) {
            bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.GAME_BABY, System.currentTimeMillis(), babyUid + "", RoleType.ANCHOR, 1
                    , 5040, "anchor_task:" + i, 0, 0);
        }
    }

    private void addCry(long babyUid, long sid, long cryAmount, GamebabyMonthActComponentAttr attr) {

        //-----更新中台水晶榜
        log.info("updateCryRank bigin,babyUid:{},cryAmount:{}", babyUid, cryAmount);
        String seq = UUID.randomUUID().toString();
        Map<Long, String> actors = Maps.newHashMap();
        //宝贝主播
        actors.put(HdztRoleId.BABY_ANCHOR, String.valueOf(babyUid));

        //签约公会
        actors.put(HdztRoleId.BABY_CHANNEL, String.valueOf(sid));

        RankDataEvent rankDataEvent = new RankDataEvent();
        rankDataEvent.setBusiId(BusiId.GAME_BABY.getValue());
        long actId = attr.getActId();
        rankDataEvent.setActId(actId);
        rankDataEvent.setSeq(seq);
        rankDataEvent.setActors(actors);
        rankDataEvent.setItemId(attr.getCryItemId());
        rankDataEvent.setCount(cryAmount);
        rankDataEvent.setScore(cryAmount);
        rankDataEvent.setTimestamp(System.currentTimeMillis());

        kafkaService.updateRanking(rankDataEvent);
        log.info("updateCryRank done,babyUid:{},cryAmount:{}", babyUid, cryAmount);


        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        //---发放勋章奖励
        String balanceKey = makeKey(attr, HONOR_CRY_BABY_BALANCE);
        long afterInc = actRedisDao.hIncrByKey(groupCode, balanceKey, babyUid + "", cryAmount);
        long beforeInc = afterInc - cryAmount;

        long beforeMod = beforeInc / 10;
        long afterMod = afterInc / 10;
        long inc = afterMod - beforeMod;
        if (inc > 0) {
            String medalKey = makeKey(attr, HONOR_CRY_AWARD_MEDAL);
            long medalDay = Convert.toLong(actRedisDao.hget(groupCode, medalKey, babyUid + ""), 0);
            //初步判断有无超出30天限额
            if (medalDay >= attr.getMedalMaxDay()) {
                log.info("勋章奖励:已达到限额:本次不增加,babyUid:{},cryAmount:{}", babyUid, cryAmount);
                return;
            }

            long realInc = 0;
            List<Long> incRet = actRedisDao.hIncrWithLimit(groupCode, medalKey, babyUid + "", inc, attr.getMedalMaxDay(), true);
            final int two = 2;
            if (incRet.get(0) == 1) {
                realInc = inc;
            } else if (incRet.get(0) == two) {
                realInc = incRet.get(1);
            }
            if (realInc > 0) {
                log.info("addCry award medal begin,babyUid:{},addday:{},seq:{}", babyUid, realInc, seq);
                Date now = commonService.getNow(actId);
                BatchWelfareResult welfareResult = hdztAwardServiceClient.doWelfare(DateUtil.format(now, DateUtil.DEFAULT_PATTERN)
                        , BusiId.GAME_BABY.getValue(), babyUid, attr.getMedalTaskId()
                        , Convert.toInt(realInc), attr.getMedalPackageId(), seq);
                log.info("addCry award medal done,babyUid:{},addday:{},seq:{},result:{}", babyUid, realInc, seq, JSON.toJSONString(welfareResult));
            }
        }
    }


    /**
     * 神豪抽奖
     *
     * @param boxType     金银魔盒  1->金 2->银
     * @param lotteryType 抽奖方式 1-> 单次抽 2->一键抽完
     * @return null 幸运之钥不足
     */
    public BatchLotteryResult lottery(long uid, int boxType, int lotteryType, GamebabyMonthActComponentAttr attr) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String puid = String.valueOf(uid);
        long actId = attr.getActId();
        int totalKey = Convert.toInt(actRedisDao.hget(groupCode, makeKey(attr, PLAYER_TOTAL_LUCK_KEY), puid));
        int used = Convert.toInt(actRedisDao.hget(groupCode, makeKey(attr, PLAYER_USED_LUCK_KEY), puid));
        int left = totalKey - used;
        if (left < 1) {
            return null;
        }
        long taskId = boxType == 1 ? attr.getGoldTaskId() : attr.getSilverTaskId();
        LocalDateTime nowDateTime = commonService.getNowDateTime(actId);
        String nowStr = nowDateTime.format(DateUtil.YYYY_MM_DD_HH_MM_SS);
        int count = count(boxType, lotteryType, left);
        if (count < 1) {
            return null;
        }
        String seq = UUID.randomUUID().toString();
        log.info("player lottery,uid:{} taskId:{} count:{} total:{} used:{}, seq:{}, boxType:{},lotteryType:{},nowstr:{}",
                puid, taskId, count, totalKey, used, seq, boxType, lotteryType, nowStr);
        if (boxType == 1) {
            actRedisDao.hIncrByKey(groupCode, makeKey(attr, PLAYER_USED_LUCK_KEY), puid, count * 10);
        } else {
            actRedisDao.hIncrByKey(groupCode, makeKey(attr, PLAYER_USED_LUCK_KEY), puid, count);
        }
        BatchLotteryResult result = hdztAwardServiceClient.doLottery(nowStr, 400, uid, taskId, count, 0, seq);
        log.info("uid:{} lottery result:{}", uid, JsonUtil.toJson(result));
        //记录中奖记录
        if (result.getCode() == 0) {
            Map<Long, Long> recordIds = result.getRecordPackages();
            for (Map.Entry<Long, Long> entry : recordIds.entrySet()) {
                String packageId = String.valueOf(entry.getValue());
                actRedisDao.hIncrByKey(groupCode, makeKey(attr, String.format(PLAYER_LOTTERY_AWARD, puid)), packageId, 1);
                bigDataService.saveNoRankDataToFile(actId, BusiId.GAME_BABY, System.currentTimeMillis(), puid, RoleType.USER, 1
                        , 5040, "player_award:" + packageId, 0, 0);
            }
        }
        return result;
    }

    public Map<Long, AwardModelInfo> packageInfoMap(GamebabyMonthActComponentAttr attr) {
        try {
            Map<Long, AwardModelInfo> gold = hdztAwardServiceClient.queryAwardTasks(attr.getGoldTaskId());
            Map<Long, AwardModelInfo> silver = hdztAwardServiceClient.queryAwardTasks(attr.getSilverTaskId());
            Map<Long, AwardModelInfo> result = new HashMap<>(16);
            if (MapUtils.isNotEmpty(gold)) {
                result.putAll(gold);
            }

            if (MapUtils.isNotEmpty(silver)) {
                result.putAll(silver);
            }

            return result;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }

    private int count(int boxType, int lottery, int left) {
        final int ten = 10;
        if (boxType == 1 && left < ten) {
            return -1;
        }
        if (boxType == 1) {
            if (lottery == 1) {
                return 1;
            }
            return left / 10;
        } else {
            if (lottery == 1) {
                return 1;
            }
            return left;
        }
    }

    public Response playerAward(long yyUid, GamebabyMonthActComponentAttr attr) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        List<String> awardList = new ArrayList<>();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Object, Object> awards = actRedisDao.hGetAll(groupCode, makeKey(attr, String.format(PLAYER_LOTTERY_AWARD, yyUid)));
        for (Map.Entry<Object, Object> aw : awards.entrySet()) {
            long pid = Convert.toLong(aw.getKey());
            AwardModelInfo awardModelInfo = packageInfoMap.get(pid);
            if (awardModelInfo != null) {
                awardList.add(awardModelInfo.getPackageName() + "*" + Convert.toLong(aw.getValue()));
            }
        }
        return Response.success(awardList);
    }

    public Response lotteryAward(BatchLotteryResult batchLotteryResult, GamebabyMonthActComponentAttr attr) {
        if (batchLotteryResult.getCode() != 0) {
            return Response.fail(3, batchLotteryResult.getReason());
        }
        List<String> awardList = Lists.newArrayList();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Long, Long> recordIds = batchLotteryResult.getRecordPackages();
        Map<Long, Integer> pidCount = Maps.newHashMap();
        for (Long pid : recordIds.values()) {
            pidCount.merge(pid, 1, Integer::sum);
        }

        for (Map.Entry<Long, Integer> entry : pidCount.entrySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(entry.getKey());
            if (awardModelInfo != null) {
                awardList.add(awardModelInfo.getPackageName() + "*" + entry.getValue());
            }
        }
        return Response.success(awardList);
    }


    public long playerLuckKey(long yyUid, long actId) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        long total = Convert.toLong(actRedisDao.hget(groupCode, makeKey(actId, 400, PLAYER_TOTAL_LUCK_KEY), String.valueOf(yyUid)));
        long used = Convert.toLong(actRedisDao.hget(groupCode, makeKey(actId, 400, PLAYER_USED_LUCK_KEY), String.valueOf(yyUid)));
        return total - used;
    }

    /**
     * 水晶数据读取
     */
    public Map<String, Object> queryCryInfo(final long babyUid, GamebabyMonthActComponentAttr attr) {
        Map<String, Object> result = Maps.newHashMap();
        UserBaseInfo userBaseInfo = commonService.getUserInfo(babyUid, true);
        //本时段剩余水晶,-1代表非采集水晶时段
        long hourLeftCry = 0;
        //本小时已经累积礼物数(分子，分子可能大于分母)
        long hourGiftAmount = 0;
        //需要完成任务的礼物数
        long giftMission = attr.getHonorCryGiftAsk();
        //我的水晶数
        long babyCryAmount = 0;
        //我的勋章天数
        long medalDay = 0;
        long actId = attr.getActId();
        Date now = commonService.getNow(actId);

        String groupCode = redisConfigManager.getGroupCode(actId);
        long limit = getCryLimit(now, attr);
        String hourAwardAmountKey = makeKey(attr, HONOR_CRY_AWARD_AMOUNT_KEY);
        String hourStr = DateUtil.format(now, DateUtil.PATTERN_YYYYMMDD);
        long cryCostAmount = Convert.toLong(actRedisDao.hget(groupCode, hourAwardAmountKey, hourStr), 0);
        hourLeftCry = limit == -1 ? limit : limit - cryCostAmount;

        String key = makeKey(attr, HONOR_GIFT_AMOUNT_KEY);
        hourGiftAmount = Convert.toLong(actRedisDao.hget(groupCode, key, hourStr + "_" + babyUid));

        String babyCryAmountKey = makeKey(attr, HONOR_CRY_BABY_BALANCE);
        babyCryAmount = Convert.toLong(actRedisDao.hget(groupCode, babyCryAmountKey, babyUid + ""));

        String medalDayKey = makeKey(attr, HONOR_CRY_AWARD_MEDAL);
        medalDay = Convert.toLong(actRedisDao.hget(groupCode, medalDayKey, babyUid + ""));


        result.put("nick", babyUid == 0 ? Base64Utils.encodeToString("虚位以待".getBytes()) : userBaseInfo.getNick());
        result.put("avatar", babyUid == 0 ? Const.IMAGE.DEFAULT_USER_LOGO : userBaseInfo.getLogo());
        result.put("hourLeftCry", hourLeftCry);
        result.put("hourGiftAmount", hourGiftAmount);
        result.put("giftMission", giftMission);
        result.put("babyCryAmount", babyCryAmount);
        result.put("medalDay", medalDay);

        return result;
    }
}
