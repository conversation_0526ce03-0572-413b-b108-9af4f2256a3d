package com.yy.gameecology.hdzj.element.component.xmodule.pepc;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.mq.hdzk.PepcGameEndEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.PepcPhaseSettleEvent;
import com.yy.gameecology.activity.bean.wzry.JoinGameResult;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.pepc.PepcAwardService;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcTeamMemberMapper;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcAwardRecord;
import com.yy.gameecology.common.db.model.gameecology.pepc.PepcGameMember;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PepcActAwardComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Component
@RestController
@RequestMapping("/5143")
public class PepcActAwardComponent extends BaseActComponent<PepcActAwardComponentAttr> {

    @Resource
    private PepcTeamMapper pepcTeamMapper;

    @Autowired
    private PepcAwardService pepcAwardService;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private PepcTeamMemberMapper pepcTeamMemberMapper;

    @Autowired
    private PepcPhaseComponent pepcPhaseComponent;

    @Autowired
    private PepcDao pepcDao;

    @Override
    public Long getComponentId() {
        return ComponentId.PEPC_AWARD;
    }


    /**
     * 首次击杀用户奖励
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = PepcGameEndEvent.class, canRetry = true)
    public void onPepcGameEndEvent(PepcGameEndEvent event, PepcActAwardComponentAttr attr) {
        log.info("onPepcGameEndEvent -> event:{}, attr:{}", event, attr);

        var phaseComponentAttr = pepcPhaseComponent.getUniqueComponentAttr(attr.getActId());
        if (phaseComponentAttr == null) {
            log.info("not config,actId:{}", event.getActId());
            return;
        }
        List<PepcGameMember> gameMembers = pepcDao.getPepcGameMemberByGameId(event.getActId(), event.getGameId());
        if (CollectionUtils.isEmpty(gameMembers)) {
            log.info("no gameMember,actId:{},gameId:{}", event.getActId(), event.getGameId());
            return;
        }
        Optional<PepcPhaseComponentAttr.PepcAwardInfo> awardInfo = phaseComponentAttr.getAwardInfos()
                .stream()
                .filter(p -> p.getRank() == -1).findFirst();
        if (awardInfo.isEmpty()) {
            log.info("no awardConfig,actId:{},gameId:{}", event.getActId(), event.getGameId());
            return;
        }
        for (PepcGameMember member : gameMembers) {
            long ele = Convert.toLong(member.getElimination(), 0);
            //要淘汰了人给给奖励
            if (ele <= 0) {
                log.info("no ele,actId:{},gameId:{},member:{}", event.getActId(), event.getGameId(), member.getUid());
                continue;
            }
            pepcAwardService.tryGrantFirstGameAward(phaseComponentAttr, awardInfo.get(), event.getGameId(), member.getUid(), new Date());
        }

    }


    /**
     * 最后一个阶段 结算发奖
     *
     */
    @HdzjEventHandler(value = PepcPhaseSettleEvent.class, canRetry = true)
    public void onPepcPhaseSettleEvent(PepcPhaseSettleEvent event, PepcActAwardComponentAttr attr) {
        log.info("onPepcPhaseSettleEvent -> event:{}, attr:{}", event, attr);
        if (!event.isLastPhase()) {
            log.info("not last phase");
            return;
        }
        var phaseComponentAttr = pepcPhaseComponent.getUniqueComponentAttr(attr.getActId());
        pepcAwardService.doSettleAward(phaseComponentAttr, event.getActId(), event.getPhaseId());
    }



//    /**
//     * &#x626B;&#x63CF; aov_match_award_record &#x8868;&#x67E5;&#x8BE2;&#x5E76;&#x66F4;&#x65B0;&#x5DF2;&#x63D0;&#x4EA4;&#x72B6;&#x6001;&#x7684;&#x6570;&#x636E;&#x4E3A;&#x6210;&#x529F;&#x3001;&#x6216;&#x5931;&#x8D25;
//     */
    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(cron = "15 45 1/3 * * ?")
    public void refreshSubmittedAwardState() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
            if (activityInfo == null) {
                continue;
            }

            Date now = commonService.getNow(actId);

            if (!actInfoService.inActTime(now, activityInfo)) {
                continue;
            }

            PepcActAwardComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

            timerSupport.work("doRefreshSubmittedPepcAwardState", 300, () -> pepcAwardService.refreshSubmittedAwardState(actId));

        }

    }



    @GetMapping("hasAward")
    public Response<Boolean> queryHasAward(@RequestParam(name = "actId") int actId,
                                           @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {
        PepcActAwardComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        int count = pepcAwardService.countGrantedAward(actId, uid);
        return Response.success(count > 0);
    }

    @GetMapping("myAwardList")
    public Response<List<PepcAwardRecord>> queryMyAwardList(@RequestParam(name = "actId") int actId,
                                                            @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {
        PepcActAwardComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        try {
            List<PepcAwardRecord> result = pepcAwardService.queryUserAwardList(actId, uid);
            return Response.success(result);
        } catch (Exception e) {
            log.error("queryMyAwardList fail: {}", e);
            return Response.fail(500, "服务器忙，请稍后再试");
        }
    }

    @RequestMapping("receiveAward")
    public Response<?> receiveAward(HttpServletRequest request,
                                    @RequestParam(name = "actId") int actId,
                                    @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                    @RequestParam(name = "awardId") long awardId,
                                    @RequestParam(name = "account") String account,
                                    @RequestParam(name = "mobile") String mobile,
                                    @RequestParam(name = "verifyCode", required = false) String verifyCode,
                                    @RequestParam(name = "recordId", required = false) String recordId,
                                    @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        PepcActAwardComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

//        if(!actInfoService.inActTime(Convert.toLong(actId))){
//            return Response.fail(401, "activity not exist");
//        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        if (StringUtils.isEmpty(account)) {
            return Response.fail(400, "账号不能为空");
        }

        if (StringUtils.isEmpty(mobile)) {
            return Response.fail(400, "手机号不能为空");
        }

        // 风控
        try {
            zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);
        } catch (SuperException e) {
            log.warn("receiveAward risk awardId:{},uid:{},e:", awardId, uid, e);
            JoinGameResult joinGameResult = new JoinGameResult();
            joinGameResult.setRiskRecheck(e.getData());
            return Response.fail(e.getCode(), e.getMessage(), joinGameResult);
        }

        try {
            return pepcAwardService.submitAwardAccountInfo(awardId, uid, account, mobile);
        } catch (BusinessException e) {
            log.warn("receiveAward warn:", e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Throwable e) {
            log.error("receiveAward fail:", e);
            return Response.fail(500, "服务器忙，请稍后再试");
        }
    }

    @RequestMapping("pepecAwardTest")
    public Response<String> pepecAwardTest(Long actId, Long awardId, Integer awardType, Long amount, Long userId) {
        if (SysEvHelper.isDeploy()) {
            return Response.fail(-1, "not test");
        }
        pepcAwardService.sendAwardNotice(actId, userId, awardId, awardType, amount);
        return Response.success(System.currentTimeMillis() + "");
    }
}
