package com.yy.gameecology.activity.bean.mq;


/**
 * 乱斗结束事件
 *
 * <AUTHOR> 2021/8/26
 * `
 */
public class ChannelFightEndEvent {

    /**
     * 游戏ID，唯一标识一场乱斗
     */
    private String gameId;
    /**
     * 开始时间
     */
    private long startTime;
    /**
     * 结束时间
     */
    private long endTime;
    /**
     * 当前子厅活动场次 子厅唯一
     */
    private long serialNo;
    /**
     * 主播uid
     */
    private long compereUid;
    private long sid;
    private long ssid;
    /**
     * 魅力值
     */
    private long charm;
    /**
     * 营收流水(不包括盖章和非分成礼物)，单位：1YB=1000，紫水晶
     */
    private long revenue;
    /**
     * 对方频道主播uid
     */
    private long extraUid;

    private long extraSid;
    private long extraSsid;
    /**
     * 对方频道魅力值
     */
    private long extraCharm;
    /**
     * 对方频道营收流水(不包括盖章和非分成礼物)，单位：1YB=1000，紫水晶
     */
    private long extraRevenue;
    /**
     * 消息时间戳
     */
    private long timestamp;
    /**
     * sid,ssid这个频道的胜利状态, 0 平   1, 胜, 2 败
     */
    private int winning;

    /**
     * 服务器收到事件的时间 戳
     */
    private long receiveTimestamp = System.currentTimeMillis();

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public long getSerialNo() {
        return serialNo;
    }

    public void setSerialNo(long serialNo) {
        this.serialNo = serialNo;
    }

    public long getCompereUid() {
        return compereUid;
    }

    public void setCompereUid(long compereUid) {
        this.compereUid = compereUid;
    }

    public long getSid() {
        return sid;
    }

    public void setSid(long sid) {
        this.sid = sid;
    }

    public long getSsid() {
        return ssid;
    }

    public void setSsid(long ssid) {
        this.ssid = ssid;
    }

    public long getCharm() {
        return charm;
    }

    public void setCharm(long charm) {
        this.charm = charm;
    }

    public long getRevenue() {
        return revenue;
    }

    public void setRevenue(long revenue) {
        this.revenue = revenue;
    }

    public long getExtraUid() {
        return extraUid;
    }

    public void setExtraUid(long extraUid) {
        this.extraUid = extraUid;
    }

    public long getExtraSid() {
        return extraSid;
    }

    public void setExtraSid(long extraSid) {
        this.extraSid = extraSid;
    }

    public long getExtraSsid() {
        return extraSsid;
    }

    public void setExtraSsid(long extraSsid) {
        this.extraSsid = extraSsid;
    }

    public long getExtraCharm() {
        return extraCharm;
    }

    public void setExtraCharm(long extraCharm) {
        this.extraCharm = extraCharm;
    }

    public long getExtraRevenue() {
        return extraRevenue;
    }

    public void setExtraRevenue(long extraRevenue) {
        this.extraRevenue = extraRevenue;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getWinning() {
        return winning;
    }

    public void setWinning(int winning) {
        this.winning = winning;
    }

    public long getReceiveTimestamp() {
        return receiveTimestamp;
    }

    public void setReceiveTimestamp(long receiveTimestamp) {
        this.receiveTimestamp = receiveTimestamp;
    }
}
