package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.client.thrift.WeidengServiceClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.redis.TailLightComponent;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardRecordInfo;
import com.yy.thrift.hdztaward.AwardRecordResult;
import com.yy.thrift.hdztranking.ActorInfoItem;
import com.yy.thrift.hdztranking.ActorQueryItem;
import com.yy.thrift.hdztranking.BusiId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;


@Service
public class ActPlayService {

    private static final Logger log = LoggerFactory.getLogger(ActPlayService.class);

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;
    @Autowired
    private ActInfoService actInfoService;
    @Autowired
    private WeidengServiceClient weidengServiceClient;
    @Autowired
    private CommonService commonService;
    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;
    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;
    @Autowired
    private ActServiceManager actServiceManager;
    @Autowired
    private TailLightComponent tailLightComponent;


    private static final Map<Long, String> ACT_SEASON_CONFIG_MAP = Maps.newHashMap();


    /**
     * 点亮1级尾灯
     *
     * @param uid
     * @param query
     * @return
     */

    public JSONObject tailLightWeb(long uid, boolean query, long actId) {
        if (tailLightComponent.isMyDuty(actId)) {
            return tailLightComponent.tailLightWeb(actId, uid, query);
        }

        JSONObject rs = new JSONObject(2);
        rs.put("status", 1);
        rs.put("msg", "not support");
        return rs;
    }


    public UserBaseInfo getUserBaseInfo(long userUid, Template template) {
        UserBaseInfo userInfo = new UserBaseInfo();
        if (template == Template.Jiaoyou) {
            Optional.ofNullable(ftsBaseInfoBridgeClient.getFtsUserInfoMap(Arrays.asList(userUid)))
                    .flatMap(map -> Optional.ofNullable(map.get(userUid)))
                    .ifPresent(info -> {
                        userInfo.setUid(userUid);
                        userInfo.setNick(info.getNick());
                        userInfo.setLogo(info.getAvatar_url());
                        userInfo.setYyno(info.getImid());
                    });
        }
        if (userInfo.getUid() == 0) {
            Optional.ofNullable(commonService.getUserInfo(userUid, false)).ifPresent(info -> {
                userInfo.setUid(userUid);
                userInfo.setNick(info.getNick());
                userInfo.setLogo(info.getLogo());
                userInfo.setYyno(info.getYyno());
            });
        }
        return userInfo;
    }

    /**
     * 查询用户分数
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param uid
     * @return
     */
    private long getScore(long actId, long rankId, long phaseId, long uid) {
        ActorQueryItem actorQueryItem = new ActorQueryItem();
        actorQueryItem.setRankingId(rankId);
        actorQueryItem.setPhaseId(phaseId);
        actorQueryItem.setActorId(String.valueOf(uid));
        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, Arrays.asList(actorQueryItem));

        if (!actorInfoItems.isEmpty()) {
            return actorInfoItems.get(0).getScore();
        }
        return 0L;
    }

    public boolean tailLight(long userUid, int level, long actId) {
        ActService actService = actServiceManager.getActService(actId);
        if (actService == null) {
            log.error("tailLight error, actService can not find .actId:{},userId:{},level:{}", actId, userUid, level);
            return false;
        }
        if (level <= 0) {
            log.error("tailLight error, level error.actId:{},userId:{},level:{}", actId, userUid, level);
            return false;
        }
        long maxLevel = actService.getTailLightMaxLevel();
        if (level > maxLevel) {
            log.error("tailLight error, level greater than max level.actId:{},userId:{},level:{}", actId, userUid, level);
            return false;
        }
        long taskId = actService.getTailLightTaskId();
        long startPackageId = actService.getTailLightStartPackageId();
        if (taskId == 0L || startPackageId == 0L) {
            log.error("tailLight error, taskId or startPackageId is zero .actId:{},userId:{},level:{},taskId:{},startPackageId:{}", actId, userUid, level, taskId, startPackageId);
            return false;
        }
        return weidengServiceClient.lightUpWeidengByHdzt(BusiId.GAME_ECOLOGY, userUid, taskId, startPackageId, level);
    }

    public Response lotteryRoll(int busiId, long actId) {
        if (busiId == 0) {
            return Response.badRequest();
        }
        try {
            AwardRecordResult AwardRecordResult = hdztAwardServiceClient.getProxy(false).getAwardRecordListEx(busiId, actId);
            List<AwardRecordInfo> awardRecordList = AwardRecordResult.getAwardList();
            List<JSONObject> data = new ArrayList<>();
            if (!CollectionUtils.isEmpty(awardRecordList)) {
                for (AwardRecordInfo awardRecordInfo : awardRecordList) {
                    JSONObject item = new JSONObject();
                    String nick = getUserBaseInfo(awardRecordInfo.getUid(), busiId == BusiId.MAKE_FRIEND.getValue() ? Template.Jiaoyou : null).getNick();
                    nick = subNickName(nick, 2);
                    item.put("nick", nick);
                    item.put("prize", awardRecordInfo.getPackageName());
                    data.add(item);
                }
            }
            return Response.success(data);
        } catch (Exception e) {
            log.error("lotteryRecord busi:{} err:{}", busiId, e.getMessage(), e);
            return Response.success(null);
        }
    }


    private static String subNickName(String nick, int subLength) {
        if (StringUtil.isNotBlank(nick)) {
            final int two = 2;
            if (nick.length() == two) {
                subLength = 1;
            }
            int sub = Math.min(nick.length(), subLength);
            nick = "**" + nick.substring(sub);
        }
        return nick;
    }
}
