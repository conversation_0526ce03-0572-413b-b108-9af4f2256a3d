package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:个人个性化任务配置
 *
 * @createBy 曾文帜
 * @create 2020-10-22 14:54
 **/
@TableColumn(underline = true)
public class ActTask implements Serializable {
    public static String TABLE_NAME = "act_task";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"task_id"};

    public static RowMapper<ActTask> ROW_MAPPER = null;



    /**
     * task_id
     */
    private Long taskId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 活动中台活动id
     */
    private Long actId;

    /**
     * 中台数据来源累榜的榜单id 匹配到了才采集数据
     */
    private Long hdztRankId;

    /**
     * 中台数据来源累榜的阶段id 匹配到了才采集数据 -1代表全部匹配
     */
    private Long hdztPhaseId;

    /**
     * 中控任务保存榜单id
     */
    private Long taskRankId;

    /**
     * 中控任务保存阶段id -1代表部分阶段
     */
    private Long taskPhaseId;

    /**
     * 任务开始时间，任务开始后才采集
     */
    private Date startTime;

    /**
     * 任务结束时间
     */
    private Date endTime;

    /**
     * 奖励贡献的成员数量 0 代表没有
     */
    private Integer awardRankCount;

    /**
     * 1===有效的任务
     */
    private Integer status;

    /**
     * 奖励展示文案
     */
    private String awardText;

    /**
     * remark
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String extJson;

    /**
     * 有多个任务时的展示排序，从小到大排序
     */
    private Integer sort;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * 贡献榜奖励的成员
     */
    private String awardRole;


    /**
     * 参与任务的角色类型
     */
    private Integer roleType;


    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getHdztRankId() {
        return hdztRankId;
    }

    public void setHdztRankId(Long hdztRankId) {
        this.hdztRankId = hdztRankId;
    }

    public Long getHdztPhaseId() {
        return hdztPhaseId;
    }

    public void setHdztPhaseId(Long hdztPhaseId) {
        this.hdztPhaseId = hdztPhaseId;
    }

    public Long getTaskRankId() {
        return taskRankId;
    }

    public void setTaskRankId(Long taskRankId) {
        this.taskRankId = taskRankId;
    }

    public Long getTaskPhaseId() {
        return taskPhaseId;
    }

    public void setTaskPhaseId(Long taskPhaseId) {
        this.taskPhaseId = taskPhaseId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getAwardRankCount() {
        return awardRankCount;
    }

    public void setAwardRankCount(Integer awardRankCount) {
        this.awardRankCount = awardRankCount;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAwardText() {
        return awardText;
    }

    public void setAwardText(String awardText) {
        this.awardText = awardText;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getAwardRole() {
        return awardRole;
    }

    public void setAwardRole(String awardRole) {
        this.awardRole = awardRole;
    }

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }
}