package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardPackageAttrConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-11-14 20:49
 **/
@Data
public class AnchorHourPkComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "监听榜单变化的榜单id")
    private long rankId;

    @ComponentAttrField(labelText = "监听榜单变化的阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "pk数据榜单id")
    private long pkRankId;

    @ComponentAttrField(labelText = "pk数据榜单阶段id")
    private long pkRankPhaseId;

    @ComponentAttrField(labelText = "每天多少小时后开始pk")
    private int pkDayStartTime;

    @ComponentAttrField(labelText = "每小时多少S后pk开始")
    private long pkHourStartTime;

    @ComponentAttrField(labelText = "每小时多少S后结算上一小时")
    private long pkHourSettleDelaySeconds;

    @ComponentAttrField(labelText = "挂件pk结果展示时间（S）")
    private long pkShowResultSeconds;

    @ComponentAttrField(labelText = "pk主持营收分白名单")
    private long pkAnchorScoreWhiteListIndex;


    @ComponentAttrField(labelText = "pk主持营收分增加随机数比例")
    private String pkAnchorScoreRandomFactor = "0.1";

    @ComponentAttrField(labelText = "pk主播补位uid", remark = "奇数的时候需要补位")
    private long pkAnchorStandbyUid;


    @ComponentAttrField(labelText = "更新pk榜单的item")
    private String updatePkRankItem;

    @ComponentAttrField(labelText = "pk榜显示TopN预加载数据")
    private long topPkRankShowPreLoad;

    @ComponentAttrField(labelText = "pk榜显示TopN")
    private long topPkRankShow;

    @ComponentAttrField(labelText = "pk抽奖",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "达成分值", remark = ">="),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<Long, AwardAttrConfig> hourRankAward = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "总奖池", remark = "单位厘")
    private long totalPool;

    @ComponentAttrField(labelText = "触发祝福抽奖的分值",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private List<Long> chatLotteryScoreLevel = Lists.newArrayList();

    @ComponentAttrField(labelText = "祝福抽奖组件索引id")
    private long chatLotteryIndex;

    @ComponentAttrField(labelText = "控制参与pk的uid", remark = "仅测试环境和灰度活动有控制",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> pkTestUid = Sets.newHashSet();

    @ComponentAttrField(labelText = "过滤黑名单房间主播参与pk")
    private boolean filterBlackRoomList = true;



}
