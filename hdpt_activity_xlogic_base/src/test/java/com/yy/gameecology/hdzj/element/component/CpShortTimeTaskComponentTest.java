package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.hdzj.element.redis.CpShortTimeTaskComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-10-28 15:34
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-3.properties", "classpath:env/local/application-inner.properties"})
public class CpShortTimeTaskComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "3");
    }
    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    final long ACT_ID = 2024103001;

    @Autowired
    private CpShortTimeTaskComponent component;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Test
    public void insertCpTest() {
        component.insertOrReplaceCurCpInfo(component.getUniqueComponentAttr(ACT_ID), redisConfigManager.getGroupCode(ACT_ID), System.currentTimeMillis() + "",
                "50042952|141772575473674", "2024111518");
    }

    @Test
    public void settle() {
        var cpInfo = component.getCurCpInfo(component.getUniqueComponentAttr(ACT_ID),redisConfigManager.getGroupCode(ACT_ID),"50042952|141772575473674","2024111518");
        component.settleExpireCpInfoByLock(component.getUniqueComponentAttr(ACT_ID),cpInfo,redisConfigManager.getGroupCode(ACT_ID),"2024111518");
    }

    @Test
    public void addWithTimeTest(){
        actRedisGroupDao.zAddWithTime("4","2024103001:zaddtest","zengwenzhi",1000,false);
    }

}
