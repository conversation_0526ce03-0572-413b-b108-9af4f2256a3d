package com.yy.gameecology.activity.bean.mms;

/**
 * Autogenerated by Thrift Compiler (0.9.1)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */

import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;
import org.apache.thrift.scheme.TupleScheme;

import java.util.*;

/**
 * 监控/举报信息签名认证包
 */
public class MmsSign implements org.apache.thrift.TBase<MmsSign, MmsSign._Fields>, java.io.Serializable, Cloneable, Comparable<MmsSign> {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("MmsSign");

    private static final org.apache.thrift.protocol.TField APP_KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("appKey", org.apache.thrift.protocol.TType.STRING, (short)1);
    private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)2);
    private static final org.apache.thrift.protocol.TField SIGN_PAR_NAMES_FIELD_DESC = new org.apache.thrift.protocol.TField("signParNames", org.apache.thrift.protocol.TType.LIST, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
        schemes.put(StandardScheme.class, new MmsSignStandardSchemeFactory());
        schemes.put(TupleScheme.class, new MmsSignTupleSchemeFactory());
    }

    /**
     * 密钥编码
     */
    public String appKey; // required
    /**
     * 签名
     */
    public String sign; // required
    /**
     * 签名参数名列表
     */
    public List<String> signParNames; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
        /**
         * 密钥编码
         */
        APP_KEY((short)1, "appKey"),
        /**
         * 签名
         */
        SIGN((short)2, "sign"),
        /**
         * 签名参数名列表
         */
        SIGN_PAR_NAMES((short)3, "signParNames");

        private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

        static {
            for (_Fields field : EnumSet.allOf(_Fields.class)) {
                byName.put(field.getFieldName(), field);
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, or null if its not found.
         */
        public static _Fields findByThriftId(int fieldId) {
            switch(fieldId) {
                case 1: // APP_KEY
                    return APP_KEY;
                case 2: // SIGN
                    return SIGN;
                case 3: // SIGN_PAR_NAMES
                    return SIGN_PAR_NAMES;
                default:
                    return null;
            }
        }

        /**
         * Find the _Fields constant that matches fieldId, throwing an exception
         * if it is not found.
         */
        public static _Fields findByThriftIdOrThrow(int fieldId) {
            _Fields fields = findByThriftId(fieldId);
            if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
            return fields;
        }

        /**
         * Find the _Fields constant that matches name, or null if its not found.
         */
        public static _Fields findByName(String name) {
            return byName.get(name);
        }

        private final short _thriftId;
        private final String _fieldName;

        _Fields(short thriftId, String fieldName) {
            _thriftId = thriftId;
            _fieldName = fieldName;
        }

        public short getThriftFieldId() {
            return _thriftId;
        }

        public String getFieldName() {
            return _fieldName;
        }
    }

    // isset id assignments
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
        Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
        tmpMap.put(_Fields.APP_KEY, new org.apache.thrift.meta_data.FieldMetaData("appKey", org.apache.thrift.TFieldRequirementType.DEFAULT,
                new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.DEFAULT,
                new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
        tmpMap.put(_Fields.SIGN_PAR_NAMES, new org.apache.thrift.meta_data.FieldMetaData("signParNames", org.apache.thrift.TFieldRequirementType.DEFAULT,
                new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST,
                        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
        metaDataMap = Collections.unmodifiableMap(tmpMap);
        org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(MmsSign.class, metaDataMap);
    }

    public MmsSign() {
    }

    public MmsSign(
            String appKey,
            String sign,
            List<String> signParNames)
    {
        this();
        this.appKey = appKey;
        this.sign = sign;
        this.signParNames = signParNames;
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public MmsSign(MmsSign other) {
        if (other.isSetAppKey()) {
            this.appKey = other.appKey;
        }
        if (other.isSetSign()) {
            this.sign = other.sign;
        }
        if (other.isSetSignParNames()) {
            List<String> __this__signParNames = new ArrayList<String>(other.signParNames);
            this.signParNames = __this__signParNames;
        }
    }

    public MmsSign deepCopy() {
        return new MmsSign(this);
    }

    @Override
    public void clear() {
        this.appKey = null;
        this.sign = null;
        this.signParNames = null;
    }

    /**
     * 密钥编码
     */
    public String getAppKey() {
        return this.appKey;
    }

    /**
     * 密钥编码
     */
    public MmsSign setAppKey(String appKey) {
        this.appKey = appKey;
        return this;
    }

    public void unsetAppKey() {
        this.appKey = null;
    }

    /** Returns true if field appKey is set (has been assigned a value) and false otherwise */
    public boolean isSetAppKey() {
        return this.appKey != null;
    }

    public void setAppKeyIsSet(boolean value) {
        if (!value) {
            this.appKey = null;
        }
    }

    /**
     * 签名
     */
    public String getSign() {
        return this.sign;
    }

    /**
     * 签名
     */
    public MmsSign setSign(String sign) {
        this.sign = sign;
        return this;
    }

    public void unsetSign() {
        this.sign = null;
    }

    /** Returns true if field sign is set (has been assigned a value) and false otherwise */
    public boolean isSetSign() {
        return this.sign != null;
    }

    public void setSignIsSet(boolean value) {
        if (!value) {
            this.sign = null;
        }
    }

    public int getSignParNamesSize() {
        return (this.signParNames == null) ? 0 : this.signParNames.size();
    }

    public java.util.Iterator<String> getSignParNamesIterator() {
        return (this.signParNames == null) ? null : this.signParNames.iterator();
    }

    public void addToSignParNames(String elem) {
        if (this.signParNames == null) {
            this.signParNames = new ArrayList<String>();
        }
        this.signParNames.add(elem);
    }

    /**
     * 签名参数名列表
     */
    public List<String> getSignParNames() {
        return this.signParNames;
    }

    /**
     * 签名参数名列表
     */
    public MmsSign setSignParNames(List<String> signParNames) {
        this.signParNames = signParNames;
        return this;
    }

    public void unsetSignParNames() {
        this.signParNames = null;
    }

    /** Returns true if field signParNames is set (has been assigned a value) and false otherwise */
    public boolean isSetSignParNames() {
        return this.signParNames != null;
    }

    public void setSignParNamesIsSet(boolean value) {
        if (!value) {
            this.signParNames = null;
        }
    }

    public void setFieldValue(_Fields field, Object value) {
        switch (field) {
            case APP_KEY:
                if (value == null) {
                    unsetAppKey();
                } else {
                    setAppKey((String)value);
                }
                break;

            case SIGN:
                if (value == null) {
                    unsetSign();
                } else {
                    setSign((String)value);
                }
                break;

            case SIGN_PAR_NAMES:
                if (value == null) {
                    unsetSignParNames();
                } else {
                    setSignParNames((List<String>)value);
                }
                break;

        }
    }

    public Object getFieldValue(_Fields field) {
        switch (field) {
            case APP_KEY:
                return getAppKey();

            case SIGN:
                return getSign();

            case SIGN_PAR_NAMES:
                return getSignParNames();

        }
        throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
        if (field == null) {
            throw new IllegalArgumentException();
        }

        switch (field) {
            case APP_KEY:
                return isSetAppKey();
            case SIGN:
                return isSetSign();
            case SIGN_PAR_NAMES:
                return isSetSignParNames();
        }
        throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
        if (that == null)
            return false;
        if (that instanceof MmsSign)
            return this.equals((MmsSign)that);
        return false;
    }

    public boolean equals(MmsSign that) {
        if (that == null)
            return false;

        boolean this_present_appKey = true && this.isSetAppKey();
        boolean that_present_appKey = true && that.isSetAppKey();
        if (this_present_appKey || that_present_appKey) {
            if (!(this_present_appKey && that_present_appKey))
                return false;
            if (!this.appKey.equals(that.appKey))
                return false;
        }

        boolean this_present_sign = true && this.isSetSign();
        boolean that_present_sign = true && that.isSetSign();
        if (this_present_sign || that_present_sign) {
            if (!(this_present_sign && that_present_sign))
                return false;
            if (!this.sign.equals(that.sign))
                return false;
        }

        boolean this_present_signParNames = true && this.isSetSignParNames();
        boolean that_present_signParNames = true && that.isSetSignParNames();
        if (this_present_signParNames || that_present_signParNames) {
            if (!(this_present_signParNames && that_present_signParNames))
                return false;
            if (!this.signParNames.equals(that.signParNames))
                return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        return 0;
    }

    @Override
    public int compareTo(MmsSign other) {
        if (!getClass().equals(other.getClass())) {
            return getClass().getName().compareTo(other.getClass().getName());
        }

        int lastComparison = 0;

        lastComparison = Boolean.valueOf(isSetAppKey()).compareTo(other.isSetAppKey());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetAppKey()) {
            lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appKey, other.appKey);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetSign()) {
            lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        lastComparison = Boolean.valueOf(isSetSignParNames()).compareTo(other.isSetSignParNames());
        if (lastComparison != 0) {
            return lastComparison;
        }
        if (isSetSignParNames()) {
            lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.signParNames, other.signParNames);
            if (lastComparison != 0) {
                return lastComparison;
            }
        }
        return 0;
    }

    public _Fields fieldForId(int fieldId) {
        return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
        schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
        schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("MmsSign(");
        boolean first = true;

        sb.append("appKey:");
        if (this.appKey == null) {
            sb.append("null");
        } else {
            sb.append(this.appKey);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("sign:");
        if (this.sign == null) {
            sb.append("null");
        } else {
            sb.append(this.sign);
        }
        first = false;
        if (!first) sb.append(", ");
        sb.append("signParNames:");
        if (this.signParNames == null) {
            sb.append("null");
        } else {
            sb.append(this.signParNames);
        }
        first = false;
        sb.append(")");
        return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
        // check for required fields
        // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
        try {
            write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
        } catch (org.apache.thrift.TException te) {
            throw new java.io.IOException(te);
        }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
        try {
            read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
        } catch (org.apache.thrift.TException te) {
            throw new java.io.IOException(te);
        }
    }

    private static class MmsSignStandardSchemeFactory implements SchemeFactory {
        public MmsSignStandardScheme getScheme() {
            return new MmsSignStandardScheme();
        }
    }

    private static class MmsSignStandardScheme extends StandardScheme<MmsSign> {

        public void read(org.apache.thrift.protocol.TProtocol iprot, MmsSign struct) throws org.apache.thrift.TException {
            org.apache.thrift.protocol.TField schemeField;
            iprot.readStructBegin();
            while (true)
            {
                schemeField = iprot.readFieldBegin();
                if (schemeField.type == org.apache.thrift.protocol.TType.STOP) {
                    break;
                }
                switch (schemeField.id) {
                    case 1: // APP_KEY
                        if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                            struct.appKey = iprot.readString();
                            struct.setAppKeyIsSet(true);
                        } else {
                            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 2: // SIGN
                        if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
                            struct.sign = iprot.readString();
                            struct.setSignIsSet(true);
                        } else {
                            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    case 3: // SIGN_PAR_NAMES
                        if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
                            {
                                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                                struct.signParNames = new ArrayList<String>(_list0.size);
                                for (int _i1 = 0; _i1 < _list0.size; ++_i1)
                                {
                                    String _elem2;
                                    _elem2 = iprot.readString();
                                    struct.signParNames.add(_elem2);
                                }
                                iprot.readListEnd();
                            }
                            struct.setSignParNamesIsSet(true);
                        } else {
                            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                        }
                        break;
                    default:
                        org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
                }
                iprot.readFieldEnd();
            }
            iprot.readStructEnd();

            // check for required fields of primitive type, which can't be checked in the validate method
            struct.validate();
        }

        public void write(org.apache.thrift.protocol.TProtocol oprot, MmsSign struct) throws org.apache.thrift.TException {
            struct.validate();

            oprot.writeStructBegin(STRUCT_DESC);
            if (struct.appKey != null) {
                oprot.writeFieldBegin(APP_KEY_FIELD_DESC);
                oprot.writeString(struct.appKey);
                oprot.writeFieldEnd();
            }
            if (struct.sign != null) {
                oprot.writeFieldBegin(SIGN_FIELD_DESC);
                oprot.writeString(struct.sign);
                oprot.writeFieldEnd();
            }
            if (struct.signParNames != null) {
                oprot.writeFieldBegin(SIGN_PAR_NAMES_FIELD_DESC);
                {
                    oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.signParNames.size()));
                    for (String _iter3 : struct.signParNames)
                    {
                        oprot.writeString(_iter3);
                    }
                    oprot.writeListEnd();
                }
                oprot.writeFieldEnd();
            }
            oprot.writeFieldStop();
            oprot.writeStructEnd();
        }

    }

    private static class MmsSignTupleSchemeFactory implements SchemeFactory {
        public MmsSignTupleScheme getScheme() {
            return new MmsSignTupleScheme();
        }
    }

    private static class MmsSignTupleScheme extends TupleScheme<MmsSign> {

        @Override
        public void write(org.apache.thrift.protocol.TProtocol prot, MmsSign struct) throws org.apache.thrift.TException {
            TTupleProtocol oprot = (TTupleProtocol) prot;
            BitSet optionals = new BitSet();
            if (struct.isSetAppKey()) {
                optionals.set(0);
            }
            if (struct.isSetSign()) {
                optionals.set(1);
            }
            if (struct.isSetSignParNames()) {
                optionals.set(2);
            }
            oprot.writeBitSet(optionals, 3);
            if (struct.isSetAppKey()) {
                oprot.writeString(struct.appKey);
            }
            if (struct.isSetSign()) {
                oprot.writeString(struct.sign);
            }
            if (struct.isSetSignParNames()) {
                {
                    oprot.writeI32(struct.signParNames.size());
                    for (String _iter4 : struct.signParNames)
                    {
                        oprot.writeString(_iter4);
                    }
                }
            }
        }

        @Override
        public void read(org.apache.thrift.protocol.TProtocol prot, MmsSign struct) throws org.apache.thrift.TException {
            TTupleProtocol iprot = (TTupleProtocol) prot;
            BitSet incoming = iprot.readBitSet(3);
            if (incoming.get(0)) {
                struct.appKey = iprot.readString();
                struct.setAppKeyIsSet(true);
            }
            if (incoming.get(1)) {
                struct.sign = iprot.readString();
                struct.setSignIsSet(true);
            }
            if (incoming.get(2)) {
                {
                    org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
                    struct.signParNames = new ArrayList<String>(_list5.size);
                    for (int _i6 = 0; _i6 < _list5.size; ++_i6)
                    {
                        String _elem7;
                        _elem7 = iprot.readString();
                        struct.signParNames.add(_elem7);
                    }
                }
                struct.setSignParNamesIsSet(true);
            }
        }
    }

}

