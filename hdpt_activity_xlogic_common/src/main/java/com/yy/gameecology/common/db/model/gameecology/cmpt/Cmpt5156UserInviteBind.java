package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-11 12:07
 **/
@Data
@TableColumn(underline = true)
public class Cmpt5156UserInviteBind implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5156_user_invite_bind";


    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5156UserInviteBind> ROW_MAPPER = null;

    private Long Id;
    /**
     * act_id
     */
    private Long actId;

    /**
     * cmpt_use_inx
     */
    private Long cmptUseInx;

    /**
     * uid
     */
    private Long uid;

    /**
     * 邀请成功首个人时，用户绑定的手机号hash
     */
    private String mobileHash;

    /**
     * 邀请成功首个人时，用户的设备号
     */
    private String hdid;

    /**
     * create_time
     */
    private Date createTime;
}
