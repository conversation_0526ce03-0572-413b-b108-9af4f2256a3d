package com.yy.gameecology.hdzj.element.component;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjHelper;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PhaseScoreAwardComponentAttr;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * 按阶段所在分值做奖励的组件
 *
 * <AUTHOR>
 * @date 2021/4/14 17:23
 */
@UseRedisStore(NeedChange = true)
@Component
public class PhaseScoreAwardComponent extends BaseActComponent<PhaseScoreAwardComponentAttr> {


    @Override
    public Long getComponentId() {
        return ComponentId.PHASE_SCORE_AWARD;
    }

    /**
     * 响应榜单变化事件，按所达分值区间发放奖励， 本函数做了防重检查，每个分值区间只能执行一次
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, PhaseScoreAwardComponentAttr attr) {
        // 先检查是否要处理的
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (!attr.isMyDuty(rankId, phaseId)) {
            return;
        }

        // 查找当前分值对应的等级分值
        log.info("onRankingScoreChanged done@event:{}, attr:{}", event, attr);
        long newLevelScore = attr.findAwardLevelScore(event.getPhaseScore());
        if (newLevelScore <= 0) {
            return;
        }

        // 提取奖励接收者
        int receiverInx = attr.getReceiverInx();
        long receiver = Long.parseLong(event.getMember().split("\\|")[receiverInx]);

        // 若设置操作返回的老值大于或等于新值，则设置失败，直接返回
        String subName = HdzjHelper.getRankingScoreChangedSubKey(event, true);
        String hashKey = makeKey(attr, subName + ":CurrLevelScore");
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        //TODO 这里如果要幂等，则要传入相同seq时，返回相同值
        long oldLevelScore = actRedisDao.hSetGrownReturnOld(groupCode, hashKey, String.valueOf(receiver), newLevelScore);
        if (oldLevelScore >= newLevelScore) {
            return;
        }

        // 对在 (oldLevelScore, newLevelScore] 这个区间的所有阶梯奖励进行发放操作
        Date now = commonService.getNow(attr.getActId());
        String time = DateUtil.format(now);
        Map<Long, Map<Long, Map<Long, Integer>>> awardConfigMap = attr.getSubScoreAwardConfig(oldLevelScore, newLevelScore);
        int index = 0;
        for (Long levelScore : awardConfigMap.keySet()) {
            String seq = event.getSeq() + StringUtil.UNDERSCORE + index;
            Map<Long, Map<Long, Integer>> taskPackageIds = awardConfigMap.get(levelScore);
            hdztAwardServiceClient.doBatchWelfare(receiver, taskPackageIds, time, attr.getRetry(), Maps.newHashMap());
            index++;
        }
    }
}
