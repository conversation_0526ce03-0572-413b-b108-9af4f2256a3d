package com.yy.gameecology.activity.worker.subscriber;

import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.SendGiftComboEvent;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.service.ActServiceManager;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * 三模板道具消费事件 统一入口
 *
 * <AUTHOR> 2019/8/23
 */
@Component
public class SendGiftConsumer {

    private static final Logger log = LoggerFactory.getLogger(SendGiftConsumer.class);

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    /**
     * @param sendGiftEvent
     * @throws IllegalArgumentException 参数异常
     */
    public void onMessage(SendGiftEvent sendGiftEvent) {
        checkEvent(sendGiftEvent);
        threadPoolManager.get("sys_important").execute(() -> handleMessage(sendGiftEvent));
    }

    public void onMessage(SendGiftComboEvent sendGiftComboEvent) {
        threadPoolManager.get("sys_important").execute(() -> handleMessage(sendGiftComboEvent));
    }

    private void checkEvent(SendGiftEvent sendGiftEvent) {
        Assert.notNull(sendGiftEvent, "sendGiftEvent is null");
        Assert.notNull(sendGiftEvent.getTemplate(), "sendGiftEvent.getTemplate is null");
        Assert.notNull(sendGiftEvent.getSendUid(), "sendGiftEvent.getSendUid is null");
        Assert.notNull(sendGiftEvent.getRecvUid(), "sendGiftEvent.getRecvUid is null");
        Assert.notNull(sendGiftEvent.getGiftId(), "sendGiftEvent.getGiftId is null");
        Assert.notNull(sendGiftEvent.getGiftNum(), "sendGiftEvent.getGiftNum is null");
        Assert.notNull(sendGiftEvent.getSid(), "sendGiftEvent.getSid is null");
        Assert.notNull(sendGiftEvent.getSsid(), "sendGiftEvent.getSsid is null");
        Assert.notNull(sendGiftEvent.getEventTime(), "sendGiftEvent.getEventTime is null");
    }

    private void handleMessage(SendGiftEvent sendGiftEvent) {
        try {
            //触发所有在活动时间内的活动实现类
            actServiceManager.invokeAllEffectActSendGiftEvent(sendGiftEvent);
        } catch (Throwable e) {
            log.error("[handleMessage invokeAllEffectActSendGiftEvent] err message:{} sendGiftEvent:{}", e.getMessage(), sendGiftEvent, e);
        }

        try {
            hdzjEventDispatcher.notify(sendGiftEvent);
        } catch (Throwable e) {
            log.error("[handleMessage notify] err message:{} sendGiftEvent:{}", e.getMessage(), sendGiftEvent, e);
        }
    }

    private void handleMessage(SendGiftComboEvent sendGiftComboEvent) {
        try {
            hdzjEventDispatcher.notify(sendGiftComboEvent);
        } catch (Throwable e) {
            log.error("[handleMessage notify] err message:{} sendGiftComboEvent:{}", e.getMessage(), sendGiftComboEvent, e);
        }
    }

}
