package com.yy.gameecology.activity.service.datatransfer.impl;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.KeyCopyResult;
import com.yy.gameecology.activity.service.datatransfer.RedisDataCopy;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-08-22 20:16
 **/
@Component
public class RedisListDataCopy  extends BaseRedisData implements RedisDataCopy {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final int BATCH_SCAN_SIZE = SysEvHelper.isDeploy() ? 500 : 5;



    @Override
    public KeyCopyResult copy(String oldRedisGroup, String newRedisGroup, String key) {
        Clock clock = new Clock();

        //如果key已经存在则先删除
        if(checkHashKey(newRedisGroup, key)){
            actRedisGroupDao.del(newRedisGroup, key);
        }

        int start = 0;
        int end = BATCH_SCAN_SIZE;
        List<String> listData = Lists.newArrayList();
        List<String> result = actRedisGroupDao.lrange(oldRedisGroup, key, start, end);
        while (CollectionUtils.isNotEmpty(result)) {
            listData.addAll(result);

            start = start + BATCH_SCAN_SIZE + 1;
            end = start + BATCH_SCAN_SIZE;
            result = actRedisGroupDao.lrange(oldRedisGroup, key, start, end);
        }


        List<List<String>> batchData = Lists.partition(listData, BATCH_SCAN_SIZE);
        batchData.forEach(p -> {
            actRedisGroupDao.rPush(newRedisGroup, key, p.toArray(new String[0]));
        });


        Long seconds = actRedisGroupDao.getExpire(oldRedisGroup, key);
        if (Convert.toLong(seconds, 0) > 0) {
            actRedisGroupDao.expire(newRedisGroup, key, seconds);
        }

        log.info("copy ok,old group:{},new group:{},key:{},size:{},cost:{}", oldRedisGroup, newRedisGroup, key, listData.size(), clock.tag());

        if (Convert.toLong(seconds, 0) <= 0) {
            log.warn("key expire time warn,key:{}", key);
        }
        KeyCopyResult copyResult = new KeyCopyResult();
        copyResult.setKey(key);
        copyResult.setExpireSeconds(seconds);
        return copyResult;
    }
}
