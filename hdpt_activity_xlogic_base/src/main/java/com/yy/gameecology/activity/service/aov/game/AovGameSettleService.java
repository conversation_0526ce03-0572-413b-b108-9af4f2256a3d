package com.yy.gameecology.activity.service.aov.game;

import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.aov.AovPushService;
import com.yy.gameecology.activity.service.wzry.GameGatewayService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.AovGameExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovGameTeamExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovGame;
import com.yy.gameecology.common.db.model.gameecology.aov.AovGameTeam;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.component.xmodule.aov.AovGameComponent;
import com.yy.gameecology.hdzj.element.component.attr.AovGameComponentAttr;
import com.yy.thrift.saibao.CreateRoomVO;
import com.yy.thrift.saibao.QuickMatchRoomInfo;
import com.yy.thrift.saibao.QuickMatchRoomInfoRsp;
import com.yy.thrift.saibao.RoomGameResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AovGameSettleService {

    @Resource
    private AovPhaseMapper aovPhaseMapper;

    @Resource
    private AovGameExtMapper aovGameExtMapper;

    @Resource
    private AovGameTeamExtMapper aovGameTeamExtMapper;

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Autowired
    private AovPushService aovPushService;

    @Autowired
    private GameGatewayService gameGatewayService;

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private AovGameTransService aovGameTransService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    /**
     * 创建尚未到达开始时间的比赛
     * @param attr
     * @param now
     */
    public void createRemoteGames(AovGameComponentAttr attr, Date now) {
        Long phaseId = aovPhaseMapper.selectProcessingPhaseId(attr.getActId(), now, AovConst.PhaseState.INITIALIZED);
        if (phaseId == null || phaseId < 0) {
            return;
        }

        List<AovGame> games = aovGameExtMapper.selectGames(phaseId, null, now, null, AovConst.GameState.INIT, null);
        if (CollectionUtils.isEmpty(games)) {
            return;
        }

        for (AovGame game : games) {
            createRemoteGame(attr, game, now);
        }
    }

    private void createRemoteGame(AovGameComponentAttr attr, AovGame game, Date now) {
        int rs = aovGameExtMapper.updateGameState(game.getId(), AovConst.GameState.INIT, AovConst.GameState.CREATING, null, null, null);
        if (rs <= 0) {
            return;
        }

        try {
            CreateRoomVO room = saiBaoClient.createRoom("王者荣耀巅峰赛", game.getBattleMode(), 1, true);
            if (room == null || StringUtils.isEmpty(room.roomId)) {
                rs = aovGameExtMapper.updateGameState(game.getId(), AovConst.GameState.CREATING, AovConst.GameState.INIT, null, null, null);
                log.error("createRemoteGame fail rollback game state gameId:{} room:{} rs:{}", game.getId(), room, rs);
                return;
            }

            rs = aovGameExtMapper.updateGameState(game.getId(), AovConst.GameState.CREATING, AovConst.GameState.CREATED, room.roomId, null, null);
            log.info("createRemoteGame success with gameId:{} roomId:{} rs:{}", game.getId(), room.roomId, rs);

            // 发送比赛直播的如流消息
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
                String url = saiBaoClient.queryLiveRoomInfo(room.roomId);
                if (StringUtils.isNotEmpty(url)) {
                    aovPushService.sendGameLiveInfo(attr.getActId(), game, url);
                }
            });

        } catch (Exception e) {
            rs = aovGameExtMapper.updateGameState(game.getId(), AovConst.GameState.CREATING, AovConst.GameState.INIT, null, null, null);
            log.error("createRemoteGame fail rollback game state gameId:{} rs:{}", game.getId(), rs, e);
        }
    }

    /**
     * 检查赛宝房间是否双方皆完成准备
     * @param attr
     * @param now
     */
    public void checkSaibaoRoomReady(AovGameComponentAttr attr, Date now) {
        Long phaseId = aovPhaseMapper.selectProcessingPhaseId(attr.getActId(), now, AovConst.PhaseState.INITIALIZED);
        if (phaseId == null || phaseId < 0) {
            return;
        }

        Date floorStartTime = DateUtils.addSeconds(now, (int) -attr.getCheckSaibao().toSeconds());
        List<AovGame> games = aovGameExtMapper.selectGames(phaseId, floorStartTime, null, null, AovConst.GameState.CREATED, null);
        if (CollectionUtils.isEmpty(games)) {
            return;
        }

        StringRedisTemplate redisTemplate = actRedisGroupDao.getRedisTemplate(redisConfigManager.getGroupCode(attr.getActId()));

        for (AovGame game : games) {
            String lockKey = BaseActComponent.makeKey(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), String.format("check_saibao_ready:%d", game.getId()));
            boolean set = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 1, TimeUnit.MINUTES));
            if (!set) {
                continue;
            }
            try {
                doCheckSaibaoRoomReady(attr, game, now);
            } catch (Exception e) {
                log.error("checkSaibaoRoomReady exception: ", e);
                redisTemplate.delete(lockKey);
            }
        }
    }

    private void doCheckSaibaoRoomReady(AovGameComponentAttr attr, AovGame game, Date now) {
        long saibaoQueryUid = Const.GEPM.getParamValueToLong(GeParamName.SAI_BAO_QUERY_ROOM_UID, 0);
        QuickMatchRoomInfoRsp rsp = gameGatewayService.queryRoomInfoTryFixUid(game.getId(), game.getChildId(), saibaoQueryUid);
        if (rsp == null) {
            return;
        }

        QuickMatchRoomInfo roomInfo = rsp.getRoomInfo();
        if (roomInfo == null) {
            log.error("doCheckSaibaoRoomReady roomInfo is empty,id:{},childId:{}", game.getId(), game.getChildId());
            return;
        }

        if (roomInfo.getSchState() == AovConst.SchState.NOT_READY) {
            Date readyDeadTime = DateUtils.addMinutes(game.getStartTime(), attr.getSaibaoReadyMin());
            if (now.after(readyDeadTime) && roomInfo.getStatus() != 10000) {
                boolean closed = saiBaoClient.closeRoom(game.getChildId());
                if (closed) {
                    aovGameTransService.saveShuttingGame(game, roomInfo, now);
                }
            }

            return;
        }

        if (roomInfo.getSchState() == AovConst.SchState.READY_GAMING || roomInfo.getSchState() == AovConst.SchState.CLOSED) {
            aovGameTransService.saveReadyGame(game, roomInfo, now);
            return;
        }

        if (roomInfo.getStatus() != 10000) {
            log.error("doCheckSaibaoRoomReady get unexpected battle schState:{} battleState:{} status:{}", roomInfo.getSchState(), roomInfo.getBattleState(), roomInfo.getStatus());
        }
    }

    /**
     * 检查shutting装填的比赛是否有比赛结果
     * @param attr
     * @param now
     */
    public void checkShuttingGames(AovGameComponentAttr attr, Date now) {
        Long phaseId = aovPhaseMapper.selectProcessingPhaseId(attr.getActId(), now, AovConst.PhaseState.INITIALIZED);
        if (phaseId == null || phaseId < 0) {
            return;
        }

        Date floorStartTime = DateUtils.addMinutes(now, -(attr.getSaibaoReadyMin() + attr.getGameCountdownMin() + 1));
        List<AovGame> games = aovGameExtMapper.selectGames(phaseId, floorStartTime, null, null, AovConst.GameState.SHUTTING, null);

        if (CollectionUtils.isEmpty(games)) {
            return;
        }

        for (AovGame game : games) {
            checkShuttingGame(attr, game, now);
        }
    }

    public void checkShuttingGame(AovGameComponentAttr attr, AovGame game, Date now) {
        long saibaoQueryUid = Const.GEPM.getParamValueToLong(GeParamName.SAI_BAO_QUERY_ROOM_UID, 0);
        RoomGameResultVO resultVO = gameGatewayService.queryGameResultTryFixUid(game.getId(), game.getChildId(), saibaoQueryUid);

        //提前判断  没有结果，没开起来
        if (resultVO == null || resultVO.getBattleStatus() < AovConst.BattleState.GAME_CREATED) {
            List<AovGameTeam> gameTeams = aovGameTeamExtMapper.selectGameTeams(game.getId(), null);
            aovGameTransService.saveShutResult(game, gameTeams, now);
            return;
        }

        // 有结果
        if (resultVO.getBattleStatus() == AovConst.BattleState.SETTLED) {
            aovGameTransService.saveReviveGameResult(game, resultVO.getResultFlag(), resultVO.getTeamMap(), attr.getActId(), now);
            return;
        }

        Date ultimateSettleTime = DateUtils.addMinutes(game.getStartTime(), attr.getUltimateSettleMin());
        if (now.after(ultimateSettleTime)) {
            List<AovGameTeam> gameTeams = aovGameTeamExtMapper.selectGameTeams(game.getId(), null);
            aovGameTransService.saveShutResult(game, gameTeams, now);
        }
    }

    /**
     * 针对变成了 ready 的比赛，查比赛结果，如果有比赛结果就保存比赛结果
     * @param attr
     * @param now
     */
    public void queryGamesResult(AovGameComponentAttr attr, Date now) {
        Long phaseId = aovPhaseMapper.selectProcessingPhaseId(attr.getActId(), now, AovConst.PhaseState.INITIALIZED);
        if (phaseId == null || phaseId < 0) {
            return;
        }

        Date floorStartTime = DateUtils.addSeconds(now, (int) -attr.getCheckGameResult().toSeconds());
        List<AovGame> games = aovGameExtMapper.selectGames(phaseId, floorStartTime, null, null, AovConst.GameState.SAIBAO_READY, null);
        if (CollectionUtils.isEmpty(games)) {
            return;
        }

        StringRedisTemplate redisTemplate = actRedisGroupDao.getRedisTemplate(redisConfigManager.getGroupCode(attr.getActId()));
        for (AovGame game : games) {
            String lockKey = BaseActComponent.makeKey(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), String.format("query_game_result:%d", game.getId()));
            boolean set = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 60, TimeUnit.SECONDS));

            if (!set) {
                return;
            }

            try {
                long saibaoQueryUid = Const.GEPM.getParamValueToLong(GeParamName.SAI_BAO_QUERY_ROOM_UID, 0);
                RoomGameResultVO resultVO = gameGatewayService.queryGameResultTryFixUid(game.getId(), game.getChildId(), saibaoQueryUid);
                if (resultVO == null || resultVO.getBattleStatus() != AovConst.BattleState.SETTLED) {
                    // 超过最终结算时间还没有结果
                    Date ultimateSettleTime = DateUtils.addMinutes(game.getStartTime(), attr.getUltimateSettleMin());
                    if (now.after(ultimateSettleTime)) {
                        if (notSettledButHasGameResult(resultVO)) {
                            log.warn("notSettledButHasGameResult,gameId:{},childId:{},resultVO:{}", game.getId(), game.getChildId(), resultVO);
                            aovGameTransService.saveGameResult(game, resultVO.getResultFlag(), resultVO.getTeamMap(), attr.getActId(), now);
                        } else {
                            aovGameTransService.saveNoResult(game, now);
                        }
                    }
                    continue;
                }

                aovGameTransService.saveGameResult(game, resultVO.getResultFlag(), resultVO.getTeamMap(), attr.getActId(), now);
            } catch (Exception e) {
                log.error("queryGamesResult exception,game id:{},e:{}", game.getId(), e.getMessage(), e);
                redisTemplate.delete(lockKey);
            }
        }
    }

    private boolean notSettledButHasGameResult(RoomGameResultVO resultVO) {
        if (resultVO == null) {
            return false;
        }
        boolean hasResult = resultVO.getTeamMap() != null
                && resultVO.getTeamMap().values().stream().filter(Objects::nonNull).anyMatch(p -> p.getScore() > 0);
        return hasResult && resultVO.getBattleStatus() == AovConst.BattleState.SETTLING;
    }

    /**
     * 结算单场比赛结果到 match_node
     * @param attr
     * @param now
     */
    public void settleResultedGames(AovGameComponentAttr attr, Date now) {
        Long phaseId = aovPhaseMapper.selectProcessingPhaseId(attr.getActId(), now, AovConst.PhaseState.INITIALIZED);
        if (phaseId == null || phaseId < 0) {
            return;
        }

        Date floorStartTime = DateUtils.addSeconds(now, (int) -attr.getSettleGame().toSeconds());
        Date ceilStartTime = DateUtils.addHours(now, -6);
        List<AovGame> games = aovGameExtMapper.selectGames(phaseId, floorStartTime, ceilStartTime, AovConst.GameSettleState.INIT, null, AovGameComponent.CLOSE_GAME_STATES);
        if (CollectionUtils.isEmpty(games)) {
            return;
        }

        for (AovGame game : games) {
            List<AovGameTeam> teams = aovGameTeamExtMapper.selectGameTeams(game.getId(), null);
            Assert.isTrue(teams.size() == 2, "team's size must be 2");
            aovGameTransService.saveSettleResult(game, teams, attr.getActId(), now);
        }
    }
}
