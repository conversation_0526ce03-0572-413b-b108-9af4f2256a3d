package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.service.datatransfer.RedisDataTransferService;
import com.yy.gameecology.activity.worker.timer.DataArchiveTimer;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-08-30 16:35
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/application-inner.properties", "classpath:env/local/group-setting-1.properties"})
public class RedisDataTransferServiceTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private RedisDataTransferService redisDataTransferService;

    @Autowired
    private DataArchiveTimer dataArchiveTimer;

    @Test
    public void copyTest() {
        redisDataTransferService.copyRedisData(2025051001L);
    }

    @Test
    public void test() {
        dataArchiveTimer.runCopyRedisData();
    }
}
