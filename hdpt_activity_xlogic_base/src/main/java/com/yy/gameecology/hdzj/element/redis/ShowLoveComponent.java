package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSortedMap;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.mq.PwOrderEvent;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ShowLoveComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Map;
import java.util.UUID;


/**
 * @Author: CXZ
 * @Desciption: 告白特效
 * @Date: 2021/4/15 16:46
 * @Modified:
 */
@UseRedisStore
@Component
public class ShowLoveComponent extends BaseActComponent<ShowLoveComponentAttr> {
    @Autowired
    private ActInfoService actInfoService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private BroadCastHelpService broadCastHelpService;

    public static final String SHOW_LOVE_RECORD = "show_love_record_%s_%s";

    @Override
    public Long getComponentId() {
        return ComponentId.SHOW_LOVE;
    }


    @HdzjEventHandler(value = PwOrderEvent.class, canRetry = false)
    public void showLove(PwOrderEvent event, ShowLoveComponentAttr attr) {
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            return;
        }

        BusiId busiId = BusiId.PEI_WAN;
        Long userUid = event.getUserUid();
        Long anchorUid = event.getPeipeiUid();

        String seq = event.getOrderId();
        long sid = event.getSid();
        long opSid = event.getOpSid();
        if (opSid != 0) {
            sid = opSid;
        }
        Long ssid = 0L;

        String giftId = attr.getGiftIds().get(0);
        //单位: 厘
        long giftNum = 0;
        final String moneyStr = "money", payMoneyStr = "payMoney";
        if (moneyStr.equals(giftId)) {
            giftNum = event.getMoney();
        } else if (payMoneyStr.equals(giftId)) {
            giftNum = event.getPayMoney();
        } else {
            return;
        }

        sendBroadcast(attr, busiId, userUid, anchorUid, giftId, giftNum / 1000, seq, sid, ssid);
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = false)
    public void showLove(SendGiftEvent event, ShowLoveComponentAttr attr) {
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            return;
        }
        log.info("event={}", JSON.toJSONString(event));
        BusiId busiId = BusiId.findByValue(attr.getBusiId());
        Long userUid = event.getSendUid();
        Long anchorUid = event.getRecvUid();
        String giftId = event.getGiftId();
        Long giftNum = event.getGiftNum();
        String seq = event.getSeq();
        Long sid = event.getSid();
        Long ssid = event.getSsid();

        if (!attr.getGiftIds().contains(giftId)) {
            return;
        }
        sendBroadcast(attr, busiId, userUid, anchorUid, giftId, giftNum, seq, sid, ssid);
    }

    public void sendBroadcast(ShowLoveComponentAttr attr, BusiId busiId, Long userUid, Long anchorUid, String giftId, Long giftNum, String seq, Long sid, Long ssid) {
        long actId = attr.getActId();
        //活动时间
        Date now = commonService.getNow(actId);
        Map.Entry<Long, Integer> levelEntry = ImmutableSortedMap.copyOf(attr.getNumLevelMap()).floorEntry(giftNum);
        if (levelEntry == null) {
            log.info("showLove ignore actId:{} uid:{} anchor:{} giftId:{} giftNum:{} ,seq:{}", actId, userUid, anchorUid, giftId, giftNum, seq);
            return;
        }
        int level = levelEntry.getValue();
        int broadcastType = attr.getLevelBorTypeMap().get(level);

        // 广播 Act202008_ShowLoveEffect
        GameecologyActivity.Act202008_ShowLoveEffect.Builder showLoveEffect = GameecologyActivity.Act202008_ShowLoveEffect.newBuilder()
                .setActId(actId)
                .setUser(broadCastHelpService.getUserInfo(userUid, busiId))
                .setAnchor(broadCastHelpService.getAnchorInfo(actId, anchorUid, false, busiId))
                .setGift(broadCastHelpService.getGiftInfo(giftId, giftNum, attr.getGiftName(), attr.getGiftIcon()))
                .setSid(sid)
                .setSsid(ssid)
                .setAsid(commonService.getAsid(sid))
                .setLevel(level);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202008_ShowLoveEffect_VALUE)
                .setAct202008ShowLoveEffect(showLoveEffect)
                .build();

        broadCastHelpService.broadcast(actId, busiId, broadcastType, sid, ssid, msg);
        log.info("showLove  uid:{} seq:{} sid:{} ssid:{}, msg:{}", userUid, seq, sid, ssid, JsonFormat.printToString(msg));

        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String recordKey = makeKey(attr, String.format(SHOW_LOVE_RECORD, busiId, day));
        String groupCode = redisConfigManager.getGroupCode(actId);
        actRedisDao.hIncrByKey(groupCode, recordKey, String.valueOf(level), 1);

        bigDataService.saveNoRankDataToFile(attr.getActId(), busiId, now.getTime(), userUid + "", RoleType.USER, broadcastType
                , 12, anchorUid + "");
    }

    public void test(long actId, int index, long busiId, long uid, long anchorUid, long sid, long ssid, long num) {
        ShowLoveComponentAttr attr = getComponentAttr(actId, index);
        Assert.notNull(attr, "not find attr,actId=" + actId + ",cmptUseInx=" + index);

        String seq = UUID.randomUUID().toString();
        String giftId = attr.getGiftIds().get(0);
        BusiId busiIdEmun = BusiId.findByValue((int) busiId);
        sendBroadcast(attr, busiIdEmun, uid, anchorUid, giftId, num, seq, sid, ssid);

    }
}
