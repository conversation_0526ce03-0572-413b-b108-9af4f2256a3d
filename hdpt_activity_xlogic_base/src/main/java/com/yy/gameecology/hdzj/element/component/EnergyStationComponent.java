package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.AnchorStartShowEvent;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardBean;
import com.yy.gameecology.hdzj.bean.EnergyTaskItem;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.EnergyStationComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.EnergyStationComponentAttr.TaskLevelInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztranking.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 能量站组件类
 *
 * <AUTHOR>
 * @date 2022/07/11
 */
@Component
@RestController
@RequestMapping("/cmpt/EnergyStation")
public class EnergyStationComponent extends BaseActComponent<EnergyStationComponentAttr> {

    // 成员完成的 能量站任务积分 key
    public static final String ENERGY_STATION_SCORE_KEY = "energy_station_score";

    // 主持能量站获得的奖励 hash {uid,count} hset
    public static final String ENERGY_STATION_AWARD_KEY = "energy_station_award";

    // 主持能量站已用紫水晶数量
    public static final String ENERGY_STATION_AWARD_USED_KEY = "energy_station_award_used";

    // 主播活动期间首次开播弹窗提示能量站
    public static final String FIRST_SHOW_TIPS_KEY = "first_show_tips";

    public static final String LUA_SCRIPT = "set_max_return_old.lua";

    @Autowired
    protected EnrollmentNewService enrollmentService;

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private SignedService signedService;

    @Autowired
    protected UserInfoService userInfoService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.ENERGY_STATION;
    }

    /**
     * 发放奖励定时器
     */
    @Scheduled(cron = "*/5 * * * * ? ")
    @NeedRecycle(author = "guoliping", notRecycle = true)
    public void giveAwards() {
        this.giveAwards(2, 3600, 60 * 24);
    }

    /**
     * 主播开播提示能量站玩法信息
     */
    @HdzjEventHandler(value = AnchorStartShowEvent.class, canRetry = true)
    public void anchorStartShowHint(AnchorStartShowEvent event, EnergyStationComponentAttr attr) {
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            return;
        }

        long uid = event.getUid();

        //帽子不弹窗
        if (ftsBaseInfoBridgeClient.isHatCompere(uid)) {
            log.info("isHatCompere,return,uid:{}", uid);
            return;
        }
        //未签约不弹窗
        ChannelInfoVo signInfo = signedService.getSignedInfo(uid, com.yy.gameecology.common.bean.Template.makefriend.getCode());
        if (signInfo == null || signInfo.getSid() == 0) {
            log.info("isHatCompere,return,uid:{}", uid);
            return;
        }


        String key = getAnchorFirstShowTipsKey(attr);
        String groupCode = this.getRedisGroupCode(actId);
        if (actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss())) {
            svcSDKService.unicastUid(uid, getNoticeMsg(actId, "anchor_first_tip", attr.getAnchorFirstTip()));
            log.info("anchorStartShowHint done@uid:{} tip:{}", uid, attr.getAnchorFirstTip());
        }
    }

    /**
     * 监听分值, 处理主持能量站问题, 所有奖励都在这里处理
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void handleRankingScoreChanged(RankingScoreChanged event, EnergyStationComponentAttr attr) {
        long rankId = event.getRankId();
        if (rankId != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        // 能量站任务累分
        long newScore = event.getPhaseScore();
        List<String> keys = Arrays.asList(makeKey(attr, ENERGY_STATION_SCORE_KEY));
        List<String> argv = Arrays.asList(newScore + "", event.getMember());
        String groupCode = getRedisGroupCode(attr.getActId());
        Long oldScore = actRedisDao.executeLua(groupCode, LUA_SCRIPT, Long.class, keys, argv);

        //旧的总分大于当前总分,则说明已处理过,
        if (oldScore >= newScore) {
            log.info("handleRankingScoreChanged skip event:{} oldScore:{}, newScore:{}", JSON.toJSONString(event), oldScore, newScore);
            return;
        }

        //使用增量加
        processTaskAward(event.getBusiId(), event.getSeq(), newScore, oldScore, event.getMember(), attr);
    }

    @GetMapping("/myStatus")
    public Response<JSONObject> myStatus(long actId, Integer useInxGroup, String memberId) {
        if (useInxGroup == null) {
            useInxGroup = 1;
        }

        // 先获取剩余奖励数
        JSONObject data = new JSONObject();
        long awardLeft = getAwardLeft(actId, useInxGroup);
        //全服剩余紫水晶数
        data.put("energyAwardLeft", awardLeft);

        // 获取成员对应的属性对象
        memberId = StringUtil.trim(memberId);
        EnergyStationComponentAttr attr = getMemberComponentAttr(actId, useInxGroup, memberId);
        if (memberId.isEmpty() || attr == null) {
            // data.put("validAnchor", 0);
            data.put("validMember", 0);
            return Response.success(data);
        }

        // 获取成员任务情况 和 奖励情况
        String key = makeKey(attr, ENERGY_STATION_AWARD_KEY);
        String groupCode = getRedisGroupCode(actId);
        long award = Convert.toLong(actRedisDao.hget(groupCode, key, memberId));
        data.put("award", award);
        data.put("taskList", getEnergyTaskItems(attr));
        // data.put("anchorType", attr.getMemberType());
        data.put("memberType", attr.getMemberType());
        data.put("validMember", 1);

        //任务在奖励用玩后还是可以完成
        String awardName = getAwardName(attr);
        String scoreKey = makeKey(attr, ENERGY_STATION_SCORE_KEY);
        long memberScore = actRedisDao.zscore(groupCode, scoreKey, memberId);
        if (attr.isTopScoreLevel(memberScore)) {
            data.put("energyShowTitle", true);
            if (isTopAwardLevel(attr, memberId)) {
                data.put("energyTitle", " 已完成最高等级!");
            } else if (awardLeft <= 0) {
                data.put("energyTitle", " (奖池已用完,升级不再获得奖励)");
            }
        } else { //未完成能量站任务
            //判断是否在奖池用完,若是,展示文案
            if (awardLeft <= 0) {
                data.put("energyShowTitle", true);
                data.put("energyTitle", " (奖池已用完,升级不再获得奖励)");
            } else {
                long leftCount = attr.getNextLevelLeftScore(memberScore);
                //距离完成当前关卡需要的数量
                data.put("energyCountLeft", leftCount);
                data.put("energyShowTitle", false);
            }
        }

        return Response.success(data);
    }


    /**
     * 挂件展示扩展信息
     */
    public Map<String, Object> buildLayerExtInfo(long actId, long roleId, String memberId, int index) {
        Map<String, Object> extMap = Maps.newHashMap();

        long awardLeft = getAwardLeft(actId, index);
        EnergyStationComponentAttr attr = this.getRoleComponentAttr(actId, index, roleId);
        //已获得
        extMap.put("energyTaskAward", this.getMyAwardCount(attr, memberId));
        extMap.put("energyAwardLeft", awardLeft);

        if (attr != null) {
            long memberScore = this.getMemberScore(attr, memberId);
            extMap.put("energyScore", memberScore);

            if (attr.isTopScoreLevel(memberScore)) {
                extMap.put("energyShowTitle", true);
                boolean bTopAwardLevel = this.isTopAwardLevel(attr, memberId);
                extMap.put("energyTitle", bTopAwardLevel ? "已获得能量站所有奖励" : "能量站奖励已抢完");
            } else {
                boolean bAwardLeft = awardLeft <= 0;
                long leftCount = attr.getNextLevelLeftScore(memberScore);
                extMap.put("energyCountLeft", leftCount);
                extMap.put("energyShowTitle", bAwardLeft);
                if (bAwardLeft) {
                    extMap.put("energyTitle", "手慢了，奖励已抢完");
                }
            }

            // 准备能量站任务级别信息
            int maxLev = attr.getTaskLevelInfos().size();
            int myLev = attr.calcScoreLevel(memberScore);
            int nextLev = myLev >= maxLev ? maxLev : myLev + 1;
            EnergyStationComponentAttr.TaskLevelInfo taskLevelInfo = attr.getTaskLevelInfos().get(nextLev - 1);

            List<ImmutableMap<String, Long>> taskItems = this.getEnergyTaskItems(attr).stream().
                    map(x -> ImmutableMap.of("level", x.getLevel(), "passValue", x.getSingleTaskScore())).
                    collect(Collectors.toList());

            Map<String, Object> map = ImmutableMap.of("nextLevName", taskLevelInfo.getName(),
                    "nextLev", nextLev, "nextLevScore", taskLevelInfo.getScore(), "taskItems", taskItems);

            extMap.put("energyMissions", Lists.newArrayList(map));
        }
        return extMap;
    }

    public long getMemberScore(EnergyStationComponentAttr attr, String memberId) {
        String scoreKey = makeKey(attr, ENERGY_STATION_SCORE_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        return actRedisDao.zscore(groupCode, scoreKey, memberId);
    }

    public String getAwardName(EnergyStationComponentAttr attr) {
        String awardName = attr.getAwardName();
        try {
            if (StringUtil.isBlank(awardName)) {
                Map<Long, AwardModelInfo> map = hdztAwardServiceClient.queryAwardTasks(attr.getAwardTaskId());
                AwardModelInfo ami = map.get(attr.getAwardPackageId());
                if (ami != null) {
                    return ami.getPackageName();
                }
            }
        } catch (Throwable t) {
            log.error("getAwardName exception@err:{}", t.getMessage(), t);
        }
        return awardName.isEmpty() ? "奖励" : awardName;
    }

    /**
     * 获取奖励剩余额度
     */
    public long getAwardLeft(long actId, int useInxGroup) {
        long maxLimitAward = this.getMaxLimitAward(actId, useInxGroup);
        String usedKey = this.getTotalAwardUsedKey(actId, useInxGroup);
        String groupCode = redisConfigManager.getGroupCode(actId);
        long used = Convert.toLong(actRedisDao.get(groupCode, usedKey));
        long left = maxLimitAward - used;
        return left > 0 ? left : 0;
    }

    /**
     * 是否得到最高级奖励
     */
    public boolean isTopAwardLevel(EnergyStationComponentAttr attr, String memberId) {
        if (attr == null) {
            return false;
        }

        long actId = attr.getActId();
        String key = makeKey(attr, ENERGY_STATION_AWARD_KEY);
        long award = Convert.toLong(actRedisDao.hget(getRedisGroupCode(actId), key, memberId), 0);
        return attr.isTopAwardLevel(award);
    }

    /**
     * 获取个人实际获得的奖励
     */
    public long getMyAwardCount(EnergyStationComponentAttr attr, String memberId) {
        if (attr == null) {
            return 0;
        }
        long actId = attr.getActId();
        String key = makeKey(attr, ENERGY_STATION_AWARD_KEY);
        return Convert.toLong(actRedisDao.hget(getRedisGroupCode(actId), key, memberId), 0);
    }

    /**
     * 获取成员的组件属性对象
     * 1）通过能量站挂靠榜单得到采榜角色和类型，
     * 2）然后用采榜角色类型+memberId 查询报名信息得到的报名角色
     * 3）若 报名橘色 在 采榜角色 中，则找到匹配的组件属性对象实例
     *
     * @param actId       - 活动ID
     * @param useInxGroup - 实例所属的分组， 见 EnergyStationComponentAttr.useInxGroup
     * @param memberId    - 成员ID
     * @return
     */
    public EnergyStationComponentAttr getMemberComponentAttr(long actId, int useInxGroup, String memberId) {
        if (StringUtil.isBlank(memberId)) {
            return null;
        }

        List<EnergyStationComponentAttr> attrs = this.getAllComponentAttrs(actId).stream().
                filter(x -> x.getUseInxGroup() == useInxGroup).collect(Collectors.toList());

        for (EnergyStationComponentAttr attr : attrs) {
            RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, attr.getRankId());
            List<List<RoleDetail>> roles = rankingInfo.getRoleItemConfig().getRoles();
            Set<Long> roleIdSet = roles.stream().map(x -> x.get(0).getRoleId()).collect(Collectors.toSet());
            for (List<RoleDetail> role : roles) {
                if (role.size() != 1) {
                    break;
                }

                int roleType = (int) role.get(0).getRoleType();
                boolean bUserOrAny = roleType == RoleType.USER.getValue() || roleType == RoleType.ANY.getValue();
                if (bUserOrAny) {
                    if (attrs.size() == 1) {
                        return attr;
                    }
                } else {
                    EnrollmentInfo einfo = enrollmentService.tryGetFirstEnrolMemberCache(actId, 0L, roleType, memberId);
                    if (einfo != null && roleIdSet.contains(einfo.getDestRoleId())) {
                        return attr;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 通过角色ID查找组件对象实例
     * 1）通过能量站挂靠榜单得到采榜角色
     * 2）若 roleId 在采榜角色中，则找到对应的组件属性对象实例
     */
    public EnergyStationComponentAttr getRoleComponentAttr(long actId, int useInxGroup, long roleId) {
        if (roleId > 0) {
            List<EnergyStationComponentAttr> attrs = this.getAllComponentAttrs(actId).stream().
                    filter(x -> x.getUseInxGroup() == useInxGroup).collect(Collectors.toList());

            for (EnergyStationComponentAttr attr : attrs) {
                RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, attr.getRankId());
                List<List<RoleDetail>> roles = rankingInfo.getRoleItemConfig().getRoles();
                for (List<RoleDetail> role : roles) {
                    if (role.size() != 1) {
                        break;
                    }
                    if (role.get(0).getRoleId() == roleId) {
                        return attr;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 获取任务信息（为了屏蔽前端变化，这里做好转换）
     */
    public List<EnergyTaskItem> getEnergyTaskItems(EnergyStationComponentAttr attr) {
        List<TaskLevelInfo> taskLevelInfos = attr.getTaskLevelInfos();
        int size = taskLevelInfos.size();

        List<EnergyTaskItem> result = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            TaskLevelInfo info = taskLevelInfos.get(i);
            EnergyTaskItem item = new EnergyTaskItem();
            long score = info.getScore();
            long award = info.getAward();
            item.setName(info.getName());
            item.setAward(award);
            item.setReq(score);
            item.setLevel(i + 1);
            item.setRate(info.getRate());
            item.setSingleTaskScore(i == 0 ? score : score - taskLevelInfos.get(i - 1).getScore());
            item.setSingleTaskAward(i == 0 ? award : award - taskLevelInfos.get(i - 1).getAward());
            result.add(item);
        }

        return result;
    }

    /**
     * 处理任务奖励
     */
    private void processTaskAward(long busiId, String seq, long newScore, long oldScore, String memberId, EnergyStationComponentAttr attr) {
        Clock clock = new Clock();
        long actId = attr.getActId();
        long incrScoreAward = -1;
        long realAward = -1;
        long memberAward = -1;

        try {
            // 先通过任务分值计算对应的奖励值，若奖励值没有变化，直接返回
            long oldScoreAward = attr.calcScoreLevelAward(oldScore);
            long newScoreAward = attr.calcScoreLevelAward(newScore);
            incrScoreAward = newScoreAward - oldScoreAward;
            if (incrScoreAward == 0) {
                log.info("processTaskAward skip1@incr award zero, actId:{}, memberId:{}, seq:{}, newScore:{}, oldScore:{}, newScoreAward:{}",
                        actId, memberId, seq, newScore, oldScore, newScoreAward);
                return;
            }

            // 带限额检查，给指定成员增加奖励值，返回结果为数组，数组元素1为 原值， 元素2为 新增奖励后的值，限额情况下若无实际奖励则直接返回
            realAward = calcRealAward(seq, incrScoreAward, attr);
            clock.tag();
            if (realAward == 0) {
                sendInfoFlow(seq, attr);
                log.info("processTaskAward skip2@real award zero, actId:{}, memberId:{}, seq:{}, newScore:{}, oldScore:{}, incrScoreAward:{}",
                        actId, memberId, seq, newScore, oldScore, incrScoreAward);
                return;
            }

            // 保存奖励
            String time = DateUtil.format(commonService.getNow(attr.getActId()));
            AwardBean awardBean = new AwardBean(seq, busiId, memberId, time, attr.getAwardTaskId(), attr.getAwardPackageId(), (int) realAward);
            this.saveAward(attr, seq, Lists.newArrayList(awardBean));

            String groupCode = this.getRedisGroupCode(actId);
            String realAwardKey = makeKey(attr, ENERGY_STATION_AWARD_KEY);
            //TODO 此处不幂等
            memberAward = actRedisDao.hIncrByKey(groupCode, realAwardKey, memberId, realAward);
            clock.tag();

            // 奖励值有变化，上报海度
            int newAwardLevel = attr.calcAwardLevel(memberAward);
            recordEnergyAnchor(realAward, actId, attr.getMemberType(), memberId, newAwardLevel);

            // 若奖励级别有变化，则广播级别设置为最新奖励级别，否则为0（表示没有变化，不用广播）
            int oldAwardLevel = attr.calcAwardLevel(memberAward - realAward);
            if (newAwardLevel > oldAwardLevel) {
                TaskLevelInfo item = attr.getTaskLevelInfos().get(newAwardLevel - 1);
                RetryTool.withRetryCheck(actId, seq, () -> {
                    broPassEnergyTask(busiId, seq, newAwardLevel, memberId, item.getAward(), attr);
                });
            }

            log.info("processTaskAward done@actId:{}, memberId:{}, seq:{}, oldScore:{}, newScore:{}, incrScoreAward:{}, realAward:{}, memberAward:{}, oldAwardLevel:{}, newAwardLevel:{} {}",
                    actId, memberId, seq, oldScore, newScore, incrScoreAward, realAward, memberAward, oldAwardLevel, newAwardLevel, clock.tag());
        } catch (Throwable t) {
            log.info("processTaskAward exception@actId:{}, memberId:{}, seq:{}, oldScore:{}, newScore:{}, incrScoreAward:{}, realAward:{}, memberAward:{}, err:{} {}",
                    actId, memberId, seq, oldScore, newScore, incrScoreAward, realAward, memberAward, t.getMessage(), clock.tag(), t);
        }
    }

    private GameecologyActivity.GameEcologyMsg getNoticeMsg(long actId, String type, String noticeMsg) {
        GameecologyActivity.CommonNoticeResponse.Builder tip = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(type)
                .setNoticeMsg(noticeMsg);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(tip).build();
        return msg;

    }

    private void sendInfoFlow(String seq, EnergyStationComponentAttr attr) {
        long actId = attr.getActId();
        final boolean rs = actRedisDao.setNX(getRedisGroupCode(actId), makeKey(attr, "award_quota_exhausted_notify"), DateUtil.today() + ", " + seq);
        if (!rs) {
            return;
        }
        String msg = "<font color=\"red\">【能量站奖励额度已用完】</font>\n" +
                "活动ID：" + actId + "\n" +
                "实例分组：" + attr.getUseInxGroup() + "\n" +
                "奖励总数：" + this.getMaxLimitAward(actId, attr.getUseInxGroup()) + "\n" +
                "奖池ID：" + attr.getAwardTaskId() + "\n" +
                "奖包ID：" + attr.getAwardPackageId() + "(" + getAwardName(attr) + ")";
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
    }

    private void broEnergyStationCP(long busiId, String seq, long level, String memberId, long awardCount, EnergyStationComponentAttr attr) {
        Map<String, String> ext = Maps.newHashMap();
        ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, memberId);

        long actId = attr.getActId();
        long rankId = attr.getRankId();
        long cpRankId = attr.getCpRankId();
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, cpRankId, attr.getPhaseId(), "", 1, ext);
        if (CollectionUtils.isEmpty(ranks)) {
            log.error("broEnergyStationCP ranks is empty@actId:{}, seq:{}, rankId:{}, memberId:{}", actId, seq, rankId, memberId);
            return;
        }

        String member = ranks.get(0).getMember();
        com.yy.gameecology.common.bean.Template template = com.yy.gameecology.common.bean.Template.getTemplate((int) busiId);
        Map<Long, UserInfoVo> map = userInfoService.getUserInfo(Lists.newArrayList(Convert.toLong(member), Convert.toLong(memberId)), template);

        //commonService.getUserInfo(Convert.toLong(member), false);
        UserInfoVo player = map.get(Convert.toLong(member));
        //commonService.getUserInfo(Convert.toLong(memberId), false);
        UserInfoVo anchor = map.get(Convert.toLong(memberId));
        JSONObject data = new JSONObject();
        data.put("level", level);
        data.put("award", awardCount);
        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId)
                .setBannerId(8L).setAnchorLogo(anchor.getAvatarUrl()).setAnchorNick(anchor.getNick())
                .setUserLogo(player.getAvatarUrl()).setUserNick(player.getNick())
                .setJsonData(data.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        svcSDKService.broadcastTemplate(Template.Jiaoyou, msg);
        log.info("energyStation cp bro done@actId:{}, seq:{}, memberId:{}, contrib:{}, level:{}, msg:{}", actId, seq, memberId, member, level, JsonFormat.printToString(msg));
    }

    private void recordEnergyAnchor(long award, long actId, String type, String member, long level) {
        if (award == 0) {
            return;
        }
        long millis = System.currentTimeMillis();
        BusiId byValue = BusiId.findByValue(500);
        String ext = "finish_energy_" + type;
        bigDataService.saveNoRankDataToFile(actId, byValue, millis, member, RoleType.ANCHOR, level, 51001, ext, 0, 0);
    }

    /**
     * 获取本活动能量站的奖励最大限额
     */
    private long getMaxLimitAward(long actId, int useInxGroup) {
        List<EnergyStationComponentAttr> attrs = this.getAllComponentAttrs(actId);
        OptionalLong max = attrs.stream().filter(x -> x.getUseInxGroup() == useInxGroup).mapToLong(EnergyStationComponentAttr::getAwardLimit).max();
        return max.orElse(0);
    }

    private String getTotalAwardUsedKey(long actId, int useInxGroup) {
        String uig = "uig" + useInxGroup;
        String subKey = String.format(COMPONENT_KEY_TPL, this.getComponentId(), uig, ENERGY_STATION_AWARD_USED_KEY);
        return Const.addActivityPrefix(actId, subKey);
    }

    private String getAnchorFirstShowTipsKey(EnergyStationComponentAttr attr) {
        String uig = "uig" + attr.getUseInxGroup();
        String subKey = String.format(COMPONENT_KEY_TPL, this.getComponentId(), uig, FIRST_SHOW_TIPS_KEY);
        return Const.addActivityPrefix(attr.getActId(), subKey);
    }

    /**
     * 在满足总量限制情况下，获取实际能得到的奖励
     */
    private long calcRealAward(String seq, long incrAward, EnergyStationComponentAttr attr) {
        long actId = attr.getActId();
        String groupCode = this.getRedisGroupCode(actId);
        long limitAward = getMaxLimitAward(actId, attr.getUseInxGroup());
        if (limitAward > 0) {
            String key = getTotalAwardUsedKey(actId, attr.getUseInxGroup());
            List<Long> result = actRedisDao.incrValueWithLimit(groupCode, key, incrAward, limitAward, true);
            log.info("calcRealAward done@actId:{}, seq:{}, incrAward:{}, limitAward:{}, result:{}", actId, seq, incrAward, limitAward, JSON.toJSONString(result));
            long ret = result.get(0);
            long score = result.get(1);
            final int mi1 = -1, mi2 = -2, i2 = 2;
            if (ret == mi1 || ret == mi2) {
                return 0;
            } else if (ret == 1) {
                return incrAward;
            } else if (ret == i2) {
                return score;
            }
        }
        throw new SuperException("总量检查异常", SuperException.E_DATA_ERROR);
    }


    private void broPassEnergyTask(long busiId, String seq, long level, String memberId, long award, EnergyStationComponentAttr attr) {
        long actId = attr.getActId();
        broAnchorAwardTips(seq, actId, "主持能量站第" + level + "站任务", memberId, award);
        final int level4 = 4;
        if (level >= level4) {
            broEnergyStationCP(busiId, seq, level, memberId, award, attr);
        }
    }

    private void broAnchorAwardTips(String seq, long actId, String missionDesc, String member, long award) {
        JSONObject json = new JSONObject();
        json.put("desc", missionDesc);
        json.put("award", award);
        GameecologyActivity.Act202008_AnchorTips.Builder anchorTips = GameecologyActivity.Act202008_AnchorTips.newBuilder()
                .setActId(actId).setType(2);
        anchorTips.setExtjson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg anchorTipsMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kAct202008_AnchorTips_VALUE)
                .setAct202008AnchorTips(anchorTips).build();
        svcSDKService.unicastUid(Convert.toLong(member), anchorTipsMsg);
        log.info("broAnchorAwardTips done@actId:{}, seq:{}, member:{}, award:{},mission:{}", actId, seq, member, award, missionDesc);
    }
}
