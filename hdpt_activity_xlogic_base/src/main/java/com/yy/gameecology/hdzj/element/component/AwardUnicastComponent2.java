package com.yy.gameecology.hdzj.element.component;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AwardUnicastComponent2Attr;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 单播协议
 */
@Component
public class AwardUnicastComponent2 extends BaseActComponent<AwardUnicastComponent2Attr> {
    @Autowired
    private CommonBroadCastService commonBroadCastService;


    @Override
    public Long getComponentId() {
        return ComponentId.AWARD_UNICAST2;
    }


    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = false)
    public void unicastAward(HdztAwardLotteryMsg event, AwardUnicastComponent2Attr attr) {
        log.info("awardUnicastComponent unicastAward:{}", JSON.toJSONString(event));

        long taskId = event.getTaskId();
        long busiId = event.getBusiId();
        if (attr.getBusiId() != busiId || !attr.getTAwardTskIds().contains(taskId)) {
            return;
        }

        long uid = event.getUid();
        Set<Long> unicastAttrPackageIds = attr.getTAwardPkgIds();
        List<HdztAwardLotteryMsg.Award> awardList = event.getData().stream()
                .filter(award -> CollectionUtils.isEmpty(unicastAttrPackageIds) || unicastAttrPackageIds.contains(award.getPackageId()))
                .toList();
        if (awardList.isEmpty()) {
            return;
        }

        if (attr.isFilterDanmaku() && commonService.inDanmuChannel(uid)) {
            log.info("filter danmaku channel,uid:{}", uid);
            return;
        }

        Map<String, String> extData = event.getExtData();

        //只支持奖包维度的通知
        HdztAwardLotteryMsg.Award award = event.getData().getFirst();

        JSONObject extJson = new JSONObject();
        boolean hit = !attr.getTAwardPkgMissIds().contains(award.getPackageId());
        extJson.put("hit", hit);
        extJson.put("name", award.getPackageName());
        extJson.put("icon", award.getPackageImage());
        extJson.put("awardCount", award.getPackageNum());
        extJson.put("unit", award.getPackageUnit());
        extJson.put("viewExt", MapUtils.getString(extData, HdztAwardServiceClient.VIEW_EXT, "{}"));

        if (attr.getDelaySeconds() > 0) {
            Const.EXECUTOR_DELAY_BRO.schedule(
                    () -> {
                        commonBroadCastService.commonNoticeUnicast(attr.getActId(), attr.getNoticeType(), extJson.toJSONString()
                                , StringUtils.EMPTY, uid);
                    }, attr.getDelaySeconds(), TimeUnit.SECONDS);

        } else {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), attr.getNoticeType(), extJson.toJSONString()
                    , StringUtils.EMPTY, uid);
        }

        log.info("awardUnicastComponent,actId:{},uid:{},noticeType:{}, award:{}", attr.getActId(), uid, attr.getNoticeType(), JSON.toJSONString(award));

    }


}
