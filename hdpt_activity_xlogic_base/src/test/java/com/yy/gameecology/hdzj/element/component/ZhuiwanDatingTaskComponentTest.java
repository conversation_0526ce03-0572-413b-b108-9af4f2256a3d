package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.hdzj.element.component.attr.ZhuiwanDatingTaskComponentAttr;
import com.yy.gameecology.hdzj.element.history.ZhuiwanDatingTaskComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-08-12 17:19
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-4.properties"})
public class ZhuiwanDatingTaskComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ZhuiwanDatingTaskComponent zhuiwanDatingTaskComponent;

    long actId = 2021084002L;

    //完成日任务
    @Test
    public void completeDayTastTest() {
        String eventStr = "{\"actId\":2021084002,\"actors\":{80001:\"123456\",80003:\"789\"},\"busiId\":800,\"ekey\":\"\",\"index\":0,\"itemCount\":1,\"itemId\":\"ZW_LMYH\",\"itemScore\":2000,\"member\":\"789\",\"occurTime\":\"2021-08-20 17:30:15\",\"phaseId\":1,\"phaseScore\":20,\"rankId\":1,\"rankScore\":5000,\"seq\":\"569d6151-a636-4837-a136-5dc1730db282\",\"timeKey\":0,\"timestamp\":\"2021-08-20 17:30:15\",\"uri\":2002}";

        RankingScoreChanged event = JSON.parseObject(eventStr, RankingScoreChanged.class);

        ZhuiwanDatingTaskComponentAttr attr = zhuiwanDatingTaskComponent.getComponentAttr(actId, 1);
        zhuiwanDatingTaskComponent.handlerScoreDayTask(event, attr);

        zhuiwanDatingTaskComponent.handlerSendAnchorAmountDayTask(event, attr);


        //完成里程碑任务
        zhuiwanDatingTaskComponent.handlerFirstSendGiftActTask(event, attr);
    }


}
