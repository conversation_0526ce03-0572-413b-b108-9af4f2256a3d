package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2067AsyncWelfare;
import com.yy.gameecology.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Slf4j
@Repository
public class AsyncWelfareDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    private static final String INSERT_IGNORE_SQL = "insert ignore into cmpt_2067_async_welfare (act_id, cmpt_use_inx, seq, busi_id, uid, task_id, task_package_ids, ext_long, ext_data, `state`, create_time, update_time) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    public int addWelfareRecord(Cmpt2067AsyncWelfare record) {
        return gameecologyDao.getJdbcTemplate().update(INSERT_IGNORE_SQL, record.getActId(), record.getCmptUseInx(), record.getSeq(), record.getBusiId(), record.getUid(), record.getTaskId(), record.getTaskPackageIds(), record.getExtLong(), record.getExtData(), record.getState(), record.getCreateTime(), record.getUpdateTime());
    }

    public int[] addWelfareRecords(List<Cmpt2067AsyncWelfare> records) {
        return gameecologyDao.batchInsert(Cmpt2067AsyncWelfare.class, records);
    }

    public List<Cmpt2067AsyncWelfare> getWelfareRecords(long actId, long cmptUseInx, Integer state, int size) {
        Cmpt2067AsyncWelfare where = new Cmpt2067AsyncWelfare();
        where.setActId(actId);
        where.setCmptUseInx(cmptUseInx);
        where.setState(state);
        //
        Date time = DateUtils.addSeconds(new Date(), -2);
        String afterWhere = " and create_time < '" + DateFormatUtils.format(time, DateUtil.DEFAULT_PATTERN) + "' order by create_time limit " + size;
        return gameecologyDao.select(Cmpt2067AsyncWelfare.class, where, afterWhere);
    }

    public int updateWelfareRecordState(long id) {
        Cmpt2067AsyncWelfare to = new Cmpt2067AsyncWelfare();
        to.setState(1);
        to.setUpdateTime(new Date());

        Cmpt2067AsyncWelfare where = new Cmpt2067AsyncWelfare();
        where.setId(id);
        where.setState(0);
        return gameecologyDao.update(Cmpt2067AsyncWelfare.class, where, to);
    }
}
