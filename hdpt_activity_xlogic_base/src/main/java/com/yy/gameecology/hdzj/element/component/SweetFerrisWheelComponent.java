package com.yy.gameecology.hdzj.element.component;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerMp4Config;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.DelaySvcSDKServiceV2;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2061LatestCp;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5149StarTaskRecord;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.RankUtils;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.BaseAward;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SweetFerrisWheelComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.SweetFerrisWheelDao;
import com.yy.gameecology.hdzj.utils.BusinessUtils;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.QueryUserTaskResponse;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingTaskItem;
import com.yy.thrift.hdztranking.UserTaskItem;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("5149")
public class SweetFerrisWheelComponent extends BaseActComponent<SweetFerrisWheelComponentAttr> implements LayerSupport {

    private static final String INFOFLOW_TITLE_TEMPLATE = "【甜蜜摩天轮】（%s）小时播报";

    private static final String INFOFLOW_MSG_TEMPLATE = """
            ###### 每日星星任务完成人数统计：
            <#list dailyStats as dailyStat>
                ${dailyStat.taskLevel} 级 ${dailyStat.passValue} 颗星星：达成总人数：${dailyStat.memberCount?c}
            </#list>
            ###### 累计星星任务完成人数统计：
            <#list totalStats as totalStat>
                ${totalStat.taskLevel} 级 ${totalStat.passValue} 颗星星：达成总人数：${totalStat.memberCount?c}
            </#list>
            ###### 奖品累计发放金额：${awardedAmount}
            详情可查看：${link}
            """;
    private static final freemarker.template.Template TEMPLATE;

    static {
        try {
            TEMPLATE = new freemarker.template.Template("infoflow_5148", INFOFLOW_MSG_TEMPLATE, null);
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }

    private static final String DAILY_STAT_KEY = "daily_task_stat:%s";

//    private static final String DAILY_AWARD_KEY = "daily_award_stat:%s";

    private static final String TOTAL_STAT_KEY = "total_task_stat";

    private static final String TOTAL_AWARD_KEY = "total_award_stat";

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SweetFerrisWheelDao sweetFerrisWheelDao;

    @Autowired
    private DelaySvcSDKServiceV2 delaySvcSDKServiceV2;

    @Autowired
    private LatestCpComponent latestCpComponent;

    @Autowired
    private CpInfoComponent cpInfoComponent;

    @Autowired
    private LimitControlComponent limitControlComponent;

    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.SWEET_FERRIS_WHEEL;
    }

    @Override
    public long getActId() {
        return ComponentId.SWEET_FERRIS_WHEEL;
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(cron = "19 0 * * * *")
    public void sendTaskStats() {
        timerSupport.work("sendTaskStats:" + ComponentId.SWEET_FERRIS_WHEEL, 10, () -> {
            Set<Long> actIds = this.getComponentEffectActIds();
            if (CollectionUtils.isEmpty(actIds)) {
                return;
            }
            for (Long actId : actIds) {
                if (!actInfoService.inActShowTime(actId)) {
                    continue;
                }

                var attr = tryGetUniqueComponentAttr(actId);
                sendStatInfoflowMsg(attr, commonService.getNow(actId));
            }
        });
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, SweetFerrisWheelComponentAttr attr) {
        log.info("onTaskProgressChanged with rankId:{} phaseId:{}, member:{}", event.getRankId(), event.getPhaseId(), event.getMember());
        if (event.getRankId() == attr.getDailyRankId() && event.getPhaseId() == attr.getDailyPhaseId()) {
            handleDailyTaskProgress(event, attr);
            return;
        }

        if (event.getRankId() == attr.getTotalRankId() && event.getPhaseId() == attr.getTotalPhaseId()) {
            handleTotalTaskProgress(event, attr);
        }
    }

    /**
     * 监听每日任务，触发“摩天轮动效” & 触发“口令抽奖”
     *
     * @param event 事件
     * @param attr 组件属性
     */
    private void handleDailyTaskProgress(TaskProgressChanged event, SweetFerrisWheelComponentAttr attr) {
        long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
        if (startIndex == currIndex) {
            return;
        }

        var cpUids = RankUtils.getCpUidByMemberId(event.getMember());
        if (cpUids == null) {
            log.warn("handleDailyTaskProgress cpUids is null");
            return;
        }
        long userUid = cpUids.getLeft(), anchorUid = cpUids.getRight();

        final long sid, ssid;
        ChannelInfoVo channel = onMicService.getOnMicChannel(anchorUid);
        if (channel != null) {
            sid = channel.getSid();
            ssid = channel.getSsid();
        } else {
            Cmpt2061LatestCp latestCp = latestCpComponent.getLatestCp(attr.getActId(), attr.getLatestCpIndex(), StringUtils.EMPTY, attr.getBaseRankId(), anchorUid);
            if (latestCp != null) {
                sid = latestCp.getSid();
                ssid = latestCp.getSsid();
            } else {
                log.error("addWatchwordLottery cannot find latestCp with anchorUid:{}", anchorUid);
                return;
            }
        }

        Date endTime = DateUtil.getDate(event.getOccurTime());
        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), endTime);

        for (long i = startIndex + 1; i <= currIndex; i++) {
            long taskLevel = i;
            if (attr.getWatchwordLotteryLevels() != null && attr.getWatchwordLotteryLevels().contains(taskLevel)) {
                addWatchwordLottery(event, attr, taskLevel, sid, ssid);
                log.info("handleDailyTaskProgress add watchword lottery done cpUids:{} taskLevel:{}", cpUids, taskLevel);
            }

            if (attr.getDailyTaskBroLevels() != null && attr.getDailyTaskBroLevels().contains(taskLevel)) {
                sendStarTaskBroadcast(attr, dateStr, taskLevel, anchorUid, userUid, sid, ssid);
                log.info("handleDailyTaskProgress send broadcast done cpUids:{} taskLevel:{}", cpUids, taskLevel);
            }

            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> addDailyTaskStat(attr, dateStr, anchorUid, userUid, taskLevel));
        }

    }

    /**
     * 触发口令抽奖
     *
     * @param event 事件
     * @param attr 组件属性
     * @param currIndex 主持uid
     * @param sid
     * @param ssid
     */
    private void addWatchwordLottery(TaskProgressChanged event, SweetFerrisWheelComponentAttr attr, long currIndex, long sid, long ssid) {
        Date time = DateUtil.getDate(event.getOccurTime());
        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), time);
        Date expiredTime = DateUtils.addSeconds(time, (int) attr.getWatchwordLotteryDuration().toSeconds());
        String seq = String.format("sfw:%s:%s:%d", dateStr, event.getMember(), currIndex);
        channelWatchwordLotteryComponent.addWatchwordLotteryBox(attr.getActId(), attr.getWatchwordLotteryIndex(), seq, event.getMember(), sid, ssid, expiredTime);
    }

    /**
     * 发全服广播，PC特效（挂件也接收） + App特效
     *
     * @param attr 组件属性
     * @param anchorUid 主持uid
     * @param userUid 用户uid
     */
    private void sendStarTaskBroadcast(SweetFerrisWheelComponentAttr attr, String dateCode, long taskLevel, long anchorUid, long userUid, long sid, long ssid) {
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(2);
        Map<Long, UserInfoVo> userInfoMap = userInfoService.getCpUserInfoWithNickExt(userUid, anchorUid, multiNickUsers);

        JSONObject json = new JSONObject(16);
        json.put("userUid", userUid);
        json.put("babyUid", anchorUid);
        json.put("sid", sid);
        json.put("ssid", ssid);

        GameecologyActivity.BannerBroadcast.Builder builder = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(5149001)
                .setBannerType(0)
                .setAnchorUid(anchorUid)
                .setUserUid(userUid)
                .setMultiNickUsers(JSONObject.toJSONString(multiNickUsers));

        var userInfo = userInfoMap.get(userUid);
        if (userInfo != null) {
            builder.setUserNick(userInfo.getNick());
            builder.setUserLogo(userInfo.getAvatarUrl());
            json.put("userLogo", userInfo.getAvatarUrl());
            json.put("userNick", userInfo.getNick());
        }

        userInfo = userInfoMap.get(anchorUid);
        if (userInfo != null) {
            builder.setAnchorNick(userInfo.getNick());
            builder.setAnchorLogo(userInfo.getAvatarUrl());
            json.put("babyLogo", userInfo.getAvatarUrl());
            json.put("babyNick", userInfo.getNick());
        }

        builder.setJsonData(json.toJSONString());
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(builder.build())
                .build();

        svcSDKService.broadcastTop(sid, msg);
        log.info("handleDailyTaskProgress task level success");

        if (StringUtils.isNotEmpty(attr.getMp4Url())) {
            sendAppStarTaskBroadcast(attr, dateCode, taskLevel, sid, anchorUid, userUid, userInfoMap);
        }
    }

    private void sendAppStarTaskBroadcast(SweetFerrisWheelComponentAttr attr, String dateCode, long taskLevel, long sid, long anchorUid, long userUid, Map<Long, UserInfoVo> userInfoMap) {
        AppBannerMp4Config mp4Config = new AppBannerMp4Config();
        mp4Config.setUrl(attr.getMp4Url());
        mp4Config.setLevel(attr.getMp4Level());


        if (MapUtils.isNotEmpty(attr.getMp4LayerExtKeyValues())) {
            List<Map<String, String>> layerExtKeyValues = new ArrayList<>(attr.getMp4LayerExtKeyValues().size());

            for (var entry : attr.getMp4LayerExtKeyValues().entrySet()) {
                String key = entry.getKey(), text = entry.getValue();
                if (StringUtils.isNotBlank(text)) {
                    text = text.replace("{userNick}", "{" + userUid + ":n}");
                    text = text.replace("{anchorNick}", "{" + anchorUid + ":n}");
                    var userInfo = userInfoMap.get(userUid);
                    if (userInfo != null) {
                        text = text.replace("{userAvatar}", userInfo.getAvatarUrl());
                    }

                    userInfo = userInfoMap.get(anchorUid);
                    if (userInfo != null) {
                        text = text.replace("{anchorAvatar}", userInfo.getAvatarUrl());
                    }
                }

                layerExtKeyValues.add(Map.of(key, text));
            }

            mp4Config.setLayerExtKeyValues(layerExtKeyValues);
        }


        int business = BusinessUtils.getAppBroBizByBusiId(attr.getBusiId());
        String seq = makeKey(attr, String.format("ferris:%d:%d:%s:%d", userUid, anchorUid, dateCode, taskLevel));
        seq = DigestUtil.sha256Hex(seq);
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, business, FstAppBroadcastType.TOP_CHANNEL, sid, 0, StringUtils.EMPTY, Collections.emptyList());
        appBannerEvent.setMp4Config(mp4Config);
        appBannerEvent.setContentType(5);
        int appId = commonService.getTurnoverAppId(attr.getBusiId());
        appBannerEvent.setAppId(appId);
        appBannerEvent.setUidList(List.of(userUid, anchorUid));

        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("sendAppStarTaskBroadcast done with userUid:{} anchorUid:{} seq:{}", userUid, anchorUid, seq);
    }

    private void addDailyTaskStat(SweetFerrisWheelComponentAttr attr, String dateCode, long anchorUid, long userUid, long taskLevel) {
        String seq = String.format("d_tsk:%d:%d:%s:%d", userUid, anchorUid, dateCode, taskLevel);
        String key = String.format(DAILY_STAT_KEY, dateCode);
        commonDataDao.hashValueIncIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), seq, key, String.valueOf(taskLevel), 1);

//        seq = String.format("d_awd:%d:%d:%s:%d", userUid, anchorUid, dateCode, taskLevel);
//        key = String.format(DAILY_AWARD_KEY, dateCode);
//        commonDataDao.hashValueIncIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), seq, key, String.valueOf(taskLevel), userUid == anchorUid ? 1 : 2);
    }

    /**
     * 监听星星榜任务，单播弹窗进行礼物选择
     *
     * @param event 事件
     * @param attr 组件属性
     */
    private void handleTotalTaskProgress(TaskProgressChanged event, SweetFerrisWheelComponentAttr attr) {
        var cpUids = RankUtils.getCpUidByMemberId(event.getMember());
        if (cpUids == null) {
            log.warn("handleTotalTaskProgress cpUids is null");
            return;
        }

        long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
        if (startIndex == currIndex) {
            return;
        }

        final long userUid = cpUids.getLeft(), anchorUid = cpUids.getRight();

        for (long i = startIndex + 1; i <= currIndex; i++) {
            String seq = makeKey(attr, String.format("%s:%d", event.getMember(), i));
            var awards = attr.getTotalTaskAwards().get(i);
            if (CollectionUtils.isEmpty(awards)) {
                log.error("handleTotalTaskProgress awards is empty taskLevel:{}", i);
                continue;
            }

            final long taskLevel = i, worth = awards.getFirst().getAwardAmount();

            // 记录数据
            var result = transactionTemplate.execute(status -> {
                int awardType = 0;
                if (CollectionUtils.isNotEmpty(attr.getTotalTaskSpareAwards())) {
                    long incAmount = worth;
                    if (userUid != anchorUid) {
                        incAmount *= 2;
                    }
                    var incResult = limitControlComponent.valueIncrIgnoreWithLimit(attr.getActId(), attr.getAwardLimitIndex(), 0, seq, incAmount);
                    if (incResult == null) {
                        return null;
                    }

                    if (incResult.isViolateLimit()) {
                        awardType = 1;
                    }
                }

                int rs = sweetFerrisWheelDao.saveStarTaskRecord(attr.getActId(), taskLevel, event.getMember(), userUid, anchorUid, awardType);
                if (rs <= 0) {
                    status.setRollbackOnly();
                    return null;
                }

                return Pair.of(1, awardType);
            });

            log.warn("handleTotalTaskProgress save record result:{} taskLevel:{} memberId:{}", result, i, event.getMember());
            if (result == null) {
                continue;
            }

            long passValue = event.getItemLevelPassMap().values().stream().filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream).limit(taskLevel).mapToLong(Long::longValue).sum();
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendTotalAwardNotice(attr, taskLevel, passValue, userUid, anchorUid, result.getRight()));

            long amount = result.getRight() == 0 ? (userUid == anchorUid ? worth : worth * 2) : 0;
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> addTotalTaskStat(attr,taskLevel, anchorUid, userUid, amount));
        }
    }

    private void sendTotalAwardNotice(SweetFerrisWheelComponentAttr attr, long currIndex, long passValue, long userUid, long anchorUid, int awardType) {
        List<BaseAward> baseTaskAwards;
        if (awardType == 0) {
            var taskAwards = attr.getTotalTaskAwards().get(currIndex);
            if (taskAwards == null) {
                log.error("handleTotalTaskProgress taskAwards is null");
                return;
            }

            baseTaskAwards = taskAwards.stream().map(SweetFerrisWheelComponent::toBaseAward).toList();
        } else {
            baseTaskAwards = attr.getTotalTaskSpareAwards().stream().map(SweetFerrisWheelComponent::toBaseAward).toList();
        }

        // 发单播弹窗
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(2);
        Map<Long, UserInfoVo> userInfoMap = userInfoService.getCpUserInfoWithNickExt(userUid, anchorUid, multiNickUsers);

        log.info("handleTotalTaskProgress passValue:{} taskLevel:{} passValue:{}", passValue, currIndex, passValue);

        JSONObject json = new JSONObject(10);
        json.put("awardType", awardType);
        json.put("taskLevel", currIndex);
        json.put("levelPassValue", passValue);
        json.put("memberId", userUid + StringUtil.VERTICAL_BAR + anchorUid);
        json.put("userUid", userUid);
        json.put("babyUid", anchorUid);
        json.put("taskAwards", baseTaskAwards);

        var userInfo = userInfoMap.get(userUid);
        if (userInfo != null) {
            json.put("userNick", userInfo.getNick());
            json.put("userLogo", userInfo.getAvatarUrl());
        }

        userInfo = userInfoMap.get(anchorUid);
        if (userInfo != null) {
            json.put("babyNick", userInfo.getNick());
            json.put("babyLogo", userInfo.getAvatarUrl());
        }

        String noticeValue = json.toJSONString();
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.SWEET_FERRIS_WHEEL_AWARD)
                .setMultiNickUsers(JSON.toJSONString(multiNickUsers))
                .setNoticeValue(noticeValue);

        var msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        if (attr.getNoticeDelay() != null && attr.getNoticeDelay().toSeconds() > 0) {
            long delaySeconds = attr.getNoticeDelay().toSeconds();
            delaySvcSDKServiceV2.unicastUid(userUid, msg, delaySeconds);
            if (userUid != anchorUid) {
                delaySvcSDKServiceV2.unicastUid(anchorUid, msg, delaySeconds);
            }
        } else {
            svcSDKService.unicastUid(userUid, msg);
            if (userUid != anchorUid) {
                svcSDKService.unicastUid(anchorUid, msg);
            }
        }
    }

    private void addTotalTaskStat(SweetFerrisWheelComponentAttr attr, long taskLevel, long anchorUid, long userUid, long amount) {
        String seq = String.format("t_tsk:%d:%d:%d", userUid, anchorUid, taskLevel);
        commonDataDao.hashValueIncIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), seq, TOTAL_STAT_KEY, String.valueOf(taskLevel), 1);

        seq = String.format("t_awd:%d:%d:%d", userUid, anchorUid, taskLevel);
        commonDataDao.valueIncrIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), seq, TOTAL_AWARD_KEY, amount);
    }

    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        if (!LayerItemTypeKey.ANCHOR.equals(layerMemberItem.getItemType())) {
            return ext;
        }

        if (!StringUtils.isNumeric(layerMemberItem.getMemberId())) {
            return ext;
        }

        var attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return ext;
        }

        int state = layerMemberItem.getState();
        if (state == ActorInfoStatus.NORMAL || state == ActorInfoStatus.DANGEROUS) {
            return ext;
        }

        Date now = commonService.getNow(actId);
        long anchorUid = Long.parseLong(layerMemberItem.getMemberId());
        String dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);
        Cmpt2061LatestCp latestCp = latestCpComponent.getLatestCp(actId, attr.getLatestCpIndex(), StringUtils.EMPTY, attr.getBaseRankId(), anchorUid);

        if (ext == null) {
            ext = new HashMap<>(15);
        }

        if (latestCp == null) {
            setSingleInfo(attr, anchorUid, ext);
            layerMemberItem.setState(ActorInfoStatus.NORMAL);
            layerMemberItem.setViewStatus(60001);
        } else {
            setCoupleInfo(attr, dateCode, anchorUid, latestCp.getUserUid(), ext);
            layerMemberItem.setState(ActorInfoStatus.NORMAL);
            layerMemberItem.setViewStatus(60002);
        }

        return ext;
    }

    @Override
    public LayerBroadcastInfo customBroadcastInTheEnd(LayerBroadcastInfo source) {
        LayerMemberItem roomItem =  source.getSubChannelInfo();
        if (roomItem == null) {
            return source;
        }

        var anchorItems =  source.getAnchorInfo();
        if (CollectionUtils.isEmpty(anchorItems)) {
            return source;
        }

        int state = roomItem.getState();
        if (state == ActorInfoStatus.NORMAL || state == ActorInfoStatus.DANGEROUS) {
            anchorItems.forEach(anchorItem -> {
                anchorItem.setState(ActorInfoStatus.NOT_IN);
                anchorItem.setViewStatus(-1);
            });
        }

        return source;
    }

    private void setSingleInfo(SweetFerrisWheelComponentAttr attr, long anchorUid, Map<String, Object> ext) {
        ext.put("babyUid", anchorUid);

        var userInfo = commonService.getUserInfo(anchorUid, true);
        if (userInfo != null) {
            ext.put("babyNick", userInfo.getNick());
            ext.put("babyLogo", userInfo.getLogo());
        }

        ext.put("totalStar", 20);
        ext.put("lightedStar", 0);
    }

    private void setCoupleInfo(SweetFerrisWheelComponentAttr attr, String dateCode, long anchorUid, long userUid, Map<String, Object> ext) {
        UserTaskItem taskItem = getUserTaskItem(attr.getActId(), attr.getDailyRankId(), attr.getDailyPhaseId(), dateCode, anchorUid, userUid);
        if (taskItem == null) {
            setSingleInfo(attr, anchorUid, ext);
            return;
        }

        long score = getBaseDailyRankScore(attr, dateCode, userUid + StringUtil.VERTICAL_BAR + anchorUid);
        ext.put("babyUid", anchorUid);
        ext.put("userUid", userUid);
        ext.put("totalStar", attr.getDailyTaskAwards().size());
        long lightedStar = taskItem.getCurTaskId() - 1;
        if (taskItem.getCurTaskScore() >= taskItem.getCurTaskScoreConfig()) {
            lightedStar = taskItem.getCurTaskId();
        }
        ext.put("lightedStar", lightedStar);

        ext.put("currentValue", score);
        ext.put("passValue", taskItem.getCurTaskScoreConfig() * attr.getBaseScore());
        ext.put("giftName", attr.getDailyTaskAwards().get(taskItem.getCurTaskId()));

        List<Long> uids = new ArrayList<>(2);
        uids.add(userUid);
        if (anchorUid != userUid) {
            uids.add(anchorUid);
        }

        var userInfoMap = commonService.batchGetUserInfos(uids, true);
        var userInfo = userInfoMap.get(userUid);
        if (userInfo != null) {
            ext.put("userNick", userInfo.getNick());
            ext.put("userLogo", userInfo.getLogo());
        }

        userInfo = userInfoMap.get(anchorUid);
        if (userInfo != null) {
            ext.put("babyNick", userInfo.getNick());
            ext.put("babyLogo", userInfo.getLogo());
        }
    }

    private long getBaseDailyRankScore(SweetFerrisWheelComponentAttr attr, String dateCode, String memberId) {
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(attr.getActId(), attr.getBaseDailyRankId(), attr.getBasePhaseId(), dateCode, memberId, Collections.emptyMap());
        if (rank == null || rank.score < 0) {
            return 0;
        }

        return rank.score;
    }

    private UserTaskItem getUserTaskItem(long actId, long rankId, long phaseId, String dateCode, long anchorUid, long userUid) {
        String member = userUid + StringUtil.VERTICAL_BAR + anchorUid;
        return getUserTaskItem(actId, rankId, phaseId, dateCode, member);
    }

    private UserTaskItem getUserTaskItem(long actId, long rankId, long phaseId, String dateCode, String memberId) {
        QueryUserTaskResponse userTaskResp = hdztRankingThriftClient.queryUserTaskInfo(actId, rankId, phaseId, dateCode, memberId);
        if (userTaskResp == null || userTaskResp.code != 0) {
            return null;
        }

        if (MapUtils.isEmpty(userTaskResp.items)) {
            return null;
        }

        return userTaskResp.items.values().stream().findFirst().orElse(null);
    }

    /**
     * 查询我的CP信息
     * @param actId
     * @param cmptInx
     * @return
     */
    @GetMapping("myCouples")
    public Response<List<CoupleInfo>> queryMyCouples(@RequestParam(name = "actId") int actId,
                                                     @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is needed");
        }

        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component is not exist");
        }

        List<CpUid> cpUids = cpInfoComponent.queryUserCpUid(actId, attr.getCpInfoComponentIndex(), StringUtils.EMPTY, uid);
        if (CollectionUtils.isEmpty(cpUids)) {
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(2);
            var userInfoMap = userInfoService.getUserInfoWithNickExt(List.of(uid), multiNickUsers);
            var userInfo = userInfoMap.get(uid);
            CoupleInfo coupleInfo = new CoupleInfo();
            coupleInfo.setUserUid(uid);
            if (userInfo != null) {
                coupleInfo.setUserNick(userInfo.getNick());
                coupleInfo.setUserLogo(userInfo.getAvatarUrl());
            }

            return Response.success(List.of(coupleInfo), multiNickUsers);
        }

        Set<Long> uids = new HashSet<>(cpUids.size() * 2);
        List<String> memberIds = new ArrayList<>(cpUids.size());
        for (CpUid cpUid : cpUids) {
            uids.add(cpUid.getAnchorUid());
            uids.add(cpUid.getUserUid());
            memberIds.add(cpUid.getMember());
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfoMap = userInfoService.getUserInfoWithNickExt(Lists.newArrayList(uids), multiNickUsers);

        var rankMap = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getTotalRankId(), attr.getTotalPhaseId(), StringUtils.EMPTY, memberIds, Collections.emptyMap());

        List<CoupleInfo> result = new ArrayList<>(cpUids.size());
        for (CpUid cpUid : cpUids) {
            CoupleInfo item = new CoupleInfo();
            item.setUserUid(cpUid.getUserUid());
            item.setBabyUid(cpUid.getAnchorUid());
            item.setMemberId(cpUid.getMember());

            long totalStar = 0;
            Rank rank = rankMap.get(cpUid.getMember());
            if (rank != null && rank.score > 0) {
                totalStar = rank.score;
            }

            item.setTotalStar(totalStar);

            var userInfo = userInfoMap.get(cpUid.getUserUid());
            if (userInfo != null) {
                item.setUserNick(userInfo.getNick());
                item.setUserLogo(userInfo.getAvatarUrl());
            }

            userInfo = userInfoMap.get(cpUid.getAnchorUid());
            if (userInfo != null) {
                item.setBabyNick(userInfo.getNick());
                item.setBabyLogo(userInfo.getAvatarUrl());
            }

            result.add(item);
        }

        result.sort(Comparator.comparing(CoupleInfo::getTotalStar).reversed());

        return Response.success(result, multiNickUsers);
    }

    /**
     * 查询摩天轮上的奖励情况
     * @param actId
     * @param cmptInx
     * @param memberId
     * @return
     */
    @GetMapping("daily/awards")
    public Response<DailyTaskInfo> queryDailyAwards(@RequestParam(name = "actId") int actId,
                                                    @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                    @RequestParam(name = "memberId") String memberId) {
        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component is not exist");
        }

        Date now = commonService.getNow(actId);
        String dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);
        DailyTaskInfo data = new DailyTaskInfo();
        var taskItem = getUserTaskItem(actId, attr.getDailyRankId(), attr.getDailyPhaseId(), dateCode, memberId);
        long score = getBaseDailyRankScore(attr, dateCode, memberId);
        if (taskItem == null) {
            data.setTotalStar(20);
            data.setLightedStar(0);
            data.setCurrentValue(0);
            data.setPassValue(attr.getBaseScore());
            data.setGiftName(attr.getDailyTaskAwards().get(1L));
        } else {
            data.setTotalStar(taskItem.getAllTaskScoreConfig());
            data.setLightedStar(taskItem.getCurTaskScore());
            long passValue = attr.getBaseScore(), currentValue = score % attr.getBaseScore();
            if (taskItem.getCurTaskScore() >= taskItem.getAllTaskScoreConfig()) {
                currentValue = passValue;
            }
            data.setCurrentValue(currentValue);
            data.setPassValue(passValue);

            long taskLevel = taskItem.getCurTaskId() + 1;
            if (taskLevel > attr.getDailyTaskAwards().size()) {
                taskLevel = attr.getDailyTaskAwards().size();
            }

            data.setGiftName(attr.getDailyTaskAwards().get(taskLevel));
        }

        return Response.success(data);
    }

    /**
     * 查询全部奖励情况
     * @param actId
     * @param cmptInx
     * @param memberId
     * @return
     */
    @GetMapping("total/awards")
    public Response<TotalTaskInfo> queryTotalAwards(@RequestParam(name = "actId") int actId,
                                                    @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                    @RequestParam(name = "memberId") String memberId) {
        long uid = getLoginYYUid();

        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component is not exist");
        }

        long score = 0;
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getTotalRankId(), attr.getTotalPhaseId(), StringUtils.EMPTY, memberId, Collections.emptyMap());
        if (rank != null && rank.score > 0) {
            score = rank.score;
        }

        UserTaskItem userTaskItem = getUserTaskItem(actId, attr.getTotalRankId(), attr.getTotalPhaseId(), StringUtils.EMPTY, memberId);
        if (userTaskItem == null) {
            return Response.fail(500, "user task config error!");
        }

        List<RankingTaskItem> taskItems = hdztRankingThriftClient.queryRankingTaskItem(attr.getActId(), attr.getTotalRankId(), attr.getTotalPhaseId());
        taskItems.sort(Comparator.comparing(RankingTaskItem::getTaskId));

        var taskRecords = sweetFerrisWheelDao.getStarTaskRecords(actId, memberId, uid);
        Map<Long, Cmpt5149StarTaskRecord> recordMap = taskRecords.stream().collect(Collectors.toMap(Cmpt5149StarTaskRecord::getTaskLevel, Function.identity()));

        TotalTaskInfo result = new TotalTaskInfo();
        List<TotalTaskItem> items = new ArrayList<>(taskItems.size());
        long passValue = 0;
        for (RankingTaskItem item : taskItems) {
            long level = item.getTaskId();
            var awardConfigs = attr.getTotalTaskAwards().get(level);
            if (CollectionUtils.isEmpty(awardConfigs)) {
                continue;
            }

            TotalTaskItem totalTaskItem = new TotalTaskItem();
            totalTaskItem.setTaskId(level);
            totalTaskItem.setTaskLevel(level);
            passValue += item.getPassValue();
            totalTaskItem.setPassValue(passValue);
            var record = recordMap.get(level);
            final List<BaseAward> awards;
            if (record == null) {
                totalTaskItem.setState(-1);
                awards = awardConfigs.stream().map(SweetFerrisWheelComponent::toBaseAward).toList();
            } else {
                totalTaskItem.setState(record.getState());

                if (record.getAwardType() == 1 && CollectionUtils.isNotEmpty(attr.getTotalTaskSpareAwards())) {
                    awardConfigs = attr.getTotalTaskSpareAwards();
                }

                if (record.getTaskId() == 0 && record.getPackageId() == 0) {
                    awards = awardConfigs.stream().map(SweetFerrisWheelComponent::toBaseAward).toList();
                } else {
                    awards = awardConfigs.stream()
                            .filter(awardConfig -> Objects.equals(awardConfig.getTAwardTskId(), record.getTaskId()) && Objects.equals(awardConfig.getTAwardPkgId(), record.getPackageId()))
                            .map(SweetFerrisWheelComponent::toBaseAward)
                            .toList();
                }
            }

            totalTaskItem.setAwards(awards);
            items.add(totalTaskItem);
        }

        result.setTaskItems(items);
        result.setCurrentValue(score);

        return Response.success(result);
    }

    @RequestMapping("receive/award")
    public Response<BaseAward> receiveTotalAward(@RequestParam(name = "actId") int actId,
                                      @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                      @RequestParam(name = "memberId") String memberId,
                                      @RequestParam(name = "taskLevel") long taskLevel,
                                      @RequestParam(name = "packageId") long packageId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is needed");
        }

        log.info("receiveTotalAward request with uid:{} memberId:{} taskLevel:{} packageId:{}", uid, memberId, taskLevel, packageId);

        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component is not exist");
        }

        var cpUids = RankUtils.getCpUidByMemberId(memberId);
        if (cpUids == null) {
            return Response.fail(400, "memberId is invalid");
        }

        long userUid = cpUids.getLeft(), anchorUid = cpUids.getRight();
        if (uid != userUid && uid != anchorUid) {
            return Response.fail(400, "memberId is invalid");
        }

        if (!actInfoService.inActShowTime(actId)) {
            return Response.fail(400, "活动已结束");
        }

        var record = sweetFerrisWheelDao.getStarTaskRecord(actId, taskLevel, memberId, uid);
        if (record == null) {
            return Response.fail(400, "您还没有完成此任务");
        }

        if (record.getState() != 0) {
            return Response.fail(400, "您已经领取过奖励了");
        }

        final AwardAttrConfig award;
        if (record.getAwardType() == 0) {
            var awards = attr.getTotalTaskAwards().get(taskLevel);
            if (CollectionUtils.isEmpty(awards)) {
                return Response.fail(400, "not award to receive");
            }

            award = awards.stream().filter(a -> a.getTAwardPkgId() == packageId).findFirst().orElse(null);
            if (award == null) {
                return Response.fail(400, "not award to receive");
            }
        } else {
            if (CollectionUtils.isEmpty(attr.getTotalTaskSpareAwards())) {
                return Response.fail(400, "not spare award to receive");
            }

            award = attr.getTotalTaskSpareAwards().stream()
                    .filter(awardConfig -> Objects.equals(awardConfig.getTAwardPkgId(), packageId))
                    .findFirst().orElse(null);

            if (award == null) {
                return Response.fail(400, "not spare award to receive");
            }
        }

        final String seq = String.format("total_award:%d:%d:%d", actId, taskLevel, record.getId());

        Integer result = transactionTemplate.execute(status -> {
            int rs = sweetFerrisWheelDao.updateStarTaskRecordReceived(record.getId(), award.getTAwardTskId(), award.getTAwardPkgId());
            if (rs <= 0) {
                return 0;
            }

            try {
                BatchWelfareResult welfareResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), uid, award.getTAwardTskId(), 1, award.getTAwardPkgId(), seq, Collections.emptyMap());
                if (welfareResult == null) {
                    status.setRollbackOnly();
                    return -1;
                }

                if (welfareResult.getCode() != 0) {
                    status.setRollbackOnly();
                    return -1;
                }
            } catch (Exception e) {
                status.setRollbackOnly();
                return -1;
            }

            return 1;
        });

        if (result == null || result == -1) {
            return Response.fail(500, "服务器正忙，请稍后再试");
        }

        if (result == 0) {
            return Response.fail(400, "您已经领取过奖励了");
        }

        return Response.success(toBaseAward(award));
    }

    public static BaseAward toBaseAward(AwardAttrConfig awardConfig) {
        BaseAward award = new BaseAward();
        award.setPackageId(awardConfig.getTAwardPkgId());
        award.setGiftCount(awardConfig.getNum());
        award.setGiftName(awardConfig.getAwardName());
        award.setGiftIcon(awardConfig.getAwardIcon());
        award.setGiftUnit(awardConfig.getUnit());
        award.setGiftAmount(awardConfig.getAwardAmount());

        return award;
    }

    @RequestMapping("send/stat")
    public Response<?> sendStat(@RequestParam(name = "actId") int actId,
                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {
        long uid = getLoginYYUid();
        if (uid != 50018033) {
            return Response.fail(403, "no right to send stat");
        }

        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        Date date = commonService.getNow(actId);

        sendStatInfoflowMsg(attr, date);

        return Response.ok();
    }

    public void sendStatInfoflowMsg(SweetFerrisWheelComponentAttr attr, Date date) {
        String dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), date);
        String key = String.format(DAILY_STAT_KEY, dateCode);
        List<TaskStat> dailyStats = getTaskStats(attr, key, attr.getDailyRankId(), attr.getDailyPhaseId());

        List<TaskStat> totalStats = getTaskStats(attr, TOTAL_STAT_KEY, attr.getTotalRankId(), attr.getTotalPhaseId());

        long awardedAmount = commonDataDao.valueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), TOTAL_AWARD_KEY);

        String link = "https://" + (SysEvHelper.isDeploy() ? "" : "test-") + "manager-hdzt.yy.com/admin-static/awardModul/awardIssueLog";

        JSONObject data = new JSONObject(3);
        data.put("link", link);
        data.put("dailyStats", dailyStats);
        data.put("totalStats", totalStats);
        data.put("awardedAmount", awardedAmount / 1000);

        String message;
        try {
            message = FreeMarkerTemplateUtils.processTemplateIntoString(TEMPLATE, data);
        } catch (Exception e) {
            log.error("processTemplate exception:", e);
            return;
        }

        String title = String.format(INFOFLOW_TITLE_TEMPLATE, dateCode);

        String msg = buildActRuliuMsg(attr.getActId(), false, title, message);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_TURNOVER, msg, Collections.emptyList());
    }

    private List<TaskStat> getTaskStats(SweetFerrisWheelComponentAttr attr, String key, long rankId, long phaseId) {
        Map<String, String> dailyTaskStats = commonDataDao.hashGetAll(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key);
        var taskItems = hdztRankingThriftClient.queryRankingTaskItem(attr.getActId(), rankId, phaseId);
        List<TaskStat> taskStats = new ArrayList<>(taskItems.size());
        long passValue = 0;
        for (var taskItem : taskItems) {
            long taskLevel = taskItem.getTaskId();
            long memberCount = Convert.toLong(dailyTaskStats.get(String.valueOf(taskLevel)));
            passValue += taskItem.getPassValue();

            taskStats.add(new TaskStat(taskLevel, passValue, memberCount));
        }

        return taskStats;
    }

    @Getter
    @Setter
    public static class CoupleInfo {
        protected String memberId;

        protected long babyUid;

        protected long userUid;

        protected String babyNick;

        protected String babyLogo;

        protected String userNick;

        protected String userLogo;

        protected long totalStar;
    }

    @Getter
    @Setter
    public static class DailyTaskInfo {
        protected long totalStar;

        protected long lightedStar;

        protected long passValue;

        protected long currentValue;

        protected String giftName;
    }

    @Getter
    @Setter
    public static class TotalTaskItem {
        protected long taskLevel;

        protected long taskId;

        protected String taskName;

        protected long passValue;

        protected List<BaseAward> awards;

        /**
         * -1: 未完成，0-已完成未领取，1-已领取
         */
        protected int state;
    }

    @Getter
    @Setter
    public static class TotalTaskInfo {
        protected List<TotalTaskItem> taskItems;

        /**
         * 当前任务分值
         */
        protected long currentValue;
    }

    @Getter
    @Setter
    public static class TaskStat {
        protected long taskLevel;

        protected long passValue;

        protected long memberCount;

        public TaskStat() {
        }

        public TaskStat(long taskLevel, long passValue, long memberCount) {
            this.taskLevel = taskLevel;
            this.passValue = passValue;
            this.memberCount = memberCount;
        }
    }
}
