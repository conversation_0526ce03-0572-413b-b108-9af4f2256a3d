package com.yy.gameecology.activity.worker.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.client.thrift.*;
import com.yy.gameecology.activity.commons.ActAttrCont;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.worker.web.BaseController;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.component.xmodule.aov.AovPhaseComponent;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.protocol.pb.layer.LayerInfo;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardIssueRecordInfo;
import com.yy.thrift.hdztaward.AwardIssueRecordResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import lombok.Data;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * desc: 跟活动无关的一些通用性接口
 *
 * @createBy CXZ
 * @create 2021-04-23 15:34
 **/
@Validated
@RestController
@CrossOrigin(allowCredentials = "true", originPatterns = {"yy.com", "*.yy.com"})
@RequestMapping("/common")
public class CommonController extends BaseController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private OnlineChannelService onlineChannelService;
    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private SignedService signedService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private CommonService commonService;


    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @RequestMapping("/getRoomNo")
    public Response getRoomNo(@RequestParam(required = false, defaultValue = "1") long sid,
                              @RequestParam(required = false, defaultValue = "1") long ssid) {
        RoomInfo info = commonService.getRoomInfoBySsid(ssid);
        if (info == null) {
            return Response.fail(400, "没有找到子频道对应的房间号");
        }
        return Response.success(info.roomId);
    }

    @RequestMapping("/getFamilyId")
    public Response getFamilyId(@RequestParam(required = false, defaultValue = "1") long sid,
                                @RequestParam(required = false, defaultValue = "1") long ssid) {
        RoomInfo info = commonService.getRoomInfoBySsid(ssid);
        if (info == null) {
            return Response.fail(400, "没有找到子频道对应的房间号");
        }
        return Response.success(info.familyId);
    }

    /**
     * 查询频道对应的模板
     *
     * @param sid
     * @param ssid
     * @return
     */
    @Report
    @RequestMapping(value = "/getBusiId")
    public Response<Long> getBusiId(Long sid, Long ssid) {
        return new Response<Long>(Code.OK.getCode(), "ok", webdbThriftClient.getBusiId(sid, ssid));
    }

    /**
     * 查询频道内的主播 （有缓存）
     *
     * @param req
     * @param resp
     * @param sid
     * @param ssid
     * @return
     */
    @GetMapping("queryChannelAnchors")
    @Report
    public Response<List<JSONObject>> queryChannelAnchors(HttpServletRequest req, HttpServletResponse resp
            , String sid, String ssid, Long busiId, Integer formBusiness, Integer filterSigned) {

        Long sidLong = StringUtil.isNumeric(sid) ? Long.parseLong(sid) : 0L;
        Long ssidLong = StringUtil.isNumeric(ssid) ? Long.parseLong(ssid) : 0L;
        List<JSONObject> result = Lists.newArrayList();
        if (sidLong.equals(0) || ssidLong.equals(0)) {
            return Response.success(result);
        }

        OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sidLong, ssidLong);
        if (onlineChannelInfo == null) {
            return Response.success(result);
        }
        BusiId busiIdEnum = null;
        //是否去业务的nick
        if (formBusiness != null && formBusiness == 1) {
            if (busiId == null || busiId.equals(0L)) {
                busiId = webdbThriftClient.getBusiId(sidLong, ssidLong);
            }
            busiIdEnum = BusiId.findByValue(busiId.intValue());
        }

        boolean isFilterSigned = filterSigned != null && filterSigned == 1;

        for (long anchorUid : onlineChannelInfo.getEffectAnchorId()) {
            UserBaseInfo userBaseInfo = broadCastHelpService.getUserBaseInfo(anchorUid, busiIdEnum);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("uid", anchorUid);
            jsonObject.put("nick", userBaseInfo.getNick());
            jsonObject.put("logo", userBaseInfo.getLogo());

            //是否签约
            long signSid = signedService.getSignedSidByBusiId(anchorUid, busiId);
            if (isFilterSigned && signSid <= 0) {
                continue;
            }

            jsonObject.put("signed", signSid > 0);
            result.add(jsonObject);
        }
        return Response.success(result);
    }

    /**
     * 获取活动属性
     *
     * @param actId    活动id
     * @param attrName 活动属性名称
     * @return 活动属性值
     */
    @GetMapping("queryActAttrValue")
    public Response<String> queryActAttrValue(long actId, String attrName) {
        //数据库配置属性的时候需要额外加多前缀 public: ，区分可暴露参数
        return Response.success(cacheService.getActAttrValue(actId, Const.addPublicAttrPrefix(attrName)));
    }

    /**
     * 给H5用的灰度名单查询接口
     *
     * @param actId 活动id
     * @return
     */
    @GetMapping("queryBrocastWhitelist")
    public Response<Map<String, Object>> queryBrocastWhitelist(long actId) {
        Map<String, Object> result = Maps.newHashMap();
        //语音房交友联运房间，处理广播白名单
        result.put(ActAttrCont.CUSTOMER_BRO_SSID, cacheService.getActAttrValue(actId, ActAttrCont.CUSTOMER_BRO_SSID));
        //灰度状态下，处理广播白名单
        result.put(ActAttrCont.ACT_GREY_CLIENT_SSID, cacheService.getActAttrValue(actId, ActAttrCont.ACT_GREY_CLIENT_SSID));

        //非灰度状态下，安卓+交友联运房，只处理act_customer_bro_ssid名单下的广播（只能测试环境验证）
        //灰度状态下，所有情况，只处理act_grey_client_ssid名单下的广播请求


        //1==灰度状态
        result.put("grey", commonService.isGrey(actId) ? 1 : 0);
        return Response.success(result);
    }

    /**
     * 查询在线频道
     */
    @GetMapping("queryOnlineChannelIsGood20230517")
    public Response<List<ChannelInfo>> queryOnlineChannel(int template) {
        return Response.success(commonService.queryOnlineChannel(Template.findByValue(template)));
    }


    @Data
    public static class CommonProtoRequest {
        private int appId;

        private int uri;

        private long actId;

        private long sid;

        private long ssid;

        private Map<String, String> data;
    }

    @Data
    public static class CommonProtoResponse {
        private int result;

        private String reason;

        private int appId;

        private int uri;

        private String data;
    }

    @Report
    @RequestMapping("/commonProtoHandler")
    public CommonProtoResponse commonProtoHandler(@RequestBody CommonProtoRequest request) {
        CommonProtoResponse response = new CommonProtoResponse();
        response.setAppId(request.getAppId());
        response.setResult(0);
        response.setReason("success");
        final long actId = request.getActId(), sid = request.getSid(), ssid = request.getSsid();
        if (request.getUri() == GameecologyActivity.PacketType.LayerBroadcastRequestUri_VALUE) {
            String from = "web";
            Date now = commonService.getNow(actId);
            LayerBroadcastInfo layerBroadcastInfo = actLayerInfoService.buildLayerInfo(actId, sid, ssid, now, from);
            LayerInfo.LayerBroadcast broadcastInfo = layerBroadcastInfo.toBroadcastInfoPb();

            //广播信息
            GameecologyActivity.GameEcologyMsg msg =
                    GameecologyActivity.GameEcologyMsg.newBuilder()
                            .setUri(GameecologyActivity.PacketType.LayerBroadcast_VALUE)
                            .setLayerBroadcast(broadcastInfo).build();


            String encoded = Base64Utils.encodeToString(msg.toByteArray());
            log.debug("queryLayerInfoHex response encoded:{}", encoded);
            response.setUri(GameecologyActivity.PacketType.LayerBroadcast_VALUE);
            response.setData(encoded);
        } else if (request.getUri() == GameecologyActivity.PacketType.kAct202008_ActInfoReq_VALUE) {
            log.info("queryActInfoHex actInfo actId:{}, sid:{} ssid:{}", actId, sid, ssid);

            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
            if (activityInfoVo == null) {
                response.setResult(400);
                response.setReason("activity not exist!");
                return response;
            }
            if (activityInfoVo.getStatus() != 1) {
                response.setResult(400);
                response.setReason("activity is not valid!");
                return response;
            }

            GameecologyActivity.Act202008_ActInfo.Builder actInfo = GameecologyActivity.Act202008_ActInfo.newBuilder()
                    .setActId(activityInfoVo.getActId())
                    .setBeginTime(activityInfoVo.getBeginTime() / 1000)
                    .setEndTime(activityInfoVo.getEndTime() / 1000)
                    .setStartShowTime(activityInfoVo.getBeginTimeShow() / 1000)
                    .setEndShowTime(activityInfoVo.getEndTimeShow() / 1000)
                    .setCurrentTime(activityInfoVo.getCurrentTime() / 1000);

            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202008_ActInfo_VALUE)
                    .setAct202008ActInfo(actInfo)
                    .build();

            String encoded = Base64Utils.encodeToString(msg.toByteArray());
            log.debug("queryActInfoHex response encoded:{}", encoded);
        } else {
            response.setResult(400);
            response.setReason("uri not valid!");
            return response;
        }

        return response;
    }

    /**
     * yy号获取用户信息(null或者0就是查自己的信息)
     */
    @RequestMapping("/userInfoByYyno")
    public Response<UserBaseInfo> userInfoByYyno(HttpServletRequest request, HttpServletResponse response, Long yyno) {
        long loginUid = getLoginYYUid(request, response);
        if (loginUid == 0) {
            return Response.fail(1, "未登录");
        }
        try {
            UserBaseInfo userBaseInfo = commonService.userInfoByYyno(yyno, loginUid);
            return Response.success(userBaseInfo);
        } catch (Exception e) {
            log.error("userInfoByYyno exception{}", e.getMessage(), e);
            return Response.fail(1, "网络异常");
        }


    }

    /**
     * 判断是否已实名
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/certify")
    public Response<AovPhaseComponent.CertifyRsp> certify(HttpServletRequest request, HttpServletResponse response) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        AovPhaseComponent.CertifyRsp rsp = new AovPhaseComponent.CertifyRsp();
        String idHash = userinfoThriftClient.getIdHash(uid);
        if(!StringUtil.isEmpty(idHash)) {
            rsp.setPass(true);
        }
        log.info("certify uid:{},pass:{}", uid, rsp.isPass());
        return Response.success(rsp);
    }

    @RequestMapping("/mobileBind")
    public Response<AovPhaseComponent.CertifyRsp> hasMobileBind() {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        AovPhaseComponent.CertifyRsp rsp = new AovPhaseComponent.CertifyRsp();
        String mobileHash = userinfoThriftClient.getMobileHash(uid);
        if(!StringUtil.isEmpty(mobileHash)) {
            rsp.setPass(true);
        }
        log.info("hasMobileBind uid:{},pass:{}", uid, rsp.isPass());
        return Response.success(rsp);
    }

    /**
     * 测试用
     */
    @RequestMapping("/testAwardIssue")
    public Response<List<AwardIssueRecordInfo>> testAwardIssue(HttpServletRequest request, HttpServletResponse response) throws TException {
        AwardIssueRecordResult rsp = hdztAwardServiceClient.queryUserAwardIssues(500, 50017977L, 40199, Lists.newArrayList(819L, 817L));
        return Response.success(rsp.getAwardList());


    }


    @RequestMapping("/listRoomInfoBySsid")
    public Response<List<RoomInfo>> listRoomInfoBySsid(HttpServletRequest request, HttpServletResponse response, Long ssid) throws TException {
        var rsp = zhuiwanRoomInfoClient.listRoomInfoBySsid(Lists.newArrayList(ssid));
        return Response.success(rsp);
    }

    @RequestMapping("/listRoomInfoBySsidTest")
    public Response<String> listRoomInfoBySsidTest(HttpServletRequest request, HttpServletResponse response) {
        long loginUid = getLoginYYUid(request, response);
        if (loginUid != 50042952) {
            return Response.fail(1, "access denied");
        }
        Clock clock = new Clock();
        List<OnlineChannelInfo> onlineChannelInfos = onlineChannelService.queryOnlineChannelInfoNoCache(com.yy.thrift.broadcast.Template.SkillCard);
        clock.tag();
        Set<Long> ssid = onlineChannelInfos.stream().map(OnlineChannelInfo::getSsid).collect(Collectors.toSet());
        List<RoomInfo> roomInfos = zhuiwanRoomInfoClient.listRoomInfoBySsid(Lists.newArrayList(ssid));
        log.info("onlineChannelInfos size:{},ssidSize:{},roomInfoSize:{},clock:{}", onlineChannelInfos.size(), ssid.size(), roomInfos.size(), clock.tag());
        return Response.ok(System.currentTimeMillis() + "");
    }


}
