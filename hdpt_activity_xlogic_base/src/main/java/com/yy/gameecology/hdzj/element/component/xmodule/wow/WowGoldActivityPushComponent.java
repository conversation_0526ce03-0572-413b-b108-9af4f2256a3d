package com.yy.gameecology.hdzj.element.component.xmodule.wow;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskCompleteEvent;
import com.yy.gameecology.activity.bean.xpush.PushRsp;
import com.yy.gameecology.activity.client.http.XPushServiceHttpClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.*;
import com.yy.gameecology.hdzj.element.component.dao.PushSatateDao;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.*;

@Component
@RestController
@RequestMapping("/5129")
public class WowGoldActivityPushComponent extends BaseActComponent<WowGoldActivityPushComponentAttr> {
    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private XPushServiceHttpClient xPushServiceHttpClient;

    @Autowired
    private PushSatateDao pushSatateDao;

    @Autowired
    private WowTaskComponent wowTaskComponent;

    @Override
    public Long getComponentId() {
        return ComponentId.WOW_ACTIVITY_MESSAGE_PUSH;
    }

    private static final int PAGE_SIZE = 500;

    @Autowired
    private WowGoldActivityLoginComponent wowGoldActivityLoginComponent;

    private static final String WOW_PUSH_STATE = "WOW_PUSH_STATE";

    private static final String WOW_PUSH_LAST_DATE = "WOW_LAST_PUSH_DATE";
    private static final String WOW_PUSH_COUNTER = "WOW_PUSH_COUNTER";

    private static final int WOW_PUSH_STATE_OPEN = 0;
    private static final int WOW_PUSH_STATE_CLOSE = 1;

    private static final String SEND_PUSH_PREFIX = "wow_send_push:%d";
    private static final int ACT_USER_MOD = 10;

    private static final int[] PUSH_PLATFORM = {3, 4};

    @GetMapping("/setPushState")
    public Response<?> setPushState(@RequestParam("actId") int actId,
                                    @RequestParam("cmptIndex") long cmptIndex,
                                    @RequestParam("state") int state) {
        WowGoldActivityPushComponentAttr attr = getComponentAttr(actId, cmptIndex);

        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }

        var actInfo = actInfoService.queryActivityInfo(attr.getActId());

        Date actNow = commonService.getNow(attr.getActId());
        long now = actNow.getTime();
        log.info("setPushState uid:{},state:{}, endTime:{}", uid, state, actInfo.getEndTime());
        if (!actInfoService.inActTime(now, actInfo)) {
            return Response.fail(-1, "非活动时间");
        }


        int pushSate = WOW_PUSH_STATE_OPEN;
        if (state == 1) {
            pushSate = WOW_PUSH_STATE_CLOSE;
        }
        pushSatateDao.updateUserPushState(attr.getActId(), uid, actNow, pushSate);
        return Response.ok();
    }

    @GetMapping("/getPushState")
    public Response<?> getPushState(@RequestParam("actId") int actId,
                                    @RequestParam("cmptIndex") long cmptIndex) {
        WowGoldActivityPushComponentAttr attr = getComponentAttr(actId, cmptIndex);

        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();

        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }

        var actInfo = actInfoService.queryActivityInfo(attr.getActId());

//        long now = commonService.getNow(attr.getActId()).getTime();
        log.info("setPushState uid:{}, endTime:{}", uid, actInfo.getEndTime());
//        if (!actInfoService.inActTime(now, actInfo)) {
//            return Response.fail(-1, "非活动时间");
//        }

        Long state = pushSatateDao.getUserPushState(attr.getActId(), uid);
        PushState ret = new PushState();
        ret.setState(WOW_PUSH_STATE_OPEN);
        if (state != null && state == WOW_PUSH_STATE_CLOSE) {
            ret.setState(WOW_PUSH_STATE_CLOSE);
        }
        return Response.success(ret);
    }

    @GetMapping("/endPushTool")
    public Response<?> sendPush(@RequestParam("actId") long actId,
                                @RequestParam("cmptIndex") long cmptIndex) {
        WowGoldActivityPushComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.success(400, "not accessible!");
        }
        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }
        sendPush();

        return Response.ok();
    }


    @NeedRecycle(author = "gaofei", notRecycle = true)
    @Scheduled(cron = "0 0 20 * * *")
    public void sendPush() {
        Set<Long> actIds = getComponentEffectActIds();
        log.info("sendPush actIds {}", actIds);
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {

            WowGoldActivityPushComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.info("sendPush attr null ");
                continue;
            }

            Date now = commonService.getNow(actId);
            var actInfo = actInfoService.queryActivityInfo(attr.getActId());
            log.info("sendPush actInfo {}", JsonUtil.toJson(actInfo));
            if (now.getTime() < attr.getPushStartTime().getTime() || now.getTime() > attr.getPushEndTime().getTime()) {
                log.info("sendPush not in push time is end {} {} {} ", actId, DateUtil.format(now), DateUtil.format(attr.getPushStartTime()),
                        DateUtil.format(attr.getPushEndTime()));
                continue;
            }


            WowGoldActivityLoginComponentAttr controlAttr = wowGoldActivityLoginComponent.getComponentAttr(actId, attr.getWowControlCmptIndex());
            if (wowGoldActivityLoginComponent.isOverPoolLimit(controlAttr)) {
                log.info("sendPush over limit");
                continue;
            }

            String groupCode = getRedisGroupCode(actId);
            String LockKey = makeKey(attr, String.format("wow_cron_push_lock_%s", DateUtil.format(now, DateUtil.PATTERN_TYPE2)));
            boolean getLock = actRedisDao.setNX(groupCode, LockKey, "1", 600);
            if (!getLock) {
                log.info("not get the lock");
                break;
            }

            long offset = 0;
            int pageSize = 500;
            long startUid = 0;
            List<Long> members;
            Date pushData = DateUtil.first(DateUtil.add(now, -2));
            Date lastFinishData = DateUtil.first(DateUtil.add(now, -2));
            log.info("sendPush maxpush:{} now:{} pushData:{},lastFinishData:{}", attr.getMaxPushTime(), DateUtil.format(now), DateUtil.format(pushData), DateUtil.format(lastFinishData));
            members = pushSatateDao.selectPushMembers(attr.getActId(), pushData, lastFinishData, attr.getMaxPushTime(), startUid, offset, pageSize);

            Map<Long, Boolean> updateUsers = new HashMap<>();

            while (CollectionUtils.isNotEmpty(members)) {
                log.info("sendPush offset:{} ret_size:{} members:{}", offset, members.size(), members);
                for (Long member : members) {
                    if (member == null) {
                        continue;
                    }
                    log.info("sendPush uid:{}", Convert.toLong(member));
                    long uid = Convert.toLong(member);
                    if (uid == 0) {
                        continue;
                    }
                    updateUsers.put(uid, true);
                }
//                offset += pageSize;
                startUid = members.getLast();
                members = pushSatateDao.selectPushMembers(attr.getActId(), pushData, lastFinishData, attr.getMaxPushTime(), startUid, offset, pageSize);
                log.info("sendPush pageSize:{} offset:{} members:{}", pageSize, offset, members);
            }
            for (Map.Entry<Long, Boolean> entry : updateUsers.entrySet()) {
                long uid = entry.getKey();
                String tips = wowTaskComponent.getNoticeTips(attr.getActId(), attr.getWowTaskCmptIndex(), uid);
                if (StringUtils.isBlank(tips)) {
                    log.info("sendPush tips null uid:{}",uid);
                    continue;
                }
                log.info("sendPush update last push time uid {} tips {} ", uid, tips);
                pushSatateDao.updateAwardRecordState(attr.getActId(), now, entry.getKey());
                trySendPush(attr, now, groupCode, entry.getKey(), tips);
            }
        }
    }


    private boolean trySendPush(WowGoldActivityPushComponentAttr attr, Date nowDate, String groupCode, long uid,String tips) {
        String key = makeKey(attr, String.format(SEND_PUSH_PREFIX, uid));
        String dateStr = DateUtil.format(nowDate, DateUtil.PATTERN_TYPE2);
        boolean flag = actRedisDao.setNX(groupCode, key, dateStr, 300);
        if (flag) {
            doSendPush(attr, tips, uid);
        }
        return flag;
    }

    private void doSendPush(WowGoldActivityPushComponentAttr attr, String tips, long uid) {
        if (StringUtils.isNotEmpty(tips)) {
            PushRsp pushRsp = xPushServiceHttpClient.push(attr.getPushAppid(), null, attr.getPushTitle(), tips, attr.getPushLink(), attr.getPushIcon(), PUSH_PLATFORM, String.valueOf(uid));
            log.info("doSendPush to uid:{} tips:{} pushRsp:{}", uid, tips, pushRsp);
        }
    }

    @HdzjEventHandler(value = DayTaskCompleteEvent.class, canRetry = true)
    public void onDayTaskCompleteEvent(DayTaskCompleteEvent event, WowGoldActivityPushComponentAttr attr) {
        try {
            Date now = commonService.getNow(attr.getActId());
            pushSatateDao.updateUserLastCompleteTime(attr.getActId(), Convert.toLong(event.getMemberId()), now);
        } catch (Exception e) {
            log.error("onDayTaskCompleteEvent memberId{} error:{} e:{}", event.getMemberId(), e.getMessage(), e);
        }
    }

    @Data
    public static class PushState {

        private int state; // 0 未领取 1 已经领取
    }

    @Data
    public static class PushInfo {

        private LocalDate lastPushDate; // 最后push时间
        private int pushTimes; // push 次数
    }

}
