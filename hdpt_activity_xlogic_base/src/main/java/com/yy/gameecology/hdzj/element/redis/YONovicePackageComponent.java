package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.client.thrift.TGiftbagStatServiceClient;
import com.yy.gameecology.activity.client.thrift.TurnoverServiceClient;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.yrpc.IMMessageServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.service.BigDataService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.ZhuiwanApp;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.RequestUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.NewUserPackageDataLog;
import com.yy.gameecology.hdzj.bean.NewUserPackageRecord;
import com.yy.gameecology.hdzj.bean.NewUserPackageStatusResp;
import com.yy.gameecology.hdzj.bean.ServerEvent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.YONovicePackageComponentAttr;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.protocol.pb.zhuiwan.risk.ZhuiyaRisk;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import com.yy.thrift.turnover.TActivityPropsDailyStat;
import com.yy.thrift.turnover.TUserProps;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * A PC 模板弹窗
 * 1. 监听用户进入PC模板事件：UserEnterTemplateEvent,PC需要调用1055请求
 * 2. 判断用户是否满足条件,发送礼包弹窗单播
 * 3. 需要接风控,风控不通过,则不弹窗 -- 对接侠专提供的接口
 * 通用弹窗协议,uri = 100004,noticeType=newUserPackage,noticeValue=PcPopUp,extJson={"sid":123,"ssid":222,"busiId":400,"uid":3333,"app":"Yomi/Zhuiwan"}
 *
 * <AUTHOR>
 * @since 2023/4/24 10:22
 **/
@UseRedisStore
@Component
@RestController
@RequestMapping("/cmpt/novice")
@AllArgsConstructor
@Slf4j
public class YONovicePackageComponent extends BaseActComponent<YONovicePackageComponentAttr> {
    /**
     * 弹窗用户缓存key
     **/
    private static final String WHITE_LIST = "whiteList";

    private static final String DATA_LOG_USER_LIST = "dataLongUserList";
    private static final String POP_UP_USER_CACHE_KEY = "popUpUser";
    private static final String ACT_DATA_DAY_LOG = "actDataDayLog:";

    private static final String ACT_DATA_LOG = "actDataLog";
    private static final String ACT_DATA_POP_UP_TP = "log_data:pop";
    private static final String ACT_DATA_OBTAIN_TP = "log_data:obtain";
    private static final String ACT_DATA_OBTAIN_APP_TP = "log_data:obtainAp";
    private static final String ACT_DATA_UID_LOGIN_ZHUIWAN_TP = "log_data:uidLoginZhuiwan";
    private static final String ACT_DATA_UID_LOGIN_YOMI_TP = "log_data:uidLoginYomi";
    private static final String ACT_DATA_HDID_LOGIN_ZHUIWAN_TP = "log_data:hdidLoginZhuiwan";
    private static final String ACT_DATA_HDID_LOGIN_YOMI_TP = "log_data:hdidLoginYomi";
    private static final String ACT_FIRST_APP_UID_LOGIN = "actAppUidFirstLogin";
    private static final String ACT_FIRST_APP_HDID_LOGIN = "actAppHdidFirstLogin";
    private static final String DRAW_RECORD = "drawRecord";
    private static final String DRAW_USER = "drawUser";
    private static final String DRAW_USER_APP = "drawUserApp";
    private static final String TODAY_AWARD_COUNT = "awardCount:";
    private static final String TODAY_AWARD_COUNT_V2 = "awardCountV2:";
    private static final int SCORE_TYPE_PC_POP_UP = 10;
    private static final int SCORE_TYPE_RECEIVE_AWARD = 11;
    private static final int SCORE_TYPE_RECEIVE_AWARD_SUCC = 12;
    private static final int SCORE_TYPE_LOGIN = 13;
    private static final int SCORE_TYPE_RECEIVE_APP_LOGIN_AWARD_SUCC = 14;

    private static final List<String> STAT_HASH_KEYS = ImmutableList.of(ACT_DATA_POP_UP_TP, ACT_DATA_OBTAIN_TP, ACT_DATA_OBTAIN_APP_TP,
            ACT_DATA_UID_LOGIN_ZHUIWAN_TP, ACT_DATA_UID_LOGIN_YOMI_TP,
            ACT_DATA_HDID_LOGIN_ZHUIWAN_TP, ACT_DATA_HDID_LOGIN_YOMI_TP);

    private final ZhuiwanRiskClient riskClient;

    private final CommonBroadCastService commonBroadCastService;

    private final KafkaTemplate<String, String> zhuiyaKafkaTemplate;

    private final BigDataService bigDataService;

    private final TGiftbagStatServiceClient tGiftbagStatServiceClient;

    private final TurnoverServiceClient turnoverServiceClient;

    private final IMMessageServiceClient imMessageServiceClient;

    private final ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    private final UserinfoThriftClient userinfoThriftClient;

    @Override
    public Long getComponentId() {
        return ComponentId.NOVICE_PACKAGE;
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, YONovicePackageComponentAttr attr) {
        long uid = event.getUid();
        log.info("uid:{}, enterTemplate event:{}", uid, JSONUtils.toJsonString(event));
        //1. 判断是否在业务时间
        if (!actInfoService.inActTime(attr.getActId())) {
            return;
        }

        // 2. 判断用户是否在名单中
        if (!inWhiteList(uid, attr)) {
            return;
        }

        // 是否达到每日限制的90%
        if (reachDailyLimit(attr)) {
            return;
        }

        // 3. 判断用户是否已经弹过窗
        if (alreadyPopUp(uid, attr)) {
            return;
        }

        // 4. 判断风控是否通过
        if (hitRiskOfPc(uid, event.getIp(), attr.getRiskStrategyKey())) {
            return;
        }

        // 5. 发送弹窗 & 记录缓存
        popUpPc(event, attr);
        addToAlreadyPopUpSet(uid, attr);
        addToDrawRecord(uid, event, attr);
        logBusinessData(attr, ACT_DATA_POP_UP_TP, 1);
    }

    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLoginEvent(ZhuiwanLoginEvent event, YONovicePackageComponentAttr attr) {
        long uid = event.getUid();
        NewUserPackageRecord record = getNewUserPackageRecord(uid, attr);
        if (record == null) {
            return;
        }
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue(attr.getBusiId()), System.currentTimeMillis(), String.valueOf(event.getUid()), RoleType.USER, 1, SCORE_TYPE_LOGIN, event.getApp());
        //只第一次登陆打点
        if (uidFirstLoginAfterPopup(attr, uid)) {
            String app = event.getApp();
            if (ZhuiwanApp.zhuiwan.name().equals(app)) {
                logBusinessData(attr, ACT_DATA_UID_LOGIN_ZHUIWAN_TP, 1);
            } else if (ZhuiwanApp.yomi.name().equals(app)) {
                logBusinessData(attr, ACT_DATA_UID_LOGIN_YOMI_TP, 1);
            }
        }

        if (event.isFirstLogin() && hdidFirstLoginAfterPopup(attr, event.getHdid())) {
            String app = event.getApp();
            if (ZhuiwanApp.zhuiwan.name().equals(app)) {
                logBusinessData(attr, ACT_DATA_HDID_LOGIN_ZHUIWAN_TP, 1);
            } else if (ZhuiwanApp.yomi.name().equals(app)) {
                logBusinessData(attr, ACT_DATA_HDID_LOGIN_YOMI_TP, 1);
            }
        }

        final int count = attr.getAppRecCount();
        boolean received = record.getAppStatus() != null && record.getAppStatus() == 1;
        if (!event.isFirstLogin() || received || count <= 0) {
            log.info("user can't get reward from app login event:{}, record:{}", event, record);
            return;
        }

        long packageId = attr.getPackageId();
        if (packageId <= 0) {
            log.error("user cant get reward from app login 没有配置奖包");
            throw new SuperException("没有配置奖包", 400);
        }

        log.info("get reward from app login event:{}, record:{}", event, record);
        boolean sendResult = false;
        long actId = attr.getActId();
        String groupCode = getRedisGroupCode(actId);
        boolean nx = actRedisDao.hsetnx(groupCode, DRAW_USER_APP, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
        if (!nx) {
            log.info("get reward from app login uid:{} cant get lock", uid);
            return;
        }
        try {
            long limit = attr.getDailyLimit();
            String today = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
            if (attr.getDayLimitConfig().containsKey(today)) {
                limit = attr.getDayLimitConfig().get(today);
            }
            List<Long> incResult = actRedisDao.incrValueWithLimit(groupCode, getTodayAwardCountKey(attr), count, limit, false);
            if (incResult.get(0) != 1) {
                log.info("get reward from app login uid:{} overlimit", uid);
                return;
            }
            String time = DateUtil.format(commonService.getNow(actId));
            String newSeq = record.getSeq() + "-app";
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), uid, attr.getTaskId(), count, packageId, newSeq, 2);
            log.info("get reward from app login doWelfare result={}", batchWelfareResult);
            sendResult = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if (sendResult) {
                record.setAppStatus(1);
                updateDrawRecord(uid, record, attr);
                // 上报数据-- scoreType = 12 领取成功
                bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue(attr.getBusiId())
                        , System.currentTimeMillis(), uid + "", RoleType.USER, 1, SCORE_TYPE_RECEIVE_APP_LOGIN_AWARD_SUCC);
                logBusinessData(attr, ACT_DATA_OBTAIN_APP_TP, 1);
            }
        } catch (Exception e) {
            log.info("user cant get reward from app login doWelfare result={}", ExceptionUtils.getStackTrace(e));
        } finally {
            if (!sendResult) {
                actRedisDao.hdel(groupCode, DRAW_USER_APP, uid + "");
            }
        }
    }

    @Scheduled(cron = "24 * * * * ?")
    @NeedRecycle(author = "liqingyang01", notRecycle = true)
    public void sendGiftExpiredMsg() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }

            List<YONovicePackageComponentAttr> attrs = getAllComponentAttrs(actId);
            if (CollectionUtils.isEmpty(attrs)) {
                continue;
            }

            for (YONovicePackageComponentAttr attr : attrs) {
                if (attr.getExpiredMsgCountdown() == 0) {
                    continue;
                }

                ActivityInfoVo actInfo = actInfoService.queryActivityInfo(actId);
                if (actInfo.getCurrentTime() + attr.getExpiredMsgCountdown() < actInfo.getEndTime()) {
                    continue;
                }

                doSendGiftExpiredMsg(attr);
            }
        }
    }

    private void doSendGiftExpiredMsg(YONovicePackageComponentAttr attr) {
        String groupCode = getRedisGroupCode(attr.getActId());

        String execKey = makeKey(attr, "send_gift_expired_msg");
        if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
            return;
        }

        String hashKey = makeKey(attr, DRAW_RECORD);
        try(Cursor<Map.Entry<String, String>> cursor = actRedisDao.getRedisTemplate(groupCode)
                .<String, String>opsForHash()
                .scan(hashKey, ScanOptions.scanOptions().count(1000).match("*").build());) {
            while (cursor.hasNext()) {
                Map.Entry<String, String> entry = cursor.next();
                try {
                    NewUserPackageRecord record = JSON.parseObject(entry.getValue(), NewUserPackageRecord.class);
                    if (record == null) {
                        continue;
                    }
                    if (Objects.equals(record.getStatus(), Const.ONE) || Objects.equals(record.getAppStatus(), Const.ONE)) {
                        long uid = Convert.toLong(entry.getKey());
                        List<TUserProps> userProps = turnoverServiceClient.getAllUserPropsByPropId(uid, attr.getPropId(), attr.getPropAppid());
                        if (CollectionUtils.isEmpty(userProps)) {
                            continue;
                        }

                        boolean propPresent = userProps.stream().findFirst().filter(prop -> prop.propsCount > 0).isPresent();
                        if (propPresent) {
                            doSendIMMsg(uid, attr);
                            doSendSms(uid, attr);
                        }
                    }
                } catch (Throwable t) {
                    log.error("handle sending expiring msg with entry:{} fail", entry, t);
                }
            }
        } catch (Exception e) {
            log.info("scan exception:", e);
        }
    }

    private void doSendIMMsg(long uid, YONovicePackageComponentAttr attr) {
        imMessageServiceClient.sendMessage(attr.getMsgAppid(), attr.getMsgSenderUid(),
                Collections.singletonList(uid), attr.getGiftExpiredMsg(), Collections.emptyMap());
        log.info("send im gift expiring msg to uid:{}", uid);
    }

    private void doSendSms(long uid, YONovicePackageComponentAttr attr) {
        userinfoThriftClient.sendMarketSms(RandomStringUtils.random(10, true, true), uid, attr.getGiftExpiredMsg());
        log.info("send sms gift expiring msg to uid:{}", uid);
    }

    /**
     * 1、本日上限达到95%时，就不弹弹窗了，预留部分礼物供未领取的人进行领取
     * 2、如果领取时依然超过上限，那加多个提示：当前领取人数过多，请明日再来领取。
     */
    private boolean reachDailyLimit(YONovicePackageComponentAttr attr) {
        long todayAwardCount = getTodayAwardCount(attr);
        String today = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        if (attr.getDayLimitConfig().containsKey(today)) {
            return todayAwardCount >= attr.getDayLimitConfig().get(today) * attr.getStopPopupRate();
        }
        return todayAwardCount >= attr.getDailyLimit() * attr.getStopPopupRate();
    }

    private boolean alreadyPopUp(long uid, YONovicePackageComponentAttr attr) {
        String popUpUserCacheKey = makeKey(attr, POP_UP_USER_CACHE_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        boolean alreadyPopUp = actRedisDao.sIsMember(groupCode, popUpUserCacheKey, String.valueOf(uid));
        if (alreadyPopUp) {
            log.info("uid={} has already pop up", uid);

        }

        return alreadyPopUp;
    }

    /**
     * 判断用户是否在白名单内
     **/
    private boolean inWhiteList(long uid, YONovicePackageComponentAttr attr) {
        String whiteListKey = makeKey(attr, WHITE_LIST);
        String groupCode = getRedisGroupCode(attr.getActId());
        boolean isMember = actRedisDao.sIsMember(groupCode, whiteListKey, uid + "");
        if (!isMember) {
            log.info("uid={} is not in whitelist", uid);
        }
        return isMember;
    }

    /**
     * 是否命中风控
     **/
    private boolean hitRiskOfPc(long uid, String ip, String riskStrategyKey) {
        ZhuiyaRisk.Client client = ZhuiyaRisk.Client.newBuilder()
                .setApp(ZhuiyaRisk.App.APP_PCYY)
                .setIp(ip)
                .setPlatform(ZhuiyaRisk.Platform.PLATFORM_PC)
                .build();
        ZhuiyaRisk.RiskReq riskReq = ZhuiyaRisk.RiskReq.newBuilder()
                .setUserId(uid)
                .setStrategyKey(riskStrategyKey)
                .setClient(client)
                .build();
        ZhuiyaRisk.RiskRsp riskRsp = riskClient.getProxy().riskCheck(riskReq);

        boolean hitRisk = riskRsp.getRiskResult() != ZhuiyaRisk.RiskResult.RISK_RESULT_VALID;
        if (hitRisk) {
            log.info("uid={} hit risk req={},risk resp={}", uid, JsonFormat.printToString(riskReq), JsonFormat.printToString(riskRsp));
        }

        return hitRisk;
    }

    /**
     * PC弹窗
     **/
    private void popUpPc(UserEnterTemplateEvent event, YONovicePackageComponentAttr attr) {
        // 通用弹窗协议,uri = 100004,noticeType=newUserPackage,noticeValue=PcPopUp,extJson={"sid":123,"ssid":222,"busiId":400,"uid":3333,"app":"Yomi/Zhuiwan"}
        JSONObject extJson = new JSONObject();
        extJson.put("sid", event.getSid());
        extJson.put("ssid", event.getSsid());
        extJson.put("busiId", event.getBusiId());
        extJson.put("app", attr.getApp());
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), attr.getNoticeType(), attr.getNoticeValue()
                , extJson.toJSONString(), event.getUid());
        log.info("popUpPc sid:{}, ssid:{}", event.getSid(), event.getSsid());

        // 上报数据 scoreType = 10 pc弹窗推送
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue((int) event.getBusiId())
                , System.currentTimeMillis(), event.getUid() + "", RoleType.USER, 1, SCORE_TYPE_PC_POP_UP);
    }

    //发送蒙层
    private void popUpMaskPc(long uid, long sid, long ssid, YONovicePackageComponentAttr attr) {
        // 通用弹窗协议,uri = 100004,noticeType=newUserPackage,noticeValue=PcPopUp,extJson={"sid":123,"ssid":222,"busiId":400,"uid":3333,"app":"Yomi/Zhuiwan"}
        JSONObject extJson = new JSONObject();
        extJson.put("sid", sid);
        extJson.put("ssid", ssid);
        extJson.put("busiId", attr.getBusiId());
        extJson.put("app", attr.getApp());
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), attr.getNoticeType(), attr.getMaskNoticeValue()
                , extJson.toJSONString(), uid);
    }

    /**
     * 添加用户到已弹窗集合
     **/
    private void addToAlreadyPopUpSet(long uid, YONovicePackageComponentAttr attr) {
        String popUpUserCacheKey = makeKey(attr, POP_UP_USER_CACHE_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        actRedisDao.sadd(groupCode, popUpUserCacheKey, uid + "");
        log.info("addToAlreadyPopUpSet uid={},key={}", uid, popUpUserCacheKey);
    }

    /**
     * 添加待领取记录
     **/
    private void addToDrawRecord(long uid, UserEnterTemplateEvent event, YONovicePackageComponentAttr attr) {
        String hashKey = makeKey(attr, DRAW_RECORD);
        String groupCode = getRedisGroupCode(attr.getActId());

        NewUserPackageRecord record = new NewUserPackageRecord();
        record.setActId(attr.getActId());
        record.setSid(event.getSid());
        record.setSsid(event.getSsid());
        record.setBusiId((int) event.getBusiId());
        record.setChannelId(attr.getChannelId());
        record.setApp(attr.getApp());

        actRedisDao.hset(groupCode, hashKey, uid + "", JSON.toJSONString(record));
        log.info("addToDrawRecord uid={},hashKey={},record={}", uid, hashKey, JSON.toJSONString(record));
    }

    private void updateDrawRecord(long uid, NewUserPackageRecord record, YONovicePackageComponentAttr attr) {
        String hashKey = makeKey(attr, DRAW_RECORD);
        String groupCode = getRedisGroupCode(attr.getActId());
        actRedisDao.hset(groupCode, hashKey, uid + "", JSON.toJSONString(record));
    }

    public void sendKafkaToZhuiYa(long uid, NewUserPackageRecord record) {
        ServerEvent event = new ServerEvent(uid, record.getApp(), record.getChannelId());

        try {
            SendResult<String, String> result = zhuiyaKafkaTemplate.send("attribution_server_event", JSON.toJSONString(event)).get(10, TimeUnit.SECONDS);
            log.info("sendKafkaToZhuiYa event={},result={}", event, result);
        } catch (Exception ex) {
            log.error("sendKafkaToZhuiYa error,event={}", event, ex);
        }
    }

    /**
     * 获取礼包状态
     **/
    @GetMapping("queryStatus")
    public Response<NewUserPackageStatusResp> queryStatus(HttpServletRequest request, HttpServletResponse response,
                                                          @RequestParam("actId") long actId,
                                                          @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx) {
        YONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }

        long uid = getLoginYYUid(request, response);
        String ip = RequestUtil.getRealIp(request);
        if (uid <= 0 || !inWhiteList(uid, attr) || hitRiskOfPc(uid, ip, attr.getRiskStrategyKey())) {
            return Response.success(new NewUserPackageStatusResp());
        }

        return Response.success(doQueryStatus(uid, attr));
    }

    public NewUserPackageStatusResp doQueryStatus(long uid, YONovicePackageComponentAttr attr) {
        NewUserPackageStatusResp resp = new NewUserPackageStatusResp();
        NewUserPackageRecord record = getNewUserPackageRecord(uid, attr);
        resp.setCanDraw(record != null);
        resp.setHasDraw(record != null && record.getStatus() == 1);

        return resp;
    }

    private NewUserPackageRecord getNewUserPackageRecord(long uid, YONovicePackageComponentAttr attr) {
        if (attr == null) {
            log.warn("not found attr");
            return null;
        }

        String hashKey = makeKey(attr, DRAW_RECORD);
        String groupCode = getRedisGroupCode(attr.getActId());
        String value = actRedisDao.hget(groupCode, hashKey, String.valueOf(uid));
        if (StringUtil.isEmpty(value)) {
            return null;
        }

        return JSON.parseObject(value, NewUserPackageRecord.class);
    }

    /**
     * 领取奖励
     **/
    @PostMapping("receiveAward")
    public Response<String> receiveAward(HttpServletRequest request, HttpServletResponse response,
                                         @RequestParam("actId") long actId,
                                         @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx,
                                         @RequestParam("sid") long sid,
                                         @RequestParam("ssid") long ssid) {
        YONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "not component found!");
        }
        long uid = getLoginYYUid(request, response);
        String ip = RequestUtil.getRealIp(request);
        log.info("receiveAward uid={},ip={},actId={},cmptInx={}", uid, ip, actId, cmptInx);
        if (uid <= 0 || !inWhiteList(uid, attr) || hitRiskOfPc(uid, ip, attr.getRiskStrategyKey())) {
            return Response.fail(400, "不满足领取条件");
        }
        try {
            doReceiveAward(uid, attr);
        } catch (SuperException e) {
            log.warn("uid:{}, actId:{}, cmptInx:{} receice award e:{}", uid, actId, cmptInx, ExceptionUtils.getStackTrace(e));
            return Response.fail(e.getCode(), e.getMessage());
        }
        popUpMaskPc(uid, sid, ssid, attr);
        return Response.success("领取成功");
    }

    public void doReceiveAward(long uid, YONovicePackageComponentAttr attr) {
        NewUserPackageRecord record = getNewUserPackageRecord(uid, attr);
        if (record == null) {
            throw new SuperException("没有待领取记录", 400);
        }

        if (record.getStatus() == 1) {
            throw new SuperException("奖励已领取", 403);
        }

        // 上报数据-- scoreType = 11 领取成功
        sendAward(uid, attr, record);
    }

    private void sendAward(long uid, YONovicePackageComponentAttr attr, NewUserPackageRecord record) {
        if (attr == null) {
            throw new SuperException("没找到属性配置", 400);
        }

        // 发放奖励
        long packageId = attr.getPackageId();
        if (packageId <= 0) {
            throw new SuperException("没有配置奖包", 400);
        }

        // scoreType = 11 点击领取
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue(attr.getBusiId())
                , System.currentTimeMillis(), uid + "", RoleType.USER, 1, SCORE_TYPE_RECEIVE_AWARD);

        String groupCode = getRedisGroupCode(attr.getActId());
        // 限额判断
        // 先设置已领取
        boolean nx = actRedisDao.hsetnx(groupCode, DRAW_USER, uid + "", DateUtil.getNowYyyyMMddHHmmss());
        if (!nx) {
            throw new SuperException("操作太频繁,请稍后重试", 400);
        }
        boolean sendResult = false;

        try {
            int count = attr.getPcRecCount();
            long limit = attr.getDailyLimit();
            String today = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
            if (attr.getDayLimitConfig().containsKey(today)) {
                limit = attr.getDayLimitConfig().get(today);
            }
            List<Long> incResult = actRedisDao.incrValueWithLimit(groupCode, getTodayAwardCountKey(attr), count, limit, false);
            if (incResult.get(0) != 1) {
                throw new SuperException("当前领取人数过多，请明日再来领取。", 400);
            }

            String time = DateUtil.format(commonService.getNow(attr.getActId()));
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(time, attr.getBusiId(), uid, attr.getTaskId(), count, packageId, record.getSeq(), 2);
            log.info("doWelfare result={}", batchWelfareResult);
            sendResult = batchWelfareResult != null && batchWelfareResult.getCode() == 0;

            if (sendResult) {
                record.setStatus(1);
                updateDrawRecord(uid, record, attr);
                sendKafkaToZhuiYa(uid, record);
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), attr.getNoticeType(), attr.getMaskNoticeValue()
                        , "", uid);

                // 上报数据-- scoreType = 12 领取成功
                bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.findByValue(attr.getBusiId())
                        , System.currentTimeMillis(), uid + "", RoleType.USER, 1, SCORE_TYPE_RECEIVE_AWARD_SUCC);
                logBusinessData(attr, ACT_DATA_OBTAIN_TP, 1);
            }
        } finally {
            if (!sendResult) {
                actRedisDao.hdel(groupCode, DRAW_USER, uid + "");
            }
        }
    }

    private long getTodayAwardCount(YONovicePackageComponentAttr attr) {
        String key = getTodayAwardCountKey(attr);
        String groupCode = getRedisGroupCode(attr.getActId());

        long todayAwardCount = Convert.toLong(actRedisDao.get(groupCode, key), 0);
        log.info("key={},todayAwardCount={}", key, todayAwardCount);

        return todayAwardCount;
    }

    private String getTodayAwardCountKey(YONovicePackageComponentAttr attr) {
        String today = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        return makeKey(attr, TODAY_AWARD_COUNT_V2 + today);
    }

    private boolean uidFirstLoginAfterPopup(YONovicePackageComponentAttr attr, long uid) {
        String key = makeKey(attr, ACT_FIRST_APP_UID_LOGIN);
        String groupCode = getRedisGroupCode(attr.getActId());
        Long row = actRedisDao.getRedisTemplate(groupCode).opsForSet().add(key, String.valueOf(uid));
        row = row == null ? 0L : row;
        return row > 0;
    }

    private boolean hdidFirstLoginAfterPopup(YONovicePackageComponentAttr attr, String hdid) {
        String key = makeKey(attr, ACT_FIRST_APP_HDID_LOGIN);
        String groupCode = getRedisGroupCode(attr.getActId());
        Long row = actRedisDao.getRedisTemplate(groupCode).opsForSet().add(key, hdid);
        row = row == null ? 0L : row;
        return row > 0;
    }

    private void logBusinessData(YONovicePackageComponentAttr attr, String tp, int count) {
        String hashKey = makeKey(attr, ACT_DATA_LOG);
        String today = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String hashKeyToday = makeKey(attr, ACT_DATA_DAY_LOG + today);
        String groupCode = getRedisGroupCode(attr.getActId());
        List<Object> list = actRedisDao.getRedisTemplate(groupCode).execute(new RedisCallback<List<Object>>() {
            @Override
            public List<Object> doInRedis(RedisConnection connection) throws DataAccessException {
                connection.openPipeline();
                connection.hIncrBy(hashKey.getBytes(), tp.getBytes(), count);
                connection.hIncrBy(hashKeyToday.getBytes(), tp.getBytes(), count);
                return connection.closePipeline();
            }
        });
    }

    @GetMapping(value = "queryDataLog", produces = "text/html;charset=UTF-8")
    public String queryDataLog(HttpServletRequest request, HttpServletResponse response,
                               @RequestParam("actId") long actId,
                               @RequestParam("cmptInx") long cmptInx) {

        YONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return "<p>未登陆!</p>";
        }
        String whiteListKey = makeKey(attr, DATA_LOG_USER_LIST);
        String groupCode = getRedisGroupCode(attr.getActId());
        boolean isMember = actRedisDao.sIsMember(groupCode, whiteListKey, String.valueOf(uid));
        if (!isMember) {
            return "<p>暂无权限访问！</p>";
        }

        NewUserPackageDataLog today = getTodayDataLog(attr);

        NewUserPackageDataLog total = getTotalDataLog(attr);

        return renderData(today, total);
    }

    private String renderData(NewUserPackageDataLog today, NewUserPackageDataLog total) {
        StringBuilder html = new StringBuilder();
        html.append("<table style='align:center' border>");
        html.append("<thead>").append("<tr>");
        html.append("<th>").append("类型").append("</th>");
        html.append("<th>").append("弹窗Uid数").append("</th>");
        html.append("<th>").append("PC领取Uid数").append("</th>");
        html.append("<th>").append("App领取Uid数").append("</th>");
        html.append("<th>").append("YO语音登陆Uid数").append("</th>");
        html.append("<th>").append("YO交友登陆Uid数").append("</th>");
        html.append("<th>").append("YO语音登陆设备数").append("</th>");
        html.append("<th>").append("YO交友登陆设备数").append("</th>");
        html.append("<th>").append("总共发放礼物数量").append("</th>");
        html.append("<th>").append("总共发放礼物金额（厘）").append("</th>");
        html.append("<th>").append("总共使用礼物个数").append("</th>");
        html.append("<th>").append("总共使用礼物金额（厘）").append("</th>");
        html.append("<th>").append("YO语音礼物使用个数").append("</th>");
        html.append("<th>").append("YO交友礼物使用个数").append("</th>");
        html.append("<th>").append("总共使用用户数").append("</th>");
        html.append("<th>").append("YO语音礼物使用用户数").append("</th>");
        html.append("<th>").append("YO交友礼物使用用户数").append("</th>");

        html.append("<th>").append("点击率").append("</th>");
        html.append("<th>").append("登陆率").append("</th>");
        html.append("<th>").append("新设备占比率").append("</th>");
        html.append("<th>").append("YO语音送礼率").append("</th>");
        html.append("<th>").append("YO交友送礼率").append("</th>");

        html.append("</tr></thead>");
        html.append("<tbody>");
        html.append("<tr>").append("<td>").append("今日").append("</td>");
        addData(html, today);
        html.append("</tr>");

        html.append("<tr>").append("<td>").append("累计").append("</td>");
        addData(html, total);
        html.append("</tr>");

        html.append("</tbody>").append("</table>");

        return html.toString();
    }

    private void addData(StringBuilder html, NewUserPackageDataLog data) {
        html.append("<td>").append(data.getPopUpNum()).append("</td>");
        html.append("<td>").append(data.getObtainNum()).append("</td>");
        html.append("<td>").append(data.getAppObtainNum()).append("</td>");
        html.append("<td>").append(data.getYomiLoginNum()).append("</td>");
        html.append("<td>").append(data.getZhuiwanLoginNum()).append("</td>");
        html.append("<td>").append(data.getYomiHdidLoginNum()).append("</td>");
        html.append("<td>").append(data.getZhuiwanHdidLoginNum()).append("</td>");
        html.append("<td>").append(data.getTotalIssueCount()).append("</td>");
        html.append("<td>").append(data.getTotalIssueAmount()).append("</td>");
        html.append("<td>").append(data.getTotalUsedCount()).append("</td>");
        //TODO:这里礼物单价是写死的
        html.append("<td>").append(data.getTotalUsedCount() * 100).append("</td>");
        html.append("<td>").append(data.getYomiUsedCount()).append("</td>");
        html.append("<td>").append(data.getZhuiwanUsedCount()).append("</td>");
        html.append("<td>").append(data.getTotalUsedUser()).append("</td>");
        html.append("<td>").append(data.getYomiUsedUser()).append("</td>");
        html.append("<td>").append(data.getZhuiwanUsedUser()).append("</td>");

        html.append("<td>").append(data.getPopUpNum() == 0 ? "0" : String.format("%.2f", (double) data.getObtainNum() / (double) data.getPopUpNum())).append("</td>");
        html.append("<td>").append(data.getPopUpNum() == 0 ? "0" : String.format("%.2f", (double) (data.getYomiLoginNum() + data.getZhuiwanLoginNum()) / (double) data.getObtainNum())).append("</td>");
        html.append("<td>").append(data.getYomiLoginNum() + data.getZhuiwanLoginNum() == 0 ? "0" : String.format("%.2f", (double) (data.getYomiHdidLoginNum() + data.getZhuiwanHdidLoginNum()) / (double) (data.getYomiLoginNum() + data.getZhuiwanLoginNum()))).append("</td>");
        html.append("<td>").append(data.getYomiLoginNum() == 0 ? "0" : String.format("%.2f", (double) data.getYomiUsedUser() / (double) data.getYomiLoginNum())).append("</td>");
        html.append("<td>").append(data.getZhuiwanLoginNum() == 0 ? "0" : String.format("%.2f", (double) data.getZhuiwanUsedUser() / (double) data.getZhuiwanLoginNum())).append("</td>");
    }

    private NewUserPackageDataLog getTodayDataLog(YONovicePackageComponentAttr attr) {
        final Date now = new Date();
        long endTime = DateUtils.ceiling(now, Calendar.DAY_OF_MONTH).getTime();
        long startTime = endTime - DateUtils.MILLIS_PER_DAY;

        return doGetDataLog(attr, false, startTime, endTime);
    }

    public NewUserPackageDataLog getTotalDataLog(YONovicePackageComponentAttr attr) {
        ActivityInfoVo actInfo = actInfoService.queryActivityInfo(attr.getActId());
        //只能从这天的00点开始
        long startTime = actInfo.getBeginTime();
        startTime = DateUtils.truncate(new Date(startTime), Calendar.DAY_OF_MONTH).getTime();
        long endTime = actInfo.getEndTime() + 1000;

        if (!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            //2023-07-01 00:00:00
            startTime = 1688140800000L;
            //2056-01-01 00:00:00
            endTime = 2713881600000L;
        }

        return doGetDataLog(attr, true, startTime, endTime);
    }

    private NewUserPackageDataLog doGetDataLog(YONovicePackageComponentAttr attr, boolean isTotal, long startTime, long endTime) {
        final Date now = new Date();
        final String key;
        if (isTotal) {
            key = makeKey(attr, ACT_DATA_LOG);
        } else {
            String today = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            key = makeKey(attr, ACT_DATA_DAY_LOG + today);
        }

        NewUserPackageDataLog result = new NewUserPackageDataLog();

        String groupCode = getRedisGroupCode(attr.getActId());

        List<String> tps = actRedisDao.getRedisTemplate(groupCode).<String, String>opsForHash().multiGet(key, STAT_HASH_KEYS);

        result.setPopUpNum(Convert.toInt(tps.get(0), 0));
        result.setObtainNum(Convert.toInt(tps.get(1), 0));
        result.setAppObtainNum(Convert.toInt(tps.get(2), 0));
        result.setZhuiwanLoginNum(Convert.toInt(tps.get(3), 0));
        result.setYomiLoginNum(Convert.toInt(tps.get(4), 0));
        result.setZhuiwanHdidLoginNum(Convert.toInt(tps.get(5), 0));
        result.setYomiHdidLoginNum(Convert.toInt(tps.get(6), 0));

        List<TActivityPropsDailyStat> stats = tGiftbagStatServiceClient.getActivityPropsDailyStat(attr.getPropAppid(), attr.getPropActivityId(), startTime, endTime);
        if (CollectionUtils.isNotEmpty(stats)) {
            if (isTotal) {
                stats.forEach(stat -> {
                    result.setTotalIssueCount(result.getTotalIssueCount() + stat.totalIssueCount);
                    result.setTotalIssueAmount(result.getTotalIssueAmount() + stat.totalIssueAmount);
                    result.setTotalUsedCount(result.getTotalUsedCount() + stat.totalUseCount);
                    result.setTotalUsedUser(result.getTotalUsedUser() + stat.totalUseUser);
                    if (!StringUtils.startsWith(stat.expand, StringUtil.OPEN_BRACE)) {
                        return;
                    }

                    JSONObject expend = JSON.parseObject(stat.expand);
                    JSONObject usedUserMap = expend.getJSONObject("total_use_user_map");
                    JSONObject usedCountMap = expend.getJSONObject("total_use_count_map");
                    int yomiUsedUser = usedUserMap.getIntValue("157") + usedUserMap.getIntValue("158");
                    int yomiUsedCount = usedCountMap.getIntValue("157") + usedCountMap.getIntValue("158");

                    int zhuiwanUsedUser = usedUserMap.getIntValue("77") + usedUserMap.getIntValue("78");
                    int zhuiwanUsedCount = usedCountMap.getIntValue("77") + usedCountMap.getIntValue("78");

                    result.setZhuiwanUsedUser(result.getZhuiwanUsedUser() + zhuiwanUsedUser);
                    result.setZhuiwanUsedCount(result.getZhuiwanUsedCount() + zhuiwanUsedCount);
                    result.setYomiUsedUser(result.getYomiUsedUser() + yomiUsedUser);
                    result.setYomiUsedCount(result.getYomiUsedCount() + yomiUsedCount);
                });
            } else {
                stats.stream()
                        .filter(stat -> DateUtils.isSameDay(new Date(stat.getDt()), now))
                        .findFirst()
                        .ifPresent(stat -> {
                            result.setTotalIssueCount(stat.totalIssueCount);
                            result.setTotalIssueAmount(stat.totalIssueAmount);
                            result.setTotalUsedCount(stat.totalUseCount);
                            result.setTotalUsedUser(stat.totalUseUser);

                            if (!StringUtils.startsWith(stat.expand, StringUtil.OPEN_BRACE)) {
                                return;
                            }

                            JSONObject expend = JSON.parseObject(stat.expand);
                            JSONObject usedUserMap = expend.getJSONObject("total_use_user_map");
                            JSONObject usedCountMap = expend.getJSONObject("total_use_count_map");
                            int yomiUsedUser = usedUserMap.getIntValue("157") + usedUserMap.getIntValue("158");
                            int yomiUsedCount = usedCountMap.getIntValue("157") + usedCountMap.getIntValue("158");

                            int zhuiwanUsedUser = usedUserMap.getIntValue("77") + usedUserMap.getIntValue("78");
                            int zhuiwanUsedCount = usedCountMap.getIntValue("77") + usedCountMap.getIntValue("78");

                            result.setZhuiwanUsedUser(zhuiwanUsedUser);
                            result.setZhuiwanUsedCount(zhuiwanUsedCount);
                            result.setYomiUsedUser(yomiUsedUser);
                            result.setYomiUsedCount(yomiUsedCount);
                        });
            }
        }

        return result;
    }

    @GetMapping("clearPopUp")
    public Response<String> clearPopUp(@RequestParam("uid") long uid,
                                       @RequestParam("actId") long actId,
                                       @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.success("not accessible!");
        }
        YONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        String popUpUserCacheKey = makeKey(attr, POP_UP_USER_CACHE_KEY);
        String groupCode = getRedisGroupCode(attr.getActId());
        actRedisDao.sRem(groupCode, popUpUserCacheKey, uid + "");
        String hashKey = makeKey(attr, DRAW_RECORD);
        actRedisDao.hdel(groupCode, hashKey, uid + "");
        actRedisDao.hdel(groupCode, DRAW_USER, uid + "");
        actRedisDao.hdel(groupCode, DRAW_USER_APP, uid + "");
        return Response.success("成功");
    }

    @GetMapping("broadcast")
    public Response<String> broadcast(@RequestParam("uid") long uid,
                                      @RequestParam("sid") long sid,
                                      @RequestParam("ssid") long ssid,
                                      @RequestParam("actId") long actId,
                                      @RequestParam(name = "cmptInx", defaultValue = "500") long cmptInx) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.success("not accessible!");
        }
        YONovicePackageComponentAttr attr = getComponentAttr(actId, cmptInx);
        popUpMaskPc(uid, sid, ssid, attr);
        return Response.success("领取成功");
    }

}
