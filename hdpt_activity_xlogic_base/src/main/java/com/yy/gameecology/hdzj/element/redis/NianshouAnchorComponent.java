package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.OnlineChannelService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.attrconfig.NianshouStatus;
import com.yy.gameecology.hdzj.element.attrconfig.NianshowCreateEvent;
import com.yy.gameecology.hdzj.element.component.attr.NianshouAnchorComponentAttr;
import com.yy.gameecology.hdzj.element.event.NianShouRandomLotteryEvent;
import com.yy.gameecology.hdzj.element.event.NianShouSuccessFightEvent;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @Author: yulianzhu
 * @Desciption: 新年兽玩法:与V1不同点:
 * 1. V1使用的是轮询定时广播,V2使用挂件的更新机制,在送礼后(和定时(时间较长))更新
 * <p>
 * 2.V1是单独的弹窗,V2是挂件的形式,单独弹窗使用组件内的广播,挂件使用的是挂件扩展字段进行信息返回
 * 3.V1年兽是主播达到条件后触发,V2年兽是定时生成
 * 4.V1神豪需先获得鞭炮后才能参与,V2只需送礼即可
 * 5.V1年兽绑定的是频道,V2年兽绑定的是主播
 * @Date: 2022-12-23
 * @Modified:
 */
@UseRedisStore
@Component
public class NianshouAnchorComponent extends BaseActComponent<NianshouAnchorComponentAttr> {
    @Autowired
    private BroActLayerService broActLayerService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    public static final String NIANSHOU_CREATE_KEY = "nianshou_create_key";

    //主播和年兽ID和时间戳 anchor_ns_id_map:uid {status|timestamp|id}
    public static final String ANCHOR_NS_ID_MAP = "anchor_ns_id_map";

    //年兽血量 ns_blood_map:uid  {nsId:血量} hincrby 年兽数据较多,按主播uid区分
    public static final String NS_BLOOD_MAP = "ns_blood_map:%s";

    // 记录年兽参与抽奖的用户 redis string key，用来标记参与顺序， 变化部分是 年兽ID  zset  uid:分值
    public static final String NS_ATTEND = "ns_attend:%s";

    //主播的所有年兽记录, map, {nsId:createTime}
    // public static final String ANCHOR_NS_RECORD = "anchor_ns_record_%s";

    public static final String NS_ID_FORM = "%s|%s|%s";

    //年兽死亡时刻,用于计算倒计时, 年兽数量较多,按主播分 ns_dead_moment:uid
    public static final String NS_DEAD_MOMENT = "ns_dead_moment:%s";

    public static final String ALL_CHANNEL_SID = "all_channel_sid";

    public static final String REFRESH_NS_LAYOUT = "refresh_ns_layout" + SysEvHelper.getGroup();


    public void refreshLayoutNianshou(NianshowCreateEvent event) {
        String member = event.getMember();
        refreshLayout(event.getActId(), Convert.toLong(member));
        log.info("refreshLayoutNianshou done, event:{}", JSON.toJSONString(event));
    }

    public void createNewNianshou(NianshowCreateEvent event) {
        log.info("createNewNianshou start event:{}", JSON.toJSONString(event));
        NianshouAnchorComponentAttr attr = getComponentAttr(event.getActId(), event.getUseIndex());
        String member = event.getMember();
        String seq = event.getSeq();
        String nsIdMapKey = makeKey(attr, ANCHOR_NS_ID_MAP);
        String groupCode = getRedisGroupCode(attr.getActId());
        String oldNsId = actRedisDao.hget(groupCode, nsIdMapKey, member);
        if (!oldNsId.startsWith(NianshouStatus.DEAD_HOLDING.getStatus())) {
            log.info("createNewNianshou done, has been created,seq:{} anchor:{}, nsInfoId:{}", seq, member, oldNsId);
            return;
        }

        Date now = commonService.getNow(attr.getActId());
        long nowSecond = DateUtil.getSeconds(now);
        String nsId = createNsId();
        String nsInfoId = String.format(NS_ID_FORM, NianshouStatus.CREATED.getStatus(), nowSecond, nsId);
        actRedisDao.hset(groupCode, nsIdMapKey, member, nsInfoId);
        log.info("createNewNianshou done,seq:{} anchor:{},new  nsInfoId:{}", seq, member, nsInfoId);
        //刷新挂件
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            refreshLayout(event.getActId(), Convert.toLong(member));

        });

        try {
            //TODO: replace with component deley
//            delayQueueService.setDelayEvent(REFRESH_NS_LAYOUT, attr.getFightTime(), event);
        } catch (Exception e) {
            log.error("createNewNianshou refreshLayout error, event:{}", JSON.toJSONString(event), e);
        }
    }


    //TODO: replace with component deley
//    @EventListener(ContextRefreshedEvent.class)
//    @NeedRecycle(author = "yulianzhu", notRecycle = true)
    public void onContextRefreshedEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() != null) {
            return;
        }

        if (SysEvHelper.isLocal()) {
            return;
        }

        String delayKey = buildDelayKey();
        log.info("initNianshowCreateThread delayKey={}", delayKey);

        //TODO: replace with component deley
//        delayQueueService.startDelaySettlement(delayKey, 1, NianshowCreateEvent.class, this::createNewNianshou);
//
//        delayQueueService.startDelaySettlement(REFRESH_NS_LAYOUT, 1, NianshowCreateEvent.class, this::refreshLayoutNianshou);

        log.info("initNianshowCreateThread done delayKey={}, refreshKey:{}", delayKey, REFRESH_NS_LAYOUT);
    }

    @Scheduled(cron = "*/30 * * * * ? ")
    @NeedRecycle(author = "yulianzhu", notRecycle = true)
    public void runCreateNianshow() {
        if (SysEvHelper.isLocal()) {
            return;
        }
        Set<Long> activityIds = getComponentEffectActIds();
        for (Long activityId : activityIds) {
            if (!actInfoService.inActTime(activityId)) {
                continue;
            }
            List<NianshouAnchorComponentAttr> componentAttrs = getAllComponentAttrs(activityId);
            for (NianshouAnchorComponentAttr componentAttr : componentAttrs) {
                runCreate(componentAttr);
            }
        }
    }

    public void runCreate(NianshouAnchorComponentAttr attr) {
        Clock clock = new Clock();
        List<Integer> shouldCreate = attr.getGenNsMinute();
        Date now = commonService.getNow(attr.getActId());
        int minute = DateUtil.getMinute(now);
        if (!shouldCreate.contains(minute)) {
            return;
        }

        String dayStr = DateUtil.format(now, DateUtil.PATTERN_TYPE9);
        long actId = attr.getActId();
        long expireTimeOneMonth = 2592000;
        String groupCode = redisConfigManager.getGroupCode(actId);
        String createdMark = "create_mark_" + dayStr;
        if (!actRedisDao.setNX(groupCode, makeKey(attr, createdMark), Const.ONESTR, expireTimeOneMonth)) {
            return;
        }
        clock.tag();

        //todo 获取当前业务所有的在麦主播 done OnlineChannelService
        int busiId = attr.getBusiId();
        List<Long> anchorUids = getAllOnlineAnchorByBusiId(attr, groupCode, busiId);
        clock.tag();

        //需要重新获取一次时间,因为获取在线主播比较耗时
        now = commonService.getNow(actId);

        String anchor2nsKey = makeKey(attr, ANCHOR_NS_ID_MAP);
        Map<Object, Object> anchor2nsMap = actRedisDao.hGetAll(groupCode, anchor2nsKey);
        clock.tag();

        List<String> keys = new ArrayList<>();
        List<String> values = new ArrayList<>();
        long nowSeconds = DateUtil.getSeconds(now);
        List<String> members = anchorUids.stream().map(String::valueOf).collect(Collectors.toList());
        for (String anchorUid : members) {
            try {
                exeCreateNs(attr, groupCode, anchor2nsMap, keys, values, nowSeconds, anchorUid);
            } catch (Exception e) {
                log.error("exeCreateNs error, anchorUid:{}", anchorUid);
            }
        }

        clock.tag();

        List<Object> resultList = actRedisDao.hsetBatchKey(groupCode, anchor2nsKey, keys, values, 0L);
        log.info("runCreate done: clock:{} anchorUids size:{}, shouldCreate size:{},created size:{}", clock.tag(), anchorUids.size(), keys.size(), resultList.size());

        if (clock.elapse() > attr.getCreTimeHold()) {
            sendBaiduInfoFlowMsg(actId, false, "创建年兽时间点 : " + DateUtil.format(now), "主播数量 :" + anchorUids.size(), "新创建的年兽数量 :" + keys.size(), "创建成功的年兽数量 :" + resultList.size(), "耗时:" + clock, "注 :主播数量和需创建年兽数量不一致是因为一些主播正在打年兽");
        }

        //todo 主动异步推送更新挂件 done
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            updateLayout(actId, anchorUids);
            try {
                cleanupExpireNs(actId, attr.getFightTime(), anchor2nsKey, anchor2nsMap, members);
            } catch (Exception e) {
                log.error("cleanupExpireNs error", e);
            }

        });
    }

    public void cleanupExpireNs(long actId, long fightTime, String anchor2nsIdKey, Map<Object, Object> oldAnchor2nsMap, List<String> newAnchorUid) {
        Clock clock = new Clock();
        //1.从旧的减去已新建年兽的主播
        //2.判断剩余的年兽时间
        //3.过期则设置
        oldAnchor2nsMap.entrySet().removeIf(entry -> newAnchorUid.contains(entry.getKey()));
        Map<String, String> needCleanMap = new HashMap<>();
        Date now = commonService.getNow(actId);
        long nowSecond = DateUtil.getSeconds(now);
        for (Map.Entry<Object, Object> entry : oldAnchor2nsMap.entrySet()) {
            String nsInfoId = String.valueOf(entry.getValue());
            long startSecond = Convert.toLong(nsInfoId.split("\\|")[1]);
            //超两个战斗时间可认为已过期
            if (startSecond + fightTime + fightTime < nowSecond) {
                needCleanMap.put(String.valueOf(entry.getKey()), nsInfoId);
            }
        }
        clock.tag();
        String groupCode = redisConfigManager.getGroupCode(actId);
        for (Map.Entry<String, String> entry : needCleanMap.entrySet()) {
            boolean del = actRedisDao.hCompareAndDel(groupCode, anchor2nsIdKey, entry.getKey(), entry.getValue());
            log.info("cleanupExpireNs del nsId uid:{},nsId:{} del:{}", entry.getKey(), entry.getValue(), del);
        }
        log.info("cleanupExpireNs done, clean size:{}, nowSecond:{}, clock:{}", needCleanMap.size(), nowSecond, clock.tag());

    }

    private void exeCreateNs(NianshouAnchorComponentAttr attr, String groupCode, Map<Object, Object> anchor2nsMap, List<String> keys, List<String> values, long nowSeconds, String member) {
        Object obj = anchor2nsMap.get(member);
        String id = createNsId();
        String value = String.format(NS_ID_FORM, NianshouStatus.CREATED.getStatus(), nowSeconds, id);
        if (obj != null) {
            String nsId = String.valueOf(obj);
            //格式: status|timestamp|nsId
            String[] split = nsId.split("\\|");
            //开始的时间点
            long startSecond = Convert.toLong(split[1]);
            //打败结算中,由结算那边创建

            if (NianshouStatus.DEAD_HOLDING.getStatus().equals(split[0])) {
                long deadSecond = Convert.toLong(actRedisDao.hget(groupCode, makeKey(attr, String.format(NS_DEAD_MOMENT, member)), nsId));
                //当两个showTime都未创建,可认为结算创建年兽失败,这里一并创建新的年兽
                if (nowSeconds > deadSecond + attr.getShowResultTime() + attr.getShowResultTime()) {
                    keys.add(member);
                    values.add(value);
                }
                return;
            }

            //表示未过期了,无需创建新的
            if (nowSeconds - startSecond < attr.getFightTime()) {
                return;
            }


        }

        keys.add(member);
        values.add(value);
    }

    private String createNsId() {
        return UUID.randomUUID().toString().replace("-", "");
    }


    private void updateLayout(long actId, List<Long> anchorUids) {
        for (Long anchorUid : anchorUids) {
            refreshLayout(actId, anchorUid);
        }
    }

    private void refreshLayout(long actId, Long anchorUid) {
        ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(anchorUid);
        if (channelInfoVo != null) {
            //获得主播所在频道,更新其挂件
            OnlineChannelInfo info = new OnlineChannelInfo();
            info.setSid(channelInfoVo.getSid());
            info.setSsid(channelInfoVo.getSsid());
            broActLayerService.broChannel(actId, info);
        }
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void fightingNianshou(RankingScoreChanged event, NianshouAnchorComponentAttr attr) {
        Clock clock = new Clock();
        long actId = event.getActId();
        if (attr.getNsRankId() != event.getRankId()) {
            return;
        }

        String member = event.getMember();
        //1.获取主播uid,获得年兽ID
        String anchorNsIdMapKey = makeKey(attr, ANCHOR_NS_ID_MAP);
        String groupCode = redisConfigManager.getGroupCode(actId);

        String nsInfoId = actRedisDao.hget(groupCode, anchorNsIdMapKey, member);
        if (StringUtil.isBlank(nsInfoId)) {
            return;
        }
        String[] split = nsInfoId.split("\\|");

        //年兽已挂或未开始
        if (NianshouStatus.DEAD_HOLDING.getStatus().equals(split[0])) {
            log.info("ns has been dead,seq:{} nsId:{}, event:{}, clock:{}", event.getSeq(), nsInfoId, JSON.toJSONString(event), clock.tag());
            return;
        }
        Date now = commonService.getNow(actId);
        long startTime = Convert.toLong(split[1]);

        //已超打年兽时间
        long nowSecond = DateUtil.getSeconds(now);
        if (nowSecond - startTime >= attr.getFightTime()) {
            log.info("ns out of time,seq:{} nsId:{}, nowSecond:{} event:{}, clock:{}", event.getSeq(), nsInfoId, nowSecond, JSON.toJSONString(event), clock.tag());
            return;
        }

        //2.计算年兽血量
        String bloodKey = makeKey(attr, String.format(NS_BLOOD_MAP, member));
        long itemScore = event.getItemScore();
        List<Long> list = actRedisDao.hIncrWithLimit(groupCode, bloodKey, nsInfoId, itemScore, attr.getNsTotalBlood(), true);
        long incr = itemScore;
        boolean success = false;
        String playerUid = event.getActors().get(attr.getPlayRoleId());
        //部分添加
        long partAdd = 2;
        String seq = event.getSeq();
        if (list.get(0) == partAdd) {
            incr = list.get(1);
            success = true;
            //全部添加
        } else if (list.get(0) == 1) {
            success = attr.getNsTotalBlood() <= list.get(1);
            //不添加
        } else {
            log.info("ns has been dead,seq:{} nsId:{}, playerUid:{}, itemScore:{}, hincr:{}, clock:{}", seq, nsInfoId, playerUid, itemScore, list, clock.tag());
            return;
        }

        //3.添加至贡献榜
        String attendKey = makeKey(attr, String.format(NS_ATTEND, nsInfoId));
        long score = actRedisDao.zincrWithTime(groupCode, attendKey, playerUid, incr);
        log.info("player attend done,seq:{} nsId:{}, playerUid:{}, incr:{}, ns dead:{}, clock:{}", seq, nsInfoId, playerUid, incr, success, clock.tag());

        //todo 随机抽奖 done
        long src = score - incr;
        long drawCount = score / attr.getDrawHold() - src / attr.getDrawHold();
        if (drawCount > 0) {
            NianShouRandomLotteryEvent randomLotteryEvent = new NianShouRandomLotteryEvent(actId, (int) drawCount, Convert.toLong(playerUid, 0), seq);
            // TODO: replace with hdzk kafka event
//            hdzjEventDispatcher.notify(actId, randomLotteryEvent, randomLotteryEvent.getSeq());
            log.info("player can draw, seq:{}, playerUid:{}, count:{}", seq, playerUid, drawCount);
        }
        final long cutBlood = incr;

        //todo 成功打败年兽,异步抽奖,  需要注意抽奖去重,一只年兽只能抽一次奖 done
        if (success) {
            //1.更换年兽状态 done
            String deadNsId = changeNsStatus(NianshouStatus.DEAD_HOLDING.getStatus(), split[1], split[2]);
            actRedisDao.hset(groupCode, anchorNsIdMapKey, member, deadNsId);
            actRedisDao.hset(groupCode, makeKey(attr, String.format(NS_DEAD_MOMENT, member)), deadNsId, nowSecond + "");
            //TODO: replace with component deley
//            delayQueueService.setDelayEvent(buildDelayKey(), 9, new NianshowCreateEvent(seq, actId, attr.getCmptUseInx(), member));
            log.info("nianshow is dead now, member:{} seq:{}, nsId:{}, now:{}, clock:{}", member, seq, nsInfoId, now, clock.tag());
            //2.todo 抽奖 done
            List<String> luckMember = new ArrayList<>();
            Set<ZSetOperations.TypedTuple<String>> tuples = actRedisDao.zrevRange(groupCode, attendKey, attr.getTopNBigDraw());
            for (ZSetOperations.TypedTuple<String> tuple : tuples) {
                if (Convert.toLong(tuple.getScore()) >= attr.getBigDrawHold()) {
                    luckMember.add(tuple.getValue());
                }
            }
            if (CollectionUtils.isEmpty(luckMember)) {
                luckMember = tuples.stream().map(ZSetOperations.TypedTuple::getValue).collect(Collectors.toList());
            }
            Collections.shuffle(luckMember);
            String luckMan = luckMember.get(0);

            // 发布年兽打败事件,用于抽大奖，上报榜单、发广播
            NianShouSuccessFightEvent successFightEvent = new NianShouSuccessFightEvent(actId, nsInfoId, seq,
                    Convert.toLong(member), Convert.toLong(playerUid), Convert.toLong(luckMan));
            // TODO: replace with hdzk kafka event
//            hdzjEventDispatcher.notify(actId, successFightEvent, successFightEvent.getSeq());
            log.info("NianShouSuccessFightEvent seq:{}, nsId:{} luckMan:{}, top:{}", seq, nsInfoId, luckMan, JSON.toJSONString(tuples));

        }
        //todo 4.主动推送扣血广播 done
        // todo 5.主动更新挂件 done
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            broFightingNianshou(seq, attr, cutBlood, member, playerUid, nsInfoId, itemScore);
            refreshLayout(actId, Convert.toLong(member));
        });
    }

    private String buildDelayKey() {
        return NIANSHOU_CREATE_KEY + SysEvHelper.getGroup();
    }

    //主播昵称, 神豪昵称, 打掉多少血,剩余多少血,
    private void broFightingNianshou(String seq, NianshouAnchorComponentAttr attr, long cutBlood, String anchor, String player, String nsId, long giftCount) {
        long anchorUid = Convert.toLong(anchor);
        ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(anchorUid);
        if (channelInfoVo == null) {
            return;
        }
        JSONObject data = new JSONObject();

        data.put("cutBlood", cutBlood);
        data.put("anchorNick", commonService.getNickName(anchorUid, false));
        long playerUid = Convert.toLong(player);
        data.put("playerNick", commonService.getNickName(playerUid, false));

        data.put("anchorUid", anchorUid);
        data.put("playerUid", playerUid);
        data.put("nsId", nsId);
        data.put("totalBlood", attr.getNsTotalBlood());
        data.put("giftName", attr.getGiftName());
        data.put("giftCount", giftCount);

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.NIANSHOU_BOX)
                //.setNoticeMsg()
                //.setNoticeValue()
                .setExtJson(JSON.toJSONString(data));

        GameecologyActivity.GameEcologyMsg msgPanel = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.broadcastSub(channelInfoVo.getSid(), channelInfoVo.getSsid(), msgPanel);
        log.info("broFightingNianshou done, seq:{} nsId:{}, cutblood:{}, playerUid:{}", seq, nsId, cutBlood, playerUid);

    }

    private String changeNsStatus(String status, String startTime, String nsId) {
        return String.format(NS_ID_FORM, status, startTime, nsId);

    }

    @Override
    public Long getComponentId() {
        return ComponentId.NIANSHOW_ANCHOR;
    }


    public Map<String, Object> queryAnchorNsINfo(Date now, NianshouAnchorComponentAttr attr, String member) {
        Map<String, Object> ext = Maps.newHashMap();
        long actId = attr.getActId();
        String nsIdMap = makeKey(attr, ANCHOR_NS_ID_MAP);
        String groupCode = getRedisGroupCode(actId);
        String redisGroupCode = groupCode;
        String nsInfoId = actRedisDao.hget(redisGroupCode, nsIdMap, member);
        //返回状态为0, 未开始
        if (StringUtil.isBlank(nsInfoId)) {
            ext.put("status", NianshouStatus.NOT_START.getStatus());
            return ext;
        }
        long nowSecond = DateUtil.getSeconds(now);
        long startSecond = Convert.toLong(nsInfoId.split("\\|")[1]);
        long leftBlood = anchorNsLeftBlood(actId, member, nsInfoId, attr);
        long leftSecond = -1;
        //超时,未开始
        if (startSecond + attr.getFightTime() <= nowSecond && leftBlood > 0) {
            ext.put("status", NianshouStatus.NOT_START.getStatus());
            return ext;
        }

        if (startSecond + attr.getFightTime() > nowSecond) {
            ext.put("status", NianshouStatus.CREATED.getStatus());
            leftSecond = startSecond + attr.getFightTime() - nowSecond;
        }

        if (nsInfoId.startsWith(NianshouStatus.DEAD_HOLDING.getStatus())) {
            ext.put("status", NianshouStatus.DEAD_HOLDING.getStatus());
            long deadSecond = Convert.toLong(actRedisDao.hget(groupCode, makeKey(attr, String.format(NS_DEAD_MOMENT, member)), nsInfoId));
            leftSecond = deadSecond + attr.getShowResultTime() - nowSecond;
        }

        ext.put("leftBlood", leftBlood);
        ext.put("leftSecond", Math.max(leftSecond, 0));
        ext.put("nsId", nsInfoId);
        ext.put("totalBlood", attr.getNsTotalBlood());
        return ext;
    }

    private long anchorNsLeftBlood(long actId, String member, String nsInfoId, NianshouAnchorComponentAttr attr) {
        String redisGroupCode = getRedisGroupCode(actId);
        //计算下一条出现的倒计时
        if (StringUtil.isBlank(nsInfoId)) {
            return -1;
        }
        if (nsInfoId.startsWith(NianshouStatus.DEAD_HOLDING.getStatus())) {
            return 0;
        }
        long hasCut = Convert.toLong(actRedisDao.hget(redisGroupCode, makeKey(attr, String.format(NS_BLOOD_MAP, member)), nsInfoId));
        return attr.getNsTotalBlood() - hasCut;
    }


    //获取top5贡献神豪
    public List<JSONObject> topNPlayer(long actId, long useIndex, String nsId, long topN) {
        List<JSONObject> userInfos = new ArrayList<>();
        if (StringUtil.isBlank(nsId)) {
            return userInfos;
        }
        nsId = getSrcNsId(nsId);

        NianshouAnchorComponentAttr attr = getComponentAttr(actId, useIndex);
        String nsAttendKey = makeKey(attr, String.format(NS_ATTEND, nsId));
        Set<ZSetOperations.TypedTuple<String>> tuples = actRedisDao.zrevRange(getRedisGroupCode(actId), nsAttendKey, topN);
        for (ZSetOperations.TypedTuple<String> tuple : tuples) {
            JSONObject userInfo = new JSONObject();
            long uid = Convert.toLong(tuple.getValue());
            userInfo.put("nickName", commonService.getNickName(uid, false));
            userInfo.put("score", Convert.toLong(tuple.getScore()));
            userInfos.add(userInfo);
        }
        return userInfos;
    }

    private String getSrcNsId(String nsId) {
        String deadHold = NianshouStatus.DEAD_HOLDING.getStatus();
        if (nsId.startsWith(deadHold)) {
            return nsId.replaceFirst(deadHold, NianshouStatus.CREATED.getStatus());
        }
        return nsId;
    }

    public List<ChannelInfo> getAllChannelInfoRetry(int busiId, int retry) {
        int tryTime = 1;
        long oneSecond = 1000;
        List<ChannelInfo> channelInfoList = new ArrayList<>();
        do {
            if (busiId == BusiId.MAKE_FRIEND.getValue()) {
                channelInfoList = commonService.queryOnlineChannel(Template.Jiaoyou);
            } else if (busiId == BusiId.GAME_BABY.getValue()) {
                channelInfoList = commonService.queryOnlineChannel(Template.Gamebaby);
            }
            if (!channelInfoList.isEmpty()) {
                return channelInfoList;
            } else {
                SysEvHelper.waiting(oneSecond);
                tryTime++;
            }
        } while (tryTime >= retry);

        return channelInfoList;
    }

    private List<Long> getAllOnlineAnchorByBusiId(NianshouAnchorComponentAttr attr, String groupCode, int busiId) {
        List<Long> sids;
        List<Long> onlineAnchors = new ArrayList<>();
        int retry = 10;
        List<ChannelInfo> channelInfoList = getAllChannelInfoRetry(busiId, retry);
        String allSidKey = makeKey(attr, ALL_CHANNEL_SID);
        if (channelInfoList.isEmpty()) {
            Set<String> sidStr = actRedisDao.sMembers(groupCode, allSidKey);
            sids = sidStr.stream().map(Convert::toLong).collect(Collectors.toList());
            sendBaiduInfoFlowMsg(attr.getActId(), true, "获取活跃频道信息失败,请排查异常", "使用缓存的频道信息, 数量:" + sids.size());
        } else {
            sids = channelInfoList.stream().map(ChannelInfo::getSid).collect(Collectors.toList());
            threadPoolManager.get(Const.GENERAL_POOL).execute(() -> actRedisDao.sBatchAdd(groupCode, allSidKey, sids.stream().map(String::valueOf).toArray(String[]::new)));
            log.info("getAllChannelInfoRetry done, sids size:{}", sids.size());
        }

        Map<Long, Map<Long, OnlineChannelInfo>> onlineChannelMap = onlineChannelService.getAllOnlineChannelMap();
        //映射多一份根据主播查所在开播频道数据
        if (MapUtils.isNotEmpty(onlineChannelMap)) {
            for (Long sid : onlineChannelMap.keySet()) {
                Map<Long, OnlineChannelInfo> ssidAnchorOnlineChannelMap = onlineChannelMap.get(sid);
                for (Long ssid : ssidAnchorOnlineChannelMap.keySet()) {
                    OnlineChannelInfo onlineChannelInfo = ssidAnchorOnlineChannelMap.get(ssid);
                    if (onlineChannelInfo != null && CollectionUtils.isNotEmpty(onlineChannelInfo.getEffectAnchorId())) {
                        if (sids.contains(sid)) {
                            onlineAnchors.addAll(onlineChannelInfo.getEffectAnchorId());
                        }
                    }
                }
            }
        }
        log.info("getAllOnlineAnchorByBusiId done, sids size:{},onlineAnchors size:{}", sids.size(), onlineAnchors);
        return onlineAnchors;
    }

    private void sendBaiduInfoFlowMsg(long actId, boolean error, Object... param) {
        StringBuilder sb = new StringBuilder();
        for (Object par : param) {
            sb.append(par).append('\n');
        }
        String msg = buildActRuliuMsg(actId, error, "年兽玩法", sb.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
    }


}
