package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-20 16:07
 **/
@TableColumn(underline = true)
public class GeSignInfo implements Serializable {
    public static String TABLE_NAME = "ge_sign_info";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[] {"uid","sid"};

    public static RowMapper<GeSignInfo> ROW_MAPPER = null;

    private Long uid;
    private Long sid;
    /**
     * 签约频道短号
     */
    private Long asId;
    /**
     * 1-交友 2-约战 3-宝贝
     */
    private Integer templateType;
    private Integer status;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getSid() {
        return sid;
    }

    public void setSid(Long sid) {
        this.sid = sid;
    }

    public Integer getTemplateType() {
        return templateType;
    }

    public void setTemplateType(Integer templateType) {
        this.templateType = templateType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getAsId() {
        return asId;
    }

    public void setAsId(Long asId) {
        this.asId = asId;
    }
}
