package com.yy.gameecology.activity.bean.acttask;


import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;

@Data
public class TaskAwardConfig {

    @ComponentAttrField(labelText = "任务名称")
    private String taskName;

    @ComponentAttrField(labelText = "任务等级")
    private long taskLevel;

    @ComponentAttrField(labelText = "获奖文案")
    private String awardText="";

    @ComponentAttrField(labelText = "奖品url")
    private String awardUrl ="";

    @ComponentAttrField(labelText = "奖品名称")
    private String awardName="";

    @ComponentAttrField(labelText = "奖品数量")
    private long awardCount;

}
