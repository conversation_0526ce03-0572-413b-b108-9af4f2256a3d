package com.yy.gameecology.activity.service.datatransfer.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.KeyCopyResult;
import com.yy.gameecology.activity.service.datatransfer.RedisDataCopy;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-08-22 20:17
 **/
@Component
public class RedisZSetDataCopy  extends BaseRedisData implements RedisDataCopy {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    private static final int BATCH_SCAN_SIZE = SysEvHelper.isDeploy() ? 500 : 5;


    @Override
    public KeyCopyResult copy(String oldRedisGroup, String newRedisGroup, String key) {
        Clock clock = new Clock();

        checkHashKey(newRedisGroup, key);

        int size = BATCH_SCAN_SIZE;
        int start = 0;
        int end = size;
        Set<ZSetOperations.TypedTuple<String>> setDatas = Sets.newHashSet();

        Set<ZSetOperations.TypedTuple<String>> result = actRedisGroupDao.getRedisTemplate(oldRedisGroup).opsForZSet().rangeWithScores(key, start, end);
        while (CollectionUtils.isNotEmpty(result)) {
            setDatas.addAll(result);

            start = start + size + 1;
            end = start + size;
            result = actRedisGroupDao.getRedisTemplate(oldRedisGroup).opsForZSet().rangeWithScores(key, start, end);
        }


        List<List<ZSetOperations.TypedTuple<String>>> batchData = Lists.partition(Lists.newArrayList(setDatas), BATCH_SCAN_SIZE);
        batchData.forEach(p -> {
            actRedisGroupDao.zAddSets(newRedisGroup, key, Sets.newHashSet(p));
        });


        Long seconds = actRedisGroupDao.getExpire(oldRedisGroup, key);
        if (Convert.toLong(seconds, 0) > 0) {
            actRedisGroupDao.expire(newRedisGroup, key, seconds);
        }

        log.info("copy ok,old group:{},new group:{},key:{},size:{},cost:{}", oldRedisGroup, newRedisGroup, key, setDatas.size(), clock.tag());

        if (Convert.toLong(seconds, 0) <= 0) {
            log.warn("key expire time warn,key:{}", key);
        }
        KeyCopyResult copyResult = new KeyCopyResult();
        copyResult.setKey(key);
        copyResult.setExpireSeconds(seconds);
        return copyResult;
    }
}
