package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.*;
import com.yy.apphistory.y2021.act2021041001.bean.JyWeekStarInfo;
import com.yy.apphistory.y2022.act2022026001.HaiduDataReporting;
import com.yy.apphistory.y2022.act2022jyjfs.bean.*;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.RankDataEvent;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.hdzt.*;
import com.yy.gameecology.activity.bean.mq.ChannelFightEndEvent;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.bean.turnover.PropsInfo;
import com.yy.gameecology.activity.bean.turnover.PropsPricing;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.client.thrift.FtsGroupCenterThriftClient;
import com.yy.gameecology.activity.client.thrift.TurnoverServiceClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.exception.BadRequestException;
import com.yy.gameecology.activity.exception.ParameterException;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingHandle;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.HdzjComponentCollaborator;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.ActJYJFSComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.fts_base_info_bridge.ChannelFightRsp;
import com.yy.thrift.fts_group_center.QueryType;
import com.yy.thrift.fts_group_center.Source;
import com.yy.thrift.fts_group_center.Visibility;
import com.yy.thrift.hdztranking.*;
import com.yy.thrift.turnover.TCurrentWeekPropRank;
import com.yy.thrift.turnover.TDatingAnchorRank;
import com.yy.thrift.turnover.TRank;
import com.yy.thrift.turnover.TWeekPropRank;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.yy.apphistory.y2021.act2021041001.CJyjfsai.*;

/**
 * @Author: CXZ
 * @Desciption: 交友积分赛
 * @Date: 2022/2/18 16:39
 * @Modified:
 */
@UseRedisStore
@Component
public class JYJFSComponent extends BaseActComponent<ActJYJFSComponentAttr>
        implements ComponentRankingHandle<ActJYJFSComponentAttr>, ComponentRankingExtHandle<ActJYJFSComponentAttr> {

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    protected TurnoverServiceClient turnoverServiceClient;


    @Autowired
    protected FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    protected FtsGroupCenterThriftClient groupCenterThriftClient;

    @Autowired
    protected OnlineChannelService onlineChannelService;

    @Autowired
    protected UserInfoService userInfoService;

    @Autowired
    private JyWeekStartService jyWeekStartService;

    @Autowired
    private EnrollmentService enrollmentService;

    @Autowired
    private HaiduDataReporting haiduDataReporting;

    @Lazy
    @Autowired
    private HdzjComponentCollaborator componentCollaborator;

//    @Autowired
//    protected DelayQueueService delayQueueService;

    @Autowired
    @Lazy
    private JYJFSComponent self;

    private static final long FIRST_SEASON_ACT_ID = 2022026001;

    private static final List<String> FILTER_ITEM_IDS = Arrays.asList("JY_QY_ZC", "JY_QY_TT", "JY_QY_ZG", "JY_QY_SP");

    /**
     * 用户积分记录  （：anchorUid）
     */
    private static final String SCORE_RECORD_KEY = "score_record:%s";

    /**
     * 4赛季合一
     **/
    private static final Long JF_ACT_ID = 2022006000L;

    /**
     * 数据上报防重
     */
    private static final String UPDATE_SEQ_KEY = "update_seq";

    /**
     * 组件缓存的key
     */
    private static final String COMPONENT_CACHE_KEY = "component_cache:%s_%s_%s";

    /**
     * 组件缓存的key
     */
    private static final String DEBRIS_COMPOUND_LIMIT_KEY = "debris_compound_limit";
    /**
     * 周星保持延迟结算的key --保持真实的时间  （每周一日期 yyyyMMdd）
     */
    public static final String SETTLE_WEEK_STAR_DELAY_TIME = "settleWeekStarDelayTime_%s";
    /**
     * 周星完成结算标识  （每周一日期 yyyyMMdd）
     */
    public static final String SETTLE_WEEK_STAR_FLAG = "settleWeekStarFlag_%s";

    /**
     * pk 赏金赛海选赛结算标识
     */
    public static final String PK_SETTLE_FLAG = "pkSettleFlag";

    /**
     * 上一轮周星奖励
     */
    public static final String LAST_WEEK_STAR_AWARD = "last_week_star_award";

    /**
     * 上一轮乱斗奖励
     */
    public static final String LAST_FIGHT_AWARD = "last_fight_award";

    /**
     * 组件缓存秒数
     */
    private static final Long COMPONENT_CACHE_SEC = 3L;

    private static final String SCORE_RECORD_MERGE_FLAG = "scoreRecordMergeFlag";

    private static final Pattern RECORD_KEY_PATTERN = Pattern.compile("^act:\\d+:hdzj_cmpt:2022026001:\\d+:score_record:(\\d+)$");

    private static final Map<String, Long> END_WEEK_STAR_DAY = ImmutableMap.of("20220530", 2022066003L, "20220711", 2022076004L);

    @Override
    public Long getComponentId() {
        return 2022026001L;
    }

    @Override
    public List<Rank> queryRank(ActJYJFSComponentAttr attr, RankingInfo rankingInfo, long rankId, long phaseId, String dateStr, long count, String pointedMember, Map<String, String> ext) {
        long actId = attr.getActId();
        RoleConfig weekStarRoleConfig = attr.getRoleConfigs().stream()
                .filter(item -> item.getWeekStartRankId() == rankId && (item.getWeekStartPhaseId() == phaseId || 0L == phaseId))
                .findFirst().orElse(null);

        RoleConfig IntegralShowRoleConfig = attr.getRoleConfigs().stream()
                .filter(item -> item.getIntegralShowRankId() == rankId && (item.getIntegralShowPhaseId() == phaseId || 0L == phaseId))
                .findFirst().orElse(null);

        List<Rank> ranks = Lists.newArrayList();
        if (weekStarRoleConfig != null) {
            String propsId = ext.getOrDefault(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, "").trim();
            if (!StringUtils.isNumeric(propsId)) {
                throw new ParameterException("findSrcMember error");
            }
            if (!getMySelf().checkWeekDay(actId, dateStr)) {
                throw new ParameterException("dateStr error");
            }
            //没到时间不展示榜单
            Date now = commonService.getNow(actId);
            Date queryDate = DateUtil.getDate(dateStr, DateUtil.PATTERN_TYPE2);
            if (now.before(queryDate)) {
                return Lists.newArrayList();
            }
            ranks = getMySelf().queryWeekStarRank(actId, dateStr, propsId, weekStarRoleConfig.getRoleId(), (int) count, pointedMember);
        } else if (IntegralShowRoleConfig != null) {
            ranks = getMySelf().queryIntegralShowRank(actId, rankId, phaseId, count, pointedMember);
        }

        return ranks;
    }

    /**
     * 增加周星榜单的预计积分/碎片
     *
     * @param attr
     * @param rankReq     请求数据
     * @param rankingInfo 榜单信息
     * @param ranks       榜单原始数据
     * @param objectList  榜单展示数据，已经填充了成员信息
     * @return
     */
    @Override
    public List<Object> handleExt(ActJYJFSComponentAttr attr, GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        long rankId = rankingInfo.getRankingId();
        RoleConfig roleConfig = attr.getRoleConfigs().stream().filter(item -> item.getWeekStartRankId() == rankId).findFirst().orElse(null);
        if (roleConfig == null) {
            return objectList;
        }

        int propsId = Integer.parseInt(rankReq.getFindSrcMember().trim());
        PropsInfo propsInfo = jyWeekStartService.queryGiftInfo(propsId);
        if (propsInfo == null) {
            log.warn("handelExt@propsId:{} not find", propsId);
            return objectList;
        }
        Optional<PropsPricing> propsPricingOp = propsInfo.getPricingList().stream()
                .filter(propsPricing -> propsPricing.getCurrencyType() == 1).findFirst();
        if (!propsPricingOp.isPresent()) {
            throw new RuntimeException("营收礼物价格查询失败，propsId:" + propsId);
        }
        long price = propsPricingOp.map(PropsPricing::getCurrencyAmount).orElse(0L);

        OptionalLong max = roleConfig.getWeekStartSettleConfig().stream().mapToLong(SettleConfig::getRank).max();
        if (!max.isPresent()) {
            log.warn("handelExt config error@rankId:{} ", rankId);
            return objectList;
        }


        for (Object object : objectList) {
            RankValueItemBase rank = (RankValueItemBase) object;
            if (rank.getRank() > max.getAsLong()) {
                break;
            }
            Map<String, String> viewExt = Maps.newHashMap();
            //营收给过来的单位都是紫水晶的单位（对应是RMB的厘，所以要除1000）
            long score = rank.getValue() / 1000;
            //如果是新榜单，那么score就是：礼物数量 * 1000 + 粉丝票数量 * 50
            String dateStr = rankReq.getDateStr();
            if (StringUtils.compare(dateStr, attr.getWeekStarTransMondayV2(), true) >= 0) {
                //礼物单价单位是厘，榜单的值是：礼物数量 * 1000 + 粉丝票数量 * 50
                score = rank.getValue() * price / 1000000;
            }

            WeekStarRecord weekStarRecord = settleWeekStarRank(Long.parseLong(rank.getKey()), rank.getRank(), score, roleConfig);
            if (weekStarRecord != null) {
                viewExt.put("addScore", weekStarRecord.getAward() + "");
                viewExt.put("scoreType", weekStarRecord.getAwardType() + "");

                if (rank.getViewExt() == null) {
                    rank.setViewExt(Maps.newHashMap());
                }
                rank.getViewExt().putAll(viewExt);
            }
        }

        return objectList;
    }

    public JYJFSComponent getMySelf() {
        return self;
    }

    @Cached(timeToLiveMillis = 24 * 60 * 60 * 1000)
    public List<String> getWeekList(long actId) {
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCache(actId);
        List<String> mondayList = Lists.newArrayList();
        if (activityInfoVo == null) {
            return mondayList;
        }
        Long beginTime = activityInfoVo.getBeginTime();
        Long endTime = activityInfoVo.getEndTime();
        mondayList = DateUtil.getMondayList(beginTime, endTime);

        return mondayList;
    }

    /**
     * 赛季信息查询
     *
     * @param actId
     * @return
     */
    @Cached(timeToLiveMillis = 1000)
    public ActivityInfoVo queryActInfo(long actId) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            throw new ParameterException("actId error.");
        }
        ActivityInfoVo activityInfo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfo == null) {
            throw new ParameterException("actId error");
        }

        ActivityInfoVo activityInfoVo = new ActivityInfoVo();
        BeanUtils.copyProperties(activityInfo, activityInfoVo);

        long beginTime = activityInfoVo.getBeginTime();
        long endTime = DateUtil.getLastDayEndTime(activityInfo.getEndTime());
        activityInfoVo.setEndTime(endTime);
        long nowTime = activityInfoVo.getCurrentTime();
        List<String> weekList = self.getWeekList(actId);
        int week = getCurrentWeek(weekList, nowTime, beginTime, endTime);

        String entryActivity = commonService.getActAttr(actId, "entryActivity");
        String activityList = commonService.getActAttr(actId, "activityList");
        Map<String, Object> ext = Maps.newHashMap();
        ext.put("currentActivity", actId);
        ext.put("currentWeek", week);
        ext.put("weeks", weekList);
        ext.put("entryActivity", Convert.toInt(entryActivity, 1));
        ext.put("activityList", activityList);

        activityInfo.setExtData(ext);
        return activityInfo;
    }

    private int getCurrentWeek(List<String> weekList, long nowTime, long beginTime, long endTime) {
        int week;
        if (beginTime >= nowTime) {
            week = 1;
        } else if (endTime <= nowTime) {
            week = weekList.size();
        } else {
            week = (int) ((nowTime - beginTime) / (DateUtil.ONE_WEEK_SECONDS * 1000) + 1);
        }

        return week;
    }

    //---------------------------------------      积分碎片记录       ----------------------------//

    /**
     * 查询主播最新积分记录--组件使用
     *
     * @param actId
     * @param uid
     * @return
     */
    @Cached(timeToLiveMillis = 5 * 1000)
    public List<JSONObject> querySeasonScoreNewestRecord(long actId, long uid, int type) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            throw new ParameterException("actId error.");
        }

        return queryAnchorSeasonScoreRecordList(uid, 1, type, attr);
    }

    @Cached(timeToLiveMillis = 5 * 1000)
    public List<JSONObject> querySeasonScoreRecord(long actId, long uid, int type) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            throw new ParameterException("actId error.");
        }
        //跟龙龙协商，后端限制最多返回100条记录
        return queryAnchorSeasonScoreRecordList(uid, 100, type, attr);
    }

    /**
     * 查询主播积分和记录排期  --需要判断主播身份
     *
     * @param actId
     * @param uid
     * @return
     */
    @Cached(timeToLiveMillis = 2 * 1000)
    public JSONObject queryAnchorScoreAndRecord(long actId, long uid) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            throw new ParameterException("actId error.");
        }
        JSONObject scoreDetail = new JSONObject();
        scoreDetail.put("seasonValue", 0L);
        scoreDetail.put("seasonRank", 0);
        scoreDetail.put("totalValue", 0L);
        scoreDetail.put("totalRank", 0);
        scoreDetail.put("list", Lists.newArrayList());

        if (uid < 0) {
            return scoreDetail;
        }

        long roleId = getMySelf().queryJyAnchorRoleId(actId, uid);
        // 非这次活动的参与角色
        if (!attr.getAnchorRoles().contains(roleId)) {
            return scoreDetail;
        }

        //积分记录
        List<JSONObject> seasonRecords = queryAnchorSeasonScoreRecordList(uid, Integer.MAX_VALUE, 0, attr);

        scoreDetail.put("list", seasonRecords);
        RoleConfig roleConfig = attr.getRoleConfig(roleId);
        // 批量查赛季积分和总积分
        List<Triple<Long, Long, Long>> rankConfig = Lists.newArrayList();
        long currentActId = getCurrentActId(SeasonInfo.ACT_IDS, uid);
        rankConfig.add(Triple.of(roleConfig.getIntegralShowRankId(), roleConfig.getIntegralShowPhaseId(), actId));
        rankConfig.add(Triple.of(roleConfig.getTotalIntegralRankId(), roleConfig.getTotalIntegralPhaseId(), actId));

        List<QueryRankingRequest> rankingRequests = Lists.newArrayList();
        for (Triple<Long, Long, Long> pair : rankConfig) {
            QueryRankingRequest rankingRequest = new QueryRankingRequest();
            rankingRequest.setActId(pair.getRight());
            rankingRequest.setRankingId(pair.getLeft());
            rankingRequest.setPhaseId(pair.getMiddle());
            rankingRequest.setRankingCount(0);
            rankingRequest.setDateStr("");
            rankingRequest.setPointedMember(uid + "");
            rankingRequest.setExtData(Maps.newHashMap());
            rankingRequests.add(rankingRequest);
        }
        List<List<Rank>> ranksList = hdztRankingThriftClient.queryBatchRanking(rankingRequests, Maps.newHashMap());
        // TODO 第一赛季先指向同一个榜单，设计到积分相同按飞船个数排序
        Rank totalRank = ranksList.get(1).get(0);
        Rank seasonRank = ranksList.get(0).get(0);

        if (seasonRank.getRank() > 0) {
            scoreDetail.put("seasonValue", Math.max(seasonRank.getScore(), 0L) / attr.getIntegralMaxMultiple());
            scoreDetail.put("seasonRank", Math.max(seasonRank.getRank(), 0));
        }

        // 兼容第一赛季,指向同一个榜单
        long integralMaxMultiple = 1;
        if (FIRST_SEASON_ACT_ID == actId && currentActId == actId) {
            totalRank = seasonRank;
            integralMaxMultiple = attr.getIntegralMaxMultiple();
        }

        // 总积分榜只会外显用户自己的名次和分数
        // 如果一直没有获得积分,则即使有收到活动礼物,总积分榜也不会有该用户的数据
        // 默认的积分和名次就都是0
        if (totalRank.getRank() > 0) {
            scoreDetail.put("totalValue", Math.max(totalRank.getScore(), 0L) / integralMaxMultiple);
            scoreDetail.put("totalRank", Math.max(totalRank.getRank(), 0L));
        }

        return scoreDetail;
    }

    /**
     * 查询主播赛季积分记录---挂件专用
     *
     * @param uid
     * @param count 查询数量
     * @return
     */
    private List<JSONObject> queryAnchorSeasonScoreRecordList(long uid, int count, int type, ActJYJFSComponentAttr attr) {
        if (uid == 0) {
            return Lists.newArrayList();
        }
        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(attr.getActId());
        if (activityInfoVo == null || activityInfoVo.getBeginTime() > activityInfoVo.getCurrentTime()) {
            return Lists.newArrayList();
        }
        List<ScoreRecord> seasonRecords = queryScoreRecordList(uid, count, type, attr);
        if (seasonRecords.isEmpty()) {
            return Lists.newArrayList();
        }
        return seasonRecords.stream().map(info -> {
                    JSONObject record = new JSONObject();
                    record.put("time", info.getCreateTime());
                    record.put("value", info.getValue());
                    record.put("source", SourceType.getAwardType(info.getSource()).getShowName());
                    record.put("valueType", info.getValueType());

                    return record;
                }
        ).collect(Collectors.toList());
    }

    @HdzjEventHandler(value = ActivityTimeStart.class, canRetry = false)
    public void mergeScoreRecord(ActivityTimeStart event, ActJYJFSComponentAttr attr) {
        long prevActId = attr.getPrevActId(), actId = attr.getActId();
        if (prevActId <= 0) {
            return;
        }

        String actGroupCode = getRedisGroupCode(actId);
        String flagKey = makeKey(attr, SCORE_RECORD_MERGE_FLAG);
        if (!actRedisDao.setNX(actGroupCode, flagKey, DateUtil.getNowYyyyMMddHHmmss(), DateUtil.ONE_MONTH_SECONDS)) {
            log.info("mergeScoreRecord ignore actId:{}", attr.getActId());
            return;
        }

        long cmptUseInx = attr.getPrevCmptUseInx();
        if (cmptUseInx <= 0) {
            cmptUseInx = attr.getCmptUseInx();
        }

        final String pattern = makeKey(prevActId, getComponentId(), cmptUseInx, String.format(SCORE_RECORD_KEY, "*"));
        String preActGroupCode = getRedisGroupCode(prevActId);
        final StringRedisTemplate prevRedisTemplate = actRedisDao.getRedisTemplate(preActGroupCode), redisTemplate = actRedisDao.getRedisTemplate(actGroupCode);
        final RedisSerializer<String> redisSerializer = RedisSerializer.string();
        prevRedisTemplate.execute((RedisCallback<Object>) connection -> {
            try (Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().count(10000).match(pattern).build())) {
                cursor.forEachRemaining(bytes -> {
                    String recordKey = redisSerializer.deserialize(bytes);
                    List<byte[]> records = connection.lRange(bytes, 0, -1);
                    if (CollectionUtils.isEmpty(records)) {
                        return;
                    }

                    List<String> values = records.stream().map(redisSerializer::deserialize).collect(Collectors.toList());
                    String keyUid = parseUid(recordKey);
                    String newKey = makeKey(attr, String.format(SCORE_RECORD_KEY, keyUid));
                    log.info("mergeScoreRecord do add record with prev recordKey:{}, newKey:{}, values:{}", recordKey, newKey, values);
                    redisTemplate.opsForList().rightPushAll(newKey, values);
                });
            } catch (Exception e) {
                log.error("mergeScoreRecord exception:", e);
            }

            return null;
        });

    }

    private static String parseUid(String recordKey) {
        Matcher matcher = RECORD_KEY_PATTERN.matcher(recordKey);
        if (matcher.matches()) {
            return matcher.group(1);
        }

        return null;
    }

    /**
     * 增加积分记录
     *
     * @param uid
     * @param source
     * @param score
     * @param seq
     * @param time
     * @param attr
     */
    private void addScoreRecord(long uid, String seq, long time, SourceType source, int valueType, long score, ActJYJFSComponentAttr attr) {
        String key = makeKey(attr, String.format(SCORE_RECORD_KEY, uid));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        ScoreRecord record = new ScoreRecord(uid, seq, time, source.getIndex(), valueType, score);
        actRedisDao.getRedisTemplate(groupCode).opsForList().leftPush(key, JSON.toJSONString(record));
    }

    /**
     * 查询赛季积分/碎片记录 （cTime 递减排序 ）
     *
     * @param uid
     * @param count 查询数量
     * @param type  0 查全部，1=积分，2=碎片
     * @return
     */
    private List<ScoreRecord> queryScoreRecordList(long uid, int count, int type, ActJYJFSComponentAttr attr) {
        String key = makeKey(attr, String.format(SCORE_RECORD_KEY, uid));
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        if (count <= 0) {
            return Lists.newArrayList();
        }

        //-1 查全部，0是第一个,分类查要把所有的记录查出来
        int queryCount = type != 0 || count == Integer.MAX_VALUE ? -1 : (count - 1);
        List<String> recordStringList = actRedisDao.getRedisTemplate(groupCode).opsForList().range(key, 0, queryCount);
        if (CollectionUtils.isEmpty(recordStringList)) {
            return Lists.newArrayList();
        }
        String json = "[" + StringUtils.join(recordStringList, ",") + "]";
        List<ScoreRecord> records = JSONObject.parseArray(json, ScoreRecord.class);
        return records.stream().filter(item -> type == 0 || item.getValueType() == type)
                .sorted(Comparator.comparing(ScoreRecord::getCreateTime, Comparator.reverseOrder()))
                .limit(count).collect(Collectors.toList());

    }

    public JSONObject queryComponentByCache(long actId, long uid, long sid, long ssid) throws Exception {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            throw new BadRequestException("活动异常");
        }
        String groupCode = redisConfigManager.getGroupCode(actId);
        String key = makeKey(attr, String.format(COMPONENT_CACHE_KEY, uid, sid, ssid));
        String componentInfoString = actRedisDao.get(groupCode, key);
        JSONObject componentInfo;
        if (componentInfoString != null) {
            componentInfo = JSONObject.parseObject(componentInfoString);
        } else {
            componentInfo = queryComponentInfo(actId, uid, sid, ssid, attr);
            actRedisDao.set(groupCode, key, componentInfo.toJSONString(), COMPONENT_CACHE_SEC);
        }

        log.info("queryComponentByCache info@actId:{} anchorUid:{} component:{}", actId, uid, componentInfo.toJSONString());
        return componentInfo;
    }

    private static final int TWO = 2;

    /**
     * 查询组件信息
     *
     * @param actId
     * @param uid
     * @return
     * @throws Exception
     */
    public JSONObject queryComponentInfo(long actId, long uid, long sid, long ssid, ActJYJFSComponentAttr attr) throws Exception {
        Clock clock = new Clock();
        JSONObject componentInfo = new JSONObject();

        // 线上灰度状态,只有白名单用户才会显示
        boolean isFilter = !commonService.checkWhiteList(actId, RoleType.ANCHOR, uid + "");
        if (isFilter) {
            return componentInfo.fluentPut("status", 3);
        }

        Long roleId = getMySelf().queryJyAnchorRoleId(actId, uid);
        //模板复制静态配置
        JSONObject format = geStaticConfigFormat(roleId, attr);

        componentInfo.fluentPutAll(JSONObject.parseObject(format.toJSONString()).getInnerMap());

        RoleConfig roleConfig = attr.getRoleConfig(roleId);
        if (roleConfig == null) {
            //异常状态返回-非参数角色
            return componentInfo;
        }

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        if (activityInfoVo == null || activityInfoVo.getBeginTime() > activityInfoVo.getCurrentTime()) {
            //活动未开始
            return componentInfo.fluentPut("status", 1);
        } else if (DateUtil.getLastDayEndTime(activityInfoVo.getEndTime()) <= activityInfoVo.getCurrentTime()) {

            log.info("queryComponentInfo actId:{} uid:{} actEndTime:{},endTime:{},cutTime:{} ", actId, uid,
                    DateUtil.getPattenStrFromTime(activityInfoVo.getEndTime(), DateUtil.DEFAULT_PATTERN),
                    DateUtil.getPattenStrFromTime(DateUtil.getLastDayEndTime(activityInfoVo.getEndTime()), DateUtil.DEFAULT_PATTERN),
                    DateUtil.getPattenStrFromTime(activityInfoVo.getCurrentTime(), DateUtil.DEFAULT_PATTERN));
            //活动已经结束
            return componentInfo.fluentPut("status", 2);
        }

        //组织榜单配了查询参数
        Map<String, QueryRankingRequest> batchQueryRequestMap = Maps.newHashMap();

        QueryRankingRequest rankingRequest = new QueryRankingRequest();
        rankingRequest.setActId(actId);
        rankingRequest.setRankingId(roleConfig.getTotalIntegralRankId());
        rankingRequest.setPhaseId(roleConfig.getTotalIntegralPhaseId());
        rankingRequest.setRankingCount(0);
        rankingRequest.setPointedMember(uid + "");
        rankingRequest.setExtData(Maps.newHashMap());

        batchQueryRequestMap.put("totalValue", rankingRequest);

        QueryRankingRequest debtisRequest = new QueryRankingRequest();
        String dateStr = DateUtil.getPattenStrFromTime(activityInfoVo.getCurrentTime(), DateUtil.PATTERN_TYPE2);
        debtisRequest.setActId(actId);
        debtisRequest.setDateStr(dateStr);
        debtisRequest.setRankingId(roleConfig.getGiftDayRankId());
        debtisRequest.setPhaseId(roleConfig.getGiftPhaseId());
        debtisRequest.setRankingCount(0);
        debtisRequest.setPointedMember(uid + "");
        debtisRequest.setExtData(Maps.newHashMap());
        batchQueryRequestMap.put("debtis", debtisRequest);

        // 批量查询榜单
        Map<String, BatchRankingItem> batchRankingItemMap = hdztRankingThriftClient.queryBatchRanking(batchQueryRequestMap, Maps.newHashMap());
        clock.tag();

        // 榜单结果填充
        for (Map.Entry<String, BatchRankingItem> entry : batchRankingItemMap.entrySet()) {
            String key = entry.getKey();
            Rank rank = entry.getValue().getPointedRank();
            if ("totalValue".equals(key)) {
                componentInfo.put("totalValue", Math.max(rank.getScore(), 0L));
            } else {
                long value = rank.getScore();
                JSONObject object = componentInfo.getJSONObject(key);
                object.put("rank", Math.max(rank.getRank(), 0));
                object.put("value", Math.max(value, 0L));
            }
        }

        // 是否处于屏蔽时间内
        long now = commonService.getNow(actId).getTime();
        ShieldPeriod shieldPeriod = attr.getShieldPeriod(now);
        if (shieldPeriod != null) {
            JSONObject viewExt = new JSONObject();
            viewExt.put("msg", shieldPeriod.getMsg());
            viewExt.put("skipTitle", shieldPeriod.getSkipTitle());
            viewExt.put("skipUrl", shieldPeriod.getSkipUrl());

            componentInfo.put("debtis", null);
            componentInfo.put("weekStar", null);
            componentInfo.put("ldInfo", null);
            componentInfo.put("viewExt", viewExt);

            return componentInfo;
        }

        // 周星
        JSONObject weekStar;
        int tryTime = 0;
        // 循环是因为营收周星查询没有时间参数，避免查询出来的礼物信息和周星榜单对不上
        do {
            String weekDay = DateUtil.getMondayDate(new Date(activityInfoVo.getCurrentTime()));
            tryTime++;
            try {
                weekStar = getAnchorWeekStar(uid, roleConfig, activityInfoVo.getCurrentTime(), attr);
                break;
            } catch (RuntimeException e) {
                if (tryTime > TWO) {
                    throw e;
                }
                log.error("queryComponentInfo getAnchorWeekStar error,try again,uid:{},roleId:{},season:{},weekDay:{} {},",
                        uid, roleId, weekDay, e.getMessage(), e);
            }
        } while (true);
        clock.tag();
        componentInfo.getJSONObject("weekStar").putAll(weekStar.getInnerMap());

        String groupCode = redisConfigManager.getGroupCode(actId);
        // 乱斗高光
        String ldKey = "ldInfo";
        if (componentInfo.containsKey(ldKey) && sid > 0 && ssid > 0) {
            ChannelFightRsp ldPk = ftsBaseInfoBridgeClient.queryChannelFight(sid, ssid);
            log.info("queryComponentInfo ld@sid:{},ssid:{},ld:{}", sid, ssid, JSON.toJSONString(ldPk));
            JSONObject ldInfo = componentInfo.getJSONObject("ldInfo");
            //非乱斗
            if (ldPk != null && ldPk.getSerialNo() != 0) {
                ldInfo.put("onPk", 1);
                ldInfo.put("pkValue", Math.floorDiv(ldPk.getRevenue(), TEN));
                if (Math.floorDiv(ldPk.getRevenue(), TEN) >= roleConfig.getFightThreshold()) {
                    ldInfo.put("predictIntegral", roleConfig.getFightAward());
                }
            }
            String lastLdStarAward = actRedisDao.hget(groupCode, makeKey(attr, LAST_FIGHT_AWARD), uid + "");
            if (StringUtils.isNotBlank(lastLdStarAward)) {
                JSONObject jsonObject = JSONObject.parseObject(lastLdStarAward);
                ldInfo.put("lastIntegral", jsonObject.getIntValue("integral"));
            }
        }

        return componentInfo;
    }

    private JSONObject geStaticConfigFormat(long roleId, ActJYJFSComponentAttr attr) {
        JSONObject componentInfo = new JSONObject();
        componentInfo.put("actId", attr.getActId());
        componentInfo.put("roleId", roleId);
        componentInfo.put("status", 0);
        componentInfo.put("giftIcon", attr.getGiftIcon());
        componentInfo.put("giftName", attr.getGiftName());
        RoleConfig roleConfig = attr.getRoleConfig(roleId);

        if (roleConfig == null) {
            //异常状态返回-非参数角色
            return componentInfo.fluentPut("status", 3);
        }

        componentInfo.put("totalValue", 0);
        componentInfo.put("totalValueThreshold", attr.getIntegralThreshold());

        JSONObject debtis = new JSONObject();
        JSONObject weekStar = new JSONObject();
        JSONObject ldInfo = new JSONObject();


        componentInfo.put("debtis", debtis);
        componentInfo.put("weekStar", weekStar);
        componentInfo.put("ldInfo", ldInfo);
        componentInfo.put("viewExt", null);


        ImmutableSortedMap<Long, Long> debrisThresholdAwardMap = ImmutableSortedMap.copyOf(roleConfig.getDebrisThresholdAwardMap());
        int index = 1;
        for (Map.Entry<Long, Long> entry : debrisThresholdAwardMap.entrySet()) {
            String thresholdKey = String.format("value%sThreshold", index);
            String awardKey = String.format("threshold%sAward", index);
            debtis.put(thresholdKey, entry.getKey());
            debtis.put(awardKey, entry.getValue());
            index++;
        }

        debtis.put("value", 0);

        ldInfo.put("predictIntegral", 0);
        ldInfo.put("onPk", 0);
        ldInfo.put("lastIntegral", 0);
        ldInfo.put("pkValue", 0);
        ldInfo.put("valueThreshold", roleConfig.getFightThreshold());
        ldInfo.put("thresholdAward", roleConfig.getFightAward());


        weekStar.put("predictIntegral", 0);
        weekStar.put("lastIntegral", 0);
        weekStar.put("lastDebris", 0);

        return componentInfo;
    }

    @Cached(timeToLiveMillis = 3 * 1000)
    public JSONObject queryAnchorSeasonScoreBySsid(long actId, long sid, long ssid) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            throw new ParameterException("actId error.");
        }
        long anchorId = 0L;
        if (sid > 0 && ssid > 0) {
            Optional<OnlineChannelInfo> channelInfo = Optional.ofNullable(onlineChannelService.get(sid, ssid));
            List<Long> effectAnchorId = channelInfo.map(OnlineChannelInfo::getEffectAnchorId).orElse(Lists.newArrayList());
            anchorId = effectAnchorId.stream().filter(Objects::nonNull).filter(uid -> uid > 0).findFirst().orElse(0L);
        }

        JSONObject seasonRank = new JSONObject();

        long roleId = anchorId > 0 ? getMySelf().queryJyAnchorRoleId(actId, anchorId) : 0L;
        seasonRank.put("anchorUid", anchorId);
        seasonRank.put("roleId", roleId);
        seasonRank.put("rank", 0);
        seasonRank.put("value", 0);

        RoleConfig roleConfig = attr.getRoleConfig(roleId);
        if (anchorId > 0 && roleConfig != null) {
            UserInfoVo userInfoVo = userInfoService.getUserInfo(Lists.newArrayList(anchorId), Template.makefriend).get(anchorId);
            String nickName = Optional.ofNullable(userInfoVo).map(UserInfoVo::getNick).orElse("神秘主播");
            seasonRank.put("nickName", nickName);
            //参与的主播
            Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, roleConfig.getIntegralRankId(), roleConfig.getIntegralPhaseId(), "", anchorId + "", Maps.newHashMap());
            if (rank != null && rank.getRank() > 0 && rank.getScore() > 0) {
                seasonRank.put("rank", rank.getRank());
                seasonRank.put("value", rank.getScore());
            }
        }

        return seasonRank;
    }

    /**
     * 查询用户角色
     *
     * @param uid
     * @return
     */
    @Cached(timeToLiveMillis = 60 * 1000)
    public Long queryJyAnchorRoleId(long actId, long uid) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            return 0L;
        }
        EnrollmentInfo firstEnrolMember = enrollmentService.getFirstEnrolMember(actId, String.valueOf(uid), RoleType.ANCHOR.getValue());
        if (firstEnrolMember != null) {
            return firstEnrolMember.getDestRoleId();
        }
        return queryJyAnchorRoleId(Lists.newArrayList(uid), attr).get(uid);
    }

    /**
     * 查询交友主播的角色id
     *
     * @param uids
     * @return
     */
    private Map<Long, Long> queryJyAnchorRoleId(List<Long> uids, ActJYJFSComponentAttr attr) {
        if (CollectionUtils.isEmpty(uids)) {
            return Maps.newHashMap();
        }
        List<String> uidStringList = uids.stream().map(String::valueOf).collect(Collectors.toList());
        Visibility visibility = commonService.isGrey(attr.getActId()) ? Visibility.Gray : Visibility.Normal;
        Map<String, String> roleMap = groupCenterThriftClient.queryMemberGroup(attr.getRoleGroupRuleId(), uidStringList, QueryType.TypeCompere, Source.SourceJY, visibility);
        return uids.stream().collect(Collectors.toMap(Function.identity(), uid -> Convert.toLong(roleMap.get(uid + ""), 0L)));
    }

    /**
     * 发放积分/碎片劵
     *
     * @param anchorUid
     * @param source
     * @param valueType
     * @param award
     * @param attr
     */
    private void sendScoreQuan(long anchorUid, SourceType source, int valueType, long award, ActJYJFSComponentAttr attr) {
        long actId = attr.getActId();
        long shopIndex = valueType == 1 ? attr.getIntegralShopIndex() : attr.getDebrisShopIndex();
        String currency = valueType == 1 ? attr.getIntegralShopCurrency() : attr.getDebrisShopCurrency();
        try {
            componentCollaborator.callComponent(actId, ComponentId.SHOP, "addUserCurrency", actId, shopIndex, anchorUid, currency, Math.toIntExact(award));
            log.info("sendScoreQuan done@actId:{} anchorUid:{} source:{} valueType:{},award:{},shopIndex:{},currency:{}",
                    actId, anchorUid, source, valueType, award, shopIndex, currency);
        } catch (Exception e) {
            log.error("sendScoreQuan award by callComponent error@ actId:{} anchorUid:{} source:{} valueType:{},award:{},shopIndex:{},currency:{} {}"
                    , actId, anchorUid, source, valueType, award, shopIndex, currency, e.getMessage(), e);
        }
    }

    public void addIntegralOrDebris(long anchorUid, SourceType source, int valueType, long award, long time, String seq, boolean tip, boolean sendQuan, ActJYJFSComponentAttr attr) {
        String item = valueType == 1 ? attr.getIntegralItem() : attr.getDebrisItem();
        Map<String, String> ext = Maps.newHashMap();
        long multiple = attr.getIntegralMaxMultiple();
        if (valueType == 1 && time <= attr.getIntegralMaxLastTime().getTime()) {
            // 已经结算了，把时间设置成结算之后
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            if (actRedisDao.get(groupCode, makeKey(attr, PK_SETTLE_FLAG)) != null) {
                log.info("addIntegralOrDebris adjust@:seq:{},time:{}", seq, DateUtil.getPattenStrFromTime(time, DateUtil.DEFAULT_PATTERN));
                time = attr.getIntegralMaxLastTime().getTime() + 3 * 1000;
            } else {
                //不进行时间调整，以我上报的时间为准
                ext = ImmutableMap.of(RankDataEvent.NOT_ADJUST_EVENT_TIME_DELAY, "1");
                updateAnchorScore(anchorUid, attr.getIntegralMaxItem(), award * multiple, time, seq + "_MAX", ext, attr);
            }
        }

        // 用于积分外显榜单，要求积分想让按礼物数量排序
        if (valueType == 1) {
            updateAnchorScore(anchorUid, attr.getIntegralMaxItem2(), award * multiple, time, seq + "_MAX2", ext, attr);
        }

        updateAnchorScore(anchorUid, item, award, time, seq, Maps.newHashMap(), attr);

        addScoreRecord(anchorUid, seq, time, source, valueType, award, attr);
        if (sendQuan) {
            sendScoreQuan(anchorUid, source, valueType, award, attr);
        }

        if (tip) {
            obtainPointNotify(attr, anchorUid, valueType, Math.toIntExact(award), source.getShowName());
        }
    }

    /**
     * 查询榜单积分的接口，积分放大了倍数需要处理下
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param size
     * @param pointMember
     * @return
     */
    @Cached(timeToLiveMillis = CacheTimeout.HDZT_RANKING_INFO)
    public List<Rank> queryIntegralShowRank(long actId, long rankId, long phaseId, long size, String pointMember) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        List<Rank> ranks = hdztRankingThriftClient.queryRankingCache(actId, rankId, phaseId, "", size, pointMember, Maps.newHashMap());

        long multiple = attr.getIntegralMaxMultiple();
        int giftScore = attr.getGiftScore();
        //处理倍数
        ranks = ranks.stream()
                .map(item -> {
                    Rank newRank = new Rank(item);
                    String giftCount = "0";
                    if (newRank.getScore() > 0) {
                        newRank.setScore(item.getScore() / multiple);
                        giftCount = "" + ((item.getScore() % multiple) / giftScore);
                    }
                    newRank.setItemDesc(giftCount);
                    return newRank;
                })
                .collect(Collectors.toList());

        return ranks;
    }

    //--------------------------------  年度资格  ------------------------------------//

    /**
     * 监听积分总榜变化事件
     * todo:跨赛季处理 DONE
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void handleTotalIntegralRankingScoreChangedEvent(RankingScoreChanged event, ActJYJFSComponentAttr attr) {
        // 过滤掉迁移造成的榜单变化事件
        String itemId = event.getItemId();
        if (filterMigrationItemId(itemId)) {
            log.info("filterMigrationItemId event={}", event);
            return;
        }

        RoleConfig roleConfig = attr.getRoleConfigs().stream()
                .filter(item -> item.getTotalIntegralRankId() == event.getRankId() && item.getTotalIntegralPhaseId() == event.getPhaseId())
                .findFirst().orElse(null);
        if (roleConfig == null) {
            log.warn("handleTotalIntegralRankingScoreChangedEvent ignore@seq:{}", event.getSeq());
            return;
        }
        long uid = Long.parseLong(event.getMember());
        long threshold = attr.getIntegralThreshold();
        long lastPhaseScore = event.getPhaseScore() - event.getItemScore();
        if (lastPhaseScore >= threshold || event.getPhaseScore() < threshold) {
            log.warn("handleTotalIntegralRankingScoreChangedEvent ignore@uid:{},lastPhaseScore:{},score:{},threshold:{},seq:{}",
                    uid, lastPhaseScore, event.getItemScore(), threshold, event.getSeq());
            return;
        }
        String seq = "ZG_" + event.getSeq();
        Date now = commonService.getNow(attr.getActId());
        boolean updateAnchorScoreResult = updateAnchorScore(uid, attr.getZGItem(), System.currentTimeMillis(), now.getTime(), seq, Maps.newHashMap(), attr);

        // 更新分数成功才发送横幅
        if (updateAnchorScoreResult) {
            anchorYearQualificationBroadcast(attr, uid);
        }
        log.info("handleTotalIntegralRankingScoreChangedEvent done@uid:{},lastPhaseScore:{},score:{},threshold:{},seq:{}",
                uid, lastPhaseScore, event.getItemScore(), threshold, seq);
    }

    //-------------------------------- 碎片  ------------------------------------//

    /**
     * 结算礼物日榜得碎片
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void handleDebrisDayRankEvent(PhaseTimeEnd event, ActJYJFSComponentAttr attr) {
        RoleConfig roleConfig = attr.getRoleConfigs().stream()
                .filter(item -> item.getGiftDayRankId() == event.getRankId() && item.getGiftPhaseId() == event.getPhaseId())
                .findFirst().orElse(null);
        if (roleConfig == null) {
            log.warn("handleDebrisDayRankEvent ignore@rankId:{},phaseId:{},seq:{}", event.getRankId(), event.getPhaseId(), event.getSeq());
            return;
        }

        Date endTime = DateUtil.getDate(event.getEndTime());
        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), endTime);
        long time = endTime.getTime();
        if (isShield(attr, time)) {
            log.info("isShield by time,event={}", event);
            return;
        }


        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), event.getRankId(), event.getPhaseId(), dateStr, Integer.MAX_VALUE, Maps.newHashMap());
        Map<Long, Long> debrisThresholdAwardMap = roleConfig.getDebrisThresholdAwardMap();
        ImmutableSortedMap<Long, Long> thresholdAwardMap = ImmutableSortedMap.copyOf(debrisThresholdAwardMap);
        //最低门槛
        long minThreshold = thresholdAwardMap.firstKey();
        for (Rank rank : ranks) {
            // 因为榜单是排号序的,没达到门槛,说明后面的也不满足条件,可以直接跳出循环
            if (rank.getScore() < minThreshold) {
                break;
            }

            long anchorUid = Long.parseLong(rank.getMember());
            long awardScore = thresholdAwardMap.floorEntry(rank.getScore()).getValue();
            String seq = "DebrisDay_" + dateStr + "_" + anchorUid;
            addIntegralOrDebris(anchorUid, SourceType.GIFT, 2, awardScore, time, seq, true, true, attr);

            haiduDataReporting.reportDebris(attr.getActId(), String.valueOf(anchorUid), awardScore, Math.toIntExact(rank.getScore()), roleConfig.getRoleId());

            log.info("handleDebrisDayRankEvent award info@anchorUid:{},score:{},awardScore:{},seq:{}",
                    anchorUid, rank.getScore(), awardScore, seq);
        }
        log.info("handleDebrisDayRankEvent end@rankId:{},phaseId:{},seq:{}", event.getRankId(), event.getPhaseId(), event.getSeq());
    }

    private static final int ONE_MINUTE = 60;
    private static final long THOUSAND = 1000;

    /**
     * 自动兑换碎片
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void compoundIntegralByDebris(RankingScoreChanged event, ActJYJFSComponentAttr attr) {
        // 过滤掉迁移造成的榜单变化事件
        String itemId = event.getItemId();
        if (filterMigrationItemId(itemId)) {
            log.info("filterMigrationItemId event={}", event);
            return;
        }

        RoleConfig roleConfig = attr.getRoleConfigs().stream()
                .filter(item -> item.getDebrisRankId() == event.getRankId() && item.getDebrisPhaseId() == event.getPhaseId())
                .findFirst().orElse(null);
        if (roleConfig == null) {
            log.warn("compoundIntegralByDebris ignore@rankId:{},phaseId:{},seq:{}", event.getRankId(), event.getPhaseId(), event.getSeq());
            return;
        }
        long actId = attr.getActId();
        int debrisRate = attr.getExchangeRate();

        long lastPhaseScore = event.getPhaseScore() - event.getItemScore();
        long anchorUid = Long.parseLong(event.getMember());

        long awardIntegral = event.getPhaseScore() / debrisRate - lastPhaseScore / debrisRate;
        if (awardIntegral <= 0) {
            log.info("compoundIntegralByDebris ignore@actId:{},anchorUid:{},lastPhaseScore:{},phaseScore:{}",
                    attr.getActId(), anchorUid, lastPhaseScore, event.getPhaseScore());
            return;
        }
        //个人兑换限制
        long personalAwardIntegral = awardIntegral;
        if (attr.getPersonalLimit() > 0) {
            personalAwardIntegral = Math.min(awardIntegral, (attr.getPersonalLimit() - lastPhaseScore / debrisRate));
            if (personalAwardIntegral <= 0) {
                log.info("compoundIntegralByDebris ignore by personal limit@actId:{},anchorUid:{},lastPhaseScore:{},phaseScore:{},limit:{}",
                        attr.getActId(), anchorUid, lastPhaseScore, event.getPhaseScore(), attr.getPersonalLimit());
                return;
            }
        }

        //总限
        long realAwardIntegral = personalAwardIntegral;
        if (attr.getTotalLimit() > 0) {
            String limitKey = makeKey(attr, DEBRIS_COMPOUND_LIMIT_KEY);
            String groupCode = redisConfigManager.getGroupCode(actId);
            long compoundIntegral = actRedisDao.incrValue(groupCode, limitKey, personalAwardIntegral);
            realAwardIntegral = Math.min(personalAwardIntegral, (attr.getTotalLimit() - compoundIntegral));
            if (realAwardIntegral <= 0) {
                log.info("compoundIntegralByDebris ignore by total limit@actId:{},anchorUid:{},award:{},total:{},limit:{}",
                        attr.getActId(), anchorUid, personalAwardIntegral, compoundIntegral, attr.getTotalLimit());
                return;
            }
        }

        //pk晋级的结算当天的兑换时间往前一天调整
        Date date = DateUtil.getDate(event.getOccurTime());
        long now = commonService.getNow(actId).getTime();
        long time = now;
        long different = date.getTime() - attr.getIntegralMaxLastTime().getTime();
        //当日并且在结算前一分钟兑换的都算前一天
        if (different > 0 && different <= (attr.getPkDelaySettleSec() - ONE_MINUTE) * THOUSAND) {
            time = DateUtil.last(DateUtil.add(date, -1)).getTime();
            log.info("compoundIntegralByDebris adjust time@actId:{},gameId:{},now:{}",
                    actId, event.getSeq(), DateUtil.getPattenStrFromTime(now, DateUtil.DEFAULT_PATTERN));
        }

        String seq = "JF_DEBRIS_" + event.getSeq();

        addIntegralOrDebris(anchorUid, SourceType.SP, 1, realAwardIntegral, time, seq, false, false, attr);

        //上报成功增加积分记录
        pointCompositeNotify(attr, anchorUid, Math.toIntExact(event.getItemScore()), Math.toIntExact(realAwardIntegral));

        haiduDataReporting.reportDebrisExchangeScore(attr.getActId(), String.valueOf(anchorUid), realAwardIntegral, roleConfig.getRoleId());

        log.info("compoundIntegralByDebris done@actId:{},anchorUid:{} award:{},seq:{}", actId, anchorUid, realAwardIntegral, event.getSeq());
    }

    private boolean filterMigrationItemId(String itemId) {
        return FILTER_ITEM_IDS.contains(itemId);
    }

    //------------------------------- PK 荣耀时刻 --------------------------------------//
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void handlePKDailyRankScoreChanged(RankingScoreChanged event, ActJYJFSComponentAttr attr) {
        final long actId = attr.getActId(), rankId = event.getRankId(), phaseId = event.getPhaseId();
        String key = rankId + ":" + phaseId;
        if (attr.getMilestoneScoreMap() == null || !attr.getMilestoneScoreMap().containsKey(key)) {
            return;
        }


        Date now = commonService.getNow(actId);
        //查member是否晋级
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, DateUtil.dateYyyyMMdd(now), 10, ImmutableMap.of(RankExtParaKey.RANK_SCORE_SOURCE, HdztRankType.CONTEST));
        Set<String> members;
        if (CollectionUtils.isNotEmpty(ranks)) {
            members = ranks.stream().map(Rank::getMember).collect(Collectors.toSet());
        } else {
            members = Collections.emptySet();
        }

        if (!members.contains(event.getMember())) {
            log.info("handlePKDailyRankScoreChanged ignore not pk member:{}", event.getMember());
            return;
        }

        List<Long> milestoneScores = attr.getMilestoneScoreMap().get(key);
        ImmutableSortedSet<Long> sortedSet = ImmutableSortedSet.copyOf(milestoneScores);

        //当前分数
        long score = phaseId > 0 ? event.getPhaseScore() : event.getRankScore();
        //本次变动之前的分数
        long lastScore = score - event.getItemScore();

        Long currentThreshold = sortedSet.floor(score);
        Long lastThreshold = sortedSet.floor(lastScore);

        if (currentThreshold == null || Objects.equals(currentThreshold, lastThreshold)) {
            log.info("handlePKDailyRankScoreChanged ignore");
            return;
        }

        JSONObject data = new JSONObject();
        data.put("threshold", currentThreshold);
        String member = event.getMember();
        long anchorUid = Long.parseLong(member);
        UserInfoVo anchor = userInfoService.getUserInfo(Collections.singletonList(anchorUid), Template.makefriend).get(anchorUid);
        if (anchor == null) {
            anchor = new UserInfoVo();
        }

        GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId)
                .setBannerId(13L).setAnchorLogo(anchor.getAvatarUrl()).setAnchorNick(anchor.getNick())
                .setJsonData(data.toJSONString());
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        svcSDKService.broadcastTemplate(com.yy.thrift.broadcast.Template.Jiaoyou, msg);
        log.info("handlePKDailyRankScoreChanged done seq:{}, member:{}, rankId:{}, phaseId:{}, threshold:{}", event.getSeq(), member, rankId, phaseId, currentThreshold);
    }

    /**
     * 查询碎片兑换进度
     *
     * @param actId
     * @param uid
     * @return
     */
    public JSONObject queryDebtisExchangeRecord(long actId, long uid) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            throw new ParameterException("actId error.");
        }

        JSONObject debtisRecord = new JSONObject();
        for (int i = 0; i < attr.getPersonalLimit(); i++) {
            int index = i + 1;
            String thresholdKey = String.format("value%sThreshold", index);
            String awardKey = String.format("threshold%sAward", index);
            debtisRecord.put(thresholdKey, index * attr.getExchangeRate());
            debtisRecord.put(awardKey, index);
        }

        Long roleId = queryJyAnchorRoleId(actId, uid);
        RoleConfig roleConfig = attr.getRoleConfig(roleId);
        long score = 0L;
        long integral = 0L;
        if (roleConfig != null) {
            Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, roleConfig.getDebrisRankId(), roleConfig.getDebrisPhaseId(), "", uid + "", Maps.newHashMap());
            score = Math.max(rank.getScore(), 0L);
            integral = Math.min(score / attr.getExchangeRate(), attr.getExchangeRate());
        }

        debtisRecord.put("value", score);
        debtisRecord.put("integral", integral);
        return debtisRecord;
    }

    //-------------------------------- 乱斗  ------------------------------------//

    private static final int THREE_SECOND = 3000;

    /**
     * 监听乱斗结束事件，结算乱斗单场
     * <p>
     * Remark 乱斗赛季间处理逻辑需确保一致
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = ChannelFightEndEvent.class, canRetry = false)
    public void handleChannelFightEndEvent(ChannelFightEndEvent event, ActJYJFSComponentAttr attr) {
        long actId = attr.getActId();
        log.info("handleChannelFightEndEvent handle with actId:{}, event:{}", actId, JsonUtil.toJson(event));
        long endTime = event.getEndTime();
        // 事件时间和收到的时间相差3秒，按收到的事件的时间算
        if (Math.abs(event.getReceiveTimestamp() - endTime) >= THREE_SECOND) {
            endTime = event.getReceiveTimestamp();
            log.info("handleChannelFightEndEvent adjust time@actId:{},gameId:{}", actId, event.getGameId());
        }

        if (!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            Date now = commonService.getNow(attr.getActId());
            log.info("handleChannelFightEndEvent adjust time by grey@actId:{},gameId:{},endTime:{}",
                    actId, event.getGameId(), DateUtil.format(now));
            endTime = now.getTime();
        }

        if (isShield(attr, endTime)) {
            log.info("current endTime is shield@actId:{},gameId:{}", actId, event.getGameId());
            return;
        }

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCache(actId);
        // 活动时间的延迟的，要特殊判断下
        long actEndTime = DateUtil.getLastDayEndTime(activityInfoVo.getEndTime());
        if (endTime < activityInfoVo.getBeginTime() || endTime > actEndTime) {
            log.info("handleChannelFightEndEvent ignore@actId:{},gamedId:{}", attr.getActId(), event.getGameId());
            return;
        }
        settleFight(event.getCompereUid(), event.getRevenue(), event.getGameId(), endTime, attr);
        settleFight(event.getExtraUid(), event.getExtraRevenue(), event.getGameId(), endTime, attr);
    }

    private static final long TEN = 10;

    /**
     * 结算乱斗单场-发积分、发广播
     *
     * @param anchorUid
     * @param revenue
     * @param gameId
     * @param endTime
     * @param attr
     */
    private void settleFight(long anchorUid, long revenue, String gameId, long endTime, ActJYJFSComponentAttr attr) {
        Long roleId = getMySelf().queryJyAnchorRoleId(attr.getActId(), anchorUid);
        RoleConfig roleConfig = attr.getRoleConfig(roleId);
        if (roleConfig == null) {
            log.info("settleFight ignore@actId:{},anchorUid:{},roleId:{},gamedId:{}", attr.getActId(), anchorUid, roleId, gameId);
            return;
        }
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String lastKey = makeKey(attr, LAST_FIGHT_AWARD);
        if (Math.floorDiv(revenue, TEN) < roleConfig.getFightThreshold()) {
            actRedisDao.hdelKey(groupCode, lastKey, Lists.newArrayList(String.valueOf(anchorUid)));
            log.info("settleFight ignore@actId:{},anchorUid:{},revenue:{},gamedId:{}", attr.getActId(), anchorUid, revenue, gameId);
            return;
        }

        String seq = "LD_" + gameId + "_" + anchorUid;
        long award = roleConfig.getFightAward();
        addIntegralOrDebris(anchorUid, SourceType.LD, 1, award, endTime, seq, true, true, attr);
        ldHighlightBroadcast(attr, anchorUid, Math.floorDiv(revenue, 10L));
        JSONObject awardJson = new JSONObject();
        awardJson.put("integral", award);

        actRedisDao.hset(groupCode, lastKey, String.valueOf(anchorUid), awardJson.toJSONString());

        haiduDataReporting.reportLDScore(attr.getActId(), String.valueOf(anchorUid), award, roleId);

        log.info("settleFight end@actId:{},anchorUid:{},revenue:{},gamedId:{},award:{}",
                attr.getActId(), anchorUid, revenue, gameId, award);

    }

    //-------------------------------- 周星  ------------------------------------//

    /**
     * 结算周星--是否结算的时间控制，定时器调用，要保证全局唯一
     */
//    @Scheduled(cron = "*/4 * * * * ?")
    public void settleWeekStar() {
        //本地不启动
        if (SysEvHelper.isLocal()) {
            return;
        }
        Set<Long> activityIds = getComponentEffectActIds();
        for (Long actId : activityIds) {
            try {
                Date now = commonService.getNow(actId);
                ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCache(actId);
                if (!actInfoService.inActTime(now, activityInfoVo)) {
                    continue;
                }
                ActJYJFSComponentAttr uniqueComponentAttr = getUniqueComponentAttr(actId);
                String nowDay = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
                String thisMonday = DateUtil.getMondayDate(now);
                String startHourMinuteS = commonService.getActAttr(actId, "settleWeekStarStartHourMinute");
                String endHourMinuteS = commonService.getActAttr(actId, "settleWeekStarEndHourMinute");
                //开始结算的时间 小时分钟 0010
                int startHourMinute = Convert.toInt(startHourMinuteS, 10);
                //结束结算的时间 小时分钟 0020
                int endHourMinute = Convert.toInt(endHourMinuteS, 20);
                int nowHourMinute = DateUtil.getHours(now) * 100 + DateUtil.getMinute(now);
                //延迟执行的秒数
                int delaySecond = 60;
                //周一 并且开始5分钟中内
                if (nowDay.equals(thisMonday) && nowHourMinute >= startHourMinute && nowHourMinute < endHourMinute) {
                    //上周日
                    Date lastDate = DateUtil.last(DateUtil.add(now, -1));
                    if (isShield(uniqueComponentAttr, lastDate.getTime())) {
                        log.info("isShield by time,last date={}", DateUtil.format(lastDate));
                        return;
                    }
                    //上周一
                    String lastMonday = DateUtil.getMondayDate(lastDate);
                    if (!getWeekList(actId).contains(lastMonday)) {
                        return;
                    }
                    String groupCode = redisConfigManager.getGroupCode(actId);
                    //延迟结算的时间
                    String key = makeKey(uniqueComponentAttr, String.format(SETTLE_WEEK_STAR_DELAY_TIME, lastMonday));
                    //即将执行的时间
                    long exeTime = System.currentTimeMillis() + delaySecond * 1000;
                    actRedisDao.setNX(groupCode, key, exeTime + "");
                    String exeTimeString = actRedisDao.get(groupCode, key);
                    if (exeTimeString == null || Long.parseLong(exeTimeString) > System.currentTimeMillis()) {
                        log.info("settleWeekStar delay execute@actId:{},lastMonday:{},exeTimeString:{}", actId, lastMonday, exeTimeString);
                        return;
                    }
                    settleWeekStar(lastMonday, lastDate, uniqueComponentAttr);
                    log.info("settleWeekStar done@actId:{},lastMonday:{}", actId, lastMonday);
                }
            } catch (Exception ex) {
                log.error("settleWeekStar error", ex);
            }
        }
    }

    /**
     * 结算周星，周星price
     *
     * @param attr
     * @param weekDay
     */
    public void settleWeekStar(String weekDay, Date time, ActJYJFSComponentAttr attr) {
        long actId = attr.getActId();

        log.info("settleWeekStar start@actId:{},weekDay:{}", actId, weekDay);
        String groupCode = redisConfigManager.getGroupCode(actId);
        //周星结算标识
        String key = makeKey(attr, String.format(SETTLE_WEEK_STAR_FLAG, weekDay));
        if (!actRedisDao.setNX(groupCode, key, DateUtil.getNowYyyyMMddHHmmss(), DateUtil.ONE_MONTH_SECONDS)) {
            log.info("settleWeekStar repetition@actId:{},weekDay:{}", actId, weekDay);
            return;
        }

        Clock clock = new Clock();
        List<RoleConfig> roleConfigs = attr.getRoleConfigs();
        List<WeekStarRecord> scoreAwards = Lists.newArrayList();
        log.info("krista role size={},weekDay:{}", roleConfigs.size(), weekDay);
        for (RoleConfig roleConfig : roleConfigs) {
            List<SettleConfig> weekStartSettleConfig = roleConfig.getWeekStartSettleConfig();
            OptionalInt maxRank = weekStartSettleConfig.stream().mapToInt(SettleConfig::getRank).max();
            if (!maxRank.isPresent()) {
                log.error("settleWeekStar config error@actId:{},weekDay:{},config:{}", actId, weekDay, JSON.toJSONString(weekStartSettleConfig));
                continue;
            }

            List<Integer> rankAward = weekStartSettleConfig.stream()
                    .map(SettleConfig::getRank).distinct().collect(Collectors.toList());
            int type = roleConfig.getWeekStartType();
            List<WeekStarRank> weekPropRanks = queryWeekStarRank(weekDay, 0, maxRank.getAsInt(), roleConfig, attr);
            log.info("settleWeekStar queryWeekStarRanking info:{}", JSON.toJSONString(weekPropRanks, SerializerFeature.DisableCircularReferenceDetect));

            clock.tag();

            for (WeekStarRank currentWeekPropRank : weekPropRanks) {
                JyWeekStarInfo jyWeekStarInfo = currentWeekPropRank.getJyWeekStarInfo();
                int propId = jyWeekStarInfo.getPropsId();
                long propPrice = jyWeekStarInfo.getPrice();
                List<Rank> anchorRankList = currentWeekPropRank.getRankList();
                if (anchorRankList.size() < maxRank.getAsInt()) {
                    throw new RuntimeException("week star size error,type=" + type + ",propId=" + propId + ",size=" + anchorRankList.size() + ",maxRank=" + maxRank.getAsInt());
                }
                Map<Integer, Rank> weekRankMap = anchorRankList.stream().collect(Collectors.toMap(Rank::getRank, Function.identity()));
                for (Integer rank : rankAward) {
                    Rank tRank = weekRankMap.get(rank);
                    long uid = Long.parseLong(tRank.getMember());
                    //营收给过来的单位都是紫水晶的单位（对应是RMB的厘，所以要除1000）
                    long score = tRank.getScore() / 1000;
                    //如果是新榜单，那么score就是：礼物数量 * 1000 + 粉丝票数量 * 50
                    if (StringUtils.compare(weekDay, attr.getWeekStarTransMondayV2(), true) >= 0) {
                        //礼物单价单位是厘，榜单的值是：礼物数量 * 1000 + 粉丝票数量 * 50
                        score = propPrice * tRank.getScore() / 1000000;
                    }
                    WeekStarRecord scoreAward = settleWeekStarRank(uid, rank, score, roleConfig);
                    if (scoreAward.getAward() > 0) {
                        scoreAwards.add(scoreAward);
                    }

                    log.info("settleWeekStar info@weekDay:{},type:{},propId:{},scoreAward:{}",
                            weekDay, type, propId, JSON.toJSONString(scoreAward));
                }
            }
        }

        Map<Long, Long> integerAwardMap = scoreAwards.stream().filter(item -> item.getAwardType() == 1)
                .collect(Collectors.toMap(WeekStarRecord::getUid, WeekStarRecord::getAward, Long::sum));
        Map<Long, Long> debrisAwardMap = scoreAwards.stream().filter(item -> item.getAwardType() == 2)
                .collect(Collectors.toMap(WeekStarRecord::getUid, WeekStarRecord::getAward, Long::sum));
        //存放用户的奖励，登记最近一次的周星奖励
        Map<String, String> awardMap = Maps.newHashMap();
        Set<Long> awardUids = Sets.newHashSet(integerAwardMap.keySet());
        awardUids.addAll(debrisAwardMap.keySet());
        for (Long uid : awardUids) {

            Long integer = integerAwardMap.getOrDefault(uid, 0L);
            Long debris = debrisAwardMap.getOrDefault(uid, 0L);
            if (integer > 0) {
                String seq = "ZX_INTEGER_" + weekDay + "_" + uid;
                addIntegralOrDebris(uid, SourceType.ZX, 1, integer, time.getTime(), seq, true, true, attr);
            }
            if (debris > 0) {
                String seq = "ZX_DEBRIS_" + weekDay + "_" + uid;
                addIntegralOrDebris(uid, SourceType.ZX, 2, debris, time.getTime(), seq, true, true, attr);
            }
            JSONObject awardJson = new JSONObject();
            awardJson.put("debris", debris);
            awardJson.put("integral", integer);
            awardMap.put(String.valueOf(uid), awardJson.toJSONString());
            log.info("settleWeekStar info@weekDay:{},awardJson:{}", weekDay, awardJson.toJSONString());
        }
        //存放最近一次的奖励
        String lastKey = makeKey(attr, LAST_WEEK_STAR_AWARD);
        actRedisDao.del(groupCode, lastKey);
        actRedisDao.getRedisTemplate(groupCode).opsForHash().putAll(lastKey, awardMap);
        List<WeekStarRecord> bannerList = scoreAwards.stream()
                .filter(item -> item.getRank() == 1 && item.getAwardType() == 1)
                .collect(Collectors.toList());
        //发放周星第一名横幅
        for (WeekStarRecord banner : bannerList) {
            weekStarHighlightBroadcast(attr, banner.getUid(), banner.getAward());
            log.info("settleWeekStar highlight info@,banner:{}", JSON.toJSONString(banner));
        }

        //如果是活动结束的周星结算，给下一个活动发广播（解决前端不展示的问题）
        if (END_WEEK_STAR_DAY.containsKey(weekDay)) {
            long nextActId = END_WEEK_STAR_DAY.get(weekDay);
            for (WeekStarRecord banner : bannerList) {
                try {
                    commonBroadCastService.commonBannerBroadcastToMakeFriend(nextActId, banner.getUid(), banner.getAward(), attr.getWeekStarHighlightSvgaUrl(), WEEK_STAR_HIGHLIGHT_BANNER_ID);
                    log.info("settleWeekStar send next actId:{} highlight info@,banner:{}", nextActId, JSON.toJSONString(banner));
                } catch (Exception ex) {
                    log.error("weekStarHighlightBroadcast send next actId error,nextActId={},uid={},score={}", nextActId, banner.getUid(), banner.getAward(), ex);
                }
            }
        }

        //上报海度
        for (WeekStarRecord award : scoreAwards) {
            haiduDataReporting.reportWeekStarScore(actId, award.getUid() + "", award.getAward(),
                    award.getRank(), award.getRoleId(), award.getThreshold(), award.getAwardType());
        }
        log.info("settleWeekStar end@actId:{},weekDay:{}", actId, weekDay);
    }

    /**
     * 结算周星
     *
     * @param anchorUid
     * @param rank
     * @param score
     * @param config
     * @return
     */
    private WeekStarRecord settleWeekStarRank(long anchorUid, long rank, long score, RoleConfig config) {
        WeekStarRecord scoreAward = new WeekStarRecord(anchorUid, score, Math.toIntExact(rank), config.getRoleId());
        Optional<SettleConfig> maxConfig = config.getWeekStartSettleConfig().stream()
                .filter(item -> item.getRank() == rank && item.getScoreThreshold() <= score)
                .max(Comparator.comparingLong(SettleConfig::getScoreThreshold));
        if (maxConfig.isPresent()) {
            SettleConfig settleConfig = maxConfig.get();
            scoreAward.setAward(settleConfig.getAward());
            scoreAward.setAwardType(settleConfig.getType());
            scoreAward.setThreshold(settleConfig.getScoreThreshold());
        }

        return scoreAward;
    }

    /**
     * 营收的接口没办法控制查询时间，可能时间临界问题
     *
     * @param uid
     * @param roleConfig
     * @param now
     * @param attr
     * @return
     */
    private JSONObject getAnchorWeekStar(long uid, RoleConfig roleConfig, long now, ActJYJFSComponentAttr attr) {
        String weekDay = DateUtil.getMondayDate(new Date(now));
        long actId = attr.getActId();

        Clock clock = new Clock();

        List<WeekStarRank> weekStarRanks = queryAnchorWeekStarRank(uid, weekDay, roleConfig, attr);

        clock.tag();
        List<WeekStarRecord> weekStarRecords = Lists.newArrayList();
        for (WeekStarRank weekStarRank : weekStarRanks) {
            JyWeekStarInfo gift = weekStarRank.getJyWeekStarInfo();
            Rank rank = weekStarRank.getRankList().get(0);
            //营收给过来的单位都是紫水晶的单位（对应是RMB的厘，所以要除1000）
            long score = rank.getScore() / 1000;
            //如果是新榜单，那么score就是：礼物数量 * 1000 + 粉丝票数量 * 50
            if (StringUtils.compare(weekDay, attr.getWeekStarTransMondayV2(), true) >= 0) {
                //礼物单价单位是厘，榜单的值是：礼物数量 * 1000 + 粉丝票数量 * 50
                score = gift.getPrice() * rank.getScore() / 1000000;
            }
            WeekStarRecord weekStarRecord = settleWeekStarRank(uid, rank.getRank(), score, roleConfig);
            weekStarRecords.add(weekStarRecord);
        }
        clock.tag();
        JSONObject weekStarInfo = new JSONObject();
        //预计获得积分
        long predictIntegral = weekStarRecords.stream()
                .filter(item -> item != null && item.getAwardType() == 1 && item.getAward() > 0)
                .mapToLong(WeekStarRecord::getAward).sum();
        weekStarInfo.put("predictIntegral", predictIntegral);
        //上轮获得的积分和碎片
        String groupCode = redisConfigManager.getGroupCode(actId);
        String lastWeekStarAward = actRedisDao.hget(groupCode, makeKey(attr, LAST_WEEK_STAR_AWARD), uid + "");
        if (StringUtils.isNotBlank(lastWeekStarAward)) {
            JSONObject jsonObject = JSONObject.parseObject(lastWeekStarAward);
            weekStarInfo.put("lastIntegral", jsonObject.getIntValue("integral"));
            weekStarInfo.put("lastDebris", jsonObject.getIntValue("debris"));
        }

        log.info("getAnchorWeekStar done@uid:{},roleId:{},weekDay:{},clock:{}", uid, roleConfig.getRoleId(), weekDay, clock.tag());
        return weekStarInfo;
    }

    /**
     * 主播周星榜单
     *
     * @param weekDay
     * @param propsId
     * @param size
     * @param pointMember
     * @return
     */
    @Cached(timeToLiveMillis = CacheTimeout.HDZT_RANKING_INFO)
    public List<Rank> queryWeekStarRank(long actId, String weekDay, String propsId, long roleId, int size, String pointMember) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);


        RoleConfig roleConfig = attr.getRoleConfig(roleId);
        List<WeekStarRank> weekPropRanks = queryWeekStarRank(weekDay, Integer.parseInt(propsId), size, roleConfig, attr);
        List<Rank> ranks = weekPropRanks.get(0).getRankList();
        //不允许查指定主播
        if (StringUtils.isNotBlank(pointMember)) {
            ranks.add(new Rank(pointMember, -1, -1, ""));
        }
        return ranks;
    }

    /**
     * 查询主播当前
     * 营收的接口没办法控制查询时间，可能时间临界问题，礼物对应不上会抛出异常
     *
     * @param anchorUid
     * @param weekDay
     * @param config
     * @param attr
     * @return
     */
    private List<WeekStarRank> queryAnchorWeekStarRank(long anchorUid, String weekDay, RoleConfig config, ActJYJFSComponentAttr attr) {

        long actId = attr.getActId();
        Clock clock = new Clock();
        boolean isTest = !SysEvHelper.isDeploy() || commonService.isGrey(actId);
        String queryWeekDay = weekDay;
        if (isTest) {
            String offsetWeekDay = DateUtil.getMondayDate();
            log.info("queryAnchorWeekStarRank offset time@weekDay:{},offsetWeekDay:{},type:{}", weekDay, offsetWeekDay, config.getWeekStartType());
            queryWeekDay = offsetWeekDay;
        }

        Map<Integer, JyWeekStarInfo> weekStarInfoMap = jyWeekStartService.queryWeekStarGiftMap(actId, queryWeekDay);
        clock.tag();

        Map<Integer, JyWeekStarInfo> weekGiftPriceMap = weekStarInfoMap.values().stream().collect(Collectors.toMap(JyWeekStarInfo::getPropsId, Function.identity()));

        List<WeekStarRank> weekStarRanks = Lists.newArrayList();
        //模式1中台榜单获取数据
        if (isTest && attr.getGreyWeekStartMode() == 1) {
            long rankId = config.getWeekStartRankId();
            long phaseId = config.getWeekStartPhaseId();

            Map<String, QueryRankingRequest> batchQueryRequestMap = Maps.newHashMap();
            for (Integer queryPropsId : weekStarInfoMap.keySet()) {
                Map<String, String> ext = Maps.newHashMap();
                ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, String.valueOf(queryPropsId));
                QueryRankingRequest rankingRequest = new QueryRankingRequest();
                rankingRequest.setActId(actId);
                rankingRequest.setRankingId(rankId);
                rankingRequest.setDateStr(weekDay);
                rankingRequest.setPhaseId(phaseId);
                rankingRequest.setRankingCount(0);
                rankingRequest.setPointedMember(anchorUid + "");
                rankingRequest.setExtData(ext);

                batchQueryRequestMap.put(String.valueOf(queryPropsId), rankingRequest);
            }

            //批量查询榜单
            Map<String, BatchRankingItem> batchRankingItemMap = hdztRankingThriftClient.queryBatchRanking(batchQueryRequestMap, Maps.newHashMap());

            for (Integer queryPropsId : weekStarInfoMap.keySet()) {
                JyWeekStarInfo gift = weekStarInfoMap.get(queryPropsId);
                BatchRankingItem batchRankingItem = batchRankingItemMap.get(String.valueOf(queryPropsId));
                Rank pointedRank = batchRankingItem.getPointedRank();
                Rank rank = new Rank(anchorUid + "", Math.max(pointedRank.getRank(), 0), Math.max(pointedRank.getScore(), 0L), pointedRank.getItemDesc());
                WeekStarRank weekStarRank = new WeekStarRank(gift, Lists.newArrayList(rank));
                weekStarRanks.add(weekStarRank);
            }
            log.info("queryAnchorWeekStarRank by hdzt@rankId:{},phaseId:{},weekDay:{},data:{} {}", rankId, phaseId, weekDay, JSON.toJSONString(weekStarRanks), clock.tag());
        } else {
            //模式0，从营收获取周星数据
            int type = config.getWeekStartType();
            List<TDatingAnchorRank> ranks = turnoverServiceClient.queryAnchorWeekStarRankingList(anchorUid, type);
            Map<Integer, TDatingAnchorRank> weekRankMap = ranks.stream().collect(Collectors.toMap(TDatingAnchorRank::getPropId, Function.identity()));

            for (Map.Entry<Integer, JyWeekStarInfo> gift : weekGiftPriceMap.entrySet()) {

                Integer propsId = gift.getKey();
                TDatingAnchorRank tRank = weekRankMap.get(propsId);
                //当前周星没有找到榜单数据
                if (tRank == null) {
                    throw new RuntimeException("getAnchorWeekStar week star not find price!type=" + type + ",propsId=" + propsId);
                }
                Rank rank = new Rank(anchorUid + "", Math.toIntExact(tRank.getRank()), tRank.getCount(), "");
                WeekStarRank weekStarRank = new WeekStarRank(gift.getValue(), Lists.newArrayList(rank));
                weekStarRanks.add(weekStarRank);
            }

        }

        return weekStarRanks;
    }

    /**
     * 查询周星榜单数据
     *
     * @param weekDay
     * @param propsId
     * @param size
     * @param config
     * @param attr
     * @return
     */
    public List<WeekStarRank> queryWeekStarRank(String weekDay, Integer propsId, int size, RoleConfig config, ActJYJFSComponentAttr attr) {
        long actId = attr.getActId();
        boolean isTest = !SysEvHelper.isDeploy() || commonService.isGrey(actId);
        //查营收数据要用  queryWeekDay
        String queryWeekDay = weekDay;
        if (isTest) {
            String offsetWeekDay = DateUtil.getMondayDate();
            log.info("queryWeekStarRank offset time@weekDay:{},offsetWeekDay:{}，propsId:{},type:{}", weekDay, offsetWeekDay, propsId, config.getWeekStartType());
            queryWeekDay = offsetWeekDay;
        }
        List<WeekStarRank> ranks = Lists.newArrayList();

        Clock clock = new Clock();
        Map<Integer, JyWeekStarInfo> weekStarInfoMap = jyWeekStartService.queryWeekStarGiftMap(actId, queryWeekDay);
        clock.tag();
        //查询的礼物id非周星礼物
        if (propsId != 0 && !weekStarInfoMap.containsKey(propsId)) {
            log.warn("queryWeekStarRank gift error@propsId:{}", propsId);
            JyWeekStarInfo gift = new JyWeekStarInfo();
            gift.setPropsId(propsId);
            ranks.add(new WeekStarRank(gift, Lists.newArrayList()));
            return ranks;
        }

        List<Integer> propsIds = propsId != 0 ? Lists.newArrayList(propsId) : Lists.newArrayList(weekStarInfoMap.keySet());
        //模式1中台榜单获取数据
        if (isTest && attr.getGreyWeekStartMode() == 1) {
            long rankId = config.getWeekStartRankId();
            long phaseId = config.getWeekStartPhaseId();

            Map<String, QueryRankingRequest> batchQueryRequestMap = Maps.newHashMap();
            for (Integer queryPropsId : propsIds) {
                Map<String, String> ext = Maps.newHashMap();
                ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, String.valueOf(queryPropsId));
                QueryRankingRequest rankingRequest = new QueryRankingRequest();
                rankingRequest.setActId(actId);
                rankingRequest.setRankingId(rankId);
                rankingRequest.setPhaseId(phaseId);
                rankingRequest.setDateStr(weekDay);
                rankingRequest.setRankingCount(size);
                rankingRequest.setPointedMember("");
                rankingRequest.setExtData(ext);

                batchQueryRequestMap.put(String.valueOf(queryPropsId), rankingRequest);
            }

            //批量查询榜单
            Map<String, BatchRankingItem> batchRankingItemMap = hdztRankingThriftClient.queryBatchRanking(batchQueryRequestMap, Maps.newHashMap());

            for (Integer queryPropsId : propsIds) {
                JyWeekStarInfo gift = weekStarInfoMap.get(queryPropsId);
                BatchRankingItem batchRankingItem = batchRankingItemMap.get(String.valueOf(queryPropsId));
                ranks.add(new WeekStarRank(gift, batchRankingItem.getData()));
            }
            log.info("queryWeekStarRank by hdzt@rankId:{},phaseId:{},weekDay:{},propsId:{},time{},data:{}", rankId, phaseId, weekDay, propsId, clock.tag(), JSON.toJSONString(ranks));
        } else {
            int type = config.getWeekStartType();
            //模式2只展示白名单的成员
            boolean isFilter = isTest && attr.getGreyWeekStartMode() == 2;
            List<TCurrentWeekPropRank> weekPropRankList = jyWeekStartService.queryWeekStarRanking(propsId, type, queryWeekDay, isFilter ? Integer.MAX_VALUE : size);
            clock.tag();
            Map<Integer, TCurrentWeekPropRank> rankMap = weekPropRankList.stream().collect(Collectors.toMap(TCurrentWeekPropRank::getPropId, Function.identity()));
            for (Integer queryPropsId : propsIds) {
                JyWeekStarInfo gift = weekStarInfoMap.get(queryPropsId);
                TCurrentWeekPropRank tRank = rankMap.get(queryPropsId);
                List<TRank> tRanksList = Optional.ofNullable(tRank)
                        .map(TCurrentWeekPropRank::getWeekPropRank)
                        .map(TWeekPropRank::getAnchorRankList)
                        .orElse(Lists.newArrayList());

                List<Rank> ranksList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(tRanksList)) {
                    ranksList = IntStream.range(0, tRanksList.size())
                            .mapToObj(i -> tRanksList.get(i).setRank(i + 1))
                            .filter(item -> !isFilter || commonService.checkWhiteList(actId, RoleType.ANCHOR, String.valueOf(item.getUid())))
                            .map(item -> new Rank(item.getUid() + "", Math.toIntExact(item.getRank()), item.getValue(), ""))
                            .limit(size)
                            .collect(Collectors.toList());

                }
                ranks.add(new WeekStarRank(gift, ranksList));
            }
            clock.tag();
            log.info("queryWeekStarRank by turnover@type:{},weekDay:{},propsId:{},isFilter:{},time:{},data:{}", type, weekDay, propsId, isFilter, clock.tag(), JSON.toJSONString(ranks));
        }

        return ranks;
    }

    /**
     * 周星礼物
     *
     * @param actId
     * @param weekDay
     * @return
     */
    @Cached(timeToLiveMillis = CacheTimeout.HDZT_RANKING_INFO)
    public List<WeekStarInfoVo> queryWeekStarGift(long actId, String weekDay) {
        ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            throw new ParameterException("actId error");
        }
        if (!checkWeekDay(actId, weekDay)) {
            throw new ParameterException("weekDay error");
        }
        long now;
        //灰度状态设置成当前周
        if (!SysEvHelper.isDeploy() || commonService.isGrey(actId)) {
            String offsetWeekDay = DateUtil.getMondayDate();
            log.info("queryWeekStarGift offset time@actId:{},weekDay:{},offsetWeekDay:{}", actId, weekDay, offsetWeekDay);
            weekDay = offsetWeekDay;
            now = System.currentTimeMillis();
        } else {
            now = commonService.getNow(actId).getTime();
        }

        return jyWeekStartService.queryWeekStarGiftMap(attr.getActId(), weekDay)
                .values().stream()
                .map(gift -> toWeekStarInfoVo(gift, now))
                .collect(Collectors.toList());
    }

    private long getTime(String weekDay) {
        LocalDate weekDate = LocalDate.parse(weekDay, DateUtil.YYYY_MM_DD);
        long weekDateTime = DateUtil.toDate(weekDate).getTime();

        return weekDateTime;
    }

    private WeekStarInfoVo toWeekStarInfoVo(JyWeekStarInfo jyWeekStarInfo, long now) {
        WeekStarInfoVo weekStarInfoVo = new WeekStarInfoVo();
        BeanUtils.copyProperties(jyWeekStarInfo, weekStarInfoVo);
        // 是否展示周星副本
        boolean showActivityProp = jyWeekStarInfo.getActivityPropId() > 0
                && jyWeekStarInfo.getActShowStartTime().getTime() <= now
                && jyWeekStarInfo.getActShowEndTime().getTime() > now;

        weekStarInfoVo.setShowActivityProp(showActivityProp);
        return weekStarInfoVo;
    }

    /**
     * pk赏金延迟结算通知
     */
//    @Scheduled(cron = "*/30 * * * * ?")
    public void notifySettle() {
        //本地不启动
        if (SysEvHelper.isLocal()) {
            return;
        }
        Set<Long> activityIds = getComponentEffectActIds();
        for (Long actId : activityIds) {
            Date now = commonService.getNow(actId);
            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityCache(actId);
            if (!actInfoService.inActTime(now, activityInfoVo)) {
                continue;
            }
            ActJYJFSComponentAttr attr = getUniqueComponentAttr(actId);
            String groupCode = redisConfigManager.getGroupCode(actId);

            boolean notify = attr.getIntegralMaxLastTime().getTime() + attr.getPkDelaySettleSec() * 1000 < now.getTime() &&
                    actRedisDao.setNX(groupCode, makeKey(attr, PK_SETTLE_FLAG), DateUtil.getNowYyyyMMddHHmmss(), DateUtil.ONE_MONTH_SECONDS);
            log.info("notifySettle start@actId:{},notify:{}", actId, notify);
            if (notify) {
                //睡眠，等待累榜处理数据
                SysEvHelper.waiting(2000);
                String tag = attr.getPkDelaySettleTag();
                for (Long rankId : attr.getPkRankId()) {
                    NotifySettleResponse resp = hdztRankingThriftClient.notifySettle(actId, rankId, 1, tag);
                    if (resp == null || resp.getCode() != 0) {
                        log.warn("notifySettle info@actId:{},rankId:{},tag:{},resp:{}", actId, rankId, tag, resp != null ? resp.toString() : "null");
                        //重试
                        resp = hdztRankingThriftClient.notifySettle(actId, rankId, 1, tag);
                    }
                    log.info("notifySettle info@actId:{},rankId:{},tag:{},resp:{}", actId, rankId, tag, resp != null ? resp.toString() : "null");
                }
                log.info("notifySettle done@actId:{}", actId);
            }
        }
    }

    /**
     * 查询时间比当前时间晚返回 false
     *
     * @param actId
     * @param queryWeekDay
     * @return
     */
    protected boolean checkWeekDay(long actId, String queryWeekDay) {
        return getWeekList(actId).contains(queryWeekDay);
    }

    /**
     * 乱斗高光横幅广播
     **/
    private void ldHighlightBroadcast(ActJYJFSComponentAttr attr, long uid, long score) {
        try {
            String delayKey = Const.addActivityPrefix(JF_ACT_ID, DELAY_BROADCAST_LD_BANNER_KEY + SysEvHelper.getGroup());

            LDBannerBroadcastDelayEvent event = LDBannerBroadcastDelayEvent
                    .builder()
                    .uid(uid)
                    .score(score)
                    .actId(attr.getActId())
                    .svgaUrl(attr.getLdHighlightSvgaUrl())
                    .build();
//            delayQueueService.setDelayEvent(delayKey, DELAY_BROADCAST_LD_BANNER_SEC, event);
        } catch (Exception ex) {
            log.error("ldHighlightBroadcast error,attr={},uid={},score={}", attr, uid, score, ex);
        }
    }

    private void doLDHighlightBroadcast(LDBannerBroadcastDelayEvent event) {
        commonBroadCastService.commonBannerBroadcastToMakeFriend(event.getActId(), event.getUid(), event.getScore(), event.getSvgaUrl(), LD_HIGHLIGHT_BANNER_ID);
    }

    /**
     * 周星高光横幅广播
     **/
    private void weekStarHighlightBroadcast(ActJYJFSComponentAttr attr, long uid, long score) {
        try {
            commonBroadCastService.commonBannerBroadcastToMakeFriend(attr.getActId(), uid, score, attr.getWeekStarHighlightSvgaUrl(), WEEK_STAR_HIGHLIGHT_BANNER_ID);
        } catch (Exception ex) {
            log.error("weekStarHighlightBroadcast error,attr={},uid={},score={}", attr, uid, score, ex);
        }
    }

    /**
     * 主持活动年度资格横幅广播
     **/
    private void anchorYearQualificationBroadcast(ActJYJFSComponentAttr attr, long uid) {
        try {
            commonBroadCastService.commonBannerBroadcastToMakeFriend(attr.getActId(), uid, 0, attr.getAnchorYearQualificationSvgaUrl(), ANCHOR_YEAR_QUALIFICATION_BANNER_ID);
        } catch (Exception ex) {
            log.error("anchorYearQualificationBroadcast error,attr={},uid={}", attr, uid, ex);
        }
    }

    private static final int CHIP_TYPE = 2;

    /**
     * 获得积分 or 碎片 弹窗
     *
     * @param uid      用户id
     * @param type     1 积分 2 碎片
     * @param count    积分/碎片数量
     * @param taskName 任务名称
     **/
    private void obtainPointNotify(ActJYJFSComponentAttr attr, long uid, int type, int count, String taskName) {
        try {
            String typeName = "积分";
            if (type == CHIP_TYPE) {
                typeName = "碎片";
            }

            String notifyValue = attr.getObtainPointNotifyMsgTemplate().replace("{taskName}", taskName)
                    .replace("{typeName}", typeName)
                    .replace("{count}", count + "");

            commonBroadCastService.commonNoticeUnicast(attr.getActId(), OBTAIN_POINT_NOTIFY_TYPE, notifyValue, "", uid);
        } catch (Exception ex) {
            log.error("obtainPointNotify error,attr={},uid={},type={},count={},taskName={}", attr, uid, type, count, taskName, ex);
        }
    }

    /**
     * 碎片自动兑换积分弹窗
     *
     * @param uid         用户id
     * @param debrisCount 碎片数量
     * @param pointCount  积分数量
     **/
    private void pointCompositeNotify(ActJYJFSComponentAttr attr, long uid, int debrisCount, int pointCount) {
        try {
            String notifyValue = attr.getPointCompositeNotifyTemplate()
                    .replace("{debrisCount}", debrisCount + "")
                    .replace("{pointCount}", pointCount + "");

            commonBroadCastService.commonNoticeUnicast(attr.getActId(), POINT_COMPOSITE_NOTIFY_TYPE, notifyValue, "", uid);
        } catch (Exception ex) {
            log.error("pointCompositeNotify error,attr={},uid={},debrisCount={},pointCount={}", attr, uid, debrisCount, pointCount, ex);
        }
    }

    private static final String ZG_PREFIX = "ZG_";

    /**
     * @param anchorUid
     * @param item
     * @param score
     * @param time
     * @param seq
     * @param attr
     * @return
     */
    private boolean updateAnchorScore(long anchorUid, String item, long score, long time, String seq, Map<String, String> extData, ActJYJFSComponentAttr attr) {
        long actId = attr.getActId();
        // 防止重复
        String key = makeKey(attr, UPDATE_SEQ_KEY);
        String groupCode = redisConfigManager.getGroupCode(actId);

        // 资格数据上报
        // 线上非灰度环境,4个赛季共用同一个id,并且重写seq
        if (seq.startsWith(ZG_PREFIX) && SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            key = Const.addActivityPrefix(JF_ACT_ID, UPDATE_SEQ_KEY);
            seq = ZG_PREFIX + anchorUid;
        }

        if (actRedisDao.hget(groupCode, key, seq) != null) {
            log.error("updateAnchorScore seq duplicate ignore@ anchorUid:{},seq:{},time:{}", anchorUid, seq, time);
            return false;
        }

        int i = 0;
        do {
            try {
                i++;
                Long roleId = queryJyAnchorRoleId(actId, anchorUid);
                if (roleId == null) {
                    log.error("updateAnchorScore queryJyAnchorRoleId error,return null,anchorUid:{},try:{}", anchorUid, i++);
                    continue;
                }
                if (!attr.getAnchorRoles().contains(roleId)) {
                    log.error("updateAnchorScore roleId error,anchorUid:{},roleId:{},seq:{},time:{}", anchorUid, roleId, seq, time);
                    return false;
                }

                Map<Long, String> actors = new HashMap<>(Collections.singletonMap(roleId, anchorUid + ""));
                UpdateRankingRequest request = new UpdateRankingRequest();
                request.setBusiId(BusiId.MAKE_FRIEND.getValue());
                request.setActId(attr.getActId());
                request.setSeq(seq);
                request.setActors(actors);
                request.setItemId(item);
                request.setCount(1);
                request.setScore(score);
                request.setRankScores(Maps.newHashMap());
                request.setTimestamp(time);
                request.setExtData(extData);
                UpdateRankingResult result = hdztRankingThriftClient.getProxy().updateRanking(request);

                log.info("updateAnchorScore updateRanking actors:{} score:{} seq:{} result:{} {}",
                        actors, score, seq, result.getCode(), result.getReason());
                actRedisDao.hset(groupCode, key, seq, DateUtil.getNowYyyyMMddHHmmss());
                return true;
            } catch (Exception e) {
                log.error("updateAnchorScore queryJyAnchorRoleId error,anchorUid:{},seq:{},time:{},try:{}", anchorUid, seq, time, i++, e);
            }

        } while (i <= 3);
        return false;
    }

    /**
     * 是否处于屏蔽时间段内
     **/
    public boolean isShield(ActJYJFSComponentAttr attr, long time) {
        // 没有配置
        if (attr.getShieldStartTime() <= 0 || attr.getShieldEndTime() <= 0) {
            return false;
        }

        return attr.getShieldStartTime() <= time && time <= attr.getShieldEndTime();
    }

    public long getCurrentActId(List<Long> actIds, long anchorId) {
        int currentActIndex = 0;
        long currentActId = actIds.get(currentActIndex);


        for (int index = 0; index < actIds.size(); index++) {
            long actId = actIds.get(index);

            ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
            if (activityInfoVo == null) {
                break;
            }

            // 第一个找到的进行中的活动
            // 测试时需要保证旧的活动已过期
            Date now = commonService.getNow(actId);
            long endTime = DateUtil.getLastDayEndTime(activityInfoVo.getEndTime());
            activityInfoVo.setEndTime(endTime);
            if (actInfoService.inActTime(now, activityInfoVo)) {
                currentActId = actId;
                currentActIndex = index;
                break;
            }
        }
        log.info("getCurrentActId currentActId={},index={}", currentActId, currentActIndex);

        // 判断当前用户是否在下一个赛季的灰度白名单中
        // 用户白名单 & 主播白名单
        // 灰度时一般两个名单是一致的,所以判断一个即可
        if (anchorId > 0 && currentActIndex < actIds.size() - 1) {
            int nextSeason = currentActIndex + 1;
            long nextActId = actIds.get(nextSeason);
            log.info("getCurrentActId nextActId={},index={}", nextActId, nextSeason);
            if (commonService.isGrey(nextActId)
                    && commonService.checkWhiteList(nextActId, RoleType.ANCHOR, anchorId + "")) {
                // 返回灰度中的活动信息
                log.info("getCurrentActId uid={} in white list", anchorId);
                currentActId = nextActId;
            }
        }

        return currentActId;
    }
}
