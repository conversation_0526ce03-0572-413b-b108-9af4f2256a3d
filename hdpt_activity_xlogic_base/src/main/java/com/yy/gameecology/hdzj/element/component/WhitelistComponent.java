package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.db.mapper.gameecology.ComponentWhitelistMapper;
import com.yy.gameecology.common.db.model.gameecology.ComponentWhitelist;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.WhitelistComponentAttr;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/whitelist")
public class WhitelistComponent extends BaseActComponent<WhitelistComponentAttr> {

    @Resource
    private ComponentWhitelistMapper componentWhitelistMapper;

    @Autowired
    private GameecologyDao gameecologyDao;

    @Override
    public Long getComponentId() {
        return ComponentId.WHITE_LIST;
    }

    @Cached(timeToLiveMillis = 3 * 60 * 1000)
    public boolean inWhitelist(long actId, long cmptInx, String member) {
        WhitelistComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            throw new IllegalArgumentException("component not config");
        }

        return componentWhitelistMapper.selectCount(actId, (int) cmptInx, member) == 1;
    }

    public List<ComponentWhitelist> batchGetComponentWhitelist(long actId, int cmptInx, List<String> members) {
        WhitelistComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(members)) {
            return Lists.newArrayList();
        }

        ComponentWhitelist where = new ComponentWhitelist();
        where.setActId(actId);
        where.setCmptUseInx(cmptInx);
        List<String> memberWrap = members.stream().map(p -> "'" + p + "'").collect(Collectors.toList());
        return gameecologyDao.select(ComponentWhitelist.class, where, String.format(" and member in(%s)", StringUtils.join(memberWrap, ",")));
    }

    @Cached(timeToLiveMillis = 3 * 60 * 1000)
    public String getConfigValue(long actId, long cmptInx, String member) {
        WhitelistComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return null;
        }

        ComponentWhitelist whitelist = componentWhitelistMapper.selectByPrimaryKey(actId, (int) cmptInx, member);
        return whitelist == null ? null : whitelist.getConfigValue();
    }

    public <T> T getConfigValue(long actId, long cmptInx, String member, Class<T> clazz) {
        WhitelistComponent proxy = (WhitelistComponent) AopContext.currentProxy();
        String configValue = proxy.getConfigValue(actId, cmptInx, member);
        if (configValue == null || clazz.isAssignableFrom(String.class)) {
            return (T) configValue;
        }

        return JSONUtils.parseObject(configValue, clazz);
    }

    public <T> T getConfigValue(long actId, long cmptInx, String member, TypeReference<T> valueTypeRef) {
        WhitelistComponent proxy = (WhitelistComponent) AopContext.currentProxy();
        String configValue = proxy.getConfigValue(actId, cmptInx, member);
        if (configValue == null) {
            return (T) configValue;
        }

        return JSON.parseObject(configValue, valueTypeRef);
    }

    public List<Long> getMemberList(long actId, long cmptInx) {
        List<ComponentWhitelist> list = componentWhitelistMapper.selectMemberList(actId,(int)cmptInx);
        if(CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }

        return list.stream().map(v->Long.parseLong(v.getMember())).collect(Collectors.toList());
    }

    public List<ComponentWhitelist> queryAllMemberList(long actId, long cmptInx) {
        return componentWhitelistMapper.selectMemberList(actId, (int) cmptInx);
    }

    public int ignoreAdd( long actId, long cmptInx,String member,String value) {
        ComponentWhitelist whitelist = new ComponentWhitelist();
        whitelist.setActId(actId);
        whitelist.setCmptUseInx((int)cmptInx);
        whitelist.setMember(member);
        whitelist.setConfigValue(value);
        return componentWhitelistMapper.insertIgnore(whitelist);
    }

    public int batchInsertIgnore(long actId, long cmptInx, List<String> members, String value) {
        List<ComponentWhitelist> insert = Lists.newArrayList();
        for (String member : members) {
            ComponentWhitelist item = new ComponentWhitelist();
            item.setActId(actId);
            item.setCmptUseInx(cmptInx);
            item.setMember(member);
            item.setConfigValue(value);
            insert.add(item);
        }
        return batchInsertIgnore(insert);
    }

    public int batchInsertIgnore(List<ComponentWhitelist> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return componentWhitelistMapper.batchInsertIgnore(list);
    }



    public long whitelistTotal(long actId, int cmptInx) {
        return componentWhitelistMapper.selectTotal(actId,cmptInx);

    }


    @RequestMapping("add")
    public Response<?> add(@RequestParam(name = "actId") long actId,
                           @RequestParam(name = "cmptInx") int cmptInx,
                           @RequestParam(name = "member") String member,
                           @RequestParam(name = "value", required = false) String value) {
        if (SysEvHelper.isDeploy()) {
            return Response.fail(401, "not support！");
        }

        ComponentWhitelist whitelist = new ComponentWhitelist();
        whitelist.setActId(actId);
        whitelist.setCmptUseInx(cmptInx);
        whitelist.setMember(member);
        whitelist.setConfigValue(value);
        int rs = componentWhitelistMapper.insertRepeatable(whitelist);
        log.info("add whitelist with member:{}, rs:{}", member, rs);
        return Response.ok();
    }

    @RequestMapping("remove")
    public Response<?> remove(@RequestParam(name = "actId") long actId,
                              @RequestParam(name = "cmptInx") int cmptInx,
                              @RequestParam(name = "member") String member) {
        if (SysEvHelper.isDeploy()) {
            return Response.fail(401, "not support！");
        }

        int rs = componentWhitelistMapper.deleteByPrimaryKey(actId, cmptInx, member);
        log.info("remove whitelist with member:{}, rs:{}", member, rs);
        return Response.ok();
    }
}
