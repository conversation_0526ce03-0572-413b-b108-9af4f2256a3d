package com.yy.gameecology.hdzj.element.component.attr;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.hdzt.ActShopExchangeResult;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import java.util.List;

@Data
public class AovInviteActShopComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private int busiId;

    @ComponentAttrField(labelText = "奖池id")
    private long taskId;

    @ComponentAttrField(labelText = "虚拟奖品items", remark = "多个房间时逗号分隔;分区显示以此顺序显示",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Integer.class)})
    private List<Integer> noNeedFillAccountItems;

    @ComponentAttrField(labelText = "心愿皮肤itemId")
    private long itemId;

    @ComponentAttrField(labelText = "秒杀时间范围", remark = "多个时间段逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private List<String> openTime = Lists.newArrayList();

    @ComponentAttrField(labelText = "商城分区", remark = "多个房间时逗号分隔;分区显示以此顺序显示",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private List<String> areaInfo = Lists.newArrayList();

    @ComponentAttrField(labelText = "兑换商品信息",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ActShopExchangeResult.class)})
    private List<ActShopExchangeResult> shopExchangeResult;

    @ComponentAttrField(labelText = "心愿皮肤未开启toast")
    private String unOpenToast;

    @ComponentAttrField(labelText = "心愿皮肤库存不足toast")
    private String noEnoughToast;
}
