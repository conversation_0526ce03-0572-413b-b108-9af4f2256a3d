<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.pepc.PepcTeamApplyMapper">
    <resultMap id="PepcTeamApplyResultMap" type="com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeamApply">
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="act_id" property="actId" jdbcType="BIGINT" />
        <result column="team_id" property="teamId" jdbcType="BIGINT" />
        <result column="uid" property="uid" jdbcType="BIGINT" />
        <result column="msg" property="msg" jdbcType="VARCHAR" />
        <result column="state" property="state" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    </resultMap>


    <select id="selectByTeamId" resultMap="PepcTeamApplyResultMap">
        SELECT * FROM pepc_team_apply WHERE team_id = #{teamId} order by create_time desc
    </select>

    <select id="listUnHandleApply" resultMap="PepcTeamApplyResultMap">
        SELECT * FROM pepc_team_apply WHERE team_id = #{teamId} and uid = #{uid} and state = 0
    </select>

    <select id="listUnHandleApplyByAct" resultMap="PepcTeamApplyResultMap">
        SELECT * FROM pepc_team_apply WHERE act_id = #{actId} and uid = #{uid} and state = 0
        and team_id in <foreach collection="teamIds" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
    </select>

    <select id="selectByUid" resultMap="PepcTeamApplyResultMap">
        SELECT * FROM pepc_team_apply WHERE act_id = #{actId} and uid = #{uid} order by create_time desc
    </select>

    <select id="selectById" resultMap="PepcTeamApplyResultMap">
        SELECT * FROM pepc_team_apply WHERE id = #{id}
    </select>

    <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeamApply">
        INSERT INTO pepc_team_apply (act_id, team_id, uid, msg, state, create_time)
        VALUES (#{actId}, #{teamId}, #{uid}, #{msg}, #{state}, #{createTime})
    </insert>

    <update id="updateState">
        UPDATE pepc_team_apply
        SET state = #{state}
        WHERE id = #{id}
    </update>

    <update id="batchUpdateState">
        UPDATE pepc_team_apply
        SET state = #{state}
        where id in <foreach collection="ids" item="applyId" separator="," open="(" close=")">#{applyId}</foreach>
        and state = #{sourceState}
    </update>

    <update id="batchUpdateUidState">
        UPDATE pepc_team_apply
        SET state = #{state}
        where uid = #{uid}
          and state = #{sourceState}
    </update>


</mapper>