package com.yy.gameecology.activity.bean;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.exception.SuperException;

import java.util.Map;

/**
 * <AUTHOR> 2019/9/2
 */
public class Response<T> {
    
    /**
     * 0为成功, 其他为失败
     */
    private int result;
    
    /**
     * 一般为失败原因
     */
    private String reason;

    private T data;

    private Map<String, Map<String, MultiNickItem>> nickExtUsers;

    public static final int OK = 0;

    public static final int BAD_REQUEST = 400;

    public Response() {
    }

    public Response(Code code) {
        this.result = code.getCode();
        this.reason = code.getReason();
    }


    public Response(SuperException e) {
        this.result = e.getCode();
        this.reason = e.getMessage();
    }
    
    public Response(SuperException e, String reason) {
        this.result = e.getCode();
        this.reason = reason;
    }

    public Response(Code code, String reason) {
        this.result = code.getCode();
        this.reason = reason;
    }

    public Response(int result, String reason) {
        this.result = result;
        this.reason = reason;
    }

    public Response(int result, String reason, T data) {
        this.result = result;
        this.reason = reason;
        this.data = data;
    }

    public Response(int result, String reason, T data, Map<String, Map<String, MultiNickItem>> nickExtUsers) {
        this.result = result;
        this.reason = reason;
        this.data = data;
        this.nickExtUsers = nickExtUsers;
    }

    public static <T> Response<T> ok(String msg){
        return new Response<>(OK, msg);
    }

    public static <T> Response<T> ok(){
        return new Response<>(OK, "OK");
    }

    public static <T> Response<T> badRequest(){
        return new Response<>(BAD_REQUEST, "Bad Request");
    }

    public static <T> Response<T> fail(int result, String reason) {
        return new Response<>(result, reason);
    }

    public static <T> Response<T> fail(int result, String reason, T data) {
        return new Response<>(result, reason, data);
    }

    public static <T> Response<T> success(T data){
        return new Response<>(OK, "OK", data);
    }

    public static <T> Response<T> success(T data, String reason) {
        return new Response<>(OK, reason, data);
    }

    public static <T> Response<T> success(T data, Map<String, Map<String, MultiNickItem>> nickExtUsers) {
        return new Response<>(OK, "OK", data, nickExtUsers);
    }

    public boolean success(){
        return result == OK;
    }

    public int getResult() {
        return result;
    }

    public String getReason() {
        return reason;
    }

    public T getData() {
        return data;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Map<String, Map<String, MultiNickItem>> getNickExtUsers() {
        return nickExtUsers;
    }

    public void setNickExtUsers(Map<String, Map<String, MultiNickItem>> nickExtUsers) {
        this.nickExtUsers = nickExtUsers;
    }

    public Response<T> withNickExtUsers(Map<String, Map<String, MultiNickItem>> nickExtUsers) {
        this.nickExtUsers = nickExtUsers;
        return this;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
