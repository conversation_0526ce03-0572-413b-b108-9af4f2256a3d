package com.yy.gameecology.hdzj.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2024/3/21
 */
@Data
public class ExchangeRecord {
    
    /**
     * 记录id
     */
    private Long id;
    
    /**
     * 兑换时间
     */
    private Long createTime;
    
    /**
     * 商品id
     */
    private String itemId;
    
    /**
     * 商品名
     */
    private String itemName;
    
    /**
     * 数量
     */
    private Long count;
    
    /**
     * 单位
     */
    private String unit;

    /**
     * 购买时消费货币的数量（单价）
     */
    private long price;

    /**
     * 购买时消费货币的名称
     */
    private String priceName;

    /**
     * 购买需要个扣除多种货币时，返回每种货币对应需要消耗的数量
     */
    private List<CurrencyInfo> priceList;
    
}
