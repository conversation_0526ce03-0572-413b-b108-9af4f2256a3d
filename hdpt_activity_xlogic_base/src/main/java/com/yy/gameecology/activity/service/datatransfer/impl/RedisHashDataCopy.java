package com.yy.gameecology.activity.service.datatransfer.impl;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.KeyCopyResult;
import com.yy.gameecology.activity.service.datatransfer.RedisDataCopy;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-08-22 20:17
 **/
@Component
public class RedisHashDataCopy extends BaseRedisData implements RedisDataCopy {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    private static final int BATCH_SCAN_SIZE = SysEvHelper.isDeploy() ? 500 : 5;

    @Override
    public KeyCopyResult copy(String oldRedisGroup, String newRedisGroup, String key) {
        Clock clock = new Clock();

        checkHashKey(newRedisGroup, key);

        List<String> fieldKey = Lists.newArrayList();
        List<String> filedValue = Lists.newArrayList();

        //get
        Cursor<Map.Entry<Object, Object>> cursor =
                actRedisGroupDao.getRedisTemplate(oldRedisGroup).opsForHash().scan(key, ScanOptions.scanOptions().count(BATCH_SCAN_SIZE).match("*").build());
        while (cursor.hasNext()) {
            Map.Entry<Object, Object> data = cursor.next();
            fieldKey.add(String.valueOf(data.getKey()));
            filedValue.add(String.valueOf(data.getValue()));
        }
        try {
            cursor.close();
        } catch (Exception e) {
            log.error("copy error,cursor failed,old group:{},new group:{},key:{},e:{}", oldRedisGroup, newRedisGroup, key, e.getMessage(), e);
            throw new RuntimeException(e);
        }

        //set
        Long seconds = actRedisGroupDao.getExpire(oldRedisGroup, key);
        List<List<String>> batchKey = Lists.partition(fieldKey, BATCH_SCAN_SIZE);
        List<List<String>> batchValue = Lists.partition(filedValue, BATCH_SCAN_SIZE);
        for (int i = 0; i < batchKey.size(); i++) {
            actRedisGroupDao.hsetBatchKey(newRedisGroup, key, batchKey.get(i), batchValue.get(i), seconds == null ? -1 : seconds);
        }


        log.info("copy ok,old group:{},new group:{},key:{},size:{},cost:{}", oldRedisGroup, newRedisGroup, key, fieldKey.size(), clock.tag());


        if (Convert.toLong(seconds, 0) <= 0) {
            log.warn("key expire time warn,key:{}", key);
        }
        KeyCopyResult copyResult = new KeyCopyResult();
        copyResult.setKey(key);
        copyResult.setExpireSeconds(seconds);
        return copyResult;
    }
}
