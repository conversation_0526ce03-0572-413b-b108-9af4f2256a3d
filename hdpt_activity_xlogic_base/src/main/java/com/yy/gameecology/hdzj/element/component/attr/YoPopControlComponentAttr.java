package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-12 20:10
 **/
@Data
public class YoPopControlComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "弹窗渠道号", remark = "多个使用英文逗号隔开", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class, labelText = "渠道号")
    })
    private List<String> market = Lists.newArrayList();

    @ComponentAttrField(labelText = "白名单组件索引")
    private long whiteListCmptInx;

    @ComponentAttrField(labelText = "弹窗链接")
    private String popupLink;
}
