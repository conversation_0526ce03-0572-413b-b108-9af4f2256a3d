package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CPWatchwordLotteryQueryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "口令抽奖组件索引", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "多个使用英文逗号隔开")
    protected List<Long> watchwordLotteryIndexes;

    @ComponentAttrField(labelText = "额外抽奖组件索引")
    protected long additionIndex;

    @ComponentAttrField(labelText = "额外索引限制")
    protected int additionCount;
}
