package com.yy.gameecology.activity.service.datatransfer.impl;

import com.yy.gameecology.activity.bean.KeyCopyResult;
import com.yy.gameecology.activity.service.datatransfer.RedisDataCopy;
import com.yy.gameecology.common.utils.Convert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2022-08-22 20:16
 **/
@Component
public class RedisStringDataCopy  extends BaseRedisData implements RedisDataCopy {
    private final Logger log = LoggerFactory.getLogger(this.getClass());



    @Override
    public KeyCopyResult copy(String oldRedisGroup, String newRedisGroup, String key) {

        checkHashKey(newRedisGroup, key);

        String value = actRedisGroupDao.get(oldRedisGroup, key);
        if (value == null) {
            return null;
        }
        Long seconds = actRedisGroupDao.getExpire(oldRedisGroup, key);
        if (Convert.toLong(seconds, 0) > 0) {
            actRedisGroupDao.set(newRedisGroup, key, value, seconds);
        } else {
            actRedisGroupDao.set(newRedisGroup, key, value);
        }

        log.info("copy ok,oldGroup:{},newGroup:{},key:{}", oldRedisGroup, newRedisGroup, key);

        if (Convert.toLong(seconds, 0) <= 0) {
            log.warn("key expire time warn,key:{}", key);
        }
        KeyCopyResult copyResult = new KeyCopyResult();
        copyResult.setKey(key);
        copyResult.setExpireSeconds(seconds);
        return copyResult;
    }
}
