package com.yy.gameecology.hdzj.element.component.xmodule.pepc;

import com.google.common.collect.ImmutableMap;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.mq.hdzk.PepcGameEndEvent;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.dao.mysql.PepcDao;
import com.yy.gameecology.activity.service.pepc.*;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.pepc.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-31 21:44
 **/
@Component
@RestController
@RequestMapping("/5140")
public class PepcPhaseComponent extends BaseActComponent<PepcPhaseComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private PepcDao pepcDao;

    @Autowired
    private PepcTeamGroupService pepcTeamGroupService;

    @Autowired
    private PepcInitGamePhaseDataService pepcInitGamePhaseDataService;

    @Autowired
    private PepcScheduleService pepcScheduleService;

    @Autowired
    private PepcAwardService pepcAwardService;

    @Autowired
    private PepcRankService pepcRankService;

    @Autowired
    private PepcGameLiveService pepcGameLiveService;

    @Autowired
    private PepcPhaseInfoService pepcPhaseInfoService;

    @Autowired
    private PepcGameService pepcGameService;

    @Autowired
    private PepcGameTestService pepcGameTestService;


    @Override
    public Long getComponentId() {
        return ComponentId.PEPC_PHASE;
    }

    /**
     * 初始化赛程阶段
     */
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void initPhaseInfoData() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            timerSupport.work("initPhaseInfoData:" + actId, 60, () -> {
                log.info("initPhaseInfoData,actId:{}", actId);
                var attr = getUniqueComponentAttr(actId);
                pepcInitGamePhaseDataService.doInitPhaseInfoData(attr);
            });
        }

    }


    /**
     * 报名结束后，初始化队伍分组数据
     */
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "* 0/1 * * * ? ")
    public void teamGrouping() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            timerSupport.work("teamGrouping:" + actId, 60, () -> {
                log.info("begin teamGrouping,actId:{}", actId);
                Date now = commonService.getNow(actId);
                pepcTeamGroupService.doTeamGrouping(now, tryGetUniqueComponentAttr(actId), actId);
            });
        }

    }

    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "* 0/1 * * * ? ")
    public void initGamePhaseData() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            timerSupport.work("initGamePhaseData:" + actId, 60, () -> {
                log.info("begin initGamePhaseData,actId:{}", actId);
                pepcInitGamePhaseDataService.doInitGamePhaseData(tryGetUniqueComponentAttr(actId), actId);
            });
        }

    }

    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "* 0/1 * * * ? ")
    public void settleSchedule() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            timerSupport.work("settleSchedule:" + actId, 60, () -> {
                log.info("begin settleSchedule,actId:{}", actId);
                pepcScheduleService.settleSchedule(tryGetUniqueComponentAttr(actId), actId);
            });
        }

    }


    @HdzjEventHandler(value = PepcGameEndEvent.class, canRetry = true)
    public void onPepcGameEndEvent(PepcGameEndEvent event, PepcPhaseComponentAttr attr) {
        log.info("onPepcGameEndEvent -> event:{}, attr:{}", event, attr);
        pepcScheduleService.settleGameResult(attr, event);
    }

    /**
     * 生成阶段数据
     */
    @RequestMapping("/initPhaseInfoDataTest")
    public Response<String> initPhaseInfoDataTest(Long actId, Long cmptInx) {
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        pepcInitGamePhaseDataService.doInitPhaseInfoData(attr);
        return Response.success(System.currentTimeMillis() + "");
    }


    /**
     * 查询活动主要配置信息
     */
    @RequestMapping("/queryPhaseInfo")
    public Response<PepcActInfoVo> queryPhaseInfo(Long actId, Long cmptInx) {
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "未配置赛事");
        }
        long uid = getLoginYYUid();

        Date now = commonService.getNow(actId);
        PepcActInfoVo actInfoVo = pepcScheduleService.getPepcActInfoVo(actId, now, attr, uid);

        return Response.success(actInfoVo);
    }

    /**
     * 皮肤配置
     */
    @RequestMapping("/getSkinConfig")
    public Response<Map<String, Object>> getSkinConfig(long actId, long cmptInx) {
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        List<ActSkinVo> actSkins = attr.getActSkinConfigs().stream().map(p -> {
            ActSkinVo skin = new ActSkinVo();
            skin.setSkinCode(p.getSkinCode());
            skin.setStartTime(p.getStartTime());
            skin.setEndTime(p.getEndTime());
            return skin;
        }).toList();
        Map<String, Object> result = ImmutableMap.of("defaultSkin", attr.getDefaultSkin()
                , "actSkins", actSkins
                , "currentTime", commonService.getNow(actId).getTime()
                , "serverTime", System.currentTimeMillis());
        return Response.success(result);
    }


    /**
     * 赛程分组信息
     *
     * @param actId
     * @param cmptInx
     * @return
     */
    @RequestMapping("/queryGroup")
    public Response<List<PepcGroupPhaseVo>> queryGroup(Long actId, Long cmptInx) {
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        return Response.success(pepcTeamGroupService.queryGroupPhase(attr));
    }

    /**
     * 查询直播信息
     */
    @RequestMapping("/queryGroupGameLive")
    public Response<GroupGameLiveVo> queryGroupGameLive(Long actId, Long cmptInx, String groupCode, Integer phaseId) {
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        Date now = commonService.getNow(actId);
        return Response.success(pepcGameLiveService.queryGroupGameLive(attr,now, groupCode, phaseId));
    }

    /**
     * 小组积分排名查询
     */
    @RequestMapping("/queryGroupRank")
    public Response<PepcTeamRankVo> queryGroupRank(Long actId, Long cmptInx, String groupCode, Integer phaseId) {
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        return Response.success(pepcTeamGroupService.queryGroupRank(attr, groupCode, phaseId));
    }

    /**
     * 我的对局数据
     */
    @RequestMapping("/queryMyGameRound")
    public Response<PepcMyGameRoundVo> queryMyGameRound(Long actId, Long cmptInx) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "请登录");
        }
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        var round = pepcScheduleService.getPepcMyGameRoundVo(attr, commonService.getNow(actId), actId, uid);
        return Response.success(round);
    }

    /**
     * 参赛队伍
     */
    @RequestMapping("/queryGroupTeam")
    public Response<PepcGroupTeamVo> queryGroupTeam(Long actId, Long cmptInx, String groupCode, Integer phaseId) {
        return Response.success(pepcTeamGroupService.queryGroupTeam(actId, cmptInx, groupCode, phaseId));
    }

    /**
     * 查询赛事结果
     */
    @RequestMapping("/queryGameResult")
    public Response<PepcGameResultVo> queryGameResult(Long actId, Long cmptInx, Long gameId) {
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        return Response.success(pepcGameService.queryGameResult(attr, cmptInx, gameId));
    }

    @RequestMapping("/replaceGame4Test")
    public Response<String> replaceGame4Test(Long actId, Long cmptInx, Long gameId, String signUpStartTime, String gameStartTime, String seq) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey(actId)) {
            return Response.fail(-1, "act not grey");
        }
        if (StringUtil.isBlank(seq)) {
            return Response.fail(-1, "seq 参数不能为空");
        }
        PepcPhaseComponentAttr attr = getComponentAttr(actId, cmptInx);

        boolean set = actRedisDao.setNX(getRedisGroupCode(actId), makeKey(attr, "replaceGame4Test_" + seq), DateUtil.format(new Date()));
        if (!set) {
            return Response.fail(-1, "seq 已经执行");
        }
        return pepcGameTestService.replaceGame4Test(attr, gameId, new Date(), DateUtil.getDate(gameStartTime));
    }


}
