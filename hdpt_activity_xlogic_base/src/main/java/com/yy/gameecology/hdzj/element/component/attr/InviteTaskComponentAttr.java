package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-10 20:58
 **/
@Data
public class InviteTaskComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "活动app,例如：yomi")
    private String app;

    @ComponentAttrField(labelText = "新老用户判断窗口期（毫秒）")
    private long newUserOffsetMill;

    @ComponentAttrField(labelText = "风控编码")
    private String riskStrategyKey;

    @ComponentAttrField(labelText = "助力风控actionType")
    private String helpRiskStrategyActionType;

    @ComponentAttrField(labelText = "二次挑战标题")
    private String recheckTitle;

    @ComponentAttrField(labelText = "二次挑战提示消息")
    private String recheckMsg;

    @ComponentAttrField(labelText = "邀请人奖励金币数量")
    private int awardAmount;

    @ComponentAttrField(labelText = "个人奖励金币上限")
    private long awardLimit;

    @ComponentAttrField(labelText = "邀请成功IM提醒appId")
    private int imMsgAppId;

    @ComponentAttrField(labelText = "邀请成功IM提醒发消息人uid")
    private long imMsgSenderUid;

    @ComponentAttrField(labelText = "邀请成功提示title")
    private String inviteSucceedTitle;

    @ComponentAttrField(labelText = "邀请成功提示消息")
    private String inviteSucceedMsg;

    @ComponentAttrField(labelText = "活动主页url")
    private String inviteSucceedLink;

    @ComponentAttrField(labelText = "被邀请成功弹窗链接")
    private String receiveInvitePopUrl;

    @ComponentAttrField(labelText = "金币发奖业务id")
    private long awardBusiId;

    @ComponentAttrField(labelText = "金币发奖奖池id")
    private long awardTaskId;

    @ComponentAttrField(labelText = "金币发奖奖包id")
    private long awardPackageId;

    @ComponentAttrField(labelText = "金币虚拟货币业务Id")
    private int currencyBusiId;

    @ComponentAttrField(labelText = "金币虚拟货币活动id")
    private String currencyCid;

    @ComponentAttrField(labelText = "邀请失败顶部toast title")
    private String inviteFailedTitle;

    @ComponentAttrField(labelText = "邀请失败顶部toast icon")
    private String inviteFailedIcon;

}
