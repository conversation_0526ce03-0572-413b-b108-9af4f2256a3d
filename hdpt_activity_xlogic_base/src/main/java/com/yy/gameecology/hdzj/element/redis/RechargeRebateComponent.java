package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.duowan.udb.util.GsonUtil;
import com.google.common.collect.Maps;
import com.google.common.primitives.Ints;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.HdztAwardLotteryMsg;
import com.yy.gameecology.activity.bean.mq.RechargeOrder;
import com.yy.gameecology.activity.bean.mq.RechargeOrderEvent;
import com.yy.gameecology.activity.client.thrift.FtsPartyGradeClient;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.AntiCheatGnService;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.RequestUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.RechargeRebateComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.RechargeRewardInfo;
import com.yy.gameecology.hdzj.element.event.RechargeActRewardEvent;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yy.gameecology.common.consts.GeParamName.IMGroup.JY_RECHARGE_NOTIFY;

/**
 * <AUTHOR> 2023/8/22
 */
@UseRedisStore
@Component
@RestController
@RequestMapping("/hdzk/rechargeRebate")
public class RechargeRebateComponent extends BaseActComponent<RechargeRebateComponentAttr> {

    public static final int LOWEST_CREDIT_SCORE = 60;
    public static final int CREDIT_UPDATE = 10000;

    @Autowired
    private FtsPartyGradeClient ftsPartyGradeClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private AntiCheatGnService antiCheatGnService;
    
    @Autowired
    private KafkaTemplate<String, String> jiaoyouCrossKafkaTemplate;

    @Override
    public Long getComponentId() {
        return ComponentId.RECHARGE_REBATE;
    }

    /**
     * 个人信息
     *
     * @param actId nginx rewrite传入
     */
    @RequestMapping("/userInfo")
    public Response<UserInfo> userInfo(Long actId){
        long uid = getLoginYYUid();
        if (uid == 0) {
            return Response.fail(401, "未登录");
        }

        long creditScore = ftsPartyGradeClient.queryUserCreditInfo(uid);

        RechargeRebateComponentAttr attr = getComponentAttr(actId, 1);
        Date now = commonService.getNow(actId);
        String key = makeKey(attr, "recharge_score:" + DateUtil.format(now, DateUtil.PATTERN_TYPE2));
        long rechargeScore = Convert.toLong(actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForZSet().score(key, String.valueOf(uid))));

        UserInfo userInfo = new UserInfo();
        userInfo.setCreditScore(creditScore);
        userInfo.setTodayScore(rechargeScore);
        return Response.success(userInfo);
    }

    /**
     * 礼包列表
     */
    @RequestMapping("/rewardInfo")
    public Response<List<RewardInfo>> rewardInfo(Long actId){

        // TODO: 上线时, 要配置追玩充值页活动信息 https://zw.yy.com/index.html#/instance/5e095b62941d235e1c6e90ce/64ec47684a0d1c08d0161e4f

        ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
        boolean inActTime = actInfoService.inActTime(activityInfo);
        Date now = new Date(activityInfo.getCurrentTime());
        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        RechargeRebateComponentAttr attr = getComponentAttr(actId, 1);

        List<RewardInfo> list = attr.getRewardInfos().stream().map(info -> {
            RewardInfo item = new RewardInfo();
            item.setRewardId(info.getRewardId());
            item.setRewardName(info.getRewardName());
            item.setRewardIcon(info.getRewardIcon());
            item.setCostValue(info.getCostValue());

            Rebate rebate = new Rebate();
            rebate.setRebateValue(info.getRebateValue());
            rebate.setRebateName(info.getRebateName());
            rebate.setRebateIcon(info.getRebateIcon());
            rebate.setRebateTag(info.getRebateRate());
            if (inActTime) {
                rebate.setTotal(info.getRebateDayLimit());
                String countKey = makeKey(attr, "rebate_exchange_count:" + info.getRewardId() + ":" + day);
                int exchangeCount = Convert.toInt(actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForValue().get(countKey)));
                rebate.setRemain(info.getRebateDayLimit() - exchangeCount);
            }
            item.setRebate(rebate);

            Privilege p1 = new Privilege();
            p1.setName(info.getShowName());
            p1.setIcon(info.getShowIcon());
            p1.setTag(info.getShowDays() + "天");
            Privilege p2 = new Privilege();
            p2.setName(info.getSignName());
            p2.setIcon(info.getSignIcon());
            p2.setTag(info.getSignDays() + "天");
            item.setPrivilegeList(Arrays.asList(p1, p2));

            return item;
        }).collect(Collectors.toList());

        return Response.success(list);
    }

    /**
     * 兑换奖励
     */
    @RequestMapping("/exchange")
    public Response<ExchangeResult> exchange(Long actId, Integer rewardId, HttpServletRequest request,
                                             @RequestParam(defaultValue = "true") Boolean hasRebate){
        long uid = getLoginYYUid();
        if (uid == 0) {
            return Response.fail(401, "未登录");
        }
        log.info("exchange uid:{} actId:{} rewardId:{} hasRebate:{}", uid, actId, rewardId, hasRebate);

        ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
        if (!actInfoService.inActTime(activityInfo)) {
            return Response.fail(400, "不在活动中");
        }

        // 参数检查
        RechargeRebateComponentAttr attr = getComponentAttr(actId, 1);
        Optional<RechargeRewardInfo> optional = attr.getRewardInfos().stream().filter(r -> r.getRewardId().equals(rewardId)).findFirst();
        if (!optional.isPresent()) {
            return Response.fail(400, "礼包不存在");
        }
        RechargeRewardInfo rewardInfo = optional.get();
        log.info("exchange uid:{} rewardInfo:{}", uid, rewardInfo);

        if (!antiCheatGnService.check202309ActExchange(uid, RequestUtil.getRealIp(request))) {
            log.error("exchange 风险分大于80 uid:{}", uid);
            return Response.fail(400, "您的帐号存在风险，暂时无法兑换");
        }

        // 黑名单
        if (CollectionUtils.isNotEmpty(attr.getBlacklist()) && attr.getBlacklist().contains(uid)) {
            return Response.fail(400, "您的帐号存在风险，暂时无法兑换");
        }

        String exchangeKey = makeKey(attr, "exchange:" + uid);
        Boolean canExchange = actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForValue().setIfAbsent(exchangeKey, "1", 1, TimeUnit.SECONDS));
        if (!Boolean.TRUE.equals(canExchange)) {
            return Response.fail(400, "操作过于频繁，请稍后再试");
        }

        // 积分余额
        Date now = new Date(activityInfo.getCurrentTime());
        String day = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String key = makeKey(attr, "recharge_score:" + day);
        long rechargeScore = Convert.toLong(actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForZSet().score(key, String.valueOf(uid))));
        log.info("exchange uid:{} score:{}", uid, rechargeScore);

        if (rechargeScore < rewardInfo.getCostValue()) {
            return Response.fail(400, "充值积分不足");
        }

        // 库存
        ExchangeResult result = new ExchangeResult();
        if (Boolean.TRUE.equals(hasRebate)) {
            String countKey = makeKey(attr, "rebate_exchange_count:" + rewardId + ":" + day);
            int exchangeCount = Convert.toInt(actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForValue().get(countKey)));
            log.info("exchange uid:{} exchangeCount:{}", uid, exchangeCount);
            if (exchangeCount >= rewardInfo.getRebateDayLimit()) {
                return Response.fail(400, "紫水晶奖励已达发放限量");
            }

            // 扣积分+库存
            Boolean exchange = actRedisGroupDao.execute(attr, redisTemplate -> {
                String script = "local costValue = tonumber( ARGV[2] )\n"
                        + "local dayLimit = tonumber( ARGV[3] )\n"
                        + "local score = tonumber( redis.call( 'ZSCORE', KEYS[1], ARGV[1] ) )\n"
                        + "if costValue > score then\n"
                        + "  return false\n"
                        + "end\n"
                        + "local count = tonumber( redis.call( 'GET', KEYS[2] ) or 0 )\n"
                        + "if count >= dayLimit then\n"
                        + "  return false\n"
                        + "end\n"
                        + "redis.call( 'ZINCRBY', KEYS[1], -costValue, ARGV[1] )\n"
                        + "redis.call( 'INCR', KEYS[2] )\n"
                        + "return true\n"
                        ;
                DefaultRedisScript<Boolean> redisScript = new DefaultRedisScript<>();
                redisScript.setResultType(Boolean.class);
                redisScript.setScriptText(script);
                return redisTemplate.execute(redisScript, Arrays.asList(key, countKey), String.valueOf(uid), String.valueOf(rewardInfo.getCostValue()), String.valueOf(rewardInfo.getRebateDayLimit()));
            });
            log.info("exchange hasRebate uid:{} result:{}", uid, exchange);
            if (!Boolean.TRUE.equals(exchange)) {
                return Response.fail(400, "紫水晶奖励已达发放限量");
            }
            result.setReward(String.format("%d万紫水晶、%d天豪标、%d天%s", rewardInfo.getRebateValue()/10000, rewardInfo.getSignDays(), rewardInfo.getShowDays(), rewardInfo.getShowName()));

            // 发奖
            Map<Long, Map<Long, Integer>> map = new HashMap<>();
            Map<Long, Integer> items = new HashMap<>();
            items.put(rewardInfo.getRebatePackageId(), rewardInfo.getRebateValue());
            items.put(rewardInfo.getShowPackageId(), rewardInfo.getShowDays());
            items.put(rewardInfo.getSignPackageId(), rewardInfo.getSignDays());
            map.put(attr.getTaskId(), items);
            hdztAwardServiceClient.doBatchWelfare(uid, map, DateUtil.format(new Date()), 1, Maps.newHashMap());
            log.info("exchange uid:{} reward:{} package:{}", uid, result.getReward(), map);
        } else {

            // 扣积分
            Boolean exchange = actRedisGroupDao.execute(attr, redisTemplate -> {
                String script = "local costValue = tonumber( ARGV[2] )\n"
                        + "local score = tonumber( redis.call( 'ZSCORE', KEYS[1], ARGV[1] ) )\n"
                        + "if costValue > score then\n"
                        + "  return false\n"
                        + "end\n"
                        + "redis.call( 'ZINCRBY', KEYS[1], -costValue, ARGV[1] )\n"
                        + "return true\n"
                        ;
                DefaultRedisScript<Boolean> redisScript = new DefaultRedisScript<>();
                redisScript.setResultType(Boolean.class);
                redisScript.setScriptText(script);
                return redisTemplate.execute(redisScript, Arrays.asList(key), String.valueOf(uid), String.valueOf(rewardInfo.getCostValue()));
            });
            log.info("exchange noneRebate uid:{} result:{}", uid, exchange);
            if (!Boolean.TRUE.equals(exchange)) {
                return Response.fail(400, "充值积分不足");
            }
            result.setReward(String.format("%d天豪标、%d天%s", rewardInfo.getSignDays(), rewardInfo.getShowDays(), rewardInfo.getShowName()));

            // 发奖
            Map<Long, Map<Long, Integer>> map = new HashMap<>();
            Map<Long, Integer> items = new HashMap<>();
            items.put(rewardInfo.getShowPackageId(), rewardInfo.getShowDays());
            items.put(rewardInfo.getSignPackageId(), rewardInfo.getSignDays());
            map.put(attr.getTaskId(), items);
            hdztAwardServiceClient.doBatchWelfare(uid, map, DateUtil.format(new Date()), 1, Maps.newHashMap());
            log.info("exchange uid:{} reward:{} package:{}", uid, result.getReward(), map);
        }

        // 兑换记录
        String recordKey = makeKey(attr, "exchange_record:" + uid);
        ExchangeRecord record = new ExchangeRecord();
        record.setTime(DateUtil.format(now, DateUtil.PATTERN_TYPE5));
        record.setReward(result.getReward());
        actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForList().leftPush(recordKey, JSON.toJSONString(record)));

        // 如流机器人通知
        String pcKey = makeKey(attr, "recharge_pc:" + day);
        long pcRecharge = Convert.toLong(actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForHash().get(pcKey, String.valueOf(uid))));
        String appKey = makeKey(attr, "recharge_app:" + day);
        long appRecharge = Convert.toLong(actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForHash().get(appKey, String.valueOf(uid))));
        String msg = String.format("%s, uid:%d, nick:%s\n今日累积充值(PC/APP):%d(%d/%d), 剩余积分:%d\n本次消耗积分:%d, 兑换礼包:%s, 奖励:%s",
                DateUtil.format(new Date()), uid, commonService.getNickName(uid, false),
                pcRecharge + appRecharge, pcRecharge, appRecharge, rechargeScore - rewardInfo.getCostValue(),
                rewardInfo.getCostValue(), rewardInfo.getRewardName(), result.getReward());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(JY_RECHARGE_NOTIFY, msg, Collections.emptyList());

        return Response.success(result);
    }

    /**
     * 兑换记录
     */
    @RequestMapping("/exchangeRecord")
    public Response<List<ExchangeRecord>> exchangeRecord(Long actId){
        long uid = getLoginYYUid();
        if (uid == 0) {
            return Response.fail(401, "未登录");
        }

        RechargeRebateComponentAttr attr = getComponentAttr(actId, 1);
        String recordKey = makeKey(attr, "exchange_record:" + uid);
        List<String> recordList = actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForList().range(recordKey, 0, -1));

        List<ExchangeRecord> list = recordList.stream().map(r -> JSON.parseObject(r, ExchangeRecord.class)).collect(Collectors.toList());

        return Response.success(list);
    }

    @RequestMapping("testRisk")
    public Response testRisk(HttpServletRequest request){
        long uid = getLoginYYUid();
        boolean check = antiCheatGnService.check202309ActExchange(uid, RequestUtil.getRealIp(request));
        return Response.success(check);
    }

    @HdzjEventHandler(value = RechargeOrderEvent.class, canRetry = true)
    public void onRechargeEvent(RechargeOrderEvent event, RechargeRebateComponentAttr attr) {

        RechargeOrder order = event.getOrder();
        long uid = order.getUid();

        if (order.getCurrencyType() != 1) {
            log.info("onRechargeEvent ignore uid:{} currencyType:{}", uid, order.getCurrencyType());
            return;
        }
        long amount = order.getAmount();
        if (amount < attr.getChargeThreshold()) {
            log.info("onRechargeEvent ignore uid:{} rmb:{}", uid, amount);
            return;
        }
        int[] channels = {78, 158, 228};
        if (!Ints.contains(channels, order.getChannelType())) {
            log.info("onRechargeEvent ignore uid:{} channel:{}", uid, order.getChannelType());
            return;
        }
        String[] payChannels = {"Weixin", "Zfb"};
        if (!ArrayUtils.contains(payChannels, order.getPayChannel())) {
            log.info("onRechargeEvent ignore uid:{} payChannel:{}", uid, order.getPayChannel());
            return;
        }
        long creditScore = ftsPartyGradeClient.queryUserCreditInfo(uid);
        if (creditScore < LOWEST_CREDIT_SCORE) {
            log.info("onRechargeEvent ignore uid:{} creditScore:{}", uid, creditScore);
            return;
        }

        // 黑名单
        if (CollectionUtils.isNotEmpty(attr.getBlacklist()) && attr.getBlacklist().contains(uid)) {
            log.info("onRechargeEvent ignore blacklist uid:{}", uid);
            return;
        }

        String seqKey = makeKey(attr, "recharge_seq");
        Boolean unique = actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForHash().putIfAbsent(seqKey, String.valueOf(order.getId()), String.valueOf(System.currentTimeMillis())));
        if (!Boolean.TRUE.equals(unique)) {
            log.info("onRechargeEvent ignore unique uid:{}", uid);
            return;
        }

        // 充值时间
        Date rechargeTime = new Date(commonService.getTime(order.getFinishTime(), attr.getActId()));

        // 增加积分
        String day = DateUtil.format(rechargeTime, DateUtil.PATTERN_TYPE2);
        String key = makeKey(attr, "recharge_score:" + day);
        long current = Convert.toLong(actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForZSet().incrementScore(key, String.valueOf(uid), amount)));
        log.info("onRechargeEvent success uid:{} score:{} current:{}", uid, amount, current);

        // 日充值金额超过1w加入信用分库
        if (current >= CREDIT_UPDATE && (current - amount < CREDIT_UPDATE)) {
            ftsPartyGradeClient.updateUserCreditInfo(uid);
        }

        int pcChannel = 228;
        if (order.getChannelType() == pcChannel) {
            String pcKey = makeKey(attr, "recharge_pc:" + day);
            actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForHash().increment(pcKey, String.valueOf(uid), amount));
        } else {
            String appKey = makeKey(attr, "recharge_app:" + day);
            actRedisGroupDao.execute(attr, redisTemplate -> redisTemplate.opsForHash().increment(appKey, String.valueOf(uid), amount));
        }

    }

    @HdzjEventHandler(value = HdztAwardLotteryMsg.class, canRetry = false)
    public void onAwardEvent(HdztAwardLotteryMsg msg, RechargeRebateComponentAttr attr) {
        if (msg == null || attr == null) {
            return;
        }
        if (!Objects.equals(msg.getTaskId(), attr.getTaskId())) {
            return;
        }
        if (msg.getActId() != attr.getActId()) {
            return;
        }
        if (CollectionUtils.isEmpty(msg.getData())) {
            return;
        }
        HdztAwardLotteryMsg.Award award = msg.getData().get(0);
        Optional<RechargeRewardInfo> rewardInfo = attr.getRewardInfos().stream().filter(info -> info.getRebateValue() == award.getPackageNum()).findFirst();
        if (rewardInfo.isEmpty()) {
            return;
        }
        RechargeRewardInfo info = rewardInfo.get();
        
        RechargeActRewardEvent event = new RechargeActRewardEvent();
        event.setProducerSeqID(UUID.randomUUID().toString());
        event.setProducerTime(System.currentTimeMillis()/1000);
        event.setActType(2);
        event.setActId(String.valueOf(msg.getActId()));
        event.setActName("充值福利大派送");
        event.setUid(award.getUid());
        event.setRewardTime(System.currentTimeMillis());
        event.setPriceType(2);
        event.setBackRate(info.getRebateRate());
        event.setChargeAmount(info.getCostValue());
        event.setRewardAmount(info.getRebateValue());
        event.setContentDesc(info.getShowDays()+"天"+info.getShowName()+"、"+info.getSignDays()+"天"+info.getSignName());
        
        jiaoyouCrossKafkaTemplate.send("topicRechargeActRewardEvent", GsonUtil.toJson(event));
    }

    @Data
    public static class UserInfo {

        /**
         * 信用分
         */
        private long creditScore;

        /**
         * 今日积分
         */
        private long todayScore;

    }

    @Data
    public static class ExchangeResult {

        /**
         * 兑换到的奖励
         */
        private String reward;

    }

    @Data
    public static class RewardInfo {

        /**
         * 礼包id
         */
        private Integer rewardId;

        /**
         * 礼包名
         */
        private String rewardName;

        /**
         * 礼包图标
         */
        private String rewardIcon;

        /**
         * 消耗积分
         */
        private Long costValue;

        /**
         * 返利奖励
         */
        private Rebate rebate;

        /**
         * 特权奖励
         */
        private List<Privilege> privilegeList;

    }

    @Data
    public static class Rebate {

        /**
         * 返利金额
         */
        private long rebateValue;

        /**
         * 返利货币
         */
        private String rebateName;

        /**
         * 返利图标
         */
        private String rebateIcon;

        /**
         * 返利角标
         */
        private String rebateTag;

        /**
         * 总份数
         */
        private int total;

        /**
         * 剩余份数
         */
        private int remain;

    }
    
    @Data
    public static class Privilege {

        /**
         * 特权名
         */
        private String name;

        /**
         * 图标
         */
        private String icon;

        /**
         * 角标
         */
        private String tag;

    }

    @Data
    public static class ExchangeRecord {

        /**
         * 时间
         */
        private String time;

        /**
         * 奖励
         */
        private String reward;

    }

}
