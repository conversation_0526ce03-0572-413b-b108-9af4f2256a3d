package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.google.protobuf.Descriptors;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.RankResultBroVo;
import com.yy.gameecology.activity.bean.hdzt.*;
import com.yy.gameecology.activity.bean.rankroleinfo.*;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.DelaySvcSDKServiceV2;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.BroadcastRankResultConfig;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.SkipChannelInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.MatchResultNotifyComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.NamedThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 赛果通知组件
 * 注意：一个活动只能配置本组件，且cmptInxUse 必须为1，不满足此条件将拒绝服务！
 *
 * <AUTHOR>
 * @date 2021/4/20 19:40
 */
@UseRedisStore
@Component
public class MatchResultNotifyComponent extends BaseActComponent<MatchResultNotifyComponentAttr> {
    // 普通榜单
    public static final int RT_COMMON = 0;
    // pk胜方
    public static final int RT_PK_WINNER = 1;
    // pk负方
    public static final int RT_PK_LOSER = 2;
    // 达到分值等级
    public static final int RT_SCORE_LEVEL = 3;
    // 贡献
    public static final int RT_CONTRIBUTION = 4;

    public static final String EXCLUDE_DANMAKU = "excludeDanmaku";

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    private ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(10, new NamedThreadFactory("MatchResultNotifyComponent"));

    private static List<Integer> BRO_BUSI_IDS = ImmutableList.of(RankDataSource.YUEZHAN, RankDataSource.BAOBEI
            , RankDataSource.JIAOYOU, RankDataSource.PEIWAN, RankDataSource.SKILL_CARD);

    private static final Map<Integer, String> URI_FILE_MAP = Maps.newHashMap();

    static {
        /** 请保持uri和message对象之间的关系，否则会读取不到 */
        GameecologyActivity.GameEcologyMsg.Builder builder = GameecologyActivity.GameEcologyMsg.newBuilder();
        Descriptors.Descriptor type = builder.getDescriptorForType();
        String fullName = type.getFullName();
        Map<Integer, String> uriFileMap = Maps.newHashMap();
        GameecologyActivity.PacketType[] packetTypes = GameecologyActivity.PacketType.values();
        for (Descriptors.FieldDescriptor fieldDescriptor : type.getFields()) {
            if (fieldDescriptor.getType() == Descriptors.FieldDescriptor.Type.MESSAGE) {
                String fileName = fieldDescriptor.getFullName().replace(fullName + ".", "");
                String fileFullType = fieldDescriptor.getMessageType().getFullName();
                String fileType = fileFullType.substring(fileFullType.lastIndexOf(".") + 1);
                for (GameecologyActivity.PacketType packetType : packetTypes) {
                    if (packetType.name().endsWith(fileType)) {
                        uriFileMap.put(packetType.getNumber(), fileName);
                        break;
                    }
                }
            }
        }
        URI_FILE_MAP.putAll(uriFileMap);
    }

    @Autowired
    private DelaySvcSDKServiceV2 delaySvcSDKServiceV2;

    @Override
    public Long getComponentId() {
        return ComponentId.MATCH_RESULT_NOTIFY;
    }

    @Override
    public boolean isUniq1UseIndex() {
        return true;
    }

    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = false)
    public void onRankingTimeEnd(RankingTimeEnd event, MatchResultNotifyComponentAttr attr) {
        doEventHandle(event.getUri(), event.getActId(), event.getRankId(), 0L, event.getTimeKey(), event.getEndTime(), attr);
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEnd(PhaseTimeEnd event, MatchResultNotifyComponentAttr attr) {
        log.info("onPhaseTimeEnd,event={}", event);
        doEventHandle(event.getUri(), event.getActId(), event.getRankId(), event.getPhaseId(), event.getTimeKey(), event.getEndTime(), attr);
    }

    @HdzjEventHandler(value = PkSettleTimeEnd.class, canRetry = false)
    public void onPkSettleTimeEnd(PkSettleTimeEnd event, MatchResultNotifyComponentAttr attr) {
        doEventHandle(event.getUri(), event.getActId(), event.getRankId(), event.getPhaseId(), event.getTimeKey(), event.getEndTime(), attr);
    }

    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = false)
    public void onPromotTimeEnd(PromotTimeEnd event, MatchResultNotifyComponentAttr attr) {
        doEventHandle(event.getUri(), event.getActId(), event.getRankId(), event.getPhaseId(), event.getTimeKey(), event.getEndTime(), attr);
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, MatchResultNotifyComponentAttr attr) {
        doScoreChangedBro(event, attr);
    }

    /**
     * 处理榜单事件变化的赛果通知
     */
    private void doScoreChangedBro(RankingScoreChanged event, MatchResultNotifyComponentAttr attr) {
        Clock clock = new Clock();
        long actId = event.getActId();
        long uri = event.getUri();
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        Date date = DateUtil.getDate(event.getOccurTime(), DateUtil.DEFAULT_PATTERN);
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), date);
        String groupCode = redisConfigManager.getGroupCode(actId);

        try {
            // 查出榜单下的所有赛果配置
            BroadcastRankResultConfig where = new BroadcastRankResultConfig();
            where.setActId(actId);
            where.setRankId(rankId);
            where.setUri((int) uri);
            where.setStatus(1);
            List<BroadcastRankResultConfig> configs = gameecologyDao.geBroadcastRankResultConfig(where);

            // 收集榜单级别的配置
            List<BroadcastRankResultConfig> configList = configs.stream().filter(config -> {
                return config.getCalcType() == RT_SCORE_LEVEL && event.getRankScore() >= config.getCalcValue() && config.getPhaseId() == 0;
            }).collect(Collectors.toList());

            // 继续收集阶段级别的配置
            if (phaseId > 0) {
                List<BroadcastRankResultConfig> phaseConfigs = configs.stream().filter(config -> {
                    return config.getCalcType() == RT_SCORE_LEVEL && event.getPhaseScore() >= config.getCalcValue() && config.getPhaseId() == phaseId;
                }).collect(Collectors.toList());
                configList.addAll(phaseConfigs);
            }

            if (configList.isEmpty()) {
                log.info("doScoreChangedBro skip2@no result config for actId:{}, rankId:{}, phaseId:{}, uri:{}", actId, rankId, phaseId, uri);
                return;
            }


            // 遍历收集的配置，将已经广播过的排除
            List<BroadcastRankResultConfig> executeConfigs = Lists.newArrayList();
            String member = event.getMember();
            for (BroadcastRankResultConfig config : configList) {
                String broConfigKey = getEventBroConfigKeyWithMember(actId, timeCode, config.getId(), config.getCalcValue(), member);
                if (!actRedisDao.hasKey(groupCode, broConfigKey)) {
                    executeConfigs.add(config);
                } else {
                    log.info("doScoreChangedBro ignore1@actId:{} configId:{}, calcValue:{}", actId, config.getId(), config.getCalcValue());
                }
            }

            if (executeConfigs.isEmpty()) {
                log.info("doScoreChangedBro skip3@no result config for actId:{}, rankId:{}, phaseId:{}, uri:{}", actId, rankId, phaseId, uri);
                return;
            }

            //逆序
            if (attr.isReversedOrder()) {
                executeConfigs = Lists.reverse(executeConfigs);
            }

            // 查询榜单配置信息，下面构造广播消息需要
            RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId, phaseId);
            if (rankingInfo == null) {
                log.error("doScoreChangedBro skip4@rankingInfo is null, actId:{}, rankId:{}, phaseId:{}, uri:{}", actId, rankId, phaseId, uri);
                return;
            }

            // 遍历所有未广播过的配置，逐一广播
            for (BroadcastRankResultConfig config : executeConfigs) {
                // 先构造广播对象
                List<Rank> ranks = Lists.newArrayList(new Rank(member, 0, config.getCalcValue(), ""));
                List<RankItemAny> rankItems = rankingAnyProcessor.genRankBaseInfo(actId, rankId, ranks, true, rankingInfo, "");
                RankMemberInfo rankMemberInfo = rankItems.get(0).getRankMemberInfos().get(0);
                long realScore = config.getPhaseId() == 0 ? event.getRankScore() : event.getPhaseScore();
                String msg = replaceText(actId, config.getText(), rankMemberInfo, config.getCalcValue(), realScore, "");
                String extjson = getExportExtjson(config.getExtjson());
                extjson = replaceText(actId, extjson, rankMemberInfo, config.getCalcValue(), realScore, "", true);
                RankResultBroVo rankResultBroVo = this.getRankResultBroVo(config, rankMemberInfo, msg, extjson, config.getCalcValue());

                // 对成功设置广播标记的进行广播（前面会检查这个标记用于排除已广播的消息）
                String broConfigRankKey = getEventBroConfigKeyWithMember(actId, timeCode, config.getId(), rankResultBroVo.getCalcValue(), member);
                if (actRedisDao.setNX(groupCode, broConfigRankKey, DateUtil.today())) {
                    broadcast(actId, rankResultBroVo.getBroBussId(), rankResultBroVo.getMessage(), 0, config);
                    log.info("doScoreChangedBro ok@actId:{}, rankId:{}, phaseId:{}, timeCode:{}, config:{}, rankResultBroVo:{}",
                            actId, rankId, phaseId, timeCode, config.getId(), rankResultBroVo);
                } else {
                    log.warn("doScoreChangedBro ignore2@actId:{}, rankId:{}, phaseId:{}, timeCode:{}, config:{}, target:{}",
                            actId, rankId, phaseId, timeCode, config.getId(), rankResultBroVo.getTarget());
                }
            }

            log.info("doScoreChangedBro done@event:{}, timeCode:{}, attr:{} {}", event, timeCode, attr, clock.tag());
        } catch (Throwable e) {
            log.error("doScoreChangedBro exception@event:{}, attr:{}, err:{} {}", event, attr, e.getMessage(), clock.tag(), e);
        }
    }

    /**
     * 统一事件参数
     *
     * @param timeKey 时间分榜: 0-不按时间再分, 1-按日再分，2-按小时再分
     */
    public void doEventHandle(Long uri, Long actId, Long rankId, Long phaseId, Long timeKey, String eventTime, MatchResultNotifyComponentAttr attr) {
        //只有日榜和小时榜需要时间
        String timeCode = TimeKeyHelper.getTimeCode(timeKey, DateUtil.getDate(eventTime, DateUtil.DEFAULT_PATTERN));
        String lockerName = getLockerName(uri, actId, rankId, phaseId, timeCode);
        int second = 30;
        Clock clock = new Clock();
        Secret lock = null;
        boolean isExecute = false;
        try {
            // 优雅停机：已关闭，不再去抢锁, 直接返回
            if (ShutdownHolder.isShuttingDown()) {
                log.warn("doEventHandle fail@application is shutdown. stop timer! lockerName:{}", lockerName);
                return;
            }
            lock = locker.lock(lockerName, second);
            if (lock != null) {
                isExecute = true;
                doEventBroHandle(uri, actId, rankId, phaseId, timeCode, attr);
            }
        } catch (Throwable e) {
            log.error("doEventHandle exception@key:{}, err:{}", lockerName, e.getMessage(), e);
        } finally {
            if (lock != null) {
                locker.unlock(lockerName, lock);
            }
            log.info("doEventHandle complete@lockerName:{},isExecute:{} {}", lockerName, isExecute, clock.tag());
        }
    }

    public void doEventBroHandle(Long uri, Long actId, Long rankId, Long phaseId, String timeCode, MatchResultNotifyComponentAttr attr) {
        Clock clock = new Clock();
        //添加延迟广播控制
        long totalDelay = attr.getGlobalDelay();
        Map<Long, Map<Long, Long>> broDelayMap = attr.getBroDelayMap();
        if (!CollectionUtils.isEmpty(broDelayMap) && broDelayMap.get(rankId) != null) {
            Long delay = broDelayMap.get(rankId).get(phaseId);
            if (delay != null) {
                totalDelay += delay;
            }
        }

        String groupCode = redisConfigManager.getGroupCode(actId);
        try {
            BroadcastRankResultConfig where = new BroadcastRankResultConfig();
            where.setActId(actId);
            where.setRankId(rankId);
            where.setPhaseId(phaseId);
            where.setUri(uri.intValue());
            where.setStatus(1);
            List<BroadcastRankResultConfig> configs = gameecologyDao.geBroadcastRankResultConfig(where);


            List<BroadcastRankResultConfig> executeConfigs = Lists.newArrayList();
            for (BroadcastRankResultConfig config : configs) {
                //执行过滤
                String broConfigKey = getEventBroConfigKey(actId, timeCode, config.getId(), config.getCalcValue());
                if (!actRedisDao.hasKey(groupCode, broConfigKey)) {
                    executeConfigs.add(config);
                } else {
                    log.info("doEventBroHandle ignore1@actId:{} configId:{}, calcValue:{}", actId, config.getId(), config.getCalcValue());
                }


            }
            if (executeConfigs.size() <= 0) {
                log.info("doEventBroHandle skip1@no result config for actId:{}, rankId:{}, phaseId:{}, uri:{}", actId, rankId, phaseId, uri);
                return;
            }
            //逆序
            if (attr.isReversedOrder()) {
                executeConfigs = Lists.reverse(executeConfigs);
            }

            RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId, phaseId);
            if (rankingInfo == null) {
                log.error("doEventBroHandle skip2@rankingInfo is null, actId:{}, rankId:{}, phaseId:{}, uri:{}", actId, rankId, phaseId, uri);
                return;
            }

            //简单榜单事件处理
            Map<BroadcastRankResultConfig, List<RankResultBroVo>> resultConfigListMap = Maps.newLinkedHashMap();
            List<BroadcastRankResultConfig> simplyRankConfigs = executeConfigs.stream().filter(config -> config.getCalcType() == RT_COMMON)
                    .collect(Collectors.toList());
            Map<BroadcastRankResultConfig, List<RankResultBroVo>> simplyResultConfigListMap = handleDynamicMatchResult(uri, rankingInfo, simplyRankConfigs, phaseId, timeCode);
            resultConfigListMap.putAll(simplyResultConfigListMap);

            //PK榜单事件处理
            List<BroadcastRankResultConfig> pkRankConfigs = executeConfigs.stream().filter(config -> config.getCalcType() == RT_PK_WINNER || config.getCalcType() == RT_PK_LOSER)
                    .collect(Collectors.toList());
            Map<BroadcastRankResultConfig, List<RankResultBroVo>> pkResultConfigListMap = handleDynamicMatchResultPK(rankingInfo, pkRankConfigs, phaseId, timeCode);
            resultConfigListMap.putAll(pkResultConfigListMap);

            //贡献榜单事件处理
            List<BroadcastRankResultConfig> contriRankConfigs = executeConfigs.stream().filter(config -> config.getCalcType() == RT_CONTRIBUTION).collect(Collectors.toList());
            Map<BroadcastRankResultConfig, List<RankResultBroVo>> contriResultConfigListMap = handleDynamicMatchResultContribution(rankingInfo, contriRankConfigs, phaseId, timeCode);
            resultConfigListMap.putAll(contriResultConfigListMap);

            //发广播
            Set<Map.Entry<BroadcastRankResultConfig, List<RankResultBroVo>>> entries = resultConfigListMap.entrySet();
            for (Map.Entry<BroadcastRankResultConfig, List<RankResultBroVo>> entry : entries) {
                executeOneMessageBroadcast(entry, actId, rankId, phaseId, timeCode, totalDelay);
                totalDelay += 3000;
            }
            log.info("doEventBroHandle done@actId:{}, rankId:{}, phaseId:{}, timeCode:{} {}", actId, rankId, phaseId, timeCode, clock.tag());
        } catch (Throwable e) {
            log.error("doEventBroHandle exception@actId:{}, rankId:{}, phaseId:{}, timeCode:{}, err:{} {}", actId, rankId, phaseId, timeCode, e.getMessage(), clock.tag(), e);
        }
    }

    private void executeOneMessageBroadcast(Map.Entry<BroadcastRankResultConfig, List<RankResultBroVo>> entry, Long actId, Long rankId, Long phaseId, String timeCode, long delay) {
        BroadcastRankResultConfig config = entry.getKey();
        String groupCode = redisConfigManager.getGroupCode(actId);
        for (RankResultBroVo rankResultBroVo : entry.getValue()) {
            String broConfigRankKey = getEventBroConfigKey(actId, timeCode, config.getId(), rankResultBroVo.getCalcValue());
            if (actRedisDao.setNX(groupCode, broConfigRankKey, DateUtil.today())) {
                // ranking=0时，会扩展为全部名次，这里遍历每个名次，单独打标记后广播
                broadcast(actId, rankResultBroVo.getBroBussId(), rankResultBroVo.getMessage(), delay, config);
                log.info("executeOneMessageBroadcast ok@delay:{}, actId:{}, rankId:{}, phaseId:{}, timeCode:{}, config:{}, rankResultBroVo:{}",
                        delay, actId, rankId, phaseId, timeCode, config.getId(), rankResultBroVo);
            } else {
                log.warn("executeOneMessageBroadcast ignore2@delay:{}, actId:{}, rankId:{}, phaseId:{}, timeCode:{}, config:{}, target:{}",
                        delay, actId, rankId, phaseId, timeCode, config.getId(), rankResultBroVo.getTarget());
            }
        }
        // 从配置层面设置标记
        String broConfigKey = getEventBroConfigKey(actId, timeCode, config.getId(), config.getCalcValue());
        actRedisDao.setNX(groupCode, broConfigKey, DateUtil.today());
    }

    /**
     * json生成 GameEcologyMsg
     */
    public GameecologyActivity.GameEcologyMsg createGameEcologyMsg(GameecologyActivity.PacketType packetType, String dataJson) {
        JSONObject act202010MatchResultBroadcast = JSONObject.parseObject(dataJson);
        JSONObject message = new JSONObject();
        //后面要做扩展要保证uri是在map里面的
        String fieldName = URI_FILE_MAP.get(packetType.getNumber());
        message.put(fieldName, act202010MatchResultBroadcast);
        message.put("uri", packetType.getNumber());
        GameecologyActivity.GameEcologyMsg.Builder msg = GameecologyActivity.GameEcologyMsg.newBuilder();
        try {
            JsonFormat.merge(message.toJSONString(), msg);
            return msg.build();
        } catch (JsonFormat.ParseException e) {
            log.error("createGameEcologyMsg error,uri:{},dataJson:{}", packetType.getNumber(), dataJson, e);
            return null;
        }
    }

    /**
     * 填充榜单数据和成员信息数据生成广播对象
     */
    public Map<BroadcastRankResultConfig, List<RankResultBroVo>> handleDynamicMatchResult(long uri, RankingInfo rankingInfo, List<BroadcastRankResultConfig> rankResultConfigs, Long phaseId, String dateStr) {
        Map<BroadcastRankResultConfig, List<RankResultBroVo>> resultConfigListMap = Maps.newLinkedHashMap();
        if (CollectionUtils.isEmpty(rankResultConfigs)) {
            return resultConfigListMap;
        }

        /* 获取数据 */
        long actId = rankingInfo.getActId();
        long rankId = rankingInfo.getRankingId();
        List<Integer> rankings = rankResultConfigs.stream().map(BroadcastRankResultConfig::getCalcValue).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        Integer rankCount = rankings.contains(0) ? Integer.MAX_VALUE : rankings.get(0);
        Map<String, String> extMap = Maps.newHashMap();
        if (uri == BaseEvent.PROMOT_TIME_END) {
            extMap.put(RankExtParaKey.RANK_TYPE_PROMOT, HdztRankType.PROMOT);
        }
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, rankCount, extMap);
        if (CollectionUtils.isEmpty(ranks)) {
            log.error("handleDynamicMatchResult match result not ready rankId:{} phaseId:{} rankCount:{},dateStr:{} ", rankId, phaseId, rankCount, dateStr);
            return resultConfigListMap;
        }

        // 配置排名校验
        List<BroadcastRankResultConfig> validConfigs = Lists.newArrayList();
        List<Integer> rankRanking = ranks.stream().map(Rank::getRank).collect(Collectors.toList());
        for (BroadcastRankResultConfig rankResultConfig : rankResultConfigs) {
            if (rankResultConfig.getCalcValue() == 0 || rankRanking.contains(rankResultConfig.getCalcValue())) {
                validConfigs.add(rankResultConfig);
            } else {
                log.error("handleDynamicMatchResult config ranking error config:{},ranking:{} rankId:{} phaseId:{} rankCount:{},dateStr:{} ",
                        rankResultConfig.getId(), rankResultConfig.getCalcValue(), rankId, phaseId, rankCount, dateStr);
            }
        }
        if (validConfigs.isEmpty()) {
            return resultConfigListMap;
        }

        /*榜单数据处理*/
        //指定了排名
        if (!rankings.contains(0)) {
            ranks = ranks.stream().filter(rank -> rankings.contains(rank.getRank())).collect(Collectors.toList());
        }
        List<RankItemAny> rankItems = rankingAnyProcessor.genRankBaseInfo(actId, rankId, ranks, true, rankingInfo, "");
        //榜单成员校验
        long membersToleSize = rankItems.stream().map(RankItemAny::getRankMemberInfos).map(List::size).count();
        long membersSize = membersToleSize / rankItems.size();
        //榜单成员数量异常,2是cp榜
        final long d1 = 1, d2 = 2;
        if (membersSize != d1 && membersSize != d2) {
            log.error("handleDynamicMatchResult member size error  rankId:{} phaseId:{} ,dateStr:{} ,membersSize:{}", rankId, phaseId, dateStr, membersSize);
            return resultConfigListMap;
        }

        Map<Integer, RankItemAny> rankItemAnyMap = rankItems.stream().collect(Collectors.toMap(RankItemAny::getRank, Function.identity()));

        /*根据配置填充数据*/
        for (BroadcastRankResultConfig rankResultConfig : validConfigs) {
            List<RankItemAny> broadcastRankItems = Lists.newArrayList();
            Integer broadcastRanking = rankResultConfig.getCalcValue();
            if (broadcastRanking == 0) {
                broadcastRankItems.addAll(rankItemAnyMap.values());
            } else {
                //上面已经做了判断 ranking必然存在
                broadcastRankItems.add(rankItemAnyMap.get(broadcastRanking));
            }

            //一个配置处理
            RankMemberInfo rankMemberInfo = null;
            List<RankResultBroVo> rankResultBroVos = Lists.newArrayList();
            try {
                for (RankItemAny broadcastRankItem : broadcastRankItems) {
                    String msg;
                    List<RankMemberInfo> rankMemberInfos = broadcastRankItem.getRankMemberInfos();
                    String text = rankResultConfig.getText();

                    //cp榜，目前只有主播-神豪的cp，以神豪为主
                    RankMemberInfo cpRankMemberInfo = null;
                    if (rankMemberInfos.size() == 2) {
                        boolean isAnchor = rankMemberInfos.get(0).getRoleType() == 200L;
                        rankMemberInfo = rankMemberInfos.get(isAnchor ? 1 : 0);
                        cpRankMemberInfo = rankMemberInfos.get(isAnchor ? 0 : 1);
                    } else {
                        rankMemberInfo = rankMemberInfos.get(0);
                    }

                    msg = replaceText(actId, text, rankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "");
                    msg = replaceText(actId, msg, cpRankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "cp");

                    Assert.isTrue(StringUtil.isNotBlank(msg) && rankMemberInfo != null, "msg is null or rankMemberInfo is null.");
                    //这里会有异常抛出，是配置级别的错误，所有要保证配置正确
                    String extjson = getExportExtjson(rankResultConfig.getExtjson());
                    extjson = replaceText(actId, extjson, rankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "", true);
                    extjson = replaceText(actId, extjson, cpRankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "cp", true);
                    RankResultBroVo rankResultBroVo = getRankResultBroVo(rankResultConfig, rankMemberInfo, msg, extjson, broadcastRankItem.getRank());
                    rankResultBroVos.add(rankResultBroVo);

                }
            } catch (Exception e) {
                log.error("handleDynamicMatchResult config msg replace error,configId:{},config:{},RankItemAny:{} "
                        , rankResultConfig.getId(), JSON.toJSONString(rankResultConfig), JSON.toJSONString(rankMemberInfo), e);
                //一个配置不能部分成功
                rankResultBroVos.clear();
            }

            if (!rankResultBroVos.isEmpty()) {
                resultConfigListMap.put(rankResultConfig, rankResultBroVos);
            }
        }
        return resultConfigListMap;

    }

    /**
     * 填充榜单数据和成员信息数据生成广播对象-pk
     * 1月份活动没有pk榜单，这个函数没有测试，可能有bug，使用之前要测试，
     */
    public Map<BroadcastRankResultConfig, List<RankResultBroVo>> handleDynamicMatchResultPK(RankingInfo rankingInfo, List<BroadcastRankResultConfig> rankResultConfigs, Long phaseId, String dateStr) {
        Map<BroadcastRankResultConfig, List<RankResultBroVo>> resultConfigListMap = Maps.newLinkedHashMap();
        if (CollectionUtils.isEmpty(rankResultConfigs)) {
            return resultConfigListMap;
        }
        long actId = rankingInfo.getActId();
        long rankId = rankingInfo.getRankingId();

        PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, phaseId, dateStr, dateStr, true, true, Maps.newHashMap());
        if (pkInfo == null || CollectionUtils.isEmpty(pkInfo.getPkGroupItems())) {
            //无pk分组信息-错误
            log.error("handleDynamicMatchResultPK can not find pk group info actId ={}, rankId={}, phaseId={}, rankId={}, dateStr={},pkInfo = {} "
                    , actId, rankId, phaseId, rankId, dateStr, pkInfo);
            return resultConfigListMap;
        }
        List<List<GroupMemberItem>> pk = pkInfo.getPkGroupItems().stream()
                .map(PkGroupItem::getMemberPkItems)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        List<Rank> ranks = pk.stream().flatMap(Collection::stream)
                .map(memItem -> new Rank(memItem.getMemberId(), memItem.getRank(), memItem.getScore(), ""))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ranks)) {
            log.error("handleDynamicMatchResultPK match result not ready rankId:{} phaseId:{},dateStr:{} ", rankId, phaseId, dateStr);
            return resultConfigListMap;
        }
        List<BroadcastRankResultConfig> validConfigs = Lists.newArrayList();
        //配置排名校验
        for (BroadcastRankResultConfig rankResultConfig : rankResultConfigs) {
            if (rankResultConfig.getCalcValue() == 0 || rankResultConfig.getCalcValue() <= pk.size()) {
                validConfigs.add(rankResultConfig);
            } else {
                log.error("handleDynamicMatchResultPK config ranking error config:{},ranking:{} rankId:{} phaseId:{},dateStr:{} ",
                        rankResultConfig.getId(), rankResultConfig.getCalcValue(), rankId, phaseId, dateStr);
            }
        }
        if (validConfigs.isEmpty()) {
            return resultConfigListMap;
        } else {
            rankResultConfigs = validConfigs;
        }

        List<RankItemAny> rankItems = rankingAnyProcessor.genRankBaseInfo(actId, rankId, ranks, true, rankingInfo, "");
        Map<Integer, RankItemAny> rankItemAnyMap = rankItems.stream().collect(Collectors.toMap(RankItemAny::getRank, Function.identity()));

        //胜者组的数据
        Map<Integer, List<GroupMemberItem>> winnerMap =
                pk.stream().collect(Collectors.toMap(pkMembers -> pkMembers.get(0).getRank() < pkMembers.get(1).getRank() ? pkMembers.get(0).getRank() : pkMembers.get(1).getRank(), Function.identity()));
        List<Integer> winnerRankSort = winnerMap.keySet().stream().sorted(Integer::compareTo).collect(Collectors.toList());

        //败者组的数据
        Map<Integer, List<GroupMemberItem>> loserMap =
                pk.stream().collect(Collectors.toMap(pkMembers -> pkMembers.get(0).getRank() < pkMembers.get(1).getRank() ? pkMembers.get(1).getRank() : pkMembers.get(0).getRank(), Function.identity()));
        List<Integer> loserRankSort = loserMap.keySet().stream().sorted(Integer::compareTo).collect(Collectors.toList());

        for (BroadcastRankResultConfig rankResultConfig : rankResultConfigs) {
            List<RankItemAny> broadcastRankItems = Lists.newArrayList();
            Integer broadcastRanking = rankResultConfig.getCalcValue().intValue();
            String text = rankResultConfig.getText();
            Map<Integer, List<GroupMemberItem>> pkMap;
            List<Integer> rankSort;
            //pk胜方
            if (rankResultConfig.getCalcType() == RT_PK_WINNER) {
                pkMap = winnerMap;
                rankSort = winnerRankSort;
            } else {
                pkMap = loserMap;
                rankSort = loserRankSort;
            }
            if (broadcastRanking == 0) {
                broadcastRankItems = pkMap.keySet().stream().map(rank -> rankItemAnyMap.get(rank)).collect(Collectors.toList());
            } else {
                //pkRank 是实际的排名，broadcastRanking是在胜者/败者组内的排名
                Integer pkRank = rankSort.get(broadcastRanking - 1);
                RankItemAny broadcastRankItem = rankItemAnyMap.get(pkRank);
                broadcastRankItems.add(broadcastRankItem);
            }
            //一个配置处理
            List<RankResultBroVo> rankResultBroVos = Lists.newArrayList();
            int index = 0;
            try {
                for (; index < broadcastRankItems.size(); index++) {
                    RankItemAny broadcastRankItem = broadcastRankItems.get(index);
                    String msg;
                    Integer ranking = broadcastRanking == 0 ? index + 1 : broadcastRanking;
                    List<RankMemberInfo> rankMemberInfos = broadcastRankItem.getRankMemberInfos();
                    List<GroupMemberItem> pkMems = pkMap.get(broadcastRankItem.getRank());
                    RankMemberInfo rankMemberInfo = rankMemberInfos.get(0);

                    Integer otherRank = pkMems.get(0).getRank() == broadcastRankItem.getRank() ? pkMems.get(1).getRank() : pkMems.get(0).getRank();
                    RankItemAny otherRankItemAny = rankItemAnyMap.get(otherRank);
                    RankMemberInfo pkRankMemberInfo = otherRankItemAny.getRankMemberInfos().get(0);

                    msg = replaceText(actId, text, rankMemberInfo, ranking, broadcastRankItem.getScore(), "");
                    msg = replaceText(actId, msg, pkRankMemberInfo, otherRankItemAny.getRank(), otherRankItemAny.getScore(), "pk");

                    String extjson = getExportExtjson(rankResultConfig.getExtjson());
                    extjson = replaceText(actId, extjson, rankMemberInfo, ranking, broadcastRankItem.getScore(), "", true);
                    extjson = replaceText(actId, extjson, pkRankMemberInfo, otherRankItemAny.getRank(), otherRankItemAny.getScore(), "pk", true);
                    RankResultBroVo rankResultBroVo = getRankResultBroVo(rankResultConfig, rankMemberInfo, msg, extjson, ranking);
                    rankResultBroVos.add(rankResultBroVo);
                }
            } catch (Exception e) {
                log.error("handleDynamicMatchResultPK config msg replace error,configId:{},config:{},RankItemAny:{} "
                        , rankResultConfig.getId(), JSON.toJSONString(rankResultConfig), JSON.toJSONString(broadcastRankItems.get(index)), e);
                rankResultBroVos.clear();
            }

            if (!rankResultBroVos.isEmpty()) {
                resultConfigListMap.put(rankResultConfig, rankResultBroVos);
            }

        }
        return resultConfigListMap;
    }

    /**
     * 填充榜单数据和成员信息数据生成广播对象
     */
    public Map<BroadcastRankResultConfig, List<RankResultBroVo>> handleDynamicMatchResultContribution(RankingInfo rankingInfo, List<BroadcastRankResultConfig> rankResultConfigs, Long phaseId, String dateStr) {
        Map<BroadcastRankResultConfig, List<RankResultBroVo>> resultConfigListMap = Maps.newLinkedHashMap();
        if (CollectionUtils.isEmpty(rankResultConfigs)) {
            return resultConfigListMap;
        }

        /* 获取数据 */
        long actId = rankingInfo.getActId();
        long rankId = rankingInfo.getRankingId();
        List<Integer> rankings = rankResultConfigs.stream().map(BroadcastRankResultConfig::getCalcValue).sorted(Comparator.reverseOrder()).collect(Collectors.toList());
        Integer rankCount = rankings.contains(0) ? Integer.MAX_VALUE : rankings.get(0);
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, rankCount, null);
        if (CollectionUtils.isEmpty(ranks)) {
            log.error("handleDynamicMatchResult match result not ready rankId:{} phaseId:{} rankCount:{},dateStr:{} ", rankId, phaseId, rankCount, dateStr);
            return resultConfigListMap;
        }

        // 配置排名校验
        List<BroadcastRankResultConfig> validConfigs = Lists.newArrayList();
        List<Integer> rankRanking = ranks.stream().map(Rank::getRank).collect(Collectors.toList());
        for (BroadcastRankResultConfig rankResultConfig : rankResultConfigs) {
            if (rankResultConfig.getCalcValue() == 0 || rankRanking.contains(rankResultConfig.getCalcValue())) {
                validConfigs.add(rankResultConfig);
            } else {
                log.error("handleDynamicMatchResult config ranking error config:{},ranking:{} rankId:{} phaseId:{} rankCount:{},dateStr:{} ",
                        rankResultConfig.getId(), rankResultConfig.getCalcValue(), rankId, phaseId, rankCount, dateStr);
            }
        }
        if (validConfigs.isEmpty()) {
            return resultConfigListMap;
        }

        /*榜单数据处理*/
        //指定了排名
        if (!rankings.contains(0)) {
            ranks = ranks.stream().filter(rank -> rankings.contains(rank.getRank())).collect(Collectors.toList());
        }
        List<RankItemAny> rankItems = rankingAnyProcessor.genRankBaseInfo(actId, rankId, ranks, true, rankingInfo, "");
        //榜单成员校验
        long membersToleSize = rankItems.stream().map(RankItemAny::getRankMemberInfos).map(List::size).count();
        long membersSize = membersToleSize / rankItems.size();
        //榜单成员数量异常,2是cp榜
        final long d1 = 1, d2 = 2;
        if (membersSize != d1 && membersSize != d2) {
            log.error("handleDynamicMatchResult member size error  rankId:{} phaseId:{} ,dateStr:{} ,membersSize:{}", rankId, phaseId, dateStr, membersSize);
            return resultConfigListMap;
        }

        Map<Integer, RankItemAny> rankItemAnyMap = rankItems.stream().collect(Collectors.toMap(RankItemAny::getRank, Function.identity()));

        /*根据配置填充数据*/
        for (BroadcastRankResultConfig rankResultConfig : validConfigs) {
            List<RankItemAny> broadcastRankItems = Lists.newArrayList();
            Integer broadcastRanking = rankResultConfig.getCalcValue();
            if (broadcastRanking == 0) {
                broadcastRankItems.addAll(rankItemAnyMap.values());
            } else {
                //上面已经做了判断 ranking必然存在
                broadcastRankItems.add(rankItemAnyMap.get(broadcastRanking));
            }

            //一个配置处理
            RankMemberInfo rankMemberInfo = null;
            List<RankResultBroVo> rankResultBroVos = Lists.newArrayList();
            try {
                // 得到贡献榜的配置
                long contribRankId = rankResultConfig.getContriRankId();
                RankingInfo contribRankingInfo = hdztRankingThriftClient.queryRankConfig(actId, contribRankId, phaseId);

                for (RankItemAny broadcastRankItem : broadcastRankItems) {
                    String msg;
                    List<RankMemberInfo> rankMemberInfos = broadcastRankItem.getRankMemberInfos();
                    String text = rankResultConfig.getText();

                    //cp榜，目前只有主播-神豪的cp，以神豪为主
                    RankMemberInfo cpRankMemberInfo = null;
                    if (rankMemberInfos.size() == 2) {
                        boolean isAnchor = rankMemberInfos.get(0).getRoleType() == 200L;
                        rankMemberInfo = rankMemberInfos.get(isAnchor ? 1 : 0);
                        cpRankMemberInfo = rankMemberInfos.get(isAnchor ? 0 : 1);
                    } else {
                        rankMemberInfo = rankMemberInfos.get(0);
                    }

                    msg = replaceText(actId, text, rankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "");
                    msg = replaceText(actId, msg, cpRankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "cp");

                    // 填充贡献者
                    ImmutableMap<String, String> extmap = ImmutableMap.of(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, rankMemberInfo.getMemberId());
                    List<Rank> contribRanks = hdztRankingThriftClient.queryRanking(actId, contribRankId, phaseId, dateStr, 1, extmap);
                    List<RankItemAny> contribRankItems = rankingAnyProcessor.genRankBaseInfo(actId, rankId, contribRanks, true, contribRankingInfo, "");
                    RankMemberInfo contribRankMemberInfo = contribRankItems.get(0).getRankMemberInfos().get(0);
                    msg = replaceText(actId, msg, contribRankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "contrib");

                    //这里会有异常抛出，是配置级别的错误，所有要保证配置正确
                    Assert.isTrue(StringUtil.isNotBlank(msg) && rankMemberInfo != null, "msg is null or rankMemberInfo is null.");
                    String extjson = getExportExtjson(rankResultConfig.getExtjson());
                    extjson = replaceText(actId, msg, contribRankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "", true);
                    extjson = replaceText(actId, msg, contribRankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "cp", true);
                    extjson = replaceText(actId, msg, contribRankMemberInfo, broadcastRanking, broadcastRankItem.getScore(), "contrib", true);
                    RankResultBroVo rankResultBroVo = getRankResultBroVo(rankResultConfig, rankMemberInfo, msg, extjson, broadcastRankItem.getRank());
                    rankResultBroVos.add(rankResultBroVo);
                }
            } catch (Exception e) {
                log.error("handleDynamicMatchResult config msg replace error,configId:{},config:{},RankItemAny:{} "
                        , rankResultConfig.getId(), JSON.toJSONString(rankResultConfig), JSON.toJSONString(rankMemberInfo), e);
                //一个配置不能部分成功
                rankResultBroVos.clear();
            }

            if (!rankResultBroVos.isEmpty()) {
                resultConfigListMap.put(rankResultConfig, rankResultBroVos);
            }
        }
        return resultConfigListMap;

    }

    private String replaceText(long actId, String text, RankMemberInfo rankMemberInfo, Integer rank, Long score, String pre) {
        return replaceText(actId, text, rankMemberInfo, rank, score, pre, false);
    }

    /**
     * 使用成员数据替换宏定义，后面还可以继续加红定义，替换json的数据，直接生成广播msg
     */
    private String replaceText(long actId, String text, RankMemberInfo rankMemberInfo, Integer rank, Long score, String pre, boolean forJson) {
        if (rankMemberInfo == null) {
            return text;
        }

        Long roleType = rankMemberInfo.getRoleType();
        Long roleId = rankMemberInfo.getRoleId();
        Long busiId = rankMemberInfo.getRoleBusiId();
        String memberId = rankMemberInfo.getMemberId();


        int hit = getHint(busiId, roleType, roleId);
        Map<String, ? extends RoleItem> roleMap = hdztRankGenRoleService.genRankItemMap(actId, 0L, busiId, roleType, roleId, hit
                , Lists.newArrayList(memberId), 0L, "");

        RoleItem roleItem = roleMap.get(memberId);
        // pc端刘俊杰的底部通知栏需要做这个替换！-added by guoliping/2021-04-27
        String name = roleItem.getName();
        if (StringUtil.isNotBlank(name)) {
            if (forJson) {
                name = Base64Utils.encodeToString(name.getBytes(StandardCharsets.UTF_8));
            } else {
                name = name.replace("<", "&lt;").replace(">", "&gt;");
            }
        }

        text = replaceText(text, "##score##", pre, score + "");
        text = replaceText(text, "##rank##", pre, rank + "");
        text = replaceText(text, "##name##", pre, name);
        text = replaceText(text, "##key##", pre, roleItem.getKey());
        text = replaceText(text, "##avatarInfo##", pre, roleItem.getAvatarInfo());

        if (roleItem instanceof ContractInfo) {
            ContractInfo contractInfo = (ContractInfo) roleItem;
            Long signSid = Convert.toLong(contractInfo.getConSid());
            Long signASid = Convert.toLong(contractInfo.getConAsid(), signSid);
            text = replaceText(text, "##sign_asid##", pre, signASid + "");
            text = replaceText(text, "##sign_sid##", pre, signSid + "");
        }

        if (roleItem instanceof GuildRoleItem) {
            GuildRoleItem guildRoleItem = (GuildRoleItem) roleItem;
            text = replaceText(text, "##sid##", pre, guildRoleItem.getSid() + "");
            text = replaceText(text, "##asid##", pre, guildRoleItem.getAsid() + "");
        } else if (roleItem instanceof SubGuildRoleItem) {
            SubGuildRoleItem subGuildRoleItem = (SubGuildRoleItem) roleItem;
            text = replaceText(text, "##sid##", pre, subGuildRoleItem.getSid() + "");
            text = replaceText(text, "##asid##", pre, subGuildRoleItem.getAsid() + "");
            text = replaceText(text, "##ssid##", pre, subGuildRoleItem.getSsid() + "");
        } else if (roleItem instanceof TeamItem) {
            TeamItem teamRankItem = (TeamItem) roleItem;
            text = replaceText(text, "##asid##", pre, teamRankItem.getAsid() + "");
        }
        return text;
    }

    /**
     * 生成广播对象
     */
    private RankResultBroVo getRankResultBroVo(BroadcastRankResultConfig config, RankMemberInfo rankMemberInfo, String msg, String extjson, Integer calcValue) {
        long actId = config.getActId();
        RankResultBroVo rankResultBroVo = new RankResultBroVo();
        rankResultBroVo.setActId(actId);
        rankResultBroVo.setMsg(msg);
        rankResultBroVo.setCalcValue(calcValue);
        rankResultBroVo.setTarget(rankMemberInfo.getMemberId());
        Assert.hasText(config.getBroadcastType(), "broadcastType is null");
        List<Integer> borBusiIds = Lists.newArrayList();
        //默认广播类型，使用角色busiId ,过于依赖榜单配置，最好还是在中控的配置中指定
        final boolean match = "0".equals(config.getBroadcastType());
        if (match) {
            Integer broBusiId = (int) rankMemberInfo.getRoleBusiId();
            borBusiIds = Lists.newArrayList(broBusiId);
        } else {
            //参数检验是否要提取一个方法？
            for (String broadcastType : config.getBroadcastType().split(StringUtil.COMMA)) {
                broadcastType = broadcastType.trim();
                Assert.isTrue(StringUtil.isNumeric(broadcastType), "broadcastType is not number");

                Integer borBusiId = Integer.valueOf(broadcastType);
                Assert.isTrue(BRO_BUSI_IDS.contains(borBusiId), borBusiId + " is not allow broadcast");
                borBusiIds.add(borBusiId);
                //避免重复
                borBusiIds = Lists.newArrayList(Sets.newHashSet(borBusiIds));
            }

        }
        rankResultBroVo.setBroBussId(borBusiIds);

        //这两个可以配置成json替换
        // 和 pc端刘俊杰约定 2021-04-27 后，ge_broadcast_rank_result_config.match_type 配置成小于10000的值，不要用0自动转换了！ - added by guoliping/2021-04-27
        Integer matchType;
        if (config.getMatchType() == 0) {
            matchType = (int) rankMemberInfo.getRoleId();
        } else {
            matchType = config.getMatchType();
        }

        Integer moduleType;
        if (config.getModuleType() == 0) {
            moduleType = changeRoleType2moduleType(rankMemberInfo.getRoleType());
        } else {
            moduleType = config.getModuleType();
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("actId", actId);
        jsonObject.put("content", msg);
        jsonObject.put("moduleType", moduleType);
        jsonObject.put("matchType", matchType);
        jsonObject.put("skipFlag", config.getSkipFlag());
        jsonObject.put("extjson", extjson);

        // 提取频道跳转信息
        if (config.getSkipFlag() != 0) {
            SkipChannelInfo skipChannelInfo = getSkipChannelInfo(rankMemberInfo, false);
            if (skipChannelInfo != null) {
                jsonObject.put("skipChannelInfo", skipChannelInfo);
            }
        }

        GameecologyActivity.GameEcologyMsg message = createGameEcologyMsg(GameecologyActivity.PacketType.kAct202010_MatchResultBroadcast, jsonObject.toJSONString());
        Assert.notNull(message, "GameEcologyMsg is null.");
        rankResultBroVo.setMessage(message);
        return rankResultBroVo;
    }

    /**
     * 获取频道跳转信息，非下面3种情况或者处理异常时返回null
     * 1) 若成员是公会角色，返回公会本身，
     * 2）用户 和 主播角色，返回对应uid当时所在频道，
     * 3）若是厅，直接从成员值上提取出 sid 和 ssid
     */
    private SkipChannelInfo getSkipChannelInfo(RankMemberInfo rankMemberInfo, boolean base64) {
        String member = rankMemberInfo.getMemberId();
        int memberRole = (int) rankMemberInfo.getRoleType();
        long topSid = 0;
        long subSid = 0;
        try {
            long memberId = Convert.toLong(member, 0);
            RoleType roleType = RoleType.findByValue(memberRole);
            if (roleType == RoleType.USER || roleType == RoleType.ANCHOR) {
                UserCurrentChannel channelInfo = commonService.getUserCurrentChannel(memberId);
                if (channelInfo != null) {
                    topSid = channelInfo.getTopsid();
                    subSid = channelInfo.getSubsid();
                }
            } else if (roleType == RoleType.GUILD) {
                topSid = memberId;
                // subSid 为 0 时客户端可默认跳接待频道， 也可调用 WebdbThriftClient.getJiedaiSsid() 获取接待频道
                subSid = 0;
            } else if (roleType == RoleType.HALL) {
                // 厅角色一定是 ${sid}_${ssid} 组成的
                String[] strs = member.split("_");
                topSid = Long.parseLong(strs[0]);
                subSid = Long.parseLong(strs[1]);
            }

            ChannelBaseInfo cbi = commonService.getChannelInfo(topSid, base64);
            SkipChannelInfo skipChannelInfo = new SkipChannelInfo();
            skipChannelInfo.setSid(topSid);
            skipChannelInfo.setSsid(subSid);
            skipChannelInfo.setAsid(cbi.getAsid());
            skipChannelInfo.setName(cbi.getName());
            skipChannelInfo.setLogo(cbi.getLogo());
            return skipChannelInfo;
        } catch (Throwable t) {
            log.error("getSkipSidAndSSid exception@memberId:{}, roleType:{}, err:{}", member, memberRole, t.getMessage(), t);
            return null;
        }
    }


    /**
     * 广播
     */
    public void broadcast(Long actId, List<Integer> busiIds, GameecologyActivity.GameEcologyMsg message, long delay, BroadcastRankResultConfig config) {
        for (Integer busiId : busiIds) {
            if (busiId == BusiId.PEI_WAN.getValue()) {
                if (delay <= 0) {
                    svcSDKService.broadcastAllChanelsInPW(actId, message);
                } else {
                    executorService.schedule(() -> svcSDKService.broadcastAllChanelsInPW(actId, message), delay, TimeUnit.MILLISECONDS);
                }
            } else if (busiId == BusiId.SKILL_CARD.getValue()) {
                if (delay <= 0) {
                    svcSDKService.broadcastAllChanelsInSkillCard(message);
                } else {
                    executorService.schedule(() -> svcSDKService.broadcastAllChanelsInSkillCard(message), delay, TimeUnit.MILLISECONDS);
                }
            } else {
                Template tmp = changeBusiId2Template((long) busiId);
                if (tmp != null) {
                    List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
                    Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
                    if (delay <= 0) {
                        if(config != null && !StringUtil.isEmpty(config.getExtjson()) && config.getExtjson().contains(EXCLUDE_DANMAKU)) {
                            svcSDKService.broadcastTemplateExclude(Template.Jiaoyou, message, exclude);
                        } else {
                            svcSDKService.broadcastTemplate(tmp, message);
                        }
                    } else {
                        if(config != null && !StringUtil.isEmpty(config.getExtjson()) && config.getExtjson().contains(EXCLUDE_DANMAKU)) {
                            delaySvcSDKServiceV2.broadcastTemplate(tmp, message, delay / 1000, 1);
                        }  else {
                            delaySvcSDKServiceV2.broadcastTemplate(tmp, message, delay / 1000, 0);
                        }
                    }
                }
            }
        }
    }

    private Template changeBusiId2Template(Long busiId) {
        switch (busiId.intValue()) {
            case RankDataSource
                    .BAOBEI:
                return Template.Gamebaby;
            case RankDataSource
                    .YUEZHAN:
                return Template.Yuezhan;
            case RankDataSource
                    .JIAOYOU:
                return Template.Jiaoyou;
            default:
                return null;
        }
    }

    /**
     * moduleType 1:公会 2:厅 3:天团 4:主播 5神豪（cp？）
     */
    private int changeRoleType2moduleType(Long roleType) {
        switch (roleType.intValue()) {
            case RoleTypeSource
                    .GUILD:
                return 1;
            case RoleTypeSource
                    .SUB_GUILD:
                return 2;
            case RoleTypeSource
                    .BABY:
                return 4;
            case RoleTypeSource
                    .USER:
                return 5;
            default:
                return 0;
        }
    }

    /**
     * 宏替换
     */
    private String replaceText(String text, String macro, String pre, String value) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(pre)) {
            macro = macro.replaceFirst("^##", "##" + pre.trim() + ".");
        }
        //防止空指针报错
        value = Convert.toString(value);
        return text.replace(macro, value);
    }

    /**
     * 角色信息生成策略
     */
    private int getHint(long busiId, long roleType, long roleId) {
        //陪玩的公会和团有独立的名称，陪玩获取
        final boolean match1 = busiId == BusiId.PEI_WAN.getValue() && (roleType == RoleType.GUILD.getValue() || roleType == RoleType.PWTUAN.getValue());
        final boolean match2 = roleType == RoleType.ANCHOR.getValue() || roleType == RoleType.WAITER.getValue() || roleType == RoleType.TING_MGR.getValue();
        if (match1) {
            return 1;
        } else if (match2) {
            //主播和接待需要获取签约信息
            return 12;
        }
        return 2;
    }

    private String getEventBroConfigKey(Long actId, String time, Long configId, Integer calcValue) {
        String key = String.format("MatchResultNotifyComponent:%d_%s:%d", configId, time, calcValue);
        return Const.addActivityPrefix(actId, key);
    }

    private String getEventBroConfigKeyWithMember(Long actId, String time, Long configId, Integer calcValue, String member) {
        String key = String.format("MatchResultNotifyComponent:%d_%s:%d:%s", configId, time, calcValue, member);
        return Const.addActivityPrefix(actId, key);
    }

    /**
     * 广播事件的key
     */
    private String getLockerName(Long uri, Long actId, Long rankId, Long phaseId, String time) {
        String key = String.format("MatchResultNotifyComponent:locker:%d_%d_%d_%s", rankId, phaseId, uri, time);
        return Const.addActivityPrefix(actId, key);
    }

    private static String getExportExtjson(String extJson) {
        String extjson = "{}";
        if (StringUtils.startsWith(extJson, StringUtil.OPEN_BRACE)) {
            JSONObject extJsonObj = JSON.parseObject(extJson);
            final String epExtJsonKey = "exportExtJson";
            if (extJsonObj.containsKey(epExtJsonKey)) {
                JSONObject exportExtJson = extJsonObj.getJSONObject(epExtJsonKey);
                extjson = exportExtJson.toJSONString();
            }
        }

        return extjson;
    }
}
