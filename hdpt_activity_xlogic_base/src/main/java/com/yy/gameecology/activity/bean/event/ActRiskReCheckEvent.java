package com.yy.gameecology.activity.bean.event;

import com.yy.gameecology.activity.commons.enums.App;
import lombok.Data;

@Data
public class ActRiskReCheckEvent {

    private int msgTp; //0 req 1 rsp

    private AppChallengeUnicastReq appChallengeUnicastReq;

    private AppChallengeRsp appChallengeRsp;

    @Data
    public static class AppChallengeUnicastReq{
        private long uid;

        /**
         * 1=风控，2=自定义
         */
        private  int type;

        /**
         * 策略id或者自定义场景
         */
        private String scene;

        /**
         * 状态码：4001=原始短信验证，5001 = udb二次挑战
         */
        private int code;

        /**
         * 提示信息
         */
        private String msg;

        /**
         * 需要判断app展示，zhuiwan,yomi
         */
        private App app;

        /**
         *  风控来源：zlt =助力团
         */
        private String from;

        /**
         * 验证框标题
         */
        private String title;

        /**
         * 挑战id
         */
        private Long recordId;

        private String mobileMask;

        /**
         * 顶级频道，不为0时，必须是在频道内弹出
         */
        private Long topSid;

        /**
         * 子频道，不为0时，必须是在频道内弹出
         */
        private Long subSid;

        /**
         * 业务参数
         */
        private String param;
    }

    @Data
    public static class AppChallengeRsp {
        private long uid;
        private long recordId;
        private boolean check;
        private String param;
    }
}
