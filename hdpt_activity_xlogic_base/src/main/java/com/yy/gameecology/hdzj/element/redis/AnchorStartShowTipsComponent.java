package com.yy.gameecology.hdzj.element.redis;


import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.AnchorStartShowEvent;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AnchorStartShowTipsComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * @Author: CXZ
 * @Desciption: 主播上麦tips组件
 * @Date: 2021/7/16 15:39
 * @Modified:
 */
@UseRedisStore
@Component
public class AnchorStartShowTipsComponent extends BaseActComponent<AnchorStartShowTipsComponentAttr> {

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    private final static String FIRST_KEY = "first_key";


    @Override
    public Long getComponentId() {
        return ComponentId.ANCHOR_START_SHOW_TIPS;
    }

    @HdzjEventHandler(value = AnchorStartShowEvent.class, canRetry = true)
    public void anchorStartShowTips(AnchorStartShowEvent event, AnchorStartShowTipsComponentAttr attr) {
        if (!ArrayUtils.contains(attr.getBusiIds(), event.getBusiId())) {
            return;
        }
        long actId = attr.getActId();
        long uid = event.getUid();
        String day = commonService.getNowDateTime(actId).format(DateUtil.YYYY_MM_DD);
        if (!attr.getLimitDays().isEmpty() && !attr.getLimitDays().contains(day)) {
            log.info("anchorStartShowTips ignore limit day@uid:{} actId:{} index:{} day:{}", uid, actId, attr.getCmptUseInx(), day);
            return;
        }
        int frequency = attr.getFrequency();
        //频率限制
        final int freType1 = 1, freType2 = 2;
        if (frequency == freType1 || frequency == freType2) {
            String key = frequency == 1 ? makeKey(attr, FIRST_KEY) : makeKey(attr, FIRST_KEY + "_" + day);
            String groupCode = redisConfigManager.getGroupCode(actId);
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
            if (!first) {
                log.info("anchorStartShowTips ignore not first@uid:{} actId:{} index:{} frequency:{} day:{}", uid, actId, attr.getCmptUseInx(), frequency, day);
                return;
            }
        }

        RetryTool.withRetryCheck(actId, event.getSeq(), () -> {
            GameecologyActivity.CommonNoticeResponse.Builder tips = GameecologyActivity.CommonNoticeResponse.newBuilder()
                    .setActId(actId)
                    .setNoticeType(attr.getNoticeType())
                    .setNoticeValue(attr.getNoticeValue())
                    .setExtJson(attr.getNoticeExt());

            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                    .setCommonNoticeResponse(tips).build();
            svcSDKService.unicastUid(uid, msg);

            log.info("anchorStartShowTips done@uid:{} index:{} msg:{}",
                    uid, attr.getCmptUseInx(), JsonFormat.printToString(tips.build()));
        });
    }


}
