package com.yy.gameecology.hdzj.element.redis;

import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CounterComponentAttr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2023-07-25 18:27
 **/
@UseRedisStore
@Component
@RestController
@RequestMapping("/calComponent")
public class CounterComponent extends BaseActComponent<CounterComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public static final String COUNTER_KEY = "counter";

    @Autowired
    private ActInfoService actInfoService;

    @Override
    public Long getComponentId() {
        return ComponentId.COUNTER;
    }

    @GetMapping("inc")
    public Response<Long> inc(HttpServletRequest req, HttpServletResponse resp, long actId, long index) {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        CounterComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            return Response.fail(-2, "参数错误");
        }
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(-3, "未在活动时间内");
        }
        String key = buildCounterKey(attr, actId, uid);
        return Response.success(actRedisDao.incrValue(getRedisGroupCode(actId), key, 1));
    }

    @GetMapping("query")
    public Response<Long> query(HttpServletRequest req, HttpServletResponse resp, long actId, long index) {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        CounterComponentAttr attr = getComponentAttr(actId, index);
        if (attr == null) {
            return Response.fail(-2, "参数错误");
        }
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(-3, "未在活动时间内");
        }
        String key = buildCounterKey(attr, actId, uid);
        return Response.success(Convert.toLong(actRedisDao.get(getRedisGroupCode(actId), key), 0));
    }

    private String buildCounterKey(CounterComponentAttr attr, long actId, long uid) {
        String key = makeKey(attr, COUNTER_KEY);
        if (StringUtil.isNotBlank(attr.getTimeFormat())) {
            key = key + ":" + DateUtil.format(commonService.getNow(actId), attr.getTimeFormat());
        }
        if (attr.isUserDistinct()) {
            key = key + ":" + uid;
        }

        return key;
    }

}
