package com.yy.gameecology.activity.worker.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.BestCPItem;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.RankItem;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.exception.GameEcologyBusinessException;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.worker.web.BaseController;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.HdztRoleId;
import com.yy.gameecology.common.db.model.gameecology.ActTaskAwardRecord;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.bean.AnchorTaskScoreInfo;
import com.yy.gameecology.hdzj.bean.ReceiveAwardInfo;
import com.yy.gameecology.hdzj.element.component.*;
import com.yy.gameecology.hdzj.element.component.attr.GamebabyMonthActComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.ShowLoveComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.TaskContributeLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.history.*;
import com.yy.gameecology.hdzj.element.redis.*;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.fts_base_info_bridge.OnlineChannel;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.*;

/**
 * 通用玩法模块http接口，勋章、尾灯，中奖记录等
 */
@Validated
@RestController
@CrossOrigin(allowCredentials = "true", originPatterns = {"yy.com", "*.yy.com"})
@RequestMapping("/act_play")
public class ActPlayController extends BaseController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;
    @Autowired
    private ActPlayService actPlayService;
    @Autowired
    private GiftPackageYBPayComponent packageComponent;
    @Autowired
    private GiftPackageV2Component packageV2Component;
    @Autowired
    private ShopComponent shopComponent;
    @Autowired
    private VotiveHouseComponent votiveHouseComponent;
    @Autowired
    private LotteryRecordComponent lotteryRecordComponent;

    @Autowired
    private WarfareRegimentComponent warfareRegimentComponent;

    @Autowired
    private MedalTaskComponent medalTaskComponent;

    @Autowired
    private PowerChargeComponent powerChargeComponent;

    @Autowired
    private PkFireComponent pkFireComponent;

    @Autowired
    private RankCardComponent rankCardComponent;

    @Autowired
    private TrueLoveCPComponent trueLoveCPComponent;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BroadCastHelpService broadCastHelpService;
    @Autowired
    private ReceiveAwardComponent receiveAwardComponent;

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private ShowLoveComponent showLoveComponent;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private ZhuiwanDatingExchangeComponent zhuiwanDatingExchangeComponent;

    @Autowired
    private AnchorBonusPkComponent anchorBonusPkComponent;

    @Autowired
    private GiftPackageQRCodePayComponent giftPackageQRCodePayComponent;

    @Autowired
    private MilestoneV2Component milestoneV2Component;

    @Autowired
    private MilestoneV3Component milestoneV3Component;

    @Autowired
    private SunshineTaskDailyAwardComponent sunshineTaskComponent;

    @Autowired
    private RollAwardRecordComponent rollAwardRecordComponent;

    @Autowired
    private ChannelArenaComponent channelArenaComponent;

    @Autowired
    private WishingBottleComponent wishingBottleComponent;

    @Autowired
    private GamebabyMonthActComponent gamebabyMonthActComponent;

    @Autowired
    private ActInfoService actInfoService;


    //act_play/showLove?actId=2021084001&userUid=2522180660&busiId=900&anchorUid=1452273082&giftId=PW_ORDER&giftNum=1314
    // &sid=1452273082&ssid=1452273082&seq=fsafwojfoa&comptUserIndex=1
    @RequestMapping("showLove")
    public Response showLove(HttpServletRequest req, HttpServletResponse resp,
                             long actId, long userUid, long anchorUid, int busiId, String giftId,
                             long giftNum, String seq, long sid, long ssid, long comptUserIndex) throws Exception {
        // 非本地要检查办公ip
        String ip = RequestUtil.getRealIp(req);
        if (!SysEvHelper.isLocal()) {
            if (!isOfficeIp(ip)) {
                throw new Exception("只允许办公网IP发起请求， your ip:" + ip);
            }
        }
        long opUid = getLoginYYUid(req, resp);
        if (SysEvHelper.isDeploy()) {
            if (opUid == 0) {
                return Response.fail(1, "未登录");
            }
            String uids = Const.getGeValue(null, GeParamName.DO_FIX_DATA_OPERA_UID, "");
            if (!ArrayUtils.contains(uids.split(StringUtil.COMMA), opUid + "")) {
                return Response.fail(2, "权限不足");
            }
        }
        String groupCode = redisConfigManager.getGroupCode(actId);
        final boolean suc = actRedisGroupDao.hsetnx(groupCode, "fix_show_love_bro", seq, StringUtil.ONE);
        if (!suc) {
            return Response.fail(3, "请勿重复提交");
        }

        ShowLoveComponentAttr componentAttr = showLoveComponent.getComponentAttr(actId, comptUserIndex);
        showLoveComponent.sendBroadcast(componentAttr, BusiId.findByValue(busiId), userUid, anchorUid, giftId, giftNum, seq, sid, ssid);
        return Response.ok();
    }

    @GetMapping("getRollAwardRecords")
    public Response lotteryRecord(long actId, int index, Integer count, Integer useCache) {
        boolean isUseCache = Convert.toInt(useCache, 1) == 1;
        List<JSONObject> data = rollAwardRecordComponent.getRollAwardRecords(actId, index, count, isUseCache);
        return Response.success(data);
    }


    @GetMapping("newLotteryRecord")
    public Response lotteryRecord(HttpServletRequest req, HttpServletResponse resp, long actId, long index
            , @RequestParam(name = "uid", defaultValue = "0") long uid) {

        if (uid <= 0) {
            uid = getLoginYYUid(req, resp);
        }

        List<JSONObject> list = lotteryRecordComponent.lotteryRecord(actId, uid, index);
        UserBaseInfo userInfo = actPlayService.getUserBaseInfo(uid, null);
        list.forEach(jsonObject -> jsonObject.put("nickname", userInfo.getNick()));

        return Response.success(list);
    }

    @GetMapping("lotteryRoll")
    public Response lotteryRecord(int from, long actId) {
        try {
            return actPlayService.lotteryRoll(from, actId);
        } catch (Exception e) {
            log.error("lotteryRoll err:{}", e.getMessage(), e);
            return Response.fail(500, "服务异常");
        }
    }

    @GetMapping("medal/medalInfo")
    public Response<JSONObject> queryMedalInfoV2(HttpServletRequest req, HttpServletResponse resp, long actId, int index) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        JSONObject data = medalTaskComponent.queryUserMedalInfo(actId, index, uid);
        return Response.success(data);
    }


    @GetMapping("tailLight")
    public JSONObject tailLight(HttpServletRequest req, HttpServletResponse resp, boolean light, long actId) {
        try {
            long uid = getLoginYYUid(req, resp);
            if (uid == 0) {
                JSONObject data = new JSONObject();
                data.put("status", 1);
                data.put("msg", "未登录");
                return data;
            }
            return actPlayService.tailLightWeb(uid, !light, actId);
        } catch (Exception e) {
            log.error("tailLight err:{}", e.getMessage(), e);
            JSONObject data = new JSONObject();
            data.put("status", 1);
            data.put("msg", "很遗憾，点亮失败，请刷新重试！");
            return data;
        }
    }


    @GetMapping("testQueryFtsAllOnLineChannel")
    public List<OnlineChannel> testQueryFtsAllOnLineChannel() {
        return ftsBaseInfoBridgeClient.queryOnlineChannel();
    }

    @GetMapping("giftPackageStatus")
    public Response<JSONObject> giftPackageStatus(HttpServletRequest req, HttpServletResponse resp,
                                                  @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                                  @NotNull @Min(1) @RequestParam(name = "index") Integer index) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return packageComponent.giftPackageStatus(actId, index, uid);
    }

    @GetMapping("buyGiftPackage")
    public Response<String> buyGiftPackage(HttpServletRequest req, HttpServletResponse resp,
                                           @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                           @NotNull @Min(1) @RequestParam(name = "index") Integer index) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        String ip = RequestUtil.getRealIp(req);
        return packageComponent.buyGiftPackage(actId, index, uid, ip);
    }

    @GetMapping("giftPackageStatusV2")
    public Response<JSONObject> giftPackageStatusV2(HttpServletRequest req, HttpServletResponse resp,
                                                    @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                                    @NotNull @Min(1) @RequestParam(name = "index") Integer index) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return packageV2Component.giftPackageStatus(actId, index, uid);

    }

    @GetMapping("buyGiftPackageV2")
    public Response<String> buyGiftPackageV2(HttpServletRequest req, HttpServletResponse resp,
                                             @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                             @NotNull @Min(1) @RequestParam(name = "index") Integer index) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        String ip = RequestUtil.getRealIp(req);
        return packageV2Component.buyGiftPackage(actId, index, uid, ip);
    }

    @GetMapping("shop/exchange")
    public Response exchange(HttpServletRequest req, HttpServletResponse resp,
                             @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                             long index, String id) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return shopComponent.exchange(actId, index, uid, id);
    }

    @GetMapping("shop/queryUserInfo")
    public Response queryUserInfo(HttpServletRequest req, HttpServletResponse resp,
                                  @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                  @NotNull @Min(1) @RequestParam(name = "index") Integer index) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return shopComponent.queryUserInfo(actId, index, uid);
    }

    @GetMapping("shop/queryCommodityInfo")
    public Response queryCommodityInfo(@NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                       @NotNull @Min(1) @RequestParam(name = "index") Integer index) {
        return shopComponent.queryCommodityInfo(actId, index);
    }

    @GetMapping("shop/queryUserPurchaseRecord")
    public Response queryUserPurchaseRecord(HttpServletRequest req, HttpServletResponse resp,
                                            @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                            @NotNull @Min(1) @RequestParam(name = "index") Integer index,
                                            @RequestParam(name = "count") Integer count) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        List<JSONObject> record = shopComponent.queryUserPurchaseRecord(actId, index, uid, count);
        return Response.success(record);

    }

    @GetMapping("house/queryUserTaskInfo")
    public Response queryUserTaskInfo(HttpServletRequest req, HttpServletResponse resp,
                                      @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                      @NotNull @Min(1) @RequestParam(name = "index") Integer index) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        List<JSONObject> record = votiveHouseComponent.queryUserTaskInfo(actId, index, uid);
        return Response.success(record);
    }

    /**
     * 战力团-查询用户是否加入战力团
     */
    @GetMapping("warfareRegiment/queryUserStatus")
    public Response queryWarfareRegimentUserStatus(HttpServletRequest req, HttpServletResponse resp,
                                                   @NotNull @Min(1) @RequestParam(name = "actId") Long actId) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return warfareRegimentComponent.queryUserStatus(actId, uid);
    }

    /**
     * 战力团-查询用户任务
     */
    @GetMapping("warfareRegiment/queryUserTaskInfo")
    public Response queryWarfareUserTaskInfo(HttpServletRequest req, HttpServletResponse resp,
                                             @NotNull @Min(1) @RequestParam(name = "actId") Long actId) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return warfareRegimentComponent.queryUserTaskInfo(actId, uid);
    }

    /**
     * 战力团-查询用户抽奖
     */
    @GetMapping("warfareRegiment/userDrawLottery")
    public Response warfareUserDrawLottery(HttpServletRequest req, HttpServletResponse resp,
                                           @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                           String hdid,
                                           @NotBlank @RequestParam(name = "taskId") String taskId) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        String ip = RequestUtil.getRealIp(req);
        return warfareRegimentComponent.userDrawLottery(actId, uid, taskId, ip, hdid);
    }

    /**
     * 战力团-pc引导用户去追玩
     */
    @GetMapping("warfareRegiment/gotoZw")
    public Response warfareGotoZw(HttpServletRequest req, HttpServletResponse resp,
                                  @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                  @NotNull @Min(1) @RequestParam(name = "anchorUid") Long anchorUid,
                                  @NotNull @Min(1) @RequestParam(name = "sid") Long sid,
                                  @NotNull @Min(1) @RequestParam(name = "ssid") Long ssid,
                                  @NotNull @RequestParam(name = "type") Integer type) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return warfareRegimentComponent.gotoZw(actId, uid, anchorUid, sid, ssid, type);
    }

    /**
     * 战力团-加入主播战力团（追玩app使用）
     */
    @GetMapping("warfareRegiment/add")
    public Response queryWarfareRegimentUserStatus(HttpServletRequest req, HttpServletResponse resp,
                                                   @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                                   @NotNull @Min(1) @RequestParam(name = "anchorUid") Long anchorUid,
                                                   String hdid,
                                                   @NotNull @Min(1) @RequestParam(name = "sid") Long sid,
                                                   @NotNull @Min(1) @RequestParam(name = "ssid") Long ssid) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        String ip = RequestUtil.getRealIp(req);
        return warfareRegimentComponent.addAnchorRegiment(actId, uid, anchorUid, sid, ssid, ip, hdid);
    }

    /**
     * 战力团-主播任务
     */
    @GetMapping("warfareRegiment/queryAnchorTask")
    public Response queryAnchorTask(HttpServletRequest req, HttpServletResponse resp,
                                    @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                    @NotNull @Min(1) @RequestParam(name = "anchorUid") Long anchorUid) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return warfareRegimentComponent.queryAnchorTask(actId, uid, anchorUid);
    }

    /**
     * 战力团-主播抽奖
     */
    @GetMapping("warfareRegiment/anchorDrawLottery")
    public Response anchorDrawLottery(HttpServletRequest req, HttpServletResponse resp,
                                      @NotNull @Min(1) @RequestParam(name = "actId") Long actId) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return warfareRegimentComponent.anchorDrawLottery(actId, uid);
    }

    /**
     * 充能条-查询最佳cp
     *
     * @param actId
     * @return
     */
    @RequestMapping("powerCharge/queryLastCP")
    public Response queryLastCP(@NotNull @Min(1) @RequestParam(name = "actId") Long actId) {
        return powerChargeComponent.queryLastCP(actId);
    }


    /**
     * 【为爱宣战】宣战卡余额查询
     */
    @RequestMapping("/pkFire/queryPkCard")
    public Response<List<RankItem>> queryPkCard(HttpServletRequest req, HttpServletResponse resp,
                                                @NotNull @Min(1) @RequestParam(name = "actId") Long actId) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }

        List<RankItem> list = pkFireComponent.queryPkCard(actId, uid);

        return Response.success(list);
    }

    /**
     * 【为爱宣战】被宣战的团查询
     */
    @RequestMapping("/pkFire/queryAcptPkMember")
    public Response<List<RankItem>> queryAcptPkMember(HttpServletRequest req, HttpServletResponse resp,
                                                      @NotNull @Min(1) @RequestParam(name = "actId") Long actId) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }

        List<RankItem> list = pkFireComponent.queryAcptPkMember(actId, uid);

        return Response.success(list);
    }

    /**
     * 【为爱宣战】发起宣战接口
     */
    @RequestMapping("/pkFire/startPk")
    public Response<String> startPk(HttpServletRequest req, HttpServletResponse resp,
                                    @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                    String cardMember, String acptMember) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }

        try {
            pkFireComponent.invokeStartPk(actId, uid, cardMember, acptMember);
        } catch (SuperException e) {
            return Response.fail(-2, e.getMessage());

        } catch (Exception e) {
            log.error("startPk error,uid:{},cardMember:{},acptMember:{},e:{}", uid, cardMember, acptMember, e.getMessage(), e);
            return Response.fail(-3, "网络超时");
        }

        return Response.ok("发起宣战成功");
    }

    /**
     * 【为爱宣战】为爱宣战pk榜查询
     */
    @RequestMapping("/pkFire/queryPkRank")
    public Response<Map<String, Object>> queryPkRank(HttpServletRequest req, HttpServletResponse resp,
                                                     @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                                     String dateStr) {
        Map<String, Object> mapResult = Maps.newHashMap();
        mapResult.put("list", pkFireComponent.queryPkRank(actId, dateStr));
        return Response.success(mapResult);
    }


    /**
     * 【荣耀卡玩法】使用荣耀积分卡
     */
    @RequestMapping("tuanRankCard/useRankCard")
    public Response<String> useRankCard(HttpServletRequest req, HttpServletResponse resp, String cardMember,
                                        @NotNull @Min(1) @RequestParam(name = "actId") Long actId) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }

        try {
            rankCardComponent.useCard(actId, 1, BusiId.PEI_WAN.getValue(), uid, RoleType.PWTUAN.getValue(), HdztRoleId.PW_TUAN, cardMember);
        } catch (SuperException e) {
            return Response.fail(-2, e.getMessage());

        } catch (Exception e) {
            log.error("useRankCard error,uid:{},cardMember:{},e:{}", uid, cardMember, e.getMessage(), e);
            return Response.fail(-3, "网络超时");
        }

        return Response.ok("使用成功");
    }

    /**
     * 【荣耀卡玩法】查询荣耀积分卡余额
     */
    @RequestMapping("tuanRankCard/queryCardScoreBalance")
    public Response<List<RankItem>> queryCardScoreBalance(HttpServletRequest req, HttpServletResponse resp,
                                                          @NotNull @Min(1) @RequestParam(name = "actId") Long actId) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }

        List<RankItem> rankItems = rankCardComponent.queryCardScoreBalance(actId, 1, RoleType.PWTUAN.getValue(), uid);
        return Response.success(rankItems);
    }

    @RequestMapping("/hourlyBestCP")
    public Response hourlyBestCP(long actId, String day) {
        Map<Object, Object> cpMap = trueLoveCPComponent.queryHourlyCPByDay(actId, day);
        List<BestCPItem> items = new ArrayList<>();
        List<Long> uids = new ArrayList<>();
        List<Long> anchorUids = new ArrayList<>();
        try {
            for (Map.Entry<Object, Object> obj : cpMap.entrySet()) {
                BestCPItem item = new BestCPItem();
                String value = String.valueOf(obj.getValue());
                String[] pas = value.split("\\|");
                item.setScore(Convert.toLong(pas[2]));
                String time = String.valueOf(obj.getKey());
                String hour = time.substring(time.length() - 2);
                item.setTime(hour);
                long bossUid = Convert.toLong(pas[0]);
                uids.add(bossUid);

                long ppUid = Convert.toLong(pas[1]);
                anchorUids.add(ppUid);
                item.setPlayerUid(bossUid);
                item.setAnchorUid(ppUid);
                items.add(item);
            }
            uids.addAll(anchorUids);
            Map<Long, WebdbUserInfo> userInfoMap = commonService.batchYyUserInfo(uids);

            for (BestCPItem item : items) {
                WebdbUserInfo anchorInfo = userInfoMap.get(item.getAnchorUid());
                if (anchorInfo != null) {
                    item.setAnchorNick(anchorInfo.getNick());
                    item.setAnchorLogo(WebdbUtils.getLogo(anchorInfo));
                }
                WebdbUserInfo userInfo = userInfoMap.get(item.getPlayerUid());
                if (userInfo != null) {
                    item.setPlayerNick(userInfo.getNick());
                    item.setPlayerLogo(WebdbUtils.getLogo(userInfo));
                }
            }
        } catch (Exception e) {
            log.error("hourlyBestCP error: {}", e.getMessage(), e);
        }

        items.sort(new Comparator<BestCPItem>() {
            @Override
            public int compare(BestCPItem o1, BestCPItem o2) {
                return Convert.toInt(o2.getTime()) - Convert.toInt(o1.getTime());
            }
        });
        return Response.success(items);
    }

    @RequestMapping("/lastHourlyBestCp")
    public Response lastHourlyBestCP(long actId) {
        JSONObject data = new JSONObject();
        String cp = trueLoveCPComponent.lastBestCP(actId);
        if (StringUtil.isBlank(cp)) {
            return Response.ok();
        }
        String[] pas = cp.split("\\|");
        long player = Convert.toLong(pas[0], 0);

        UserBaseInfo playerInfo = commonService.getUserInfo(player, false);
        data.put("player", playerInfo);

        long anchorUid = Convert.toLong(pas[1], 0);
        UserBaseInfo anchorInfo = commonService.getUserInfo(anchorUid, false);
        data.put("anchor", anchorInfo);
        return Response.success(data);
    }


    /**
     * 扣除领取资格领取奖励
     */
    @RequestMapping("/receiveAwardComponent/releaseAward")
    public Response<String> releaseAward(HttpServletRequest req, HttpServletResponse resp,
                                         long actId, long cmptUseInx, String awardCode, int partialRelease) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }
        try {
            receiveAwardComponent.releaseAward(actId, cmptUseInx, uid, awardCode, partialRelease);
            return Response.ok();
        } catch (GameEcologyBusinessException e) {
            log.warn("releaseAward warn,actId:{},uid:{},cmptUseInx:{},awardCode:{}，partialRelease:{},e:{}", actId, uid, cmptUseInx, awardCode, partialRelease, e.getMessage());
            return Response.fail(e.getCode(), e.getMessage());

        } catch (Throwable e) {
            log.error("releaseAward error,actId:{},uid:{},cmptUseInx:{},awardCode:{}，partialRelease:{},e:{}", actId, uid, cmptUseInx, awardCode, partialRelease, e.getMessage(), e);
            return Response.fail(10000, e.getMessage());
        }
    }

    /**
     * 查询领取状态
     */
    @RequestMapping("/receiveAwardComponent/queryAwardRecordStatus")
    public Response<List<ReceiveAwardInfo>> queryAwardRecordStatus(HttpServletRequest req, HttpServletResponse resp,
                                                                   long actId, long cmptUseInx) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }
        try {
            List<ReceiveAwardInfo> result = receiveAwardComponent.queryAwardRecordStatus(actId, uid, cmptUseInx);
            return Response.success(result);
        } catch (Throwable e) {
            log.error("queryAwardRecordStatus,actId:{},uid:{},cmptUseInx:{},e:{}", actId, uid, cmptUseInx, e.getMessage(), e);
            return Response.fail(10000, e.getMessage());
        }
    }

    /**
     * 查询奖励记录
     */
    @RequestMapping("/receiveAwardComponent/queryAwardRecord")
    public Response<List<ActTaskAwardRecord>> queryAwardRecord(HttpServletRequest req, HttpServletResponse resp,
                                                               long actId, long cmptUseInx) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }
        try {
            return Response.success(receiveAwardComponent.queryAwardRecord(actId, uid, cmptUseInx));
        } catch (Throwable e) {
            log.error("releaseAward error,actId:{},uid:{},cmptUseInx:{}，partialRelease:{},e:{}", actId, uid, cmptUseInx, e.getMessage(), e);
            return Response.fail(10000, e.getMessage());
        }
    }

    /**
     * 随机获取陪玩约会插件频道
     */
    @RequestMapping("/getRadomPeiwanPluginChannel")
    public Response<List<ChannelInfoVo>> getRadomPeiwanPluginChannel() {
        return Response.success(zhuiWanPrizeIssueServiceClient.getRadomPeiwanPluginChannel(10));
    }

    /**
     * 查询奖励记录
     *
     * @param actId
     * @param index
     * @return
     */
    @RequestMapping("/zw_exchange/queryQuotaBalance")
    public Response<String> queryQuotaBalance(long actId, long index) {
        return zhuiwanDatingExchangeComponent.queryQuotaBalance(actId, index);
    }

    /**
     * 查询奖励记录
     *
     * @param actId
     * @param index
     * @return
     */
    @RequestMapping("/zw_exchange/queryUserInfo")
    public Response<String> queryUserInfo(HttpServletRequest req, HttpServletResponse resp,
                                          long actId, long index) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }

        return zhuiwanDatingExchangeComponent.queryUserInfo(actId, index, uid);
    }

    /**
     * 查询奖励记录
     *
     * @param actId
     * @param index
     * @return
     */
    @RequestMapping("/zw_exchange/exchange")
    public Response exchange(HttpServletRequest req, HttpServletResponse resp,
                             long actId, long index, int count, int currencyType, String authCode) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }
        authCode = Convert.toString(authCode);

        return zhuiwanDatingExchangeComponent.exchange(actId, index, uid, count, currencyType, authCode);
    }

    /**
     * 查询奖励记录
     *
     * @param actId
     * @param index
     * @return
     */
    @RequestMapping("/zw_exchange/queryRecord")
    public Response queryRecord(HttpServletRequest req, HttpServletResponse resp,
                                long actId, long index, Integer count) {

        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(-1, "未登录");
        }
        List<JSONObject> records = zhuiwanDatingExchangeComponent.queryUserRecord(actId, index, uid, count);
        return Response.success(records);
    }

    @RequestMapping("/anchorBonusPkComponent/queryAllBonusRecord")
    public Response queryAllBonusRecord(long actId, long rankId) {
        Set<ZSetOperations.TypedTuple<String>> typedTuples = anchorBonusPkComponent.queryAllBonusRecord(actId, rankId);
        return Response.success(typedTuples);
    }

    @GetMapping("/giftPackageV3/giftPackageStatus")
    public Response<JSONObject> giftPackageStatusV3(HttpServletRequest req, HttpServletResponse resp,
                                                    @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                                    @NotNull @Min(1) @RequestParam(name = "index") Integer index) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return giftPackageQRCodePayComponent.giftPackageStatus(actId, index, uid);

    }

    @GetMapping("/giftPackageV3/requestBuyGiftPackage")
    public Response<Map<String, Object>> requestBuyGiftPackage(HttpServletRequest req, HttpServletResponse resp,
                                                               @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                                               @NotNull @Min(1) @RequestParam(name = "index") Integer index,
                                                               int payWay) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        String ip = RequestUtil.getRealIp(req);
        return giftPackageQRCodePayComponent.requestBuyGiftPackage(actId, index, uid, payWay, ip);
    }

    @GetMapping("/giftPackageV3/queryPayStatus")
    public Response<Map<String, Object>> queryPayStatus(HttpServletRequest req, HttpServletResponse resp,
                                                        @NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                                        @NotNull @Min(1) @RequestParam(name = "index") Integer index,
                                                        long orderId) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return giftPackageQRCodePayComponent.queryPayStatus(actId, index, orderId);
    }

    @GetMapping("/milestone/getPwChannelAllTuan")
    public Response<List<MemberInfo>> getPwChannelAllTuan(@NotNull @Min(1) @RequestParam(name = "actId") Long actId, Long sid) {
        List<MemberInfo> pwChannelAllTuan = milestoneV3Component.getPwChannelAllTuan(actId, sid);
        return Response.success(pwChannelAllTuan);
    }

    @GetMapping("/milestone/getPwMilestoneStatus")
    public Response<JSONObject> getPwMilestoneStatus(@NotNull @Min(1) @RequestParam(name = "actId") Long actId,
                                                     @NotNull @Min(1) @RequestParam(name = "teamId") Long teamId) {

        JSONObject pwMilestoneStatus = milestoneV2Component.getPwMilestoneStatus(actId, teamId);
        return Response.success(pwMilestoneStatus);
    }

    @GetMapping("/sunshine/queryAnchorTaskScore")
    public Response<AnchorTaskScoreInfo> queryAnchorTaskScore(@RequestParam("actId") long actId, @RequestParam("uid") long uid,
                                                              @RequestParam(value = "taskTime", required = false) String taskTime, @RequestParam(name = "index", defaultValue = "1") long index) {
        if (StringUtil.isBlank(taskTime)) {
            Date now = commonService.getNow(actId);
            taskTime = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        }
        AnchorTaskScoreInfo info = sunshineTaskComponent.queryAnchorTaskScore(actId, uid, taskTime, index);
        UserBaseInfo userInfo = commonService.getUserInfo(uid, true);
        userInfo.setLogo(userInfo.getLogo());
        userInfo.setNick(userInfo.getNick());
        return Response.success(info);
    }

    @GetMapping("/sunshine/queryAnchorTaskAward")
    public Response<List<String>> queryAnchorTaskAward(@RequestParam("actId") long actId, @RequestParam("uid") long uid, Long index) {
        if (index == null) {
            index = 1L;
        }
        List<String> list = sunshineTaskComponent.queryAnchorFinishTaskAward(actId, index, uid);
        return Response.success(list);
    }

    @GetMapping("/sunshine/getTotalPool")
    public Response<Long> getTotalPool(@RequestParam("actId") long actId, @RequestParam("index") long index) {
        return Response.success(sunshineTaskComponent.getTotalPoolBalance(actId, index));
    }


    // @Autowired
    private SvcSDKService svcSDKService;

    //todo 记得删除
    // @GetMapping("/channelArena/clickAward")
    public Response clickAward(long actId, long uid, long sid, int level) throws TException {
        Clock clock = new Clock();
        GameecologyActivity.GameEcologyMsg.Builder geMsgBuilder = GameecologyActivity.GameEcologyMsg.newBuilder();
        geMsgBuilder.setUri(GameecologyActivity.PacketType.kTreasureBoxDrawRespUri_VALUE);
        GameecologyActivity.TreasureBoxDrawResp.Builder rspBuilder = GameecologyActivity.TreasureBoxDrawResp.newBuilder()
                .setActId(actId)
                .setLevel(level)
                .setRetCode(99)
                .setRetMsg("系统繁忙，请稍后再试！");
        String award = "未中奖";
        try {
            log.info("treasureBoxDraw uid:{},level:{}, channel:{}", uid, level, sid);
            award = channelArenaComponent.clickAward(actId, 1, uid, sid, level);
            rspBuilder.setRetCode(0);
            rspBuilder.setRetMsg(award);
            GameecologyActivity.GameEcologyMsg rspMsg = geMsgBuilder.setTreasureBoxDrawResp(rspBuilder).build();
            svcSDKService.unicastUid(uid, rspMsg);
            log.info("superWinnerDraw done@uid:{} sid:{} ssid:{} ip:{} req:{} {}", uid, sid, clock.tag());
        } catch (GameEcologyBusinessException e) {  //未抽中
            rspBuilder.setRetCode(e.getCode());
            rspBuilder.setRetMsg(e.getMessage());
            GameecologyActivity.GameEcologyMsg rspMsg = geMsgBuilder.setTreasureBoxDrawResp(rspBuilder).build();
            svcSDKService.unicastUid(uid, rspMsg);
            log.warn("superWinnerDraw GameEcologyBusinessException,rsp:{}", rspMsg.toString(), e);
        }
        return Response.success(award);
    }


    /**
     * 查询新年祝福余额
     */
    @GetMapping("/wishingBottle/queryBalance")
    public Response<Map<Object, Object>> queryWishingBalance(HttpServletRequest req, HttpServletResponse resp, long actId, long index) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        return Response.success(wishingBottleComponent.queryUserBalance(actId, index, uid));
    }

    /**
     * 查询某天获得瓜分资格的人数
     */
    @GetMapping("/wishingBottle/queryCarveAmount")
    public Response<Long> queryCarveAmount(HttpServletRequest req, HttpServletResponse resp, long actId, long index, String dateCode) {
        long uid = getLoginYYUid(req, resp);
        if (uid == 0) {
            return Response.fail(1, "未登录");
        }
        if (StringUtil.isBlank(dateCode)) {
            dateCode = DateUtil.format(commonService.getNow(actId), DateUtil.PATTERN_TYPE2);
        }
        return Response.success(wishingBottleComponent.queryCarveAmount(actId, index, dateCode));
    }

    //宝贝月度活动
    @RequestMapping("/playerAward")
    public Response playerAward(HttpServletRequest req, HttpServletResponse resp) {
        if (isClosed(req)) {
            return Response.fail(-99, MSG_CLOSE_HINT);
        }
        long yyUid = getLoginYYUid(req, resp);
        long actId = Convert.toLong(req.getParameter("actId"));
        GamebabyMonthActComponentAttr attr = gamebabyMonthActComponent.getComponentAttr(actId, 400);
        return gamebabyMonthActComponent.playerAward(yyUid, attr);
    }

    @RequestMapping("/lottery")
    public Response lottery(HttpServletRequest req, HttpServletResponse resp,long actId, int boxType, int lotteryType) {
        if (isClosed(req)) {
            return Response.fail(-99, MSG_CLOSE_HINT);
        }
        int status = actInfoService.actTimeStatus(actId);
        if (status > 0) {
            return Response.fail(3, "活动已结束!");
        }
        if (status < 0) {
            return Response.fail(3, "活动未开始!");
        }

        try {
            long yyUid = getLoginYYUid(req, resp);
            log.info("lottery uid:{}", yyUid);
            GamebabyMonthActComponentAttr attr = gamebabyMonthActComponent.getComponentAttr(actId, 400);
            BatchLotteryResult lottery = gamebabyMonthActComponent.lottery(yyUid, boxType, lotteryType, attr);
            //null-> 抽奖机会不足
            if (lottery == null) {
                return Response.fail(2, "幸运之钥不足");
            }
            return gamebabyMonthActComponent.lotteryAward(lottery, attr);
        } catch (Exception e) {
            log.error("lottery exception@uid:{}, err:{} " , e.getMessage() , ExceptionUtils.getStackTrace(e));
            return new Response<>(Code.E_SYS_BUSY);
        }

    }

    //荣耀水晶数据信息 查询
    @RequestMapping(value = "/queryMyGloryCrystal")
    public Response queryMyGloryCrystal(HttpServletRequest req, HttpServletResponse resp, long actId, Long uid) {
        if (isClosed(req) || uid == null) {
            return Response.fail(-99, MSG_CLOSE_HINT);
        }
        Clock clock = new Clock();
        try {
            //long yyUid = getLoginYYUid(req, resp);
            GamebabyMonthActComponentAttr attr = gamebabyMonthActComponent.getComponentAttr(actId, 400);
            Map<String, Object> result = gamebabyMonthActComponent.queryCryInfo( uid, attr);
            return Response.success(result);
        } catch (Exception e) {
            log.error("queryMyGloryCrystal exception@uid:{}, err:{} {}" , e.getMessage(), clock.tag(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    @RequestMapping("/playerLuckKey")
    public Response playerLuckKey(HttpServletRequest request, HttpServletResponse response) {
        if (isClosed(request)) {
            return Response.fail(-99, MSG_CLOSE_HINT);
        }
        long yyUid = getLoginYYUid(request, response);
        log.info("playerLuckKey uid:{}", yyUid);
        if (yyUid == 0) {
            return Response.fail(-1, "请登录后操作");
        }
        long actId = Convert.toLong(request.getParameter("actId"));
        return Response.success(gamebabyMonthActComponent.playerLuckKey(yyUid, actId));
    }

    @Autowired
    private TaskContributeLotteryComponent taskContributeLotteryComponent;

    @Autowired
    private CacheService cacheService;


    @RequestMapping("/fixGuoQingData")
    public Response<String> fixData(HttpServletRequest request, HttpServletResponse response, long actId, long index, int write, String fixDataKey) throws Exception {
        TaskContributeLotteryComponentAttr attr = taskContributeLotteryComponent.getComponentAttr(actId, index);
        List<String> contents = readFromFile(cacheService.getActAttrValue(actId, fixDataKey));
        if (write != Const.ONE) {
            return Response.ok(JSON.toJSONString(contents));
        }
        String keyPre = "fixData:";
        if (!actRedisGroupDao.setNX(redisConfigManager.getGroupCode(actId), Const.addActivityPrefix(actId, keyPre + fixDataKey), System.currentTimeMillis() + "")) {
            return Response.ok("already set:" + fixDataKey);
        }
        int count = 0;
        Set<String> seqs = Sets.newHashSet();
        for (String event : contents) {
            if (!StringUtil.isNotBlank(event)) {
                continue;
            }
            RankingScoreChanged rankingScoreChanged = JSON.parseObject(event, RankingScoreChanged.class);
            if (seqs.contains(rankingScoreChanged.getSeq())) {
                log.warn("seq dup:" + rankingScoreChanged.getSeq());
                continue;
            }
            seqs.add(rankingScoreChanged.getSeq());
            count++;
            taskContributeLotteryComponent.onRankingScoreChanged(rankingScoreChanged, attr);
        }
        return Response.ok("fix size:" + count);
    }

    private List<String> readFromFile(String urlContent) {
        List<String> result = Lists.newArrayList();
        try {
            URL url = new URL(urlContent);
            BufferedReader reader = new BufferedReader(new InputStreamReader(url.openStream()));

            // 读取URL中的文本内容
            String line;
            while ((line = reader.readLine()) != null) {
                result.add(line);
            }
            // 关闭输入流
            reader.close();
        } catch (IOException e) {
            log.error("read error", e);
        }


        return result;
    }

}
