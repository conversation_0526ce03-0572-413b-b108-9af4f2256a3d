package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-21 14:22
 **/
@Getter
@Setter
public class PuzzleComponentAttr extends ComponentAttr {
    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    protected int busiId;

    @ComponentAttrField(labelText = "时间分榜", dropDownSourceBeanClass = TimeKeySource.class)
    protected long timeKey;

    @ComponentAttrField(labelText = "cp榜单id")
    private long rankId;

    @ComponentAttrField(labelText = "cp阶段Id")
    private long phaseId;

    @ComponentAttrField(labelText = "任务阈值(显示用)", remark = ",多个逗号分隔", subFields = {
            @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)
    })
    private List<Long> missions = Lists.newArrayList();

    @ComponentAttrField(labelText = "必得未获得碎片的任务等级",remark = "从1开始",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> sendNotObtainedTaskLevel = Sets.newHashSet();

    @ComponentAttrField(labelText = "碎片抽奖奖池Id")
    private long pieceLotteryTaskId;

    /**
     * 抽中的碎片packageId，对应的实际发奖奖励
     */
    @ComponentAttrField(labelText = "碎片发奖奖励", remark = "抽中的碎片packageId，对应的实际发奖奖励",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "抽中的packageId"),
                    @SubField(fieldName = Constant.VALUE, type = PieceAward.class, labelText = "value")
            })
    private Map<Long, PieceAward> tAwardPkgPieces = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "等级数", remark = "对轮次取模，定义当前拼图的等级，默认值：1表示只有一个等级")
    private int levelMod = 1;

    /**
     * 集齐部分拼图奖励
     */
    @ComponentAttrField(labelText = "集齐部分拼图奖励", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "拼图等级", remark = "轮次对等级数取模后的等级数，第一个等级是：0而不是1"),
                    @SubField(fieldName = Constant.KEY2, type = String.class, labelText = "部分拼图编码", remark = "例如：123"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "value")
            })
    private Map<Integer, Map<String, AwardAttrConfig>> somePartsAward = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "发奖总限额")
    private Long cpAwardLimit;

    /**
     * 集齐全部拼图奖励
     */
    @ComponentAttrField(labelText = "集齐全部拼图奖励", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "拼图等级", remark = "轮次对等级数取模后的等级数，第一个等级是：0而不是1"),
                    @SubField(fieldName = Constant.KEY2, type = String.class, labelText = "发奖类型", remark = "1==奖池充足时候发奖配置 -1奖池不足时的发奖配置"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "value")
            })
    private Map<Integer, Map<Integer, AwardAttrConfig>> collectAllAward = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "最后CP组件索引")
    protected long latestCpIndex;

    @ComponentAttrField(labelText = "口令抽奖组件索引", subFields = {
            @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "拼图等级", remark = "轮次对等级数取模后的等级数，第一个等级是：0而不是1"),
            @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "口令抽奖组件索引")
    })
    protected Map<Integer, Long> watchwordLotteryIndexes;

    @ComponentAttrField(labelText = "口令抽奖时长")
    protected Duration watchwordLotteryDuration;

    @ComponentAttrField(labelText = "广播模板过滤弹幕游戏模板")
    private boolean filterDanmaku = true;

    @ComponentAttrField(labelText = "mp4 key配置", remark = "",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "key"),
                    @SubField(fieldName = Constant.VALUE, type = String.class, labelText = "value")
            })
    private Map<String, String> mp4LayerExtKeyValues = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "mp4特效url", propType = ComponentAttrCollector.PropType.RESOURCE)
    private String mp4Url;

    @ComponentAttrField(labelText = "优先级", remark = "特效排队显示优先级，值越小，优先级越高；目前默认全屏礼物特效的优先级为999，如果优先级低于全屏礼物特效优先级，需要该值大于999")
    private int mp4Level;

    public Set<String> getAllPieceCodes() {
        return tAwardPkgPieces.values().stream().map(PieceAward::getPieceCode).collect(Collectors.toSet());
    }

    @Data
    public static class PieceAward {
        @ComponentAttrField(labelText = "奖励奖池Id")
        protected Long tAwardTskId;

        @ComponentAttrField(labelText = "奖励奖包Id", remark = "填0，则是抽奖")
        protected Long tAwardPkgId;

        @ComponentAttrField(labelText = "碎片编码")
        protected String pieceCode;
    }
}
