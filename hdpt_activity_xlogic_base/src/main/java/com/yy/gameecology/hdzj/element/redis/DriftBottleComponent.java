package com.yy.gameecology.hdzj.element.redis;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.driftbottle.CpGiftDetailVo;
import com.yy.gameecology.activity.bean.driftbottle.CpNoticeInfoVo;
import com.yy.gameecology.activity.bean.driftbottle.DriftBottleGiftVo;
import com.yy.gameecology.activity.bean.driftbottle.RoadMapCpInfoVo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rank.RankItemUserAnchor;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.activity.service.rankext.RankExtHandler;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;



/**
 * 聊天室漂流瓶玩法
 */
@UseRedisStore
@Slf4j
@RestController
@RequestMapping("/cmpt/driftBottle")
@Component
public class DriftBottleComponent extends BaseActComponent<DriftBottleComponentAttr> implements RankExtHandler, LayerSupport {




    /**
     * 路线图
     */
    private static final String ROAD_MAP_CP_ZSET_KEY = "road_map_cp_zset:%s";

    /**
     * 漂流瓶cp当前公里
     */
    private static final String DRIFT_BOTTLE_TOTAL_STEP = "drift_bottle_total_step";

    /**
     * 漂流瓶每个主持最新cp
     */
    private static final String DRIFT_BOTTLE_LATEST_CP = "drift_bottle_latest_cp:%s:%d";

    /**
     * 漂流瓶最强CP
     */
    private static final String DRIFT_BOTTLE_TOPONE_STATISTIC = "drift_bottle_topone_statistics";

    /**
     * 漂流瓶幸运CP
     */
    private static final String DRIFT_BOTTLE_LUCKY_STATISTIC  = "drift_bottle_lucky_statistics";

    /**
     * 发放礼物总金额
     */
    private static final String DRIFT_BOTTLE_TOTAL_AWARD_STATISTIC = "drift_bottle_total_award_statistics";

    /**
     * 最强CP未弹窗key
     */
    private static final String TOP_ONE_ENTER_CHANNEL_NOTICE = "top_one_enter_channel_notice";
    private static final String LAETST_ENTER_CHANNEL_NOTICE = "latest_enter_channel_notice";

    private static final String TOP_ONE_HAS_NOTICE = "top_one_has_notice:%d";
    private static final String LAETST_HAS_NOTICE = "latest_has_notice:%d";

    public static final String CP_LAST_ROOM = "cp_last_room:%s";



    private static final long TOP_ONE = 1;

    private static final long TOPONE_BRO_BANNER_ID = 5094001L;

    private static final long LATEST_BRO_BANNER_ID = 5094002L;

    private static final String TOPONE_NOTICE_TYPE = "5094_top1";
    private static final String LATEST_NOTICE_TYPE = "5094_latest";
    private static final int TOP_APP_BANNER_CONTENT_TYPE = 6;



    private static final String TOP_ONE_NOTICE_UNICAST_TEXT = "恭喜成为%s场最强CP，平分%s元礼物奖励！";
    private static final String LATEST_NOTICE_UNICAST_TEXT = "恭喜成为%s场幸运CP，获得%s进场秀1天奖励！";




    @Autowired
    protected WebdbThriftClient webdbThriftClient;


    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;


    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private RetryTool retryTool;


    @Override
    public Long getComponentId() {
        return ComponentId.DRIFT_BOTTLE;
    }


    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, DriftBottleComponentAttr attr) {
        long uid = event.getUid(), actId = attr.getActId(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        log.info("DriftBottleComponent onUserEnterTemplate uid:{},actId:{},extJson:{},sid:{}",uid,attr.getActId(),extJson,sid);
        boolean actEnterEvent = StringUtil.isNotBlank(extJson) && extJson.contains(String.valueOf(attr.getActId()));
        if(!actEnterEvent) {
            log.warn("DriftBottleComponent  not this actId UserEnterTemplateEvent uid:{}",uid);
            return;
        }

        Date now = commonService.getNow(attr.getActId());

        String topKey = makeKey(attr, TOP_ONE_ENTER_CHANNEL_NOTICE);
        String redisGroup = getRedisGroupCode(attr.getActId());
        String value = actRedisDao.hget(redisGroup,topKey,String.valueOf(uid));
        if(StringUtils.isNotEmpty(value)){
            CpNoticeInfoVo json = JsonUtil.toObject(value,CpNoticeInfoVo.class);
            String hasNoticeKey = makeKey(attr,String.format(TOP_ONE_HAS_NOTICE,uid));
            if(actRedisDao.hsetnx(redisGroup,hasNoticeKey,json.getAwardTime(),DateUtil.format(now,DateUtil.PATTERN_TYPE1))){
                log.info("onUserEnterTemplate commonNoticeUnicast actId:{},uid:{},noticeType:{}",actId,uid,TOPONE_NOTICE_TYPE);
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, uid);
            }
        }

        String latestKey = makeKey(attr, LAETST_ENTER_CHANNEL_NOTICE);
        String latestValue = actRedisDao.hget(redisGroup,latestKey,String.valueOf(uid));
        if(StringUtils.isNotEmpty(latestValue)){
            CpNoticeInfoVo json = JsonUtil.toObject(latestValue,CpNoticeInfoVo.class);
            String hasNoticeKey = makeKey(attr,String.format(LAETST_HAS_NOTICE,uid));
            if(actRedisDao.hsetnx(redisGroup,hasNoticeKey,json.getAwardTime(),DateUtil.format(now,DateUtil.PATTERN_TYPE1))){
                log.info("onUserEnterTemplate commonNoticeUnicast actId:{},uid:{},noticeType:{}",actId,uid,LATEST_NOTICE_TYPE);
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), LATEST_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, uid);
            }
        }
    }

    /**
     * 榜单结算
     */
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void handlePhaseTimeEnd(PhaseTimeEnd event, DriftBottleComponentAttr attr) {
        log.info("DriftBottleComponent  handlePhaseTimeEnd event:{},attr:{}", JsonUtil.toJson(event), JsonUtil.toJson(attr));
        if (event.getRankId() != attr.getRankId()) {
            return;
        }
        if (event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));

        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), timeCode, TOP_ONE, null);
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("DriftBottleComponent handlePhaseTimeEnd empty rank,actId:{},rankId:{},phaseId:{},timeCode:{}", event.getActId(), event.getRankId(), event.getPhaseId(), timeCode);
            return;
        }

        //最强CP
        releaseTopOneAward(attr, ranks.get(0), timeCode,event);

        //幸运CP
        releaseLatestCpAward(attr,timeCode,event);

    }

    private void releaseLatestCpAward(DriftBottleComponentAttr attr, String timeCode,PhaseTimeEnd event) {
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String groupCode = getRedisGroupCode(attr.getActId());


        String roadMapZsetKey = getRoadMapCpZsetKey(attr, timeCode);
        Set<ZSetOperations.TypedTuple<String>> roadMapZset  = actRedisDao.zrevRange(groupCode,roadMapZsetKey,1);

        if (CollectionUtils.isNotEmpty(roadMapZset)) {
            RoadMapCpInfoVo record = JSON.parseObject(roadMapZset.stream().findFirst().get().getValue(), RoadMapCpInfoVo.class);
            long userUid = record.getUserUid();
            long babyUid = record.getBabyUid();
            String welfareTime = DateUtil.format(commonService.getNow(attr.getActId()));
            String userHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_latestcp_"+ timeCode + "_" + userUid ));
            String babyHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_latestcp_"+ timeCode + "_" + babyUid ));
            BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), userUid, attr.getLatestCpTaskId(), 1, attr.getLatestCpPackageId(), userHashSeq, 2);
            boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if(!suc) {
                // 需要人工介入处理
                log.error("DriftBottleComponent user releaseLatestCpAward error,  actId:{}, uid:{}, seq:{}  ret:{}", attr.getActId(), userUid, userHashSeq, batchWelfareResult);
            }

            if(userUid!=babyUid){
                batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), babyUid, attr.getLatestCpTaskId(), 1, attr.getLatestCpPackageId(), babyHashSeq, 2);
                suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
                if(!suc) {
                    // 需要人工介入处理
                    log.error("DriftBottleComponent baby releaseLatestCpAward error,  actId:{}, uid:{}, seq:{}  ret:{}", attr.getActId(), babyUid, babyHashSeq, batchWelfareResult);
                }
            }

            //数据统计-保存幸运CP
            saveLuckyCpInfo( attr, userUid, babyUid, timeCode, record.getCureentStep());


            //幸运CP顶部广播
            String bannerSeq = "seq:latestbanner:" + seq;
            RetryTool.withRetryCheck(attr.getActId(), bannerSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
                broLatestCpBanner(userUid,babyUid,attr,seq,timeCode);
            });

            //幸运CP弹窗
            String noticeSeq = "seq:latestnotice:" + seq;
            retryTool.asyWithRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
                try {
                    Thread.sleep(7000);
                    unicastLuckyCpNotice(timeCode,userUid,babyUid,attr);
                } catch (InterruptedException e) {
                    log.error("delay send unicast interrupted:", e);
                }

            },Const.GENERAL_POOL);

        }

    }

    private void unicastLuckyCpNotice(String timeCode,long userUid,long babyUid,DriftBottleComponentAttr attr) {
        //幸运CP弹窗
        CpNoticeInfoVo json = new CpNoticeInfoVo();
        String noticeTime = null;
        try {
            noticeTime = DateUtil.format(DateUtils.parseDate(timeCode,DateUtil.PATTERN_TYPE9),DateUtil.PATTERN_TYPE11);
        } catch (ParseException e) {
            log.error("unicastLuckyCpNotice timeCode:{},userUid:{},babyUid:{},exception:{}",timeCode,userUid,babyUid,e.getMessage(),e);
        }

        String awardTexts = String.format(LATEST_NOTICE_UNICAST_TEXT,noticeTime,attr.getLatestCpShowName());
        json.setAwardTexts(awardTexts);
        json.setAwardLogo(attr.getLatestCpShowUrl());
        json.setAwardTime(timeCode);
        log.info("releaseLatestCpAward commonNoticeUnicast actId:{},timeCode:{},userUid:{},babyUid:{}",attr.getActId(),timeCode,userUid,babyUid);
        String key = makeKey(attr, LAETST_ENTER_CHANNEL_NOTICE);
        UserCurrentChannel userChannel = commonService.getUserCurrentChannel(userUid);
        UserCurrentChannel babyChannel = commonService.getUserCurrentChannel(babyUid);
        if(userChannel!=null){
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), LATEST_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, userUid);
        }else{
            log.warn("releaseLatestCpAward userUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),userUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(userUid),JsonUtil.toJson(json));
        }

        if(babyChannel!=null) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), LATEST_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, babyUid);
        }else{
            log.warn("releaseLatestCpAward babyUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),babyUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(babyUid),JsonUtil.toJson(json));
        }

    }

    private void broLatestCpBanner(long userUid ,long babyUid, DriftBottleComponentAttr attr,String seq,String timeCode) {
        Set<Long> uids = new HashSet<>();
        uids.add(userUid);
        uids.add(babyUid);

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo babyInfoVo = userInfos.get(babyUid);
        ext.put("userUid",userUid);
        ext.put("babyUid",babyUid);
        ext.put("showLogo",attr.getLatestCpShowUrl());
        ext.put("showAmount","1天");
        if(userInfoVo!=null ){
            ext.put("userLogo",userInfoVo.getAvatarUrl());
            ext.put("userNick",userInfoVo.getNick());
        }

        if(babyInfoVo!=null) {
            ext.put("babyLogo",babyInfoVo.getAvatarUrl());
            ext.put("babyNick",babyInfoVo.getNick());
        }
        ext.put("nickExtUsers",JsonUtil.toJson(multiNickUsers));

        String cpLastRoom = makeKey(attr, String.format(CP_LAST_ROOM, timeCode));
        String member = userUid + "|" + babyUid;
        String sidSsidStr = actRedisDao.hget(getRedisGroupCode(attr.getActId()), cpLastRoom,member);
        long sid;
        long ssid;
        if(sidSsidStr != null) {
            String[] sidSsid = sidSsidStr.split("_");
            sid = Convert.toLong(sidSsid[0]);
            ssid = Convert.toLong(sidSsid[1]);
        } else {
            ssid = 0L;
            sid = 0L;
        }

        ext.put("sid",sid);
        ext.put("ssid",ssid);

        log.info("DriftBottleComponent broLatestCpBanner commonBannerBroadcast ext:{}",JsonUtil.toJson(ext));
        commonBroadCastService.commonBannerBroadcast(0, 0, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), 4
                , attr.getActId(), 0L,0L, LATEST_BRO_BANNER_ID, 0L, ext);


    }




    private void releaseTopOneAward(DriftBottleComponentAttr attr, Rank rank, String timeCode,PhaseTimeEnd event) {
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String[] members = rank.getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long babyUid = Long.parseLong(members[1]);

        //获取CP总里程
        long totalStep = getTotalStep(attr, timeCode);
        if (totalStep <= 0) {
            log.error("releaseTopOneAward totalStep is 0,actId:{},timeCode:{},members:{}",attr.getActId(),timeCode,rank.getMember());
            return;
        }

        //单轮次礼物有上限
        long exchangeGiftStep = exchangeGiftStep(attr,totalStep);

        DriftBottleGiftVo awardAConfig = calAwardConfig(attr, exchangeGiftStep);
        if (awardAConfig == null) {
            log.error("releaseTopOneAward calAwardConfig is null,actId:{},timeCode:{},members:{}",attr.getActId(),timeCode,rank.getMember());
            return;
        }

        long multipleStep = exchangeGiftStep / awardAConfig.getStep();
        if (multipleStep <= 0) {
            log.error("releaseTopOneAward multipleStep <=0 ,actId:{},timeCode:{},members:{}",attr.getActId(),timeCode,rank.getMember());
            return;
        }

        long awardACount = multipleStep * awardAConfig.getStep() * attr.getStepExchangeGiftUnit() / awardAConfig.getGiftPrice();
        long remainderStep = exchangeGiftStep % awardAConfig.getStep();
        DriftBottleGiftVo awardBConfig = null;
        long awardBCount = 0L;
        if (remainderStep > 0) {
            awardBConfig = getMinStepAwardConfig(attr);
            if (awardBConfig == null) {
                log.error("releaseTopOneAward getMinStepAwardConfig is null");
                return;
            }
            awardBCount = remainderStep * attr.getStepExchangeGiftUnit() / awardBConfig.getGiftPrice();
        }

        String welfareTime = DateUtil.format(commonService.getNow(attr.getActId()));
        String userHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + userUid +"_A"));
        BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), userUid, awardAConfig.getTaskId(), (int)awardACount, awardAConfig.getPackageId(), userHashSeq, 2);
        boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
        if(!suc) {
            // 需要人工介入处理
            log.error("DriftBottleComponent user doWelfare A error,  actId:{}, uid:{}, seq:{},taskId:{}, packageId:{},awardACount:{}, ret:{}", attr.getActId(), userUid, userHashSeq,
                    awardAConfig.getTaskId(), awardAConfig.getPackageId(),awardACount, batchWelfareResult);
        }

        String babyHashSeq = null;
        if(userUid!=babyUid){
            babyHashSeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + babyUid +"_A"));
            batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), babyUid, awardAConfig.getTaskId(), (int)awardACount, awardAConfig.getPackageId(), babyHashSeq, 2);
            suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if(!suc) {
                // 需要人工介入处理
                log.error("DriftBottleComponent baby doWelfare A error,  actId:{}, uid:{}, seq:{}, taskId:{}, packageId:{},awardACount:{}, ret:{}", attr.getActId(), babyUid, babyHashSeq,
                        awardAConfig.getTaskId(), awardAConfig.getPackageId(),awardACount,batchWelfareResult);
            }
        }

        if(awardBCount>0) {
            userHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + userUid +"_B"));
            babyHashSeq =  MD5SHAUtil.getMD5(makeKey(attr,seq + "_toponecp_"+ timeCode + "_" + babyUid +"_B"));
            batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), userUid, awardBConfig.getTaskId(), (int)awardBCount, awardBConfig.getPackageId(), userHashSeq, 2);
            suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
            if(!suc) {
                // 需要人工介入处理
                log.error("DriftBottleComponent user doWelfare B error,  actId:{}, uid:{}, seq:{}  ,taskId:{}, packageId:{},awardBCount:{},ret:{}", attr.getActId(), userUid, userHashSeq,
                        awardBConfig.getTaskId(), awardBConfig.getPackageId(),awardBCount,batchWelfareResult);
            }
            if(userUid!=babyUid){
                batchWelfareResult = hdztAwardServiceClient.doWelfare(welfareTime, attr.getBusiId(), babyUid, awardBConfig.getTaskId(), (int)awardBCount, awardBConfig.getPackageId(), babyHashSeq, 2);
                suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
                if(!suc) {
                    // 需要人工介入处理
                    log.error("DriftBottleComponent baby doWelfare B error,  actId:{}, uid:{}, seq:{} ,taskId:{}, packageId:{},awardBCount:{},  ret:{}", attr.getActId(), babyUid, babyHashSeq,
                            awardBConfig.getTaskId(), awardBConfig.getPackageId(),awardBCount, batchWelfareResult);
                }
            }
        }

        log.info("DriftBottleComponent releaseTopOneAward ,actId:{},timeCode:{},userUid:{},babyUid:{},totalStep:{},awardACount:{},awardBCount:{},exchangeGiftStep:{}",attr.getActId(),timeCode,userUid,babyUid,
                totalStep,awardACount,awardBCount,exchangeGiftStep);


        //数据统计-保存最强CP
        saveToponeCpInfo( attr, userUid, babyUid, timeCode, rank.getScore());

        //累计发放金额
        String redisGroup = getRedisGroupCode(attr.getActId());
        String totalAwardKey = makeKey(attr,DRIFT_BOTTLE_TOTAL_AWARD_STATISTIC);
        String totalAwardSeq = makeKey(attr,"seq:"+seq+":"+timeCode);
        actRedisDao.incrValueWithSeq(redisGroup,totalAwardSeq,totalAwardKey,2*exchangeGiftStep*attr.getStepExchangeGiftUnit(),24 * 60 * 60);


        //cp特效
        String bannerSeq = makeKey(attr, "seq:topbanner:" + seq);
        RetryTool.withRetryCheck(attr.getActId(), bannerSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            broTopBanner(userUid,babyUid,attr,seq,totalStep, awardAConfig, exchangeGiftStep);
        });


        //弹窗
        String noticeSeq = makeKey(attr, "seq:topnotice:" + seq);
        retryTool.asyWithRetryCheck(attr.getActId(), noticeSeq, DateUtil.ONE_WEEK_SECONDS, () -> {
            try {
                Thread.sleep(7000);
                unicastTopNotice(timeCode,totalStep,userUid,babyUid,attr,awardAConfig,exchangeGiftStep);
            } catch (InterruptedException e) {
                log.error("delay send unicast interrupted:", e);
            }

        },Const.GENERAL_POOL);

    }

    private long exchangeGiftStep(DriftBottleComponentAttr attr,long totalStep ) {
        long exchangeGiftStep = totalStep;
        if(attr.getExchangeGiftStepLimit()>0 && totalStep>attr.getExchangeGiftStepLimit()){
            exchangeGiftStep = attr.getExchangeGiftStepLimit();
        }
        return exchangeGiftStep;
    }


    private void unicastTopNotice(String timeCode,long totalStep,long userUid,long babyUid,DriftBottleComponentAttr attr,DriftBottleGiftVo awardAConfig,long exchangeGiftStep) {
        CpNoticeInfoVo json = new CpNoticeInfoVo();
        String noticeTime = null;
        try {
            noticeTime = DateUtil.format(DateUtils.parseDate(timeCode,DateUtil.PATTERN_TYPE9),DateUtil.PATTERN_TYPE11);
        } catch (ParseException e) {
            log.error("DriftBottleComponent noticeTime error ,timeCode:{}",timeCode);
        }

        String moneyText = formatMoney(2*exchangeGiftStep*attr.getStepExchangeGiftUnit());
        String awardTexts = String.format(TOP_ONE_NOTICE_UNICAST_TEXT,noticeTime,moneyText);
        json.setAwardTexts(awardTexts);
        json.setAwardLogo(awardAConfig.getGiftLogo());
        json.setAwardTime(timeCode);
        log.info("releaseTopOneAward commonNoticeUnicast actId:{},timeCode:{},userUid:{},babyUid:{},awardTexts:{}",attr.getActId(),timeCode,userUid,babyUid,awardTexts);

        String key = makeKey(attr, TOP_ONE_ENTER_CHANNEL_NOTICE);
        UserCurrentChannel userChannel = commonService.getUserCurrentChannel(userUid);
        UserCurrentChannel babyChannel = commonService.getUserCurrentChannel(babyUid);
        if(userChannel!=null){
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, userUid);
        }else{
            log.warn("releaseTopOneAward userUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),userUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(userUid),JsonUtil.toJson(json));
        }

        if(babyChannel!=null) {
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), TOPONE_NOTICE_TYPE, JsonUtil.toJson(json), StringUtils.EMPTY, babyUid);
        }else{
            log.warn("releaseTopOneAward babyUid not in channel ,actId:{},userUid:{},timeCode:{}",attr.getActId(),babyUid,timeCode);
            actRedisDao.hset(getRedisGroupCode(attr.getActId()),key,String.valueOf(babyUid),JsonUtil.toJson(json));
        }
    }

    private void saveLuckyCpInfo(DriftBottleComponentAttr attr,long userUid,long babyUid,String timeCode,long cureentStep) {
        //数据统计-保存最强CP
        String redisGroup = getRedisGroupCode(attr.getActId());
        String luckyKey = makeKey(attr,DRIFT_BOTTLE_LUCKY_STATISTIC);
        RoadMapCpInfoVo toponeInfo = new RoadMapCpInfoVo();
        toponeInfo.setUserUid(userUid);
        toponeInfo.setBabyUid(babyUid);
        toponeInfo.setCureentStep(cureentStep);
        actRedisDao.hset(redisGroup,luckyKey,timeCode, JsonUtil.toJson(toponeInfo));
    }

    private void saveToponeCpInfo(DriftBottleComponentAttr attr,long userUid,long babyUid,String timeCode,long addStep) {
        //数据统计-保存最强CP
        String redisGroup = getRedisGroupCode(attr.getActId());
        String toponeKey = makeKey(attr,DRIFT_BOTTLE_TOPONE_STATISTIC);
        RoadMapCpInfoVo toponeInfo = new RoadMapCpInfoVo();
        toponeInfo.setUserUid(userUid);
        toponeInfo.setBabyUid(babyUid);
        toponeInfo.setAddStep(addStep);
        actRedisDao.hset(redisGroup,toponeKey,timeCode, JsonUtil.toJson(toponeInfo));
    }

    private  void broTopBanner(long userUid ,long babyUid, DriftBottleComponentAttr attr,String seq,long totalStep,DriftBottleGiftVo awardAConfig,long exchangeGiftStep) {
        //TOP1CP动画
        Set<Long> uids = new HashSet<>();
        uids.add(userUid);
        uids.add(babyUid);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);

        Map<String, Object> ext = Maps.newHashMap();
        UserInfoVo userInfoVo = userInfos.get(userUid);
        UserInfoVo babyInfoVo = userInfos.get(babyUid);
        ext.put("userUid",userUid);
        ext.put("babyUid",babyUid);
        ext.put("svgaUrl",attr.getTop1SvgaUrl());
        if(userInfoVo!=null ){
            ext.put("userLogo",userInfoVo.getAvatarUrl());
            ext.put("userNick",Base64Utils.encodeToString(Convert.toString(userInfoVo.getNick()).getBytes()));
        }

        if(babyInfoVo!=null) {
            ext.put("babyLogo",babyInfoVo.getAvatarUrl());
            ext.put("babyNick",Base64Utils.encodeToString(Convert.toString(babyInfoVo.getNick()).getBytes()));
        }
        ext.put("giftLogo",awardAConfig.getGiftLogo());
        //显示两个人的礼物数量
        ext.put("giftAmount",String.valueOf(2*exchangeGiftStep*attr.getStepExchangeGiftUnit()));

        log.info("DriftBottleComponent commonBannerBroadcast ext:{}",JsonUtil.toJson(ext));
        commonBroadCastService.commonBannerBroadcast(0, 0, 0, com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), 4
                , attr.getActId(), 0L,0L, TOPONE_BRO_BANNER_ID, 0L, ext);


        //TOP1发送app cp特效
        broTopOneCpApp( seq, attr, userInfos, userUid, babyUid, totalStep, awardAConfig,exchangeGiftStep);

    }

    private void broTopOneCpApp(String seq,DriftBottleComponentAttr attr,Map<Long, UserInfoVo> userInfoVoMap,long userUid,long babyUid,long totalStep,DriftBottleGiftVo awardAConfig,
                                long exchangeGiftStep) {

        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        svgaConfig.setDuration(8);
        svgaConfig.setSvgaURL(attr.getAppTop1SvgaUrl());

        List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, userInfoVoMap, userUid,babyUid,exchangeGiftStep);
        svgaConfig.setContentLayers(broContentLayers);

        //头像
        svgaConfig.setImgLayers(buildSvgaImageConfig(attr,userInfoVoMap,userUid,babyUid,awardAConfig));


        final int bcType = FstAppBroadcastType.ALL_TEMPLATE;
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getAppBannerBusiId(),
                bcType, 0, 0, "",
                Lists.newArrayList());

        appBannerEvent.setContentType(TOP_APP_BANNER_CONTENT_TYPE);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setSvgaConfig(svgaConfig);
        appBannerEvent.setUidList(Lists.newArrayList(userUid,babyUid));
        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("broTopOneCpApp app done seq:{}, userUid:{} ,babyUid:{},event:{}",  seq, userUid,babyUid, JSON.toJSONString(appBannerEvent));
    }

    private List<Map<String, String>> buildSvgaImageConfig(DriftBottleComponentAttr attr,Map<Long, UserInfoVo> userInfoVoMap,long userUid,long babyUid,DriftBottleGiftVo awardAConfig) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }

        Map<Integer, String> imageMap = attr.getSvgaImgLayers();
        UserInfoVo userInfoVo = userInfoVoMap.get(userUid);
        UserInfoVo babyInfoVo = userInfoVoMap.get(babyUid);
        for (Integer userType : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String imageKey = imageMap.get(userType);
            String image = StringUtil.EMPTY;
            if(userType==1){
                image = babyInfoVo!=null ? babyInfoVo.getAvatarUrl() :StringUtil.EMPTY ;
            }else if(userType==2){
                image = userInfoVo!=null ? userInfoVo.getAvatarUrl() :StringUtil.EMPTY ;
            }else{
                image = awardAConfig.getGiftLogo();
            }
            broImgLayer.put(imageKey, image);
            broImgLayers.add(broImgLayer);
        }

        return broImgLayers;


    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(DriftBottleComponentAttr attr,Map<Long, UserInfoVo> userInfoVoMap,long userUid,long babyUid,long exchangeGiftStep) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        Map<Integer, BannerSvgaTextConfig> textMap = attr.getSvgaText();

        for (Integer userType : textMap.keySet()) {
            Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
            BannerSvgaTextConfig textConfig = attr.getSvgaText().get(userType);
            if (textConfig == null) {
                continue;
            }
            AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
            //配置动态替换文本
            String text = StringUtils.EMPTY;
            if(userType==3){
                String moneyText = formatMoney(2*exchangeGiftStep*attr.getStepExchangeGiftUnit());
                text = textConfig.getText().replace("{nick}",moneyText + "元");
            }else{
                UserInfoVo userInfoVo = userType==1? userInfoVoMap.get(babyUid) : userInfoVoMap.get(userUid);
                //text = contextReplace(textConfig.getText(),userInfoVo,textConfig.getNameCountLimit());
                text = contextReplace(textConfig.getText(),"{"+userInfoVo.getUid()+":n}",textConfig.getNameCountLimit());
            }

            appBannerSvgaText.setText(text);
            appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
            appBannerSvgaText.setGravity(textConfig.getGravity());
            if (StringUtil.isNotBlank(textConfig.getImages())) {
                appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
            }
            if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
            }
            broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

            if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                broContentLayers.add(broSvgaTextLayer);
            }

        }

        return broContentLayers;

    }

    private String contextReplace(String context,UserInfoVo userInfoVo,int nameCountLimit) {
        if(userInfoVo!=null){
            context = context.replace("{nick}",userInfoVo.getNick());
        }else{
            context = StringUtil.EMPTY;
        }

        return context;
    }

    private String contextReplace(String context,String nick,int nameCountLimit) {
        return  context.replace("{nick}",nick);
    }


    /**
     * 监听榜单变化
     * 更新漂流路线图、最新送礼的cp
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void handleScoreChange(RankingScoreChanged event, DriftBottleComponentAttr attr) {
        log.info("DriftBottleComponent  handleScoreChange event:{},attr:{}", JsonUtil.toJson(event), JsonUtil.toJson(attr));
        if (event.getRankId() != attr.getRankId()) {
            return;
        }
        if (event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        String eventSeq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String[] members = event.getMember().split("\\|");
        long userUid = Long.parseLong(members[0]);
        long babyUid = Long.parseLong(members[1]);
        long score = event.getItemScore();
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getOccurTime()));

        String groupCode = getRedisGroupCode(attr.getActId());
        String totalStepKey = getTotalStepKey(attr);
        String totalStepSeq = makeKey(attr, "seq:totalStep:" + eventSeq);
        Long totalStep = actRedisDao.hIncrByKeyWithSeq(groupCode, totalStepSeq, totalStepKey, timeCode, score, DateUtil.ONE_MONTH_SECONDS);
        if (totalStep == null) {
            log.error("DriftBottleComponent handleScoreChange ignore by seq has done, actId:{},member:{},eventSeq:{},timeCode:{}", attr.getActId(), event.getMember(), eventSeq,timeCode);
            return;
        }


        //改为sorted存储路线图
        RoadMapCpInfoVo roadMapCpInfoVo = new RoadMapCpInfoVo();
        roadMapCpInfoVo.setUserUid(userUid);
        roadMapCpInfoVo.setBabyUid(babyUid);
        roadMapCpInfoVo.setAddStep(score);
        roadMapCpInfoVo.setCureentStep(totalStep);
        String roadMapZsetKey = getRoadMapCpZsetKey(attr, timeCode);
        actRedisDao.zAdd(groupCode,roadMapZsetKey,JsonUtil.toJson(roadMapCpInfoVo),totalStep);


        //更新当前主持最新CP,跟路线图保持一致
        String latestKey = getLatestCPKey(attr, timeCode,babyUid);
        actRedisDao.zAdd(groupCode,latestKey,String.valueOf(userUid),totalStep);


        //记录当前cp 最新room，兼容没有传厅的情况
        String key = makeKey(attr, String.format(CP_LAST_ROOM, timeCode));
        String value = event.getActors().get(attr.getTingActor());
        if(StringUtils.isNotEmpty(value)) {
            actRedisDao.hset(getRedisGroupCode(attr.getActId()), key, event.getMember(), value);
        }

    }


    private String getLatestCPKey(DriftBottleComponentAttr attr, String timeCode,long babyUid) {
        return makeKey(attr, String.format(DRIFT_BOTTLE_LATEST_CP, timeCode,babyUid));
    }

    private String getTotalStepKey(DriftBottleComponentAttr attr) {
        return makeKey(attr, DRIFT_BOTTLE_TOTAL_STEP);
    }



    public String getRoadMapCpZsetKey(DriftBottleComponentAttr attr, String timeCode) {
        return makeKey(attr, String.format(ROAD_MAP_CP_ZSET_KEY, timeCode));
    }


    /**
     * 漂流路线图
     *
     * @param dateStr
     * @return
     */
    @RequestMapping(value = "/getRoadMapCpList")
    public Response<List<RoadMapCpInfoVo>> getRoadMapCpList(@RequestParam("actId") long actId,
                                                            @RequestParam(name = "cmptInx", defaultValue = "810") long cmptInx,
                                                            String dateStr) {

        DriftBottleComponentAttr attr = getComponentAttr(actId, cmptInx);

        List<RoadMapCpInfoVo> list = Lists.newArrayList();
        String groupCode = getRedisGroupCode(attr.getActId());

        String roadMapZsetKey = getRoadMapCpZsetKey(attr, dateStr);
        Set<ZSetOperations.TypedTuple<String>> tuples  = actRedisDao.zrevRange(groupCode,roadMapZsetKey,attr.getRoadMapTopN());
        if (!CollectionUtils.isEmpty(tuples)) {
            Set<Long> uids = Sets.newHashSetWithExpectedSize(tuples.size()*2);
            for (ZSetOperations.TypedTuple<String> tuple : tuples) {
                RoadMapCpInfoVo record = JSON.parseObject( tuple.getValue(), RoadMapCpInfoVo.class);
                uids.add(record.getUserUid());
                uids.add(record.getBabyUid());
                list.add(record);
            }
            Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
            Map<Long, UserInfoVo> userInfos = getUserInfoWithNickExt(Lists.newArrayList(uids),multiNickUsers);
            list.stream().forEach(v -> {
                long babyUid = v.getBabyUid();
                long userUid = v.getUserUid();
                if(userInfos!=null && userInfos.containsKey(userUid)) {
                    v.setUserLogo(userInfos.get(userUid).getAvatarUrl());
                    v.setUserNick(userInfos.get(userUid).getNick());
                }
                if(userInfos!=null && userInfos.containsKey(babyUid)) {
                    v.setBabyLogo(userInfos.get(babyUid).getAvatarUrl());
                    v.setBabyNick(userInfos.get(babyUid).getNick());
                }
                if (v.getNickExtUsers() == null) {
                    v.setNickExtUsers(new HashMap<>(2));
                }
                if (multiNickUsers != null && multiNickUsers.containsKey(String.valueOf(babyUid))) {
                    v.getNickExtUsers().put(String.valueOf(babyUid), multiNickUsers.get(String.valueOf(babyUid)));
                    v.getNickExtUsers().put(String.valueOf(userUid), multiNickUsers.get(String.valueOf(userUid)));
                }
            });

        }




        return Response.success(list);
    }


    @Override
    public List<String> supportKeys() {


        return null;
    }

    @Override
    public List<Object> addViewExt(GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        DriftBottleComponentAttr attr = tryGetUniqueComponentAttr(rankReq.getActId());

        if (attr == null || CollectionUtils.isEmpty(objectList)) {
            return objectList;
        }

        if (attr.getRankId() == rankReq.getRankId()) {
            RankItemUserAnchor rankItem = (RankItemUserAnchor) objectList.get(0);
            if (rankItem.getViewExt() == null) {
                rankItem.setViewExt(new HashMap<>(2));
            }
            if (rankItem.getUserRankItem() != null && rankItem.getBabyRankItem() != null) {
                long totalStep = getTotalStep(attr, rankReq.getDateStr());
                long exchangeGiftStep = exchangeGiftStep(attr,totalStep);
                rankItem.getViewExt().put("totalStep", String.valueOf(totalStep));
                DriftBottleGiftVo config = calAwardConfig(attr, exchangeGiftStep);
                if (config != null) {
                    rankItem.getViewExt().put("giftLogo", config.getGiftLogo());
                    rankItem.getViewExt().put("giftAmount", String.valueOf(2*exchangeGiftStep * attr.getStepExchangeGiftUnit()));
                    List<CpGiftDetailVo> giftDetail = getGiftDetailByTotalStep(attr,totalStep,rankReq.getDateStr(),exchangeGiftStep);
                    rankItem.getViewExt().put("giftDetail",JsonUtil.toJson(giftDetail));
                }
            }
        }
        return objectList;
    }

    private List<CpGiftDetailVo> getGiftDetailByTotalStep(DriftBottleComponentAttr attr , long totalStep,String timeCode,long exchangeGiftStep) {
        List<CpGiftDetailVo> giftLst = Lists.newArrayList();
        DriftBottleGiftVo awardAConfig = calAwardConfig(attr, exchangeGiftStep);
        if (awardAConfig == null) {
            log.error("getGiftDetailByTotalStep calAwardConfig is null,actId:{},timeCode:{}",attr.getActId(),timeCode);
            return giftLst;
        }

        long multipleStep = exchangeGiftStep / awardAConfig.getStep();
        if (multipleStep <= 0) {
            log.error("getGiftDetailByTotalStep multipleStep <=0 ,actId:{},timeCode:{}",attr.getActId(),timeCode);
            return giftLst;
        }

        long awardACount = multipleStep * awardAConfig.getStep() * attr.getStepExchangeGiftUnit() / awardAConfig.getGiftPrice();
        long remainderStep = exchangeGiftStep % awardAConfig.getStep();
        DriftBottleGiftVo awardBConfig = null;
        long awardBCount = 0L;
        if (remainderStep > 0) {
            awardBConfig = getMinStepAwardConfig(attr);
            if (awardBConfig == null) {
                log.error("releaseTopOneAward getMinStepAwardConfig is null");
                return giftLst;
            }
            awardBCount = remainderStep * attr.getStepExchangeGiftUnit() / awardBConfig.getGiftPrice();
        }

        CpGiftDetailVo itemA = new CpGiftDetailVo();
        itemA.setGiftName(awardAConfig.getGiftName());
        itemA.setGiftCount(awardACount*2);
        giftLst.add(itemA);
        if(awardBCount>0){
            CpGiftDetailVo itemB = new CpGiftDetailVo();
            itemB.setGiftName(awardBConfig.getGiftName());
            itemB.setGiftCount(awardBCount*2);
            giftLst.add(itemB);
        }
        return giftLst;
    }
    private DriftBottleGiftVo calAwardConfig(DriftBottleComponentAttr attr, long step) {
        DriftBottleGiftVo config = null;
        //attr.getScoreAward().keySet() 要从小到大排序
        List<DriftBottleGiftVo> top1GiftInfo = attr.getTop1GiftInfo().stream().sorted(Comparator.comparing(DriftBottleGiftVo::getStep)).toList();
        for (DriftBottleGiftVo item : top1GiftInfo) {
            if (step >= item.getStep()) {
                config = item;
            }
        }
        return config;
    }

    private DriftBottleGiftVo getMinStepAwardConfig(DriftBottleComponentAttr attr) {

        return attr.getTop1GiftInfo().stream().filter(v -> v.getStep() == attr.getTop1GiftMinStep()).findFirst().orElse(null);
    }


    @Override
    public long getActId() {
        return ComponentId.DRIFT_BOTTLE;
    }


    @Override
    public LayerBroadcastInfo customBroadcastInTheEnd(LayerBroadcastInfo source) {
        if (source.getActBusiId() != (long) BusiId.SKILL_CARD.getValue()) {
            return source;
        }
        List<LayerMemberItem> layerMemberItems = source.getExtMemberItem();
        if (CollectionUtils.isEmpty(layerMemberItems)) {
            return source;
        }

        long actId = source.getActId();
        DriftBottleComponentAttr attr = tryGetUniqueComponentAttr(actId);
        Date now = commonService.getNow(attr.getActId());
        String timeCode = DateUtil.format(DateUtil.getDayOf30MinuteInterval(now), DateUtil.PATTERN_TYPE9);

        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        Set<Long> userInfoIds = new HashSet<>();
        for (LayerMemberItem layerMemberItem : layerMemberItems) {
            if (!LayerItemTypeKey.CP_LATEST.equals(layerMemberItem.getItemType())) {
                continue;
            }
            String babyUid = layerMemberItem.getMemberId();
            userInfoIds.add(Long.parseLong(babyUid));


            RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, attr.getRankId(), now, attr.getPhaseId());
            PhaseInfo curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, attr.getPhaseId());
            long leftSeconds = actLayerInfoService.getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
            layerMemberItem.setCurPhaseInfo(curPhaseInfo);
            layerMemberItem.setLeftSeconds(leftSeconds);

            //获取最新的CP
            String latestUid = getLatestCpUid(attr, actId,   timeCode, Long.parseLong(babyUid));
            if (StringUtils.isEmpty(latestUid)) {
                continue;
            }

            userInfoIds.add(Long.parseLong(latestUid));
            ActorQueryItem queryItem = new ActorQueryItem();
            //cp榜member组成 用户|主播
            queryItem.setActorId(latestUid + "|" + babyUid);
            queryItem.setRankingId(attr.getRankId());
            queryItem.setPhaseId(attr.getPhaseId());
            queryItem.setDateStr(timeCode);
            queryItem.setWithStatus(false);
            queryCpScorePara.add(queryItem);
        }

        if (CollectionUtils.isEmpty(userInfoIds)) {
            return source;
        }

        //获取CP总里程
        long totalStep = getTotalStep(attr, now);
        long exchangeGiftStep = exchangeGiftStep(attr,totalStep);
        DriftBottleGiftVo config = calAwardConfig(attr, exchangeGiftStep);
        String giftLogo = null;
        Long giftAmount = null;
        if (config != null) {
            giftLogo = config.getGiftLogo();
            //显示两个人的礼物金额
            giftAmount = 2*exchangeGiftStep * attr.getStepExchangeGiftUnit();
        }else{
            giftLogo = getMinStepAwardConfig(attr).getGiftLogo();
            giftAmount = 0L;
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(userInfoIds.size());
        Map<Long, UserInfoVo> userInfoVoMap = getUserInfoWithNickExt(Lists.newArrayList(userInfoIds),multiNickUsers);

        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
        for (LayerMemberItem item : layerMemberItems) {
            if (!LayerItemTypeKey.CP_LATEST.equals(item.getItemType())) {
                continue;
            }
            final String memberId = item.getMemberId();
            Map<String, Object> itemExt = item.getExt();
            if (itemExt == null) {
                itemExt = Maps.newHashMap();
            }
            itemExt.put("giftAmount", String.valueOf(giftAmount));
            itemExt.put("giftLogo", giftLogo);
            itemExt.put("babyUid", Long.parseLong(memberId));
            UserInfoVo babyInfoVo = userInfoVoMap.get(Convert.toLong(memberId, 0));
            if (babyInfoVo != null) {
                String nick = Convert.toString(babyInfoVo.getNick());
                itemExt.put("babyNick", Base64Utils.encodeToString(nick.getBytes()));
                itemExt.put("babyLogo", babyInfoVo.getAvatarUrl());
            }
            Map<String, Map<String, MultiNickItem>> nickExtUsers = new HashMap<>();
            if (multiNickUsers != null && multiNickUsers.containsKey(memberId)) {
                nickExtUsers.put(memberId, multiNickUsers.get(memberId));
            }
            for (ActorInfoItem actorInfoItem : actorInfoItems) {
                if (actorInfoItem.getActorId().contains("|" + memberId)) {
                    String userUid = actorInfoItem.getActorId().split("\\|")[0];
                    item.setRank(Convert.toInt(actorInfoItem.getRank()));
                    item.setScore(actorInfoItem.getScore());
                    item.setCurRankId(actorInfoItem.getRankingId());
                    item.setCurPhaseId(actorInfoItem.getPhaseId());

                    itemExt.put("userUid", userUid);
                    UserInfoVo userInfoVo = userInfoVoMap.get(Convert.toLong(userUid, 0));
                    if (userInfoVo != null) {
                        String nick = Convert.toString(userInfoVo.getNick());
                        itemExt.put("userNick", Base64Utils.encodeToString(nick.getBytes()));
                        itemExt.put("userLogo", userInfoVo.getAvatarUrl());

                    }

                    if (multiNickUsers != null && multiNickUsers.containsKey(String.valueOf(userUid))) {
                        nickExtUsers.put(userUid, multiNickUsers.get(userUid));
                    }
                    break;
                }
            }
            itemExt.put("nickExtUsers", nickExtUsers);
        }

        return source;
    }

    private String getLatestCpUid(DriftBottleComponentAttr attr,long actId,  String timeCode,long babyUid) {

        String groupCode = getRedisGroupCode(actId);
        String latestKey = getLatestCPKey(attr, timeCode,babyUid);
        Set<ZSetOperations.TypedTuple<String>> latestCpInfo = actRedisDao.zrevRange(groupCode,latestKey,1);

        if (CollectionUtils.isNotEmpty(latestCpInfo)) {
            return latestCpInfo.stream().findFirst().get().getValue();
        }else{
            return StringUtils.EMPTY;
        }

    }
   private Map<Long, UserInfoVo> getUserInfoWithNickExt(List<Long> uids,Map<String, Map<String, MultiNickItem>> multiNickUsers) {
       Map<Long, UserInfoVo> userInfoVoMap = null;
       BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
       if (batched == null || MapUtils.isEmpty(batched.getUserInfoMap())) {
           log.error("trying to get user info with nick ext fail");
       } else {
           userInfoVoMap = UserInfoService.getUserInfo(batched.getUserInfoMap());
           if (StringUtils.startsWith(batched.getNickExt(), StringUtil.OPEN_BRACE)) {
               NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
               if (MapUtils.isNotEmpty(nickExt.getUsers())) {
                   multiNickUsers.putAll(nickExt.getUsers());
               }
           }
       }


       if (userInfoVoMap == null) {
           userInfoVoMap = userInfoService.getUserInfo(uids, Template.getTemplate(810));
       }
       return userInfoVoMap;

   }


    private Long getTotalStep(DriftBottleComponentAttr attr, Date now) {
        String timeCode = DateUtil.format(DateUtil.getDayOf30MinuteInterval(now), DateUtil.PATTERN_TYPE9);
        return getTotalStep(attr, timeCode);
    }


    private Long getTotalStep(DriftBottleComponentAttr attr, String timeCode) {
        String groupCode = getRedisGroupCode(attr.getActId());
        String totalStepKey = getTotalStepKey(attr);
        String stepValue = actRedisDao.hget(groupCode, totalStepKey, timeCode);
        return Convert.toLong(stepValue, 0L);
    }


    @GetMapping("/taskStatic")
    public Response<String> taskStatic(HttpServletRequest request, HttpServletResponse response, Long actId) {
        Date now = commonService.getNow(actId);
        Date noticeDate = DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30));
        doStaticReport(actId, now, getUniqueComponentAttr(actId),noticeDate);
        return Response.ok();
    }

    /**
     * 每半小时一次日报
     */
    @NeedRecycle(author = "chengaojie", notRecycle = true)
    @Scheduled(cron = "11 0/30 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }
            DriftBottleComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            Date now = commonService.getNow(attr.getActId());
            Date noticeDate = DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30));

            String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE9);
            String groupCode = getRedisGroupCode(actId);
            String execKey = makeKey(attr, "execDriftBottleStatic:"+timeCode);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}",execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId,now);

            doStaticReport(actId, now, attr,noticeDate);

        }
    }

    public void doStaticReport(long actId, Date now, DriftBottleComponentAttr attr,Date noticeDate) {
        if(attr==null) {
            log.error("doStaticReport doStaticReport attr is null,actId:{}",actId);
            return;
        }
        String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE9);
        String noticeTime = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE12);

        StringBuilder content = new StringBuilder();
        content.append(String.format("### 场次时间：%s场\n", noticeTime));

        long totalStep = getTotalStep(attr, timeCode);
        content.append("全服累计km：").append(totalStep).append("\n");
        if(totalStep>0){
            //两人的礼物
            long exchangeGiftStep = exchangeGiftStep(attr,totalStep);
            content.append("本轮累计礼物：").append(formatMoney(2*exchangeGiftStep*attr.getStepExchangeGiftUnit())).append("元\n");
        }else{
            content.append("本轮累计礼物：0元\n");
        }

        Set<Long> uids = new HashSet<>();
        //最强cp
        String groupCode = getRedisGroupCode(actId);
        String toponeKey = makeKey(attr,DRIFT_BOTTLE_TOPONE_STATISTIC);
        String topValue = actRedisDao.hget(groupCode,toponeKey,timeCode);
        RoadMapCpInfoVo toponeInfo = null;
        if (StringUtils.isNotEmpty(topValue)) {
            toponeInfo = JSONUtils.parseObject(topValue, RoadMapCpInfoVo.class);
            uids.add(toponeInfo.getUserUid());
            uids.add(toponeInfo.getBabyUid());
        }

        //幸运CP
        String luckyKey = makeKey(attr,DRIFT_BOTTLE_LUCKY_STATISTIC);
        String luckyValue = actRedisDao.hget(groupCode,luckyKey,timeCode);
        RoadMapCpInfoVo luckyInfo = null;
        if (StringUtils.isNotEmpty(luckyValue)) {
            luckyInfo = JSONUtils.parseObject(luckyValue, RoadMapCpInfoVo.class);
            uids.add(luckyInfo.getUserUid());
            uids.add(luckyInfo.getBabyUid());
        }

        Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(uids), Template.getTemplate(810));
        if(toponeInfo!=null){
            UserInfoVo userInfo = userInfoVoMap.get(toponeInfo.getUserUid());
            UserInfoVo babyInfo = userInfoVoMap.get(toponeInfo.getBabyUid());
            content.append("本轮最强CP：");
            content.append(babyInfo.getNick()).append("(").append(babyInfo.getUid()).append(")&");
            content.append(userInfo.getNick()).append("(").append(userInfo.getUid()).append(")、");
            content.append("贡献").append(toponeInfo.getAddStep()).append("KM\n");
        }
        if(luckyInfo!=null) {
            UserInfoVo userInfo = userInfoVoMap.get(luckyInfo.getUserUid());
            UserInfoVo babyInfo = userInfoVoMap.get(luckyInfo.getBabyUid());
            content.append("本轮幸运CP：");
            content.append(babyInfo.getNick()).append("(").append(babyInfo.getUid()).append(")&");
            content.append(userInfo.getNick()).append("(").append(userInfo.getUid()).append(")、");
            content.append("贡献第").append(luckyInfo.getCureentStep()).append("KM\n");
        }

        String totalAwardKey = makeKey(attr,DRIFT_BOTTLE_TOTAL_AWARD_STATISTIC);
        String totalAwardValue = actRedisDao.get(groupCode,totalAwardKey);

        if(StringUtils.isNotEmpty(totalAwardValue)) {
            content.append("截止当前，玩法累计发放礼物金额：").append(formatMoney(Convert.toLong(totalAwardValue,0))).append("\n");
        }

        String msg = buildActRuliuMsg(actId, false, "恋爱漂流瓶玩法-半小时", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

    }


    public static String formatMoney(long awardNum) {
        DecimalFormat df = new DecimalFormat("#.#");
        return df.format((float)awardNum/1000);
    }

}
