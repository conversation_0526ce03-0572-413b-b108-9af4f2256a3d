package com.yy.gameecology.hdzj.element.redis;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.ScheduledExt;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.StartShowEvent;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.OnlineChannelService;
import com.yy.gameecology.activity.service.SignedService;
import com.yy.gameecology.activity.service.layer.BroActLayerService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SlotGrabComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardProp;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

@UseRedisStore
@Slf4j
@RestController
@RequestMapping("5088")
public class SlotGrabComponent extends BaseActComponent<SlotGrabComponentAttr> implements LayerSupport, InitializingBean {

    /**
     * 记录每个位置当前的member，member = {"anchor":111,"user":2222,"score":10000,"packageId":708}<br/>
     * {yyyyMMddHH}:slot_member:{slot}
     */
    private static final String SLOT_MEMBER_KEY = "%s:slot_member:%d";

    /**
     * 记录每个小时挑战成功的记录
     * {yyyyMMddHH}:hour_challenge
     */
    private static final String HOUR_CHALLENGE_RECORD = "%s:hour_challenge";

    /**
     * 记录主持发起挑战的记录
     * anchor_challenge:{uid}
     */
    private static final String ANCHOR_CHALLENGE_RECORD = "anchor_challenge:%d";

    /**
     * 记录用户发起挑战的记录
     * user_challenge:{uid}
     */
    private static final String USER_CHALLENGE_RECORD = "user_challenge:%d";

    /**
     * 记录主持被挑战的记录
     * anchor_challenged:{uid}
     */
    private static final String ANCHOR_CHALLENGED_RECORD = "anchor_challenged:%d";

    /**
     * 记录用户被挑战的记录
     * user_challenged:{uid}
     */
    private static final String USER_CHALLENGED_RECORD = "user_challenged:%d";

    /**
     * 每小时的结算标识，防止重复结算
     * {yyyyMMddHH}:settle
     */
    private static final String SETTLE_KEY = "%s:settle";

    /**
     * 每小时发起挑战的主持，当延迟队列处理挑战结算 & 判断本小时的挑战结算都已完成，可触发小时结算
     * {yyyyMMddHH}:challenge
     */
    private static final String CHALLENGER = "%s:challenge";

    /**
     * 记录每次挑战的信息
     * {yyyyMMddHH}:challenger:{uid}
     */
    private static final String CHALLENGER_RECORD = "%s:challenger_record:%d";

    /**
     * 记录每次挑战的分
     * {yyyyMMddHH}:challenge_score:{uid}:{mmss}
     */
    private static final String CHALLENGE_SCORE = "%s:challenge_score:%d:%s";

    private static final String GIFT_SEQ = "gift_seq:%s";

    private static final String GLOBAL_AWARD = "global_award";

    private static final String USER_AWARD = "user_award:%d";

    private static final String USER_AWARD_NOTICE = "award_notice:%s";

    private static final String ANCHOR_FIRST_NOTICE = "anchor_first_notice";

    private static final String UPDATE_SCORE_SCRIPT = """
            local val = tonumber(redis.call('GET', KEYS[1]) or 0)
            if val == 1 then
                return 0
            end
            local contribute_key = KEYS[2] .. ':contribute'
            redis.call('SETEX', KEYS[1], 86400, '1')
            redis.call('INCRBY', KEYS[2], ARGV[2])
            redis.call('EXPIRE', KEYS[2], 86400)
            redis.call('ZINCRBY', contribute_key, ARGV[2], ARGV[1])
            redis.call('EXPIRE', contribute_key, 86400)
            return 1
            """;

    private static final String SETTLE_SCRIPT = """
            local anchor = ARGV[1]
            local time = ARGV[2]
            local slot_value = redis.call('GET', KEYS[1]) or '{}'
            local slot_member = cjson.decode(slot_value)
            local slot_score = slot_member.score or 0
            local score = tonumber(redis.call('GET', KEYS[2]) or 0)
            local user_members = redis.call('ZREVRANGE', KEYS[2] .. ':contribute', 0, 0)
            local user = '0'
            if user_members and #user_members > 0 then
                user = user_members[1]
            end
            local new_slot_value = string.format('{"anchor":%s,"user":%s,"score":%d,"time":%s}', anchor, user, score, time)
            if score > slot_score then
                redis.call('SET', KEYS[1], new_slot_value)
                return {slot_value, new_slot_value}
            end
            return {slot_value, new_slot_value}
            """;

    private final RedisScript<Long> updateScoreScript = new DefaultRedisScript<>(UPDATE_SCORE_SCRIPT, Long.class);

    private final RedisScript<List> settleScript = new DefaultRedisScript<>(SETTLE_SCRIPT, List.class);

    @Resource(name = "zpopByScoreWithScoresScript")
    private RedisScript<List<String>> zpopByScoreWithScoresScript;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private SignedService signedService;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Autowired
    private BroActLayerService broActLayerService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.SLOT_GRAB;
    }

    @Override
    public long getActId() {
        return ComponentId.SLOT_GRAB;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        registerDelayQueue("refresh_layer", this::refreshLayer);
    }

    @ScheduledExt
    @Scheduled(initialDelay = 6000, fixedDelay = 1000)
    @NeedRecycle(author = "liqingyang", notRecycle = true)
    public void handleChallengeSettle() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        actIds.parallelStream().forEach(actId -> {
            SlotGrabComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                return;
            }

            Date now = commonService.getNow(actId);
            Date prevHour = DateUtils.addHours(now, -1);
            if (!inGrabTime(attr, now) && !inGrabTime(attr, prevHour)) {
                return;
            }

            doHandleSlotChallengeSettle(attr);
        });
    }

    @ScheduledExt
    @Scheduled(initialDelay = 7000, fixedDelay = 1000)
    @NeedRecycle(author = "liqingyang", notRecycle = true)
    public void handleSlotPhaseSettle() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        actIds.parallelStream().forEach(actId -> {
            SlotGrabComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                return;
            }

            Date now = commonService.getNow(actId);
            Date prevHour = DateUtils.addHours(now, -1);
            long fragmentSecond = DateUtils.getFragmentInSeconds(now, Calendar.HOUR_OF_DAY);
            // 不在结算时间内 或 不在小时的 6 ~ 50s 内（6s之后是为了保证挑战结算都已执行完）
            if (!inGrabTime(attr, prevHour) || fragmentSecond > 50 || fragmentSecond < 6) {
                return;
            }

            StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
            String hour = DateFormatUtils.format(prevHour, DateUtil.PATTERN_TYPE7);
            String challengeKey = makeKey(attr, String.format(CHALLENGER, hour));
            Long size = redisTemplate.opsForZSet().size(challengeKey);
            if (size != null && size.intValue() != 0) {
                if (fragmentSecond > 10) {
                    // alert
                    String msg = buildActRuliuMsg(actId, true, "位置挑战结算超时", "时间已经超过10s，上一小时的位置挑战结算还未完成");
                    baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
                    log.error("{} has pass 10s, still have delay task not handle", hour);
                }
                return;
            }

            handleSlotPhaseSettle(attr, hour);
        });
    }

    private void handleSlotPhaseSettle(SlotGrabComponentAttr attr, String hour) {
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        String settleKey = makeKey(attr, String.format(SETTLE_KEY, hour));
        boolean set = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(settleKey, DateUtil.getNowYyyyMMddHHmmss(), 10, TimeUnit.MINUTES));
        if (set) {
            boolean canRedo = true;
            try {
                List<String> slotMemberKeys = IntStream.range(1, attr.getSlotSize() + 1).mapToObj(i -> makeKey(attr, String.format(SLOT_MEMBER_KEY, hour, i))).toList();
                List<String> slotMemberValues = redisTemplate.opsForValue().multiGet(slotMemberKeys);
                if (CollectionUtils.isEmpty(slotMemberValues) || slotMemberValues.size() != attr.getSlotSize()) {
                    log.error("settle phase member size unexpected:{}", slotMemberValues);
                    return;
                }

                Map<Integer, SlotMember> members = new HashMap<>(attr.getSlotSize());
                SlotMember bigSlotMember = null;
                int bigSlot = 0;
                for (int i = 0; i < attr.getSlotSize(); i++) {
                    String slotMemberValue = slotMemberValues.get(i);
                    if (!StringUtils.startsWith(slotMemberValue, StringUtil.OPEN_BRACE)) {
                        continue;
                    }

                    SlotMember slotMember = JSON.parseObject(slotMemberValue, SlotMember.class);
                    if (slotMember == null) {
                        continue;
                    }

                    int slot = i + 1;
                    members.put(slot, slotMember);

                    boolean find =bigSlotMember == null || bigSlotMember.score < slotMember.score
                                  || (bigSlotMember.score == slotMember.score && bigSlotMember.time > slotMember.time);
                    // 找出大奖的slot
                    if (find) {
                        bigSlotMember = slotMember;
                        bigSlot = slot;
                    }
                }

                if (members.isEmpty()) {
                    return;
                }

                // 先放入大奖的奖包
                Map<Integer, Long> slotPackageIds = new HashMap<>(members.size());
                slotPackageIds.put(bigSlot, attr.getBigPackageId());

                if (members.size() > 1) {
                    List<Integer> slots = new ArrayList<>(members.keySet());
                    Collections.shuffle(slots);
                    int index = 0;
                    for (int slot: slots) {
                        if (slot != bigSlot) {
                            slotPackageIds.put(slot, attr.getNormalPackageIds().get(index++));
                        }
                    }
                }

                Set<Long> uids = new HashSet<>(members.size() * 2);
                Map<Integer, AwardProp> slotAwardProp = new HashMap<>(members.size());
                Map<String, String> updateRecordMap = new HashMap<>(members.size());
                Map<String, List<byte[]>> awardRecordMap = new HashMap<>(members.size() * 2 + 1);
                for (int slot = 1; slot <= attr.getSlotSize(); slot++) {
                    SlotMember slotMember = members.get(slot);
                    if (slotMember == null) {
                        continue;
                    }

                    final long userUid = slotMember.user, anchorUid = slotMember.anchor;
                    uids.add(userUid);
                    uids.add(anchorUid);
                    final long packageId = slotPackageIds.get(slot);
                    String userSeq = String.format("award_user:%s:%d:%d", hour, slot, userUid),
                            anchorSeq = String.format("award_anchor:%s:%d:%d", hour, slot, anchorUid);
                    hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), anchorUid, attr.getTaskId(), 1, packageId, anchorSeq, Collections.emptyMap());
                    hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), userUid, attr.getTaskId(), 1, packageId, userSeq, Collections.emptyMap());

                    // 回写packageId
                    slotMember.setPackageId(packageId);
                    String slotMemberKey = slotMemberKeys.get(slot - 1);
                    String slotMemberValue = JSON.toJSONString(slotMember);
                    updateRecordMap.put(slotMemberKey, slotMemberValue);

                    // 中奖记录
                    AwardRecord awardRecord = new AwardRecord();
                    awardRecord.setSlot(slot);
                    awardRecord.setHour(hour);
                    awardRecord.setAnchorUid(anchorUid);
                    awardRecord.setUserUid(userUid);
                    awardRecord.setPackageId(packageId);
                    byte[] awardRecordBytes = JSON.toJSONString(awardRecord).getBytes(StandardCharsets.UTF_8);

                    String globalKey = makeKey(attr, GLOBAL_AWARD);
                    String anchorAwardKey  = makeKey(attr, String.format(USER_AWARD, anchorUid));
                    String userAwardKey  = makeKey(attr, String.format(USER_AWARD, userUid));
                    List<byte[]> globalAwardRecords = awardRecordMap.computeIfAbsent(globalKey, k -> new ArrayList<>(attr.getSlotSize() * 2));
                    List<byte[]> anchorAwardRecords = awardRecordMap.computeIfAbsent(anchorAwardKey, k -> new ArrayList<>(attr.getSlotSize() * 2));
                    List<byte[]> userAwardRecords = awardRecordMap.computeIfAbsent(userAwardKey, k -> new ArrayList<>(attr.getSlotSize() * 2));

                    globalAwardRecords.add(awardRecordBytes);
                    anchorAwardRecords.add(awardRecordBytes);
                    userAwardRecords.add(awardRecordBytes);

                    AwardProp prop = attr.getAwardProps().get(packageId);
                    slotAwardProp.put(slot, prop);
                }

                //更新packageId
                redisTemplate.opsForValue().multiSet(updateRecordMap);

                //保存奖励记录
                redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                    for (Map.Entry<String, List<byte[]>> entry : awardRecordMap.entrySet()) {
                        if (CollectionUtils.isEmpty(entry.getValue())) {
                            continue;
                        }
                        String key = entry.getKey();
                        byte[][] values = entry.getValue().toArray(byte[][]::new);
                        connection.lPush(key.getBytes(StandardCharsets.UTF_8), values);
                    }
                    return null;
                });

                canRedo = false;

                //发送结果到全频道广播
                Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(uids), true);
                List<SlotMemberInfo> slotMemberInfos = new ArrayList<>(attr.getSlotSize());
                for (int slot = 1; slot <= attr.getSlotSize(); slot++) {
                    SlotMember slotMember = members.getOrDefault(slot, new SlotMember());
                    SlotMemberInfo item = new SlotMemberInfo(slotMember);
                    item.setSlot(slot);
                    setUserInfo(item, userInfos);
                    AwardProp prop = slotAwardProp.get(slot);
                    if (prop != null) {
                        item.setAwardName(prop.getAwardName());
                        item.setAwardIcon(prop.getAwardIcon());
                    }

                    if (slotMember.packageId == attr.getBigPackageId()) {
                        item.setBigAward(1);
                    }

                    slotMemberInfos.add(item);
                }

                GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                        .setActId(attr.getActId())
                        .setBannerId(5088003)
                        .setBannerType(0)
                        .setJsonData(JSON.toJSONString(slotMemberInfos));

                GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                        .setBannerBroadcast(bannerBroadcast).build();

                svcSDKService.broadcastTemplate(InterstellarTreasureComponent.getTemplateByBusiId(attr.getBusiId()), msg);
                log.info("handleSlotPhaseSettle finish with:{} {}", members, slotAwardProp);

                for (SlotMemberInfo slotMemberInfo : slotMemberInfos) {
                    threadPoolManager.get(Const.GENERAL_POOL).execute(() -> notifyAwardNotice(attr, redisTemplate, hour, slotMemberInfo));
                }

                threadPoolManager.get(Const.GENERAL_POOL).execute(() -> notifyInfoflow(attr, hour, slotMemberInfos));
            } catch (Exception e) {
                log.error("handleSlotPhaseSettle exception:", e);
                if (canRedo) {
                    redisTemplate.delete(settleKey);
                }
                String msg = buildActRuliuMsg(attr.getActId(), true, "爱情卡位战结算失败", "爱情卡位战小时结算失败：canRedo=" + canRedo + "，错误信息：" + e.getMessage());
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Collections.emptyList());
            }
        }
    }

    public void notifyAwardNotice(SlotGrabComponentAttr attr, StringRedisTemplate redisTemplate, String hour, SlotMemberInfo slotMemberInfo) {
        UserCurrentChannel anchorChannel = commonService.getUserCurrentChannel(slotMemberInfo.anchorUid);
        UserCurrentChannel userChannel = commonService.getUserCurrentChannel(slotMemberInfo.userUid);
        JSONObject data = new JSONObject();
        data.put("hour", hour);
        data.put("slot", slotMemberInfo.slot);
        data.put("awardName", slotMemberInfo.awardName);
        data.put("awardIcon", slotMemberInfo.awardIcon);
        String noticeValue = data.toJSONString();
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(PBCommonNoticeType.AWARD_NOTICE)
                .setNoticeValue(noticeValue);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        String key = makeKey(attr, String.format(USER_AWARD_NOTICE, hour.substring(0, 8)));
        if (anchorChannel != null) {
            svcSDKService.unicastUid(slotMemberInfo.anchorUid, msg);
            log.info("notifyAwardNotice send award notice with {} {}", slotMemberInfo.anchorUid, noticeValue);
        } else {
            redisTemplate.opsForHash().put(key, String.valueOf(slotMemberInfo.anchorUid), noticeValue);
        }

        if (userChannel != null) {
            svcSDKService.unicastUid(slotMemberInfo.userUid, msg);
            log.info("notifyAwardNotice send award notice with {} {}", slotMemberInfo.userUid, noticeValue);
        } else {
            redisTemplate.opsForHash().put(key, String.valueOf(slotMemberInfo.userUid), noticeValue);
        }
    }

    public void notifyInfoflow(SlotGrabComponentAttr attr, String hour, List<SlotMemberInfo> slotMemberInfos) {
        StringBuilder msg = new StringBuilder();
        for (SlotMemberInfo info : slotMemberInfos) {
            if (info.anchorUid == 0) {
                continue;
            }
            msg.append("位置【").append(info.slot)
                    .append("】 -> 主持：")
                    .append(new String(Base64Utils.decodeFromString(info.anchorNick), StandardCharsets.UTF_8))
                    .append("[").append(info.anchorUid).append("]，神豪：")
                    .append(new String(Base64Utils.decodeFromString(info.userNick), StandardCharsets.UTF_8))
                    .append("[").append(info.userUid).append("]，奖励：")
                    .append(info.awardName).append("，分值：").append(info.score).append("\n");

        }
        String message = buildActRuliuMsg(attr.getActId(), false, "爱情卡位战结算播报【" + hour + "】", msg.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, message, Collections.emptyList());
    }

    private void doHandleSlotChallengeSettle(SlotGrabComponentAttr attr) {
        Date now = commonService.getNow(attr.getActId());
        String hour = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE7);
        doHandleSlotChallengeSettle(attr, hour, now.getTime());

        long second = DateUtils.getFragmentInSeconds(now, Calendar.HOUR_OF_DAY);
        if (second < 6) {
            Date prevHourDate = DateUtils.addHours(now, -1);
            if (inGrabTime(attr, prevHourDate)) {
                String prevHour = DateFormatUtils.format(prevHourDate, DateUtil.PATTERN_TYPE7);
                doHandleSlotChallengeSettle(attr, prevHour, now.getTime());
            }
        }
    }

    private void doHandleSlotChallengeSettle(SlotGrabComponentAttr attr, String hour, long timestamp) {
        final long actId = attr.getActId();
        String challengeKey = makeKey(attr, String.format(CHALLENGER, hour));
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        List<String> result = redisTemplate.execute(zpopByScoreWithScoresScript, List.of(challengeKey), "0", String.valueOf(timestamp), "2");
        if (CollectionUtils.isNotEmpty(result)) {
            for (int i = 0; i < result.size(); i+=2) {
                String uid = result.get(i);
                handleDelaySettle(attr, hour, Long.parseLong(uid));
            }
        }
    }

    private void handleDelaySettle(SlotGrabComponentAttr attr, final String hour, final long uid) {
        long actId = attr.getActId();
        String challengerKey = makeKey(attr, String.format(CHALLENGER_RECORD, hour, uid));
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        String value = redisTemplate.opsForValue().get(challengerKey);
        if (!StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            log.error("handleDelaySettle challenge record invalid: uid={}, record value={}", uid, value);
            return;
        }

        ChallengeRecord record = JSON.parseObject(value, ChallengeRecord.class);
        if (record == null || record.getState() != 0) {
            log.error("handleDelaySettle challenge record invalid: uid={}, record value={}", uid, value);
            return;
        }

        String time = DateFormatUtils.format(record.startTime, "mmss");
        String scoreKey = makeKey(attr, String.format(CHALLENGE_SCORE, hour, uid, time));
        try {
            String slotMemberKey = makeKey(attr, String.format(SLOT_MEMBER_KEY, hour, record.slot));
            List<String> rs = redisTemplate.execute(settleScript, List.of(slotMemberKey, scoreKey), String.valueOf(uid), String.valueOf(record.startTime.getTime()));
            if (rs != null && rs.size() == 2) {
                String slotMemberValue = rs.get(0);
                String newSlotMemberValue = rs.get(1);

                SlotMember slotMember = JSON.parseObject(slotMemberValue, SlotMember.class);
                SlotMember newSlotMember = JSON.parseObject(newSlotMemberValue, SlotMember.class);

                long userUid = newSlotMember.user;
                long opponentUserUid = slotMember.user;
                long opponentAnchorUid = slotMember.anchor;
                long score = newSlotMember.score;
                long opponentScore = slotMember.score;

                record.setUserUid(userUid);
                record.setOpponentAnchorUid(opponentAnchorUid);
                record.setOpponentUserUid(opponentUserUid);
                record.setScore(score);
                record.setOpponentScore(opponentScore);
                record.setState(1);

                String recordValue = JSON.toJSONString(record);

                if (score == 0 || opponentScore == 0) {
                    redisTemplate.opsForValue().set(challengerKey, recordValue, attr.getShowDuration(), TimeUnit.SECONDS);
                } else {
                    byte[] recordValueBytes = recordValue.getBytes(StandardCharsets.UTF_8);
                    String hourRecordKey = makeKey(attr, String.format(HOUR_CHALLENGE_RECORD, hour));
                    String anchorRecordKey = makeKey(attr, String.format(ANCHOR_CHALLENGE_RECORD, uid));
                    String userRecordKey = makeKey(attr, String.format(USER_CHALLENGE_RECORD, userUid));
                    String opponentAnchorRecordKey = makeKey(attr, String.format(ANCHOR_CHALLENGED_RECORD, opponentAnchorUid));
                    String opponentUserRecordKey = makeKey(attr, String.format(USER_CHALLENGED_RECORD, opponentUserUid));
                    redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                        // 顶部的本小时轮播只播挑战成功的
                        if (score > opponentScore) {
                            connection.lPush(hourRecordKey.getBytes(StandardCharsets.UTF_8), recordValueBytes);
                        }
                        connection.lPush(anchorRecordKey.getBytes(StandardCharsets.UTF_8), recordValueBytes);
                        connection.lPush(userRecordKey.getBytes(StandardCharsets.UTF_8), recordValueBytes);
                        connection.lPush(opponentAnchorRecordKey.getBytes(StandardCharsets.UTF_8), recordValueBytes);
                        connection.lPush(opponentUserRecordKey.getBytes(StandardCharsets.UTF_8), recordValueBytes);
                        // 更新记录
                        connection.setEx(challengerKey.getBytes(StandardCharsets.UTF_8), attr.getShowDuration(), recordValueBytes);
                        return null;
                    });
                }

                // 挑战成功发组件广播
                if (score > opponentScore) {
                    long bannerId = score >= attr.getHighlightScore() ? 5088002 : 5088001;
                    List<Long> uids = List.of(record.anchorUid, record.userUid, record.opponentAnchorUid, record.opponentUserUid);
                    Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(uids, true);
                    ChallengeRecordDetail detail = new ChallengeRecordDetail(record);
                    setUserInfo(detail, userInfos);
                    GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                            .setActId(actId)
                            .setBannerId(bannerId)
                            .setAnchorUid(record.anchorUid)
                            .setAnchorNick(detail.anchorNick)
                            .setAnchorLogo(detail.anchorAvatar)
                            .setAnchorScore(score)
                            .setUserUid(detail.userUid)
                            .setUserNick(detail.userNick)
                            .setUserLogo(detail.userAvatar)
                            .setBannerType(0)
                            .setJsonData(JSON.toJSONString(detail));

                    GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                            .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                            .setBannerBroadcast(bannerBroadcast).build();

                    svcSDKService.broadcastTemplate(InterstellarTreasureComponent.getTemplateByBusiId(attr.getBusiId()), msg);
                    log.info("finish settle challenge send broadcast:{} detail:{}", bannerId, detail);
                }

                // 结算结束后触发更新挂件
                threadPoolManager.get(ThreadPoolNames.CMPT_BUILD_LAYER).execute(() -> {
                    freshLayer(attr, record.sid, record.ssid);
                });

                // 延迟事件 - 展示结束后触发更新挂件
                long expiredMills = System.currentTimeMillis() + (attr.getShowDuration() + 1) * DateUtils.MILLIS_PER_SECOND;
                publishDelayEvent(attr, "refresh_layer", new ChannelInfo(record.sid, record.ssid), expiredMills);

                log.info("finish settle challenge with record:{}", recordValue);
            }
        } catch (Exception e) {
            log.error("handleDelaySettle fail:", e);
        }
    }

    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        SlotGrabComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return ext;
        }

        if (LayerItemTypeKey.ANCHOR.equals(layerMemberItem.getItemType())) {
            String memberId = layerMemberItem.getMemberId();
            long anchorUid = Long.parseLong(memberId);
            LayerGrabInfo grabInfo = getLayerGrabInfo(attr, anchorUid);
            if (grabInfo == null) {
                return ext;
            }

            if (ext == null) {
                ext = new HashMap<>(10);
            }

            ext.put("slotGrab", grabInfo);
            return ext;
        }

        if (StringUtils.isNotEmpty(attr.getNotAnchorItemKey()) && attr.getNotAnchorItemKey().equals(layerMemberItem.getItemType())) {
            String memberId = layerMemberItem.getMemberId();
            Pair<Long, Long> channel = getChannelInfo(memberId, layerMemberItem.getItemType());
            if (channel == null) {
                return ext;
            }

            long sid = channel.getLeft(), ssid = channel.getRight();
            OnlineChannelInfo channelInfo = onlineChannelService.get(sid, ssid);
            if (channelInfo == null || CollectionUtils.isEmpty(channelInfo.getEffectAnchorId())) {
                return ext;
            }

            List<LayerGrabInfo> grabInfos = batchGetLayerGrabInfos(attr, channelInfo.getEffectAnchorId());
            if (CollectionUtils.isEmpty(grabInfos)) {
                return ext;
            }

            if (ext == null) {
                ext = new HashMap<>(10);
            }

            ext.put("slotGrabs", grabInfos);
            return ext;
        }

        return ext;
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void handleSendGift(SendGiftEvent event, SlotGrabComponentAttr attr) {
        final long actId = attr.getActId(), uid = event.getRecvUid();
        if (!attr.getGiftIdScores().containsKey(event.getGiftId())) {
            if (log()) {
                log.info("handleSendGift giftId not match:{}", event.getGiftId());
            }
            return;
        }

        Date eventTime = event.getEventTime();
        if (!SysEvHelper.isDeploy() || commonService.isGrey(actId)) {
            eventTime = commonService.getNow(actId);
        }

        firstNoticeIfNotNotice(attr, uid, eventTime);

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));

        final String hour = DateFormatUtils.format(eventTime, DateUtil.PATTERN_TYPE7);

        String challengerKey = makeKey(attr, String.format(CHALLENGER_RECORD, hour, uid));
        String value = redisTemplate.opsForValue().get(challengerKey);
        if (!StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            if (log()) {
                log.info("handleSendGift record invalid:{}", value);
            }
            return;
        }

        ChallengeRecord record = JSON.parseObject(value, ChallengeRecord.class);
        if (record == null || record.getState() != 0) {
            if (log()) {
                log.info("handleSendGift record state invalid:{}", value);
            }
            return;
        }

        if (record.getStartTime().after(eventTime) || record.getEndTime().before(eventTime)) {
            if (log()) {
                log.info("handleSendGift not in challenge time:{} {}", value, eventTime);
            }
            return;
        }

        String time = DateFormatUtils.format(record.startTime, "mmss");
        String seqKey = makeKey(attr, String.format(GIFT_SEQ, event.getSeq()));
        String scoreKey = makeKey(attr, String.format(CHALLENGE_SCORE, hour, uid, time));
        long userUid = event.getSendUid(), giftScore = event.getGiftNum() * attr.getGiftIdScores().get(event.getGiftId());
        Long rs = redisTemplate.execute(updateScoreScript, List.of(seqKey, scoreKey), String.valueOf(userUid), String.valueOf(giftScore));
        log.info("handleSendGift update score with uid:{} userUid:{} giftId:{}, score:{}, rs:{}", uid, userUid, event.getGiftId(), giftScore, rs);
    }

    public void handleAnchorStartShow(StartShowEvent event, SlotGrabComponentAttr attr) {
        log.info("handleAnchorStartShow with {}", event);
        if (!Objects.equals(attr.getBusiId(), event.getBusiId().getValue())) {
            return;
        }

        firstNoticeIfNotNotice(attr, event.getUid(), event.getEventTime());
    }

    private void firstNoticeIfNotNotice(SlotGrabComponentAttr attr, long anchorUid, Date eventTime) {
        boolean inGrabTime = inGrabTime(attr, eventTime);
        if (!inGrabTime) {
            return;
        }

        String key = makeKey(attr, ANCHOR_FIRST_NOTICE);
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        boolean rs = redisTemplate.opsForHash().putIfAbsent(key, String.valueOf(anchorUid), DateUtil.getNowYyyyMMddHHmmss());
        if (rs) {
            GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                    .setActId(attr.getActId())
                    .setNoticeType(PBCommonNoticeType.SLOT_FIRST_NOTICE)
                    .setNoticeValue(StringUtils.EMPTY);

            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                    .setCommonNoticeResponse(panel).build();
            svcSDKService.unicastUid(anchorUid, msg);
            log.info("first notice send unicast with uid:{}", anchorUid);
        }
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        SlotGrabComponentAttr attr = getComponentAttr(request.getActId(), request.getCmptIndex());
        if (attr == null) {
            return super.commonOperatePbRequest(request);
        }

        final long actId = request.getActId(), uid = request.getOpUid();
        String opType = request.getOpType();
        if ("enter_channel".equals(opType)) {
            Date now = commonService.getNow(actId);
            String dateStr = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2);
            String key = makeKey(attr, String.format(USER_AWARD_NOTICE, dateStr));
            StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
            String noticeValue = redisTemplate.<String, String>opsForHash().get(key, String.valueOf(uid));
            if (StringUtils.isNotEmpty(noticeValue)) {
                Long rs = redisTemplate.opsForHash().delete(key, String.valueOf(uid));
                if (rs > 0) {
                    GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                            .setActId(attr.getActId())
                            .setNoticeType(PBCommonNoticeType.AWARD_NOTICE)
                            .setNoticeValue(noticeValue);

                    GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                            .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                            .setCommonNoticeResponse(panel).build();
                    svcSDKService.unicastUid(uid, msg);
                    log.info("commonOperatePbRequest send award notice with {} {}", uid, noticeValue);
                }
            }
        }

        return super.commonOperatePbRequest(request);
    }

    @RequestMapping("grab/slot")
    public Response<?> grabSlot(@RequestParam(name = "actId") int actId,
                                @RequestParam(name = "cmptIndex") int cmptIndex,
                                @RequestParam(name = "slot") int slot,
                                long sid, long ssid) {
        SlotGrabComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        final Date now = commonService.getNow(actId);
        boolean inGrabTime = inGrabTime(attr, now);
        if (!inGrabTime) {
            return Response.fail(400, "当前住在卡位战开始时段！");
        }

        if (slot > attr.getSlotSize()) {
            return Response.fail(400, "参数错误");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "请先登录");
        }

        long sign = signedService.getSignedSidByBusiId(uid, attr.getBusiId());
        if (sign <= 0) {
            return Response.fail(403, "签约后才能发起挑战");
        }

        OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
        if (onlineChannelInfo == null) {
            return Response.fail(409, "上座后才能发起挑战");
        }

        String hour = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE7);
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        long hourEndTime = cn.hutool.core.date.DateUtil.endOfHour(now).getTime();
        long expiredTime = Math.min(hourEndTime, now.getTime() + attr.getChallengeDuration() * 1000L);
        String challengerKey = makeKey(attr, String.format(CHALLENGER_RECORD, hour, uid));
        ChallengeRecord record = new ChallengeRecord();
        record.setSlot(slot);
        record.setAnchorUid(uid);
        record.setSid(sid);
        record.setSsid(ssid);
        record.setState(0);
        record.setStartTime(now);
        record.setEndTime(new Date(expiredTime));
        boolean add = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(challengerKey, JSON.toJSONString(record), 1, TimeUnit.HOURS));
        if (!add) {
            return Response.fail(400, "请勿重复发起挑战");
        }

        // 添加延迟处理
        String delayKey = makeKey(attr, String.format(CHALLENGER, hour));
        redisTemplate.opsForZSet().add(delayKey, String.valueOf(uid), expiredTime + 200);

        return Response.success(1);
    }

    @GetMapping("my/status")
    public Response<JSONObject> queryMyStatus(@RequestParam(name = "actId") int actId,
                                              @RequestParam(name = "cmptIndex") int cmptIndex, long sid, long ssid) {
        SlotGrabComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        final long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        Date now = commonService.getNow(actId);
        String hour = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE7);
        String challengerKey = makeKey(attr, String.format(CHALLENGER_RECORD, hour, uid));
        String value = redisTemplate.opsForValue().get(challengerKey);
        JSONObject data = new JSONObject(10);
        UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
        if (userInfo != null) {
            data.put("nick", userInfo.getNick());
            data.put("avatar", userInfo.getHdLogo());
        }

        // 判断是否在玩法时间内
        boolean inGrabTime = inGrabTime(attr, now);
        data.put("inGrabTime", inGrabTime ? 1 : 0);

        if (StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            ChallengeRecord record = JSON.parseObject(value, ChallengeRecord.class);
            if (record != null) {
                data.put("state", 7);
                data.put("sid", record.sid);
                data.put("ssid", record.ssid);
                return Response.success(data);
            }
        }

        long sign = signedService.getSignedSidByBusiId(uid, attr.getBusiId());
        if (sign <= 0) {
            data.put("state", 0);
            return Response.success(data);
        }

        OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
        if (onlineChannelInfo == null) {
            data.put("state", 1);
            return Response.success(data);
        }

        List<Long> anchorUids = onlineChannelInfo.getEffectAnchorId();
        if (CollectionUtils.isNotEmpty(anchorUids) && anchorUids.contains(uid)) {
            data.put("state", 3);
            return Response.success(data);
        }

        data.put("state", 1);
        return Response.success(data);
    }

    @GetMapping("slots")
    public Response<List<SlotMemberInfo>> querySlots(@RequestParam(name = "actId") int actId,
                                  @RequestParam(name = "cmptIndex") int cmptIndex,
                                  @RequestParam(name = "hour", required = false, defaultValue = "0") int hour) {
        SlotGrabComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        if (hour == 0) {
            Date now = commonService.getNow(actId);
            if (!actInfoService.inActTime(now, (long) actId)) {
                return Response.fail(400, "not in activity time");
            }

            LocalTime curTime = DateUtil.toLocalDateTime(now).toLocalTime();
            if (curTime.isBefore(attr.getStartTime()) || curTime.isAfter(attr.getEndTime())) {
                return Response.fail(433, "not in slot-grab time");
            }

            hour = Integer.parseInt(DateFormatUtils.format(now, DateUtil.PATTERN_TYPE7));

            // 判断是否主持挑战中
            long uid = getLoginYYUid();
            if (uid > 0) {
                String challengerKey = makeKey(attr, String.format(CHALLENGER_RECORD, hour, uid));
                String value = redisTemplate.opsForValue().get(challengerKey);
                if (StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
                    return Response.fail(1, "当前挑战中，前往查看");
                }
            }
        } else {
            //校验hour是否合法
            Date date = cn.hutool.core.date.DateUtil.parse(String.valueOf(hour), DateUtil.PATTERN_TYPE7);
            boolean inGrabTime = inGrabTime(attr, date);

            if (!inGrabTime) {
                return Response.fail(433, "hour not in slot-grab time");
            }
        }

        List<String> slotMemberKeys = new ArrayList<>(attr.getSlotSize());
        for (int i = 1; i <= attr.getSlotSize(); i++) {
            slotMemberKeys.add(makeKey(attr, String.format(SLOT_MEMBER_KEY, hour, i)));
        }

        List<String> memberValues = redisTemplate.opsForValue().multiGet(slotMemberKeys);

        if (memberValues == null || memberValues.size() != attr.getSlotSize()) {
            return Response.fail(500, "data invalid");
        }

        Map<Integer, SlotMember> slotMembers = new HashMap<>(attr.getSlotSize());
        Set<Long> uids = new HashSet<>(attr.getSlotSize() * 2);
        for (int i = 0; i < attr.getSlotSize(); i++) {
            String memberValue = memberValues.get(i);
            if (!StringUtils.startsWith(memberValue, StringUtil.OPEN_BRACE)) {
                SlotMember slotMember = new SlotMember();
                slotMembers.put(i + 1, slotMember);
                continue;
            }

            SlotMember slotMember = JSON.parseObject(memberValue, SlotMember.class);
            slotMembers.put(i + 1, slotMember);
            uids.add(slotMember.anchor);
            uids.add(slotMember.user);
        }

        List<SlotMemberInfo> result = new ArrayList<>(attr.getSlotSize());
        Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(List.copyOf(uids), false);
        for (int i = 0; i < memberValues.size(); i++) {
            int slot = i + 1;
            SlotMember slotMember = slotMembers.get(slot);
            SlotMemberInfo item = new SlotMemberInfo(slotMember);
            item.setSlot(slot);
            setUserInfo(item, userInfoMap);

            long packageId = slotMember.packageId;
            if (packageId == attr.getBigPackageId()) {
                item.setBigAward(1);
            }
            AwardProp prop = attr.getAwardProps().get(packageId);
            if (prop != null) {
                item.setAwardName(prop.getAwardName());
                item.setAwardIcon(prop.getAwardIcon());
            }

            result.add(item);
        }

        return Response.success(result);
    }

    /**
     * 全服当前小时的挑战记录
     * @param actId
     * @param cmptIndex
     * @return
     */
    @GetMapping("challenge/record")
    public Response<List<ChallengeRecordDetail>> queryChallengeRecord(@RequestParam(name = "actId") int actId,
                                                  @RequestParam(name = "cmptIndex") int cmptIndex) {
        SlotGrabComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        final Date now = commonService.getNow(actId);
        boolean inGrabTime = inGrabTime(attr, now);
        if (!inGrabTime) {
            return Response.fail(400, "not in slot-grab time");
        }

        String hour = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE7);
        String hourKey = makeKey(attr, String.format(HOUR_CHALLENGE_RECORD, hour));
        List<ChallengeRecordDetail> details = queryChallengeRecord(actId, hourKey);

        return Response.success(details);
    }

    /**
     * 我的挑战（被挑战）记录
     * @param actId
     * @param cmptIndex
     * @param type
     * @return
     */
    @GetMapping("my/challenge/record")
    public Response<List<ChallengeRecordDetail>> queryUserChallengeRecord(@RequestParam(name = "actId") int actId,
                                                                          @RequestParam(name = "cmptIndex") int cmptIndex,
                                                                          @RequestParam(name = "type") int type) {
        SlotGrabComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        long uid = getLoginYYUid();

        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        boolean isSign = signedService.getSignedSidByBusiId(uid, attr.getBusiId()) > 0;
        final String key;
        if (isSign) {
            if (type == 0) {
                key = makeKey(attr, String.format(ANCHOR_CHALLENGE_RECORD, uid));
            } else {
                key = makeKey(attr, String.format(ANCHOR_CHALLENGED_RECORD, uid));
            }
        } else {
            if (type == 0) {
                key = makeKey(attr, String.format(USER_CHALLENGE_RECORD, uid));
            } else {
                key = makeKey(attr, String.format(USER_CHALLENGED_RECORD, uid));
            }
        }

        List<ChallengeRecordDetail> details = queryChallengeRecord(actId, key);

        return Response.success(details);
    }

    @GetMapping("award/record")
    public Response<List<AwardRecordDetail>> queryAwardRecord(@RequestParam(name = "actId") int actId,
                                              @RequestParam(name = "cmptIndex") int cmptIndex,
                                              @RequestParam(name = "type") int type) {
        SlotGrabComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        final String key;
        if (type == 0) {
            key = makeKey(attr, GLOBAL_AWARD);
        } else {
            long uid = getLoginYYUid();
            if (uid <= 0) {
                return Response.fail(401, "login is need!");
            }

            key = makeKey(attr, String.format(USER_AWARD, uid));
        }

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        List<String> records = redisTemplate.opsForList().range(key, 0, 49);
        if (CollectionUtils.isEmpty(records)) {
            return Response.success(Collections.emptyList());
        }

        List<AwardRecordDetail> details = records.stream().map(record -> JSON.parseObject(record, AwardRecord.class)).map(AwardRecordDetail::new).toList();
        Set<Long> uids = new HashSet<>(details.size() * 2);
        for (AwardRecordDetail detail : details) {
            uids.add(detail.userUid);
            uids.add(detail.anchorUid);
        }

        Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(uids), false);
        for (AwardRecordDetail detail : details) {
            UserBaseInfo userInfo = userInfos.get(detail.userUid);
            if (userInfo != null) {
                detail.setUserNick(userInfo.getNick());
                detail.setUserAvatar(userInfo.getHdLogo());
            }

            UserBaseInfo anchorInfo = userInfos.get(detail.anchorUid);
            if (anchorInfo != null) {
                detail.setAnchorNick(anchorInfo.getNick());
                detail.setAnchorAvatar(anchorInfo.getHdLogo());
            }

            AwardProp prop = attr.getAwardProps().get(detail.packageId);
            if (prop != null) {
                detail.setAwardName(prop.getAwardName());
                detail.setAwardIcon(prop.getAwardIcon());
            }

            detail.setBigAward(detail.packageId == attr.getBigPackageId() ? 1 : 0);
        }

        return Response.success(details);
    }

    public void refreshLayer(SlotGrabComponentAttr attr, Object event) {
        if (event instanceof ChannelInfo channel) {
            freshLayer(attr, channel.getSid(), channel.getSsid());
        }
    }

    private void freshLayer(SlotGrabComponentAttr attr, long sid, long ssid) {
        OnlineChannelInfo channelInfo = new OnlineChannelInfo();
        channelInfo.setSid(sid);
        channelInfo.setSsid(ssid);
        broActLayerService.broChannel(attr.getActId(), channelInfo);
        log.info("freshLayer with sid:{}, ssid:{}", sid, ssid);
    }

    private List<ChallengeRecordDetail> queryChallengeRecord(long actId, String key) {
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        List<String> records = redisTemplate.opsForList().range(key, 0, 49);
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        List<ChallengeRecordDetail> details = records.stream().map(record -> JSON.parseObject(record, ChallengeRecord.class)).map(ChallengeRecordDetail::new).toList();
        Set<Long> uids = new HashSet<>(details.size() * 4);
        for (ChallengeRecordDetail detail : details) {
            uids.add(detail.userUid);
            uids.add(detail.anchorUid);
            uids.add(detail.opponentUserUid);
            uids.add(detail.opponentAnchorUid);
        }

        Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(uids), false);
        for (ChallengeRecordDetail detail : details) {
            setUserInfo(detail, userInfos);
        }

        return details;
    }

    private boolean inGrabTime(SlotGrabComponentAttr attr, Date date) {
        if (!actInfoService.inActTime(date, attr.getActId())) {
            return false;
        }

        LocalTime curTime = DateUtil.toLocalDateTime(date).toLocalTime();
        return !curTime.isBefore(attr.getStartTime()) && !curTime.isAfter(attr.getEndTime());
    }

    private LayerGrabInfo getLayerGrabInfo(SlotGrabComponentAttr attr, long anchorUid) {
        final long actId = attr.getActId();
        Date now = commonService.getNow(actId);
        String hour = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE7);
        String challengeKey = makeKey(attr, String.format(CHALLENGER_RECORD, hour, anchorUid));
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        String value = redisTemplate.opsForValue().get(challengeKey);
        if (!StringUtils.startsWith(value, StringUtil.OPEN_BRACE)) {
            return null;
        }

        ChallengeRecord record = JSON.parseObject(value, ChallengeRecord.class);
        if (record == null) {
            return null;
        }

        if (record.getState() == 0) {
            String slotMemberKey = makeKey(attr, String.format(SLOT_MEMBER_KEY, hour, record.getSlot()));
            String slotMemberValue = redisTemplate.opsForValue().get(slotMemberKey);
            long slotScore = 0;
            if (StringUtils.startsWith(slotMemberValue, StringUtil.OPEN_BRACE)) {
                SlotMember slotMember = JSON.parseObject(slotMemberValue, SlotMember.class);
                if (slotMember != null) {
                    slotScore = slotMember.score;
                }
            }
            record.setOpponentScore(slotScore);

            String time = DateFormatUtils.format(record.startTime, "mmss");
            String scoreKey = makeKey(attr, String.format(CHALLENGE_SCORE, hour, anchorUid, time));
            String scoreValue = redisTemplate.opsForValue().get(scoreKey);
            long score = Convert.toLong(scoreValue, 0L);
            record.setScore(score);

            if (score > 0) {
                Set<String> members = redisTemplate.opsForZSet().reverseRange(scoreKey + ":contribute", 0, 0);
                if (CollectionUtils.isNotEmpty(members)) {
                    long userUid = members.stream().findFirst().map(Long::parseLong).orElse(0L);
                    record.setUserUid(userUid);
                }
            }
        }

        Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.of(anchorUid, record.getUserUid()), true);
        UserBaseInfo anchorInfo = userInfos.get(anchorUid);
        UserBaseInfo userInfo = userInfos.get(record.getUserUid());

        LayerGrabInfo result = new LayerGrabInfo(record);

        if (anchorInfo != null) {
            result.setAnchorNick(anchorInfo.getNick());
            result.setAnchorAvatar(anchorInfo.getHdLogo());
        }

        if (userInfo != null) {
            result.setUserNick(userInfo.getNick());
            result.setUserAvatar(userInfo.getHdLogo());
        }


        final int countdown;
        if (record.getState() == 0) {
            countdown = (int) Math.max(0, (record.getEndTime().getTime() - now.getTime()) / DateUtils.MILLIS_PER_SECOND);
        } else {
            Long ttl = redisTemplate.getExpire(challengeKey, TimeUnit.SECONDS);
            countdown = ttl == null ? 0 : Math.max(1, ttl.intValue());
        }
        result.setCountdown(countdown);

        return result;
    }

    private List<LayerGrabInfo> batchGetLayerGrabInfos(SlotGrabComponentAttr attr, List<Long> uids) {
        long actId = attr.getActId();
        Date now = commonService.getNow(actId);
        if (!inGrabTime(attr, now)) {
            return Collections.emptyList();
        }

        String hour = DateFormatUtils.format(now, DateUtil.PATTERN_TYPE7);

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        List<String> recordKeys = uids.stream().map(uid -> makeKey(attr, String.format(CHALLENGER_RECORD, hour, uid))).toList();
        List<String> recordValues = redisTemplate.opsForValue().multiGet(recordKeys);
        if (CollectionUtils.isEmpty(recordValues)) {
            return Collections.emptyList();
        }

        List<Long> challengeAnchors = new ArrayList<>(uids.size());
        List<String> scoreKeys = new ArrayList<>(uids.size());
        List<String> contributeKeys = new ArrayList<>(uids.size());
        Map<Long, ChallengeRecord> recordMap = new HashMap<>(uids.size());
        List<Integer> slots = new ArrayList<>(attr.getSlotSize());
        List<Long> showAnchors = new ArrayList<>(uids.size());
        Set<Long> allUids = new HashSet<>(uids.size() * 2);
        for (String recordValue : recordValues) {
            if (StringUtils.startsWith(recordValue, StringUtil.OPEN_BRACE)) {
                ChallengeRecord record = JSON.parseObject(recordValue, ChallengeRecord.class);
                recordMap.put(record.anchorUid, record);
                allUids.add(record.anchorUid);
                if (record.getState() == 0) {
                    challengeAnchors.add(record.anchorUid);
                    String time = DateFormatUtils.format(record.startTime, "mmss");
                    String scoreKey = makeKey(attr, String.format(CHALLENGE_SCORE, hour, record.anchorUid, time));
                    scoreKeys.add(scoreKey);
                    contributeKeys.add(scoreKey + ":contribute");

                    int slot = record.getSlot();
                    if (!slots.contains(slot)) {
                        slots.add(slot);
                    }
                } else {
                    showAnchors.add(record.anchorUid);
                    allUids.add(record.userUid);
                }
            }
        }

        List<Long> scores = batchGetChallengeScores(redisTemplate, scoreKeys);
        Map<Integer, Long> slotScores = batchGetSlotScore(attr, hour, slots);
        List<Long> contributeUsers = batchGetTopContributeUids(redisTemplate, contributeKeys);
        for (int i = 0; i < challengeAnchors.size(); i++) {
            long uid = challengeAnchors.get(i);
            ChallengeRecord record = recordMap.get(uid);
            int slot = record.getSlot();
            long slotScore = slotScores.getOrDefault(slot, 0L);
            long score = scores.size() > i ? scores.get(i) : 0;
            long contributeUid = contributeUsers.size() > i ? contributeUsers.get(i) : 0;
            record.setScore(score);
            record.setOpponentScore(slotScore);
            record.setUserUid(contributeUid);

            if (contributeUid > 0) {
                allUids.add(contributeUid);
            }
        }

        Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(allUids), true);
        Map<Long, Long> recordTtl = batchGetRecordTTl(attr, hour, showAnchors);
        List<LayerGrabInfo> result = new ArrayList<>(recordMap.size());
        for (ChallengeRecord record : recordMap.values()) {
            LayerGrabInfo info = new LayerGrabInfo(record);
            UserBaseInfo userInfo = userInfos.get(record.getUserUid());
            if (userInfo != null) {
                info.setUserNick(userInfo.getNick());
                info.setUserAvatar(userInfo.getHdLogo());
            }

            UserBaseInfo anchorInfo = userInfos.get(record.getAnchorUid());
            if (anchorInfo != null) {
                info.setAnchorNick(anchorInfo.getNick());
                info.setAnchorAvatar(anchorInfo.getHdLogo());
            }

            long countdown;
            if (record.getState() == 0) {
                countdown = (record.getEndTime().getTime() - now.getTime()) / 1000;
                countdown = Math.max(0L, countdown);
            } else {
                countdown = recordTtl.getOrDefault(record.getAnchorUid(), 1L);
            }

            info.setCountdown(countdown);

            result.add(info);
        }

        return result;
    }

    private Map<Integer, Long> batchGetSlotScore(SlotGrabComponentAttr attr, String hour, List<Integer> slots) {
        if (CollectionUtils.isEmpty(slots)) {
            return Collections.emptyMap();
        }

        List<String> slotMemberKeys = slots.stream().map(slot -> makeKey(attr, String.format(SLOT_MEMBER_KEY, hour, slot))).toList();
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        List<String> slotMemberValues = redisTemplate.opsForValue().multiGet(slotMemberKeys);
        if (CollectionUtils.isEmpty(slotMemberValues)) {
            return Collections.emptyMap();
        }

        Map<Integer, Long> result = new HashMap<>(slots.size());
        for (int i = 0; i < slots.size(); i++) {
            int slot = slots.get(i);
            String slotMemberValue = slotMemberValues.get(i);
            long score = 0;
            if (StringUtils.startsWith(slotMemberValue, StringUtil.OPEN_BRACE)) {
                SlotMember slotMember = JSON.parseObject(slotMemberValue, SlotMember.class);
                if (slotMember != null) {
                    score = slotMember.score;
                }
            }
            result.put(slot, score);
        }

        return result;
    }

    private List<Long> batchGetChallengeScores(StringRedisTemplate redisTemplate, List<String> scoreKeys) {
        List<String> scoreValues = redisTemplate.opsForValue().multiGet(scoreKeys);
        if (CollectionUtils.isEmpty(scoreValues)) {
            return Collections.emptyList();
        }

        List<Long> result = new ArrayList<>(scoreKeys.size());
        for (String scoreValue : scoreValues) {
            result.add(Convert.toLong(scoreValue, 0L));
        }

        return result;
    }

    private List<Long> batchGetTopContributeUids(StringRedisTemplate redisTemplate, List<String> contributeKeys) {
        if (CollectionUtils.isEmpty(contributeKeys)) {
            return Collections.emptyList();
        }

        if (contributeKeys.size() == 1) {
            Set<String> rs = redisTemplate.opsForZSet().reverseRange(contributeKeys.get(0), 0, 0);
            if (CollectionUtils.isEmpty(rs)) {
                return Collections.emptyList();
            }

            String value = rs.stream().findFirst().orElse(null);
            return Collections.singletonList(Convert.toLong(value, 0L));
        }

        List<Object> rs = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            for (String contributeKey : contributeKeys) {
                connection.zRevRange(contributeKey.getBytes(StandardCharsets.UTF_8), 0, 0);
            }
            return null;
        });

        if (CollectionUtils.isEmpty(rs)) {
            return Collections.emptyList();
        }

        List<Long> result = new ArrayList<>(contributeKeys.size());
        for (Object obj : rs) {
            if (obj instanceof Collection<?> collection) {
                if (CollectionUtils.isEmpty(collection)) {
                    result.add(0L);
                    continue;
                }

                result.add(Convert.toLong(collection.stream().findFirst().orElse(null), 0L));
                continue;
            }

            result.add(0L);
        }

        return result;
    }

    private Map<Long, Long> batchGetRecordTTl(SlotGrabComponentAttr attr, String hour, List<Long> showAnchors) {
        if (CollectionUtils.isEmpty(showAnchors)) {
            return Collections.emptyMap();
        }

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        if (showAnchors.size() == 1) {
            String recordKey = makeKey(attr, String.format(CHALLENGER_RECORD, hour, showAnchors.get(0)));
            Long ttl = redisTemplate.getExpire(recordKey, TimeUnit.SECONDS);
            ttl = ttl == null ? 1 : Math.max(ttl, 1L);
            return Map.of(showAnchors.get(0), ttl);
        }

        List<String> recordKeys = showAnchors.stream().map(uid -> makeKey(attr, String.format(CHALLENGER_RECORD, hour, uid))).toList();
        List<Object> rs = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            for (String recordKey : recordKeys) {
                connection.ttl(recordKey.getBytes(StandardCharsets.UTF_8), TimeUnit.SECONDS);
            }
            return null;
        });

        if (CollectionUtils.isEmpty(rs)) {
            return Collections.emptyMap();
        }

        Map<Long, Long> result = new HashMap<>(showAnchors.size());
        for (int i = 0; i < showAnchors.size(); i++) {
            long uid = showAnchors.get(i);
            long ttl = Convert.toLong(rs.get(i), 1L);
            ttl = Math.max(1L, ttl);
            result.put(uid, ttl);
        }

        return result;
    }

    private Pair<Long, Long> getChannelInfo(String memberId, String itemTypeKey) {
        switch (itemTypeKey) {
            case LayerItemTypeKey.HALL:
                if (!StringUtils.contains(memberId, StringUtil.AND)) {
                    return null;
                }
                String[] arr = memberId.split(StringUtil.AND);
                return Pair.of(Long.parseLong(arr[0]), Long.parseLong(arr[1]));
            case LayerItemTypeKey.ROOM:
                if (!StringUtils.isNumeric(memberId)) {
                    return null;
                }
                RoomInfo roomInfo = zhuiwanRoomInfoClient.roomInfoByRoomId(Integer.parseInt(memberId));
                if (roomInfo == null) {
                    return null;
                }

                return Pair.of(roomInfo.sid, roomInfo.ssid);
            default:
                return null;
        }
    }

    private void setUserInfo(ChallengeRecordDetail detail, Map<Long, UserBaseInfo> userInfos) {
        UserBaseInfo userInfo = userInfos.get(detail.userUid);
        if (userInfo != null) {
            detail.setUserNick(userInfo.getNick());
            detail.setUserAvatar(userInfo.getHdLogo());
        }

        userInfo = userInfos.get(detail.anchorUid);
        if (userInfo != null) {
            detail.setAnchorNick(userInfo.getNick());
            detail.setAnchorAvatar(userInfo.getHdLogo());
        }

        userInfo = userInfos.get(detail.opponentUserUid);
        if (userInfo != null) {
            detail.setOpponentUserNick(userInfo.getNick());
            detail.setOpponentUserAvatar(userInfo.getHdLogo());
        }

        userInfo = userInfos.get(detail.opponentAnchorUid);
        if (userInfo != null) {
            detail.setOpponentAnchorNick(userInfo.getNick());
            detail.setOpponentAnchorAvatar(userInfo.getHdLogo());
        }
    }

    private void setUserInfo(SlotMemberInfo item, Map<Long, UserBaseInfo> userInfos) {
        UserBaseInfo userInfo = userInfos.get(item.userUid);
        UserBaseInfo anchorInfo = userInfos.get(item.anchorUid);
        if (userInfo != null) {
            item.setUserNick(userInfo.getNick());
            item.setUserAvatar(userInfo.getHdLogo());
        }

        if (anchorInfo != null) {
            item.setAnchorNick(anchorInfo.getNick());
            item.setAnchorAvatar(anchorInfo.getHdLogo());
        }
    }

    @Data
    public static class ChallengeRecord {
        protected int slot;

        protected long anchorUid;

        protected long sid;

        protected long ssid;

        protected long userUid;

        protected long opponentAnchorUid;

        protected long opponentUserUid;

        protected long score;

        protected long opponentScore;

        /**
         * 0-挑战中，1-已结算展示中
         */
        protected int state = 0;

        protected Date startTime;

        protected Date endTime;
    }

    @Data
    public static class ChallengeRecordDetail extends ChallengeRecord {
        protected String anchorNick;

        protected String anchorAvatar;

        protected String userNick;

        protected String userAvatar;

        protected String opponentAnchorNick;

        protected String opponentAnchorAvatar;

        protected String opponentUserNick;

        protected String opponentUserAvatar;

        public ChallengeRecordDetail() {}

        public ChallengeRecordDetail(ChallengeRecord record) {
            this.slot = record.slot;
            this.anchorUid = record.anchorUid;
            this.sid = record.sid;
            this.ssid = record.ssid;
            this.userUid = record.userUid;
            this.opponentAnchorUid = record.opponentAnchorUid;
            this.opponentUserUid = record.opponentUserUid;
            this.score = record.score;
            this.opponentScore = record.opponentScore;
            this.state = record.state;
            this.startTime = record.startTime;
            this.endTime = record.endTime;
        }
    }

    @Data
    public static class AwardRecord {
        protected String hour;

        protected int slot;

        protected long anchorUid;

        protected long userUid;

        protected long packageId;
    }

    @Data
    public static class AwardRecordDetail extends AwardRecord {
        protected String anchorNick;

        protected String anchorAvatar;

        protected String userNick;

        protected String userAvatar;

        protected String awardName;

        protected String awardIcon;

        protected int bigAward;

        public AwardRecordDetail() {}

        public AwardRecordDetail(AwardRecord record) {
            this.hour = record.hour;
            this.slot = record.slot;
            this.anchorUid = record.anchorUid;
            this.userUid = record.userUid;
            this.packageId = record.packageId;
        }
    }

    @Data
    public static class SlotMember {
        protected long anchor = 0;

        protected long user = 0;

        protected long score = 0;

        protected long time = 0;

        protected long packageId = 0;

    }

    @Data
    public static class SlotMemberInfo {
        protected int slot;

        protected long anchorUid;

        protected String anchorNick;

        protected String anchorAvatar;

        protected long userUid;

        protected String userNick;

        protected String userAvatar;

        protected long score;

        protected int bigAward;

        protected String awardName;

        protected String awardIcon;

        public SlotMemberInfo() {}

        public SlotMemberInfo(SlotMember member) {
            this.anchorUid = member.anchor;
            this.userUid = member.user;
            this.score = member.score;
        }
    }

    @Data
    public static class LayerGrabInfo {
        protected long anchorUid;

        protected String anchorNick;

        protected String anchorAvatar;

        protected long userUid;

        protected String userNick;

        protected String userAvatar;

        protected int slot;

        protected long score;

        protected long slotScore;

        protected Date startTime;

        protected Date endTime;

        protected int state;

        protected long countdown;

        public LayerGrabInfo() {}

        public LayerGrabInfo(ChallengeRecord record) {
            this.slot = record.slot;
            this.anchorUid = record.anchorUid;
            this.userUid = record.userUid;
            this.score = record.score;
            this.slotScore = record.opponentScore;
            this.state = record.state;
            this.startTime = record.startTime;
            this.endTime = record.endTime;
        }
    }
}
