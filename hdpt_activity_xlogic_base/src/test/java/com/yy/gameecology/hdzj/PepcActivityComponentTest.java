package com.yy.gameecology.hdzj;


import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.client.thrift.UserinfoThriftClient;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.pepc.PepcActTeamService;
import com.yy.gameecology.activity.service.pepc.PepcAwardService;
import com.yy.gameecology.activity.service.pepc.PepcPushService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.mapper.pepc.PepcGameMemberMapper;
import com.yy.gameecology.common.db.mapper.pepc.PepcMatchAwardRecordExtMapper;
import com.yy.gameecology.hdzj.element.component.attr.PepcActPushComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.PepcActTeamComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.PepcPhaseComponentAttr;
import com.yy.gameecology.hdzj.element.component.xmodule.pepc.PepcActTeamComponent;
import com.yy.gameecology.hdzj.element.component.xmodule.pepc.PepcGameComponent;
import com.yy.gameecology.hdzj.element.component.xmodule.pepc.PepcPhaseComponent;
import com.yy.gameecology.hdzj.element.component.xmodule.pepc.PepcPushComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-6.properties", "classpath:env/local/application-inner.properties"})
public class PepcActivityComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "6");
    }

    @Autowired
    private PepcActTeamComponent pepcActTeamComponent;

    @Autowired
    private PepcPushComponent pepcPushComponent;

    @Autowired
    private PepcPhaseComponent pepcPhaseComponent;

    @Autowired
    private PepcActTeamService pepcActTeamService;

    @Autowired
    private PepcAwardService pepcActAwardService;

    @Autowired
    private UserinfoThriftClient userinfoThriftClient;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private PepcPushService pepcPushService;

    @Autowired
    private PepcMatchAwardRecordExtMapper pepcMatchAwardRecordExtMapper;

    @Autowired
    private PepcGameMemberMapper pepcGameMemberMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    protected ActInfoService actInfoService;

    @Autowired
    private PepcGameComponent pepcGameComponent;


    private long ActId = 2025046019;

    @Test
    public void createTeamTest() {
        long componentUseIndex = 810;
        Date now = commonService.getNow(ActId);

       PepcActTeamComponentAttr attr = pepcActTeamComponent.getUniqueComponentAttr(ActId);
//        PepcActPushComponentAttr attr = pepcPushComponent.getUniqueComponentAttr(ActId);
//        attr.setTeamDeclaration("sdfdsa");
//        attr.setTeamMemberMax(4);
//        attr.setTeamMemberMin(1);
//       var rep = pepcActTeamComponent.doCreateTeam(ActId, "", 1, 1745337600000L, 50013181L, attr);
//        ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(ActId);
//        log.info("rsp {} {} {}" ,activityInfo,activityInfo.getEndTime(), DateUtils.format(activityInfo.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
       var rsp =  pepcActTeamComponent.InSignUpTime(ActId,now);
        log.info("rsp {} " ,rsp);
        rsp =  pepcActTeamComponent.InSignUpTime(2025046019,now);
        log.info("rsp {} " ,rsp);

    }

    @Test
    public void getTeamTest() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);
        var rep = pepcActTeamComponent.getTteamList(ActId, 810, 50013181, /*AovPhase aovPhase,*/0, 30);
        log.info("rsp {}", rep.toString());
    }

    @Test
    public void applyTeamTest() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);
        var rep = pepcActTeamComponent.doCreateTeam(ActId, "ssss", 0, /*AovPhase aovPhase,*/1745337600000L, 50013181, attr);
        log.info("rsp {}", rep.toString());
    }

    @Test
    public void joinTeamTest() throws InterruptedException {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);
        var rep = pepcActTeamComponent.doJoinTeam(ActId, 810, "add", 814, 50013181, attr);
        log.info("rsp {}", rep);

       /* rep = pepcActTeamComponent.doJoinTeam(ActId, 810, "add", 51, 50015399, attr);
        log.info("rsp {}" ,rep.toString());*/

//        Thread.sleep(40000);

//        rep = pepcActTeamComponent.doJoinTeam(ActId, 810, "add", 12, 50013193, attr,aovPhase);
//        log.info("rsp {}" ,rep.toString());
    }

    @Test
    public void ApplySwitchTest() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 12;
        int rs = pepcActTeamService.turnNeedApply(teamId);
        log.info("turnApplySwitch turn with needApply:{} rs:{}", teamId, rs);
    }


    @Test
    public void applyListTest() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
        var rs = pepcActTeamComponent.applyList(ActId, 810);
        log.info("applyListTest turn with needApply:{} rs:{}", teamId, rs);
    }

    @Test
    public void applyTest() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
        var rs = pepcActTeamComponent.apply(ActId, 810, 27, 1);
        log.info("applyListTest turn with needApply:{} rs:{}", teamId, rs);
//        rs = pepcActTeamComponent.apply(ActId,810,7,0);
//        log.info("applyListTest turn with needApply:{} rs:{}",teamId, rs);
    }

    @Test
    public void kickOutMemberTest() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
        var rs = pepcActTeamComponent.kickOutMember(ActId, 810, 102, 1074561851);
        log.info("applyListTest turn with needApply:{} rs:{}", teamId, rs);
    }

    @Test
    public void quitTeam() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
        var rs = pepcActTeamComponent.quitTeam(ActId, 810, 104);
        log.info("applyListTest turn with needApply:{} rs:{}", teamId, rs);
    }

    @Test
    public void renameTeam() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("通道测试不通过");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
        var rs = pepcActTeamComponent.changeTeamName(ActId, 810, 9, "反方向的钟");
        log.info("applyListTest turn with needApply:{} rs:{}", teamId, rs);
    }

    @Test
    public void renameDeclarationTeam() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
        var rs = pepcActTeamComponent.changeDeclaration(ActId, 810, "反方向的钟");
        log.info("applyListTest turn with needApply:{} rs:{}", teamId, rs);
    }

    @Test
    public void getMyTeam() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
//        var rs = pepcActTeamComponent.myTeamInfo(ActId,810,"反方向的钟");
//        log.info("applyListTest turn with needApply:{} rs:{}",teamId, rs);
    }

    @Test
    public void queryLayerInfo() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
//        var rs = pepcActTeamComponent.myTeamInfo(ActId,810,"反方向的钟");
//        log.info("applyListTest turn with needApply:{} rs:{}",teamId, rs);
//        pepcActTeamComponent.queryChannelLayerInfo()
    }


    @Test
    public void subscribeInfo() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = new PepcActTeamComponentAttr();
        attr.setTeamDeclaration("sdfdsa");
        attr.setTeamMemberMax(4);
        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
//        var rs = pepcActTeamComponent.myTeamInfo(ActId,810,"反方向的钟");
//        log.info("applyListTest turn with needApply:{} rs:{}",teamId, rs);
//        pepcActTeamComponent.queryChannelLayerInfo()
//        pepcPushComponent.subscribe(ActId,810, 20250425);
    }

    @Test
    public void freshReward() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = pepcActTeamComponent.getUniqueComponentAttr(ActId);
//        attr.setTeamDeclaration("sdfdsa");
//        attr.setTeamMemberMax(4);
//        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
//        var rs = pepcActTeamComponent.myTeamInfo(ActId,810,"反方向的钟");
//        log.info("applyListTest turn with needApply:{} rs:{}",teamId, rs);
//        pepcActTeamComponent.queryChannelLayerInfo()hdzj_component_ui
//        pepcPushComponent.subscribe(ActId,810, 20250425);
        pepcActAwardService.refreshSubmittedAwardState(ActId);

    }

    @Test
    public void mockAddTeamMate() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = pepcActTeamComponent.getUniqueComponentAttr(ActId);
//        attr.setTeamDeclaration("sdfdsa");
//        attr.setTeamMemberMax(4);
//        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
//        var rs = pepcActTeamComponent.myTeamInfo(ActId,810,"反方向的钟");
//        log.info("applyListTest turn with needApply:{} rs:{}",teamId, rs);
//        pepcActTeamComponent.queryChannelLayerInfo()
//        pepcPushComponent.subscribe(ActId,810, 20250425);
        pepcActTeamComponent.mockBatchAddTeamate(ActId, 810, "");

    }

    @Test
    public void mockcreateTeam() {
        long componentUseIndex = 810;

        PepcActTeamComponentAttr attr = pepcActTeamComponent.getUniqueComponentAttr(ActId);
//        attr.setTeamDeclaration("sdfdsa");
//        attr.setTeamMemberMax(4);
//        attr.setTeamMemberMin(1);

//        int needApply = team.getNeedApply();
//        log.info("turnApplySwitch current:{}", needApply);
        long teamId = 3;
//        var rs = pepcActTeamComponent.myTeamInfo(ActId,810,"反方向的钟");
//        log.info("applyListTest turn with needApply:{} rs:{}",teamId, rs);
//        pepcActTeamComponent.queryChannelLayerInfo()
//        pepcPushComponent.subscribe(ActId,810, 20250425);
//        pepcActTeamComponent.mockAddTeam(ActId,810,"2632135114,50013181,1023722532");
        userinfoThriftClient.sendMarketSms("111111111111", 50013181L, "你被踢了");

    }

    @Test
    public void trySendStartGameNotice() throws InterruptedException {
//        pepcPushComponent.trySendStartGameNotice();
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> pepcPushComponent.trySendStartGameNotice());
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> pepcPushComponent.trySendStartGameNotice());
        Thread.sleep(40000);

    }

    @Test
    public void trySendSubscribeGameNotice() throws InterruptedException {
//        pepcPushComponent.trySendStartGameNotice();

        PepcActPushComponentAttr attr = new PepcActPushComponentAttr();

        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> pepcPushComponent.trySendSubscribePush());
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> pepcPushComponent.trySendSubscribePush());
        Thread.sleep(40000);
    }


    @Test
    public void tryReward() {
        PepcPhaseComponentAttr attr = pepcPhaseComponent.tryGetUniqueComponentAttr(ActId);
        Optional<PepcPhaseComponentAttr.PepcAwardInfo> awardInfo = attr.getAwardInfos()
                .stream()
                .filter(p -> p.getRank().equals(4) && p.getMemberAmount() == 2)
                .findFirst();
        Date d = new Date();
        log.info("reward {}", awardInfo.get());
        pepcActAwardService.tryGrantSettleAward(attr, awardInfo.get(), 4, 2, 2971104449L, d);
//        pepcActAwardService.tryGrantSettleAward(attr, awardInfo.get(), 7, 1, 3035264634L, d);
    }

    @Test
    public void OtherTest() {
//        List<Long> idList = new ArrayList<>();
//        idList.add(1L);
//        long count =  pepcMatchAwardRecordExtMapper.CountAwardRankingNum(ActId,idList);
//        log.info("count {}", count);
//        count =  pepcMatchAwardRecordExtMapper.CountAwardRankingNum(ActId, Arrays.asList(2L));
//        log.info("count {}", count);
//        count =  pepcMatchAwardRecordExtMapper.CountAwardRankingNum(ActId,Arrays.asList(3L,4L));
//        log.info("count {}", count);
//        count =  pepcMatchAwardRecordExtMapper.CountAwardRankingNum(ActId,Arrays.asList(5L,6L,7l,8L));
//        log.info("count {}", count);


////        pepcPushService.sendStatisticNotice(attr, commonService.getNow(ActId));
//        List<Long> idList = new ArrayList<>();
//        idList.add(47L);
////        pepcMatchAwardRecordExtMapper.ReBatchUpdateAwardRecordState(idList,0,30,10);
//       long rsp =  pepcGameMemberMapper.countSucGameUidCount(ActId,80L);
//        log.info("count {}", rsp);
//
//        rsp =  pepcGameMemberMapper.countSucGameUidCount(ActId,80004L);
//        log.info("count {}", rsp);

//        var rsp = pepcGameComponent.queryLayerStatus("12343455",ActId,1538657792L,2830358845L);
//        log.info("rsp {}", rsp);

       var rsp1 = pepcActTeamComponent.queryChannelLayerInfo(ActId,810,1538657792L,2830358845L);
       log.info("rsp {}", rsp1);



    }


}
