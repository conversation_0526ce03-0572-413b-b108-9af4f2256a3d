package com.yy.gameecology.activity.worker.subscriber;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.JiaoyouComboEndEvent;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.MqConst;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import com.yy.thrift.hdztranking.RoleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * 交友连送事件
 *
 * <AUTHOR>
 * @since 2023/7/7 17:05
 **/
@ConditionalOnExpression(Const.EXPRESSION_NOT_HISTORY)
@Component
@Slf4j
public class JiaoyouComboEndKafkaConsumer {
    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    @KafkaListener(containerFactory = "jiaoyouWxKafkaContainerFactory", id = "jy_wx_kafka_comboEnd",
            topics = MqConst.JY_COMBO_END_TOPIC,
            groupId = "${kafka.jiaoyou.wx.group}")
    public void onJiaoyouComboEndMessageFromWx(ConsumerRecord<String, String> consumerRecord) {
        onJiaoyouComboEndMessage("wx", consumerRecord.value());
    }

    @KafkaListener(containerFactory = "jiaoyouSzKafkaContainerFactory", id = "jy_sz_kafka_comboEnd",
            topics = MqConst.JY_COMBO_END_TOPIC,
            groupId = "${kafka.jiaoyou.sz.group}")
    public void onJiaoyouComboEndMessageFromSz(ConsumerRecord<String, String> consumerRecord) {
        onJiaoyouComboEndMessage("sz", consumerRecord.value());
    }

    private void onJiaoyouComboEndMessage(String from, String payload) {
        log.info("onJiaoyouComboEndMessage from={},payload={}", from, payload);
        try {
            JiaoyouComboEndEvent event = JSON.parseObject(payload, JiaoyouComboEndEvent.class);
            hdzjEventDispatcher.notify(event.getSendUid(), RoleType.USER, event, event.getSeqId());
        } catch (Exception ex) {
            log.error("onJiaoyouComboEndMessage error,from={},payload={}", from, payload, ex);
        }
    }
}
