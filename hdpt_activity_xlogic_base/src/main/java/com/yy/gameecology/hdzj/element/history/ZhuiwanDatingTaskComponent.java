package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRoleId;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.TaskConfig;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.ZhuiwanDatingTaskComponentAttr;
import com.yy.gameecology.hdzj.element.redis.ReceiveAwardComponent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * desc:追玩约会插件
 *
 * @createBy 曾文帜
 * @create 2021-08-10 19:17
 **/
@UseRedisStore
@Component
public class ZhuiwanDatingTaskComponent extends BaseActComponent<ZhuiwanDatingTaskComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ReceiveAwardComponent receiveAwardComponent;

    /**
     * 日任务#送礼累积分数任务#送礼累积分数
     * %s 日期编码 yyyyMMdd
     */
    private static final String SEND_GIFT_SCORE_DAY_TASK = "send_gift_score_day_task:%s";

    /**
     * 日任务#送礼累积主播数任务#送礼累积主播数
     * %s 日期编码 yyyyMMdd
     */
    private static final String SEND_GIFT_ANCHOR_AMOUNT_DAY_TASK = "send_anchor_amount_day_task:%s";

    /**
     * 送礼uid记录
     * %s:%s_%s 日期编码 yyyyMMdd,用户uid,主播uid
     */
    private static final String SEND_ANCHOR_UID_RECORD_DAY_TASK = "send_anchor_uid_record_day_task:%s:%s_%s";

    /**
     * 首次赠送礼物标记 %s uid
     */
    private static final String FIRST_SEND_GIFT_TASK = "first_send_gift_task:%s";


    /**
     * 用户累积完成日任务子任务数
     * %s: daycode
     */
    private static final String COMPLETE_DAY_SUB_TASK_AMOUNT = "complete_day_sub_task_amount:%s";

    /**
     * 用户累积完成日任务数
     */
    private static final String COMPLETE_DAY_TASK_AMOUNT = "complete_day_task_amount";

    @Override
    public Long getComponentId() {
        return ComponentId.ZHUIWAN_DATING_TASK;
    }


    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, ZhuiwanDatingTaskComponentAttr attr) {
        if (!attr.isMyDuty(event.getRankId())) {
            return;
        }

        threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
            @Override
            public void run() {
                handlerScoreDayTask(event, attr);
            }
        });

        threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
            @Override
            public void run() {
                handlerSendAnchorAmountDayTask(event, attr);
            }
        });

        threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
            @Override
            public void run() {
                handlerFirstSendGiftActTask(event, attr);
            }
        });

    }

    /**
     * 日任务#送出20元约会礼物：单日累计送出甜蜜壁咚和浪漫约会礼物共价值20元；
     */
    public void handlerScoreDayTask(RankingScoreChanged event, ZhuiwanDatingTaskComponentAttr attr) {

        long actId = attr.getActId();
        Date now = commonService.getNow(actId);
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String groupCode = getRedisGroupCode(actId);
        long addScore = event.getItemScore();
        String sendGiftScoreKey = makeKey(actId, attr.getCmptId(), attr.getCmptUseInx(), String.format(SEND_GIFT_SCORE_DAY_TASK, dayCode));
        long afterAddScore = actRedisDao.hIncrByKey(groupCode, sendGiftScoreKey, event.getMember(), addScore);
        TaskConfig config = attr.getTaskConfigMap().get(attr.getScoreDayTaskCode());
        if (afterAddScore >= config.getCompleteAmount() && afterAddScore - addScore < config.getCompleteAmount()) {
            log.info("handlerScoreDayTask add,uid:{},event:{},attr:{}", event.getMember(), JSON.toJSONString(event), JSON.toJSONString(attr));
            long uid = Convert.toLong(event.getMember(), 0);
            addQualificationAward(event, attr, config, uid, dayCode);

            handlerCompleteSubDayTask(event, attr);
        }

    }

    /**
     * 与3个不同的人完成甜蜜壁咚和浪漫约会：单日累计对3个不同uid分别送出甜蜜壁咚或浪漫约会礼物，金额不限
     */
    public void handlerSendAnchorAmountDayTask(RankingScoreChanged event, ZhuiwanDatingTaskComponentAttr attr) {

        long actId = attr.getActId();
        Date now = commonService.getNow(actId);
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String groupCode = getRedisGroupCode(actId);
        String userId = event.getMember();
        String anchorId = event.getActors().get(HdztRoleId.ZW_ANCHOR);

        String sendGiftRecordKey = makeKey(actId, attr.getCmptUseInx(), String.format(SEND_ANCHOR_UID_RECORD_DAY_TASK, dayCode, userId, anchorId));
        if (actRedisDao.setNX(groupCode, sendGiftRecordKey, DateUtil.format(new Date()), DateUtil.ONE_MONTH_SECONDS)) {
            String amountKey = makeKey(actId, attr.getCmptUseInx(), String.format(SEND_GIFT_ANCHOR_AMOUNT_DAY_TASK, dayCode));
            long afterAdd = actRedisDao.hIncrByKey(groupCode, amountKey, userId, 1L);
            TaskConfig config = attr.getTaskConfigMap().get(attr.getSendAnchorAmountDayTaskCode());
            if (afterAdd >= config.getCompleteAmount() && afterAdd - 1 < config.getCompleteAmount()) {
                log.info("handlerSendAnchorAmountDayTask add,uid:{},event:{},attr:{}", event.getMember(), JSON.toJSONString(event), JSON.toJSONString(attr));
                long uid = Convert.toLong(userId, 0);
                addQualificationAward(event, attr, config, uid, dayCode);

                handlerCompleteSubDayTask(event, attr);
            }
        }

    }

    /**
     * 首次送出礼物任务
     */
    public void handlerFirstSendGiftActTask(RankingScoreChanged event, ZhuiwanDatingTaskComponentAttr attr) {
        long actId = attr.getActId();
        String groupCode = getRedisGroupCode(actId);
        String userId = event.getMember();

        String key = makeKey(actId, attr.getCmptId(), attr.getCmptUseInx(), String.format(FIRST_SEND_GIFT_TASK, userId));
        if (actRedisDao.setNX(groupCode, key, DateUtil.format(new Date()), DateUtil.ONE_MONTH_SECONDS)) {
            log.info("handlerFirstSendGiftActTask add,uid:{},event:{},attr:{}", event.getMember(), JSON.toJSONString(event), JSON.toJSONString(attr));
            TaskConfig config = attr.getTaskConfigMap().get(attr.getFirstSendActTaskCode());
            long uid = Convert.toLong(userId, 0);
            addQualificationAward(event, attr, config, uid, "");
        }
    }

    /**
     * 完成日子任务
     */
    public void handlerCompleteSubDayTask(RankingScoreChanged event, ZhuiwanDatingTaskComponentAttr attr) {
        log.info("handlerCompleteSubDayTask,uid:{},event:{},attr:{}", event.getMember(), JSON.toJSONString(event), JSON.toJSONString(attr));
        long actId = attr.getActId();
        String groupCode = getRedisGroupCode(actId);
        String userId = event.getMember();
        Date now = commonService.getNow(actId);
        String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        String key = makeKey(attr.getActId(), attr.getCmptUseInx(), String.format(COMPLETE_DAY_SUB_TASK_AMOUNT, dayCode));
        int inc = 1;
        long afterInc = actRedisDao.hIncrByKey(groupCode, key, userId, inc);
        if (afterInc >= attr.getSubDayTaskAmount() && afterInc - inc < attr.getSubDayTaskAmount()) {
            log.info("handlerCompleteSubDayTask add,uid:{},event:{},attr:{}", event.getMember(), JSON.toJSONString(event), JSON.toJSONString(attr));
            handlerCompleteDayTask(event, attr);
        }

    }

    /**
     * 完成日任务累积天数达到一定值
     * 完成日任务的时候触发这里调用
     */
    public void handlerCompleteDayTask(RankingScoreChanged event, ZhuiwanDatingTaskComponentAttr attr) {
        log.info("handlerCompleteDayTask add,uid:{},event:{},attr:{}", event.getMember(), JSON.toJSONString(event), JSON.toJSONString(attr));
        //    里程碑任务
        //    a)首次送出约会礼物：在活动期间送出甜蜜壁咚或浪漫约会礼物，收礼对象和金额不限；
        //    b)累计完成日任务3天：在活动期间完成日任务中所有任务内容，累计3天；
        //    c)累计完成日任务7天：在活动期间完成日任务中所有任务内容，累计7天；
        //    d)累计完成日任务10天：在活动期间完成日任务中所有任务内容，累计10天；
        long actId = attr.getActId();
        String groupCode = getRedisGroupCode(actId);
        String userId = event.getMember();

        String key = makeKey(actId, attr.getCmptId(), attr.getCmptUseInx(), COMPLETE_DAY_TASK_AMOUNT);
        long afterInc = actRedisDao.hIncrByKey(groupCode, key, userId, 1);
        String taskCode = String.format(attr.getCompleteDayTaskCode(), afterInc);
        TaskConfig config = attr.getTaskConfigMap().get(taskCode);
        if (config == null) {
            log.warn("本次完成日任务未配置奖励,taskCode:{},event:{},attr:{}", taskCode, JSON.toJSONString(event), JSON.toJSONString(attr));
            return;
        }

        long uid = Convert.toLong(userId, 0);
        addQualificationAward(event, attr, config, uid, "");
    }


    /**
     * 增加领奖资格
     */
    private void addQualificationAward(RankingScoreChanged event, ZhuiwanDatingTaskComponentAttr attr, TaskConfig config, long uid, String dayCode) {
        String awardCode = config.getAwardCode();
        int amount = config.getAwardAmount();
        long index = attr.getReceiveAwardComponentIndex();
        String seq = config.getTaskCode() + "_" + dayCode + ":" + event.getSeq();
        receiveAwardComponent.addAwardQualificationWithRetry(index, seq, attr.getActId(), uid, awardCode, amount, attr.getAwardRetry());
    }


}
