package com.yy.gameecology.hdzj.element.redis;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerMp4Config;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.yrpc.CurrencyClient;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpDiaryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.CpFireWork;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
@UseRedisStore
@RequestMapping("/cpDiary")
@RestController
@Component
public class CpDiaryComponent extends BaseActComponent<CpDiaryComponentAttr> implements LayerSupport {

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private Locker locker;

    @Autowired
    private CurrencyClient currencyClient;

    public static final int LOCK_TTL = 5;

    public static final int RETRY_CNT = 5;

    private static final long CP_BRO_BANNER_ID = 5201314L;

    private static final long CP_BRO_LAYOUT_BANNER_ID = 5201315L;

    private static final String RETRY_SEQ = "retry_seq";

    private static final String RETRY_DAILY_SEQ = "retry_daily_seq";

    private static final String FIRE_TASK_SET = "fire_task_set";

    private static final String FIRE_TASK_HASH = "fire_task_hash";

    private static final String FIRE_CHANNEL_HASH = "fire_channel_hash";

    private static final String CLOSE_USER_HASH = "close_user_hash";

    private static final String APP_BRO_MP4 = "app_bro_mp4:";

    private static final String LAST_JOB_TIME = "last_job_time:%s";

    public static final String TASK_COMPLETE_LOCK = "taskCompleteLock:";

    private static final String DELAY_HANDLE_TASK_COMPLETED = "cp_delay_handle_task_completed";

    private static final String SCORE_CHANGE_POP = "score_change_pop:";

    private static final String PUZZLE_CP_BALANCE = "puzzle_cp_balance:%s";

    private static final int DAILY_TP = 1;
    private static final int MISSION_TP = 2;

    private static final int USER_PACKAGE_INDEX = 0;

    private static final int ANCHOR_PACKAGE_INDEX = 1;

    @PostConstruct
    @NeedRecycle(notRecycle = true, author = "guanqihua")
    public void init() throws Exception {
        registerDelayQueue(DELAY_HANDLE_TASK_COMPLETED, this::handleTaskCompleted);
    }

    @RequestMapping("/broTest")
    public Response broTest(HttpServletRequest request, HttpServletResponse response, long actId, long cmptId, long sid, long ssid) {
        if (!SysEvHelper.isDeploy()) {
            Map<String, Object> ext = Maps.newHashMap();
            ext.put("fireId", System.currentTimeMillis());
            ext.put("playTime", 120);
            ext.put("fireTp", 1);
            commonBroadCastService.commonBannerBroadcast(sid, ssid, 0, Template.findByValue(5), 2
                    , actId, actId, 100, CP_BRO_BANNER_ID, 0L, ext);

        }
        return Response.success("succ");
    }

    @RequestMapping("/broUidTest")
    public Response broUidTest(HttpServletRequest request, HttpServletResponse response, long actId, long cmptId, long uid) {
        if (!SysEvHelper.isDeploy()) {
            Map<String, Object> ext = Maps.newHashMap();
            ext.put("fireId", System.currentTimeMillis());
            ext.put("playTime", 120);
            ext.put("fireTp", 1);
            commonBroadCastService.commonNoticeUnicast(actId,
                    "love", JsonUtil.toJson(ext), StringUtils.EMPTY, uid);

        }
        return Response.success("succ");
    }

    @RequestMapping("/getCpList")
    public Response getCpList(HttpServletRequest request, HttpServletResponse response, long actId, long cmptId) {
        long uid = getLoginYYUid(request, response);
        if(uid <= 0) {
            return Response.fail(400, "未登陆");
        }
        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }
        List<CpMember> cpMembers = new ArrayList<>();
        CpDiaryComponentAttr cpDiaryComponentAttr = getComponentAttr(actId, cmptId);
        Map<String, QueryRankingRequest> reqMap = Maps.newHashMap();
        Map<String, QueryRankingRequest> antiReqMap = Maps.newHashMap();
        QueryRankingRequest contributeReq = new QueryRankingRequest();
        contributeReq.setActId(actId);
        contributeReq.setRankingId(cpDiaryComponentAttr.getCpContributeRankId());
        contributeReq.setPhaseId(cpDiaryComponentAttr.getPhaseId());
        contributeReq.setFindSrcMember(Convert.toString(uid));
        contributeReq.setRankingCount(cpDiaryComponentAttr.getRankLimit());
        reqMap.put(Convert.toString(uid), contributeReq);
        List<Long> uids = new ArrayList<>();
        Map<String, BatchRankingItem> conTop = hdztRankingThriftClient.queryBatchRanking(reqMap, null);
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(Convert.toLong(rank.getMember()));
            }
        }
        uids.add(uid);
        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        List<String> memberIds = new ArrayList<>();
        Map<Long, UserBaseInfo> userMap = commonService.batchGetUserInfos(uids, false);
        UserBaseInfo loginUserInfo = userMap.get(uid);
        MemInfo loginMemInfo = new MemInfo();
        loginMemInfo.setUid(uid);
        loginMemInfo.setName(loginUserInfo.getNick());
        loginMemInfo.setAvatar(StringUtil.isNotBlank(loginUserInfo.getHdLogo())
                ? loginUserInfo.getHdLogo() : loginUserInfo.getLogo());
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                CpMember cpMember = new CpMember();
                long toUid = Convert.toLong(rank.getMember());
                UserBaseInfo toUserInfo = userMap.get(toUid);
                String cpMemberStr = toUid + "|" + uid;
                MemInfo toMemInfo = new MemInfo();
                toMemInfo.setUid(toUid);
                toMemInfo.setName(toUserInfo.getNick());
                toMemInfo.setAvatar(StringUtil.isNotBlank(toUserInfo.getHdLogo())
                        ? toUserInfo.getHdLogo() : toUserInfo.getLogo());
                cpMember.setCpMember(cpMemberStr);
                cpMember.setAnchor(loginMemInfo);
                cpMember.setUser(toMemInfo);
                cpMembers.add(cpMember);
                memberIds.add(cpMemberStr);
            }
        }

        uids = new ArrayList<>();
        QueryRankingRequest antiContributeReq = new QueryRankingRequest();
        antiContributeReq.setActId(actId);
        antiContributeReq.setRankingId(cpDiaryComponentAttr.getCpAntiContributeRankId());
        antiContributeReq.setPhaseId(cpDiaryComponentAttr.getPhaseId());
        antiContributeReq.setFindSrcMember(Convert.toString(uid));
        antiContributeReq.setRankingCount(cpDiaryComponentAttr.getRankLimit());
        antiReqMap.put(Convert.toString(uid), antiContributeReq);
        Map<String, BatchRankingItem> conAntiTop = hdztRankingThriftClient.queryBatchRanking(antiReqMap, null);
        for (BatchRankingItem value : conAntiTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(Convert.toLong(rank.getMember()));
            }
        }
        userMap = commonService.batchGetUserInfos(uids, false);
        for (BatchRankingItem value : conAntiTop.values()) {
            for (Rank rank : value.getData()) {
                CpMember cpMember = new CpMember();
                long toUid = Convert.toLong(rank.getMember());
                UserBaseInfo toUserInfo = userMap.get(toUid);
                String cpMemberStr = uid + "|" + toUid;
                MemInfo toMemInfo = new MemInfo();
                toMemInfo.setUid(toUid);
                toMemInfo.setName(toUserInfo.getNick());
                toMemInfo.setAvatar(StringUtil.isNotBlank(toUserInfo.getHdLogo())
                        ? toUserInfo.getHdLogo() : toUserInfo.getLogo());
                cpMember.setCpMember(cpMemberStr);
                cpMember.setAnchor(toMemInfo);
                cpMember.setUser(loginMemInfo);
                cpMembers.add(cpMember);
                memberIds.add(cpMemberStr);
            }
        }
        for (String memberId : memberIds) {
            ActorQueryItem queryItem = new ActorQueryItem();
            queryItem.setActorId(memberId);
            queryItem.setRankingId(cpDiaryComponentAttr.getCpRankId());
            queryItem.setPhaseId(cpDiaryComponentAttr.getPhaseId());
            queryItem.setWithStatus(false);
            queryCpScorePara.add(queryItem);
        }
        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
        Map<String, Long> scoreMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(actorInfoItems)) {
            for (ActorInfoItem actorInfoItem : actorInfoItems) {
                scoreMap.put(actorInfoItem.getActorId(), actorInfoItem.getScore());
            }
        }
        for (CpMember cpMember : cpMembers) {
            String cp = cpMember.getCpMember();
            cpMember.setScore(scoreMap.getOrDefault(cp, 0L));
        }
        return Response.success(cpMembers);
    }

    @RequestMapping("/getCpMissions")
    public Response getCpMissions(HttpServletRequest request, HttpServletResponse response, String cpMember, long actId, long cmptId) {
        long uid = getLoginYYUid(request, response);
        if(uid <= 0) {
            return Response.fail(400, "未登陆");
        }
        int status = actInfoService.actTimeStatus(actId);
        if (status > 0) {
            return Response.fail(3, "活动已结束!");
        }
        if (status < 0) {
            return Response.fail(3, "活动未开始!");
        }
        CpMission cpMission = new CpMission();
        UserBaseInfo loginUserInfo = null;
        if(StringUtil.isEmpty(cpMember)) {
            cpMember = "1001|"+uid;
            List<Long> uids = new ArrayList<>();
            uids.add(Convert.toLong(uid));
            Map<Long, UserBaseInfo> userMap = commonService.batchGetUserInfos(uids, false);
            loginUserInfo = userMap.get(uid);
        }
        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        ActorQueryItem queryItem = new ActorQueryItem();
        ActorQueryItem dailyQueryItem = new ActorQueryItem();
        queryItem.setActorId(cpMember);
        CpDiaryComponentAttr cpDiaryComponentAttr = getComponentAttr(actId, cmptId);
        queryItem.setRankingId(cpDiaryComponentAttr.getCpRankId());
        queryItem.setPhaseId(cpDiaryComponentAttr.getPhaseId());
        queryItem.setWithStatus(false);
        queryCpScorePara.add(queryItem);

        dailyQueryItem.setRankingId(cpDiaryComponentAttr.getCpDailyRankId());
        dailyQueryItem.setPhaseId(cpDiaryComponentAttr.getPhaseId());
        dailyQueryItem.setActorId(cpMember);
        dailyQueryItem.setWithStatus(false);
        String dateKey = DateUtil.format(commonService.getNow(actId), "yyyyMMdd");
        dailyQueryItem.setDateStr(dateKey);
        queryCpScorePara.add(dailyQueryItem);

        List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
        if (!CollectionUtils.isEmpty(actorInfoItems)) {
            ActorInfoItem actorInfoItem = actorInfoItems.get(0);
            cpMission.setTotalScore(actorInfoItem.getScore());
            if(actorInfoItems.size() > 1) {
                ActorInfoItem actorInfoDailyItem = actorInfoItems.get(1);
                cpMission.setDailyScore(actorInfoDailyItem.getScore());
            }
        }
        List<Mission> missions = new ArrayList<>();
        int i = 0;
        for (Long missionVal : cpDiaryComponentAttr.getMissions()) {
            Mission mission = new Mission();
            mission.setTaskId(++i);
            mission.setScore(missionVal);
            mission.setFinish(missionVal <= cpMission.getTotalScore());
            missions.add(mission);
        }
        List<Mission> dailyMissions = new ArrayList<>();
        i = 0;
        for (Long dailyMissionVal : cpDiaryComponentAttr.getDailyMissions()) {
            Mission mission = new Mission();
            mission.setTaskId(++i);
            mission.setScore(dailyMissionVal);
            mission.setFinish(dailyMissionVal <= cpMission.getDailyScore());
            dailyMissions.add(mission);
        }
        cpMission.setMissions(missions);
        cpMission.setDailyMissions(dailyMissions);
        if(loginUserInfo != null) {
            cpMission.setAvatar(loginUserInfo.getHdLogo());
            cpMission.setNick(loginUserInfo.getNick());
        }

        return Response.success(cpMission);
    }

    @RequestMapping("/closeFire")
    public Response<?> closeFire(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptId, @RequestParam(name = "fireId") String fireId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        CpDiaryComponentAttr attr = getComponentAttr(actId, cmptId);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }


        log.info("uid:{} close fireId:{} play", uid, fireId);
        actRedisDao.hset(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr,CLOSE_USER_HASH), uid + "", fireId);
        return Response.success(null);
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, CpDiaryComponentAttr attr) {
        long uid = event.getUid(), actId = attr.getActId(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        log.info("uid:{}, enterTemplate event:{}", uid, JSONUtils.toJsonString(event));
        boolean app = StringUtil.isNotBlank(event.getHost());

        final String member = sid + "_" + ssid;
        final String groupCode = getRedisGroupCode(actId);
        CpFireWorkVo fireWork = getFireWorkByMemberId(member, attr);
        Date now = new Date();
        if (fireWork != null && fireWork.getEndTime() > now.getTime()) {
            String closeFireId = actRedisDao.hget(groupCode, makeKey(attr,CLOSE_USER_HASH), uid + "");
            long playTime = StringUtil.isNotBlank(closeFireId) &&
                    closeFireId.equals(fireWork.getFireId()) ? 0 : (fireWork.getEndTime() - now.getTime())/1000;
            log.info("enter channel uid:{}, closeFireId:{}, playTime:{}", uid, closeFireId, playTime);
            Map<String, String> ext = Maps.newHashMap();
            ext.put("fireTp", String.valueOf(fireWork.getFid()));
            ext.put("fireId", fireWork.getFireId());
            ext.put("playTime", playTime + "");
            if(!app) {
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), "love", JsonUtil.toJson(ext), StringUtils.EMPTY, uid);
            } else {
                boolean hasBro = actRedisDao.zSetNX(groupCode, makeKey(attr, APP_BRO_MP4+fireWork.getFireId()), uid+"", 30*86400);
                log.info("fire uid:{}, key:{}, hasBro:{}", uid, makeKey(attr, APP_BRO_MP4+fireWork.getFireId()), hasBro);
                if(hasBro) {
                    try {
                        Thread.sleep(2000);
                    } catch (Exception e) {
                        log.error("sleep error:{}", e.getMessage(), e);
                    }
                    broMp42App(attr.getActId(), fireWork.getFireId() + "_"+uid, fireWork.getJyBusiness(), 6, sid, ssid,
                            Lists.newArrayList(uid), fireWork.getMp4Url(), fireWork.getBroLevel());
                }
            }
        }
    }

    @HdzjEventHandler(value =TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, CpDiaryComponentAttr attr) {
        //日贡献
        if(attr.getCpDailyRankId() == event.getRankId() && attr.getPhaseId() == event.getPhaseId()) {
            log.info("daily processTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
            String eKey = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
            eKey = MD5SHAUtil.getMD5(eKey);
            long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
            if (startIndex == currIndex) {
                return;
            }
            String member = event.getMember();
            String[] members = member.split("\\|");
            long userId = Convert.toLong(members[0]);
            long anchorUid = Convert.toLong(members[1]);
            for (long i = startIndex + 1; i <= currIndex; i++) {
                String seq = eKey + i;
                long userPackageId = attr.getDailyMissionAward().get((int) i).get(USER_PACKAGE_INDEX);
                long anchorPackageId = attr.getDailyMissionAward().get((int) i).get(ANCHOR_PACKAGE_INDEX);
                handleDailyTaskComplete(attr, attr.getActId(), i, attr.getCpMissionTaskId(),
                        userPackageId, anchorPackageId, DAILY_TP, userId, anchorUid, attr.getDailyMissionName().get((int)i), seq);
            }
        }
        //总贡献榜
        if (attr.getCpRankId() == event.getRankId() && attr.getPhaseId() == event.getPhaseId()) {
            log.info("processTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
            long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
            if (startIndex == currIndex) {
                return;
            }

            final long actId = attr.getActId();
            String channelMember = event.getActors().get(Convert.toLong(attr.getTingRoleId()));
            String[] sidAndSsid = channelMember.split("_");
            final long sid = Convert.toLong(sidAndSsid[0]), ssid = Convert.toLong(sidAndSsid[1]);

            String member = event.getMember();
            String[] members = member.split("\\|");
            long userId = Convert.toLong(members[0]);
            long anchorUid = Convert.toLong(members[1]);

            final Date now = commonService.getNow(actId);
            String eKey = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
            final String groupCode = getRedisGroupCode(actId);

            String lockKey = makeKey(attr, TASK_COMPLETE_LOCK + channelMember);
            Secret secret = locker.lock(lockKey, LOCK_TTL, System.currentTimeMillis() + "", RETRY_CNT);
            if (secret == null) {
                return;
            }
            try {
                final String delayKey = Const.addActivityPrefix(actId, DELAY_HANDLE_TASK_COMPLETED);
                String lastJobKey = makeKey(attr, String.format(LAST_JOB_TIME, channelMember));
                for (long i = startIndex + 1; i <= currIndex; i++) {
                    String dateKey = DateUtil.format(now, DateUtil.PATTERN_TYPE7);
                    long lastJobTime = Convert.toLong(actRedisDao.getRedisTemplate(groupCode).opsForValue().get(lastJobKey));
                    log.info("lastJobTime:{}, now:{}", lastJobTime, now.getTime());
                    long delaySecs = lastJobTime == 0L || lastJobTime - System.currentTimeMillis() <= 0 ?
                            0L : (lastJobTime - System.currentTimeMillis()) / 1000;
                    final String fireId = event.getMember() + "_" + dateKey + "_" + i, seq = eKey + i;
                    final Map<Long, CpFireWork> fireWorkMap = attr.getFireWorkList().stream()
                            .collect(Collectors.toMap(CpFireWork::getFid, fireWork -> fireWork));
                    CpFireWork cpFireWork = fireWorkMap.get(i);
                    CpFireWorkVo cpFireWorkVo = new CpFireWorkVo();
                    cpFireWorkVo.setSid(sid);
                    cpFireWorkVo.setSsid(ssid);
                    cpFireWorkVo.setFireId(fireId);
                    cpFireWorkVo.setFid(cpFireWork.getFid());
                    cpFireWorkVo.setName(cpFireWork.getName());
                    cpFireWorkVo.setLogo(cpFireWork.getLogo());
                    cpFireWorkVo.setJyBusiness(cpFireWork.getJyBusiness());
                    cpFireWorkVo.setMp4Url(cpFireWork.getMp4Url());
                    cpFireWorkVo.setBroLevel(cpFireWork.getBroLevel());
                    cpFireWorkVo.setMember(event.getMember());
                    cpFireWorkVo.setChannelMember(channelMember);
                    cpFireWorkVo.setValidSec(cpFireWork.getValidSec());

                    CpFireEvent cpFireEvent = new CpFireEvent();
                    cpFireEvent.setActId(actId);
                    cpFireEvent.setCmptUse(attr.getCmptUseInx());
                    cpFireEvent.setTaskId(i);
                    cpFireEvent.setCpFireWorkVo(cpFireWorkVo);
                    cpFireEvent.setSeq(seq);
                    cpFireEvent.setBroTemplate(attr.getBroTemplate());
                    cpFireEvent.setGroupCode(groupCode);
                    cpFireEvent.setUid(userId);
                    cpFireEvent.setAnchorUid(anchorUid);
                    cpFireEvent.setLevelName(attr.getMissionName().get((int) i));
                    if (!attr.getMissionAward().isEmpty() && attr.getMissionAward().get((int) i).size() > 1) {
                        cpFireEvent.setUserAwardPackageId(attr.getMissionAward().get((int) i).get(USER_PACKAGE_INDEX));
                        cpFireEvent.setAnchorAwardPackageId(attr.getMissionAward().get((int) i).get(ANCHOR_PACKAGE_INDEX));
                    }
                    cpFireEvent.setAwardTaskId(attr.getCpMissionTaskId());
                    if (delaySecs == 0L) {
                        cpFireWorkVo.setEndTime(System.currentTimeMillis() + (long) cpFireWork.getValidSec() * 1000);
                        handleTaskCompleted(attr, cpFireEvent);
                    } else {
                        cpFireWorkVo.setEndTime(System.currentTimeMillis() + (delaySecs + (long) cpFireWork.getValidSec()) * 1000);
                        long expiredMills = System.currentTimeMillis() + delaySecs * 1000;
                        publishDelayEvent(attr, DELAY_HANDLE_TASK_COMPLETED, cpFireEvent, expiredMills);
                    }
                    delaySecs += cpFireWork.getValidSec();
                    actRedisDao.getRedisTemplate(groupCode).opsForValue().set(lastJobKey, System.currentTimeMillis() + (delaySecs * 1000) + "");
                }
            } catch (Exception e) {
                log.error("taskComplete uid:{} e:{}", event.getMember(), ExceptionUtils.getStackTrace(e));
            } finally {
                locker.unlock(lockKey, secret, RETRY_CNT);
            }
        }
    }

    private void handleDailyTaskComplete(CpDiaryComponentAttr attr, long actId, long taskId,
                                         long awardTaskId, long userAwardPackageId, long anchorAwardPackageId, int tp, long uid, long anchorUid, String levelName, String seq) {
        if (actRedisDao.hsetnx(redisConfigManager.getGroupCode(actId), makeKey(attr, RETRY_DAILY_SEQ), seq, StringUtil.ONE)) {
            popAward(actId, taskId, awardTaskId, userAwardPackageId, tp, uid, levelName);
            popAward(actId, taskId, awardTaskId, anchorAwardPackageId, tp, anchorUid, levelName);
        }
        BatchWelfareResult batchWelfareUserResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), uid,
                awardTaskId, 1, userAwardPackageId, seq + ":" + uid, 3);

        BatchWelfareResult batchWelfareAnchorResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), anchorUid,
                awardTaskId, 1, anchorAwardPackageId, seq + ":" + anchorUid, 3);
        log.info("daily reward from doWelfare userResult={}, anchorResult={} seq:{}", JsonUtil.toJson(batchWelfareUserResult), JsonUtil.toJson(batchWelfareAnchorResult), seq);

    }

    private void handleTaskCompleted(CpDiaryComponentAttr attr, Object e) {
        if (!(e instanceof CpFireEvent event)) {
            log.error("handleTaskCompleted delay event not satisfy");
            return;
        }
        log.info("handleTaskCompleted cpFireEvent:{}", JsonUtil.toJson(event));
        if (actRedisDao.hsetnx(event.getGroupCode(), makeKey(event.getActId(), event.getCmptUse(), RETRY_SEQ), event.getSeq(), StringUtil.ONE)) {
            actRedisDao.hset(event.getGroupCode(), makeKey(event.getActId(), event.getCmptUse(), FIRE_CHANNEL_HASH), event.getCpFireWorkVo().getChannelMember(), JsonUtil.toJson(event.getCpFireWorkVo()));
            actRedisDao.zAdd(event.getGroupCode(), makeKey(event.getActId(), event.getCmptUse(), FIRE_TASK_SET), event.getCpFireWorkVo().getFireId(), event.getCpFireWorkVo().getEndTime());
            actRedisDao.hset(event.getGroupCode(), makeKey(event.getActId(), event.getCmptUse(), FIRE_TASK_HASH), event.getCpFireWorkVo().getFireId(), JsonUtil.toJson(event.getCpFireWorkVo()));
            Map<String, Object> ext = Maps.newHashMap();
            ext.put("fireId", event.getCpFireWorkVo().getFireId());
            ext.put("playTime", event.getCpFireWorkVo().getValidSec());
            ext.put("fireTp", event.getCpFireWorkVo().getFid());
            commonBroadCastService.commonBannerBroadcast(event.getCpFireWorkVo().getSid(), event.getCpFireWorkVo().getSsid(), 0, Template.findByValue(event.getBroTemplate()), 2
                    , event.getActId(), Convert.toLong(0), 0, CP_BRO_BANNER_ID, 0L, ext);
            popLayout(event.getActId(), event.getBroTemplate(), event.getUid(), event.getAnchorUid(), event.getCpFireWorkVo().getSid(), event.getCpFireWorkVo().getSsid(), event.getTaskId());

            popAward(event.getActId(), event.getTaskId(), event.getAwardTaskId(), event.getUserAwardPackageId(), MISSION_TP, event.getUid(), event.getLevelName());
            popAward(event.getActId(), event.getTaskId(), event.getAwardTaskId(), event.getAnchorAwardPackageId(), MISSION_TP, event.getAnchorUid(), event.getLevelName());
            broMp42App(event.getActId(), event.getSeq(), event.getCpFireWorkVo().getJyBusiness(), 1,
                    event.getCpFireWorkVo().getSid(), event.getCpFireWorkVo().getSsid(), new ArrayList<>(), event.getCpFireWorkVo().getMp4Url(), event.getCpFireWorkVo().getBroLevel());
        }
        BatchWelfareResult batchWelfareUserResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), event.getUid(),
                event.getAwardTaskId(), 1, event.getUserAwardPackageId(), event.getSeq() + ":" + event.getUid(), 3);

        BatchWelfareResult batchWelfareAnchorResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), event.getAnchorUid(),
                event.getAwardTaskId(), 1, event.getAnchorAwardPackageId(), event.getSeq() + ":" + event.getAnchorUid(), 3);
        log.info("reward from doWelfare userResult={}, anchorResult={} seq:{}", JsonUtil.toJson(batchWelfareUserResult), JsonUtil.toJson(batchWelfareAnchorResult), event.getSeq());
    }


    /**
     * 榜单改变事件,上报爱神阶段榜
     **/
    @HdzjEventHandler(value = RankingScoreChanged.class,canRetry = true)
    public void onRankScoreChange(RankingScoreChanged event, CpDiaryComponentAttr attr) {
        log.info("rankScoreChange event:{}", JsonUtil.toJson(event));
        long rankId = event.getRankId();
        if(rankId == attr.getCpDailyRankId()) {
            long total = event.getRankScore();
            long uid = Convert.toLong(event.getMember().split("\\|")[1]);
            if(total - event.getItemScore() < attr.getScoreThreshold() && total >= attr.getScoreThreshold()) {
                if (!actRedisDao.hsetnx(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, SCORE_CHANGE_POP),
                        uid+"", commonService.getNow(attr.getActId()).getTime()+"")) {
                    log.info("rankScoreChange uid:{} already pop", uid);
                    return;
                }
                log.info("rankScoreChange handle event:{}", JsonUtil.toJson(event));
                Map<Long, AwardModelInfo> packageInfo = packageInfoMap(attr);
                String gift = "";
                if(attr.getDailyMissionAward().containsKey(1)) {
                    List<Long> tasks = attr.getDailyMissionAward().get(1);
                    if(tasks.size() > 1) {
                        long pid = Convert.toLong(tasks.get(1));
                        AwardModelInfo awardModelInfo = packageInfo.get(pid);
                        gift = awardModelInfo.getPackageName();
                    }
                }
                long diff = 0;
                if(!attr.getDailyMissions().isEmpty()) {
                    diff = attr.getDailyMissions().get(0) - total;
                }
                if(diff > 0) {
                    popScoreChange(attr, diff, gift, uid);
                }
            }
        }
    }

    public CpFireWorkVo getFireWorkByMemberId(String memberId, CpDiaryComponentAttr attr) {
        String fireWorkStr = actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, FIRE_CHANNEL_HASH), memberId);
        if (fireWorkStr == null) {
            return null;
        }
        CpFireWorkVo fireWork = JsonUtil.toObject(fireWorkStr, CpFireWorkVo.class);
        if (fireWork == null || fireWork.getEndTime() - System.currentTimeMillis() <= 0) {
            return null;
        }
        return fireWork;
    }

    public Map<Long, AwardModelInfo> packageInfoMap(CpDiaryComponentAttr attr) {
        try {
            var result = hdztAwardServiceClient.queryAwardTasks(attr.getCpMissionTaskId());
            return result == null ? Collections.emptyMap() : result;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }

    private void popScoreChange(CpDiaryComponentAttr attr, long val, String gift, long uid) {
        JSONObject extJson = new JSONObject();
        extJson.put("val", val);
        extJson.put("gift", gift);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), "loveTask", extJson.toJSONString()
                , Strings.EMPTY, uid);
    }

    private void popAward(long actId, long taskId, long awardTaskId, long awardPackageId, int tp, long uid, String levelName) {
        Map<Long, AwardModelInfo> awardModelInfoMap = new HashMap<>();
        try {
            awardModelInfoMap = hdztAwardServiceClient.queryAwardTasks(awardTaskId);
        } catch (Exception e) {
            log.error("queryAwardTasks error{}", e.getMessage(), e);
        }
        AwardModelInfo awardModelInfo = awardModelInfoMap.get(awardPackageId);
        JSONObject extJson = new JSONObject();
        extJson.put("icon", awardModelInfo.getPackageImage() == null ? "" : awardModelInfo.getPackageImage());
        extJson.put("gift", awardModelInfo.getPackageName() == null ? "" : awardModelInfo.getPackageName());
        extJson.put("tp", tp);
        extJson.put("level", taskId);
        extJson.put("levelText", levelName);
        commonBroadCastService.commonNoticeUnicast(actId, "loveAward", extJson.toJSONString()
                , Strings.EMPTY, uid);
    }

    private void popLayout(long actId, int broTemplate, long uid, long anchorUid, long sid, long ssid, long taskId) {
        List<Long> uids = Lists.newArrayList(uid, anchorUid);
        Map<Long, UserBaseInfo>  userBaseInfoMap = commonService.batchGetUserInfos(uids, false);
        UserBaseInfo anchorUserBaseInfo = userBaseInfoMap.get(anchorUid);
        UserBaseInfo userBaseInfo = userBaseInfoMap.get(uid);
        Map<String, Object> ext = Maps.newHashMap();
        ext.put("anchorAvatar", anchorUserBaseInfo == null ? "https://s1.yy.com/guild/header/10001.jpg" : anchorUserBaseInfo.getHdLogo());
        ext.put("userAvatar", userBaseInfo == null ? "https://s1.yy.com/guild/header/10001.jpg" : userBaseInfo.getHdLogo());
        ext.put("sid", sid);
        ext.put("ssid", ssid);
        ext.put("tp", taskId);
        commonBroadCastService.commonBannerBroadcast(sid, ssid, 0, Template.findByValue(broTemplate), 4
                , actId, Convert.toLong(anchorUid), 0, CP_BRO_LAYOUT_BANNER_ID, 0L, ext);
    }

    public void broMp42App(long actId, String seq, int business, int bcType, long sid,
                           long ssid, List<Long> uids, String mp4Url, int broLevel) {
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(actId, seq, business,
                bcType, sid, ssid, "",
                Lists.newArrayList());
        appBannerEvent.setUid(0);
        appBannerEvent.setUidList(Lists.newArrayList());
        appBannerEvent.setContentType(5);
        appBannerEvent.setPushUidlist(uids);
        AppBannerMp4Config appBannerMp4Config = new AppBannerMp4Config();
        appBannerMp4Config.setUrl(mp4Url);
        appBannerMp4Config.setLevel(broLevel);
        appBannerEvent.setMp4Config(appBannerMp4Config);
        kafkaService.sendAppBannerKafka(appBannerEvent);
    }

    @Data
    public static class CpFireEvent {

        private String seq;

        private String groupCode;

        private long actId;

        private long cmptUse;

        private long taskId;

        private int broTemplate;

        private long uid;

        private long anchorUid;

        private long awardTaskId;

        private long userAwardPackageId;

        private long anchorAwardPackageId;

        private String levelName;

        private CpFireWorkVo cpFireWorkVo;
    }


    @Data
    private static class CpFireWorkVo {
        private String fireId;

        private long fid;

        private String name;

        private String logo;

        private String member;

        private long endTime;

        private long sid;

        private long ssid;

        private int jyBusiness;

        private String mp4Url;

        private int broLevel;

        private String channelMember;

        private int validSec;
    }

    @Data
    private static class CpMember {
        private String cpMember;

        private MemInfo anchor;

        private MemInfo user;

        private long score;
    }

    @Data
    private static class MemInfo {
        private long uid;

        private String name;

        private String avatar;
    }

    @Data
    private static class CpMission {
        private List<Mission> missions;

        private List<Mission> dailyMissions;

        private long dailyScore;

        private long totalScore;

        private String avatar;

        private String nick;
    }

    @Data
    private static class Mission {
        private long taskId;

        private boolean finish;

        private long score;
    }

    @Override
    public long getActId() {
        return ComponentId.CP_DIARY;
    }

    @Override
    public Long getComponentId() {
        return ComponentId.CP_DIARY;
    }
}
