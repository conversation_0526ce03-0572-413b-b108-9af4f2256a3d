## gameecology

活动浮层挂件协议： 链接中 V20210415_520_activity 是分支名

https://git.yy.com/server/yuezhanbaobei/gameecology/gameecology_java/-/blob/V20210415_520_activity/gameecology_activity/src/main/java/com/yy/gameecology/activity/protocol/pb/layer_info.proto

* [git地址](https://git.yy.com/server/yuezhanbaobei/gameecology/gameecology_java)

> gameecology_stream
>- [发布中心](https://s.sysop.yy.com/service/overview/3@gameecology@gameecology-stream-docker/release/container)

活动平台-活动逻辑模块，和业务系统对接，需要解决具体的业务逻辑问题，活动开发的编码工作集中于此

### 本地启动jvm参数
```jvm
-Dgroup=##{对应套数}
-Denv=local
-Dcsp.sentinel.log.output.type=console
--add-opens
java.base/java.lang=ALL-UNNAMED
--add-opens
java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
--add-opens
java.base/java.util=ALL-UNNAMED
--add-opens
java.base/java.util.concurrent=ALL-UNNAMED
--add-opens
java.base/sun.net.util=ALL-UNNAMED
--add-opens
java.base/java.lang.reflect=ALL-UNNAMED
-Dlogging.level.org.springframework=TRACE
-DMY_HOST_IP=***********
-Ds2s.hdzk.server.key=##{可以在服务治理的配置中查看}
-Dio.lettuce.core.epoll=false
```

**注意**：s2s.hdzk.server.key 要用对应 activity 套上的测试配置对应值

**注意**：添加系统环境变量MY_HOST_IP=***********、ENV=DEV，添加了系统环境变量就可以不用配置MY_HOST_IP的jvm参数了