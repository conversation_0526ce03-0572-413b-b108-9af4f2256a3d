package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.client.thrift.FtsRoomManagerThriftClient;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.BroadCastHelpService;
import com.yy.gameecology.activity.service.UserAccumulatedService;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.GiftBO;
import com.yy.gameecology.hdzj.bean.GiftPlaneBO;
import com.yy.gameecology.hdzj.bean.SendGiftBO;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.ActivityPlaneComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.BlackListComponentAttr;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.fts_room_manager.AntiPoachingInfo;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.common.gson.GsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.yy.gameecology.hdzj.consts.ComponentId.ACTIVITY_PLANE;

/**
 * 1. 广播协议
 * A. uri = 100016
 *
 * <AUTHOR>
 * @since 2021/7/1
 */
@Component
@Slf4j
public class ActivityPlaneComponent extends BaseActComponent<ActivityPlaneComponentAttr> {

    public static final String GIFT_COMBO_KEY = "pgc";

    public static final String GIFT_COMBO_FINISH_KEY = "pgcf";

    @Autowired
    protected FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private UserAccumulatedService accumulatedService;

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private BlackListComponent blackListComponent;

    @Autowired
    private FtsRoomManagerThriftClient ftsRoomManagerThriftClient;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    /**
     * 交友连送结束事件，和业务统一
     */
    @HdzjEventHandler(value = JiaoyouComboEndEvent.class, canRetry = true)
    public void jiaoyouComboEndEvent(JiaoyouComboEndEvent event, ActivityPlaneComponentAttr attr) {
        log.info("jiaoyouComboEndEvent process, event:{}", JSON.toJSONString(event));
        if (attr.getTemplate() != Template.Jiaoyou.getValue()) {
            log.info("not jiaoyou event return,event:{}", JSON.toJSONString(event));
            return;
        }

        //用户若在黑名单的厅送礼,则不触发横幅
        if (attr.getBlackListCmptUseIndex() != 0) {
            BlackListComponentAttr blackListComponentAttr = blackListComponent.getComponentAttr(attr.getActId(), attr.getBlackListCmptUseIndex());
            if (StringUtil.isNotBlank(blackListComponentAttr.getBlackList())) {
                String blackList = blackListComponentAttr.getBlackList();
                String ting = event.getSid() + "_" + event.getSsid();
                if (StringUtil.isNotBlank(ting) && blackList.contains(ting)) {
                    log.info("user in black list ting, memberId:{}, event:{}", event.getSendUid(), JSON.toJSONString(event));
                    return;
                }
            }
        }

        //总价值
        final long priceSum = MapUtils.getLongValue(event.getExpand(), "priceSum");
        String giftId = event.getExpand().get("propId") + "";
        int combo = MapUtils.getIntValue(event.getExpand(), "combo");
        long giftNum = MapUtils.getLongValue(event.getExpand(), "count");
        long player = event.getSendUid();
        long anchor = event.getRecvUid();
        //少于xx yb不起飞机
        final int i100 = 100;
        if (priceSum < attr.getThresholdFirePlane() * i100) {
            return;
        }
        //礼物控制
        if (!attr.getGiftIds().contains(giftId)) {
            return;
        }

        if(antiPoaching(event.getSid(), event.getSsid())) {
            log.info("antiPoaching sid:{}, ssid:{}, event:{}", event.getSid(), event.getSsid(), JSON.toJSONString(event));
            return;
        }

        //seq控制
        String key = makeKey(attr.getActId(), 0, "seq:" + event.getSeqId());
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        long expireSeconds = expireSeconds(attr.getActId());
        if (!actRedisDao.setNX(groupCode, key, DateUtil.getNowYyyyMMddHHmmss(), expireSeconds)) {
            log.warn("already invoke,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
            return;
        }

        //起飞机，上报海度
        Template template = getTemplate(attr.getTemplate());
        long asid = commonService.getAsid(event.getSid());
        GameecologyActivity.ActPlaneBanner.Builder banner = GameecologyActivity.ActPlaneBanner.newBuilder()
                .setActId(attr.getActId())
                .setCommbo(combo).setSid(event.getSid()).setSsid(event.getSsid())
                .setChannel(asid).setAnchor(getAnchorInfo(anchor, event.getSid(), event.getSsid(), template))
                .setUser(getUserInfo(player, getTemplate(attr.getTemplate())))
                .setGift(getGiftInfo(getGiftBO(giftId, attr), giftNum));
        GiftBO giftBO = getGiftBO(giftId, attr);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kActPlaneBannerUri_VALUE)
                .setActPlaneBanner(banner).build();
        RetryTool.withRetryCheck(attr.getActId(), event.getSeqId(), () -> {
            if(giftBO.getExcludeDanmaku() == 1) {
                List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
                Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
                svcSDKService.broadcastTemplateExclude(template, msg, exclude);
            } else {
                svcSDKService.broadcastTemplate(template, msg);
            }
        });

        log.info("takeOffPlane jiaoyou done uid:{}, anchor:{},channel:{}, count:{},giftId:{}, giftNum:{}", player, anchor, event.getSid(), combo, giftId, giftNum);

        Date now = commonService.getNow(attr.getActId());
        String ext = String.format("giftId=%s|send=%s|receive=%s|sid=%s|ssid=%s", giftId, player, anchor, event.getSid(), event.getSsid());
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.MAKE_FRIEND, now.getTime(), player + "", RoleType.USER, giftNum
                , 14, ext);
    }


    @Override
    public Long getComponentId() {
        return ACTIVITY_PLANE;
    }


    /**
     * 需要任务调度
     */
    public void finishGiftCombo(ActivityPlaneComponentAttr attr) {
        if (!isGiftCombo(null, attr)) {
            return;
        }
        List<AccumulatedRecord> records = accumulatedService
                .getUseAccumulatedRecord(attr.getActId(), getGiftComboKey(attr));
        log.info("finishGiftCombo actId:{},index:{},recordSize:{}", attr.getActId(),
                attr.getCmptUseInx(), records.size());
        records.forEach(record -> takeOffPlane(attr, record));
    }

    private void takeOffPlane(ActivityPlaneComponentAttr attr, AccumulatedRecord record) {
        if (record != null) {
            SendGiftBO giftBO = JSONUtils.parseObject(record.getData(), SendGiftBO.class);
            takeOffPlane(buildGiftPlaneBO(record.getCount(), giftBO), attr);
        }
    }

//    private void action(SendGiftBO bo, ActivityPlaneComponentAttr attr) {
//        if (isGiftCombo(bo, attr)) {
//            giftCombo(bo, attr);
//        } else {
//            takeOffPlane(buildGiftPlaneBO(1, bo), attr);
//        }
//    }

    private GiftPlaneBO buildGiftPlaneBO(int count, SendGiftBO bo) {
        if (bo == null) {
            return null;
        }
        GiftPlaneBO planeBO = new GiftPlaneBO();
        planeBO.setCount(count);
        planeBO.setSendUid(bo.getSendUid());
        planeBO.setRecvUid(bo.getRecvUid());
        planeBO.setGiftId(bo.getGiftId());
        planeBO.setGiftNum(bo.getGiftNum());
        planeBO.setSid(bo.getSid());
        planeBO.setSsid(bo.getSsid());
        return planeBO;
    }

    /**
     * 连击
     */
//    void giftCombo(SendGiftBO bo, ActivityPlaneComponentAttr attr) {
//        String boRecord = JSONUtils.toJsonString(bo);
//        log.info("giftCombo bo:{}", boRecord);
//        String judge = bo.getRecvUid() + "_" + bo.getGiftId() + "_" + bo.getGiftNum();
//        AccumulatedRecord record = accumulatedService
//                .accumulatedUserData(attr.getActId(), getGiftComboKey(attr), bo.getSendUid(), boRecord,
//                        judge, attr.getComboTime(), bo.getSeq());
//        takeOffPlane(attr, record);
//    }

    private void takeOffPlane(GiftPlaneBO bo, ActivityPlaneComponentAttr attr) {
        if (bo == null) {
            return;
        }
        if (getPlaneCount(bo, attr) <= 0) {
            return;
        }
        //起飞机
        doTakeOffPlane(bo, attr);
        //上报数据到海度
        updateToHive(bo, attr);
    }

    private void doTakeOffPlane(GiftPlaneBO bo, ActivityPlaneComponentAttr attr) {
        log.info("takeOffPlane starting,bo:{}", bo);
        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.kActPlaneBannerUri_VALUE)
                .setActPlaneBanner(buildPlaneBanner(bo, attr)).build();
        svcSDKService.broadcastTemplate(getTemplate(attr.getTemplate()), msg);

        log.info("takeOffPlane  is successful, bo:{}", bo);
    }


    public Response doTakeOffPlane(long actId, long index, long busiId, long sendUid, long recvUid, String giftId, long giftNum, int count, long sid, long ssid, String seq) {
        ActivityPlaneComponentAttr attr = getComponentAttr(actId, index);
        Assert.notNull(attr, "not find attr by cmptUseInx = " + index);

        BusiId busiIdEnum = broadCastHelpService.changeBroTemplate2BusiId(getTemplate(attr.getTemplate()));
        Assert.isTrue(busiIdEnum != null && busiIdEnum.getValue() == busiId, "业务id错误");

        final boolean rs = actRedisDao.hsetnx(getRedisGroupCode(actId), makeKey(attr, "doTakeOffPlaneSeq"), seq, DateUtil.getNowYyyyMMddHHmmss());
        if (!rs) {
            return Response.fail(1, "重复操作！");
        }
        long expireSeconds = expireSeconds(attr.getActId());
        actRedisDao.expire(getRedisGroupCode(actId), makeKey(attr, "doTakeOffPlaneSeq"), expireSeconds);

        GiftPlaneBO bo = new GiftPlaneBO();
        bo.setSendUid(sendUid);
        bo.setRecvUid(recvUid);
        bo.setGiftId(giftId);
        bo.setGiftNum(giftNum);
        bo.setCount(count);
        bo.setSid(sid);
        bo.setSsid(ssid);

        doTakeOffPlane(bo, attr);
        return Response.ok();
    }

    /**
     * 上报数据到海度
     *
     * @param bo
     * @param attr
     */
    private void updateToHive(GiftPlaneBO bo, ActivityPlaneComponentAttr attr) {
        long actId = attr.getActId();
        Date now = commonService.getNow(actId);
        long total = bo.getCount() * bo.getGiftNum();
        BusiId busiId = broadCastHelpService.changeBroTemplate2BusiId(getTemplate(attr.getTemplate()));
        String ext = String.format("giftId=%s|send=%s|receive=%s|sid=%s|ssid=%s", bo.getGiftId(), bo.getSendUid(), bo.getRecvUid(), bo.getSid(), bo.getSsid());
        bigDataService.saveNoRankDataToFile(actId, busiId, now.getTime(), bo.getSendUid() + "", RoleType.USER, total
                , 14, ext, bo.getCount(), bo.getRecvUid());
    }


    private Template getTemplate(Integer template) {
        return Template.findByValue(template);
    }

    private int getPlaneCount(GiftPlaneBO bo, ActivityPlaneComponentAttr attr) {
        return BigInteger.valueOf(getGiftPrice(bo.getGiftId(), attr))
                .multiply(BigInteger.valueOf(bo.getGiftNum()))
                .multiply(BigInteger.valueOf(bo.getCount()))
                .divide(BigInteger.valueOf(getThresholdFirePlane(attr))).intValue();
    }


    public GameecologyActivity.AnchorInfo.Builder getAnchorInfo(GiftPlaneBO bo, Template template) {
        UserBaseInfo userInfo = getUserBaseInfo(bo.getRecvUid(), template);
        GameecologyActivity.AnchorInfo.Builder anchor = GameecologyActivity.AnchorInfo.newBuilder()
                .setUid(bo.getRecvUid())
                .setLogo(userInfo.getLogo())
                .setNick(userInfo.getNick())
                .setSid(bo.getSid())
                .setSsid(bo.getSsid())
                .setAsid(commonService.getAsid(bo.getSid()));
        return anchor;
    }

    public GameecologyActivity.AnchorInfo.Builder getAnchorInfo(long receiveUid, long sid, long ssid, Template template) {
        UserBaseInfo userInfo = getUserBaseInfo(receiveUid, template);
        GameecologyActivity.AnchorInfo.Builder anchor = GameecologyActivity.AnchorInfo.newBuilder()
                .setUid(receiveUid)
                .setLogo(userInfo.getLogo())
                .setNick(userInfo.getNick())
                .setSid(sid)
                .setSsid(ssid)
                .setAsid(commonService.getAsid(sid));
        return anchor;
    }

    private GameecologyActivity.GiftInfo.Builder getGiftInfo(GiftBO bo, long giftNum) {
        return GameecologyActivity.GiftInfo.newBuilder()
                .setId(bo.getGiftId())
                .setCount(giftNum)
                .setName(bo.getName())
                .setUrl(bo.getUrl());
    }


    private GameecologyActivity.UserInfo.Builder getUserInfo(long userUid, Template template) {
        UserBaseInfo userInfo = getUserBaseInfo(userUid, template);
        return GameecologyActivity.UserInfo.newBuilder()
                .setUid(userUid)
                .setLogo(userInfo.getLogo())
                .setNick(userInfo.getNick());
    }

    /**
     * 获取gift combo redis key
     *
     * @return
     */
    protected String getGiftComboKey(ComponentAttr attr) {
        return makeKey(attr, GIFT_COMBO_KEY);
    }

    /**
     * 准备数据
     *
     * @param event
     * @return
     */
    protected SendGiftBO prepare(SendGiftEvent event) {
        return SendGiftBO.builder()
                .template(event.getTemplate())
                .seq(event.getSeq())
                .sendUid(event.getSendUid())
                .recvUid(event.getRecvUid())
                .giftId(event.getGiftId())
                .giftNum(event.getGiftNum())
                .sid(event.getSid())
                .ssid(event.getSsid())
                .eventTime(event.getEventTime())
                .sourceChannel(event.getSourceChannel())
                .signSid(event.getSignSid())
                .jsonMap(event.getJsonMap())
                .build();
    }

    /**
     * 获取触发活动飞机的礼物价值阈值
     *
     * @param attr
     * @return
     */
    protected int getThresholdFirePlane(ActivityPlaneComponentAttr attr) {
        return attr.getThresholdFirePlane();
    }

    /**
     * 获取本次礼物的价值
     *
     * @param giftId
     * @param attr
     * @return
     */
    protected long getGiftPrice(String giftId, ActivityPlaneComponentAttr attr) {
        GiftBO giftBO = getGiftBO(giftId, attr);
        if (giftBO == null) {
            return 0L;
        }
        return giftBO.getPrice();
    }

    private boolean check(SendGiftBO bo, ActivityPlaneComponentAttr attr) {
        return checkGift(bo.getGiftId(), attr)
                && checkTemplate(bo.getTemplate(), attr)
                && checkGiftNum(bo.getGiftNum(), attr)
                && checkOther(bo, attr);
    }

    protected boolean checkOther(SendGiftBO event, ActivityPlaneComponentAttr attr) {
        return true;
    }

    protected boolean checkGiftNum(Long giftNum, ActivityPlaneComponentAttr attr) {
        return true;
    }

    protected boolean checkTemplate(Template template, ActivityPlaneComponentAttr attr) {
        return template != null && template.getValue() == attr.getTemplate();
    }

    /**
     * 是否处理该gift
     *
     * @param giftId
     * @param attr
     * @return true:符合 false:不符合
     */
    protected boolean checkGift(String giftId, ActivityPlaneComponentAttr attr) {
        Map<String, GiftBO> giftMap = attr.getGiftPriceMap();
        if (MapUtils.isNotEmpty(giftMap)) {
            return giftMap.containsKey(giftId);
        }
        return attr.isAllGifts() || attr.getGiftIds().contains(giftId);
    }

    /**
     * 是否进行连击
     *
     * @param bo
     * @param attr
     * @return default value is {@code true}
     */
    protected boolean isGiftCombo(SendGiftBO bo, ActivityPlaneComponentAttr attr) {
        return attr.isCombo();
    }

    protected void after(SendGiftBO bo, ActivityPlaneComponentAttr attr) {

    }

    private GameecologyActivity.ActPlaneBanner.Builder buildPlaneBanner(GiftPlaneBO bo,
                                                                        ActivityPlaneComponentAttr attr) {
        Template template = getTemplate(attr.getTemplate());
        return GameecologyActivity.ActPlaneBanner.newBuilder()
                .setActId(attr.getActId())
                .setSid(bo.getSid())
                .setSsid(bo.getSsid())
                .setAsid(commonService.getAsid(bo.getSid()))
                .setCommbo(bo.getCount())
                .setChannel(commonService.getAsid(bo.getSid()))
                .setAnchor(getAnchorInfo(bo, template))
                .setUser(getUserInfo(bo.getSendUid(), template))
                .setGift(getGiftInfo(getGiftBO(bo.getGiftId(), attr), bo.getGiftNum()));
    }

    private UserBaseInfo getUserBaseInfo(long userUid, Template template) {
        UserBaseInfo userInfo = new UserBaseInfo();
        if (template == Template.Jiaoyou) {
            Optional.ofNullable(ftsBaseInfoBridgeClient.getFtsUserInfoMap(Arrays.asList(userUid)))
                    .flatMap(map -> Optional.ofNullable(map.get(userUid)))
                    .ifPresent(info -> {
                        userInfo.setUid(userUid);
                        userInfo.setNick(info.getNick());
                        userInfo.setLogo(info.getAvatar_url());
                        userInfo.setYyno(info.getImid());
                    });
        }
        if (userInfo.getUid() == 0) {
            Optional.ofNullable(commonService.getUserInfo(userUid, false)).ifPresent(info -> {
                userInfo.setUid(userUid);
                userInfo.setNick(info.getNick());
                userInfo.setLogo(info.getLogo());
                userInfo.setYyno(info.getYyno());
            });
        }
        return userInfo;
    }

    private GiftBO getGiftBO(String giftId, ActivityPlaneComponentAttr attr) {
        return attr.getGiftPriceMap().get(giftId);
    }

    public boolean antiPoaching(long sid, long ssid) {
        List<AntiPoachingInfo> list = ftsRoomManagerThriftClient.getAntiPoachingList();
        Set<String> set = new HashSet<>();
        if(list != null) {
            for (AntiPoachingInfo antiPoachingInfo : list) {
                if(antiPoachingInfo.isBillboard) {
                    String ting = antiPoachingInfo.getSid() + "_" + antiPoachingInfo.getSsid();
                    set.add(ting);
                }
            }
            log.info("antiPoachingInfo list:{}", GsonUtil.toJson(list));
            String key = sid+"_"+ssid;
            return set.contains(key);
        } else {
            log.info("antiPoachingInfos is null");
        }
        return false;
    }

    private long expireSeconds(long actId) {
        var actInfo = actInfoService.queryActivityInfo(actId);
        Random r = new Random();
        return (actInfo.getEndTime() - System.currentTimeMillis()) / 1000 + DateUtil.ONE_DAY_SECONDS + r.nextInt(3000);
    }
}
