package com.yy.gameecology.hdzj.element.component.attr.bean;

import lombok.Data;

/**
 * 夏日探险奖励信息
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class SummerAdventureReward {
    
    /**
     * 活动ID
     */
    private long actId;
    
    /**
     * 组件使用索引
     */
    private long cmptUseInx;
    
    /**
     * 用户UID
     */
    private long uid;
    
    /**
     * 主持UID
     */
    private long anchorUid;
    
    /**
     * CP成员标识 (格式: uid|anchorUid)
     */
    private String cpMember;
    
    /**
     * 探险序列号
     */
    private String adventureSeq;
    
    /**
     * 格子位置
     */
    private int gridPosition;
    
    /**
     * 奖励类型 (1:头像框 2:气泡 3:礼物 4:其他)
     */
    private int rewardType;
    
    /**
     * 奖励ID
     */
    private long rewardId;
    
    /**
     * 奖励名称
     */
    private String rewardName;
    
    /**
     * 奖励数量
     */
    private int rewardCount;
    
    /**
     * 奖励图标
     */
    private String rewardIcon;
    
    /**
     * 奖励描述
     */
    private String rewardDesc;
    
    /**
     * 是否稀有奖励
     */
    private boolean isRareReward;
    
    /**
     * 获得者类型 (1:用户 2:主持)
     */
    private int receiverType;
    
    /**
     * 获得者UID
     */
    private long receiverUid;
    
    /**
     * 发放状态 (1:待发放 2:已发放 3:发放失败)
     */
    private int issueStatus;
    
    /**
     * 发放时间
     */
    private long issueTime;
    
    /**
     * 创建时间
     */
    private long createTime;
}
