package com.yy.gameecology.hdzj.element.component.xmodule.aov;

import com.yy.gameecology.activity.bean.mq.HdzkAovGameEvent;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AovRankComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.UpdateRankingRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AovRankComponent extends BaseActComponent<AovRankComponentAttr> {

    @Override
    public Long getComponentId() {
        return ComponentId.AOV_RANK;
    }

    @HdzjEventHandler(value = HdzkAovGameEvent.class, canRetry = true)
    public void handleAovGame(HdzkAovGameEvent event, AovRankComponentAttr attr) {
        if (event.getState() != AovConst.GameState.RESULTED) {
            log.info("handleAovGame skip game state not resulted gameId:{} state:{}", event.getGameId(), event.getState());
            return;
        }

        updateKillRank(attr, event.getPhaseId(), event.getGameId(), event.getCamp1Members(), event.getEndTime());
        if (event.getCamp1Score() > 0) {
            updateWinRank(attr, event.getPhaseId(), event.getGameId(), event.getCamp1Members(), event.getEndTime());
        }
        updateKillRank(attr, event.getPhaseId(), event.getGameId(), event.getCamp2Members(), event.getEndTime());
        if (event.getCamp2Score() > 0) {
            updateWinRank(attr, event.getPhaseId(), event.getGameId(), event.getCamp2Members(), event.getEndTime());
        }
    }

    private void updateWinRank(AovRankComponentAttr attr, long phaseId, long gameId, List<HdzkAovGameEvent.GameMember> members, Date time) {
        for (HdzkAovGameEvent.GameMember member : members) {
            final long uid = member.getUid();
            String seq = String.format("aov_game_win_rank:%d:%d", gameId, uid);
            UpdateRankingRequest request = new UpdateRankingRequest();
            request.setBusiId(attr.getBusiId());
            request.setActId(attr.getActId());
            request.setSeq(seq);
            request.setActors(Map.of(attr.getPhaseRoleId(), String.valueOf(phaseId), attr.getUserRoleId(), String.valueOf(uid)));
            request.setItemId(attr.getWinItem());
            request.setCount(1);
            request.setScore(1);
            request.setTimestamp(time.getTime());
            request.setExtData(Collections.emptyMap());

            boolean rs = hdztRankingThriftClient.updateRankWithRetry(request, 3);
            log.info("updateWinRank with gameId:{} uid:{} rs:{}", gameId, uid, rs);
        }
    }

    private void updateKillRank(AovRankComponentAttr attr, long phaseId, long gameId, List<HdzkAovGameEvent.GameMember> members, Date time) {
        for (HdzkAovGameEvent.GameMember member : members) {
            if (member.getKillCnt() <= 0) {
                log.info("updateRank skip member with zero kill count gameId:{} uid:{}", gameId, member.getUid());
                continue;
            }

            final long uid = member.getUid();
            String seq = String.format("aov_game_kill_rank:%d:%d", gameId, uid);
            UpdateRankingRequest request = new UpdateRankingRequest();
            request.setBusiId(attr.getBusiId());
            request.setActId(attr.getActId());
            request.setSeq(seq);
            request.setActors(Map.of(attr.getPhaseRoleId(), String.valueOf(phaseId), attr.getUserRoleId(), String.valueOf(uid)));
            request.setItemId(attr.getKillItem());
            request.setCount(member.getKillCnt());
            request.setScore(member.getKillCnt());
            request.setTimestamp(time.getTime());
            request.setExtData(Collections.emptyMap());

            boolean rs = hdztRankingThriftClient.updateRankWithRetry(request, 3);
            log.info("updateKillRank with gameId:{} uid:{} kill:{} rs:{}", gameId, uid, member.getKillCnt(), rs);
        }
    }

    public long getPrePhaseScore(long actId, long prePhaseId, long uid) {
        if (prePhaseId <= 0) {
            return 0;
        }
        AovRankComponentAttr attr = tryGetUniqueComponentAttr(actId);
        if (attr == null) {
            return 0;
        }

        Map<String, String> ext = Map.of(RankExtParaKey.QUERY_RANK_TYPE, HdztRankType.SRC, RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, String.valueOf(prePhaseId));

        String member = String.valueOf(uid);
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, attr.getKillRankId(), attr.getPhaseId(), StringUtils.EMPTY, 1, member, ext);
        if (CollectionUtils.isEmpty(ranks)) {
            return 0;
        }

        return ranks.stream().filter(rank -> StringUtils.equals(rank.getMember(), member)).map(Rank::getScore).filter(score -> score > 0).findFirst().orElse(0L);
    }
}
