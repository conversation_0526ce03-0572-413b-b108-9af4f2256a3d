package com.yy.gameecology.activity.config.redis;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.Serializable;


@Configuration
public class RedisConfig1 extends BaseRedisConfig {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /* ##############################  gameecology redis  ############################# */

    public RedisConfig1(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        super(builderCustomizers);
    }

    @Bean
    @ConfigurationProperties("gameecology.group1.redis")
    public RedisProperties redisGroup1Properties() {
        return new RedisProperties();
    }

    @Bean
    public RedisConnectionFactory redisGroup1ConnectionFactory(RedisProperties redisGroup1Properties) {
        return new LettuceConnectionFactory(getRedisConfig(redisGroup1Properties), getClientConfig(redisGroup1Properties));
    }

    @Bean
    public StringRedisTemplate stringRedisGroup1Template(RedisConnectionFactory redisGroup1ConnectionFactory) {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(redisGroup1ConnectionFactory);
        return stringRedisTemplate;
    }

    @Bean
    public RedisTemplate<Serializable, Serializable> redisGroup1Template(RedisConnectionFactory redisGroup1ConnectionFactory) {
        RedisTemplate<Serializable, Serializable> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisGroup1ConnectionFactory);
        return redisTemplate;
    }

    @Bean
    public RedissonClient redissonClient1(RedisProperties redisGroup1Properties) {
        Config config = getRedissonConfig(redisGroup1Properties);
        config.setCheckLockSyncedSlaves(false);
        log.info("Creating RedissonClient for group1 with config: host={}, port={}, database={}",
                redisGroup1Properties.getHost(),
                redisGroup1Properties.getPort(),
                redisGroup1Properties.getDatabase());
        return Redisson.create(config);
    }

}
