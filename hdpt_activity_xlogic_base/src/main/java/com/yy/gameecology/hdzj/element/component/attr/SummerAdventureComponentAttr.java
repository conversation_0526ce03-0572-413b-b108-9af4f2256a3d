package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.SummerAdventureMapGrid;
import lombok.Data;

import java.util.List;

/**
 * 夏日探险组件属性配置
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class SummerAdventureComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private int busiId;

    @ComponentAttrField(labelText = "全程阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "cp榜单id")
    private long cpRankId;

    @ComponentAttrField(labelText = "cp日榜单id")
    private long cpDailyRankId;

    @ComponentAttrField(labelText = "cp 用户对主持贡献榜单id")
    private long cpContributeRankId;

    @ComponentAttrField(labelText = "cp 主持对用户贡献榜单id")
    private long cpAntiContributeRankId;

    @ComponentAttrField(labelText = "厅角色id")
    private String tingRoleId;

    @ComponentAttrField(labelText = "获得骰子所需礼物金额", remark = "CP一次性送礼金额，单位：元")
    private double diceGiftAmount = 131.4;

    @ComponentAttrField(labelText = "每次探险最大骰子数", remark = "每次参与探险时最多使用的骰子数量")
    private int maxDicePerAdventure = 30;

    @ComponentAttrField(labelText = "骰子上限", remark = "CP活动期间最多可获得的骰子数量")
    private int maxDiceLimit = 500;

    @ComponentAttrField(labelText = "地图格子数量", remark = "探险地图的格子总数")
    private int mapGridCount = 30;

    @ComponentAttrField(labelText = "地图格子配置", 
        subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = SummerAdventureMapGrid.class))
    private List<SummerAdventureMapGrid> mapGrids;

    @ComponentAttrField(labelText = "探险奖励任务ID", remark = "发放探险奖励的任务ID")
    private long adventureTaskId;

    @ComponentAttrField(labelText = "用户奖励包ID", remark = "用户获得奖励的包ID")
    private long userPackageId;

    @ComponentAttrField(labelText = "主持奖励包ID", remark = "主持获得奖励的包ID")
    private long anchorPackageId;

    @ComponentAttrField(labelText = "广播横幅ID", remark = "探险过程广播的横幅ID")
    private long broadcastBannerId;

    @ComponentAttrField(labelText = "统计推送群ID", remark = "实时统计推送到生产群的群ID")
    private String statisticsGroupId;

    @ComponentAttrField(labelText = "是否开启榜单统计", remark = "是否支持榜单功能")
    private boolean enableRanking = true;

    @ComponentAttrField(labelText = "是否开启玩法明细导出", remark = "是否支持玩法明细导出")
    private boolean enableDetailExport = true;
}
