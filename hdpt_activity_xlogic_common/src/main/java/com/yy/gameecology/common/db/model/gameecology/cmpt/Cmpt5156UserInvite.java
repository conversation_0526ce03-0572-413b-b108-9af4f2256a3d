package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-06-11 14:05
 **/
@Data
@TableColumn(underline = true)
public class Cmpt5156UserInvite implements Serializable {

    private static final long serialVersionUID = 1L;


    public static String TABLE_NAME = "cmpt_5156_user_invite";


    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5156UserInvite> ROW_MAPPER = null;
    /**
     * id
     */
    private Long id;

    /**
     * act_id
     */
    private Long actId;

    /**
     * cmpt_use_inx
     */
    private Long cmptUseInx;

    /**
     * uid
     */
    private Long uid;

    /**
     * 被邀请人uid
     */
    private Long invitedUid;

    /**
     * 被邀请人设备号
     */
    private String invitedHdid;

    /**
     * 邀请备注字段
     */
    private String invitedDesc;

    /**
     * 奖励数量
     */
    private Long awardAmount;

    /**
     * 0==待领取 1==已领取 -1==无需领取
     */
    private Integer awardReceiveState;

    /**
     * 100==邀请成功 200==金币达到上限
     */
    private Integer state;

    /**
     * create_time
     */
    private Date createTime;


}
