package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.acttask.TaskAwardConfig;
import com.yy.gameecology.activity.bean.acttask.TaskFinishInfoVo;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;

import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.TaskCompleteAwardComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 完成任务发奖
 */
@UseRedisStore
@RestController
@RequestMapping("/cmpt/taskCompleteAward")
@Component
public class TaskCompleteAwardComponent extends BaseActComponent<TaskCompleteAwardComponentAttr> {


    @Autowired
    private CommonBroadCastService commonBroadCastService;

    private static final String RED_DOT_KEY = "reddot:";

    private static final String FINISH_TASK_LIST_KEY = "finishTask:";

    private static final String TASK_STATIC_KEY = "task_static_";
    @Override
    public Long getComponentId() {
        return ComponentId.TASK_COMPLETE_AWARD_NOTICE;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void awardNotice(TaskProgressChanged event, TaskCompleteAwardComponentAttr attr) {
        log.info("TaskCompleteAwardComponent event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        if (event.getRankId() != attr.getTaskRankId()) {
            log.info("TaskCompleteAwardComponent rankId:{} not match", event.getRankId());
            return;
        }

        if (event.getRankId() == attr.getTaskRankId() && event.getPhaseId() == attr.getTaskPhaseId()) {
            List<TaskAwardConfig> awardList = attr.getAward();
            List<String> awardTexts = Lists.newArrayList();
            for( long startIndex = event.getStartTaskIndex()+1 ;startIndex<=event.getCurrTaskIndex();startIndex++) {
                long finalStartIndex = startIndex;
                TaskAwardConfig awardItem = awardList.stream().filter(v -> v.getTaskLevel() == finalStartIndex).findFirst().orElse(null);
                if (awardItem != null) {
                    awardTexts.add(awardItem.getAwardText());
                    log.info("TaskCompleteAwardComponent pc bro uid:{}, event:{}", event.getMember(), JSON.toJSONString(event));
                }else{
                    log.warn("TaskCompleteAwardComponent awardItem is null,event:{}", JSON.toJSONString(event));
                }
            }
            if(awardTexts.size()>0) {
                JSONObject json = new JSONObject(6);
                json.put("awardTexts", awardTexts);
                json.put("moduleName", attr.getModuleName());
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), "taskCompleteAwardNotice", JsonUtil.toJson(json), StringUtils.EMPTY, Convert.toLong(event.getMember()));
            }
        }

        if(attr.isAddFinishTaskList()){
            addUserFinishTask(event,attr);
        }

        if(attr.isShowRedDot()){
            addUserTaskRedDot(event,attr);
        }

        //更新每日任务用户完成数
        refreshTaskStaticRecord(event,attr);
    }


    private void addUserTaskRedDot(TaskProgressChanged event, TaskCompleteAwardComponentAttr attr) {
        String key = buildRedDotKey(attr, event.getMember());
        actRedisDao.incrValue(getRedisGroupCode(attr.getActId()), key, 1);
    }

    private String buildRedDotKey(TaskCompleteAwardComponentAttr attr,String member) {
        return makeKey(attr.getActId(), attr.getCmptId(), 500, RED_DOT_KEY + member);
    }


    //完成任务记录
    private void addUserFinishTask(TaskProgressChanged event, TaskCompleteAwardComponentAttr attr) {

        log.info("TaskCompleteAwardComponent addUserFinishTask event:{}",JSON.toJSONString(event));
        String redisCode = getRedisGroupCode(attr.getActId());
        String eventMemberSeq = (StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq()) + ":" + event.getMember();
        for( long startIndex = event.getStartTaskIndex()+1 ;startIndex<=event.getCurrTaskIndex();startIndex++) {
            long finalStartIndex = startIndex;
            List<TaskAwardConfig> awardList = attr.getAward();
            TaskAwardConfig awardItem = awardList.stream().filter(v -> v.getTaskLevel() == finalStartIndex).findFirst().orElse(null);
            if (awardItem != null) {
                TaskFinishInfoVo item = new TaskFinishInfoVo();
                item.setMember(event.getMember());
                item.setTaskName(awardItem.getTaskName());
                item.setAwardName(awardItem.getAwardName());
                item.setAwardCount(awardItem.getAwardCount());
                item.setAwardTime(event.getOccurTime());
                String finishTaskKey = buildFinishTaskListKey(attr, event.getMember());
                String finishTaskSeq = makeKey(attr, "seq:finishtask:" + eventMemberSeq + ":"+finalStartIndex);
                actRedisDao.lPushWithSeq(redisCode, finishTaskSeq, finishTaskKey, JSON.toJSONString(item), DateUtil.TWO_MONTH_SECONDS);
            }
        }
    }


    //过任务所有获奖组件用同一个key
    private String buildFinishTaskListKey(TaskCompleteAwardComponentAttr attr, String member) {

        return makeKey(attr.getActId(), attr.getCmptId(), 500, FINISH_TASK_LIST_KEY + member);
    }


    private void  refreshTaskStaticRecord(TaskProgressChanged event, TaskCompleteAwardComponentAttr attr) {
        String redisCode = getRedisGroupCode(attr.getActId());

        try {
            Date now = DateUtils.parseDate(event.getOccurTime(),DateUtil.DEFAULT_PATTERN);
            String dayCode = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
            String key = buildTaskStaticKey(attr,dayCode);
            for( long startIndex = event.getStartTaskIndex()+1 ;startIndex<=event.getCurrTaskIndex();startIndex++) {
                actRedisDao.hIncrByKey(redisCode,key,event.getRankId()+"|"+startIndex,1);
            }

        } catch (Exception e) {
            log.error("TaskCompleteAwardComponent refreshTaskStaticRecord event:{},error:{}", JSON.toJSON(event),e.getMessage());
        }
    }


    private String buildTaskStaticKey(TaskCompleteAwardComponentAttr attr ,String dayCode) {
        return makeKey(attr.getActId(), attr.getCmptId(), 500, TASK_STATIC_KEY + dayCode);
    }


    public  Map<String, Long> getTaskStaticInfo(long actId,String dayCode) {
        String redisCode = getRedisGroupCode(actId);
        String kye = makeKey(actId, this.getComponentId(), 500, TASK_STATIC_KEY + dayCode);
        Map<Object, Object> taskMap = actRedisDao.hGetAll(redisCode,kye);
        Map<String, Long> result = Maps.newHashMap();
        if(taskMap!=null) {
            taskMap.forEach((k, v) -> result.put(Convert.toString(k), Convert.toLong(v)));
        }

        return result;

    }




    /**
     * 查看所有已完成的历史任务
     * @param request
     * @param response
     * @param actId 活动id
     * @param cmptUseInx 序号可不填
     * @return
     */
    @GetMapping("/getFinishTaskList")
    public Response<List<TaskFinishInfoVo>> getFinishTaskList(HttpServletRequest request, HttpServletResponse response,
                                                              @RequestParam(name = "actId") long actId,
                                                              @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {

        long loginUid = getLoginYYUid(request, response);
        if (loginUid <= 0) {
            return Response.fail(401, "未登录！");
        }


        TaskCompleteAwardComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }

        String redisCode = getRedisGroupCode(actId);
        String finishTaskKey = buildFinishTaskListKey(attr, String.valueOf(loginUid));
        List<TaskFinishInfoVo> list = Lists.newArrayList();

        List<String> awardRecord = actRedisDao.lrange(redisCode, finishTaskKey, 0, 1000);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            for (String item : awardRecord) {
                TaskFinishInfoVo record = JSON.parseObject(item, TaskFinishInfoVo.class);
                list.add(record);
            }
        }

        return Response.success(list);
    }

    /**
     * 任意任务完成红点
     * @param req
     * @param resp
     * @param actId
     * @param cmptUseInx  可不传
     * @return
     */
    @GetMapping("/queryRedDot")
    public Response<Long> queryRedDot(HttpServletRequest req, HttpServletResponse resp, long actId,
                                      @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        TaskCompleteAwardComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(-2, "参数错误");
        }
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(-3, "未在活动时间内");
        }

        String key = buildRedDotKey(attr, String.valueOf(uid));
        return Response.success(Convert.toLong(actRedisDao.get(getRedisGroupCode(actId), key), 0));
    }

    /**
     * 点击红点
     * @param req
     * @param resp
     * @param actId
     * @param cmptUseInx
     * @return
     */
    @GetMapping("/clickRedDot")
    public Response clickRedDot(HttpServletRequest req, HttpServletResponse resp, long actId,
                                      @RequestParam(name = "cmptUseInx", defaultValue = "500") long cmptUseInx) {
        long uid = getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        TaskCompleteAwardComponentAttr attr = getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            return Response.fail(-2, "参数错误");
        }
        if (!actInfoService.inActTime(actId)) {
            return Response.fail(-3, "未在活动时间内");
        }
        String key = buildRedDotKey(attr, String.valueOf(uid));
        actRedisDao.del(getRedisGroupCode(actId), key);
        return Response.ok();
    }



}
