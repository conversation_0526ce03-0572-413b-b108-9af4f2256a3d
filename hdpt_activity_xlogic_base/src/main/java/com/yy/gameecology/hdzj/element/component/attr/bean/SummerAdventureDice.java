package com.yy.gameecology.hdzj.element.component.attr.bean;

import lombok.Data;

/**
 * 夏日探险骰子信息
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class SummerAdventureDice {
    
    /**
     * 活动ID
     */
    private long actId;
    
    /**
     * 组件使用索引
     */
    private long cmptUseInx;
    
    /**
     * 用户UID
     */
    private long uid;
    
    /**
     * 主持UID
     */
    private long anchorUid;
    
    /**
     * CP成员标识 (格式: uid|anchorUid)
     */
    private String cpMember;
    
    /**
     * 当前骰子数量
     */
    private int diceCount;
    
    /**
     * 总获得骰子数量
     */
    private int totalDiceCount;
    
    /**
     * 创建时间
     */
    private long createTime;
    
    /**
     * 更新时间
     */
    private long updateTime;
}
