package com.yy.gameecology.hdzj.element.component.xmodule.wow;

import cn.yy.ent.zhuiya.task.noaward.gen.pb.NoAwardTask;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SubChannelInfoVo;
import com.yy.gameecology.activity.bean.YoPopupMessage;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.mq.NaTaskFinishedEvent;
import com.yy.gameecology.activity.bean.mq.UserPostDetailKafkaEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.bean.mq.ZhuiwanSignInEvent;
import com.yy.gameecology.activity.bean.mq.hdzk.DayTaskCompleteEvent;
import com.yy.gameecology.activity.client.http.BigDaHttpClient;
import com.yy.gameecology.activity.client.thrift.FtsZhuiyaRecommendClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.client.yrpc.*;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.ClientInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.CurrencyBusId;
import com.yy.gameecology.common.consts.NewUserStatus;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.common.utils.WebUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.JumpTaskResp;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTask;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTaskItem;
import com.yy.gameecology.hdzj.bean.daytask.CurDayTaskItemVo;
import com.yy.gameecology.hdzj.bean.daytask.UpdateDayTaskReq;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.DayTaskComponent2;
import com.yy.gameecology.hdzj.element.component.KaiHeiQcoinsComponent;
import com.yy.gameecology.hdzj.element.component.LimitControlComponent;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.DayTaskComponent2Attr;
import com.yy.gameecology.hdzj.element.component.attr.WowTaskComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AppPopupConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.DayTaskConfig;
import com.yy.protocol.pb.shop.ZhuiyaShopServer;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.signin.ZhuiwanSign;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-07 16:04
 **/
@Component
@RestController
@RequestMapping("/5130")
public class WowTaskComponent extends BaseActComponent<WowTaskComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());


    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    private DayTaskComponent2 dayTaskComponent2;

    @Autowired
    private LimitControlComponent limitControlComponent;

    @Autowired
    private ZhuiyaLoginClient zhuiyaLoginClient;

    @Autowired
    private ZhuiyaNoAwardTaskClient zhuiyaNoAwardTaskClient;

    @Autowired
    private ZhuiyaShopServerClient zhuiyaShopServerClient;

    @Autowired
    private CurrencyClient currencyClient;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private KaiHeiQcoinsComponent kaiHeiQcoinsComponent;

    @Autowired
    private FtsZhuiyaRecommendClient ftsZhuiyaRecommendClient;

    /**
     * 自动填写用户Q币发奖信息延迟任务
     */
    private static final String DELAY_HANDLE_FILL_QCOIN_INFO = "fill_qcion_account_info_task";

    private final static String RECENTLY_SSIDINFO = "recently_ssidInfo";

    /**
     * 跳转任务时间
     * %s uid
     */
    private final static String JUMP_TASK_DATE = "jump_task_date:%s";

    /**
     * 激活任务时候，用于透传到追玩的活动任务id
     */
    private static final String ACTIVE_TASK_EXT_FIELD_NAME = "hdzkTaskId";

    /**
     * 需要手工激活的追玩任务id
     */
    private static final String MANUAL_ZHUI_YA_TASK_ID = "manual_zhuiya_task_id";

    /**
     * 需要自动激活的追玩任务id
     */
    private static final String AUTO_ACTIVE_ZHUI_YA_TASK_ID = "auto_active_zhuiya_task_id";

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private BigDaHttpClient bigDaHttpClient;
    @Autowired
    private MemberInfoService memberInfoService;

    @Override
    public Long getComponentId() {
        return ComponentId.WOW_TASK;
    }

    /**
     * 线上验证用的签到验证接口
     */
    @RequestMapping("/testOnSignInEvent")
    public Response<String> testOnSignInEvent(long actId, long uid) {
        if (!commonService.isGrey(actId) && SysEvHelper.isDeploy()) {
            return Response.fail(-1, "not grey");
        }
        var attr = tryGetUniqueComponentAttr(actId);
        ZhuiwanSignInEvent event = new ZhuiwanSignInEvent();
        event.setSignDate(new Date());
        event.setUid(uid);
        event.setApp(attr.getApp());
        onSignInEvent(event, attr);

        return Response.ok(Convert.toString(System.currentTimeMillis()));
    }

    @RequestMapping("/testAppPop")
    public Response<String> testAppPop(long actId, long uid, String title, String content) {
        if (!commonService.isGrey(actId) && SysEvHelper.isDeploy()) {
            return Response.fail(-1, "not grey");
        }
        var attr = tryGetUniqueComponentAttr(actId);
        AppPopupConfig config = attr.getAwardNotice().get(1);
        String seq = UUID.randomUUID().toString();
        String text = config.getText().replace("$awardNum", "1");
        doSendNotice(seq, uid, attr.getAwardPopUpTitle(), text, config.getLink());


        return Response.ok(Convert.toString(System.currentTimeMillis()));
    }

    @PostConstruct
    @NeedRecycle(notRecycle = true, author = "zengwenzhi")
    public void init() throws Exception {
        registerDelayQueue(DELAY_HANDLE_FILL_QCOIN_INFO, this::fillInAccountInfo);
    }

    /**
     * 预热用户最近登录频道
     */
    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLoginEvent(ZhuiwanLoginEvent event, WowTaskComponentAttr attr) {
        log.info("onZhuiwanLoginEvent event:{}", JSON.toJSONString(event));
        if (!event.getApp().equals(attr.getApp())) {
            return;
        }

        String member = Convert.toString(event.getUid());
        String inWhiteList = whitelistComponent.getConfigValue(attr.getActId(), attr.getOriWhiteListCmptIndex(), member);
        if (StringUtil.isBlank(inWhiteList)) {
            log.info("white list empty,uid:{}", event.getUid());
        }

        String ssidInfo = bigDaHttpClient.queryUserRecentlyVisitedChannels(member);
        if (StringUtil.isNotBlank(ssidInfo)) {
            commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), RECENTLY_SSIDINFO, member, ssidInfo);
        }
    }


    /**
     * 签到任务-----打卡任务数值累加
     * 注意：因为都是新用户，所以不存在上线当天，活动开始前已经完成签到任务，需要同步数据的情况！！！
     */
    @HdzjEventHandler(value = ZhuiwanSignInEvent.class, canRetry = true)
    public void onSignInEvent(ZhuiwanSignInEvent event, WowTaskComponentAttr attr) {
        if (!event.getApp().equals(attr.getApp())) {
            return;
        }

        //当日没有点击过签到按钮的需要拦截
        String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        if (notJumpTask(event.getUid(), attr, dateCode, TaskItemCode.SIGN)) {
            log.info("onSignInEvent not jump,uid:{},nowDateCode:{},jumpDateCode:{}", event.getUid(), dateCode, dateCode);
            return;
        }

        //更新任务进度
        boolean updateResult = updateSignTask(attr, event.getSignDate(), event.getUid());

        log.info("onSignInEvent done,uid:{},updateResult:{},event:{},attr:{}", event.getUid(), updateResult, JSON.toJSONString(event), JSON.toJSONString(attr));
    }

    @HdzjEventHandler(value = UserPostDetailKafkaEvent.class, canRetry = true)
    public void onUserPostDetailKafkaEvent(UserPostDetailKafkaEvent event, WowTaskComponentAttr attr) {
        log.info("onUserPostDetail event:{}", JSON.toJSONString(event));
        Date now = commonService.getNow(attr.getActId());
        if (!actInfoService.inActTime(now, attr.getActId())) {
            return;
        }

        //非指定的话题id
        if (attr.getPostTopicId() > 0 && !attr.getPostTopicId().equals(event.getTopicId())) {
            log.info("onUserPostDetail topicId not equal,uid:{},topicId:{},configTopicId:{}", event.getUid(), event.getTopicId(), attr.getPostTopicId());
            return;
        }

        //当日没有点击过签到按钮的需要拦截
        String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        if (notJumpTask(event.getUid(), attr, dateCode, TaskItemCode.POST)) {
            log.info("onUserPostDetail not jump,uid:{},nowDateCode:{},jumpDateCode:{}", event.getUid(), dateCode, dateCode);
            return;
        }


        //更新任务进度
        boolean updateResult = updatePostTask(event.getUid(), attr, dateCode);

        log.info("onUserPostDetail done,uid:{},updateResult:{},event:{},attr:{}", event.getUid(), updateResult, JSON.toJSONString(event), JSON.toJSONString(attr));

    }

    /**
     * 任务完成事件： 完成当天任务，激活下一天任务
     */
    @HdzjEventHandler(value = NaTaskFinishedEvent.class, canRetry = true)
    public void onNaTaskFinishedEvent(NaTaskFinishedEvent event, WowTaskComponentAttr attr) {
        log.info("onNaTaskFinishedEvent event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        String ext = event.getExpand();
        if (StringUtil.isBlank(ext)) {
            return;
        }
        JSONObject extObj = JSON.parseObject(ext);
        Long hdzkTaskId = extObj.getLong(ACTIVE_TASK_EXT_FIELD_NAME);
        if (hdzkTaskId == null) {
            return;
        }

        DayTaskComponent2Attr dayTaskComponentAttr = dayTaskComponent2.getComponentAttr(attr.getActId(), attr.getDayTaskCmptIndex());
        Optional<DayTaskConfig> dayTaskConfig = dayTaskComponentAttr.getDayTaskConfig().stream().filter(p -> p.getTaskId() == hdzkTaskId).findFirst();
        if (dayTaskConfig.isEmpty()) {
            return;
        }
        DayTaskConfig taskConfig = dayTaskConfig.get();

        //更新任务进度
        String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String seq = event.getTaskId() + "_" + event.getStage() + "_" + event.getUid() + "_" + event.getFinishedTime();
        if (updateTask(attr, seq, event.getUid(), dateCode, taskConfig.getPassItem())) {
            return;
        }

        log.info("onNaTaskFinishedEvent done,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
    }

    private boolean updatePostTask(long uid, WowTaskComponentAttr attr, String dateCode) {
        String seq = "post:" + uid + ":" + dateCode;
        return updateTask(attr, seq, uid, dateCode, TaskItemCode.POST);
    }

    private boolean notJumpTask(long uid, WowTaskComponentAttr attr, String dateCode, String taskItem) {
        String key = buildLastJumpTaskDate(uid);
        String jumpDateCode = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key, taskItem);
        return !dateCode.equals(jumpDateCode);
    }


    /**
     * 用户完成所有任务，发放奖励
     */
    @HdzjEventHandler(value = DayTaskCompleteEvent.class, canRetry = true)
    public void onDayTaskCompleteEvent(DayTaskCompleteEvent event, WowTaskComponentAttr attr) {
        log.info("onDayTaskCompleteEvent begin,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));


        //获奖顶部提醒
        if (attr.getAwardNotice().containsKey(event.getDayIndex())) {
            AppPopupConfig config = attr.getAwardNotice().get(event.getDayIndex());
            String seq = makeKey(attr, String.format("notice:%s:%s", event.getDayIndex(), event.getMemberId()));
            if (!SysEvHelper.isDeploy()) {
                seq = UUID.randomUUID().toString();
            }
            String text = config.getText().replace("$awardNum", Convert.toString(event.getAwardNum()));
            doSendNotice(seq, Convert.toLong(event.getMemberId()), attr.getAwardPopUpTitle(), text, config.getLink());
        }

        //TODO 更新任务完成统计
        //updateDayTaskStatic(attr, event.getDayIndex(), event.getSeq());

        //看下一级任务是否需要自动激活
        var dayAttr = dayTaskComponent2.getComponentAttr(attr.getActId(), attr.getDayTaskCmptIndex());
        int nextIndex = event.getDayIndex() + 1;
        List<DayTaskConfig> nextDayTaskConfigList = dayAttr.getDayTaskConfig()
                .stream()
                .filter(p -> p.getTaskDayIndex() == nextIndex)
                .toList();
        if (!CollectionUtils.isEmpty(nextDayTaskConfigList)) {
            for (DayTaskConfig config : nextDayTaskConfigList) {
                autoActiveNextTask(Convert.toLong(event.getMemberId()), attr, config);
            }
        }
    }

    @RequestMapping("/taskList")
    public Response<Map<String, Object>> taskList(long actId, long cmptIndex, Long userId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        if (userId != null) {
            uid = userId;
        }
        Map<String, Object> result = Maps.newHashMap();

        //任务读取
        WowTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(-2, "未配置");
        }
        CurDayTask curDayTask = dayTaskComponent2.queryCurDayTask(actId, attr.getDayTaskCmptIndex(), Convert.toString(uid));
        result.put("dayShowIndex", curDayTask.getDayShowIndex());
        //已完成打卡的天数
        result.put("signedDay", curDayTask.getSignedDay());

        //任务详情
        List<CurDayTaskItemVo> curDayTaskItemVos = getCurDayTaskItemVos(curDayTask, attr, uid);
        result.put("curTaskItem", curDayTaskItemVos);

        //全部任务完成
        boolean allComplete = curDayTask.allComplete(attr.getLastTaskDayIndex());
        result.put("allComplete", allComplete);

        return Response.success(result);
    }

    private @NotNull List<CurDayTaskItemVo> getCurDayTaskItemVos(CurDayTask curDayTask, WowTaskComponentAttr attr, long uid) {
        List<CurDayTaskItemVo> curDayTaskItemVos = Lists.newArrayList();


        //任务完成进度
        List<NoAwardTask.UserTaskId> taskIds = Lists.newArrayList();
        for (CurDayTaskItem taskItem : curDayTask.getCurTaskItem()) {

            CurDayTaskItemVo vo = new CurDayTaskItemVo();
            vo.setTaskName(taskItem.getTaskName());
            vo.setState(taskItem.getState());
            vo.setPassItem(taskItem.getPassItem());

            Map<String, Long> itemZhuiwanTaskId = attr.getTaskProcessShow().get(curDayTask.getDayShowIndex());
            if (itemZhuiwanTaskId != null && itemZhuiwanTaskId.containsKey(taskItem.getPassItem())) {
                long oriTaskId = itemZhuiwanTaskId.get(taskItem.getPassItem());
                NoAwardTask.UserTaskId userTaskId = NoAwardTask.UserTaskId.newBuilder()
                        .setTaskId(oriTaskId)
                        .setUid(uid)
                        .setStage(Convert.toString(curDayTask.getDayShowIndex()))
                        .build();
                taskIds.add(userTaskId);

                vo.setOriTaskId(oriTaskId);

            }

            curDayTaskItemVos.add(vo);
        }
        if (!CollectionUtils.isEmpty(taskIds)) {
            NoAwardTask.GetUserTaskReq getUserTaskReq = NoAwardTask.GetUserTaskReq.newBuilder()
                    .setUid(uid)
                    .addAllUserTaskId(taskIds).build();
            NoAwardTask.GetUserTaskRsp rsp = zhuiyaNoAwardTaskClient.getUserTask(getUserTaskReq);
            if (!CollectionUtils.isEmpty(rsp.getUserTaskList())) {
                for (CurDayTaskItemVo vo : curDayTaskItemVos) {
                    Optional<NoAwardTask.UserTaskVo> userTaskVo = rsp.getUserTaskList()
                            .stream()
                            .filter(p -> p.getUserTaskId().getTaskId() == vo.getOriTaskId()).findFirst();
                    if (userTaskVo.isPresent()) {
                        vo.setOriTaskProcess(userTaskVo.get().getTaskProgress());
                        vo.setOriTaskGoal(userTaskVo.get().getTaskGoal());
                    }
                }
            }
        }
        return curDayTaskItemVos;
    }

    /**
     * 点击去完成，激活任务
     */
    @RequestMapping("/jumpTask")
    public Response<JumpTaskResp> jumpTask(long actId, long cmptIndex, String curTaskItem, Integer newVersion) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "not login");
        }
        log.info("jumpTask begin,actId:{},cmptIndex:{},uid:{},curTaskItem:{}", actId, cmptIndex, uid, curTaskItem);

        var attr = getComponentAttr(actId, cmptIndex);

        //活动已结束
        if (!actInfoService.inActTime(attr.getActId())) {
            log.info("act end,actId:{},uid:{},item:{}", actId, uid, curTaskItem);
            return Response.fail(3, "活动已结束");
        }

        boolean isPoolOut = isPoolOut(attr);
        if (isPoolOut) {
            log.info("act end,actId:{},uid:{},item:{}", actId, uid, curTaskItem);
            return Response.fail(1, "奖池已抢完，活动已结束");
        }

        //记录跳转时间
        saveJumpTaskDate(attr, uid, curTaskItem);

        try {
            JumpTaskResp resp = manualActiveUserTask(attr, uid, curTaskItem, newVersion);
            log.info("jumpTask done,actId:{},cmptIndex:{},uid:{},curTaskItem:{},result:{}", actId, cmptIndex, uid, curTaskItem, JSON.toJSONString(resp));
            return Response.success(resp);

        } catch (SuperException e) {
            log.warn("jumpTask warn,actId:{},cmptIndex:{},uid:{},curTaskItem:{},e:{}", actId, cmptIndex, uid, curTaskItem, e.getMessage(), e);
            return Response.fail(2, e.getMessage());
        } catch (Exception e) {
            log.warn("jumpTask error,actId:{},cmptIndex:{},uid:{},curTaskItem:{},e:{}", actId, cmptIndex, uid, curTaskItem, e.getMessage(), e);
            return Response.fail(500, "网络超时");
        }

    }

    /**
     * 频道跳转完成任务上报
     */
    @RequestMapping("/enterChannel")
    public Response<Map<String, Object>> enterChannel(long actId, long cmptIndex) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "not login");
        }
        var attr = getComponentAttr(actId, cmptIndex);
        String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String seq = String.format("task_enterChannel_%s_%s", dateCode, uid);
        updateTask(attr, seq, uid, dateCode, TaskItemCode.JUMP_CHANNEL);
        log.info("enterChannel,actId:{},uid:{}", actId, uid);
        return Response.ok();
    }

    /**
     * 金币信息查询
     */
    @RequestMapping("/coin")
    public Response<Map<String, Object>> coin(long actId, long cmptIndex) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "not login");
        }
        var attr = getComponentAttr(actId, cmptIndex);
        long pendingCoin = currencyClient.balance(uid, CurrencyBusId.ACT, attr.getTaskAwardCId());

        long alreadyWithdrawn = 0;
        var req = ZhuiyaShopServer.BuyHistoryReq.newBuilder()
                .setShop(ZhuiyaShopServer.Shop.HDPT_ACTIVITY)
                .setUid(uid)
                .setShopId(Convert.toString(actId))
                .build();
        var rsp = zhuiyaShopServerClient.buyHistory(req);
        if (!CollectionUtils.isEmpty(rsp.getBuyShopItemLogList())) {
            alreadyWithdrawn = rsp.getBuyShopItemLogList().stream().mapToLong(p -> p.getCount() * p.getItemPrice().getPrice()).sum();
        }

        long totalBalance = limitControlComponent.queryPoolBalance(actId, attr.getPoolLimitCmptIndex(), attr.getCoinLimitId());

        Map<String, Object> result = Maps.newHashMap();
        result.put("pendingCoin", pendingCoin);
        result.put("alreadyWithdrawn", alreadyWithdrawn);
        result.put("totalBalance", totalBalance);

        return Response.success(result);
    }


    /**
     * 金币提取
     */
    @RequestMapping("/withdrawCoin")
    public Response<Map<String, Object>> withdrawCoin(long actId, long cmptIndex,
                                                      HttpServletRequest request, HttpServletResponse response,
                                                      String verifyCode, String recordId, String verifyToken) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "not login");
        }
        log.info("withdrawCoin,actId:{},uid:{}", actId, uid);
        var attr = getComponentAttr(actId, cmptIndex);

        //活动已结束
        if (!actInfoService.inActTime(attr.getActId())) {
            return Response.fail(1, "活动已结束");
        }
        int userState = queryUserStatus(attr, uid);
        if (userState == NewUserStatus.NOT_IN_LIST) {
            log.info("not in white list,actId:{},uid:{}", actId, uid);
            return Response.fail(2, "没有活动参与资格");
        }

        //风控
        if (StringUtil.isNotBlank(attr.getRiskStrategyKey())) {
            try {
                zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);
            } catch (SuperException e) {
                log.warn("login doRiskCheck warn,actId:{},uid:{},e:{},e", actId, uid, e.getMessage(), e);
                if (e.getData() != null) {
                    return Response.fail(e.getCode(), e.getMessage(), Map.of("riskRecheck", e.getData()));
                } else {
                    return Response.fail(e.getCode(), e.getMessage());
                }
            } catch (Exception e) {
                log.error("login doRiskCheck error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
                return Response.fail(SuperException.E_FAIL, "网络超时");
            }
        }

        long pendingCoin = currencyClient.balance(uid, CurrencyBusId.ACT, attr.getTaskAwardCId());

        //只能提取1000的整数倍
        long realWithDrawCoin = (pendingCoin / 1000) * 1000;

        var releaseShowItem = attr.getShopItem().get(realWithDrawCoin);
        if (releaseShowItem == null) {
            log.info("no config coin,actId:{},uid:{},pendingCoin:{},realWithDrawCoin:{}", actId, uid, pendingCoin, realWithDrawCoin);
            return Response.fail(3, "累计魔兽金币满1000即可提取");
        }


        //扣减奖池(先扣奖池，再兑换，宁愿多扣，也不要少扣)
        String seq = makeKey(attr, String.format("withdrawCoin_%s_%s", uid, System.currentTimeMillis()));
        CommonDataDao.ValueIncResult result = limitControlComponent.valueIncrIgnoreWithLimit(attr.getActId(), attr.getPoolLimitCmptIndex(), attr.getPoolLimitCmptIndex(), seq, realWithDrawCoin);
        if (result.isViolateLimit()) {
            log.warn("withdrawCoin isViolateLimit,actId:{},uid:{}", actId, uid);
            return Response.fail(SuperException.AWARD_POOL_NOTENOUGH, "奖池已用完");
        }

        //虚拟货币兑换成金币
        ClientInfo clientInfo = WebUtil.getClientInfo(request);
        var buyClientInfo = ZhuiyaShopServer.ClientInfo.newBuilder()
                .setApp(ZhuiyaShopServerYrpc.getApp(clientInfo.app()))
                .setDeviceId(Convert.toString(clientInfo.hdid()))
                .setIp(Convert.toString(clientInfo.ip()))
                .setPlatform(ZhuiyaShopServerYrpc.getPlatform(clientInfo.platform()))
                .build();
        var req = ZhuiyaShopServer.BuyShopItemReq.newBuilder()
                .setShop(ZhuiyaShopServer.Shop.HDPT_ACTIVITY)
                .setUid(uid)
                .setShopId(Convert.toString(actId))
                .setItemId(releaseShowItem.getShopItem())
                .setCount(1)
                .setClientInfo(buyClientInfo)
                .build();
        var resp = zhuiyaShopServerClient.buyShopItem(req);
        log.info("withdrawCoin uid:{} actId:{} itemId:{} resp:{}", uid, actId, releaseShowItem, JsonFormat.printToString(resp));
        if (resp.getCode() != ZhuiyaShopServer.BuyCode.SUCCESS) {
            return Response.fail(resp.getCode().getNumber(), resp.getMessage());
        } else {
            //自动填写领奖信息
            KaiHeiQcoinsComponent.FillQCoinAccountInfo accountInfo = new KaiHeiQcoinsComponent.FillQCoinAccountInfo();
            accountInfo.setActId(attr.getActId());
            accountInfo.setUid(uid);
            accountInfo.setTaskId(attr.getCoinAwardPoolTaskId());
            List<Long> packageIds = attr.getShopItem().values().stream()
                    .map(WowTaskComponentAttr.AwardShopItemConfig::getPackageId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            accountInfo.setPackageIds(packageIds);
            publishDelayEvent(attr, DELAY_HANDLE_FILL_QCOIN_INFO, accountInfo, System.currentTimeMillis() + DateUtils.MILLIS_PER_MINUTE);
            log.info("publishDelayEvent auto fill account,uid:{},accountInfo:{}", uid, JSON.toJSONString(accountInfo));
        }

        return Response.ok();
    }


    /**
     * 用户最近访问过的频道
     */
    @RequestMapping("/userChannelList")
    public Response<Map<String, Object>> userChannelList(long actId, long cmptIndex) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(-1, "not login");
        }
        Map<String, Object> result = Maps.newHashMap();
        List<SubChannelInfoVo> vos = Lists.newArrayList();
        var attr = getComponentAttr(actId, cmptIndex);
        String info = commonDataDao.hashValueGet(actId, attr.getCmptId(), attr.getCmptUseInx(), RECENTLY_SSIDINFO, Convert.toString(uid));
        List<String> sids = Lists.newArrayList();
        if (StringUtil.isNotBlank(info)) {
            String[] infoItemArray = info.split(",");
            for (String infoItem : infoItemArray) {
                String[] ssidArray = infoItem.split("_");
                SubChannelInfoVo vo = new SubChannelInfoVo();
                long sid = Convert.toLong(ssidArray[0].trim(), 0);
                vo.setSid(sid);
                long ssid = Convert.toLong(ssidArray[1].trim(), 0);
                //如果是顶级频道，海度的数据ssid是0，这里转换下
                vo.setSsid(ssid == 0 ? sid : ssid);
                vos.add(vo);

                sids.add(vo.getSid() + "_" + vo.getSsid());
            }
        }

        //填充基础信息
        Map<String, MemberInfo> subChannelMemberInfo = memberInfoService.querySubChannelMemberInfo(sids);
        for (SubChannelInfoVo vo : vos) {
            var suChannelInfo = subChannelMemberInfo.get(vo.getSid() + "_" + vo.getSsid());
            if (suChannelInfo != null) {
                vo.setName(suChannelInfo.getName());
                vo.setAsid(Convert.toLong(suChannelInfo.getAsid(), 0));
                vo.setLogo(suChannelInfo.getLogo());
            }
        }


        result.put("channelList", vos);
        return Response.success(result);
    }


    /**
     * 气泡提醒文案
     */
    public String getNoticeTips(long actId, long cmptIndex, long uid) {
        WowTaskComponentAttr attr = getComponentAttr(actId, cmptIndex);
        Date currentDate = commonService.getNow(actId);

        String tips = null;

        //奖池已消耗完，不弹出气泡
        if (isPoolOut(attr)) {
            return tips;
        }

        Date lastCompleteDay = null;
        CurDayTask curDayTask = dayTaskComponent2.queryCurDayTask(actId, attr.getDayTaskCmptIndex(), Convert.toString(uid));
        if (curDayTask != null && StringUtil.isNotBlank(curDayTask.getCompleteDayCode())) {
            lastCompleteDay = DateUtil.getDate(curDayTask.getCompleteDayCode(), DateUtil.PATTERN_TYPE2);
            long offsetDay = DateUtil.getDays(lastCompleteDay, currentDate);
            //超过N天未打开
            if (offsetDay > attr.getSignTaskTipsDay()) {
                long left = attr.getLastTaskDayIndex() - curDayTask.getSignedDay();
                if (left > 0) {
                    tips = attr.getSignTaskTips()
                            .replace("$notSign", Convert.toString(offsetDay))
                            .replace("$leftDay", Convert.toString(left));
                }
            }
        }
        log.info("getNoticeTips,actId:{},uid:{},tips:{},currentDate:{},lastCompleteDay:{}", actId, uid, tips, DateUtil.format(currentDate), DateUtil.format(lastCompleteDay));
        return tips;
    }


    /**
     * 记录最近一次点击时间，用于判断签到事件是否可以自动完成任务
     */
    private void saveJumpTaskDate(WowTaskComponentAttr attr, long uid, String taskItem) {
        String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
        String key = buildLastJumpTaskDate(uid);
        commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key, taskItem, dateCode);
    }

    /**
     * 触发需要手工点击跳转任务后才需要激活的任务
     */
    public JumpTaskResp manualActiveUserTask(WowTaskComponentAttr attr, long uid, String curTaskItem, Integer newVersion) {

        if (TaskItemCode.SIGN.equals(curTaskItem)) {
            return new JumpTaskResp(manualActiveSignTask(attr, uid, curTaskItem), 0, 0);
        } else if (TaskItemCode.POST.equals(curTaskItem)) {
            return new JumpTaskResp(manualActivePostTask(attr, uid, curTaskItem, newVersion), 0, 0);
        } else if (TaskItemCode.JUMP_CHANNEL.equals(curTaskItem)) {
            //进入频道任务用补量库
            ChannelInfo channelInfo = ftsZhuiyaRecommendClient.getDirectRecommendLibrary(attr.getDirectRecommendLibrary());
            if (channelInfo == null) {
                log.warn("getDirectRecommendLibrary empty");
                channelInfo = new ChannelInfo(attr.getDirectRecommendDefaultJumpSid(), attr.getDirectRecommendDefaultJumpSsId());
            }
            return new JumpTaskResp("", channelInfo.getSid(), channelInfo.getSsid());
        } else {
            String jumpUrl = manualActiveZhuiyaTask(attr, uid, curTaskItem);
            return new JumpTaskResp(resolveManualActiveZhuiyaTaskJumpUrl(attr, jumpUrl, curTaskItem, newVersion), 0, 0);
        }
    }

    public String resolveManualActiveZhuiyaTaskJumpUrl(WowTaskComponentAttr attr, String oriJumpUrl, String curTaskItem, Integer newVersion) {
        if (TaskItemCode.POST_LIKE.equals(curTaskItem)) {
            return Const.isOne(newVersion)
                    ? attr.getNewAppBrowseJumpUrl().replace("$taskId", "0").replace("$taskType", "0")
                    : attr.getPostJumpUrl();
        }
        if (StringUtil.isBlank(oriJumpUrl)) {
            return oriJumpUrl;
        }
        if (TaskItemCode.BROWSE_TOPICS.equals(curTaskItem)) {
            //zhuiwan://community/browseTask/9/10081
            String[] array = oriJumpUrl.split("/");
            String taskId = array[array.length - 2];
            String taskType = array[array.length - 1];
            String browsTopicNewJumpUrl = attr.getNewAppBrowseJumpUrl().replace("$taskId", taskId).replace("$taskType", taskType);
            return Const.isOne(newVersion) ? browsTopicNewJumpUrl : oriJumpUrl;
        }

        return oriJumpUrl;
    }

    public String manualActiveSignTask(WowTaskComponentAttr attr, long uid, String curTaskItem) {

        ZhuiwanSign.LastSignInRecordReq req = ZhuiwanSign.LastSignInRecordReq.newBuilder().setApp(ZhuiyaPbCommon.App.APP_YOMI).setUid(uid).build();
        ZhuiwanSign.LastSignInRecordRsp rsp = zhuiyaLoginClient.queryLastSignInRecord(req);
        if (rsp == null || rsp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
            log.error("queryLastSignInRecord error,req:{},rsp:{}", JsonFormat.printToString(req), rsp == null ? null : JsonFormat.printToString(rsp));
            return attr.getSignTaskJumpUrl();
        }
        String lastSignDate = rsp.getResult().getLastSignDate();
        String today = DateUtil.format(new Date(), DateUtil.PATTERN_TYPE2);
        if (today.equals(lastSignDate)) {
            boolean result = updateSignTask(attr, commonService.getNow(attr.getActId()), uid);
            log.info("syncCurDaySign done,uid:{},result:{}", uid, result);
            throw new SuperException("您今日已在福利专区签到，任务已完成", Code.E_DUP_DATA.getCode());
        }

        return attr.getSignTaskJumpUrl();
    }

    public String manualActivePostTask(WowTaskComponentAttr attr, long uid, String curTaskItem, Integer newVersion) {
        Date now = new Date();
        Date start = DateUtil.getDayBeginTime(now);
        Date end = DateUtil.getDayEndTime(now);
        long postCount = zhuiWanPrizeIssueServiceClient.queryUserPostCount(uid, start, end, attr.getPostTopicId());
        if (postCount > 0) {
            String today = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
            boolean result = updatePostTask(uid, attr, today);
            log.info("manualActivePostTask done,uid:{},result:{}", uid, result);
            throw new SuperException("您今日已发帖，任务已完成", Code.E_DUP_DATA.getCode());
        }

        return Const.isOne(newVersion) ? attr.getNewAppPostJumpUrl() : attr.getPostJumpUrl();
    }

    public String manualActiveZhuiyaTask(WowTaskComponentAttr attr, long uid, String curTaskItem) {
        long actId = attr.getActId();
        String jumpUrl = StringUtil.EMPTY;
        List<DayTaskConfig> dayTaskConfigs = dayTaskComponent2.queryDayTaskConfig(actId, attr.getDayTaskCmptIndex(), Convert.toString(uid));
        Optional<DayTaskConfig> dayTaskConfig = dayTaskConfigs
                .stream()
                .filter(p -> p.getPassItem().equals(curTaskItem) && p.getTaskExt().contains(MANUAL_ZHUI_YA_TASK_ID))
                .findFirst();
        if (dayTaskConfig.isEmpty()) {
            return jumpUrl;
        }
        DayTaskConfig taskConfig = dayTaskConfig.get();
        JSONObject extData = JSON.parseObject(taskConfig.getTaskExt());
        JSONArray zhuiyaTaskIds = extData.getJSONArray(MANUAL_ZHUI_YA_TASK_ID);
        long taskStartTime = DateUtil.getDayBeginTime(new Date()).getTime();
        var activityInfo = actInfoService.queryActivityInfo(actId);
        long endTime = activityInfo.getEndTime();

        for (Object taskIdObj : zhuiyaTaskIds) {
            long taskId = Convert.toLong(taskIdObj);
            long activeTime = attr.getActiveTimeDayFirstZhuiyaTaskId().contains(taskId)
                    ? DateUtil.getDayBeginTime(new Date()).getTime()
                    : System.currentTimeMillis();

            boolean reset = !attr.getNotResetZhuiyaTaskId().contains(taskId);
            String expand = JSON.toJSONString(ImmutableMap.of(ACTIVE_TASK_EXT_FIELD_NAME, taskConfig.getTaskId()));
            NoAwardTask.ActiveUserTaskReq req = NoAwardTask.ActiveUserTaskReq
                    .newBuilder().setUid(uid)
                    .setTaskId(taskId)
                    .setStage(Convert.toString(taskConfig.getTaskDayIndex()))
                    .setStartTime(taskStartTime)
                    .setEndTime(endTime)
                    .setActiveTime(activeTime)
                    .setResetProgress(reset)
                    .setExpand(expand)
                    .build();
            NoAwardTask.ActiveUserTaskRsp rsp = zhuiyaNoAwardTaskClient.activeUserTask(req);
            boolean rspError = rsp == null || rsp.getCode() != 0;
            if (rspError) {
                log.error("activeUserTask error,req:{},rsp:{}", JsonFormat.printToString(req), rsp == null ? null : JsonFormat.printToString(rsp));
                continue;
            }
            //已完成任务
            if (rsp.getUserTask().getState() == 1) {
                String dateCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
                String seq = "synctask:" + taskId + ":" + uid;
                updateTask(attr, seq, uid, dateCode, curTaskItem);
                throw new SuperException("任务已完成", Code.E_DUP_DATA.getCode());
            }
            //取任意1个
            if (StringUtil.isNotBlank(rsp.getUserTask().getJumpUrl())) {
                jumpUrl = rsp.getUserTask().getJumpUrl();
            }

        }
        return jumpUrl;
    }

    private void autoActiveNextTask(long uid, WowTaskComponentAttr attr, DayTaskConfig taskConfig) {
        log.info("autoActiveNextTask,uid:{},taskConfig:{}", uid, JSON.toJSONString(taskConfig));
        String taskExt = taskConfig.getTaskExt();
        if (StringUtil.isBlank(taskExt)) {
            return;
        }
        JSONObject taskExtObj = JSON.parseObject(taskExt);
        if (!taskExtObj.containsKey(AUTO_ACTIVE_ZHUI_YA_TASK_ID)) {
            return;
        }
        log.info("autoActiveNextTask begin,uid:{},taskConfig:{}", uid, JSON.toJSONString(taskConfig));
        //有配置要激活下一级任务的，才需要激活
        JSONArray zhuiyaTaskIds = taskExtObj.getJSONArray(AUTO_ACTIVE_ZHUI_YA_TASK_ID);
        long taskStartTime = DateUtil.getDayBeginTime(new Date()).getTime();
        var activityInfo = actInfoService.queryActivityInfo(attr.getActId());
        long endTime = activityInfo.getEndTime();

        for (Object taskIdObj : zhuiyaTaskIds) {
            long taskId = Convert.toLong(taskIdObj);
            //第15天自动激活的任务，要次日0点才能开始做
            long activeTime = attr.getActiveTimeDayFirstZhuiyaTaskId().contains(taskId)
                    ? DateUtil.getDayBeginTime(DateUtil.add(new Date(), Const.ONE)).getTime()
                    : System.currentTimeMillis();

            boolean reset = !attr.getNotResetZhuiyaTaskId().contains(taskId);
            String expand = JSON.toJSONString(ImmutableMap.of(ACTIVE_TASK_EXT_FIELD_NAME, taskConfig.getTaskId()));
            NoAwardTask.ActiveUserTaskReq taskReq = NoAwardTask.ActiveUserTaskReq
                    .newBuilder().setUid(uid)
                    .setTaskId(taskId)
                    .setStage(Convert.toString(taskConfig.getTaskDayIndex()))
                    .setStartTime(taskStartTime)
                    .setEndTime(endTime)
                    .setActiveTime(activeTime)
                    .setResetProgress(reset)
                    .setExpand(expand)
                    .build();
            NoAwardTask.ActiveUserTaskRsp rsp = zhuiyaNoAwardTaskClient.activeUserTask(taskReq);
            log.info("autoActiveNextTask done,uid:{},taskId:{},stage:{}", uid, taskId, taskReq.getStage());
            if (rsp == null || rsp.getCode() != 0) {
                log.error("autoActiveNextTask error,req:{},rsp:{}", JsonFormat.printToString(taskReq), rsp == null ? null : JsonFormat.printToString(rsp));
            }
        }
    }

    /**
     * 更新福利大厅签到任务
     */
    private boolean updateSignTask(WowTaskComponentAttr attr, Date taskDate, long uid) {
        //更新任务进度
        Date signDate = attr.isSignTaskUseTestTime() ? commonService.getNow(attr.getActId()) : taskDate;
        String dateCode = DateUtil.format(signDate, DateUtil.PATTERN_TYPE2);
        String seq = "sign:" + uid + ":" + dateCode;
        return updateTask(attr, seq, uid, dateCode, TaskItemCode.SIGN);
    }


    private boolean updateTask(WowTaskComponentAttr attr, String seq, long uid, String dateCode, String item) {
        log.info("updateTask seq:{},uid:{},dayCode:{},item:{}", seq, uid, dateCode, item);
        //不在活动时间范围内
        if (!actInfoService.inActTime(attr.getActId())) {
            log.info("updateTask act end");
            return false;
        }
        //新用户任务资格名单判断
        if (!isNewUser(attr, uid)) {
            log.info("updateTask return,not new user,uid:{}", uid);
            return false;
        }

        //奖池消耗完
        if (isPoolOut(attr)) {
            log.info("updateTask pool out");
            return false;
        }

        //更新任务进度
        UpdateDayTaskReq req = new UpdateDayTaskReq();
        req.setActId(attr.getActId());
        req.setCmptIndex(attr.getDayTaskCmptIndex());
        req.setSeq(seq);
        req.setMember(Convert.toString(uid));
        req.setDayCode(dateCode);
        req.setItem(item);
        req.setValue(1);
        dayTaskComponent2.updateTask(req);

        return true;
    }

    /**
     * 顶部提醒
     */
    public void doSendNotice(String seq, long uid, String title, String message, String link) {
        Map<String, String> extend = Maps.newHashMapWithExpectedSize(2);
        //客户端不用等待首页弹窗完成也能显示
        extend.put("ignoreMainPopup", "1");

        YoPopupMessage yoMessage = YoPopupMessage.builder()
                .app("yomi")
                //默认android,-1 安卓+ios
                .platform(-1)
                .title(title)
                .content(Base64Utils.encodeToString(message.getBytes()))
                .innerContent(message)
                .icon("https://ihdpt.bs2cdn.yy.com/**********/jewyejjsy4q8jd53bibj5jbmdppqgzwi.png")
                .extend(extend)
                .link(link).build();
        zhuiWanPrizeIssueServiceClient.sendPopupMessage(seq, uid, yoMessage);
    }

    public void fillInAccountInfo(WowTaskComponentAttr attr, Object object) {
        if (!(object instanceof KaiHeiQcoinsComponent.FillQCoinAccountInfo info)) {
            log.error("fillInAccountInfo type error,object:{}", JSON.toJSONString(object));
            return;
        }
        log.info("fillInAccountInfo,info:{}", JSON.toJSONString(info));
        kaiHeiQcoinsComponent.fillInAccountInfo(info);
    }


    private boolean isPoolOut(WowTaskComponentAttr attr) {
        long balance = limitControlComponent.queryPoolBalance(attr.getActId(), attr.getPoolLimitCmptIndex(), attr.getCoinLimitId());
        return balance <= 0;
    }

    private boolean isNewUser(WowTaskComponentAttr attr, long uid) {
        return NewUserStatus.NEW_USER == queryUserStatus(attr, uid);
    }

    /**
     * -1未进入资格名单
     * 1 新用户
     * 0 老用户
     */
    public int queryUserStatus(WowTaskComponentAttr attr, long uid) {
        Integer state = whitelistComponent.getConfigValue(attr.getActId(), attr.getLoginUserWhiteListCmptIndex(), Convert.toString(uid), Integer.class);
        if (state == null) {
            return NewUserStatus.NOT_IN_LIST;
        }

        return Convert.toInt(state, NewUserStatus.NOT_IN_LIST);
    }


    private String buildLastJumpTaskDate(long uid) {
        return String.format(JUMP_TASK_DATE, uid);
    }

    interface TaskItemCode {
        /**
         * 签到任务
         */
        String SIGN = "ZHUIYA_YOMI_SIGN";
        /**
         * 进入频道任务
         */
        String JUMP_CHANNEL = "JUMP_CHANNEL";
        /**
         * 频道停留任务
         */
        String STAY_CHANNEL = "STAY_CHANNEL";
        /**
         * 流量话题广场任务
         */
        String BROWSE_TOPICS = "BROWSE_TOPICS";
        /**
         * 广场发帖任务
         */
        String POST = "POST";

        /**
         * 点赞任务
         */
        String POST_LIKE = "POST_LIKE";
        /**
         * 充值任务或者打赏任务1
         */
        String RECHARGE1 = "RECHARGE1";

        /**
         * 充值任务或者打赏任务2
         */
        String RECHARGE2 = "RECHARGE2";
    }
}
