package com.yy.gameecology.common.db.model.gameecology;


import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-11-30 20:36
 **/
@TableColumn(underline = true)
public class ActResult implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "act_result";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"act_id", "type", "group_id", "rank"};

    public static RowMapper<ActResult> ROW_MAPPER = null;

    /**
     * act_id
     */
    private Long actId;

    /**
     * 1-交友 2-约战 3-宝贝 4-互动全品类 5-陪玩
     */
    private Integer type;

    /**
     * 分组id
     */
    private String groupId;

    /**
     * 排名，从1开始算起
     */
    private Integer rank;

    /**
     * 分组名称，分组id的冗余字段，这里不单独弄表了
     */
    private String groupName;

    /**
     * 成员id(后台程序管理和填入)
     */
    private String memberId;

    /**
     * 角色类型 200-主播 400-公会 401-厅 ; cp的话也搞成200
     */
    private Integer roleType;

    /**
     * 称号
     */
    private String title;

    /**
     * 从中台哪个榜单id拿数据
     */
    private Integer hdztRankId;

    /**
     * 从中台哪个阶段拿数据
     */
    private Integer hdztPhaseId;

    /**
     * 从榜单拿数据的成员名次
     */
    private Integer hdztRank;

    /**
     * 产生榜单名次的最早时间
     */
    private Date genDataTime;

    /**
     * 1-发布状态 数据展示给前端，其他的话元素也要返回，但是member_id不展示(后台程序管理和填入)
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    private String extData;

    private String contributor;

    public ActResult() {
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getHdztRankId() {
        return hdztRankId;
    }

    public void setHdztRankId(Integer hdztRankId) {
        this.hdztRankId = hdztRankId;
    }

    public Integer getHdztPhaseId() {
        return hdztPhaseId;
    }

    public void setHdztPhaseId(Integer hdztPhaseId) {
        this.hdztPhaseId = hdztPhaseId;
    }

    public Integer getHdztRank() {
        return hdztRank;
    }

    public void setHdztRank(Integer hdztRank) {
        this.hdztRank = hdztRank;
    }

    public Date getGenDataTime() {
        return genDataTime;
    }

    public void setGenDataTime(Date genDataTime) {
        this.genDataTime = genDataTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExtData() {
        return extData;
    }

    public void setExtData(String extData) {
        this.extData = extData;
    }

    public String getContributor() {
        return contributor;
    }

    public void setContributor(String contributor) {
        this.contributor = contributor;
    }
}
