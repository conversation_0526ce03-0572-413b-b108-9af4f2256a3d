package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.OnlineChannelService;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.element.redis.AnchorHourPkComponent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-11-25 11:40
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties", "classpath:env/local/application-inner.properties"})
public class AnchorHourPkComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "2");
    }

    final long ACT_ID = 2024112001;


    @Autowired
    private AnchorHourPkComponent anchorHourPkComponent;

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Test
    public void initTest() {
        var attr = anchorHourPkComponent.getUniqueComponentAttr(ACT_ID);
        anchorHourPkComponent.initPkData(attr, DateUtil.getDate("2024-12-25 18:10:04"));
    }

    @Test
    public void settleTest() {
        var attr = anchorHourPkComponent.getUniqueComponentAttr(ACT_ID);
//        anchorHourPkComponent.settlePkData(attr, DateUtil.getDate("2024-12-11 17:10:00"));
//        anchorHourPkComponent.settlePkData(attr, DateUtil.getDate("2024-12-11 18:00:01"));
        anchorHourPkComponent.settlePkData(attr, DateUtil.getDate("2024-12-11 18:00:03"));
    }

    @Test
    public void queryPkResultTest() {
        anchorHourPkComponent.queryPkInfo(ACT_ID, 810, "2024121117", 1454054224L, 1454054224L);
    }

    @Test
    public void queryPackageInfo(){

        List<OnlineChannelInfo> onlineChannelInfos = onlineChannelService.queryOnlineChannelInfoNoCache(com.yy.thrift.broadcast.Template.SkillCard);
        anchorHourPkComponent.queryPackageInfo(20259L,92221L);
    }

}
