package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.mq.ChannelFightEndEvent;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.element.redis.ChanelFightWallComponent;
import com.yy.gameecology.hdzj.element.component.attr.ChanelFightWallComponentAttr;
import com.yy.thrift.hdztranking.BusiId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Random;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023/5/24 10:34
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-1.properties"
})
public class ChanelFightWallComponentTest {
    @Autowired
    private ChanelFightWallComponent component;

    static {
        System.setProperty("group", "1");
    }

    @Test
    public void onChannelFightEndEventTest() {
        long actId = 2023061001;
        int index = BusiId.MAKE_FRIEND.getValue();
        Random random = new Random();
        int base = 1_000_000_000;
        int seed = 10000;

        ChanelFightWallComponentAttr attr = component.getComponentAttr(actId, index);

        String json = "{\"charm\":20778,\"compereUid\":2908899211,\"endTime\":1684895565,\"extraCharm\":12488,\"extraRevenue\":113500,\"extraSid\":1457673797,\"extraSsid\":2813276625,\"extraUid\":67833005,\"gameId\":\"5180550:2810030400:1457673797:2813276625:1684894955\",\"receiveTimestamp\":1684895567091,\"revenue\":143100,\"serialNo\":1684894955,\"sid\":5180550,\"ssid\":2810030400,\"startTime\":1684894955,\"timestamp\":1684895567,\"winning\":1}";
        ChannelFightEndEvent event = JSON.parseObject(json, ChannelFightEndEvent.class);
        for (int i = 0; i < 20; i++) {
            event.setGameId(UUID.randomUUID().toString());
            event.setSerialNo(System.currentTimeMillis() / 1000);
            event.setRevenue(random.nextInt(seed) + base);
            event.setCompereUid(event.getCompereUid() + i);
            event.setExtraUid(event.getExtraUid() + i);

            component.onChannelFightEndEvent(event, attr);
            try {
                Thread.sleep(1000);
            } catch (Exception ex) {
            }
        }
    }

    @Test
    public void commonOperatePbRequestTest() {
        CommonPBOperateRequest request = new CommonPBOperateRequest();
        request.setActId(2023061001);
        request.setCmptId(5065);
        request.setCmptIndex(BusiId.MAKE_FRIEND.getValue());
        request.setOpTarget("GetData");

        component.commonOperatePbRequest(request);
    }

}
