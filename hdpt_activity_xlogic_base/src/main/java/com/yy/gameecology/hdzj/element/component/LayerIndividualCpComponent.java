package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.PhaseInfo;
import com.yy.gameecology.activity.bean.hdzt.*;
import com.yy.gameecology.activity.bean.mq.LayerRequestEvent;
import com.yy.gameecology.activity.bean.uniqcp.UniqCpRankItem;
import com.yy.gameecology.activity.service.HdztRankService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.LayerViewStatus;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AnchorTeamComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.LayerIndividualCpComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.protocol.pb.layer.LayerInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-24 10:57
 **/
@RequestMapping("/5151")
@Component
@RestController
public class LayerIndividualCpComponent extends BaseActComponent<LayerIndividualCpComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private HdztRankService hdztRankService;

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private UserInfoService userInfoService;

    private final long AUTO_USE_TIME_SEQ = 0;


    //用于补偿更新挂件，防止如果用户1分钟内[定时更新频率]，从前99名[挂件显示名次]，被人挤到了到了300名[定时更新Top N]后，挂件名次更新不到
    private final String LAST_REFRESH_USER_CACHE = "last_refresh_layer_cache";

    @Override
    public Long getComponentId() {
        return ComponentId.LAYER_INDIVIDUAL_CP;
    }


    /**
     * 交友挂件状态查询
     */
    @Report
    @RequestMapping(value = "/queryLayerStatus")
    public Response<Map<String, Object>> queryLayerStatus(String seq, Long actId, Long sid, Long ssid) {
        log.info("queryLayerStatus,actId:{},sid:{},ssid:{},seq:{}", actId, sid, ssid, seq);
        Clock clock = new Clock();
        try {
            if (actId == null || sid == null || ssid == null) {
                log.warn("para error,actid:{},sid:{},ssid:{}", actId, sid, ssid);
                return new Response<>(Code.E_DATA_ERROR.getCode(), "para error", null);
            }
            Map<String, Object> result = queryLayerStatus(actId, sid, ssid);
            log.info("queryLayerStatus,actId:{},sid:{},ssid:{},seq:{},result:{}", actId, sid, ssid, seq, JSON.toJSONString(result));
            return new Response<>(Code.OK.getCode(), "ok", result);
        } catch (Exception e) {
            log.error("buildLayerInfo exception,actId:{},seq:{},sid:{},ssid:{},clock:{},err:{} {}", actId, seq, sid, ssid, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }

    private Map<String, Object> queryLayerStatus(Long actId, Long sid, Long ssid) {
        Map<String, Object> result = Maps.newHashMap();
        int showStatus = actInfoService.inActShowTime(actId) ? 1 : 0;
        result.put("showStatus", showStatus);
        result.put("time", System.currentTimeMillis());
        result.put("grey", commonService.isGrey(actId));
        return result;
    }


    /**
     * 测试辅助挂件查询http接口
     */
    @RequestMapping(value = "/queryLayerInfo")
    public LayerBroadcastInfo queryLayerInfo(String seq, Long actId, Long sid, Long ssid, Long uid, Long cmptIndex) {
        var attr = getComponentAttr(actId, cmptIndex);
        return getLayerBroadcastInfo(AUTO_USE_TIME_SEQ, attr, 0, 0, uid);
    }

    /**
     * 活动开始，全服广播，显示出挂件来
     */
    @HdzjEventHandler(value = ActivityTimeStart.class, canRetry = false)
    public void onActivityTimeStart(ActivityTimeStart event, LayerIndividualCpComponentAttr attr) {
        log.info("onActivityTimeStart,actId:{}", event.getActId());
        GameecologyActivity.GameEcologyMsg msg = buildLayerPbInfo(AUTO_USE_TIME_SEQ, attr, 0, 0, 0);
        svcSDKService.broadcastTemplate(attr.getActId(), com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), msg);
    }

    /**
     * 小时换榜，重置挂件状态
     */
    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = false)
    public void onRankingTimeEnd(RankingTimeEnd event, LayerIndividualCpComponentAttr attr) {
        log.info("onRankingTimeEnd,actId:{},rankId:{},endTime:{}", event.getActId(), event.getRankId(), event.getEndTime());
        if (event.getRankId() != attr.getRankId()) {
            return;
        }
        //以当前小时开始为seq，防止覆盖已经送礼有数据的挂件显示
        long seq = DateUtil.getHourBeginTime(new Date()).getTime();
        GameecologyActivity.GameEcologyMsg msg = buildLayerPbInfo(seq, attr, 0, 0, 0);
        svcSDKService.broadcastTemplate(attr.getActId(), com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), msg);
    }

    /**
     * 活动结束 挂件切成结束状态
     */
    @HdzjEventHandler(value = ActivityTimeEnd.class, canRetry = false)
    public void onActivityTimeEnd(ActivityTimeEnd event, LayerIndividualCpComponentAttr attr) {
        log.info("onActivityTimeEnd,actId:{}", event.getActId());
        GameecologyActivity.GameEcologyMsg msg = buildLayerPbInfo(AUTO_USE_TIME_SEQ, attr, 0, 0, 0);
        svcSDKService.broadcastTemplate(attr.getActId(), com.yy.thrift.broadcast.Template.findByValue(attr.getBroTemplate()), msg);
    }

    /**
     * 定时补偿更新挂件用户名次
     */
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void refreshMinLayerTimer() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            var attr = getUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("refreshLayerTimer:" + actId, 60, () -> {
                //更新TOP N
                log.info("begin refreshLayerTimer,actId:{}", actId);
                Set<Long> currentRefreshUid = refreshLayer(AUTO_USE_TIME_SEQ, attr, attr.getMinRefreshMemberCount());

                //补偿更新被人挤到TOP N外的
                Set<Long> lastRefreshUid = getLastRefreshUid(attr);
                Set<Long> miss = lastRefreshUid.stream().filter(p -> !currentRefreshUid.contains(p)).collect(Collectors.toSet());
                log.info("refreshLayerTimer actId:{}, miss:{}", actId, miss.size());
                unicastLayerInfo(AUTO_USE_TIME_SEQ, attr, miss);

                setLastRefreshUid(attr, currentRefreshUid);

            });
        }
    }

    /**
     * 定时补偿更新挂件用户名次
     */
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0/3 * * * * ? ")
    public void highFrequencyRefreshLayerTimer() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            var attr = getUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("refreshLayerTimer:" + actId, 60, () -> {
                log.info("begin highFrequencyRefreshLayerTimer,actId:{}", actId);
                refreshLayer(AUTO_USE_TIME_SEQ, attr, attr.getHighFrequencyRefreshMemberCount());
            });
        }
    }

    /**
     * 榜单变化，更新用户挂件
     */
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, LayerIndividualCpComponentAttr attr) {
        if (event.getRankId() != attr.getRankId()) {
            return;
        }
        CpUid cpUid = Const.splitCpMember(event.getMember());
        unicastLayerInfo(0, attr, cpUid.getUserUid());
        unicastLayerInfo(0, attr, cpUid.getAnchorUid());
    }


    /**
     * 更新榜单前N名，以及刷新上一轮的前N名
     */
    private Set<Long> refreshLayer(long sequence, LayerIndividualCpComponentAttr attr, long topN) {
        String dateStr = StringUtil.EMPTY;
        if (StringUtil.isNotBlank(attr.getDateFormat())) {
            dateStr = DateUtil.format(commonService.getNow(attr.getActId()), attr.getDateFormat());
        }
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), dateStr, topN, Maps.newHashMap());
        Set<Long> uids = ranks.stream()
                .map(Rank::getMember)
                .flatMap(member -> Arrays.stream(Const.splitCpMemberSimple(member)))
                .map(Long::parseLong)
                .collect(Collectors.toSet());

        unicastLayerInfo(sequence, attr, uids);

        return uids;
    }


    private Set<Long> getLastRefreshUid(LayerIndividualCpComponentAttr attr) {
        String key = makeKey(attr, LAST_REFRESH_USER_CACHE);
        List<String> datas = actRedisDao.lrange(getRedisGroupCode(attr.getActId()), key, 0, attr.getMinRefreshMemberCount());
        if (CollectionUtils.isEmpty(datas)) {
            return Sets.newHashSet();
        }
        return datas.stream().map(Convert::toLong).collect(Collectors.toSet());
    }

    private void setLastRefreshUid(LayerIndividualCpComponentAttr attr, Set<Long> uids) {
        String key = makeKey(attr, LAST_REFRESH_USER_CACHE);
        String redisGroup = getRedisGroupCode(attr.getActId());
        //无关紧要的辅助数据，丢了也影响极小，redis可分多次操作
        actRedisDao.del(redisGroup, key);
        if (CollectionUtils.isNotEmpty(uids)) {
            String[] datas = uids.stream().map(Convert::toString).toArray(String[]::new);
            actRedisDao.rPush(redisGroup, key, datas);
            actRedisDao.expire(redisGroup, key, DateUtil.THREE_MIN_SECONDS);
        }
    }


    @HdzjEventHandler(value = LayerRequestEvent.class, canRetry = false)
    public void onLayerRequestEvent(LayerRequestEvent event, LayerIndividualCpComponentAttr attr) {
        unicastLayerInfo(0, attr, event.getUid());
    }

    private void unicastLayerInfo(long sequence, LayerIndividualCpComponentAttr attr, Set<Long> uids) {
        for (Long uid : uids) {
            unicastLayerInfo(sequence, attr, uid);
        }
    }

    public void unicastLayerInfo(long sequence, LayerIndividualCpComponentAttr attr, long userUid) {
        GameecologyActivity.GameEcologyMsg msg = buildLayerPbInfo(sequence, attr, 0, 0, userUid);
        svcSDKService.unicastUid(userUid, msg);
    }

    public GameecologyActivity.GameEcologyMsg buildLayerPbInfo(long sequence, LayerIndividualCpComponentAttr attr, long sid, long ssid, long uid) {
        LayerBroadcastInfo layerBroadcastInfo = getLayerBroadcastInfo(sequence, attr, 0, 0, uid);
        LayerInfo.LayerBroadcast pbInfo = layerBroadcastInfo.toBroadcastInfoPb();

        return GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.LayerBroadcast_VALUE)
                .setLayerBroadcast(pbInfo)
                .build();
    }

    public LayerBroadcastInfo getLayerBroadcastInfo(long sequence, LayerIndividualCpComponentAttr attr, long sid, long ssid, long uid) {
        long actId = attr.getActId();
        Date now = commonService.getNow(actId);

        String member = Convert.toString(uid);
        var actInfo = actInfoService.queryActivityInfo(actId);

        LayerBroadcastInfo layerInfo = buildLayerInfo(actId, now, actInfo);
        if (sequence > 0) {
            layerInfo.setSequence(sequence);
        }
        LayerMemberItem layerMemberItem = new LayerMemberItem();
        layerMemberItem.setItemType(attr.getItemType());
        layerMemberItem.setRank(-1);
        CpUid cpUid = null;
        cpUid = fillUserMemberItemInfo(attr, uid, now, actId, member, layerMemberItem, cpUid);

        //倒计时
        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, attr.getRankId());
        PhaseInfo curPhaseInfo = hdztRankingThriftClient.queryRankingPhaseInfo(actId, attr.getPhaseId());
        layerMemberItem.setCurPhaseInfo(curPhaseInfo);
        long leftSeconds = actLayerInfoService.getPhaseLeftSeconds(rankingInfo, curPhaseInfo, now);
        layerMemberItem.setLeftSeconds(leftSeconds);

        layerMemberItem.setCurRankNameShow(rankingInfo.getRankingNameShow());

        //常规状态
        layerMemberItem.setViewStatus(calViewState(now, actInfo, cpUid));

        layerInfo.setExtMemberItem(Lists.newArrayList(layerMemberItem));
        return layerInfo;
    }

    private CpUid fillUserMemberItemInfo(LayerIndividualCpComponentAttr attr, long uid, Date now, long actId, String member, LayerMemberItem layerMemberItem, CpUid cpUid) {
        if (uid > 0) {
            List<Long> uids = Lists.newArrayList(uid);

            //分数排名
            String dateStr = StringUtil.EMPTY;
            if (StringUtil.isNotBlank(attr.getDateFormat())) {
                dateStr = DateUtil.format(now, attr.getDateFormat());
            }
            UniqCpRankItem uniqCpRankItem = hdztRankService.queryUniqCpTopActorInfo("", actId, attr.getRankId(), attr.getPhaseId(), dateStr, member);
            if (uniqCpRankItem != null) {
                layerMemberItem.setRank(uniqCpRankItem.getRank());
                layerMemberItem.setScore(uniqCpRankItem.getScore());
                if (StringUtil.isNotBlank(uniqCpRankItem.getMember())) {
                    cpUid = Const.splitCpMember(uniqCpRankItem.getMember());
                    uids.add(cpUid.getAnchorUid());
                    uids.add(cpUid.getUserUid());
                }
            }

            //用户头像信息
            Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(uids, Template.unknown);
            Map<String, Object> extInfo = Maps.newHashMap();
            setMemberInfo(cpUid, userInfoVoMap, extInfo);
            layerMemberItem.setExt(extInfo);
        }
        return cpUid;
    }

    private static LayerBroadcastInfo buildLayerInfo(long actId, Date now, ActivityInfoVo actInfo) {
        LayerBroadcastInfo layerInfo = new LayerBroadcastInfo();
        layerInfo.setActId(actId);
        layerInfo.setCurrentTime(now.getTime());
        layerInfo.setActEndTime(actInfo.getEndTime());
        layerInfo.setActEndShowTime(actInfo.getEndTimeShow());
        layerInfo.setActBeginTime(actInfo.getBeginTime());
        layerInfo.setActBeginShowTime(actInfo.getBeginTimeShow());
        layerInfo.setActBusiId(actInfo.getBusiId());
        layerInfo.setSequence(System.currentTimeMillis());

        return layerInfo;
    }

    private static void setMemberInfo(CpUid cpUid, Map<Long, UserInfoVo> userInfoVoMap, Map<String, Object> extInfoOutPut) {
        if (cpUid != null) {
            UserInfoVo user = userInfoVoMap.get(cpUid.getUserUid());
            if (user != null) {
                extInfoOutPut.put("userLogo", user.getAvatarUrl());
            }
            UserInfoVo anchor = userInfoVoMap.get(cpUid.getAnchorUid());
            if (anchor != null) {
                extInfoOutPut.put("anchorLogo", anchor.getAvatarUrl());
            }

        }
    }

    private int calViewState(Date now, ActivityInfoVo actInfoVo, CpUid cpUid) {
        if (actInfoService.inActEndShowTime(now, actInfoVo)) {
            return LayerViewStatus.ACT_END_107;
        }
        if (cpUid == null) {
            return LayerViewStatus.NOT_IN_104;
        }
        return LayerViewStatus.NORMAL_100;
    }
}
