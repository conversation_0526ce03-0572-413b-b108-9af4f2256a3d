package com.yy.gameecology.common.db.model.gameecology.aov;

import java.util.Date;

public class AovGame {
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private Long phaseId;

    /**
     * 
     */
    private Long roundId;

    /**
     * 
     */
    private Integer roundNum;

    /**
     * 
     */
    private String roundName;

    /**
     * 
     */
    private Integer advanceNodeIndex;

    /**
     * 战队1队伍ID
     */
    private Long camp1TeamId;

    /**
     * 战队2队伍ID
     */
    private Long camp2TeamId;

    /**
     * 总共的bo数
     */
    private Integer bo;

    /**
     * 比赛模式：0-5V5,1-1V1,3-3V3
     */
    private Integer battleMode;

    /**
     * 当前所属的bo数
     */
    private Integer curBo;

    /**
     * 
     */
    private String childId;

    /**
     * 
     */
    private Integer state;

    /**
     * 是否结算到node
     */
    private Integer settleState;

    /**
     * 开始时间，必不为空
     */
    private Date startTime;

    /**
     * 双方都准备完成的时间
     */
    private Date readyTime;

    /**
     * 变为完结状态的时间
     */
    private Date endTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public Long getRoundId() {
        return roundId;
    }

    public void setRoundId(Long roundId) {
        this.roundId = roundId;
    }

    public Integer getRoundNum() {
        return roundNum;
    }

    public void setRoundNum(Integer roundNum) {
        this.roundNum = roundNum;
    }

    public String getRoundName() {
        return roundName;
    }

    public void setRoundName(String roundName) {
        this.roundName = roundName == null ? null : roundName.trim();
    }

    public Integer getAdvanceNodeIndex() {
        return advanceNodeIndex;
    }

    public void setAdvanceNodeIndex(Integer advanceNodeIndex) {
        this.advanceNodeIndex = advanceNodeIndex;
    }

    public Long getCamp1TeamId() {
        return camp1TeamId;
    }

    public void setCamp1TeamId(Long camp1TeamId) {
        this.camp1TeamId = camp1TeamId;
    }

    public Long getCamp2TeamId() {
        return camp2TeamId;
    }

    public void setCamp2TeamId(Long camp2TeamId) {
        this.camp2TeamId = camp2TeamId;
    }

    public Integer getBo() {
        return bo;
    }

    public void setBo(Integer bo) {
        this.bo = bo;
    }

    public Integer getBattleMode() {
        return battleMode;
    }

    public void setBattleMode(Integer battleMode) {
        this.battleMode = battleMode;
    }

    public Integer getCurBo() {
        return curBo;
    }

    public void setCurBo(Integer curBo) {
        this.curBo = curBo;
    }

    public String getChildId() {
        return childId;
    }

    public void setChildId(String childId) {
        this.childId = childId == null ? null : childId.trim();
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getSettleState() {
        return settleState;
    }

    public void setSettleState(Integer settleState) {
        this.settleState = settleState;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getReadyTime() {
        return readyTime;
    }

    public void setReadyTime(Date readyTime) {
        this.readyTime = readyTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}