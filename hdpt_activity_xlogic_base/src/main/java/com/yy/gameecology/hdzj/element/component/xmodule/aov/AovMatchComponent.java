package com.yy.gameecology.hdzj.element.component.xmodule.aov;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.service.aov.match.AovMatchService;
import com.yy.gameecology.activity.service.aov.match.AovMatchSettleService;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.bean.aov.AovMatchInfo;
import com.yy.gameecology.hdzj.bean.aov.AovMatchNodeInfo;
import com.yy.gameecology.hdzj.bean.aov.AovRoundInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AovMatchComponentAttr;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("5117")
public class AovMatchComponent extends BaseActComponent<AovMatchComponentAttr> {

    @Autowired
    private AovMatchService aovMatchService;

    @Autowired
    private AovMatchSettleService aovMatchSettleService;

    @Override
    public Long getComponentId() {
        return ComponentId.AOV_MATCH;
    }

    /**
     * 报名结束后，根据报名完成的队伍数，初始化matchNode
     */
    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 3000, fixedDelay = 120000)
    public void initMatchNodes() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovMatchComponentAttr attr = tryGetUniqueComponentAttr(actId);
            aovMatchSettleService.initMatchNodes(attr, commonService.getNow(actId));
        }
    }

    /**
     * 结束调整状态
     */
    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 2000, fixedDelay = 120000)
    public void closeAdjustingNodes() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovMatchComponentAttr attr = tryGetUniqueComponentAttr(actId);
            aovMatchSettleService.closeAdjustingNodes(attr, commonService.getNow(actId));
        }
    }

    /**
     * 创建roundState = creatable 的round里的比赛
     */
    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 2000, fixedDelay = 6000)
    public void createRoundGames() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovMatchComponentAttr attr = tryGetUniqueComponentAttr(actId);
            aovMatchSettleService.createRoundGames(attr, commonService.getNow(actId));
        }
    }

    /**
     * 对完成了比赛创建（roundState == created）的round 尝试拉取其下所有node进行晋级结算
     */
    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(initialDelay = 2000, fixedDelay = 6000)
    public void settleRound() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                continue;
            }

            AovMatchComponentAttr attr = tryGetUniqueComponentAttr(actId);
            aovMatchSettleService.settleRounds(attr, commonService.getNow(actId));
        }
    }

    /**
     * 查询节点树
     * @param actId
     * @param cmptInx
     * @param phaseId
     * @return
     */
    @GetMapping("matchNodes")
    public Response<List<AovMatchNodeInfo>> queryMatchNodes(@RequestParam(name = "actId") int actId,
                                                            @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                            @RequestParam(name = "phaseId") long phaseId,
                                                            @RequestParam(name = "teamId", required = false, defaultValue = "0") long teamId) {

        AovMatchComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        return aovMatchService.queryMatchNodes(attr, phaseId, teamId, commonService.getNow(actId));
    }

    @GetMapping("rounds")
    public Response<List<AovRoundInfo>> queryRoundInfos(@RequestParam(name = "actId") int actId,
                                                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                                @RequestParam(name = "phaseId") long phaseId,
                                                                @RequestParam(name = "teamId", required = false, defaultValue = "0") long teamId) {
        AovMatchComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        return aovMatchService.queryRoundInfos(attr, phaseId, teamId, commonService.getNow(actId));
    }

    /**
     * 获取对阵列表
     */
    @GetMapping("queryAovMatchList")
    public Response<List<AovMatchInfo>> queryAovMatchList(@RequestParam(name = "actId") int actId,
                                                          @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                          @RequestParam(name = "phaseId") long phaseId) {

        AovMatchComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        return Response.success(aovMatchService.queryAovMatchList(attr, actId, phaseId));
    }
}
