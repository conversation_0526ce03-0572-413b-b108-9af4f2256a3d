package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardBean;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PhaseTopnSlipAwardComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022.10.28 17:20
 * 阶段top n主播或用户瓜分奖池
 * 取topN 主播 按分值比例
 */
@UseRedisStore
@Component
public class PhaseTopnSlipAwardComponent extends BaseActComponent<PhaseTopnSlipAwardComponentAttr> implements ComponentRankingExtHandle<PhaseTopnSlipAwardComponentAttr> {

    public static final String PHASE_TOP_N_AWARD_RECORD = "top_n_award_record:%s:%s:%s";

    @Override
    public Long getComponentId() {
        return ComponentId.TOP_N_SPILT_AWARD;
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEnd(PhaseTimeEnd event, PhaseTopnSlipAwardComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();

        if (rankId != attr.getRankId() || phaseId != attr.getPhaseId()) {
            return;
        }

        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));
        long actId = attr.getActId();
        List<Rank> listAnchor = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, attr.getTopN(), Maps.newHashMap());
        long totalScore = 0;
        for (Rank rank : listAnchor) {
            totalScore += rank.getScore();
        }
        List<AwardBean> awardBeanList = new ArrayList<>();
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        List<String> hashKey = new ArrayList<>();
        List<String> hashField = new ArrayList<>();
        List<Long> hashValue = new ArrayList<>();
        String key = makeKey(attr, String.format(PHASE_TOP_N_AWARD_RECORD, rankId, phaseId, dateStr));
        for (Rank rank : listAnchor) {
            int awardNum = Convert.toInt(rank.getScore() * attr.getTotalAward() / totalScore);
            Map<Long, Integer> packageIdNumMap = ImmutableMap.of(attr.getPackageId(), awardNum);
            Map<Long, Map<Long, Integer>> taskPackageIds = ImmutableMap.of(attr.getTaskId(), packageIdNumMap);
            String myseq = java.util.UUID.randomUUID() + "#topNAward" + (awardBeanList.size() + 1);
            AwardBean awardBean = new AwardBean(myseq, attr.getBusiId(), rank.getMember(), time, taskPackageIds);
            awardBeanList.add(awardBean);
            hashKey.add(key);
            hashField.add(rank.getMember());
            hashValue.add((long) awardNum);
        }
        String eseq = event.getEkey() + "|" + event.getSeq();
        actRedisDao.zBatchIncr(getRedisGroupCode(actId), hashKey, hashField, hashValue);

        // 保存奖励
        this.saveAward(attr, eseq, awardBeanList);
        log.info("phaseTopNAward save award done, list:{}", JSON.toJSONString(awardBeanList));

    }

    /**
     * 发放奖励， 使用 list 的 pop、push， 避免了定时器锁的需求
     */
    @Scheduled(cron = "*/5 * * * * ? ")
    @NeedRecycle(author = "yulianzhu", notRecycle = true)
    public void giveAwards() {
        this.giveAwards(2, 3600, 60 * 24);
    }

    @Override
    public List<Object> handleExt(PhaseTopnSlipAwardComponentAttr attr, GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {
        Long rankId = rankReq.getRankId();
        Long phaseId = rankReq.getPhaseId();
        if (!rankId.equals(attr.getRankId()) || !phaseId.equals(attr.getPhaseId())) {
            return objectList;
        }
        String dateStr = StringUtil.isEmpty(rankReq.getDateStr()) ? "" : rankReq.getDateStr();
        String key = makeKey(attr, String.format(PHASE_TOP_N_AWARD_RECORD, rankId, phaseId, dateStr));
        Set<ZSetOperations.TypedTuple<String>> typedTuples = actRedisDao.zrevRange(getRedisGroupCode(attr.getActId()), key, 0, -1);
        Map<String, Long> awardUid2Score = Maps.newHashMap();
        long totalScore = 0;
        if (typedTuples != null && !typedTuples.isEmpty()) {
            for (ZSetOperations.TypedTuple<String> typedTuple : typedTuples) {
                awardUid2Score.put(typedTuple.getValue(), Convert.toLong(typedTuple.getScore()));
            }
        } else {
            int j = 1;
            for (Object o : objectList) {
                if(j > attr.getTopN()) {
                    break;
                }
                RankValueItemBase item = (RankValueItemBase) o;
                totalScore += item.getValue();
                j++;
            }

            int i = 1;
            for (Object o : objectList) {
                RankValueItemBase item = (RankValueItemBase) o;
                int awardNum = Convert.toInt(item.getValue() * attr.getTotalAward() / totalScore);
                awardUid2Score.put(item.getKey(), (long)awardNum);
                i++;
                if(i > 3) {
                    break;
                }
            }
        }
        for (Object o : objectList) {
            RankValueItemBase item = (RankValueItemBase) o;
            if (item.getViewExt() == null) {
                item.setViewExt(Maps.newHashMap());
            }

            item.getViewExt().put("extAwardNum", (awardUid2Score.getOrDefault(item.getKey(), 0L)) + "");
        }
        return objectList;
    }
}
