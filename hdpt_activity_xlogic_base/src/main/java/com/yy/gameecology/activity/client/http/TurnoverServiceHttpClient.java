
package com.yy.gameecology.activity.client.http;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.activity.bean.turnover.PropsInfo;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.HttpClientHelper;
import com.yy.gameecology.common.utils.MyCharsets;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: CXZ
 * @Desciption: 营收的http请求服务
 * @Date: 2021/3/23 19:47
 * @Modified:
 */
@Service
public class TurnoverServiceHttpClient {

    private static Logger log = LoggerFactory.getLogger(TurnoverServiceHttpClient.class);


    private static HttpClientHelper httpClientHelper = new HttpClientHelper(MyCharsets.UTF_8);
    /**
     * 营收 http访问域名
     */
    @Value("${turnoverService_http_host}")
    private String httpHost;
    /**
     * 查询营收所有礼物的接口
     */
    private String queryAllGiftServer = "/turnover?appId=turnover&sign=39d1d59d22da80b767ad189a596f0270&data=%7B%22version%22%3A0%2C%22appId%22%3A2%2C%22cmd%22%3A1010%2C%22jsonMsg%22%3A%7B%22cmd%22%3A1010%2C%22uid%22%3A%************%22%2C%22appId%22%3A2%7D%7D&_=1581473175075";

    /**
     *  查询银收的礼物map-有缓存，不要直接改实体
     * @return
     */
    @Cached(timeToLiveMillis = 1 * 60 * 60 * 1000)
    public Map<Integer, PropsInfo> queryAllGiftInfoMapCache() {
        return queryAllGiftInfoMap();
    }

    /**
     * 查询银收的礼物map
     * @return
     */
    public Map<Integer, PropsInfo> queryAllGiftInfoMap() {
        List<PropsInfo> propsInfos = queryAllGiftInfoList();
        return propsInfos.stream().collect(Collectors.toMap(PropsInfo::getPropsId, Function.identity()));
    }

    /**
     * 查询银收的礼物列表
     * @return
     */
    public List<PropsInfo> queryAllGiftInfoList() {
        Clock clock = new Clock();
        String url = httpHost + queryAllGiftServer;
        String responseString = "";
        try {
            responseString = httpClientHelper.excuteGet(url);
            if (StringUtils.isBlank(responseString)) {
                log.error("queryAllGiftInfo error,return null@url:{},responseString:{},clock:{}"
                        , url, responseString, clock.tag());
                return null;
            }
            //解析json
            JSONArray giftList = Optional.ofNullable(responseString)
                    .map(JSON::parseObject)
                    .map(json -> json.getString("jsonMsg"))
                    .map(JSON::parseObject)
                    .map(json -> json.getJSONArray("propsList")).orElse(new JSONArray());

            List<PropsInfo> propsInfos = JSONObject.parseArray(giftList.toJSONString(), PropsInfo.class);
            log.info("queryAllGiftInfo done@url:{}, pros size:{},clock:{}", url, propsInfos.size(), clock.tag());
            return propsInfos;
        } catch (Exception e) {
            log.error("queryAllGiftInfo error@url:{},responseString:{},clock:{}"
                    , url, responseString, clock.tag(), e);
            return null;
        }
    }

}
