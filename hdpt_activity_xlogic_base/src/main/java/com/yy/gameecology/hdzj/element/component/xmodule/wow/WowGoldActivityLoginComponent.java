package com.yy.gameecology.hdzj.element.component.xmodule.wow;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.YoPopupMessage;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.mq.ZhuiwanLoginEvent;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiyaLoginClient;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.NewUserStatus;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.LimitControlComponent;
import com.yy.gameecology.hdzj.element.component.WhitelistComponent;
import com.yy.gameecology.hdzj.element.component.attr.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.DayTaskDao;
import com.yy.gameecology.hdzj.element.component.dao.PushSatateDao;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.protocol.pb.zhuiwan.common.ZhuiyaPbCommon;
import com.yy.protocol.pb.zhuiwan.login.LoginRecord;
import com.yy.thrift.hdztranking.BusiId;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/5128")
@Component
public class WowGoldActivityLoginComponent extends BaseActComponent<WowGoldActivityLoginComponentAttr> {

    @Autowired
    private DayTaskDao dayTaskDao;

    @Override
    public Long getComponentId() {
        return ComponentId.WOW_ACTIVITY_LOGIN;
    }

    @Autowired
    private WhitelistComponent whitelistComponent;

    @Autowired
    private LimitControlComponent limitControlComponent;

    @Autowired
    private WowTaskComponent wowTaskComponent;

    @Autowired
    private WowGoldActivityCcontrolComponent wowGoldActivityCcontrolComponent;

    @Autowired
    private ZhuiyaLoginClient loginClient;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private PushSatateDao pushSatateDao;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;


    // 老用户 奖励类型
    private static final int REWARD_TYPE_OLD = 1;
    // 新用户 奖励类型
    private static final int REWARD_TYPE_NEW = 2;

    /**
     * 是否抽过奖的key
     */
    private static final String LOTTERY_KEY = "wow_lottery";
    /**
     * 是否抽过奖的key
     */
    private static final String APP_WEB_TYPE = "yypc";

    /**
     * 发奖key
     */
    private static final String REWARD_KEY = "wow_login_reward_";

    /**
     * 发奖key
     */
    private static final String GET_REWARD_KEY = "wow_reward_finish";

    private static final String LIMIT_REDUCE_TIP = "wow_login_reward_reduce";

    private static final String SPEAK_APP_POP = "SPEAK_POP";
    private static final String WOW_GOLD_COIN = "WOW_GOLD_COIN";

    private static final String GET_REWARD_POP = "REWARD_POP";

    private static final int MAX_TASK_DAY = 15;

    /*
     * 查询用户类型 是否抽奖 YYHeader-Platform 1：Android，2：iOS ，0-未知, 1-安卓，2-ios，3-pc，4-h5，5-web
     * */
    @GetMapping("/getUserState")
    public Response<?> getUserState(@RequestParam("actId") int actId,
                                    @RequestParam("cmptIndex") long cmptIndex) {
        WowGoldActivityLoginComponentAttr attr = getComponentAttr(actId, cmptIndex);

        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();

        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }

        var actInfo = actInfoService.queryActivityInfo(attr.getActId());

        long now = commonService.getNow(attr.getActId()).getTime();
        log.info("inAddWhiteListEndTime uid:{},now:{},endTime:{}", uid, now, actInfo.getEndTime());

        LoginResp resp = getActivityUserType(attr, uid);
        log.info("inAddWhiteListEndTime uid:{},now:{},app:{},hdid:{}", uid, now,
                actInfo.getEndTime(), resp);

//        if (!actInfoService.inActTime(now, actInfo)) {
//             Response.fail(-1, "非活动时间",resp);
//        }

        return Response.success(resp);
    }

    @GetMapping("/lottery")
    public Response<?> lottery(@RequestParam("actId") int actId,
                               @RequestParam("cmptIndex") long cmptIndex,
                               @RequestHeader(name = "x-fts-host-name", required = false) String app) {
        WowGoldActivityLoginComponentAttr attr = getComponentAttr(actId, cmptIndex);

        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();

        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }

        var actInfo = actInfoService.queryActivityInfo(attr.getActId());

        long now = commonService.getNow(attr.getActId()).getTime();
        log.info("Lottery uid:{},now:{}, endTime:{}", uid, now, actInfo.getEndTime());
        if (!actInfoService.inActTime(now, actInfo)) {
            return Response.fail(-1, "非活动时间");
        }

        if (isOverPoolLimit(attr)) {
            log.info("Lottery limit over uid:{},now:{}, endTime:{}", uid, now, actInfo.getEndTime());
            return Response.fail(-1, "活动已结束");
        }

        String member = Convert.toString(uid);
        boolean isInWhitelist = getWhiteListState(attr.getActId(), attr.getWhitelistCmptIndex(), member);
        if (!isInWhitelist) {
            log.info("not white uid {}", uid);
            return Response.fail(-1, "活动仅对魔兽世界用户且Yo语音新人开放\n您暂不符合活动参与条件");
        }

        if (StringUtils.isBlank(app)) {
            app = "unknow";
        }
        String ret = getLotteryState(attr, member);
        if (ret == null) {
            commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), LOTTERY_KEY, member, app);
            log.info("Lottery uid:{},actInfo:{} app:{}", uid, actInfo,app);
        }

        return Response.ok();
    }

    @GetMapping("/getLoginReward")
    public Response<?> getLoginReward(long actId, long cmptIndex, long rewardType,
                                      HttpServletRequest request, HttpServletResponse response, String verifyCode, String recordId, String verifyToken) {
        WowGoldActivityLoginComponentAttr attr = getComponentAttr(actId, cmptIndex);

        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }

        var actInfo = actInfoService.queryActivityInfo(attr.getActId());

        Date nowDate = commonService.getNow(attr.getActId());
        long now = nowDate.getTime();
        log.info("getloginReward uid:{}, rewardType:{}", uid, rewardType);
        if (!actInfoService.inActTime(now, actInfo)) {
            return Response.fail(-1, "非活动时间");
        }

        if (isOverPoolLimit(attr)) {
            log.info("getloginReward limit over uid:{},rewardType:{}", uid, rewardType);
            return Response.fail(-1, "奖池已用完");
        }

        String memnber = Convert.toString(uid);
        int userState = getActivityUserState(attr.getActId(), attr.getUserTypeListCmptIndex(), memnber);

        if (userState == NewUserStatus.NOT_IN_LIST) {
            log.info("getloginReward uid:{},userState:{}, rewardType:{}", uid, userState, rewardType);
            return Response.fail(-1, "无领奖资格");
        }

        if (userState == NewUserStatus.OLD_USER && rewardType != REWARD_TYPE_OLD) {
            log.info("getloginReward uid:{},userState:{}, rewardType:{}", uid, userState, rewardType);
            return Response.fail(-1, "参数错误");
        }

        if (userState == NewUserStatus.NEW_USER && rewardType != REWARD_TYPE_NEW) {
            log.info("getloginReward uid:{},userState:{}, rewardType:{}", uid, userState, rewardType);
            return Response.fail(-1, "参数错误");
        }

        //风控
        if (StringUtil.isNotBlank(attr.getRiskStrategyKey())) {
            try {
                zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);
            } catch (SuperException e) {
                log.warn("login doRiskCheck warn,actId:{},uid:{},e:{},e {}", actId, uid, e.getMessage(), e);
                if (e.getData() != null) {
                    return Response.fail(e.getCode(), e.getMessage(), Map.of("riskRecheck", e.getData()));
                } else {
                    return Response.fail(e.getCode(), e.getMessage());
                }
            } catch (Exception e) {
                log.error("login doRiskCheck error@actId:{},uid:{} {}", actId, uid, e.getMessage(), e);
                return Response.fail(SuperException.E_FAIL, "网络超时");
            }
        }

        // lock
//        String lockKey = String.format("%s_%s%s", attr.getActId(), REWARD_KEY, uid);
//        boolean success = actRedisDao.setNX(getRedisGroupCode(attr.getActId()), lockKey, "1", 10);
//        if (!success) {
//            log.info("getloginReward uid:{},app:{}, rewardType:{}", uid, rewardType);
//            return Response.fail(-1, "系统忙请稍后重试");
//        }

        boolean getReward = getUserLoginRewardState(attr, memnber);
        if (getReward) {
            log.info("getloginReward uid:{} rewardType:{}", uid, rewardType);
//            actRedisDao.del(getRedisGroupCode(attr.getActId()), lockKey);
            return Response.fail(-1, "奖励已领取");
        }

        AwardAttrConfig awardAttrConfig = attr.getRewardConfig().get(rewardType + "");

        log.info("getloginReward uid:{},rewardType:{}, awardAttrConfig:{}", uid, rewardType, awardAttrConfig);

        String limitSeq = makeKey(attr, String.format("%s_%s", LIMIT_REDUCE_TIP, uid));
        if (userState == NewUserStatus.NEW_USER) {
            CommonDataDao.ValueIncResult limitResult = limitControlComponent.valueIncrIgnoreWithLimit(attr.getActId(), attr.getPoolLimitIndex(), attr.getPackageId(), limitSeq, awardAttrConfig.getAwardAmount());
            if (limitResult.isViolateLimit()) {
                log.info("getloginReward limit over uid:{},userState:{},rewardType:{},limitResult:{}", uid, userState, rewardType, limitResult);
//                actRedisDao.del(getRedisGroupCode(attr.getActId()), lockKey);
                return Response.fail(-1, "奖池已用完");
            }
        }

        String member = Convert.toString(uid);
        String awardAnchorSeq = makeKey(attr, REWARD_KEY + uid);
        String time = DateUtil.format(commonService.getNow(attr.getActId()));
        hdztAwardServiceClient.doWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), uid, awardAttrConfig.getTAwardTskId(), awardAttrConfig.getNum(),
                awardAttrConfig.getTAwardPkgId(), awardAnchorSeq, Maps.newHashMap());

        String value = JSONUtils.toJsonString(awardAttrConfig);
        setUserLoginRewardState(attr, member, value);

        addLoginLimitAmount(attr,limitSeq,nowDate,awardAttrConfig.getAwardAmount());

//        actRedisDao.del(getRedisGroupCode(attr.getActId()), lockKey);

        if (userState == NewUserStatus.OLD_USER) {
            String seq = makeKey(attr, GET_REWARD_POP + uid);
            doSendNotice(seq, uid, attr.getOldRewardPopTitle(), attr.getOldRewardPopMessage(), attr.getOldRewardPopLink());
        }

        log.info("Lottery uid:{},actInfo:{} awardAttrConfig:{} userState:{}", uid, actInfo, awardAttrConfig, userState);

        return Response.ok();
    }

    @GetMapping("/getLoginRewardState")
    public Response<?> getLoginRewardState(long actId, long cmptIndex, long rewardType) {
        WowGoldActivityLoginComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "param error");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "您还没有登录，请完成登录后刷新本页面");
        }

        var actInfo = actInfoService.queryActivityInfo(attr.getActId());

        long now = commonService.getNow(attr.getActId()).getTime();
        log.info("getloginReward uid:{}, rewardType:{}", uid, rewardType);
        if (!actInfoService.inActTime(now, actInfo)) {
            return Response.fail(-1, "非活动时间");
        }

//        if (isOverPoolLimit(attr)) {
//            log.info("getloginReward limit over  uid:{},rewardType:{}", uid, rewardType);
//            return Response.fail(-1, "活动结束");
//        }

        String memnber = Convert.toString(uid);
        int userState = getActivityUserState(attr.getActId(), attr.getUserTypeListCmptIndex(), memnber);

        if (userState == NewUserStatus.NOT_IN_LIST) {
            log.info("getloginReward uid:{},app:{}, rewardType:{}", uid, rewardType);
            return Response.fail(-1, "无领奖资格");
        }

        if (userState == NewUserStatus.OLD_USER && rewardType != REWARD_TYPE_OLD) {
            log.info("getloginReward uid:{},app:{}, rewardType:{}", uid, rewardType);
            return Response.fail(-1, "参数错误");
        }
        if (userState == NewUserStatus.NEW_USER && rewardType != REWARD_TYPE_NEW) {
            log.info("getloginReward uid:{},app:{}, rewardType:{}", uid, rewardType);
            return Response.fail(-1, "参数错误");
        }
        boolean getReward = getUserLoginRewardState(attr, memnber);

        log.info("getloginReward uid:{},rewardType:{}, getReward:{}", uid, rewardType, getReward);


        LoginReward ret = new LoginReward();
        ret.setFinish(getReward == true ? 1 : 0);

        return Response.success(ret);

    }

    /**
     * 顶部提醒
     */
    public void doSendNotice(String seq, long uid, String title, String message, String link) {
        Map<String, String> extend = Maps.newHashMapWithExpectedSize(2);
        //客户端不用等待首页弹窗完成也能显示
        extend.put("ignoreMainPopup", "1");

        YoPopupMessage yoMessage = YoPopupMessage.builder()
                .app("yomi")
                //默认android,-1 安卓+ios
                .platform(-1)
                .title(title)
                .content(Base64Utils.encodeToString(message.getBytes()))
                .innerContent(message)
                .icon("https://ihdpt.bs2cdn.yy.com/adminweb/kwf8kzabj85wkteimzfhask7bfbz6w3x.png")
                .extend(extend)
                .link(link).build();
        zhuiWanPrizeIssueServiceClient.sendPopupMessage(seq, uid, yoMessage);
    }

    /**
     * 登陆事件，锁定新老用户
     */
    @HdzjEventHandler(value = ZhuiwanLoginEvent.class, canRetry = true)
    public void onZhuiwanLoginEvent(ZhuiwanLoginEvent event, WowGoldActivityLoginComponentAttr attr) {
        log.info("onZhuiwanLoginEvent event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        if (!event.getApp().equals(attr.getAppName())) {
            return;
        }
        String memberId = Convert.toString(event.getUid());

        if (StringUtil.isEmpty(event.getHdid())) {
            log.info("onZhuiwanLoginEvent hid is null event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
            return;
        }
        //没资格的用户也要占用设备使用
        isDeviceFirstUse(attr, event.getUid(), event.getHdid());

        boolean inPreWhiteList = getWhiteListState(attr.getActId(), attr.getWhitelistCmptIndex(), memberId);
        if (!inPreWhiteList) {
            log.info("onZhuiwanLoginEvent not in white list uid:{}", event.getUid());
            return;
        }

        int userType = getActivityUserState(attr.getActId(), attr.getUserTypeListCmptIndex(), memberId);
        if (userType != NewUserStatus.NOT_IN_LIST) {
            log.info("onZhuiwanLoginEvent already login uid:{}", event.getUid());
            return;
        }

        Date now = commonService.getNow(attr.getActId());
        var actInfo = actInfoService.queryActivityInfo(attr.getActId());
        if (!actInfoService.inActTime(now, actInfo)) {
            log.info("onZhuiwanLoginEvent activity is finish", event.getUid());
            return;
        }

        boolean newUserState = genUserState(attr, actInfo, event.getUid(), event.getHdid());
        log.info("onZhuiwanLoginEvent done,uid:{},hid{},newUserState:{}", event.getUid(), event.getHdid(), newUserState);
    }


    public Boolean isOverPoolLimit(WowGoldActivityLoginComponentAttr attr) {
        long balance = limitControlComponent.queryPoolBalance(attr.getActId(), attr.getPoolLimitIndex(), attr.getPackageId());
        log.info("balance:{},limite:{}", attr.getPoolLimitIndex(), balance);
        return balance <= 0;
    }

    public void addLoginLimitAmount(WowGoldActivityLoginComponentAttr attr,String seq,Date now,long amount) {
        if (amount == 0) {
            return ;
        }
        seq ="day_statics_"+ seq;
        limitControlComponent.valueIncrIgnoreWithLoginLimit(attr.getActId(), attr.getPoolLimitIndex(), attr.getPackageId(), seq, now, amount);
    }

    public boolean getWhiteListState(long actId, long cmpIndex, String member) {
        return whitelistComponent.inWhitelist(actId, cmpIndex, member);
    }

    public int getActivityUserState(long actId, long cmpIndex, String member) {
        String value = whitelistComponent.getConfigValue(actId, cmpIndex, member);
        if (value == null) {
            return NewUserStatus.NOT_IN_LIST;
        }
        return Integer.valueOf(value);
    }

    // 是否抽奖 新用户需要抽过奖才能完成任务
    public String getLotteryState(WowGoldActivityLoginComponentAttr attr, String member) {
        String value = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), LOTTERY_KEY, member);
        log.info("actId {} CmptId {} CmptUseInx {} key {} member{} value:{}", attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), LOTTERY_KEY, member, value);
        return value;
    }

    // 是否抽奖 新用户需要抽过奖才能完成任务
    public long getLotteryCount(WowGoldActivityLoginComponentAttr attr) {
        long count = commonDataDao.hashValueCount(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), LOTTERY_KEY);
        return count;
    }

    // 增加
    public void addLoginRewardState(WowGoldActivityLoginComponentAttr attr, String key, String member, String value) {
        commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), key, member, value);
    }

    public boolean getUserLoginRewardState(WowGoldActivityLoginComponentAttr attr, String member) {
        String value = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), GET_REWARD_KEY, member);
        return (value == null ? false : true);
    }

    public void setUserLoginRewardState(WowGoldActivityLoginComponentAttr attr, String member, String value) {
        commonDataDao.hashValueSet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), GET_REWARD_KEY, member, value);
    }

    public long getUserLoginRewardCount(WowGoldActivityLoginComponentAttr attr) {
        return commonDataDao.hashValueCount(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), GET_REWARD_KEY);
    }

    public LoginResp getActivityUserType(WowGoldActivityLoginComponentAttr attr, long uid) {
        LoginResp resp = new LoginResp(false, false);
        String member = Convert.toString(uid);
        boolean isInWhitelist = getWhiteListState(attr.getActId(), attr.getWhitelistCmptIndex(), member);
        if (!isInWhitelist) {
            log.info("not white uid {}", uid);
            return resp;
        }
        resp.setWhiteUid(true);
        String lotteryState = getLotteryState(attr, member);
        if (lotteryState != null) {
            resp.setLottery(true);
        }
        int userType = getActivityUserState(attr.getActId(), attr.getUserTypeListCmptIndex(), member);
        resp.setUserType(userType);
        return resp;
    }

    /**
     * 获取用户状态，如果不存在的时候，会根据当前给的uid和hdid写入用户状态再返回
     */
    public LoginResp getOrSaveUserNewState(WowGoldActivityLoginComponentAttr attr, ActivityInfoVo actInfo, long uid, String hdId) {
        LoginResp resp = new LoginResp(false, false);
        String member = Convert.toString(uid);
        boolean isInWhitelist = whitelistComponent.inWhitelist(attr.getActId(), attr.getWhitelistCmptIndex(), member);
        if (!isInWhitelist) {
            return resp;
        }
        resp.setWhiteUid(true);
        String lotteryState = getLotteryState(attr, member);
        if (lotteryState != null) {
            resp.setLottery(true);
        }
        int userType = getActivityUserState(attr.getActId(), attr.getUserTypeListCmptIndex(), member);
        if (userType != NewUserStatus.NOT_IN_LIST) {
            resp.setUserType(userType);
            return resp;
        }

        if (StringUtil.isBlank(hdId)) {
            log.warn("hdid not find,uid:{}, hdid:{}", uid, hdId);
            resp.setWhiteUid(false);
            resp.setLottery(false);
            return resp;
        }

        //---写新老用户：登陆后才能判断设备，最终判断新老用户
        boolean newUser = genUserState(attr, actInfo, uid, hdId);
        if (newUser) {
            resp.setUserType(NewUserStatus.NEW_USER);
        } else {
            resp.setUserType(NewUserStatus.OLD_USER);
        }
        return resp;
    }

    public boolean genUserState(WowGoldActivityLoginComponentAttr attr, ActivityInfoVo actInfo, long uid, String hdId) {
        String member = Convert.toString(uid);
        LoginRecord.LoginRecordReq req = LoginRecord.LoginRecordReq.newBuilder().setUid(uid).setHdid(hdId)
                .setApp(ZhuiyaPbCommon.App.APP_YOMI).build();
        LoginRecord.LoginRecordRsp loginRecordRsp = loginClient.queryLoginRecord(req);
        log.info("isNewUser info @req:{},rsp:{}", JsonFormat.printToString(req), JsonFormat.printToString(loginRecordRsp));
        if (loginRecordRsp.getCode() != ZhuiyaPbCommon.RspCode.RSP_CODE_SUCCESS_VALUE) {
            throw new BusinessException(500, loginRecordRsp.getMessage());
        }
        var loginInfo = loginRecordRsp.getResult();
        long uidFirstLoginTime = loginInfo.getUidFirstLoginTime();
        long deviceFirstLoginTime = loginInfo.getDeviceFirstLoginTime();
        if (uidFirstLoginTime == 0) {
            log.warn("getOrSaveUserNewState user login time is zero,uid:{},uidFirstLoginTime:{}", uid, uidFirstLoginTime);
            uidFirstLoginTime = System.currentTimeMillis();
        }
        if (deviceFirstLoginTime == 0) {
            log.warn("getOrSaveUserNewState device login time is zero,uid:{},deviceFirstLoginTime:{}", uid, deviceFirstLoginTime);
            deviceFirstLoginTime = System.currentTimeMillis();
        }
        long firstLoginTime = Math.min(uidFirstLoginTime, deviceFirstLoginTime);
        long timeOffset = System.currentTimeMillis() - DateUtil.ONE_HOUR_MILL_SECONDS;
        // firstLoginTime > timeOffset 是为了活动开始前，测试环境测试方便
        boolean newLogin = firstLoginTime > timeOffset || firstLoginTime > actInfo.getBeginTime();

        //设备是否首次使用
        boolean deviceFirstUse = isDeviceFirstUse(attr, uid, hdId);

        log.info("getOrSaveUserNewState uid:{} deviceFirstUse:{} newLogin:{}", member, deviceFirstUse, newLogin);
        boolean newUser = newLogin && deviceFirstUse;

        String newUserStr = newUser ? "1" : "0";
        whitelistComponent.ignoreAdd(attr.getActId(), attr.getUserTypeListCmptIndex(), member, newUserStr);
        return newUser;
    }

    private boolean isDeviceFirstUse(WowGoldActivityLoginComponentAttr attr, long uid, String hdId) {
//        if (StringUtil.isBlank(hdId)) {
//            return false;
//        }
        //设备是否首次使用
        String value = whitelistComponent.getConfigValue(attr.getActId(), attr.getHidListCmptIndex(), hdId);
        if (value == null) {
            log.info("isDeviceFirstUse new device，actId:{},hidListCmptIndex:{},curUid:{},deviceUid:{}", attr.getActId(), attr.getHidListCmptIndex(), uid, hdId);
            whitelistComponent.ignoreAdd(attr.getActId(), attr.getHidListCmptIndex(), hdId, uid + "");
            return true;
        }
        boolean deviceFirstUse = Convert.toString(uid).equals(value);

        log.info("isDeviceFirstUse device，actId:{},hidListCmptIndex:{},curUid:{},deviceUid:{} deviceFirstUse:{} value:{}", attr.getActId(), attr.getHidListCmptIndex(), uid, hdId, deviceFirstUse,value);

        return deviceFirstUse;
    }

    /*
     * 首页弹窗
     * */
    @GetMapping("/getAppPopupUrl")
    public Response<HomePopupVo> getAppPopupUrl(@RequestParam(name = "actId") long actId,
                                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                @RequestParam(name = "uid") long uid,
                                                @RequestParam(name = "hdid") String hdid) {
        WowGoldActivityLoginComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        log.info("getAppPopupUrl with uid:{} hdid:{}", uid, hdid);
        Date now = commonService.getNow(actId);

        var actInfo = actInfoService.queryActivityInfo(attr.getActId());

        if (!actInfoService.inActTime(now, actInfo)) {
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        if (uid <= 0) {
            log.info("getAppPopupUrl login empty actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        if (StringUtil.isBlank(hdid)) {
            log.info("getAppPopupUrl hdid is null uid:{} hdid:{}", uid, hdid);
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        String member = Convert.toString(uid);

        boolean white = getWhiteListState(actId, attr.getWhitelistCmptIndex(), member);
        if (!white) {
            log.info("getAppPopupUrl not in white list uid:{} hdid:{}", uid, hdid);
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        boolean newUser = false;
        int userState = getActivityUserState(attr.getActId(), attr.getUserTypeListCmptIndex(), member);
        if (userState == NewUserStatus.NOT_IN_LIST) {
            newUser = genUserState(attr, actInfo, uid, hdid);
        } else if (userState == NewUserStatus.NEW_USER) {
            newUser = true;
        }

        if (isOverPoolLimit(attr)) {
            // 奖池耗完按照 活动结束处理
            log.info("getAppPopupUrl limit over with uid:{} hdid:{}", uid, hdid);
            return Response.success(new HomePopupVo(StringUtils.EMPTY));
        }

        String lotteryState = getLotteryState(attr, member);
        if (!StringUtil.isBlank(lotteryState)) {
            log.info("getAppPopupUrl lottery uid:{} hdid:{} lotteryState:{}", uid, hdid, lotteryState);
            if (newUser) {
                log.info("getAppPopupUrl new user uid:{} hdid:{}", uid, hdid);
                return Response.success(new HomePopupVo(attr.getNewUserPop()));
            } else {
                log.info("getAppPopupUrl old user uid:{} hdid:{}", uid, hdid);
                return Response.success(new HomePopupVo(attr.getOldUserPop()));
            }
        }

        return Response.success(new HomePopupVo(StringUtils.EMPTY));
    }

    /*
     * 入口气泡
     * */
    @GetMapping("/appPortalInfo")
    public TopFuncAttachResp queryAppPortalInfo(@RequestParam("actId") int actId,
                                                @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                                @RequestParam(name = "uid") long uid,
                                                @RequestParam(name = "hdid") String hdid) {
        log.info("appPortalInfo actId:{},uid:{},hdid:{}", actId, uid, hdid);
        TopFuncAttachResp resp = new TopFuncAttachResp();
        WowGoldActivityLoginComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            resp.setResult(400);
            resp.setMsg("activity not exist");
            return resp;
        }

        if (uid <= 0) {
            resp.setResult(401);
            resp.setMsg("需要登录态");
            log.info("appPortalInfo login empty actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        TopFuncAttachVo data = new TopFuncAttachVo();
        resp.setData(data);

        Date now = commonService.getNow(actId);
        var actInfo = actInfoService.queryActivityInfo(actId);
        if (!actInfoService.inActTime(now, actInfo)) {
            data.setShow(false);
            log.info("appPortalInfo act time actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        String member = Convert.toString(uid);
        boolean inWhitelist = getWhiteListState(attr.getActId(), attr.getWhitelistCmptIndex(), member);
        if (!inWhitelist) {
            data.setShow(false);
            log.info("appPortalInfo white list empty actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        if (StringUtil.isBlank(hdid)) {
            data.setShow(false);
            log.info("appPortalInfo old user actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        int userState = getActivityUserState(attr.getActId(), attr.getUserTypeListCmptIndex(), member);
        boolean newUser = false;
        if (userState == NewUserStatus.NOT_IN_LIST) {
            newUser = genUserState(attr, actInfo, uid, hdid);
        } else if (userState == NewUserStatus.NEW_USER) {
            newUser = true;
        }

        data.setShow(true);
        if (!newUser) {
            log.info("appPortalInfo old user actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        // 获取tips
        Long state = pushSatateDao.getUserPushState(attr.getActId(), uid);
        if (state != null && state > 0) {
            log.info("close push switch actId:{},uid:{},hdid:{}", actId, uid, hdid);
            return resp;
        }

        if (isOverPoolLimit(attr)) {
            log.info("getloginReward limit over  uid:{}", uid);
            return resp;
        }

        String tips = wowTaskComponent.getNoticeTips(actId, attr.getTaskCmptIndex(), uid);
        if (StringUtils.isNotEmpty(tips)) {
            data.setTips(tips);
            data.setTipsSeq(DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2));
        }

        log.info("appPortalInfo done actId:{},uid:{},hdid:{} resp:{}", actId, uid, hdid, resp);
        return resp;
    }

    @GetMapping("/removeLotteryTool")
    public Response<?> removeLotteryTool(@RequestParam("actId") int actId,
                                         @RequestParam("cmptIndex") long cmptIndex,
                                         @RequestParam("uid") long uid) {
        WowGoldActivityLoginComponentAttr attr = getComponentAttr(actId, cmptIndex);

        if (attr == null) {
            return Response.fail(400, "param error");
        }

        String member = Convert.toString(uid);
        commonDataDao.hashValueDel(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), LOTTERY_KEY, member);

        return Response.ok();
    }


    @NeedRecycle(author = "gaofei", notRecycle = true)
    @Scheduled(cron = "0 5 * * * ?")
    public void reportSyncDate() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            ActivityInfoVo activityInfo = actInfoService.queryActivityInfo(actId);
            if (activityInfo == null) {
                continue;
            }

            Date now = commonService.getNow(actId);

            if (!actInfoService.inActTime(DateUtil.addMinutes(now,-1*60), activityInfo)) {
                continue;
            }

            WowGoldActivityLoginComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }

           timerSupport.work("doStaticReport", 300, () -> doStaticReport(actId,now,attr));
//            doStaticReport(actId,now,attr);
        }
    }

    /**
     * 每日数据报告
     * 1 新用户
     * 0 老用户
     */
    public void doStaticReport(long actId, Date now, WowGoldActivityLoginComponentAttr attr) {
        Date nowDate = DateUtil.addMinutes(now,-1*60);
        String hourCode = DateUtil.format(nowDate, DateUtil.PATTERN_TYPE8);
        log.info("in {}",hourCode);
        long limitId = attr.getPackageId();
        StringBuilder content = new StringBuilder();
        content.append("【魔兽活动小时汇总】\n");
        content.append("时段：").append(hourCode).append("\n");
        content.append("【奖池跟踪】\n");

        // 今日总发放
        long totalAmount = limitControlComponent.getAwardDaySendDay(attr.getActId(), attr.getTaskCmptIndex(), nowDate, limitId);
        // 日发放
        long loginAmount = limitControlComponent.getAwardDayLoginSend(actId, attr.getPoolLimitIndex(), nowDate, limitId);
         // 奖池余额
        long balance = limitControlComponent.queryPoolBalance(attr.getActId(), attr.getPoolLimitIndex(), limitId);


        content.append("今日发放总金额：").append(totalAmount).append("\n");

        content.append("今日登录任务发奖总金额：").append(loginAmount).append("\n");

        content.append("总奖池剩余金额：").append(balance).append("\n");


        content.append("【累计完成情况跟踪】\n");
        long lotteryCount = getLotteryCount(attr);
        content.append("累计抽奖人数：").append(lotteryCount).append("\n");
        long loginCount = getUserLoginRewardCount(attr);
        content.append("累计完成登录任务人数：").append(loginCount).append("\n");

        long addCount = 0;

        Map<Long, Long> taskUsers = new HashMap<>();

        for (int dayIndex = MAX_TASK_DAY; dayIndex >= 1; dayIndex--) {
            long count = dayTaskDao.getTaskFinishCount(attr.getActId(), dayIndex);
            taskUsers.put(Convert.toLong(dayIndex),Convert.toLong(count+addCount));
            addCount += count;
        }

        for (int dayIndex = 1; dayIndex <= MAX_TASK_DAY; dayIndex++) {
            content.append("累计完成第").append(dayIndex).append("天任务人数：").append(taskUsers.get(Convert.toLong(dayIndex))).append("\n");
        }

        content.append("【白名单监控】\n");

        String timeCode = DateUtil.format(nowDate, DateUtil.PATTERN_TYPE5);
        WowGoldActivityCcontrolComponent.Statics s = wowGoldActivityCcontrolComponent.getWhiteStatics(attr.getActId(),attr.getControlEnterIndex(),timeCode);

        content.append("当前白名单总人数：").append(s.getTotalCount()).append("\n");

        content.append("今日新增的白名单人数：").append(s.getDayCount()).append("\n");

        String msg = buildActRuliuMsg(actId, false, "魔兽开黑如流播报", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_PC_USER_GROWTH, msg, Lists.newArrayList());
    }



    @Data
    public static class LoginResp {

        private boolean isWhiteUid;// 是否为白名单用户

        private boolean isLottery; // 是否抽过奖

        private int userType; // 0 旧用户 1 新用户 2 没有判定过

        // 构造函数
        public LoginResp(boolean isWhiteUid, boolean isLottery) {
            this.isWhiteUid = isWhiteUid;
            this.isLottery = isLottery;
            this.userType = NewUserStatus.NOT_IN_LIST;
        }
    }

    @Data
    public static class LoginReward {

        private int finish; // 0 未领取 1 已经领取
    }

    @Data
    public static class HomePopupVo {
        protected String popupLink;

        public HomePopupVo() {
        }

        public HomePopupVo(String popupLink) {
            this.popupLink = popupLink;
        }
    }

    @Data
    public static class TopFuncAttachVo {
        /**
         * 是否需要显示该功能
         */
        private boolean show;
        /**
         * 下方tips展示文案 （值为空则不需要展示）
         */
        private String tips;
        /**
         * 下方 tips 展示控制seq，如果客户端本地该 seq 已经展示过了，那么不需要展示； 如果该值为空，表示无需展示
         */
        private String tipsSeq;
    }

    @Data
    public static class TopFuncAttachResp {

        protected int result = 0;

        protected String msg;

        protected TopFuncAttachVo data;
    }
}
