package com.yy.gameecology.activity.client.yrpc;

import com.googlecode.protobuf.format.JsonFormat;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.protocol.pb.skillcard.RoomNo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/7/13 16:20
 **/
@Component
@Slf4j
public class UgcRoomClient {
    @Reference(protocol = "yrpc", owner = "${skillcard_room_server.s2sname}", registry = {"yrpc-reg"}, lazy = true)
    private UgcRoomService proxy;

    @Reference(protocol = "yrpc", owner = "${skillcard_room_server.s2sname}", registry = {"yrpc-reg"}, lazy = true, retries = 2, cluster = "failover")
    private UgcRoomService readProxy;


    /**
     * 添加ugc白名单
     *
     * @param uid         用户id
     * @param expiredTime 过期时间，秒,可以取活动结束时间
     * @param from        白名单来源,可以是具体的活动id
     **/
    public boolean addUgcWhitelist(long uid, long expiredTime, String from) {
        RoomNo.AddUGCWhitelistReq req = RoomNo.AddUGCWhitelistReq.newBuilder()
                .setUid(uid)
                .setExpireTime(expiredTime)
                .setFrom(from)
                .build();

        try {
            RoomNo.AddUGCWhitelistRsp resp = this.proxy.addUgcWhitelist(req);
            log.info("addUgcWhitelist req={} resp={}", JsonFormat.printToString(req), JsonFormat.printToString(resp));

            return resp.getCode() == Const.ZERO;
        } catch (Exception ex) {
            log.error("addUgcWhitelist error,req={}", JsonFormat.printToString(req), ex);
        }

        return false;
    }

    /**
     * 获取ugc房间信息
     *
     * @param uid   用户id
     * @param actId 活动id，用于记录请求
     **/
    public ChannelInfo obtainUgcRoomInfo(long uid, long actId) {
        RoomNo.ObtainUGCRoomInfoReq req = RoomNo.ObtainUGCRoomInfoReq.newBuilder()
                .setUid(uid)
                .setFrom(actId + "")
                .build();

        try {
            RoomNo.ObtainUGCRoomInfoRsp rsp = this.readProxy.obtainUgcRoomInfo(req);
            log.info("obtainUgcRoomInfo req={},resp={}", JsonFormat.printToString(req), JsonFormat.printToString(rsp));
            if (rsp.getCode() == Const.ZERO) {
                return new ChannelInfo(rsp.getRoomInfo().getSid(), rsp.getRoomInfo().getSsid());
            }
        } catch (Exception ex) {
            log.error("obtainUgcRoomInfo error,req={}", JsonFormat.printToString(req), ex);
        }

        return null;
    }
}
