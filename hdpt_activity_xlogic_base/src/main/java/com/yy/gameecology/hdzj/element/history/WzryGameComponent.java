package com.yy.gameecology.hdzj.element.history;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.mq.ZhuiwanGameNotify;
import com.yy.gameecology.activity.client.thrift.SaiBaoClient;
import com.yy.gameecology.activity.client.thrift.TurnoverCurrencyServiceThriftClient;
import com.yy.gameecology.activity.client.thrift.TurnoverProductServiceClient;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.dao.mysql.WzryDao;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.wzry.WzryGameInitService;
import com.yy.gameecology.activity.service.wzry.WzryGameService;
import com.yy.gameecology.activity.service.wzry.WzryGameSettleService;
import com.yy.gameecology.activity.worker.timer.TimerSupport;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.SimpleResult;
import com.yy.gameecology.common.consts.BattleMode;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeHandlerConst;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.model.gameecology.wzry.WzryGameView;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.handler.annotation.GeHandler;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.wzry.*;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.WzryGameComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.WzryTaskComponentAttr;
import com.yy.gameecology.hdzj.utils.RandomUtil;
import com.yy.thrift.gameecology_bridge.StringRequest;
import com.yy.thrift.gameecology_bridge.StringResponse;
import com.yy.thrift.turnover.TAppId;
import com.yy.thrift.turnover.TReverseConsumeProductRequest;
import org.apache.commons.collections.CollectionUtils;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * desc:王者荣耀赏金赛
 *
 * <AUTHOR>
 * @date 2024-01-11 15:47
 **/

/**
 * 王者荣耀赏金赛
 */
@Component
@RestController
@RequestMapping("/cmpt/wzryGame")
public class WzryGameComponent extends BaseActComponent<WzryGameComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TimerSupport timerSupport;

    @Autowired
    private WzryGameService wzryGameService;

    @Autowired
    private SaiBaoClient saiBaoClient;

    @Autowired
    private WzryDao wzryDao;


    @Autowired
    private WzryGameInitService wzryGameInitService;

    @Autowired
    private WzryGameSettleService wzryGameSettleService;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Autowired
    private WzryTaskComponent wzryTaskComponent;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private TurnoverCurrencyServiceThriftClient turnoverCurrencyServiceThriftClient;

    @Autowired
    private TurnoverProductServiceClient turnoverProductServiceClient;

    @Autowired
    private ThreadPoolManager threadPoolManager;


    /**
     * 赛事列表初始化，按天控制重复执行，去除key
     */
    private static final String CREATE_GAME_DAY_CODE = "create_game_day";


    /**
     * 比赛开始后10分钟内进入赛宝
     */
    private static final int JOIN_WZSB_READY_TIME = 10;

    /**
     * 赛宝人齐后，王者荣耀10分钟倒计时开始
     */
    private static final int JOIN_WZ_GAME_READY_TIME = 10;

    /**
     * 一次取多少条超时未进入赛宝的数据进行取消处理
     */
    private static final int CANCEL_SB_TIME_OUT_BATCH = 50;

    /**
     * 一次取多少条超时未进入王者荣耀的数据进行取消处理
     */
    private static final int CANCEL_WZRY_TIME_OUT_BATCH = 50;

    /**
     * 一次取多少条进行中的游戏做游戏结束结算处理
     */
    private static final int CLOSE_GAME_OVER_LIST_BATCH = 50;

    /**
     * 1V1在报名结束后延迟分配队伍
     * 为避免用户参赛和分配队伍之间有临界问题，刚好踩点报名的用户分配不到队伍，分配队伍需要在报名结束后延后分配
     */
    private static final int ALLOCATION_GAME_TEAM_OFFSET_SECONDS = 10;

    /**
     * 赛事预约记录
     */
    private static final String SUBSCRIBE_RECORD = "subscribe_record:%s";

    private static final String WITH_DRAW_TOTAL_AMOUNT = "with_draw_total_amount";

    private static final String WITH_DRAW_UID_AMOUNT = "with_draw_uid_amount";


    @Override
    public Long getComponentId() {
        return ComponentId.WZRY_GAME;
    }

    /**
     * 营收提现回调用检查
     */
    @GeHandler(rid = GeHandlerConst.RID_WZRY, fid = GeHandlerConst.FID_WZRY_WITHDRAW_CHECK, method = GeHandler.Method.read)
    public StringResponse withDrawCheck(StringRequest request) {
        log.info("withDrawCheck request:{}", request);
        WithDrawCheckResp resp = new WithDrawCheckResp();
        resp.setCanWithdraw(true);
        resp.setMessage("ok");

        var attr = getUniqueComponentAttr(request.getActId());
        if (StringUtil.isNotBlank(attr.getRiskStrategyKey())) {
            WithDrawCheckReq req = com.alibaba.fastjson.JSON.parseObject(request.getData(), WithDrawCheckReq.class);
            boolean risk = zhuiwanRiskClient.hitRiskOfApp(req.getUid(), req.getHdid(), req.getIp(), req.getApp(), req.getMarket(), req.getClientType(), attr.getRiskStrategyKey());
            if (risk) {
                resp.setCanWithdraw(false);
                resp.setMessage("设备存在异常，请稍后重试");
                log.warn("withDrawCheck hit risk,actId:{},uid:{},seq:{}", request.getActId(), req.getUid(), req.getSeq());
            }
        }

        var result = SimpleResult.genStringResponse(0, "ok");
        result.setData(com.alibaba.fastjson.JSON.toJSONString(resp));

        return result;
    }

    /**
     * 营收提现成功结果通知
     */
    @GeHandler(rid = GeHandlerConst.RID_WZRY, fid = GeHandlerConst.FID_WZRY_WITHDRAW_CALL_BACK, method = GeHandler.Method.write)
    public StringResponse withDrawCallBack(StringRequest request) {
        log.info("withDrawCallBack request:{}", request);
        WithDrawNotify notify = JSON.parseObject(request.getData(), WithDrawNotify.class);
        if (!notify.isSuccess()) {
            log.warn("withDrawCallBack not success");
        } else {
            //这个字段营收后补
            long actId = request.getActId() == 0 ? 2024017001 : request.getActId();
            var attr = getUniqueComponentAttr(actId);
            String redis = getRedisGroupCode(actId);

            String total = makeKey(attr, WITH_DRAW_TOTAL_AMOUNT);
            long totalAfter = actRedisDao.incrValue(redis, total, notify.getAmount());
            log.info("withDrawCallBack total uid:{},amount:{},after:{}", notify.getUid(), notify.getAmount(), totalAfter);
            if (totalAfter >= attr.getTotalWithDrawWarn()) {
                String msg = String.format("活动总提现金额：%s\n 历史累积提现金额：%s \n uid：%s \n 请留意是否符合预期", notify.getAmount() / 100, totalAfter / 100, notify.getUid());
                msg = buildActRuliuMsg(actId, true, "活动总提现成功金额超出预期", msg);
                baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, Lists.newArrayList());
            }

            String userKey = makeKey(attr, WITH_DRAW_UID_AMOUNT);
            long after = actRedisDao.hIncrByKey(redis, userKey, Convert.toString(notify.getUid()), notify.getAmount());
            log.info("withDrawCallBack user uid:{},amount:{},after:{}", notify.getUid(), notify.getAmount(), after);
            if (after >= attr.getUserWithDrawWarn()) {
                String msg = String.format("当前用户提现金额：%s\n 历史累积提现金额：%s \n uid：%s \n 请留意是否符合预期", notify.getAmount() / 100, after / 100, notify.getUid());
                msg = buildActRuliuMsg(actId, true, "用户提现成功金额超出预期", msg);
                baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, Lists.newArrayList());
            }

        }
        return SimpleResult.genStringResponse(0, "ok");
    }

    /**
     * 赛宝游戏结束通知事件
     */
    @HdzjEventHandler(value = ZhuiwanGameNotify.class, canRetry = false)
    public void zhuiwanGameNotify(ZhuiwanGameNotify event, WzryGameComponentAttr attr) {
        log.info("zhuiwanGameNotify event:{},attr:{}", com.alibaba.fastjson.JSON.toJSONString(event), JSON.toJSONString(attr));

        //为了降低代码复杂度和测试复杂度，这里不加任务结算逻辑，结算只认1个口，就是定时器主动查询的口
    }


    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void autoCreateGame() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            timerSupport.work("autoCreateGame:" + actId, 60, () -> {
                log.info("begin auto create game,actId:{}", actId);
                autoCreateGame(tryGetUniqueComponentAttr(actId));
            });
        }

    }

    /**
     * 赛宝赛事初始化
     */
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void autoInitGame() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            timerSupport.work("autoInitGame:" + actId, 60, () -> {
                log.info("begin auto init game,actId:{}", actId);
                //提前30分钟初始化赛宝赛事
                Date signUpBefore = DateUtil.addMinutes(commonService.getNow(actId), Const.THIRTY);
                wzryGameInitService.autoInitSbGame(actId, signUpBefore, 100);
            });
        }
    }

    /**
     * 给1V1赛事分配队伍
     */
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0/30 * * * * ? ")
    public void autoAllocationGameTeam() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActShowTime(actId)) {
                return;
            }
            timerSupport.work("autoAllocationGameTeam:" + actId, 60, () -> {
                log.info("begin autoAllocationGameTeam,actId:{}", actId);
                //报名时间截止后，开始分配队伍
                Date now = commonService.getNow(actId);
                now = DateUtil.addSeconds(now, -ALLOCATION_GAME_TEAM_OFFSET_SECONDS);
                wzryGameInitService.autoAllocationGameTeam(actId, now, 10);
            });
        }
    }


    /**
     * 关闭超时赛事和推进赛事状态
     */
    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0/5 * * * * ? ")
    public void cancelSbTimeOutGame() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                return;
            }
            WzryGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("cancelSbTimeOutGame:" + actId, 60, () -> {
                log.info("begin cancelSbTimeOutGame game,actId:{}", actId);
                //比赛开始10分钟后，检查赛宝赛事有无开启，如无开启则关闭赛事
                Date now = commonService.getNow(actId);
                Date signUpEndTimeEnd = DateUtil.addMinutes(now, -JOIN_WZSB_READY_TIME);
                wzryGameSettleService.cancelSbTimeOutGame(attr, actId, now, signUpEndTimeEnd, CANCEL_SB_TIME_OUT_BATCH);
            });
        }
    }


    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0/5 * * * * ? ")
    public void cancelWzryTimeOutGame() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                return;
            }
            WzryGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("cancelWzryTimeOutGame:" + actId, 60, () -> {
                log.info("begin cancelWzryTimeOutGame game,actId:{}", actId);
                //报名结束10分钟后，检查赛宝赛事有无开启，如无开启则关闭赛事
                Date now = commonService.getNow(actId);
                wzryGameSettleService.cancelWzryTimeOutGame(attr, now, JOIN_WZ_GAME_READY_TIME, CANCEL_WZRY_TIME_OUT_BATCH);
            });
        }
    }

    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0/5 * * * * ? ")
    public void closeGameOverList() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                return;
            }
            WzryGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("closeGameOverList:" + actId, 60, () -> {
                log.info("begin closeGameOverList game,actId:{}", actId);
                //报名结束10分钟后，检查赛宝赛事有无开启，如无开启则关闭赛事
                wzryGameSettleService.closeGameOverList(attr, CLOSE_GAME_OVER_LIST_BATCH);
            });
        }
    }

    @NeedRecycle(author = "zengwenzhi", notRecycle = true)
    @Scheduled(cron = "0 0 10 * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                return;
            }
            WzryGameComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                continue;
            }
            timerSupport.work("staticReport:" + actId, 60, () -> {
                log.info("begin staticReport game,actId:{}", actId);
                doStaticReport(actId, commonService.getNow(actId), attr);
            });
        }
    }

    public void doStaticReport(long actId, Date now, WzryGameComponentAttr attr) {
        Date preDay = DateUtil.add(now, -1);
        String dayCode = DateUtil.format(preDay, DateUtil.PATTERN_TYPE2);
        StringBuilder content = new StringBuilder();
        content.append(String.format("### 数据日期：%s\n", dayCode));

        WzryTaskComponentAttr tastAttr = wzryTaskComponent.tryGetUniqueComponentAttr(actId);
        if (tastAttr != null) {
            content.append("### 【完成任务数据】\n");
            Map<String, String> data = wzryTaskComponent.queryStaticReport(actId, tastAttr, dayCode);
            for (String name : data.keySet()) {
                content.append(name).append("：").append(data.get(name)).append("\n");
            }
        }

        ActivityInfoVo activityInfoVo = actInfoService.queryActivityInfo(actId);
        Date preDayBegin = DateUtil.getDayBeginTime(preDay);
        Date preDayEnd = DateUtil.getDayBeginTime(now);

        //---赛事数据
        content.append("### 【赛事数据】\n");
        content.append("参赛人次：").append(wzryDao.queryJoinGameUserCount(actId, preDayBegin, preDayEnd)).append("\n");
        content.append("参赛人数(去重)：").append(wzryDao.queryJoinGameUserDistinctCount(actId, preDayBegin, preDayEnd)).append("\n");
        content.append("获得1v1赏金人数：").append(wzryDao.queryWinnerUserCount(actId, BattleMode.GAME_1V1, preDayBegin, preDayEnd)).append("\n");
        content.append("获得5v5赏金人数：").append(wzryDao.queryWinnerUserCount(actId, BattleMode.GAME_5V5, preDayBegin, preDayEnd)).append("\n");


        //---营收数据
        content.append("### 【营收数据】\n");

        Map<String, String> history = turnoverCurrencyServiceThriftClient.queryMonthSettleApplyInfo(attr.getTurnoverAppId(), activityInfoVo.getBeginTime(), preDayEnd.getTime());
        content.append("历史提现人数：").append(history.getOrDefault("userNum", "0")).append("\n");
        content.append("历史提现金额(厘)：").append(history.getOrDefault("applyRealAmount", "0")).append("\n");

        Map<String, String> day = turnoverCurrencyServiceThriftClient.queryMonthSettleApplyInfo(attr.getTurnoverAppId(), preDayBegin.getTime(), preDayEnd.getTime());
        content.append("日提现人数：").append(day.getOrDefault("userNum", "0")).append("\n");
        content.append("日提现金额(厘)：").append(day.getOrDefault("applyRealAmount", "0")).append("\n");

        String msg = buildActRuliuMsg(actId, false, "王者荣耀赏金赛日报", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, Lists.newArrayList());
    }

    public void autoCreateGame(WzryGameComponentAttr attr) {
        int preDay = attr.getCreateGamePreDay();
        Date now = commonService.getNow(attr.getActId());
        for (int i = 0; i < preDay; i++) {
            Date gameTime = DateUtil.add(now, i);
            if (!actInfoService.inActTime(gameTime, attr.getActId())) {
                break;
            }
            String dayCode = DateUtil.format(gameTime, DateUtil.PATTERN_TYPE2);
            autoCreateGame(attr, dayCode);
        }
    }

    public void autoCreateGame(WzryGameComponentAttr attr, String dayCode) {
        String redisGroup = getRedisGroupCode(attr.getActId());

        //---判断当天有无重复创建
        String createGameRecord = makeKey(attr, CREATE_GAME_DAY_CODE);
        //因为上层有锁，这里读取是安全的，未保证最终能创建成功，要完成创建后，再写入完成标记！！！
        String alreadyCreate = actRedisDao.hget(redisGroup, createGameRecord, dayCode);
        if (StringUtil.isNotBlank(alreadyCreate)) {
            log.info("already create,actId:{},day:{}", attr.getActId(), dayCode);
            return;
        }

        wzryGameInitService.autoCreateGame(attr, dayCode);

        //创建完成，写入完成标记
        actRedisDao.hset(redisGroup, createGameRecord, dayCode, DateUtil.format(new Date()));
        log.info("create done,actId:{},day:{}", attr.getActId(), dayCode);
    }

    @GetMapping("/initCreateGame")
    public Response<Map<String, Object>> initCreateGame(HttpServletRequest request, HttpServletResponse response, Long actId, String dayCode) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            Response.fail(-1, "未登录");
        }
        WzryGameComponentAttr attr = getUniqueComponentAttr(actId);
        if (!attr.getInitGameAuth().contains(uid)) {
            Response.fail(-1, "no auto");
        }
        autoCreateGame(attr, dayCode);
        return Response.ok();
    }

    @GetMapping("/initCreateSbGame")
    public Response<Map<String, Object>> initCreateSbGame(HttpServletRequest request, HttpServletResponse response, Long actId, int min) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            Response.fail(-1, "未登录");
        }
        WzryGameComponentAttr attr = getUniqueComponentAttr(actId);
        if (!attr.getInitGameAuth().contains(uid)) {
            Response.fail(-1, "no auto");
        }
        //提前10分钟初始化赛宝赛事
        Date signUpBefore = DateUtil.addMinutes(commonService.getNow(actId), min);
        wzryGameInitService.autoInitSbGame(actId, signUpBefore, Const.TEN);
        return Response.ok();
    }

    @GetMapping("/notifyStatic")
    public Response<String> notifyStatic(HttpServletRequest request, HttpServletResponse response, Long actId, String dayCode) {
        doStaticReport(actId, DateUtil.getDate(dayCode, DateUtil.PATTERN_TYPE2), getUniqueComponentAttr(actId));
        return Response.ok();
    }

    /**
     * 赛事列表
     */
    @GetMapping("/gameList")
    public Response<Map<String, Object>> gameList(HttpServletRequest request, HttpServletResponse response, Long actId, Integer page, Integer pageSize) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            Response.fail(-1, "未登录");
        }

        Map<String, Object> result = Maps.newHashMap();
        //每页最多15条，防止前端参数打爆接口
        pageSize = Math.min(pageSize, 15);
        var gameList = queryGameList(actId, uid, page, pageSize);
        result.put("gameList", gameList);

        //页面下次刷新时间
        Date now = commonService.getNow(actId);
        Date recentlyStartTime = wzryDao.getRecentlyGameTime(actId, now);
        if (recentlyStartTime == null) {
            var attr = getUniqueComponentAttr(actId);
            recentlyStartTime = DateUtil.addMinutes(now, Math.max(attr.getCreate1V1GameTimeSpanMin(), Const.TEN));
        }
        var nextReloadDataTime = DateUtil.format(DateUtil.addSeconds(recentlyStartTime, RandomUtil.RD.nextInt(3, 7)));
        result.put("nextReloadDataTime", nextReloadDataTime);

        result.put("serverTime", DateUtil.format(commonService.getNow(actId)));

        return Response.success(result);
    }


    /**
     * 压力测试用
     */
    @GetMapping("/testMulAddGame")
    public Response<String> testMulAddGame(long actId, String gameViewCode, int uidSize) {
        if (!SysEvHelper.isDev()) {
            return Response.fail(-1, "prod");
        }
        long uidStart = 50042952L;
        for (int i = 0; i < uidSize; i++) {
            long uid = uidStart + i;
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(() -> {
                wzryGameService.joinGame(getUniqueComponentAttr(actId), uid, "yomi", "", "", 1, gameViewCode);
            });
        }

        return Response.ok();
    }

    @GetMapping("/refundMepiao9527")
    public Response<String> refundMepiao9527(HttpServletRequest request, HttpServletResponse response, long actId, String seq, long refundUid, long price) {
        long uid = getLoginYYUid(request, response);
        if (uid != 50042952L) {
            return Response.fail(-1, "refuse");
        }
        if (Const.ONESTR.equals(Const.GEPM.getParamValue("close_refundMepiao9527", Const.ONESTR))) {
            return Response.fail(-2, "not open");
        }
        WzryGameComponentAttr attr = getUniqueComponentAttr(actId);
        TReverseConsumeProductRequest tRequest = new TReverseConsumeProductRequest();
        tRequest.setUid(uid);
        tRequest.setAppid(TAppId.findByValue(attr.getTurnoverAppId()));
        tRequest.setAmount(price);
        tRequest.setSeqId(seq);
        tRequest.setDescription("赛事取消退票");
        var result = turnoverProductServiceClient.reverseConsumeProduct(tRequest);
        return Response.ok(result.toString());
    }


    public List<WzryGameVo> queryGameList(long actId, long uid, Integer page, Integer paseSize) {
        WzryGameComponentAttr attr = getUniqueComponentAttr(actId);
        Date now = commonService.getNow(actId);
        //最近2天未开始的赛事
        var gameViewList = wzryDao.queryWzryGameView(actId, now, attr, 2, Convert.toInt(page, 0), Convert.toInt(paseSize, 5));
        //用户参赛记录
        var joinGameRecord = wzryGameService.queryUserJoinGameRecord(actId, gameViewList, uid);
        //用户关注赛事记录
        Map<String, Long> userSub = queryUserSubGame(attr, uid, gameViewList);

        return wzryGameService.buildWzryLast2DayGameVo(attr, now, gameViewList, joinGameRecord, userSub);
    }

    /**
     * 用户预约了的赛事
     *
     * @return key game_view_code,value game_view_id
     */
    private Map<String, Long> queryUserSubGame(WzryGameComponentAttr attr, long uid, List<WzryGameView> gameViewList) {
        Map<String, Long> result = Maps.newHashMap();

        if (CollectionUtils.isEmpty(gameViewList)) {
            return result;
        }
        List<String> Keys = gameViewList.stream().map(p -> buildSubscribeKey(attr, p.getGameViewCode())).toList();
        List<Object> objects = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId())).executePipelined((RedisConnection connection) -> {
            for (String key : Keys) {
                connection.hGet(key.getBytes(), Convert.toString(uid).getBytes());
            }
            return null;
        });

        for (int i = 0; i < gameViewList.size(); i++) {
            if (objects.get(i) != null) {
                var gameView = gameViewList.get(i);
                result.put(gameView.getGameViewCode(), gameView.getId());
            }
        }

        return result;
    }


    /**
     * 我报名的赛事-快速进入赛事入口
     */
    @GetMapping("/myGame")
    public Response<Map<String, Object>> myGame(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            Response.fail(-1, "未登录");
        }

        Date startTimeStart = DateUtil.addMinutes(commonService.getNow(actId), -(JOIN_WZSB_READY_TIME + JOIN_WZ_GAME_READY_TIME));
        Date startTimeEnd = DateUtil.addMinutes(commonService.getNow(actId), (JOIN_WZSB_READY_TIME + JOIN_WZ_GAME_READY_TIME));

        Map<String, Object> result = Maps.newHashMap();
        WzryGameComponentAttr attr = getUniqueComponentAttr(actId);
        result.put("gameList", wzryGameService.getWzryMyGameVo(actId, attr, startTimeStart, startTimeEnd, uid));
        return Response.success(result);
    }


    /**
     * 我的参赛历史记录
     */
    @GetMapping("/myGameRecord")
    public Response<Map<String, Object>> myGameRecord(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex, Integer page, Integer pageSize) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            Response.fail(-1, "未登录");
        }
        WzryGameComponentAttr attr = getUniqueComponentAttr(actId);
        pageSize = Math.min(pageSize, 20);
        List<WzryMyGameRecordVo> gameRecordVos = wzryGameService.getWzryMyGameCloseGame(attr, actId, uid, Convert.toInt(page, 0), Convert.toInt(pageSize, 5));
        return Response.success(ImmutableMap.of("gameList", gameRecordVos));
    }


    /**
     * 报名参赛
     */
    @GetMapping("/joinGame")
    public Response<String> joinGame(HttpServletRequest request, HttpServletResponse response,
                                     @RequestHeader("x-fts-host-name") String app,
                                     @RequestHeader("YYHeader-y0") String hdid,
                                     @RequestHeader(value = "YYHeader-Platform", defaultValue = "0") int clientType,
                                     Long actId, Long cmptIndex
            , String gameViewCode) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        WzryGameComponentAttr attr = getUniqueComponentAttr(actId);
        try {
            String ip = RequestUtil.getRealIp(request);
            wzryGameService.joinGame(attr, uid, app, ip, hdid, clientType, gameViewCode);

        } catch (SuperException e) {
            log.warn("joinGame warn,actId:{},gameViewCode:{},uid:{},e:{},e", actId, gameViewCode, uid, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());

        } catch (Exception e) {
            log.warn("joinGame error,actId:{},gameViewCode：{},uid:{},e:{},e", actId, gameViewCode, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }

        return Response.success("ok");
    }


    /**
     * 进入开黑房间，根据频道号查询赛事信息
     */
    @GetMapping("/queryGame")
    public Response<Map<String, Object>> queryGame(HttpServletRequest request, HttpServletResponse response, Long actId, String gameCode, Long sid, Long ssid) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        try {
            //要求同一场活动，保证ssid尽可能不重复
            WzryMyGameVo wzryMyGameVo = wzryGameService.queryWzryMyGameVo(actId, gameCode, sid, ssid, uid);
            log.info("queryGame actId:{},uid:{},gameCode:{},sid:{},ssid:{},wzryMyGameVo:{}", actId, uid, gameCode, sid, ssid, JSON.toJSONString(wzryMyGameVo));
            var result = ImmutableMap.of("game", wzryMyGameVo, "serverTime", DateUtil.format(commonService.getNow(actId)));
            return Response.success(result);

        } catch (SuperException e) {
            log.warn("queryGame warn,actId:{},sid:{},ssid:{},uid:{},e:{},e", actId, sid, ssid, uid, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());

        } catch (Exception e) {
            log.warn("queryGame error,actId:{},sid：{},ssid:{},uid:{},e:{},e", actId, sid, ssid, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }
    }

    /**
     * 赛宝房间跳转（获取开黑房间链接）
     */
    @GetMapping("/jumpGame")
    public Response<String> jumpGame(HttpServletRequest request, HttpServletResponse response, Long actId, String gameCode, Long cmptIndex) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }
        try {
            String url = wzryGameService.getJumpGame(actId, gameCode, uid);
            log.info("jumpGame actId:{},gameCode:{},uid:{}", actId, gameCode, uid);
            return Response.success(url);

        } catch (SuperException e) {
            log.warn("jumpGame warn,actId:{},gameCode:{},uid:{},e:{},e", actId, gameCode, uid, e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());

        } catch (Exception e) {
            log.warn("jumpGame error,actId:{},gameCode：{},uid:{},e:{},e", actId, gameCode, uid, e.getMessage(), e);
            return Response.fail(SuperException.E_FAIL, "网络超时");
        }

    }

    @GetMapping("/testJumpGame")
    public String testJumpGame(HttpServletRequest request, HttpServletResponse response, Long actId, String game, int seat, int team, long uid) {
        if (!SysEvHelper.isDev()) {
            return "not dev";
        }
        var joinResult = saiBaoClient.joinRoom(game, uid, seat, team);
        return joinResult.getRoomUrl() + game + "?" + joinResult.getRoomParameters();
    }

    /**
     * 预约比赛
     */
    @GetMapping("/subscribe")
    public Response<String> subscribe(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex, String gameViewCode) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(-1, "未登录");
        }

        return subscribe(actId, gameViewCode, uid);

    }

    public Response<String> subscribe(long actId, String gameViewCode, long uid) {
        log.info("subscribe begin actId:{},gameViewCode:{},uid:{}", actId, gameViewCode, uid);
        WzryGameComponentAttr attr = getUniqueComponentAttr(actId);
        String key = buildSubscribeKey(attr, gameViewCode);
        if (!actRedisDao.hsetnx(getRedisGroupCode(actId), key, Convert.toString(uid), DateUtil.getNowYyyyMMddHHmmss())) {
            log.info("subscribe return actId:{},gameViewCode:{},uid:{}", actId, gameViewCode, uid);
            return Response.fail(-2, "已预约");
        }


        WzryGameView where = new WzryGameView();
        where.setGameViewCode(gameViewCode);
        where.setActId(actId);
        WzryGameView gameView = gameecologyDao.selectOne(WzryGameView.class, where, "");

        Date now = commonService.getNow(actId);
        if (now.after(gameView.getSignUpStartTime())) {
            return Response.fail(-3, "赛事已开始报名");
        }


        Date pushTime = SysEvHelper.isDeploy() && !commonService.isGrey(actId) ? gameView.getSignUpStartTime() : DateUtil.addMinutes(new Date(), 1);
        zhuiWanPrizeIssueServiceClient.sendTimingPush(attr.getFromId(), pushTime
                , attr.getTitle(), attr.getContent(), attr.getImage(), attr.getLink(), attr.getFromId(), Convert.toString(uid));

        log.info("subscribe end actId:{},gameViewCode:{},uid:{}", actId, gameViewCode, uid);
        return Response.ok();
    }


    /**
     * 赏金弹幕
     */
    @GetMapping("/awardRoll")
    public Response<Map<String, Object>> awardRoll(HttpServletRequest request, HttpServletResponse response, Long actId, Long cmptIndex) {
        Map<String, Object> result = Maps.newHashMap();

        var awardList = wzryGameService.getWzryAwardRollVos(actId, 30);
        return Response.success(ImmutableMap.of("awardList", awardList));
    }

    @Report
    @RequestMapping(value = "/queryLayerStatus")
    public Response<Map<String, Object>> queryLayerStatus(String seq, Long actId, Long sid, Long ssid) {
        Clock clock = new Clock();
        try {
            if (actId == null || sid == null || ssid == null) {
                log.warn("para error,actid:{},sid:{},ssid:{}", actId, sid, ssid);
                return new Response<>(Code.E_DATA_ERROR.getCode(), "para error", null);
            }
            Map<String, Object> result = Maps.newHashMap();
            result.put("showStatus", wzryGameService.existRoomChannel(actId, sid, ssid) ? 1 : 0);
            result.put("time", System.currentTimeMillis());
            result.put("grey", commonService.isGrey(actId));
            return new Response<>(Code.OK.getCode(), "ok", result);
        } catch (Exception e) {
            log.error("buildLayerInfo exception,actId:{},seq:{},sid:{},ssid:{},clock:{},err:{}", actId, seq, sid, ssid, clock.tag(), e.getMessage(), e);
            return new Response<>(Code.E_SYS_BUSY);
        }
    }


    private String buildSubscribeKey(WzryGameComponentAttr attr, String gameViewCode) {
        return makeKey(attr, String.format(SUBSCRIBE_RECORD, gameViewCode));
    }

}
