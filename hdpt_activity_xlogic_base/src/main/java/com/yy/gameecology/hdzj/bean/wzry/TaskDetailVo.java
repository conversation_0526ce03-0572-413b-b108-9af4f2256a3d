package com.yy.gameecology.hdzj.bean.wzry;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author; CXZ
 * @Desciption;
 * @Date; 2024/1/17 20;01
 * @Modified;
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TaskDetailVo {

    /**
     * 门票剩余数量
     */
    private long balance;
    /**
     * 是否是新用户
     */
    private boolean isNewUser;
    /**
     * 签到详情
     */
    private SignInDetailVo signInInfo;
    /**
     * 任务列表
     */
    private List<TaskInfoVo> taskList;
}
