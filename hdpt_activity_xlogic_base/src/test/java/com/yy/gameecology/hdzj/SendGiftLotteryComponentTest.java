package com.yy.gameecology.hdzj;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.hdzj.element.history.SendGiftLotteryComponent;
import com.yy.gameecology.hdzj.element.component.attr.BoxLotteryComponentAttr;
import com.yy.thrift.broadcast.Template;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties","classpath:env/local/group-setting-3.properties"})
public class SendGiftLotteryComponentTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SendGiftLotteryComponent sendGiftLottery;

    @Test
    public void doNotify() throws Exception {

        BoxLotteryComponentAttr attr = sendGiftLottery.getComponentAttr(2021053001L, 1);

        SendGiftEvent event = new SendGiftEvent();
        event.setSendUid(2522180660L);
        event.setRecvUid(2550839108L);
        event.setGiftId("BB_EMTS");
        event.setGiftNum(13140000L);
        event.setSeq(UUID.randomUUID().toString());
        event.setSid(1452225881L);
        event.setSsid(1452225881L);
        event.setGiftNum(1L);
        event.setTemplate(Template.Gamebaby);
        while (true){
            try {
                sendGiftLottery.sendGiftLottery(event,attr);
            } catch (Exception e){
                log.error(" {}",e.getMessage(),e);
            }

        }

    }
    @Test
    public void finishGiftComboTest() throws Exception {

        BoxLotteryComponentAttr attr = sendGiftLottery.getComponentAttr(2021053001L, 1);


        while (true){
            sendGiftLottery.finishGiftCombo();
        }

    }

}
