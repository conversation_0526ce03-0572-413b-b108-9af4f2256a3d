package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.FtsRoomMgrInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.client.thrift.FtsRecommendDataThriftClient;
import com.yy.gameecology.activity.client.thrift.FtsRoomManagerThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.bean.UserInfo;
import com.yy.gameecology.common.client.WebdbServiceClient;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.HdztRankType;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.TeamDTO;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.AnchorTeamComponent;
import com.yy.gameecology.hdzj.element.component.attr.MatchResultOnlineBroComponentAttr;
import com.yy.java.webdb.WebdbSubChannelInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.*;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.yy.gameecology.common.consts.PBCommonBannerId.ACT_TOP_N;

/**
 * *
 * 1. 弹窗协议
 * A. uri = 100008
 * B. bannerId = 16
 * C. bannerType 配置使用,区分不同赛果
 * D1. 公会类型的jsonData：{"rank":"","sid":"","logo":"","memberId":"","nick":""}
 * D2. 战队类型的jsonData: {"rank":"","uid":"","teamName":"","teamMembers":""}
 * D3. 用户/主播类型的jsonData:  {"rank":"",uid":"","nick":"","logo":""}
 * D4. CP类型的jsonData:  {"rank":"","playerUid":"","anchorUid":"","player":{"uid":"","nick":"","logo":""},"anchor":{"uid":"","nick":"","logo":""}}
 * D5. 厅类型的jsonData: {"rank":"","sid":"","logo":"","memberId":"","nick":""}
 *
 * <AUTHOR>
 * @date 2022.10.09 15:10
 * 赛果广播需等待用户在频道内才播,活动结束后一小时内有效
 * 注意: 存在不同阶段都获得弹窗时,需分开配置,如前面阶段获得top8弹窗 最后阶段又获得top3弹窗,此时需要分开配置
 */
@UseRedisStore
@Component
public class MatchResultOnlineBroComponent extends BaseActComponent<MatchResultOnlineBroComponentAttr> {

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private AnchorTeamComponent anchorTeamComponent;

    @Autowired
    private WebdbServiceClient webdbServiceClient;
    @Autowired
    private FtsRecommendDataThriftClient ftsRecommendDataThriftClient;

    @Autowired
    private FtsRoomManagerThriftClient ftsRoomManagerThriftClient;


    //需要广播的uid
    public static final String NEED_BRO_UID = "need_bro_uid";

    //活动结束时,神豪或主播不在频道内,可等待1小时内广播 存在单个神豪霸占多个榜单 所以需要用list need_bro_record list
    public static final String NEED_BRO_USER_RECORD = "need_bro_record";

    //活动结束时,神豪或主播不在频道内,可等待1小时内广播 存在单个神豪霸占多个榜单 所以需要用list need_bro_record list
    // cp的广播需要区分,所以要分开存储
    public static final String NEED_BRO_CP_RECORD = "need_bro_cp_record";

    /**
     * 过期时间,单位:秒
     */
    public static final String EXPIRE_TIME = "expire_time";

    @Override
    public Long getComponentId() {
        return ComponentId.MATCH_RESULT_BRO_ONLINE;
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEnd(PhaseTimeEnd event, MatchResultOnlineBroComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();

        String rankPhaseKey = rankId + "_" + phaseId;
        MatchResultOnlineBroComponentAttr.BroConfig broConfig = attr.getBroConfigMapV2().get(rankPhaseKey);
        if (broConfig == null) {
            return;
        }
        SysEvHelper.waiting(broConfig.getSettleDelay());
        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));
        //todo 支持可能是pk晋级 done
        List<Rank> topNRank = getTopNRank(attr.getActId(), rankId, phaseId, dateStr, broConfig.getPromotType(), broConfig.getTopN());
        if (CollectionUtils.isNotEmpty(topNRank) && broConfig.getThresholdScore() > 0) {
            topNRank = topNRank.stream().filter(rank -> rank.getScore() >= broConfig.getThresholdScore()).toList();
        }
        handleBroBanner(event.getSeq(), topNRank, attr, broConfig);
        log.info("handleBroBanner done:seq:{} config:{}, ranks:{}", event.getSeq(), JSON.toJSONString(broConfig), JSON.toJSONString(topNRank));
    }

    //目前只支持两种晋级类型 pk和排名
    private List<Rank> getTopNRank(long actId, long rankId, long phaseId, String dateStr, String promotType, int topN) {
        final boolean pk = "pk".equals(promotType);
        if (pk) {
            //pk晋级,注意这里只获取胜利方的topN
            List<GroupMemberItem> winnerItems = new ArrayList<>();
            PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, phaseId, "00000000", "", false, true, Maps.newHashMap());
            pkInfo.getPkGroupItems().stream().map(PkGroupItem::getMemberPkItems).flatMap(Collection::stream)
                    .forEach(
                            memberPkItems -> {
                                GroupMemberItem memberItem1 = memberPkItems.get(0);
                                GroupMemberItem memberItem2 = memberPkItems.get(1);
                                GroupMemberItem winner = memberItem1.getRank() < memberItem2.getRank() ? memberItem1 : memberItem2;
                                winnerItems.add(winner);
                            });
            List<GroupMemberItem> collect = winnerItems.stream().sorted(Comparator.comparing(GroupMemberItem::getScore).reversed()).toList();
            int count = 1;
            List<Rank> ranks = new ArrayList<>();
            for (GroupMemberItem item : collect) {
                if (count <= topN) {
                    Rank rank = new Rank();
                    rank.setMember(item.getMemberId());
                    rank.setRank(count++);
                    rank.setScore(item.getScore());
                    ranks.add(rank);
                }
            }
            //如果胜方不够topN,则加上复活的主播 count从1开始
            if (count < topN + 1) {
                //复活结算稍晚些,需等待3秒
                SysEvHelper.waiting(3000);
                Map<String, String> ext = Maps.newHashMap();
                ext.put(RankExtParaKey.RANK_PK_REVIVE, HdztRankType.REVIVE);
                List<Rank> revives = hdztRankingThriftClient.queryRankingCache(actId, rankId, phaseId, dateStr, topN + 1 - count, "", ext);
                for (Rank revive : revives) {
                    revive.setRank(count++);
                    ranks.add(revive);
                }
            }
            return ranks;
        }

        return hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, topN, Maps.newHashMap());
    }

    private void handleBroBanner(String seq, List<Rank> ranks, MatchResultOnlineBroComponentAttr attr, MatchResultOnlineBroComponentAttr.BroConfig broConfig) {
        int roleType = broConfig.getRoleType();
        long delay = broConfig.getInitDelaySecond();
        switch (roleType) {
            //公会
            case 1:
                for (Rank rank : ranks) {
                    long sid = Long.parseLong(rank.getMember());
                    ChannelBaseInfo ci = commonService.getChannelInfo(sid, true);
                    ImmutableMap<String, Object> data = ImmutableMap.of("rank", rank.getRank() + broConfig.getRankOffset(), "sid",
                            ci.getAsid(), "asid", ci.getAsid(), "logo", ci.getLogo(), "memberId", rank.getMember(), "nick", ci.getName());

                    Const.EXECUTOR_DELAY_GENERAL.schedule(() -> {
                        broBanner(seq, sid, attr.getBusiId(), attr.getActId(), ACT_TOP_N, broConfig.getRoleType(),
                                broConfig.getBannerType(), broConfig.getBroadcastType(), broConfig.getExcludeDanmaku(), sid, 0, data);
                    }, delay, TimeUnit.SECONDS);

                    delay += broConfig.getInterval();
                }
                break;
            //战队
            case 2:
                broTeamResult(seq, ranks, attr, broConfig);
                break;
            //单个用户 主播or神豪
            case 3:
                for (Rank rank : ranks) {
                    long uid = Long.parseLong(rank.getMember());
                    UserBaseInfo ui = commonService.getUserInfo(uid, true);
                    ImmutableMap<String, Object> data = ImmutableMap.of("rank", rank.getRank() + broConfig.getRankOffset(), "uid", uid, "nick",
                            ui.getNick(), "logo", ui.getLogo());

                    Const.EXECUTOR_DELAY_GENERAL.schedule(() -> broUser(seq, attr, uid, data, broConfig), delay, TimeUnit.SECONDS);
                    delay += broConfig.getInterval();
                }
                break;
            //cp
            case 4:
                for (Rank rank : ranks) {
                    String[] uids = rank.getMember().split("\\|");
                    long userUid = Long.parseLong(uids[0]);
                    UserBaseInfo userUI = commonService.getUserInfo(userUid, true);
                    Map<String, Object> userMap = ImmutableMap.of("uid", userUid, "nick", userUI.getNick(), "logo", userUI.getLogo());

                    long anchorUid = Long.parseLong(uids[1]);
                    UserBaseInfo anchorUI = commonService.getUserInfo(anchorUid, true);
                    Map<String, Object> anchorMap = ImmutableMap.of("uid", anchorUid, "nick", anchorUI.getNick(), "logo", anchorUI.getLogo());
                    ImmutableMap<String, Object> data = ImmutableMap.of("rank", rank.getRank() + broConfig.getRankOffset(), "player", userMap,
                            "anchor", anchorMap, "playerUid", userUid, "anchorUid", anchorUid, "excludeDanmaku", broConfig.getExcludeDanmaku());

                    broCPBanner(seq, attr, broConfig, userUid, anchorUid, data);
                }
                break;
            // 厅 TODO
            case 5:
                List<String> memberIds = Lists.newArrayList();
                ranks.forEach(rank -> {
                    memberIds.add(rank.getMember());
                });

                // 频道信息,key:sid_ssid
                Map<String, WebdbSubChannelInfo> subChannels = webdbServiceClient.batchGetSubChannelInfo(memberIds);

                // boss后台配置的推荐图,key:sid_ssid
                Map<String, String> recommendPicMap = ftsRecommendDataThriftClient.batchGetRecommendConfigPicture(memberIds, changeToBusinessType(attr.getBusiId()));

                // 厅管图:key:sid_ssid
                Map<String, String> roomMgrPicMap = ftsRecommendDataThriftClient.batchGetRoomMgrPicByChannel(memberIds);

                Map<String, FtsRoomMgrInfoVo> roomMgrInfoMap = ftsRoomManagerThriftClient.ftsBatchGetRoomMgrInfoByCh(memberIds);

                for (Rank rank : ranks) {
                    String[] sid_ssid = rank.getMember().split(StringUtil.UNDERSCORE);
                    long sid = Convert.toLong(sid_ssid[0]);
                    long ssid = Convert.toLong(sid_ssid[1]);
                    ChannelBaseInfo ci = commonService.getChannelInfo(sid, false);
                    String logo = getChannelLogo(roomMgrPicMap.get(rank.getMember()), recommendPicMap.get(rank.getMember()), ci.getLogo());
                    WebdbSubChannelInfo subChannelInfo = subChannels.get(rank.getMember());
                    String nick = "";
                    if (subChannelInfo != null) {
                        nick = subChannelInfo.getName();
                    }
                    if (StringUtil.isEmpty(nick)) {
                        nick = ci.getName();
                    }

                    nick = getHallName(rank.getMember(),nick,roomMgrInfoMap);

                    nick = Base64.encodeBase64String(nick.getBytes());
                    ImmutableMap<String, Object> data = ImmutableMap.of("rank", rank.getRank() + broConfig.getRankOffset(), "sid",
                            ci.getAsid(),  "logo", logo, "memberId", rank.getMember(), "nick", nick);

                    Const.EXECUTOR_DELAY_GENERAL.schedule(() -> {
                        broBanner(seq, sid, attr.getBusiId(), attr.getActId(), ACT_TOP_N, broConfig.getRoleType(),
                                broConfig.getBannerType(), broConfig.getBroadcastType(), broConfig.getExcludeDanmaku(), sid, ssid, data);
                    }, delay, TimeUnit.SECONDS);
                    delay += broConfig.getInterval();
                }
                break;
            //房间
            case 6:
                for (Rank rank : ranks) {
                    int roomId = Convert.toInt(rank.getMember());
                    RoomInfo roomInfo = commonService.getRoomInfoByRoomId(roomId);
                    if (roomInfo == null || roomInfo.getTitle() == null) {
                        log.error("getRoomInfoByRoomId is null, will not bro match result pop, familyId:{}", roomId);
                        continue;
                    }
                    //todo 添加默认头像
                    if (roomInfo.getCover() == null) {
                        roomInfo.setCover("");
                    }
                    ImmutableMap<String, Object> data = ImmutableMap.of("rank", rank.getRank() + broConfig.getRankOffset(), "roomId", roomId, "nick",
                            roomInfo.getTitle(), "logo", roomInfo.getCover());
                    broBanner(seq, roomId, attr.getBusiId(), attr.getActId(), ACT_TOP_N, broConfig.getRoleType(),
                            broConfig.getBannerType(), broConfig.getBroadcastType(), roomId, 0, data, broConfig.getExcludeDanmaku());
                }
                break;
            //家族
            case 7:
                for (Rank rank : ranks) {
                    long familyId = Convert.toLong(rank.getMember());
                    FamilyBasicInfo info = commonService.getFamilyBasicInfo(familyId);
                    if (info == null || info.getFamilyName() == null) {
                        log.error("getFamilyBasicInfo is null, will not bro match result pop, familyId:{}", familyId);
                        continue;
                    }
                    //todo 添加默认头像
                    if (info.getCover() == null) {
                        info.setCover("");
                    }
                    ImmutableMap<String, Object> data = ImmutableMap.of("rank", rank.getRank() + broConfig.getRankOffset(), "familyId", familyId, "nick",
                            info.getFamilyName(), "logo", info.getCover());
                    broBanner(seq, familyId, attr.getBusiId(), attr.getActId(), ACT_TOP_N, broConfig.getRoleType(),
                            broConfig.getBannerType(), broConfig.getBroadcastType(), familyId, 0, data, broConfig.getExcludeDanmaku());
                }
                break;
            default:
                break;
        }
    }

    private String getHallName(String member,String nick,Map<String, FtsRoomMgrInfoVo> roomMgrInfoMap) {
        if(roomMgrInfoMap!=null && roomMgrInfoMap.containsKey(member) && StringUtils.isNotEmpty(roomMgrInfoMap.get(member).getName())){
            return roomMgrInfoMap.get(member).getName();
        }else{
            return nick;
        }
    }


    /**
     * 1 交友 2 约战 3 宝贝 4 打通房 101 派单房 201 游戏房 301 语音房 401 YY开黑房 501 技能卡房
     **/
    private int changeToBusinessType(int busiId) {
        if (BusiId.MAKE_FRIEND.getValue() == busiId) {
            return 1;
        }
        if (BusiId.GAME_BABY.getValue() == busiId) {
            return 3;
        }
        if (BusiId.SKILL_CARD.getValue() == busiId) {
            return 501;
        }

        return 0;
    }

    private String getChannelLogo(String roomMgrPic, String recommendPic, String channelPic) {
        if (StringUtil.isNotBlank(roomMgrPic)) {
            return roomMgrPic;
        }

        if (StringUtil.isNotBlank(recommendPic)) {
            return recommendPic;
        }

        if (StringUtil.isNotBlank(channelPic)) {
            return channelPic;
        }

        return "";
    }

    private void broCPBanner(String seq, MatchResultOnlineBroComponentAttr attr, MatchResultOnlineBroComponentAttr.BroConfig broConfig,
                             long userId, long anchorUid, Object data) {
        int roleType = broConfig.getRoleType();
        int broadcastType = broConfig.getBroadcastType();
        int bannerType = broConfig.getBannerType();
        UserCurrentChannel userCurrentChannel = commonService.getNoCacheUserCurrentChannel(userId, 2);
        UserCurrentChannel anchorCurrentChannel = commonService.getNoCacheUserCurrentChannel(anchorUid, 2);
        if (userCurrentChannel != null && anchorCurrentChannel != null && userCurrentChannel.getSubsid() == anchorCurrentChannel.getSubsid()) {
            broBanner(seq, userId, attr.getBusiId(), attr.getActId(), ACT_TOP_N, roleType, bannerType, broadcastType,
                    userCurrentChannel.getTopsid(), userCurrentChannel.getSubsid(), data, broConfig.getExcludeDanmaku());
            return;
        }
        broCP(seq, attr, userCurrentChannel, broConfig, userId, data);
        broCP(seq, attr, anchorCurrentChannel, broConfig, anchorUid, data);
        if (userCurrentChannel == null || anchorCurrentChannel == null) {
            actRedisDao.lpush(getRedisGroupCode(attr.getActId()), makeKey(attr, NEED_BRO_CP_RECORD), JSON.toJSONString(data));
        }
    }

    private void broCP(String seq, MatchResultOnlineBroComponentAttr attr, UserCurrentChannel currentChannel,
                       MatchResultOnlineBroComponentAttr.BroConfig broConfig, long uid, Object data) {
        if (currentChannel == null) {
            String key = makeKey(attr, NEED_BRO_UID);
            String redisGroupCode = getRedisGroupCode(attr.getActId());
            actRedisDao.sAdd(redisGroupCode, key, String.valueOf(uid));
            log.info("broCP user not in channel,seq:{} uid:{}, data:{}", seq, uid, JSON.toJSONString(data));
            return;
        }
        broBanner(seq, uid, attr.getBusiId(), attr.getActId(), ACT_TOP_N, broConfig.getRoleType(), broConfig.getBannerType(),
                broConfig.getBroadcastType(), currentChannel.getTopsid(), currentChannel.getSubsid(), data, broConfig.getExcludeDanmaku());
    }

    private void broUser(String seq, MatchResultOnlineBroComponentAttr attr, long uid, Map<String, Object> data,
                         MatchResultOnlineBroComponentAttr.BroConfig broConfig) {
        int bannerType = broConfig.getBannerType();
        int roleType = broConfig.getRoleType();
        int broadcastType = broConfig.getBroadcastType();
        String redisGroupCode = getRedisGroupCode(attr.getActId());

        long sid = 0;
        long ssid = 0;
        // 只有子频道和顶级频道才需要用户在房信息
        if (broadcastType == BroadcastConfig.BroType.SUB_CHANNEL.code || broadcastType == BroadcastConfig.BroType.TOP_CHANNEL.code) {
            UserCurrentChannel currentChannel = commonService.getNoCacheUserCurrentChannel(uid, 2);
            if (currentChannel == null) {
                if (attr.getDelaySecond() <= 0) {
                    // 延迟时间小于等于0,表示不延迟
                    return;
                }
                Map<String, Object> info = Maps.newHashMap();
                info.putAll(data);
                info.put("bannerType", bannerType);
                info.put("roleType", roleType);
                info.put("broadcastType", broadcastType);
                info.put("excludeDanmaku", broConfig.getExcludeDanmaku());
                String key = makeKey(attr, NEED_BRO_UID);

                actRedisDao.sAdd(redisGroupCode, key, String.valueOf(uid));
                actRedisDao.lpush(redisGroupCode, makeKey(attr, NEED_BRO_USER_RECORD), JSON.toJSONString(info));
                //添加过期时间
                Date now = commonService.getNow(attr.getActId());
                long expire = attr.getDelaySecond() + DateUtil.getSeconds(now);
                actRedisDao.set(redisGroupCode, makeKey(attr, EXPIRE_TIME), expire + "");
                log.info("broAnchor user not in channel, uid:{}, data:{}", uid, JSON.toJSONString(info));
                return;
            }

            // 战队结果广播：多个主播处于同一个子频道,会造成多次弹窗
            sid = currentChannel.getTopsid();
            ssid = currentChannel.getSubsid();
            final int two = 2;
            if (roleType == two) {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
                int rank = jsonObject.getIntValue("rank");
                String value = sid + "_" + ssid + "_" + rank;
                String key = makeKey(attr, "team_result_bro");
                if (!actRedisDao.hsetnx(redisGroupCode, key, value, value)) {
                    log.info("had  team_result_bro broBanner,uid:{} sid={},ssid={},data={}", uid, sid, ssid, jsonObject.toJSONString());
                    return;
                }
            }
        }

        broBanner(seq, uid, attr.getBusiId(), attr.getActId(), ACT_TOP_N, roleType, bannerType, broadcastType,
                sid, ssid, data, broConfig.getExcludeDanmaku());

    }

    /**
     * 战队结果广播
     **/
    private void broTeamResult(String seq, List<Rank> ranks, MatchResultOnlineBroComponentAttr attr, MatchResultOnlineBroComponentAttr.BroConfig broConfig) {
        try {
            for (Rank rank : ranks) {
                TeamDTO teamDTO = anchorTeamComponent.queryTeam(attr.getActId(), broConfig.getTeamCmptUseIndex(), rank.getMember());
                if (teamDTO == null) {
                    log.error("not found team info {}", rank.getMember());
                    continue;
                }
                List<Long> memberIds = anchorTeamComponent.getMemberIds(teamDTO.getMemberIdList());
                Map<Long, UserInfo> userInfoMap = webdbThriftClient.batchGetUserInfo2(memberIds);
                for (Long memberId : memberIds) {
                    ImmutableMap<String, Object> data = ImmutableMap.of(
                            "rank", rank.getRank() + broConfig.getRankOffset(),
                            "uid", memberId,
                            "teamName", Base64.encodeBase64String(teamDTO.getTeamName().getBytes()),
                            "teamMembers", userInfoMap.values());
                    broUser(seq, attr, memberId, data, broConfig);
                }
            }
        } catch (Exception ex) {
            log.error("broTeamResult error,seq={}", seq, ex);
        }
    }

    @Scheduled(cron = "*/3 * * * * ? ")
    @NeedRecycle(author = "yulianzhu", notRecycle = true)
    public void needBro() {
        String timerName = "locker_broadcast_online_" + this.getComponentId();
        timerSupport.work(timerName, 180, () -> {
            Set<Long> activityIds = this.getComponentEffectActIds();
            if (CollectionUtils.isEmpty(activityIds)) {
                return;
            }

            activityIds.stream()
                    //.filter(actId -> actInfoService.inActTime(actId))
                    .map(this::getAllComponentAttrs)
                    .flatMap(Collection::stream)
                    .forEach(this::broOnlineBanner);
        });
        //log.info("needBro Scheduled done@act size:{}", activityIds.size());
    }

    private void broOnlineBanner(MatchResultOnlineBroComponentAttr attr) {
        long actId = attr.getActId();
        Date now = commonService.getNow(actId);
        long nowSeconds = DateUtil.getSeconds(now);
        String groupCode = getRedisGroupCode(actId);
        String expireTime = makeKey(attr, EXPIRE_TIME);
        long expire = Convert.toLong(actRedisDao.get(groupCode, expireTime));
        //往前调时间时不弹窗, 容差10s
        final boolean match = nowSeconds < (expire - 3610) || nowSeconds > expire;
        if (match) {
            return;
        }

        String needBroUidKey = makeKey(attr, NEED_BRO_UID);

        Set<String> members = actRedisDao.sMembers(groupCode, needBroUidKey);
        if (CollectionUtils.isEmpty(members)) {
            return;
        }
        for (String member : members) {
            long uid = Convert.toLong(member);
            UserCurrentChannel currentChannel = commonService.getNoCacheUserCurrentChannel(uid);
            if (currentChannel == null) {
                continue;
            }
            Long busiId = webdbThriftClient.getBusiId(currentChannel.getTopsid(), currentChannel.getSubsid());
            if (busiId.intValue() != attr.getBusiId()) {
                continue;
            }

            //等待16秒,让模板加载完
            SysEvHelper.waiting(16000);
            exeBro(attr, actId, groupCode, needBroUidKey, uid, currentChannel);
        }
    }

    private void exeBro(MatchResultOnlineBroComponentAttr attr, long actId, String redisGroupCode, String needBroUidKey,
                        long uid, UserCurrentChannel currentChannel) {
        List<String> lrange = actRedisDao.lrange(redisGroupCode, makeKey(attr, NEED_BRO_USER_RECORD), 0, -1);

        for (String record : lrange) {
            JSONObject data = JSONObject.parseObject(record);
            Long member = data.getLong("uid");
            int bannerType = data.getIntValue("bannerType");
            int roleType = data.getIntValue("roleType");
            int broadcastType = data.getIntValue("broadcastType");
            int excludeDanmaku = data.getIntValue("excludeDanmaku");
            if ((member != null && member == uid)) {
                broBanner("extBro", uid, attr.getBusiId(), attr.getActId(), ACT_TOP_N, roleType, bannerType,
                        broadcastType, currentChannel.getTopsid(), currentChannel.getSubsid(), data, excludeDanmaku);
            }
        }

        List<String> cpRecord = actRedisDao.lrange(redisGroupCode, makeKey(attr, NEED_BRO_CP_RECORD), 0, -1);
        for (String record : cpRecord) {
            JSONObject data = JSONObject.parseObject(record);
            long playerUid = Convert.toLong(data.get("playerUid"));
            long anchorUid = Convert.toLong(data.get("anchorUid"));
            int bannerType = data.getIntValue("bannerType");
            int roleType = data.getIntValue("roleType");
            int broadcastType = data.getIntValue("broadcastType");
            int excludeDanmaku = data.getIntValue("excludeDanmaku");
            if (playerUid == uid || anchorUid == uid) {
                broBanner("extBro", uid, attr.getBusiId(), attr.getActId(), ACT_TOP_N, roleType, bannerType,
                        broadcastType, currentChannel.getTopsid(), currentChannel.getSubsid(), data, excludeDanmaku);
            }
        }
        actRedisDao.sRem(getRedisGroupCode(actId), needBroUidKey, String.valueOf(uid));
        log.info("extBro user bro banner done: uid:{}", uid);
    }

    private void broBanner(String seq, long uid, int busiId, long actId, long bannerId, int roleType, int bannerType,
                           int broadcastType, long sid, long ssid, Object data, int excludeDanmaku) {
        broBanner(seq, uid, busiId, actId, bannerId, roleType, bannerType, broadcastType, excludeDanmaku, sid, ssid, data);
    }

    /**
     * 任务达成广播
     */
    private void broBanner(String seq, long uid, int busiId, long actId, long bannerId, int roleType, int bannerType,
                           int broadcastType, int excludeDanmaku, long sid, long ssid, Object data) {
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setJsonData(JSON.toJSONString(data)).setBannerId(bannerId).setBannerType(bannerType)
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        if (busiId == BusiId.MAKE_FRIEND.getValue() && excludeDanmaku == 1) {
            broadCastHelpService.broadcastExcludeDanmakuChannel(bannerBroMsg);
        } else {
            broadCastHelpService.broadcast(actId, BusiId.findByValue(busiId), broadcastType, sid, ssid, bannerBroMsg);
        }

        log.info("broBanner done uid:{} seq:{} bannerId:{}, bannerType:{},broadcastType:{} sid:{}, ssid:{} data:{}", uid, seq, bannerId, bannerType, broadcastType, sid, ssid, JSON.toJSONString(data));
    }
}
