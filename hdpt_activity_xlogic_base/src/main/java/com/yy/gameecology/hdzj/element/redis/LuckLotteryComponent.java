package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.SendGiftComboEvent;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.common.Code;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.JsonUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.LuckLotteryComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.RoleType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;

@UseRedisStore
@Slf4j
@RestController
@RequestMapping("/luck_lottery")
public class LuckLotteryComponent extends BaseActComponent<LuckLotteryComponentAttr> {

    /**
     * 神豪总幸运之钥数 hash
     */
    public static final String PLAYER_TOTAL_LUCK_KEY = "player_total_luck_key";

    /**
     * 神豪已使用的幸运之钥 hash
     */
    public static final String PLAYER_USED_LUCK_KEY = "player_used_luck_key";

    /**
     * 神豪累计幸运点数(中大奖之后清空)
     */
    public static final String PLAYER_TOTAL_LUCK_POINT_NO_AWARD = "player_total_luck_key_no_award";

    /**
     * 神豪抽奖记录 player_lottery_award:uid {item:count}
     */
    public static final String PLAYER_LOTTERY_AWARD = "player_lottery_award:%s";

    public static final String LUCK_LOTTERY_LOCK = "luck_lottery_lock:%s";


    @Override
    public Long getComponentId() {
        return ComponentId.LUCK_LOTTERY;
    }

    @HdzjEventHandler(value = SendGiftComboEvent.class, canRetry = false)
    public void onSendGiftComboEvent(SendGiftComboEvent sendGiftComboEvent, LuckLotteryComponentAttr attr) {
        log.info("onSendGiftComboEvent with event:{}", sendGiftComboEvent);

        //不在活动时间范围内
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            return;
        }

        if(sendGiftComboEvent.getTemplate().getValue() != Template.ZhuiWan.getValue()
                && sendGiftComboEvent.getTemplate().getValue() != Template.SkillCard.getValue()) {
            return;
        }

        Long sendUid = sendGiftComboEvent.getSendUid();
        Long giftNum = sendGiftComboEvent.getGiftNum();
        LocalDateTime localDateTime = commonService.getNowDateTime(actId);
        String day = localDateTime.format(DateUtil.YYYY_MM_DD);
        Long anchorUid = sendGiftComboEvent.getRecvUid();
        String playerUid = String.valueOf(sendUid);
        String giftId = sendGiftComboEvent.getGiftId();

        //非活动礼物
        if (!attr.getGiftNum2LuckyKeyNum().containsKey(giftId)) {
            return;
        }

        if (!commonService.checkWhiteList(actId, RoleType.ANCHOR, anchorUid + "")) {
            log.warn("not in white list,return,trans:{}", JSON.toJSONString(sendGiftComboEvent));
            return;
        }
        if (!commonService.checkWhiteList(actId, RoleType.USER, playerUid + "")) {
            log.warn("not in white list,return,trans:{}", JSON.toJSONString(sendGiftComboEvent));
            return;
        }

        String groupCode = redisConfigManager.getGroupCode(actId);
        String base = attr.getGiftNum2LuckyKeyNum().get(giftId);
        long luckKey = (long) (giftNum*sendGiftComboEvent.getComboHits() / Double.parseDouble(base));
        if (luckKey > 0) {
            String key = makeKey(attr, PLAYER_TOTAL_LUCK_KEY);
            actRedisDao.hIncrByKey(groupCode, key, playerUid, luckKey);
            String text = "获得祈愿点，去抽大奖>>";
            bro(attr, sendUid, sendGiftComboEvent.getSeq(), text);
        }
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = false)
    public void onSendGiftEvent(SendGiftEvent sendGiftEvent, LuckLotteryComponentAttr attr) {
        log.info("onSendGiftEvent with event:{}", sendGiftEvent);
        //不在活动时间范围内
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            return;
        }

        List<Long> recvUids = new ArrayList<>();
        //追玩，技能卡事件需要特殊处理(这里要区分全麦打赏)
        if(sendGiftEvent.getTemplate().getValue() == Template.ZhuiWan.getValue()
                || sendGiftEvent.getTemplate().getValue() == Template.SkillCard.getValue()) {
            JSONObject expandJo = sendGiftEvent.getJsonMap();
            if(!expandJo.containsKey("isMultiple") || !expandJo.containsKey("receivers") || expandJo.getJSONArray("receivers").size() <= 1){
                log.info("callback handleGiftKafkaEvent is not multiple cant process");
                return;
            }
            String key = makeKey(attr, LUCK_LOTTERY_LOCK);
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String turnoverIdStr = expandJo.getString("turnoverOrderId");
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(turnoverIdStr), DateUtil.getNowYyyyMMddHHmmss());
            if (!first) {
                return;
            }
            JSONArray jsonArray = expandJo.getJSONArray("receivers");
            for (Object o : jsonArray) {
                JSONObject jo = (JSONObject) o;
                recvUids.add(((JSONObject) o).getLong("targetUid"));
            }
        }


        Long sendUid = sendGiftEvent.getSendUid();
        Long giftNum = sendGiftEvent.getGiftNum();
        LocalDateTime localDateTime = commonService.getNowDateTime(actId);
        String day = localDateTime.format(DateUtil.YYYY_MM_DD);
        Long anchorUid = sendGiftEvent.getRecvUid();
        String playerUid = String.valueOf(sendUid);
        String giftId = sendGiftEvent.getGiftId();
        if(recvUids.isEmpty()) {
            recvUids.add(anchorUid);
        }
        //非宝贝的送礼行为
        if (anchorUid.equals(sendUid)) {
            return;
        }

        //非活动礼物
        if (!attr.getGiftNum2LuckyKeyNum().containsKey(giftId)) {
            return;
        }
        for (Long recvUid : recvUids) {
            if (!commonService.checkWhiteList(actId, RoleType.ANCHOR, recvUid + "")) {
                log.warn("not in white list,return,trans:{}", JSON.toJSONString(sendGiftEvent));
                return;
            }
        }
        if (!commonService.checkWhiteList(actId, RoleType.USER, playerUid + "")) {
            log.warn("not in white list,return,trans:{}", JSON.toJSONString(sendGiftEvent));
            return;
        }

        String groupCode = redisConfigManager.getGroupCode(actId);
        String base = attr.getGiftNum2LuckyKeyNum().get(giftId);
        long luckKey = (long) (giftNum * recvUids.size()/ Double.parseDouble(base));
        if (luckKey > 0) {
            String key = makeKey(attr, PLAYER_TOTAL_LUCK_KEY);
            actRedisDao.hIncrByKey(groupCode, key, playerUid, luckKey);
            String text = "获得"+luckKey+"祈愿点，去祈愿可获奖励";
            bro(attr, sendUid, sendGiftEvent.getSeq(), text);
        }
    }

    public long playerLuckKey(long yyUid, long actId, long cmptIndex) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        long total = Convert.toLong(actRedisDao.hget(groupCode, makeKey(actId, cmptIndex, PLAYER_TOTAL_LUCK_KEY), String.valueOf(yyUid)));
        long used = Convert.toLong(actRedisDao.hget(groupCode, makeKey(actId, cmptIndex, PLAYER_USED_LUCK_KEY), String.valueOf(yyUid)));
        return total - used;
    }

    @RequestMapping("/playerLuckKey")
    public Response playerLuckKey(HttpServletRequest request, HttpServletResponse response, long cmptIndex) {
        if (isClosed(request)) {
            return Response.fail(-99, MSG_CLOSE_HINT);
        }
        long yyUid = getLoginYYUid(request, response);
        log.info("playerLuckKey uid:{}", yyUid);
        if (yyUid == 0) {
            return Response.fail(-1, "请登录后操作");
        }
        long actId = Convert.toLong(request.getParameter("actId"));
        return Response.success(playerLuckKey(yyUid, actId, cmptIndex));
    }

    @RequestMapping("/playerLuckPoint")
    public Response playerLuckPoint(HttpServletRequest request, HttpServletResponse response, long cmptIndex) {
        if (isClosed(request)) {
            return Response.fail(-99, MSG_CLOSE_HINT);
        }
        long yyUid = getLoginYYUid(request, response);
        log.info("playerLuckPoint uid:{}", yyUid);
        if (yyUid == 0) {
            return Response.fail(-1, "请登录后操作");
        }
        long actId = Convert.toLong(request.getParameter("actId"));
        String groupCode = redisConfigManager.getGroupCode(actId);
        long total = Convert.toLong(actRedisDao.hget(groupCode, makeKey(actId, cmptIndex, PLAYER_TOTAL_LUCK_POINT_NO_AWARD), String.valueOf(yyUid)));
        return Response.success(total);
    }

    public long getLuckPoint(long uid, long actId, long cmptIndex) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        return Convert.toLong(actRedisDao.hget(groupCode, makeKey(actId, cmptIndex, PLAYER_TOTAL_LUCK_POINT_NO_AWARD), String.valueOf(uid)));
    }
    public void clearLuckPoint(long uid, long actId, long cmptIndex) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        actRedisDao.hdel(groupCode, makeKey(actId, cmptIndex, PLAYER_TOTAL_LUCK_POINT_NO_AWARD), String.valueOf(uid));
    }


    @RequestMapping("/lottery")
    public Response lottery(HttpServletRequest req, HttpServletResponse resp,long actId,
                            int boxType, int lotteryType, long cmptIndex) {
        if (isClosed(req)) {
            return Response.fail(-99, MSG_CLOSE_HINT);
        }
        int status = actInfoService.actTimeStatus(actId);
        if (status > 0) {
            return Response.fail(3, "活动已结束!");
        }
        if (status < 0) {
            return Response.fail(3, "活动未开始!");
        }
        try {
            long yyUid = getLoginYYUid(req, resp);
            log.info("lottery uid:{}", yyUid);
            LuckLotteryComponentAttr attr = getComponentAttr(actId, cmptIndex);
            if(!attr.isCanLottery()){
                return Response.fail(3, "参数错误");
            }
            BatchLotteryResult lottery = lottery(yyUid, boxType, lotteryType, attr);
            //null-> 抽奖机会不足
            if (lottery == null) {
                if(StringUtil.isEmpty(attr.getErrorMsg())) {
                    return Response.fail(2, "幸运之钥不足");
                } else {
                    return Response.fail(2, attr.getErrorMsg());
                }

            }
            return lotteryAward(lottery, attr);
        } catch (Exception e) {
            log.error("lottery exception@uid:{}, err:{} " , e.getMessage() , ExceptionUtils.getStackTrace(e));
            return new Response<>(Code.E_SYS_BUSY);
        }

    }

    @RequestMapping("/playerAward")
    public Response playerAward(HttpServletRequest req, HttpServletResponse resp, long cmptIndex) {
        if (isClosed(req)) {
            return Response.fail(-99, MSG_CLOSE_HINT);
        }
        long yyUid = getLoginYYUid(req, resp);
        long actId = Convert.toLong(req.getParameter("actId"));
        LuckLotteryComponentAttr attr = getComponentAttr(actId, cmptIndex);
        return playerAward(yyUid, attr);
    }

    public Response playerAward(long yyUid, LuckLotteryComponentAttr attr) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        List<String> awardList = new ArrayList<>();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Object, Object> awards = new HashMap<>();
        awards = actRedisDao.hGetAll(groupCode, makeKey(attr, String.format(PLAYER_LOTTERY_AWARD, yyUid)));
        for (Map.Entry<Object, Object> aw : awards.entrySet()) {
            long pid = Convert.toLong(aw.getKey());
            AwardModelInfo awardModelInfo = packageInfoMap.get(pid);
            if (awardModelInfo != null) {
                awardList.add(awardModelInfo.getPackageName() + "*" + Convert.toLong(aw.getValue()));
            }
        }
        return Response.success(awardList);
    }

    public Response lotteryAward(BatchLotteryResult batchLotteryResult, LuckLotteryComponentAttr attr) {
        if (batchLotteryResult.getCode() != 0) {
            return Response.fail(3, batchLotteryResult.getReason());
        }
        List<Award> awardList = Lists.newArrayList();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Long, Long> recordIds = batchLotteryResult.getRecordPackages();
        Map<Long, Integer> pidCount = Maps.newHashMap();
        for (Long pid : recordIds.values()) {
            pidCount.merge(pid, 1, Integer::sum);
        }

        for (Map.Entry<Long, Integer> entry : pidCount.entrySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(entry.getKey());
            if (awardModelInfo != null) {
                if(awardModelInfo.getPackageName().contains("谢谢参与")) {
                    continue;
                }
                Award award = new Award();
                award.setName(awardModelInfo.getPackageName());
                award.setImg(awardModelInfo.getPackageImage());
                award.setNum(entry.getValue());
                awardList.add(award);
            }
        }
        return Response.success(awardList);
    }

    public Map<Long, AwardModelInfo> packageInfoMap(LuckLotteryComponentAttr attr) {
        try {
            Map<Long, AwardModelInfo> gold = hdztAwardServiceClient.queryAwardTasks(attr.getGoldTaskId());
            Map<Long, AwardModelInfo> silver = hdztAwardServiceClient.queryAwardTasks(attr.getSilverTaskId());

            Map<Long, AwardModelInfo> result = new HashMap<>(16);
            if (MapUtils.isNotEmpty(gold)) {
                result.putAll(gold);
            }

            if (MapUtils.isNotEmpty(silver)) {
                result.putAll(silver);
            }

            return result;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks fail", e);
        }
        return Collections.emptyMap();
    }

    /**
     * 神豪抽奖
     *
     * @param boxType     金银魔盒  1->金 2->银
     * @param lotteryType 抽奖方式 1-> 单次抽 2->一键抽完
     * @return null 幸运之钥不足
     */
    public BatchLotteryResult lottery(long uid, int boxType, int lotteryType, LuckLotteryComponentAttr attr) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String puid = String.valueOf(uid);
        long actId = attr.getActId();
        int totalKey = Convert.toInt(actRedisDao.hget(groupCode, makeKey(attr, PLAYER_TOTAL_LUCK_KEY), puid));
        int used = Convert.toInt(actRedisDao.hget(groupCode, makeKey(attr, PLAYER_USED_LUCK_KEY), puid));
        int left = totalKey - used;
        if (left < 1) {
            return null;
        }
        long taskId = boxType == 1 ? attr.getGoldTaskId() : attr.getSilverTaskId();
        LocalDateTime nowDateTime = commonService.getNowDateTime(actId);
        String nowStr = nowDateTime.format(DateUtil.YYYY_MM_DD_HH_MM_SS);
        int count = count(boxType, lotteryType, left);
        if (count < 1) {
            return null;
        }
        String seq = UUID.randomUUID().toString();
        log.info("player lottery,uid:{} taskId:{} count:{} total:{} used:{}, seq:{}, boxType:{},lotteryType:{},nowstr:{}",
                puid, taskId, count, totalKey, used, seq, boxType, lotteryType, nowStr);
        String point = makeKey(attr, PLAYER_TOTAL_LUCK_POINT_NO_AWARD);
        if (boxType == 1) {
            actRedisDao.hIncrByKey(groupCode, makeKey(attr, PLAYER_USED_LUCK_KEY), puid, count * 10);
            actRedisDao.hIncrByKey(groupCode, point, puid, count * 10);
        } else {
            actRedisDao.hIncrByKey(groupCode, makeKey(attr, PLAYER_USED_LUCK_KEY), puid, count);
            actRedisDao.hIncrByKey(groupCode, point, puid, count);
        }
        BatchLotteryResult result = hdztAwardServiceClient.doLottery(nowStr, BusiId.GAME_ECOLOGY.getValue(), uid, taskId, count, 0, seq);
        log.info("uid:{} lottery result:{}", uid, JsonUtil.toJson(result));
        //记录中奖记录
        if (result.getCode() == 0) {
            Map<Long, Long> recordIds = result.getRecordPackages();
            for (Map.Entry<Long, Long> entry : recordIds.entrySet()) {
                String packageId = String.valueOf(entry.getValue());
                actRedisDao.hIncrByKey(groupCode, makeKey(attr, String.format(PLAYER_LOTTERY_AWARD, puid)), packageId, 1);
                bigDataService.saveNoRankDataToFile(actId, BusiId.GAME_BABY, System.currentTimeMillis(), puid, RoleType.USER, 1
                        , 5040, "player_award:" + packageId, 0, 0);
            }
        }
        return result;
    }

    private int count(int boxType, int lottery, int left) {
        final int ten = 10;
        if (boxType == 1 && left < ten) {
            return -1;
        }
        if (boxType == 1) {
            if (lottery == 1) {
                return 1;
            }
            return left / 10;
        } else {
            if (lottery == 1) {
                return 1;
            }
            return left;
        }
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void onTaskCompleteEvent(TaskProgressChanged event, LuckLotteryComponentAttr attr) {
        if (attr.getRankIds().contains(event.getRankId())) {
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            final long finishIndexCount = event.getCurrTaskIndex() - event.getStartTaskIndex();
            if (finishIndexCount > 0) {
                actRedisDao.hIncrByKey(groupCode, makeKey(attr, PLAYER_TOTAL_LUCK_KEY), event.getMember(), finishIndexCount);
            }
        }
    }

    private void bro(LuckLotteryComponentAttr attr, long uid, String seq, String text) {
        UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, false);
        JSONObject json = new JSONObject(6);
        json.put("text", text);
        json.put("jumpUrl", attr.getBroJumpUrl());
        json.put("avatar", userBaseInfo.getHdLogo());

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType("cp_lottery")
                .setExtJson(json.toJSONString());

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        RetryTool.withRetryCheck(attr.getActId(), seq, () -> {
            svcSDKService.unicastUid(uid, msg);
        });
    }

    @Data
    public static class Award {
        private String name;

        private String img;

        private int num;
    }
}
