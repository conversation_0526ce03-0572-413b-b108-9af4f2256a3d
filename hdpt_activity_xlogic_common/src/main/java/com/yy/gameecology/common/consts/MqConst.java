package com.yy.gameecology.common.consts;


/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-12-29 15:49
 **/
public class MqConst {


    public static final int CONSUME_TYPE_RABBIT = 1;

    public static final int CONSUME_TYPE_KAFKA = 2;


    //#########################################kafka topic start ############################################################################

    /**
     * 预发布环境占位用
     */
    public static final String GAMEBABY_NONE = "gamebaby_none";

    /**
     * 模板道具支付成功
     */
    public static final String MBDJ_PAYOK_TOPIC = "gamebaby_mbdj_payok_topic";

    /**
     * 活动道具送礼完成MQ发送
     */
    public static final String HDDJ_PAYOK_TOPIC = "gamebaby_hddj_payok_topic";


    /**
     * 红心送礼完成MQ发送
     */
    public static final String REDHEART_PAYOK_TOPIC = "gamebaby_redheart_payok_topic";


    /**
     * 土豪进入频道通知
     */
    public static final String RICHER_ACCESS_NOTICE_TOPIC = "gamebaby_richer_access_notice_topic";


    /**
     * 宝贝上麦通知
     */
    public static final String BABY_ONMIC_NOTICE_TOPIC = "gamebaby_baby_onmic_notice_topic";

    /**
     * pk结束
     */
    public static final String BABY_PK_END_TOPIC = "gamebaby_baby_pk_end_topic";


    /**
     * 宝贝连送结束事件
     */
    public static final String BABY_COMBO_END_TOPIC = "baby_combo_end";



    /**
     * 活动平台预发布环境占位用
     */
    public static final String HDPT_NONE = "hdpt_none";

    /**
     * 交友开播变更
     */
    public static final String JY_ANCHOR_SHOW_CHANGE_TOPIC = "jy_anchor_show_change_topic";

    /**
     * 约战开播变更
     */
    public static final String YUEZHAN_ANCHOR_SHOW_CHANGE_TOPIC = "yuezhan_anchor_show_change_topic";

    /**
     * 陪玩订单完成
     */
    public static final String PW_ORDER_COMPLETE_TOPIC = "pw_order_complete_topic";

    /**
     * 交友连送结束topic
     */
    public static final String JY_COMBO_END_TOPIC = "propsComboEndCross";
    public static final String JY_FIGHT_END_TOPIC = "appChannelFightEndInfoCross";

    public static final String JY_PROPS_TOPIC = "propsUseInfoCross";
    public static final String JY_PUSH_LIVE_TOPIC = "msgPushCompereLiveNotifyCross";
    public static final String JY_SEAL_TOPIC = "propsSealInfoCross";
    public static final String JY_COMPERE_ONLINE = "appEventCompereOnlineCross";

    /**
     * 交友充值事件
     */
    public static final String JY_RECHARGE_TOPIC = "revenueChargeInfoCross";

    public static final String COMMON_CHARGE_ORDER_DATING = "common_charge_order_Dating";

    public static final String TURNOVER_COMMON_PLANE_TOPIC = "propsCommonPlaneActivity";

    public static final String PAY_ORDER_RESULT_BABY = "pay_order_result_baby";
    public static final String PAY_ORDER_RESULT_DATING = "pay_order_result_dating";
    public static final String PAY_ORDER_RESULT_VIPPK = "pay_order_result_vippk";

    /**
     * 交友更新挂件
     */
    public static final String PENDANT_LIST_UPDATE_EVENT = "pendantListUpdateEvent";

    /**
     * 移动端横幅推送
     * http://jywiki.yy.com/docs/share/cross_event_push#f0bdbu
     */
    public static final String APP_ACT_BANNER_EVENT = "activityBannerEvent";

    /**
     * 追玩APP通用弹窗
     */
    public static final String ZHUIWAN_APP_POP_UP = "zhuiwan_common_pop_up_topic";

    public static final String ZHUIWAN_ACT_RISK_RE_CHECK_TOPIC = "zhuiwan_act_risk_re_check_topic";

    //#########################################kafka topic end ############################################################################


    //core 进程
    public static final String CORE_PAYOK_PUSH_TO_FTS_QUEUE = "payok.push.to.fts.queue";

    //act 进程
    public static final String ACT_BABY_ONMIC_NOTICE_QUEUE = "baby.onmic.notice.queue";

    public static final String ACT_HDDJ_PAYOK_ACTIVITY_QUEUE = "hddj.payok.activity.queue";

    public static final String ACT_MBDJ_PAYOK_ACTIVITY_QUEUE = "mbdj.payok.activity.queue";

    public static final String ACT_REDHEART_PAYOK_ACTIVITY_QUEUE = "redheart.payok.activity.queue";

    public static final String ACT_USER_ACCESS_QUEUE = "user.access.queue";

    public static final String ACT_BABY_PK_END_QUEUE = "baby.pk.end.queue";

    public static final String MBDJ_PAYOK_G_YY_COM_QUEUE = "mbdj.payok.g.yy.com.queue";

    /**
     * 公屏聊天流水
     */
    public static final String SUB_CHANNEL_TEXT_CHAT = "sub_channelTextchat";

}
