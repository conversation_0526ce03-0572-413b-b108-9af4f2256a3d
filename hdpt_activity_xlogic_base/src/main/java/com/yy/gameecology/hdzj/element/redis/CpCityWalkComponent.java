package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.*;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.*;
import lombok.Data;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duowan.udb.util.GsonUtil;
import com.google.common.collect.Maps;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.NickExt;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.BestCp;
import com.yy.gameecology.hdzj.bean.CpMember;
import com.yy.gameecology.hdzj.bean.CpMemberInfo;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpCityWalkComponentAttr;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.broadcast.Template;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
@UseRedisStore
@RequestMapping("/5105")
@RestController
@Component
public class CpCityWalkComponent extends BaseActComponent<CpCityWalkComponentAttr> {

    private static final String CITY_SEND_GIFT_LOCK = "city_send_gift_lock";

    private static final String CITY_LIGHT_INCR = "city_light_incr";

    private static final String CITY_LIGHT_INFO = "city_light_info_%s_%s";

    private static final String CITY_LIGHT_SET = "city_light_set_%s";

    private static final String CP_LAST_CITY = "cp_last_city";

    private final String SEND_CHAT_LOTTERY_SEQ = "send_chat_lottery_%s";

    private final String TASK_COMPLETE_ZSET = "task_complete";

    private final String ENTER_ROOM_BRO_SEQ = "enter_room_%s";

    private static final String CP_ROOM_TOAST = "5105_cp_room_toast";

    private static final String CHANNEL_CHAT_AWARD = "5105_chat_award";

    //获奖toast 单播
    private static final String CP_LUCK_TOAST = "cp_city_award";

    //挂件单播 cp打卡
    private static final String CP_LIGHT_TOAST = "cp_city_award_layer";

    private final String JACK_POINT_AWARD_LIMIT = "jack_point_award_limit";

    private final String CP_LOTTERY_LOCK = "cp_lottery_lock";

    private static final String CITY_CP_SCORE = "city_cp_score";

    //最强cp广播
    private static final int BEST_CP_BANNER_ID = 51051;

    //最强cp 幸运cp广播
    private static final int BEST_CP_LAYER_BANNER_ID = 51052;

    private static final int BEST_TP = 1;

    private static final int LUCK_TP = 2;

    /**
     * 我的中奖记录
     */
    private final String MY_AWARD_LIST = "my_award_list:%s";

    /**
     * 全局中奖记录
     */
    private final String WHOLE_AWARD_LIST = "whole_award_list";

    private static final String AWARD_RECORD_CNT = "award_record_cnt:%s";

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private SignedService signedService;

    @Autowired
    private Locker locker;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Override
    public Long getComponentId() {
        return ComponentId.CP_CITY_WALK;
    }

    @NeedRecycle(author = "guanqhua", notRecycle = true)
    @Scheduled(cron = "0 0 * * * ? ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            Date now = commonService.getNow(actId);
            now = DateUtil.addMinutes(now, 10);
            ActivityInfoVo actInfo = hdztRankingThriftClient.queryActivityInfo(actId);
            if (!actInfoService.inActTime(now, actInfo)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }
            CpCityWalkComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            now = commonService.getNow(attr.getActId());
            Date noticeDate = DateUtil.getDayOf30MinuteInterval(DateUtil.addMinutes(now,-30));
            String timeCode = DateUtil.format(noticeDate,DateUtil.PATTERN_TYPE9);
            String groupCode = getRedisGroupCode(actId);
            String execKey = makeKey(attr, "execCpCityWalkStatic:"+timeCode);
            if (!actRedisDao.setNX(groupCode, execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}",execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId,now);
            doStaticReport(attr);
        }
    }

    public void doStaticReport(CpCityWalkComponentAttr attr) {
        StringBuilder content = new StringBuilder();
        content.append("### 最强CP累计发放礼物情况:\n");
        for (long packageId : attr.getPackageMap().keySet()) {
            if(attr.getPackageMap().get(packageId).getValue() > 0) {
                CpCityWalkComponentAttr.PackageInfo packageInfo = attr.getPackageMap().get(packageId);
                long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, String.format(AWARD_RECORD_CNT, packageInfo.getAwardName()+packageInfo.getAwardTimeUnit()))));
                content.append(packageInfo.getAwardName()).append(packageInfo.getAwardTimeUnit()).append(":").append(value).append("\n");
            }
        }
        String msg = buildActRuliuMsg(attr.getActId(), false, "【都市漫游】", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

        content = new StringBuilder();
        content.append("### 幸运CP累计发放道具情况:\n");
        for (long packageId : attr.getPackageMap().keySet()) {
            if(attr.getPackageMap().get(packageId).getValue() == 0) {
                CpCityWalkComponentAttr.PackageInfo packageInfo = attr.getPackageMap().get(packageId);
                long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, String.format(AWARD_RECORD_CNT, packageInfo.getAwardName()+packageInfo.getAwardTimeUnit()))));
                content.append(packageInfo.getAwardName()).append(packageInfo.getAwardTimeUnit()).append(":").append(value).append("\n");
            }
        }
        msg = buildActRuliuMsg(attr.getActId(), false, "【都市漫游】", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

        content = new StringBuilder();
        content.append("### 祝福口令累计发放道具情况\n");
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        for (long packageId : packageInfoMap.keySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(packageId);
            long value = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(AWARD_RECORD_CNT, awardModelInfo.getPackageName()+"_chat"))));
            content.append(awardModelInfo.getPackageName()).append(":").append(value).append("\n");
        }
        msg = buildActRuliuMsg(attr.getActId(), false, "【都市漫游】", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
    }

    @RequestMapping("/testBroBestCp")
    public Response testBroBestCp(long actId, long cmptInx, long uid, int tp, long sid, long ssid) {
        if (SysEvHelper.isDeploy()) {
            return Response.success(null);
        }
        WebdbUserInfo webdbUserInfo = webdbUinfoClient.getUserInfo(uid);
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        broBanner(attr, sid, ssid, uid, uid, System.currentTimeMillis()+"", webdbUserInfo, webdbUserInfo, "黑悟空", 1);
        broLayer(attr, sid, ssid, webdbUserInfo, webdbUserInfo, "https://gamebaby.bs2dl.yy.com/adminweb/xz2bf7c8a8wcmpj7t7szjpfhqakhcrxh.png", "1天", tp);
        return Response.success(null);
    }

    @RequestMapping("/testBroListCity")
    public Response testBroListCity(long actId, long cmptInx, long uid, String noticeType) {
        if (SysEvHelper.isDeploy()) {
            return Response.success(null);
        }
        WebdbUserInfo webdbUserInfo = webdbUinfoClient.getUserInfo(uid);
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        broUser(attr, actId, "黑悟空", "https://gamebaby.bs2dl.yy.com/adminweb/xz2bf7c8a8wcmpj7t7szjpfhqakhcrxh.png",
                webdbUserInfo, webdbUserInfo,
                noticeType, 1);
        return Response.success(null);
    }

    @RequestMapping("/testBroChat")
    public Response testBroCpPuzzle(long actId, long cmptInx, long uid, String sid, String ssid) {
        if (SysEvHelper.isDeploy()) {
            return Response.success(null);
        }
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        JSONObject extJson = new JSONObject();
        extJson.put("name", "黑悟空");
        extJson.put("icon", "https://gamebaby.bs2dl.yy.com/adminweb/xz2bf7c8a8wcmpj7t7szjpfhqakhcrxh.png");
        extJson.put("awardCount", 1);
        extJson.put("hit", true);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), CHANNEL_CHAT_AWARD, extJson.toJSONString()
                ,StringUtils.EMPTY , uid);

        JSONObject chatJson = new JSONObject();
        chatJson.put("text", "公屏发送“风景如诗，你自成画”有机会获得祝福奖励！");
        chatJson.put("sid", sid);
        chatJson.put("ssid", ssid);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), CP_ROOM_TOAST,
                JsonUtil.toJson(chatJson) , StringUtils.EMPTY, uid);
        return Response.success(null);
    }

    @RequestMapping("/getAwardPool")
    public Response getAwardPool(long actId, long cmptInx) {
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        if(attr == null) {
            return Response.fail(400, "参数错误");
        }
        long consume = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, JACK_POINT_AWARD_LIMIT)));
        if(attr.getJackPointLimit() - consume < 0) {
            return Response.success(0);
        } else {
            return Response.success(attr.getPriceLimit() - consume < 0
                    ? 0 : attr.getPriceLimit() - consume);
        }
    }

    /**
     * 我的获奖记录
     */
    @RequestMapping("/queryMyAwardRecord")
    public Response<AwardRecordRsp> queryMyAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                                       Long actId, long cmptInx) {
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            return Response.fail(400, "未登录");
        }
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }
        return Response.success(getAwardListByKey(actId, makeKey(attr, String.format(MY_AWARD_LIST, uid)), attr));
    }

    /**
     * 全服获奖记录
     */
    @RequestMapping("/queryAllAwardRecord")
    public Response<AwardRecordRsp> queryAllAwardRecord(HttpServletRequest request, HttpServletResponse response,
                                                        Long actId, long cmptInx) {
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未启用玩法");
        }
        return Response.success(getAwardListByKey(actId, makeKey(attr, WHOLE_AWARD_LIST), attr));
    }

    private AwardRecordRsp getAwardListByKey(long actId, String awardKey, CpCityWalkComponentAttr attr) {
        AwardRecordRsp rsp = new AwardRecordRsp();
        String redisGroup = getRedisGroupCode(actId);
        List<String> awardRecord = actRedisDao.lrange(redisGroup, awardKey, 0, 499);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            List<AwardRecord> list = Lists.newArrayListWithCapacity(awardRecord.size());
            List<Long> uids = new ArrayList<>();
            Set<Long> hset = new HashSet<>();
            for (String item : awardRecord) {
                AwardRecord record = JSON.parseObject(item, AwardRecord.class);
                if(!hset.contains(record.getUserUid())) {
                    uids.add(record.getUserUid());
                    hset.add(record.getUserUid());
                }
                if(!hset.contains(record.getBabyUid())) {
                    uids.add(record.getBabyUid());
                    hset.add(record.getBabyUid());
                }
                list.add(record);
            }
            Map<String, Map<String, MultiNickItem>> users = new HashMap<>();
            Map<String, WebdbUserInfo> userInfoMap = new HashMap<>();
            List<List<Long>> uidPageList = splitList(uids, 200);
            for (List<Long> subList : uidPageList) {
                BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(subList);
                if(batched != null) {
                    userInfoMap.putAll(batched.getUserInfoMap());
                    NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                    users.putAll(nickExt.getUsers());
                }
            }
            if(!users.isEmpty()) {
                if(attr.isNeedNickExt()) {
                    rsp.setNickExtUsers(users);
                }
                for (AwardRecord record : list) {
                    long babyUid = record.getBabyUid();
                    long uid = record.getUserUid();
                    record.setBabyNick(userInfoMap.get(babyUid+"").getNick());
                    record.setUserNick(userInfoMap.get(uid+"").getNick());
                }
            }
            rsp.setList(list);
        }
        return rsp;
    }

    public static <T> List<List<T>> splitList(List<T> list, int size) {
        List<List<T>> subLists = new ArrayList<>();
        int startIndex = 0;
        while (startIndex < list.size()) {
            int endIndex = Math.min(startIndex + size, list.size());
            subLists.add(new ArrayList<>(list.subList(startIndex, endIndex)));
            startIndex = endIndex;
        }
        return subLists;
    }

    @RequestMapping("/listCity")
    public Response listCity(long actId, long cmptInx, @RequestParam(required = false, defaultValue = "0") int r) {
        LightCityRsp rsp = new LightCityRsp();
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        int cnt = Convert.toInt(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, CITY_LIGHT_INCR)));
        int round = (int)cnt / attr.getTotalCity() + ((cnt % attr.getTotalCity()) > 0 ? 1: 0);
        rsp.setRound(round);
        round = r > 0 ? r : round;
        List<LightCity> lightCities = new ArrayList<>();
        List<Long> uids = new ArrayList<>();
        String preListCitySetKey = String.format(CITY_LIGHT_SET, round);
        List<Object> keys = new ArrayList<>();
        for (Integer i : attr.getCityMap().keySet()) {
            keys.add(Convert.toString(i));
        }
        List<Object> hmap = actRedisDao.hmGet(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, preListCitySetKey), keys);
        int cityNo = 1;
        for (Object o : hmap) {
            if(o != null) {
                LightCity lightCity = new LightCity();
                CpMemberInfo anchorMem = new CpMemberInfo();
                CpMemberInfo userMem = new CpMemberInfo();
                String cpMember = Convert.toString(o);
                String[]cpArr = cpMember.split("\\|");
                userMem.setUid(Convert.toLong(cpArr[0]));
                anchorMem.setUid(Convert.toLong(cpArr[1]));
                lightCity.setIndex(cityNo);
                lightCity.setAnchor(anchorMem);
                lightCity.setUser(userMem);
                lightCity.setCp(cpMember);
                lightCities.add(lightCity);
                uids.add(Convert.toLong(cpArr[0]));
                uids.add(Convert.toLong(cpArr[1]));
                ChannelInfoVo channelInfoVo = onlineChannelService.getChannelInfoVo(Convert.toLong(cpArr[1]));
                if(channelInfoVo != null) {
                    lightCity.setSid(channelInfoVo.getSid());
                    lightCity.setSsid(channelInfoVo.getSsid());
                }
            }
            cityNo++;
        }

        if(!uids.isEmpty()) {
            BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
            for (LightCity lightCity : lightCities) {
                CpMemberInfo memInfo = lightCity.getAnchor();
                WebdbUserInfo anchorUserInfo = batched.getUserInfoMap().get(Convert.toString(memInfo.getUid()));
                memInfo.setAvatar(WebdbUtils.getLogo(anchorUserInfo));
                memInfo.setName(anchorUserInfo.getNick());
                CpMemberInfo userMemInfo = lightCity.getUser();
                WebdbUserInfo userInfo = batched.getUserInfoMap().get(Convert.toString(userMemInfo.getUid()));
                userMemInfo.setAvatar(WebdbUtils.getLogo(userInfo));
                userMemInfo.setName(userInfo.getNick());
            }
            if(batched != null) {
                if(attr.isNeedNickExt()) {
                    NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                    rsp.setNickExtUsers(nickExt.getUsers());
                }
            }
        }
        rsp.setLightCities(lightCities);
        return Response.success(rsp);
    }

    @RequestMapping("/getCpList")
    public Response getCpList(HttpServletRequest request, HttpServletResponse response,
                              long actId, long cmptInx) {
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            CpListRsp rsp = new CpListRsp();
            return Response.success(rsp);
        }
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        // 用户是主播 -》 查cp
        List<String> uids = getRankMembers(actId, attr.getCpContributeRankId(),
                attr.getPhaseId(), Convert.toString(uid), attr.getRankLimit());
        List<CpMember> cpMembers = new ArrayList<>();
        List<String> memberIds = new ArrayList<>();
        for (String member : uids) {
            CpMember cpMember = new CpMember();
            long toUid = Convert.toLong(member);
            String cpMemberStr = toUid + "|" + uid;
            CpMemberInfo toMemInfo = new CpMemberInfo();
            toMemInfo.setUid(toUid);
            CpMemberInfo loginMemInfo = new CpMemberInfo();
            loginMemInfo.setUid(uid);
            cpMember.setCpMember(cpMemberStr);
            cpMember.setAnchor(loginMemInfo);
            cpMember.setUser(toMemInfo);
            cpMembers.add(cpMember);
            memberIds.add(cpMemberStr);
        }

        // 用户是土豪 -》 查cp
        uids = getRankMembers(actId, attr.getCpAntiContributeRankId(),
                attr.getPhaseId(), Convert.toString(uid), attr.getRankLimit());
        for (String member : uids) {
            CpMember cpMember = new CpMember();
            long toUid = Convert.toLong(member);
            String cpMemberStr = uid + "|" + toUid;
            CpMemberInfo toMemInfo = new CpMemberInfo();
            toMemInfo.setUid(toUid);
            CpMemberInfo loginMemInfo = new CpMemberInfo();
            loginMemInfo.setUid(uid);
            cpMember.setCpMember(cpMemberStr);
            cpMember.setAnchor(toMemInfo);
            cpMember.setUser(loginMemInfo);
            cpMembers.add(cpMember);
            memberIds.add(cpMemberStr);
        }
        int cnt = Convert.toInt(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, CITY_LIGHT_INCR)));
        int round = cnt / attr.getTotalCity() + ((cnt % attr.getTotalCity()) > 0 ? 1: 0);
        List<Object> keys = new ArrayList<>();
        String preListCitySetKey = String.format(CITY_LIGHT_SET, round);
        for (Integer i : attr.getCityMap().keySet()) {
            keys.add(Convert.toString(i));
        }
        List<Object> hmap = actRedisDao.hmGet(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, preListCitySetKey), keys);
        Map<String, Integer> cpCountMap = new HashMap<>();
        for (Object o : hmap) {
            cpCountMap.put(Convert.toString(o), cpCountMap.getOrDefault(Convert.toString(o), 0) + 1);
        }
        uids = new ArrayList<>();
        for (CpMember cpMember : cpMembers) {
            String cp = cpMember.getUser().getUid()+"|"+cpMember.getAnchor().getUid();
            cpMember.setScore(cpCountMap.getOrDefault(cp, 0));
            uids.add(Convert.toString(cpMember.getUser().getUid()));
            uids.add(Convert.toString(cpMember.getAnchor().getUid()));
        }

        List<Long> uidLongs = uids.stream().map(Long::parseLong).collect(Collectors.toList());
        uidLongs.add(uid);
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uidLongs);
        for (CpMember cpMember : cpMembers) {
            CpMemberInfo memInfo = cpMember.getAnchor();
            WebdbUserInfo anchorUserInfo = batched.getUserInfoMap().get(Convert.toString(memInfo.getUid()));
            memInfo.setAvatar(WebdbUtils.getLogo(anchorUserInfo));
            memInfo.setName(anchorUserInfo.getNick());
            CpMemberInfo userMemInfo = cpMember.getUser();
            WebdbUserInfo userInfo = batched.getUserInfoMap().get(Convert.toString(userMemInfo.getUid()));
            userMemInfo.setAvatar(WebdbUtils.getLogo(userInfo));
            userMemInfo.setName(userInfo.getNick());
        }
        cpMembers.sort(new Comparator<CpMember>() {
            @Override
            public int compare(CpMember o1, CpMember o2) {
                return (int)(o2.getScore()-o1.getScore());
            }
        });
        CpListRsp rsp = new CpListRsp();
        rsp.setCpMembers(cpMembers);
        if(batched != null) {
            if(attr.isNeedNickExt()) {
                NickExt nickExt = JSON.parseObject(batched.getNickExt(), NickExt.class);
                rsp.setNickExtUsers(nickExt.getUsers());
            }
            rsp.setAvatar(WebdbUtils.getLogo(batched.getUserInfoMap().get(uid+"")));
        }
        rsp.setUid(uid);
        return Response.success(rsp);
    }

    @RequestMapping("/getBestCp")
    public Response<BestCpListRsp> getBestCp(Long actId, long cmptInx) {
        CpCityWalkComponentAttr attr = getComponentAttr(actId, cmptInx);
        List<BestCp> bestCps = new ArrayList<>();
        Date now = commonService.getNow(attr.getActId());
        String taskCompletePuzzleKey = String.format(TASK_COMPLETE_ZSET);
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, taskCompletePuzzleKey), now.getTime(), Long.MAX_VALUE);
        for (String raw : rawSet) {
            BestCp bestCp = JsonUtils.deserialize(raw, BestCp.class);
            bestCps.add(bestCp);
        }
        bestCps.sort(new Comparator<BestCp>() {
            @Override
            public int compare(BestCp o1, BestCp o2) {
                return (int) (o2.getEndTime() - o1.getEndTime());
            }
        });
        List<Long> uids = new ArrayList<>();
        for (BestCp bestCp : bestCps) {
            uids.add(bestCp.getBabyUid());
            uids.add(bestCp.getUserUid());
        }
        CpTaskMember cpTaskMember = null;
        Response<LightCityRsp> listCityRsp = listCity(actId, cmptInx, 0);
        if(listCityRsp.getResult() == 0 && listCityRsp.getData() != null
                && !listCityRsp.getData().getLightCities().isEmpty()) {
            LightCity maxLightCity = listCityRsp.getData().getLightCities().get(0);
            int max = 1;
            Map<String, Integer> countMap = new HashMap<>();
            for (LightCity lightCity : listCityRsp.getData().getLightCities()) {
                String cp = lightCity.getUser().getUid() + "_" + lightCity.getAnchor().getUid();
                countMap.put(cp, countMap.getOrDefault(cp, 0) + 1);
            }
            for (LightCity lightCity : listCityRsp.getData().getLightCities()) {
                String cp = lightCity.getUser().getUid() + "_" + lightCity.getAnchor().getUid();
                if(countMap.get(cp) > max) {
                    max = countMap.get(cp);
                    maxLightCity = lightCity;
                }
            }
            cpTaskMember = new CpTaskMember();
            cpTaskMember.setAnchor(maxLightCity.getAnchor());
            cpTaskMember.setUser(maxLightCity.getUser());
            cpTaskMember.setScore(max);
            uids.add(cpTaskMember.getAnchor().getUid());
            uids.add(cpTaskMember.getUser().getUid());
        }
        BestCpListRsp rsp = new BestCpListRsp();
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(uids));
        List<CpTaskMember> cpMembers = new ArrayList<>();
        if(batched != null){
            Map<String, CpMemberInfo> userInfoMap = batched.getUserInfoMap().entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, i -> toMemInfo(i.getValue())));
            cpMembers = bestCps.stream()
                    .map(bestCp -> {
                        CpTaskMember cpTaskMembert = new CpTaskMember();
                        cpTaskMembert.setAnchor(userInfoMap.get(String.valueOf(bestCp.getBabyUid())));
                        cpTaskMembert.setUser(userInfoMap.get(String.valueOf(bestCp.getUserUid())));
                        cpTaskMembert.setStartTime(bestCp.getStartTime());
                        cpTaskMembert.setEndTime(bestCp.getEndTime());
                        cpTaskMembert.setSid(bestCp.getSid());
                        cpTaskMembert.setSsid(bestCp.getSsid());
                        cpTaskMembert.setText(bestCp.getText());
                        return cpTaskMembert;
                    }).collect(Collectors.toList());
            NickExt nickExt = JsonUtils.deserialize(batched.getNickExt(), NickExt.class);
            rsp.setNickExtUsers(nickExt.getUsers());
        }

        rsp.setCpMembers(cpMembers);
        rsp.setCurrentBestCp(cpTaskMember);
        rsp.setCurrentTime(now.getTime());
        log.info("getBestCp size:{}", cpMembers.size());
        return Response.success(rsp);
    }

    private CpMemberInfo toMemInfo(WebdbUserInfo userBaseInfo) {
        CpMemberInfo memInfo = new CpMemberInfo();
        memInfo.setUid(Long.parseLong(userBaseInfo.getUid()));
        memInfo.setName(userBaseInfo.getNick());
        memInfo.setAvatar(WebdbUtils.getLogo(userBaseInfo));
        return memInfo;
    }

    public boolean check(long actId, String giftId, CpCityWalkComponentAttr attr, long uid) {
        if (!actInfoService.inActTime(actId)) {
            return false;
        }
        if(!attr.getGiftValueMap().containsKey(giftId)) {
            log.info("gift consumer check cant find giftId:{}", giftId);
            return false;
        }
        if(signedService.getSignedSidByBusiId(uid, attr.getBusiId()) == 0) {
            log.info("gift consumer check cant find signinfo uid:{}, busiId:{}", uid, attr.getBusiId());
            return false;
        }
        return true;
    }

    //交友连送/全麦/单送  语音房连送/单送 todo 后续stream接入 改成监听榜单事件
    @HdzjEventHandler(value = SendGiftComboEvent.class, canRetry = true)
    public void onSendGiftComboEvent(SendGiftComboEvent sendGiftComboEvent, CpCityWalkComponentAttr attr) {
        if(!check(attr.getActId(), sendGiftComboEvent.getGiftId(), attr, sendGiftComboEvent.getRecvUid())) {
            return;
        }
        Long giftNum = sendGiftComboEvent.getGiftNum();
        long val = attr.getGiftValueMap().get(sendGiftComboEvent.getGiftId()) * giftNum * sendGiftComboEvent.getComboHits();
        if(val < attr.getThreshold()) {
            return;
        }
        int cnt = (int)(val / attr.getThreshold());
        long recvUid = sendGiftComboEvent.getRecvUid();
        if(attr.getBusiId() == 500) {
            recvUid = Convert.toLong(sendGiftComboEvent.getJsonMap().getLong("micCompere"), 0L);
            int grabLoveType = Convert.toInt(sendGiftComboEvent.getJsonMap().getInteger("grabLoveType"), 0);
            List<Integer> notSettleType = List.of(1,3,4,5,6,10,11,12,13,14,15,17);
            if(notSettleType.contains(grabLoveType)) {
                log.info("sendGiftComboEvent skip type:{}, event:{}", grabLoveType, GsonUtil.toJson(sendGiftComboEvent));
            }

            List<Integer> multiPlayType = List.of(8, 801, 802, 803, 18, 1801, 1802, 1803, 19, 1901, 1902, 1903);
            if(multiPlayType.contains(grabLoveType)) {
                recvUid = sendGiftComboEvent.getRecvUid();
            }
            int settlementMode = Convert.toInt(sendGiftComboEvent.getJsonMap().getInteger("settlementMode"), 0);
            if(settlementMode == 5 || settlementMode == 6) {
                long settlementUID = Convert.toLong(sendGiftComboEvent.getJsonMap().getLong("settlementUID"), 0L);
                recvUid = settlementUID;
            }
        }
        log.info("sendGiftComboEvent receiveUid:{}, val:{}, cnt:{}, event:{}", recvUid, val, cnt, GsonUtil.toJson(sendGiftComboEvent));
        for(int i = 0; i < cnt; i++) {
            lightCity(attr, sendGiftComboEvent.getSeq()+"_"+i, recvUid,
                    sendGiftComboEvent.getSendUid(), sendGiftComboEvent.getSid(), sendGiftComboEvent.getSsid());
        }
    }

    //语音房全麦
    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent sendGiftEvent, CpCityWalkComponentAttr attr) {
        if(!check(attr.getActId(), sendGiftEvent.getGiftId(), attr, sendGiftEvent.getRecvUid())) {
            return;
        }
        long actId = attr.getActId();
        //不在这里处理
        if(sendGiftEvent.getTemplate().getValue() == Template.Jiaoyou.getValue()) {
            return;
        }
        Long giftNum = sendGiftEvent.getGiftNum();
        long val = attr.getGiftValueMap().get(sendGiftEvent.getGiftId()) * giftNum;
        if(val < attr.getThreshold()) {
            return;
        }
        List<Long> recvUids = new ArrayList<>();
        //追玩，技能卡事件需要特殊处理(这里要区分全麦打赏)
        if(sendGiftEvent.getTemplate().getValue() == Template.ZhuiWan.getValue()
                || sendGiftEvent.getTemplate().getValue() == Template.SkillCard.getValue()) {
            JSONObject expandJo = sendGiftEvent.getJsonMap();
            if(!expandJo.containsKey("isMultiple") || !expandJo.containsKey("receivers") || expandJo.getJSONArray("receivers").size() <= 1){
                log.info("callback handleGiftKafkaEvent is not multiple cant process");
                return;
            }
            String key = makeKey(attr, CITY_SEND_GIFT_LOCK);
            String groupCode = redisConfigManager.getGroupCode(attr.getActId());
            String turnoverIdStr = expandJo.getString("turnoverOrderId");
            boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(turnoverIdStr), DateUtil.getNowYyyyMMddHHmmss());
            if (!first) {
                return;
            }
            JSONArray jsonArray = expandJo.getJSONArray("receivers");
            for (Object o : jsonArray) {
                recvUids.add(((JSONObject) o).getLong("targetUid"));
            }
        }
        String playerUid = String.valueOf(sendGiftEvent.getSendUid());
        for (Long recvUid : recvUids) {
            if (!commonService.checkWhiteList(actId, RoleType.ANCHOR, recvUid + "")) {
                log.warn("not in white list,return,trans:{}", JSON.toJSONString(sendGiftEvent));
                return;
            }
        }
        if (!commonService.checkWhiteList(actId, RoleType.USER, playerUid + "")) {
            log.warn("not in white list,return,trans:{}", JSON.toJSONString(sendGiftEvent));
            return;
        }
        int cnt = (int)(val / attr.getThreshold());
        log.info("sendGiftEvent val:{}, cnt:{}, event:{}", val, cnt, GsonUtil.toJson(sendGiftEvent));
        for (Long recvUid : recvUids) {
            for(int i = 0; i < cnt; i++) {
                lightCity(attr, sendGiftEvent.getSeq()+"_"+recvUid+"_"+i, recvUid,
                        sendGiftEvent.getSendUid(), sendGiftEvent.getSid(), sendGiftEvent.getSsid());
            }
        }
    }

    public void incrLogVal(CpCityWalkComponentAttr attr, String key, int val) {
        actRedisDao.incrValue(redisConfigManager.getGroupCode(attr.getActId()), key, val);
    }

    public void lightCity(CpCityWalkComponentAttr attr, String seq, long anchorUid, long uid, long sid, long ssid) {
        seq = MD5SHAUtil.getMD5(seq);
        List<Long> ret = actRedisDao.incrValueWithSeq(redisConfigManager.getGroupCode(attr.getActId()),
                seq, makeKey(attr, CITY_LIGHT_INCR), 1, DateUtil.ONE_MONTH_SECONDS);
        long cnt = ret.get(0);
        int round = (int)cnt / attr.getTotalCity() + ((cnt % attr.getTotalCity()) > 0 ? 1: 0);
        int lightCity = (int)cnt % attr.getTotalCity() == 0
                ? attr.getTotalCity() : (int)cnt% attr.getTotalCity();
        log.info("light city round:{}, city:{}, cp:{}", round, lightCity, uid+"_"+anchorUid);
        String preKey = String.format(CITY_LIGHT_INFO, round, lightCity);
        String cpMember = uid + "|" + anchorUid;
        actRedisDao.set(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, preKey), cpMember);
        String preListCitySetKey = String.format(CITY_LIGHT_SET, round);
        actRedisDao.hset(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, preListCitySetKey), lightCity+"", cpMember, DateUtil.ONE_MONTH_SECONDS);
        actRedisDao.hset(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, preListCitySetKey), lightCity+"", cpMember, DateUtil.ONE_MONTH_SECONDS);
        actRedisDao.hset(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, CP_LAST_CITY), cpMember, sid + "_" + ssid, DateUtil.ONE_MONTH_SECONDS);
        actRedisDao.zIncrWithSeq(redisConfigManager.getGroupCode(attr.getActId()),
                seq+"_zset", makeKey(attr, CITY_CP_SCORE),
                cpMember, 1);

        //结算
        if(lightCity == attr.getTotalCity()) {
            List<Object> keys = new ArrayList<>();
            for (Integer i : attr.getCityMap().keySet()) {
                keys.add(Convert.toString(i));
            }
            long maxUid = uid;
            long maxAnchorUid = anchorUid;
            int maxLightCity = 0;
            Map<String, Integer> cpCountMap = new HashMap<>();
            List<Object> hmap = actRedisDao.hmGet(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, preListCitySetKey), keys);
            for (Object o : hmap) {
                cpCountMap.put(Convert.toString(o), cpCountMap.getOrDefault(Convert.toString(o), 0) + 1);
                if(cpCountMap.get(Convert.toString(o)) > maxLightCity) {
                    maxUid = Convert.toLong(Convert.toString(o).split("\\|")[0]);
                    maxAnchorUid = Convert.toLong(Convert.toString(o).split("\\|")[1]);
                    maxLightCity = cpCountMap.get(Convert.toString(o));
                }
            }
            String maxCp = maxUid+"|"+maxAnchorUid;
            String sidSsidStr = actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, CP_LAST_CITY), maxCp);
            long broSid = Convert.toLong(sidSsidStr.split("_")[0], sid);
            long broSsid = Convert.toLong(sidSsidStr.split("_")[1], ssid);
            log.info("brosid:{}, brossid:{}, sid:{}, ssid:{}", broSid, broSsid, sid, ssid);
            List<Long> uids = new ArrayList<>();
            uids.add(maxUid);
            uids.add(maxAnchorUid);
            BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
            WebdbUserInfo anchorUserInfo = batched.getUserInfoMap().get(Convert.toString(maxAnchorUid));
            WebdbUserInfo userInfo = batched.getUserInfoMap().get(Convert.toString(maxUid));

            log.info("light all city round:{}, city:{}, cp:{}", round, lightCity, maxUid+"_"+maxAnchorUid);
            String lockName = makeKey(attr, CP_LOTTERY_LOCK);
            int second = 30;
            Secret lock = null;
            try {
                lock = locker.lock(lockName, second);
                if (lock != null) {
                    long limit = Convert.toLong(actRedisDao.get(redisConfigManager.getGroupCode(attr.getActId()),
                            makeKey(attr, JACK_POINT_AWARD_LIMIT)));
                    long lotteryTaskId = attr.getBestCpTaskMap().get("0");
                    String dateStr = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
                    if (attr.getBestCpTaskMap().containsKey(dateStr)) {
                        lotteryTaskId = attr.getBestCpTaskMap().get(dateStr);
                    }
                    CpCityWalkComponentAttr.PackageInfo packageInfo = new CpCityWalkComponentAttr.PackageInfo();
                    if (limit < attr.getJackPointLimit()) {
                        log.info("best cp consume:{}, cp:{}", limit, cpMember);
                        //lock
                        BatchLotteryResult result = hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(),
                                BusiId.GAME_ECOLOGY.getValue(), maxAnchorUid, lotteryTaskId, 1, 0, seq);
                        log.info("uid:{} lottery result:{}", maxAnchorUid, JsonUtil.toJson(result));
                        //记录中奖记录
                        if (result.getCode() == 0) {
                            Map<Long, Long> recordIds = result.getRecordPackages();
                            for (Map.Entry<Long, Long> entry : recordIds.entrySet()) {
                                packageInfo =
                                        attr.getPackageMap().get(entry.getValue());
                                //发奖 incr limit
                                actRedisDao
                                        .incrValueWithLimitSeq(redisConfigManager.getGroupCode(attr.getActId()),
                                                seq+"_award+limit", makeKey(attr, JACK_POINT_AWARD_LIMIT), packageInfo.getValue(), attr.getJackPointLimit(), true, 60 * 60 * 24 * 10);
                            }
                            locker.unlock(lockName, lock);
                        } else {
                            throw new RuntimeException("lottery error");
                        }
                    } else {
                        //兜底
                        log.info("best cp back up consume:{}, cp:{}", limit, cpMember);
                        packageInfo = attr.getPackageMap().get(attr.getBestCpBackUpPackageId());
                    }
                    log.info("best cp packageInfo:{}, cp:{}", JsonUtil.toJson(packageInfo), cpMember);
                    hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                            maxUid, packageInfo.getAwardTaskId(), 1, packageInfo.getAwardPackageId(), seq+"_uid_"+maxUid, null);
                    hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                            maxAnchorUid, packageInfo.getAwardTaskId(), 1, packageInfo.getAwardPackageId(), seq + "_anchor_" + maxAnchorUid, null);
                    incrLogVal(attr, makeKey(attr, String.format(AWARD_RECORD_CNT, packageInfo.getAwardName()+packageInfo.getAwardTimeUnit())), 1);
                    //write log
                    AwardRecord awardRecord = new AwardRecord();
                    awardRecord.setBabyUid(maxAnchorUid);
                    awardRecord.setUserUid(maxUid);
                    awardRecord.setTimestamp(commonService.getNow(attr.getActId()).getTime());
                    awardRecord.setBigAward(packageInfo.getBigAward() > 0);
                    awardRecord.setTp(BEST_TP);
                    awardRecord.setGift(packageInfo.getAwardTimeUnit() + packageInfo.getAwardName());
                    addAwardLog(attr, maxUid, maxAnchorUid, seq, JsonUtil.toJson(awardRecord));
                    //设置cp
                    zaddBectCpTaskInfo(attr, broSid, broSsid, maxAnchorUid, maxUid, seq);
                    broBanner(attr, broSid, broSsid, maxAnchorUid, maxUid, seq, userInfo, anchorUserInfo, packageInfo.getAwardTimeUnit()+packageInfo.getAwardName(), maxLightCity);
                    broLayer(attr, broSid, broSsid, userInfo, anchorUserInfo, packageInfo.getAwardPic(), packageInfo.getAwardTimeUnit(), BEST_TP);
                } else {
                    throw new RuntimeException("lottery get lock error");
                }
            } catch (Exception e) {
                log.info("light best cp error {}", e.getMessage(), e);
            } finally {
                if (lock != null) {
                    locker.unlock(lockName, lock);
                }
            }
        }

        List<Long> uids = new ArrayList<>();
        uids.add(uid);
        uids.add(anchorUid);
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(uids);
        WebdbUserInfo anchorUserInfo = batched.getUserInfoMap().get(Convert.toString(anchorUid));
        WebdbUserInfo userInfo = batched.getUserInfoMap().get(Convert.toString(uid));

        boolean luckCp = attr.getSpecialCityNum().contains(lightCity);
        if(luckCp) {
            log.info("light luck city round:{}, city:{}, cp:{}", round, lightCity, uid+"_"+anchorUid);
            long lotteryTaskId = attr.getLuckCpTaskMap().get("0");
            String dateStr = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
            if(attr.getLuckCpTaskMap().containsKey(dateStr)) {
                lotteryTaskId = attr.getLuckCpTaskMap().get(dateStr);
            }
            CpCityWalkComponentAttr.PackageInfo packageInfo = new CpCityWalkComponentAttr.PackageInfo();
            BatchLotteryResult result = hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(),
                    BusiId.GAME_ECOLOGY.getValue(), anchorUid, lotteryTaskId, 1, 0, seq + "luck");
            //记录中奖记录
            if (result.getCode() == 0) {
                Map<Long, Long> recordIds = result.getRecordPackages();
                for (Map.Entry<Long, Long> entry : recordIds.entrySet()) {
                    packageInfo = attr.getPackageMap().get(entry.getValue());
                }
            } else {
                throw new RuntimeException("lottery error");
            }
            log.info("luck cp packageInfo:{}, cp:{}", JsonUtil.toJson(packageInfo), cpMember);
            //write log
            incrLogVal(attr, makeKey(attr, String.format(AWARD_RECORD_CNT, packageInfo.getAwardName()+packageInfo.getAwardTimeUnit())), 1);
            AwardRecord awardRecord = new AwardRecord();
            awardRecord.setBabyUid(anchorUid);
            awardRecord.setUserUid(uid);
            awardRecord.setTimestamp(commonService.getNow(attr.getActId()).getTime());
            awardRecord.setBigAward(packageInfo.getBigAward() > 0);
            awardRecord.setTp(LUCK_TP);
            awardRecord.setGift(packageInfo.getAwardTimeUnit() + packageInfo.getAwardName());
            addAwardLog(attr, uid, anchorUid, seq+"_luck", JsonUtil.toJson(awardRecord));
            hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                    uid, packageInfo.getAwardTaskId(), 1, packageInfo.getAwardPackageId(), seq+"_luck_uid_"+uid, null);
            hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(),
                    anchorUid, packageInfo.getAwardTaskId(), 1, packageInfo.getAwardPackageId(), seq+"_luck_anch_"+anchorUid, null);
            broUser(attr, attr.getActId(), packageInfo.getAwardTimeUnit() + packageInfo.getAwardName(), packageInfo.getAwardPic(), userInfo, anchorUserInfo, CP_LUCK_TOAST, lightCity);
            broLayer(attr, sid, ssid, userInfo, anchorUserInfo, packageInfo.getAwardPic(), packageInfo.getAwardTimeUnit(), LUCK_TP);
        }
        broUser(attr, attr.getActId(), "", "", userInfo, anchorUserInfo, CP_LIGHT_TOAST, lightCity);
    }

    //单播
    private void broUser(CpCityWalkComponentAttr attr, long actId, String name, String pic, WebdbUserInfo userInfo, WebdbUserInfo anchorUserInfo,
                         String noticeType, int city) {
        JSONObject msg = new JSONObject();
        //获奖toast
        if(CP_LUCK_TOAST.equals(noticeType)) {
            msg.put("tp", LUCK_TP);
            msg.put("text", "打卡" + attr.getCityMap().get(city));
            msg.put("img", pic);
            msg.put("gift", name);
        } else {
            //挂件打卡
            msg.put("text", "打卡" + attr.getCityMap().get(city));
            msg.put("userUid", userInfo.getUid());
            msg.put("babyUid", anchorUserInfo.getUid());
            msg.put("userNick", org.apache.commons.codec.binary.Base64.encodeBase64String(userInfo.getNick().getBytes()));
            msg.put("babyNick", org.apache.commons.codec.binary.Base64.encodeBase64String(anchorUserInfo.getNick().getBytes()));
            msg.put("userLogo", WebdbUtils.getLogo(userInfo));
            msg.put("babyLogo", WebdbUtils.getLogo(anchorUserInfo));
        }
        commonBroadCastService.commonNoticeUnicast(actId, noticeType, JsonUtil.toJson(msg) ,StringUtils.EMPTY
                , Convert.toLong(userInfo.getUid()));
        commonBroadCastService.commonNoticeUnicast(actId, noticeType, JsonUtil.toJson(msg) ,StringUtils.EMPTY
                , Convert.toLong(anchorUserInfo.getUid()));
    }

    //挂件广播
    private void broLayer(CpCityWalkComponentAttr attr, long sid, long ssid, WebdbUserInfo userInfo,
                          WebdbUserInfo anchorUserInfo, String pic, String num, int tp) {
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        String logo = WebdbUtils.getLogo(userInfo);
        String anchorNick = anchorUserInfo.getNick();
        String anchorLogo = WebdbUtils.getLogo(anchorUserInfo);
        Map<String, Object> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("userNick", org.apache.commons.codec.binary.Base64.encodeBase64String(nick.getBytes()));
        extMap.put("babyNick", org.apache.commons.codec.binary.Base64.encodeBase64String(anchorNick.getBytes()));
        extMap.put("userLogo", logo);
        extMap.put("babyLogo", anchorLogo);
        extMap.put("text", num);
        extMap.put("gift", pic);
        extMap.put("tp", tp);
        extMap.put("sid", sid);
        extMap.put("ssid", ssid);
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setUserUid(Convert.toLong(anchorUserInfo.getUid())).setUserNick(nick).setUserLogo(logo)
                .setBannerId(BEST_CP_LAYER_BANNER_ID).setBannerType(0)
                .setUserScore(0).setJsonData(JSON.toJSONString(extMap))
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        BusiId busiId = BusiId.findByValue((int) attr.getBusiId());
        broadCastHelpService.broadcast(attr.getActId(), busiId, attr.getLayerBroType(), sid, ssid, bannerBroMsg);
    }

    //全服最强cp
    private void broBanner(CpCityWalkComponentAttr attr, long sid, long ssid,
                           long anchorUid, long uid, String seq,  WebdbUserInfo userInfo,
                           WebdbUserInfo anchorUserInfo, String awardName, int city) {
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        String logo = WebdbUtils.getLogo(userInfo);
        String anchorNick = anchorUserInfo.getNick();
        String anchorLogo = WebdbUtils.getLogo(anchorUserInfo);
        Map<String, Object> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("userNick", org.apache.commons.codec.binary.Base64.encodeBase64String(nick.getBytes()));
        extMap.put("babyNick", org.apache.commons.codec.binary.Base64.encodeBase64String(anchorNick.getBytes()));
        extMap.put("userLogo", logo);
        extMap.put("babyLogo", anchorLogo);
        extMap.put("babyUid", anchorUid);
        extMap.put("userUid", uid);
        extMap.put("text", attr.getBannerText());
        extMap.put("gift", awardName);

        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setUserUid(uid).setUserNick(nick).setUserLogo(logo)
                .setBannerId(BEST_CP_BANNER_ID).setBannerType(0)
                .setUserScore(0).setJsonData(JSON.toJSONString(extMap))
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        BusiId busiId = BusiId.findByValue((int) attr.getBusiId());
        broadCastHelpService.broadcast(attr.getActId(), busiId, attr.getBannerBroType(), sid, ssid, bannerBroMsg);
        if(attr.getBannerSvag() != null) {
            List<BannerSvagConfig> list = new ArrayList<>(attr.getBannerSvag().values());
            BannerSvagConfig svagConfig = list.get(0);
            AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();
            UserBaseInfo userInfoT = new UserBaseInfo();
            userInfoT.setUid(Convert.toLong(userInfo.getUid()));
            userInfoT.setNick(userInfo.getNick());
            UserBaseInfo anchorUserInfoT = new UserBaseInfo();
            anchorUserInfoT.setUid(Convert.toLong(anchorUserInfo.getUid()));
            anchorUserInfoT.setNick(anchorUserInfo.getNick());
            //svga内嵌文字
            Set<Long> uids = new HashSet<>();
            uids.add(anchorUid);
            uids.add(uid);
            List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, svagConfig, userInfoT, anchorUserInfoT);
            broSvgaConfig.setContentLayers(broContentLayers);
            //svga内嵌图片
            MemberInfo memberInfo = new MemberInfo();
            memberInfo.setHdLogo(logo);
            MemberInfo anchorMemberInfo = new MemberInfo();
            anchorMemberInfo.setHdLogo(anchorLogo);
            List<Map<String, String>> broImgLayers = getSvgaImageConfig(attr, memberInfo, anchorMemberInfo);
            broSvgaConfig.setImgLayers(broImgLayers);

            broSvgaConfig.setLoops(attr.getLoops());

            AppBannerLayout layout = new AppBannerLayout();
            layout.setType(attr.getLayoutType());
            if (StringUtil.isNotBlank(attr.getLayoutMargin())) {
                layout.setMargin(JSON.parseObject(attr.getLayoutMargin(), new TypeReference<Map<String, List<Integer>>>() {
                }));
            }
            broSvgaConfig.setLayout(layout);
            broSvgaConfig.setWhRatio(attr.getWhRatio());
            broSvgaConfig.setClickLayerName(svagConfig.getClickLayerName());
            broSvgaConfig.setSvgaURL(svagConfig.getSvgaURL());
            broSvgaConfig.setJumpSvgaURL(svagConfig.getJumpSvgaURL());
            broSvgaConfig.setMiniURL(svagConfig.getMiniURL());
            broSvgaConfig.setJumpMiniURL(svagConfig.getJumpMiniURL());

            AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getBroBusiId(),
                    3, 0, 0, "",
                    Lists.newArrayList());
            appBannerEvent.setUid(uid);
            appBannerEvent.setUidList(List.copyOf(uids));
            appBannerEvent.setContentType(6);
            appBannerEvent.setAppId(KafkaService.getTurnoverAppId((int) attr.getBusiId()));
            appBannerEvent.setSvgaConfig(broSvgaConfig);
            List<Map<String, String>> layerKeys = new ArrayList<>();
            for (Map<String, AppBannerSvgaText> broContentLayer : broContentLayers) {
                for (String key : broContentLayer.keySet()) {
                    Map<String, String> map = new HashMap<>();
                    map.put(key, broContentLayer.get(key).getText());
                    layerKeys.add(map);
                }
            }
            for (Map<String, String> broImgLayer : broImgLayers) {
                for (String key : broImgLayer.keySet()) {
                    Map<String, String> map = new HashMap<>();
                    map.put(key, broImgLayer.get(key));
                    layerKeys.add(map);
                }
            }
            broMp42App(attr.getActId(), seq, attr.getBroBusiId(), attr.getBcType(), sid, ssid, List.copyOf(uids),
                    svagConfig.getSvgaURL(), 999, layerKeys);
        }
        StringBuilder content = new StringBuilder();
        content.append("### 最强CP\n");
        content.append("主持昵称（UID）：").append(anchorUserInfo.getNick()).append("(").append(anchorUserInfo.getUid()).append(")\n");
        content.append("神豪昵称（UID）：").append(userInfo.getNick()).append("(").append(userInfo.getUid()).append(")\n");
        content.append("打卡城市数量 ：").append(city).append("\n");
        content.append("获得的礼物奖励：").append(awardName).append("\n");
        String ticket = String.format("yy://pd-[sid=%s&subid=%s]", sid, ssid);
        content.append("所在房间飞机票：").append(ticket);
        String msg = buildActRuliuMsg(attr.getActId(), false, "【都市漫游】", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
    }

    public void broMp42App(long actId, String seq, int business, int bcType, long sid,
                           long ssid, List<Long> uids, String mp4Url, int broLevel, List<Map<String, String>> layerKeyValues) {
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(actId, seq, business,
                bcType, sid, ssid, "",
                Lists.newArrayList());
        appBannerEvent.setUid(0);
        appBannerEvent.setUidList(uids);
        appBannerEvent.setContentType(5);
        appBannerEvent.setPushUidlist(uids);
        AppBannerMp4Config appBannerMp4Config = new AppBannerMp4Config();
        appBannerMp4Config.setUrl(mp4Url);
        appBannerMp4Config.setLevel(broLevel);
        appBannerMp4Config.setLayerExtKeyValues(layerKeyValues);
        appBannerEvent.setMp4Config(appBannerMp4Config);
        kafkaService.sendAppBannerKafka(appBannerEvent);
        log.info("app bro mp4 done seq:{}, event:{}", seq, JSON.toJSONString(appBannerEvent));
    }

    private void zaddBectCpTaskInfo(CpCityWalkComponentAttr attr, long sid, long ssid, long babyUid, long userUid, String seq) {
        String taskCompletePuzzleKey = String.format(TASK_COMPLETE_ZSET);
        Date now = commonService.getNow(attr.getActId());
        double score = now.getTime() + 300000;
        BestCp bestCp = new BestCp();
        bestCp.setSid(sid);
        bestCp.setSsid(ssid);
        bestCp.setBabyUid(babyUid);
        bestCp.setUserUid(userUid);
        bestCp.setStartTime(now.getTime());
        bestCp.setEndTime(now.getTime() + 300000);
        bestCp.setText(attr.getBestCpText());
        bestCp.setSeq(seq);
        actRedisDao.zAdd(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, taskCompletePuzzleKey), JsonUtil.toJson(bestCp), score);
        log.info("add zset puzzle zkey:{}, babyCpInfo:{}", taskCompletePuzzleKey, JsonUtil.toJson(bestCp));
    }

    private void addAwardLog(CpCityWalkComponentAttr attr, long uid, long babyUid, String seq, String content) {
        //写我的中奖记录
        String userRecordKey = makeKey(attr, String.format(MY_AWARD_LIST, uid));
        String anchorRecordKey = makeKey(attr, String.format(MY_AWARD_LIST, babyUid));
        String userSeq = makeKey(attr,"seq:userawardrecordcitywalk:"+ seq + "_" + uid);
        String redisGroup = redisConfigManager.getGroupCode(attr.getActId());

        actRedisDao.lPushWithSeq(redisGroup, userSeq, userRecordKey, content, DateUtil.ONE_DAY_SECONDS);
        String anchorSeq = makeKey(attr,"seq:anchorawardrecordcitywalk:"+ seq + "_"+ babyUid );
        actRedisDao.lPushWithSeq(redisGroup, anchorSeq, anchorRecordKey, content, DateUtil.ONE_DAY_SECONDS);
        //写全服中奖记录
        String wholeKey = makeKey(attr, WHOLE_AWARD_LIST);
        String wholdSeq = makeKey(attr,"seq:wholdawardrecordcitywalk:"+seq + "_" + uid+"_" + babyUid);
        actRedisDao.lPushWithSeq(redisGroup, wholdSeq, wholeKey, content, DateUtil.ONE_DAY_SECONDS);
    }

    public List<String> getRankMembers(long actId, long rankId, long phaseId, String findSrcMember, long count) {
        Map<String, QueryRankingRequest> reqMap = Maps.newHashMap();
        QueryRankingRequest req = new QueryRankingRequest();
        req.setActId(actId);
        req.setRankingId(rankId);
        req.setPhaseId(phaseId);
        req.setFindSrcMember(findSrcMember);
        req.setRankingCount(count);
        reqMap.put(findSrcMember, req);
        List<String> uids = new ArrayList<>();
        Map<String, BatchRankingItem> conTop = hdztRankingThriftClient.queryBatchRanking(reqMap, null);
        for (BatchRankingItem value : conTop.values()) {
            for (Rank rank : value.getData()) {
                uids.add(rank.getMember());
            }
        }
        return uids;
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = true)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, CpCityWalkComponentAttr attr) {
        long uid = event.getUid(), actId = attr.getActId(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        boolean actEnterEvent = StringUtil.isNotBlank(extJson) && extJson.contains(String.valueOf(attr.getActId()));
        //协议只发1次，确保是本次活动触发的，才弹窗
        if (!actEnterEvent) {
            log.warn("not this actId UserEnterTemplateEvent uid:{}", uid);
            return;
        }
        log.info("onUserEnterTemplate uid:{},actId:{},extJson:{},sid:{},ssid:{}", uid, attr.getActId(), extJson, sid, ssid);
        Date now = commonService.getNow(attr.getActId());
        //发放进房口令，一个用户只发送一次
        String currentTing = sid + "_" + ssid;
        List<BestCp> bestCps = new ArrayList<>();
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, TASK_COMPLETE_ZSET), now.getTime(), Long.MAX_VALUE);
        for (String raw : rawSet) {
            BestCp bestCp = JsonUtils.deserialize(raw, BestCp.class);
            bestCps.add(bestCp);
        }
        String seq = "";
        for (BestCp bestCp : bestCps) {
            String sidSsid = bestCp.getSid() + "_"+ bestCp.getSsid();
            if(currentTing.equals(sidSsid)) {
                seq = bestCp.getSeq();
                String groupCode = getRedisGroupCode(attr.getActId());
                String key = makeKey(attr, String.format(ENTER_ROOM_BRO_SEQ, seq));
                boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
                if(first) {
                    JSONObject chatJson = new JSONObject();
                    chatJson.put("text", "公屏发送“风景如诗，你自成画”有机会获得祝福奖励！");
                    chatJson.put("sid", sid);
                    chatJson.put("ssid", ssid);
                    commonBroadCastService.commonNoticeUnicast(attr.getActId(), CP_ROOM_TOAST,
                            JsonUtil.toJson(chatJson) , StringUtils.EMPTY, event.getUid());
                    break;
                }
            }
        }
    }

    @HdzjEventHandler(value = ChannelChatTextInnerEvent.class, canRetry = true)
    public void onChannelTextChatInnerEvent(ChannelChatTextInnerEvent event, CpCityWalkComponentAttr attr) {
        long sid = event.getTopsid();
        long ssid = event.getSubsid();
        long uid = event.getUid();
        String currentTing = sid + "_" + ssid;
        Date now = commonService.getNow(attr.getActId());
        List<BestCp> bestCps = new ArrayList<>();
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, TASK_COMPLETE_ZSET), now.getTime(), Long.MAX_VALUE);
        for (String raw : rawSet) {
            BestCp bestCp = JsonUtils.deserialize(raw, BestCp.class);
            bestCps.add(bestCp);
        }
        String seq = "";
        for (BestCp bestCp : bestCps) {
            String sidSsid = bestCp.getSid() + "_"+ bestCp.getSsid();
            if(currentTing.equals(sidSsid)) {
                seq = bestCp.getSeq();
                if(textMatch(event.getChat(), attr.getTargetWord())) {
                    String groupCode = redisConfigManager.getGroupCode(attr.getActId());
                    String key = makeKey(attr, String.format(SEND_CHAT_LOTTERY_SEQ, seq));
                    boolean first = actRedisDao.hsetnx(groupCode, key, String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
                    if(first){
                        String time = DateUtil.format(now);
                        String lotterySeq = MD5SHAUtil.getMD5(makeKey(attr,seq + "_chat_" + uid));
                        BatchLotteryResult result = hdztAwardServiceClient.doLottery(time, attr.getBusiId(),
                                uid, attr.getChatTaskId(), 1, 0, lotterySeq);
                        log.info("onChannelTextChatInnerEvent uid:{} lottery result:{}", uid, JsonUtil.toJson(result));
                        Response<List<LotteryAward>> response = lotteryAward(result, attr);
                        if(response.success()) {
                            JSONObject extJson = new JSONObject();
                            extJson.put("hit", false);
                            if(!response.getData().isEmpty()) {
                                LotteryAward award = response.getData().get(0);
                                extJson.put("name", award.getName());
                                extJson.put("icon", award.getImg());
                                extJson.put("awardCount", 1);
                                extJson.put("hit", true);
                                incrLogVal(attr, makeKey(attr, String.format(AWARD_RECORD_CNT, award.getName()+"_chat")), 1);
                            }
                            commonBroadCastService.commonNoticeUnicast(attr.getActId(), CHANNEL_CHAT_AWARD, extJson.toJSONString()
                                    ,StringUtils.EMPTY , uid);
                            break;
                        }
                    } else {
                        log.warn("onChannelTextChatInnerEvent not first uid:{},sid:{},ssid:{}",uid,sid,ssid);
                    }
                } else {
                    log.info("onChannelTextChatInnerEvent not match uid:{},sid:{},ssid:{},chat:{}",uid,sid,ssid,event.getChat());
                }
            }
        }
    }

    public Response<List<LotteryAward>> lotteryAward(BatchLotteryResult batchLotteryResult, CpCityWalkComponentAttr attr) {
        if (batchLotteryResult.getCode() != 0) {
            return Response.fail(3, batchLotteryResult.getReason());
        }
        List<LotteryAward> awardList = Lists.newArrayList();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Long, Long> recordIds = batchLotteryResult.getRecordPackages();
        Map<Long, Integer> pidCount = Maps.newHashMap();
        for (Long pid : recordIds.values()) {
            pidCount.merge(pid, 1, Integer::sum);
        }
        for (Map.Entry<Long, Integer> entry : pidCount.entrySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(entry.getKey());
            if (awardModelInfo != null) {
                if(awardModelInfo.getPackageName().contains("谢谢参与")) {
                    continue;
                }
                LotteryAward award = new LotteryAward();
                award.setName(awardModelInfo.getPackageName());
                award.setImg(awardModelInfo.getPackageImage());
                award.setNum(entry.getValue() == 1 ? 0 : entry.getValue());
                awardList.add(award);
            }
        }
        return Response.success(awardList);
    }

    public Map<Long, AwardModelInfo> packageInfoMap(CpCityWalkComponentAttr attr) {
        try {
            Map<Long, AwardModelInfo> visit = hdztAwardServiceClient.queryAwardTasks(attr.getChatTaskId());
            return  visit == null ? Collections.emptyMap() : visit;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }

    public boolean textMatch(String chatText, String targetWord) {
        String cleanedInput = chatText.replaceAll("[\\p{P}\\p{S}\\s]+", "");
        return containsTargetSequence(cleanedInput, targetWord);
    }

    public static boolean containsTargetSequence(String input, String target) {
        int targetIndex = 0;
        for (int i = 0; i < input.length(); i++) {
            if (input.charAt(i) == target.charAt(targetIndex)) {
                targetIndex++;
                if (targetIndex == target.length()) {
                    return true;
                }
            }
        }
        return false;
    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(CpCityWalkComponentAttr attr, BannerSvagConfig svagConfig,
                                                                   UserBaseInfo userInfo, UserBaseInfo anchorUserInfo) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        String contentLayerCodes = svagConfig.getContentLayerCodes();
        if (StringUtil.isNotBlank(contentLayerCodes)) {
            String[] contentLayerCodeArr = contentLayerCodes.split(",");
            for (String contentLayerCode : contentLayerCodeArr) {
                Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
                BannerSvgaTextConfig textConfig = attr.getSvgaText().get(contentLayerCode);
                if (textConfig == null) {
                    continue;
                }
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
                //配置动态替换文本
                String text = contextReplace(textConfig.getText(), userInfo, anchorUserInfo);
                if (attr.getTextDynamicValue() != null) {
                    Map<String, String> replaceValue = attr.getTextDynamicValue();
                    for (String key : replaceValue.keySet()) {
                        text = text.replace(key, replaceValue.get(key));
                    }
                }
                appBannerSvgaText.setText(text);
                appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
                appBannerSvgaText.setGravity(textConfig.getGravity());
                if (StringUtil.isNotBlank(textConfig.getImages())) {
                    appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
                }
                if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                    appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
                }
                broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

                if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                    broContentLayers.add(broSvgaTextLayer);
                }
            }
        }
        return broContentLayers;
    }

    private List<Map<String, String>> getSvgaImageConfig(CpCityWalkComponentAttr attr, MemberInfo memberInfo, MemberInfo anchorMemberInfo) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }
        Map<String, String> imageMap = attr.getSvgaImgLayers();
        for (String imageKey : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String image = imageMap.get(imageKey);
            String value = replaceImage(image, memberInfo, anchorMemberInfo);
            broImgLayer.put(imageKey, value);
            broImgLayers.add(broImgLayer);
        }
        return broImgLayers;
    }

    private String contextReplace(String context, UserBaseInfo userInfo, UserBaseInfo anchorUserInfo) {
        context = context.replace("{nick}", String.format("{%s:n}", userInfo.getUid()));
        context = context.replace("{anchorNick}", String.format("{%s:n}", anchorUserInfo.getUid()));
        return context;
    }

    private String replaceImage(String context, MemberInfo memberInfo, MemberInfo anchorMemberInfo) {
        return context.replace("{header}", Convert.toString(memberInfo.getHdLogo()))
                .replace("{anchorHeader}", Convert.toString(anchorMemberInfo.getHdLogo()));
    }

    @Data
    private static class CpListRsp {
        List<CpMember> cpMembers;

        Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private long uid;

        private String avatar;

        private boolean sign;
    }

    @Data
    public static class LotteryAward {
        private String name;

        private String img;

        private int num;
    }

    @Data
    private static class BestCpListRsp {
        private long currentTime;
        private CpTaskMember currentBestCp;
        private List<CpTaskMember> cpMembers;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    public static class CpTaskMember {
        private CpMemberInfo anchor;
        private CpMemberInfo user;
        private long sid;
        private long ssid;
        private long startTime;
        private long endTime;
        private String text;
        private long score;
    }

    @Data
    private static class LightCityRsp {
        private int round;
        private List<LightCity> lightCities;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    private static class LightCity {
        private int index;
        private CpMemberInfo anchor;
        private CpMemberInfo user;
        private long sid;
        private long ssid;
        private String cp;
    }

    @Data
    private static class AwardRecordRsp {
        private List<AwardRecord> list;

        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    private static class AwardRecord {
        private Long babyUid;

        private Long userUid;

        private String babyNick;

        private String userNick;

        private String gift;

        private int tp;

        private long timestamp;

        private boolean bigAward;
    }

}
