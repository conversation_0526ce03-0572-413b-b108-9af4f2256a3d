package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.duowan.udb.util.GsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.*;
import com.yy.gameecology.activity.bean.actlayer.LayerBroadcastInfo;
import com.yy.gameecology.activity.bean.actlayer.LayerMemberItem;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.actlayer.OnlineChannelInfo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerMp4Config;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.bean.rankroleinfo.SubGuildRoleItem;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.client.thrift.FtsRecommendDataThriftClient;
import com.yy.gameecology.activity.client.thrift.FtsRoomManagerThriftClient;
import com.yy.gameecology.activity.client.yrpc.CurrencyClient;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.rolebuilder.impl.SubGuildRoleBuilder;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.service.layer.ActLayerInfoService;
import com.yy.gameecology.activity.service.layer.LayerSupport;
import com.yy.gameecology.common.bean.ChannelBaseInfo;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.LayerItemTypeKey;
import com.yy.gameecology.common.consts.LayerViewStatus;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.CommonPBOperateRequest;
import com.yy.gameecology.hdzj.bean.CommonPBOperateResp;
import com.yy.gameecology.hdzj.bean.PersonalTask;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.FireworkComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.Firework;
import com.yy.gameecology.hdzj.element.component.attr.bean.FireworkVo;
import com.yy.gameecology.hdzj.element.component.attr.bean.ShopItem;
import com.yy.gameecology.hdzj.element.component.attr.bean.ShopItemVo;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.protocol.pb.layer.LayerInfo;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.fts_base_info_bridge.SendHgameMessageReq;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.yy.thrift.hdztranking.RoleType.HALL;

@UseRedisStore
@Slf4j
@RestController
@RequestMapping("/5081")
public class FireworkComponent extends BaseActComponent<FireworkComponentAttr> implements LayerSupport {

    private static final String BRO_SEQ = "broSeq";

    private static final String FIRE_CHANNEL_HASH = "fire_channel_hash";

    private static final String FIRE_TASK_SET = "fire_task_set";

    private static final String FIRE_TASK_HASH = "fire_task_hash";

    private static final String LOTTERY_TASK_HASH = "lottery_task_hash:";

    private static final String CONSUME_USER_HASH = "consume_user_hash:";

    private static final String CLOSE_USER_HASH = "close_user_hash";

    private static final String SHOP_USER_ITEM_HASH = "shop_user_item_hash";

    private static final String APP_BRO_MP4 = "app_bro_mp4:";

    private static final String EXCHANGE_LOCK = "exchage_lock:";

    private static final String RETRY_SEQ = "retry_seq";

    private static final String FIRE_LEVEL_LOG = "fire_level_log";

    private static final String FIRE_CARD_LOG = "fire_card_log";

    private static final String LOTTERY_ITEM_LOG = "lottery_item_log";

    private static final String SHOP_ITEM_HASH = "shop_item_hash";

    private static final String LOTTERY_FIRE_COUNT = "lottery_fire_count:";

    private static final String ROBOT_TOKEN_PREFIX = "https://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=";

    private static final String FIREWORK_OP_TYPE = "firework_op";

    private static final String COMPLETE_SET_MGR_NOTIFY = "complete_set_mgr_notify_%s_%s";

    private static final long LOTTERY_BRO_BANNER_ID = 5081004L;

    private static final long SCREEN_BRO_BANNER_ID = 5081005L;

    private static final long PHOTO_BRO_BANNER_ID = 5081006L;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CurrencyClient currencyClient;

    @Autowired
    private ActLayerInfoService actLayerInfoService;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private FtsRecommendDataThriftClient ftsRecommendDataThriftClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private FtsRoomManagerThriftClient ftsRoomManagerThriftClient;

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    @Override
    public Long getComponentId() {
        return ComponentId.FIREWORK;
    }

    @Override
    public CommonPBOperateResp commonOperatePbRequest(CommonPBOperateRequest request) {
        log.info("commonOperatePbRequest:{}", JSONUtils.toJsonString(request));

        String opType = request.getOpType();
        if (!FIREWORK_OP_TYPE.equals(opType)) {
            log.warn("wrong request opType: {}", opType);
            return new CommonPBOperateResp(400, StringUtils.EMPTY, "unsupported opType");
        }

        final String fireId = request.getOpId();
        if (StringUtils.isEmpty(fireId)) {
            log.warn("wrong request with empty fireId");
            return new CommonPBOperateResp(400, StringUtils.EMPTY, "fireId is empty!");
        }

        FireworkComponentAttr attr = getComponentAttr(request.getActId(), request.getCmptIndex());
        if (attr == null) {
            return new CommonPBOperateResp(400, StringUtil.EMPTY, "component not exist!");
        }

        FireworkVo fireWork = getFireWorkByFireId(fireId, attr);
        if (fireWork == null) {
            log.warn("can not find firework with fireId:{}", fireId);
            return new CommonPBOperateResp(400, StringUtils.EMPTY, "cannot find firework with opId");
        }

        if (request.getOpUid() != fireWork.getAnchorId()) {
            log.error("wrong req uid is not the same op:{}, anchor:{}", request.getOpUid(), fireWork.getAnchorId());
            return new CommonPBOperateResp(401, StringUtils.EMPTY, "opUid and anchor not the same!");
        }

        String hashKey = fireId + "_" + request.getOpSid() + "_" + request.getOpSsid();
        boolean set = actRedisDao.hsetnx(getRedisGroupCode(attr.getActId()), makeKey(attr, BRO_SEQ), hashKey, DateUtil.getNowYyyyMMddHHmmss());
        if (set) {
            JSONObject jsonObject = JSON.parseObject(request.getExtjson());
            String url = jsonObject.getString("url");
            String text = jsonObject.getString("text");

            long broType = 2;
            Map<String, String> ext = Maps.newHashMap();
            ext.put("url", url);
            ext.put("text", text);
            UserBaseInfo userBaseInfo = commonService.getUserInfo(request.getOpUid(), true);
            ext.put("nick", userBaseInfo.getNick());
            commonBroadCastService.commonBannerBroadcast(request.getOpSid(), request.getOpSsid(), 0, Template.findByValue(attr.getBroTemplate()), broType
                    , attr.getActId(), Convert.toLong(request.getOpUid()), 0, PHOTO_BRO_BANNER_ID, 0L, ext);
        }

        return new CommonPBOperateResp(0, "", "");
    }

    @Override
    public long getActId() {
        return ComponentId.FIREWORK;
    }

    @Override
    public Map<String, Object> buildItemMemberExtInfo(long actId, LayerMemberItem layerMemberItem, Map<String, Object> ext) {
        // 过滤
        if (!LayerItemTypeKey.CMPT_ITEM.equals(layerMemberItem.getItemType())) {
            return ext;
        }

        Map<String, Object> extMap = new HashMap<>();
        if (LayerItemTypeKey.CMPT_ITEM.equals(layerMemberItem.getItemType())) {
            FireworkComponentAttr attr = tryGetUniqueComponentAttr(actId);
            final String member = layerMemberItem.getMemberId();
            Set<Long> tingRoles = attr.getTingRoleIds();
            if (CollectionUtils.isNotEmpty(tingRoles)) {
                EnrollmentInfo enrollmentInfo = enrollmentService.tryGetFirstEnrolMemberCache(actId, (long) attr.getBuisId(), layerMemberItem.getRoleType(), member);
                if (enrollmentInfo == null) {
//                    layerMemberItem.setState(ActorInfoStatus.NOT_IN);
//                    layerMemberItem.setViewStatus(LayerViewStatus.ERROR);
                    layerMemberItem.setMemberId(StringUtil.ZERO);
                    layerMemberItem.setViewStatus(LayerViewStatus.ERROR);
                    return ext;
                }

                long roleId = enrollmentInfo.getDestRoleId();
                if (!tingRoles.contains(roleId)) {
                    layerMemberItem.setMemberId(StringUtil.ZERO);
                    layerMemberItem.setViewStatus(LayerViewStatus.ERROR);
                    return ext;
                }
            }

            FireworkVo fireWork = getFireWorkByMemberId(member, attr);
            extMap.put("taskComplete", false);
            long sid = Convert.toLong(member.split("_")[0]);
            long ssid = Convert.toLong(member.split("_")[1]);
            FtsRoomMgrInfoVo mgrInfoVo = ftsRoomManagerThriftClient.ftsGetRoomMgrInfoByCh(member);
            if (mgrInfoVo != null) {
                layerMemberItem.setNickName(Base64.encodeBase64String(mgrInfoVo.getName().getBytes()));
            }
            SubGuildRoleBuilder rankBuilder = new SubGuildRoleBuilder();
            Map<String, SubGuildRoleItem> subGuildRoleItemMap = rankBuilder.buildRankByYy(Collections.singleton(member));
            if(StringUtils.isEmpty(layerMemberItem.getNickName())) {
            layerMemberItem.setNickName(Base64.encodeBase64String(subGuildRoleItemMap.get(member).getName().getBytes()));
            }
            // 交友则替换推荐图
            if (attr.getBuisId() == BusiId.MAKE_FRIEND.getValue()) {
                List<String> memberIds = Lists.newArrayList(member);
                Map<String, String> roleMemberPicMap = ftsRecommendDataThriftClient.batchGetRoomMgrPicByChannel(memberIds);
                // boss后台配置的推荐图,key:sid_ssid
                int businessType = MemberInfoService.changeToBusinessType(attr.getBuisId());
                Map<String, String> recommendPicMap = ftsRecommendDataThriftClient.batchGetRecommendConfigPicture(List.copyOf(memberIds), businessType);
                layerMemberItem.setLogo(subGuildRoleItemMap.get(member).getAvatarInfo());
                if(recommendPicMap.containsKey(member)) {
                    layerMemberItem.setLogo(recommendPicMap.get(member));
                }
                if(roleMemberPicMap.containsKey(member)) {
                    layerMemberItem.setLogo(roleMemberPicMap.get(member));
                }
            }
            Date nowVir = commonService.getNow(attr.getActId());
            String hourStr = DateUtil.format(nowVir, DateUtil.PATTERN_TYPE7);
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, attr.getTaskRankId(), attr.getTaskPhaseId(), hourStr, 0, member, null);
            long score = CollectionUtils.isEmpty(ranks) ? 0 : ranks.get(0).getScore();
            score = Math.max(0, score);
            long targetScore = 0;
            for (Long attrScore : attr.getScores()) {
                if (score < attrScore) {
                    targetScore = attrScore;
                    break;
                }
            }
            if (score >= attr.getScores().get(attr.getScores().size() - 1)) {
                targetScore = attr.getScores().get(attr.getScores().size() - 1);
            }

            PersonalTask personalTask = new PersonalTask();
            personalTask.setMyScore(score);
            personalTask.setMyTarget(targetScore);
            personalTask.setFinish(score >= targetScore);
            extMap.put("personalTask", personalTask);

            if (fireWork != null) {
                long cTime = fireWork.getEndTime() - (long) attr.getValidSec() * 1000;
                Date now = new Date();
                long playTime = (cTime + 15 * 1000 - now.getTime()) / 1000;
                if (playTime > 0) {
                    extMap.put("fireTp", fireWork.getFid());
                    extMap.put("word", fireWork.getName());
                    extMap.put("playTime", playTime);
                    extMap.put("taskComplete", true);
                }
                extMap.put("anchors", Collections.singleton(fireWork.getAnchorId()));
            }

            if(!extMap.containsKey("anchors")) {
                OnlineChannelInfo onlineChannelInfo = onlineChannelService.get(sid, ssid);
                if(onlineChannelInfo != null) {
                    extMap.put("anchors", onlineChannelInfo.getEffectAnchorId());
                }
            }

            return extMap;
        }

        return null;
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, FireworkComponentAttr attr) {
        if (attr.getTaskRankId() != event.getRankId() || attr.getTaskPhaseId() != event.getPhaseId()) {
            log.info("processTaskProgressChanged rankId or phaseId not fit event:{}", JSON.toJSONString(event));
            return;
        }

        log.info("processTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
        long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
        if (startIndex == currIndex) {
            return;
        }

        String member = event.getMember();
        String[] sidAndSsid = member.split("_");
        final long actId = attr.getActId();
        final long sid = Convert.toLong(sidAndSsid[0]), ssid = Convert.toLong(sidAndSsid[1]);
        //replace anchorUid
        FtsRoomMgrInfoVo roomMgrInfo = null;
        if(attr.isToRoomMgr()) {
            roomMgrInfo = ftsRoomManagerThriftClient.ftsGetRoomMgrInfoByCh(member);
        }
        final long anchorUid = roomMgrInfo != null ? roomMgrInfo.getMgrUid() : getAnchorUid(attr.getAnchorRoleId(), event.getActors());
        final Map<Long, Firework> fireWorkMap = attr.getFireWorkList().stream()
                .collect(Collectors.toMap(Firework::getPackageId, fireWork -> fireWork));

        final Date now = commonService.getNow(actId);
        String eKey = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();

        List<Map<String, Object>> extList = new ArrayList<>();
        final String groupCode = getRedisGroupCode(actId);

        long playTime = -1;
        for (long i = startIndex; i < currIndex; i++) {
            String dateKey = DateUtil.format(now, DateUtil.PATTERN_TYPE7);

            final String fireId = event.getMember() + "_" + dateKey + "_" + i, seq = eKey + i;
            log.info("onTaskProgressChanged level:{}, seq:{}", i, seq);
            BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(seq, attr.getBuisId(), anchorUid, attr.getFireLotteryTaskId(), 3);
            if (result.getCode() != 0 || result.getRecordPackages().isEmpty()) {
                log.error("lottery error uid:{}, fireId:{}, ret:{}", anchorUid, fireId, JsonUtil.toJson(result));
                continue;
            }
            Map<Long, Long> recordIds = result.getRecordPackages();
            final long packageId = recordIds.values().stream().findFirst().orElse(0L);
            Firework firework = fireWorkMap.get(packageId);
            if (firework == null) {
                log.warn("can not lottery a firework with packageId:{}", packageId);
                continue;
            }

            String fireLogHourKey = FIRE_LEVEL_LOG + "_" + dateKey + "_"+ i;
            String fireLogAllKey = FIRE_LEVEL_LOG + "_"+ i;

            final String fireCardLogHourKey = FIRE_CARD_LOG + "_" + dateKey,
                    fireCardLogAllKey = FIRE_CARD_LOG;

            actRedisDao.hIncrByKey(groupCode, makeKey(attr, fireLogHourKey),  member, 1);
            actRedisDao.hIncrByKey(groupCode, makeKey(attr, fireLogAllKey),  member, 1);
            actRedisDao.hIncrByKey(groupCode, makeKey(attr, fireCardLogHourKey),  firework.getFid() + "", 1);
            actRedisDao.hIncrByKey(groupCode, makeKey(attr, fireCardLogAllKey),  firework.getFid() + "", 1);

            FireworkVo fireWork = new FireworkVo(firework);
            fireWork.setFireId(fireId);
            fireWork.setEndTime(System.currentTimeMillis() + (long) attr.getValidSec() * 1000);
            fireWork.setAnchorId(Convert.toLong(anchorUid));

            if(actRedisDao.hsetnx(groupCode, makeKey(attr, RETRY_SEQ),  seq, StringUtil.ONE)) {
                actRedisDao.hset(groupCode, makeKey(attr, FIRE_CHANNEL_HASH), event.getMember(), JsonUtil.toJson(fireWork));
                actRedisDao.zAdd(groupCode, makeKey(attr, FIRE_TASK_SET), fireId, fireWork.getEndTime());
                actRedisDao.hset(groupCode, makeKey(attr, FIRE_TASK_HASH), fireId, JsonUtil.toJson(fireWork));

                //抽奖广播
                Map<String, Object> ext = Maps.newHashMap();
                ext.put("fireId", fireId);
                ext.put("word", fireWork.getName());
                ext.put("playTime", 120);
                ext.put("fireTp", fireWork.getFid());
                ext.put("canDraw", "1");
                extList.add(ext);

                long broType = attr.getBroConfig().get((int) i + 1);
                ext = Maps.newHashMap();
                MemberInfo memberInfo = memberInfoService.getMemberInfo((long) attr.getBuisId(), HALL, member);
                String text = String.format(attr.getScreenText(), fireWork.getName(), memberInfo.getName());
                ext.put("text", text);
                ext.put("nick", memberInfo.getName());
                ext.put("logo", memberInfo.getHdLogo());

                // 给挂件的
                commonBroadCastService.commonBannerBroadcast(sid, ssid, 0, Template.findByValue(attr.getBroTemplate()), broType
                        , attr.getActId(), anchorUid, event.getRankScore(), SCREEN_BRO_BANNER_ID, 0L, ext);

                UserBaseInfo userInfo = commonService.getUserInfo(anchorUid, false);
                if (attr.getCanPhoto() > 0) {
                    //拍照单播
                    ext = Maps.newHashMap();
                    ext.put("fireId", fireId);
                    ext.put("fireTp", firework.getFid());
                    ext.put("time", DateUtil.format(new Date(), "yyyy.MM.dd HH:mm"));
                    ext.put("giftIcon", fireWork.getLogo());
                    ext.put("nick", userInfo.getNick());
                    ext.put("logo", userInfo.getLogo());
                    // 拍照的
                    commonBroadCastService.commonNoticeUnicast(attr.getActId(), "fireworkPhoto", JsonUtil.toJson(ext), StringUtils.EMPTY, anchorUid);
                }
            }
            if(attr.isExcludeDanmaku()) {
                List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
                Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
                log.info("danmuku channel:{}, member:{}", exclude, member);
                if(exclude.contains(member)) {
                    log.info("danmuku channel do not bro, member:{}", member);
                    playTime = 0;
                }
            }

            if(i == currIndex - 1) {
                refreshLayer(attr, now, sid, ssid);
                if(playTime != 0) {
                    broMp42App(attr.getActId(), seq, fireWork.getJyBusiness(), 1, sid, ssid, new ArrayList<>(), fireWork.getMp4Url(), fireWork.getBroLevel());
                }
            }

            // 加个延迟防止照片和弹窗对不上
            SysEvHelper.waiting(2000);
        }

        SysEvHelper.waiting(4000);

        for (Map<String, Object> map : extList) {
            //弹幕游戏不允许播特效。
            if(playTime == 0L) {
                map.put("playTime", 0);
            }
            // 卡片抽奖 + 烟花（侧边的烟花）
            commonBroadCastService.commonBannerBroadcast(sid, ssid, 0, Template.findByValue(attr.getBroTemplate()), 2
                    , attr.getActId(), anchorUid, event.getRankScore(), LOTTERY_BRO_BANNER_ID, 0L, map);
            SysEvHelper.waiting(300);
        }

        // 发交友房管厅管理后台（hgame）消息
        if(roomMgrInfo != null) {
            Map<String, Long> balanceMap = getFireWorks(anchorUid, attr);
            int minAmount = Integer.MAX_VALUE;
            for (Firework firework : attr.getFireWorkList()) {
                int amount = balanceMap.getOrDefault(firework.getCid(), 0L).intValue();
                minAmount = Math.min(amount, minAmount);
            }
            List<Integer> completeList = attr.getJyMgrMsgMap().keySet().stream().sorted(new Comparator<Integer>() {
                @Override
                public int compare(Integer o1, Integer o2) {
                    return o1 - o2;
                }
            }).toList();;
            int needComplete = 0;
            for (Integer cSet : completeList) {
                if(minAmount >= cSet) {
                    needComplete = cSet;
                }
            }
            if(attr.getJyMgrMsgMap().containsKey(needComplete)) {
                String sendKey = String.format(COMPLETE_SET_MGR_NOTIFY, needComplete, anchorUid);
                if(actRedisDao.setNX(groupCode, makeKey(attr, sendKey), needComplete+"")) {
                    SendHgameMessageReq sendHgameMessageReq = new SendHgameMessageReq();
                    sendHgameMessageReq.setSeqID(eKey);
                    sendHgameMessageReq.setUid(anchorUid);
                    sendHgameMessageReq.setTitle(attr.getJyMgrMsgMap().get(needComplete).getTitle());
                    sendHgameMessageReq.setContent(String.format(attr.getJyMgrMsgMap().get(needComplete).getMsg(), roomMgrInfo.getName()));
                    sendHgameMessageReq.setTimestamp(System.currentTimeMillis()/1000);
                    ftsBaseInfoBridgeClient.sendHgameMessage(sendHgameMessageReq);
                }
            }
        }
    }

    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, FireworkComponentAttr attr) {
        long uid = event.getUid(), actId = attr.getActId(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        log.info("uid:{}, enterTemplate event:{}", uid, JSONUtils.toJsonString(event));
        boolean app = StringUtil.isNotBlank(extJson) && extJson.contains("app");

        final String member = sid + "_" + ssid;
        final String groupCode = getRedisGroupCode(actId);
        FireworkVo fireWork = getFireWorkByMemberId(member, attr);
        Date now = new Date();
        if (fireWork != null && fireWork.getEndTime() > now.getTime()) {
            String closeFireId = actRedisDao.hget(groupCode, CLOSE_USER_HASH, uid + "");
            long playTime = StringUtil.isNotBlank(closeFireId) &&
                    closeFireId.equals(fireWork.getFireId()) ? 0 : (fireWork.getEndTime() - now.getTime())/1000;
            log.info("enter channel uid:{}, closeFireId:{}, playTime:{}", uid, closeFireId, playTime);
            boolean canDraw = StringUtil.isEmpty(actRedisDao.hget(groupCode, makeKey(attr, LOTTERY_TASK_HASH + uid), fireWork.getFireId()));
            Map<String, String> ext = Maps.newHashMap();
            ext.put("fireTp", String.valueOf(fireWork.getFid()));
            ext.put("fireId", fireWork.getFireId());
            ext.put("word", fireWork.getName());
            ext.put("playTime", playTime + "");
            ext.put("canDraw", canDraw ? "1" : "0");

            if(attr.isExcludeDanmaku()) {
                List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
                Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
                log.info("danmuku channel:{}, member:{}", exclude, member);
                if(exclude.contains(member)) {
                    log.info("danmuku channel do not bro, member:{}", member);
                    return;
                }
            }

            if(!app) {
                commonBroadCastService.commonNoticeUnicast(attr.getActId(), "firework", JsonUtil.toJson(ext), StringUtils.EMPTY, uid);
            }

            if (app) {
                boolean hasBro = actRedisDao.zSetNX(groupCode, makeKey(attr, APP_BRO_MP4+fireWork.getFireId()), uid+"", 30*86400);
                if(hasBro) {
                    broMp42App(attr.getActId(), fireWork.getFireId() + "_"+uid, fireWork.getJyBusiness(), 6, sid, ssid,
                            Lists.newArrayList(uid), fireWork.getMp4Url(), fireWork.getBroLevel());
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        log.error("enterchannel error e:{}", ExceptionUtils.getStackTrace(e));
                    }
                    commonBroadCastService.commonNoticeUnicast(attr.getActId(),
                            "firework", JsonUtil.toJson(ext), StringUtils.EMPTY, uid);
                }
            }
        }
    }

    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent event, FireworkComponentAttr attr) {
        log.info("onSendGiftEvent:{}", event);
        if (!actInfoService.inActTime(attr.getActId())) {
            return;
        }
        String giftId = event.getGiftId();
        Long sid = event.getSid();
        if (attr.getGiftConfigs().containsKey(giftId)) {
            long val = attr.getGiftConfigs().get(giftId);
            actRedisDao.hIncrByKey(getRedisGroupCode(attr.getActId()),
                    makeKey(attr, CONSUME_USER_HASH + event.getSendUid()), String.valueOf(sid), val);
        }
    }

    @RequestMapping("/show")
    public Response<Boolean> show(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        FireworkComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }
        List<String> cids = attr.getFireWorkList().stream()
                .map(Firework::getCid)
                .collect(Collectors.toList());
        Map<String, Long> balances = currencyClient.balance(uid, 1, cids);
        //long sid = signedService.getSignedSidByBusiId(uid, attr.getBuisId());
        return Response.success(balances != null && !balances.isEmpty());
    }

    @RequestMapping("/listFireAmount")
    public Response<FireWorkResp> listFireAmount(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        FireworkComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        try {
            Map<String, Long> balanceMap = getFireWorks(uid, attr);
            List<FireworkVo> fireWorks = new ArrayList<>();
            int minAmount = Integer.MAX_VALUE;
            for (Firework firework : attr.getFireWorkList()) {
                FireworkVo fireWork = new FireworkVo(firework);
                if (balanceMap.containsKey(fireWork.getCid())) {
                    fireWork.setAmount(balanceMap.getOrDefault(fireWork.getCid(), 0L));
                }
                minAmount = fireWork.getAmount() < minAmount ? (int) fireWork.getAmount() : minAmount;
                fireWorks.add(fireWork);
            }
            FireWorkResp fireWorkResp = new FireWorkResp();
            fireWorkResp.setAmounts(fireWorks);
            fireWorkResp.setCompleteSet(minAmount);
            UserBaseInfo userBaseInfo = commonService.getUserInfo(uid, false);
            fireWorkResp.setNick(userBaseInfo.getNick());
            fireWorkResp.setAvatar(userBaseInfo.getLogo());
            return Response.success(fireWorkResp);
        } catch (Exception e) {
            return Response.fail(500, "系统繁忙，请稍后再试！");
        }
    }

    @RequestMapping("/listItems")
    public Response<ItemsResp> listItems(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        FireworkComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        ItemsResp itemsResp = new ItemsResp();
        try {
            List<ShopItemVo> shopItemVos = new ArrayList<>(attr.getShopItems().size());
            List<Firework> fireworks = attr.getFireWorkList();
            Map<Object, Object> consumeMap = actRedisDao.hGetAll(redisConfigManager.getGroupCode(actId),
                    makeKey(attr, SHOP_ITEM_HASH));
            for (ShopItem shopItem : attr.getShopItems()) {
                ShopItemVo shopItemVo = new ShopItemVo(shopItem);
                int consume = Convert.toInt(consumeMap.getOrDefault(shopItem.getSid()+"", 0));
                // 返回小于 0 == 没有限制
                long remain = shopItem.getTotalAmount() >= 0 ? Math.max(0, shopItem.getTotalAmount() - consume) : shopItem.getTotalAmount();
                shopItemVo.setAmount(remain);
                JSONObject priceConfig = JSONObject.parseObject(shopItem.getPriceJson());
                List<FireworkVo> fireworkVos = new ArrayList<>();
                for (Firework firework : fireworks) {
                    long price = Convert.toLong(priceConfig.getOrDefault(firework.getCid(), 0L));
                    FireworkVo fireworkVo = new FireworkVo(firework);
                    fireworkVo.setAmount(price);
                    fireworkVos.add(fireworkVo);
                }

                if (!StringUtil.isEmpty(shopItem.getCid())) {
                    long amount = Convert.toLong(actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                            makeKey(attr, SHOP_USER_ITEM_HASH), uid + "_" + shopItem.getSid()));

                    // 小于0 == 没有限制
                    shopItemVo.setCanExchange(shopItem.getUserLimit() < 0 || amount < shopItem.getUserLimit());
                    shopItemVo.setExchangeNum((int)amount);
                }
                shopItemVo.setFires(fireworkVos);
                shopItemVos.add(shopItemVo);
            }
            itemsResp.setItems(shopItemVos);
            return Response.success(itemsResp);
        } catch (Exception e) {
            return Response.fail(500, "系统繁忙，请稍后再试！");
        }
    }

    @RequestMapping("/listFireChannel")
    public Response<FireChannelResp> listFireChannel(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        FireworkComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        FireChannelResp resp = new FireChannelResp();

        long min = System.currentTimeMillis() - ((long)attr.getValidSec() +10) * 1000L;
        final String groupCode = getRedisGroupCode(actId);
        try {
            Set<ZSetOperations.TypedTuple<String>> set =
                    actRedisDao.zrevRangeByScoreWithScore(groupCode, makeKey(attr, FIRE_TASK_SET), min, Long.MAX_VALUE, 1000);

            if (CollectionUtils.isEmpty(set)) {
                resp.setChannels(Collections.emptyList());
                return Response.success(resp);
            }

            Set<String> memberIds = new HashSet<>(set.size());
            Set<Long> sids = new HashSet<>(set.size());
            List<ChannelVo> channels = new ArrayList<>(set.size());
            Map<String, FtsRoomMgrInfoVo> roomMgrInfoMap = ftsRoomManagerThriftClient.ftsBatchGetRoomMgrInfoByCh(new ArrayList<>(memberIds));
            for (ZSetOperations.TypedTuple<String> tuple : set) {
                String member = tuple.getValue();
                if (StringUtils.contains(member, StringUtil.UNDERSCORE)) {
                    ChannelVo channelVo = new ChannelVo();
                    if(roomMgrInfoMap!=null && roomMgrInfoMap.containsKey(member) && StringUtils.isNotEmpty(roomMgrInfoMap.get(member).getName())) {
                        channelVo.setNick(roomMgrInfoMap.get(member).getName());
                    }
                    String[] ssidArr = member.split(StringUtil.UNDERSCORE);
                    final long sid = Convert.toLong(ssidArr[0]), ssid = Convert.toLong(ssidArr[1]);
                    String memberId = sid + StringUtil.UNDERSCORE + ssid;
                    channelVo.setSid(sid);
                    channelVo.setSsid(ssid);
                    sids.add(sid);
                    if (!memberIds.contains(memberId)) {
                        memberIds.add(memberId);
                        channels.add(channelVo);
                    }
                }
            }

            // 交友则替换推荐图
            if (attr.getBuisId() == BusiId.MAKE_FRIEND.getValue()) {
                Map<String, String> roleMemberPicMap = ftsRecommendDataThriftClient.batchGetRoomMgrPicByChannel(memberIds);
                // boss后台配置的推荐图,key:sid_ssid
                int businessType = MemberInfoService.changeToBusinessType(attr.getBuisId());
                Map<String, String> recommendPicMap = ftsRecommendDataThriftClient.batchGetRecommendConfigPicture(List.copyOf(memberIds), businessType);
                for (ChannelVo channel : channels) {
                    String member = channel.getSid() + "_" + channel.getSsid();
                    if(recommendPicMap.containsKey(member)) {
                        channel.setAvatar(recommendPicMap.get(member));
                    }
                    if(roleMemberPicMap.containsKey(member)) {
                        channel.setAvatar(roleMemberPicMap.get(member));
                    }
                }
            }

            SubGuildRoleBuilder rankBuilder = new SubGuildRoleBuilder();
            Map<String, SubGuildRoleItem> subGuildRoleItemMap = rankBuilder.buildRankByYy(memberIds);
            Map<Long, ChannelBaseInfo> channelBaseInfoMap = commonService.getChannelInfos(List.copyOf(sids), false);
            for (ChannelVo channel : channels) {
                long sid = channel.getSid();
                long ssid = channel.getSsid();
//                ChannelBaseInfo channelBaseInfo = commonService.getChannelInfo(sid, false);
                ChannelBaseInfo channelBaseInfo = channelBaseInfoMap.get(sid);
                if(sid != ssid) {
                    String sidSsid = sid + "_" + ssid;
                    if (subGuildRoleItemMap.containsKey(sidSsid) && StringUtils.isEmpty(channel.getNick())) {
                        SubGuildRoleItem subGuildRoleItem = subGuildRoleItemMap.get(sidSsid);
                        channel.setNick(subGuildRoleItem.getName());
                    }
                } else if (channelBaseInfo != null && StringUtils.isEmpty(channel.getNick())) {
                    channel.setNick(channelBaseInfo.getName());
                }

                if(channelBaseInfo != null && StringUtil.isEmpty(channel.getAvatar())) {
                    channel.setAvatar(channelBaseInfo.getLogo());
                }
            }
            resp.setChannels(channels);
            return Response.success(resp);
        } catch (Exception e) {
            log.error("listFireChannel exception:" , e);
            return Response.fail(500, "系统繁忙，请稍后再试！");
        }
    }

    @RequestMapping("/exchange")
    public Response<?> exchange(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam(name = "id") final int id) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        FireworkComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(403, "不在活动时间内");
        }

        Map<Integer, ShopItem> idToShopItemMap = attr.getShopItems().stream()
                .collect(Collectors.toMap(ShopItem::getSid, item -> item));
        final ShopItem shopItem = idToShopItemMap.get(id);
        if (shopItem == null) {
            log.info("exchange shop item is null id:{}", id);
            return Response.fail(1, "参数异常");
        }

        final String groupCode = getRedisGroupCode(actId);

        List<String> cids = attr.getFireWorkList().stream()
                .map(Firework::getCid)
                .collect(Collectors.toList());

        String lockName = makeKey(attr, EXCHANGE_LOCK + id);
        Secret lock = null;
        String seq = attr.getActId() + "_" + System.currentTimeMillis() + "_" + uid + "_" + id;
        seq = MD5SHAUtil.getMD5(seq);
        try {
            lock = locker.lock(lockName, 5, seq, 5);
            if (lock != null) {
                // 总消耗数
                if (shopItem.getTotalAmount() >= 0) {
                    int consume = Convert.toInt(actRedisDao.hget(groupCode, makeKey(attr, SHOP_ITEM_HASH), id + ""));
                    if (shopItem.getTotalAmount() - consume <= 0) {
                        return Response.fail(1, "礼物已兑换完");
                    }
                }

                // 余额
                Map<String, Long> cidCosts = new HashMap<>();
                Map<String, Long> balanceMap = currencyClient.balance(uid, 1, cids);
                log.info("exchange balance id:{}, uid:{}, balanceMap:{}", id, uid, JsonUtil.toJson(balanceMap));
                if (balanceMap.isEmpty()) {
                    return Response.success(2, "余额不足");
                }

                // 个人消耗数
                if (shopItem.getUserLimit() >= 0) {
                    int exchangeNum = Convert.toInt(actRedisDao.hget(groupCode, makeKey(attr, SHOP_USER_ITEM_HASH), uid + "_" + id));
                    if (exchangeNum > shopItem.getUserLimit()) {
                        log.info("exchange id:{}, item can only exchange once per user uid:{}, exchangeRedisNum:{}", id, uid, exchangeNum);
                        return Response.success(1, "兑换超出上限");
                    }
                }

                JSONObject priceConfig = JSONObject.parseObject(shopItem.getPriceJson());
                for (String cid : priceConfig.keySet()) {
                    long balance = Convert.toInt(balanceMap.getOrDefault(cid, 0L));
                    long amount = priceConfig.getLongValue(cid);
                    if(amount > balance) {
                        log.info("user doesnt have enough money, uid:{}, cid:{}, map:{}", uid, shopItem.getCid(), JsonUtil.toJson(balanceMap));
                        return Response.success(2, "余额不足");
                    }
                    cidCosts.put(cid, priceConfig.getLongValue(cid));
                }

                int ret = currencyClient.consume(uid, 1, cidCosts, seq);
                if (ret == 1) {
                    BatchWelfareResult batchWelfareResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), uid,
                            shopItem.getTaskId(), 1, shopItem.getPackageId(), seq, 3);
                    final boolean suc = batchWelfareResult != null && batchWelfareResult.getCode() == 0;
                    if(!suc) {
                        // 需要人工介入处理
                        log.error("actId:{}, uid:{}, seq:{}, shopId:{} exchange error ret:{}", actId, uid, seq, id, batchWelfareResult);
                    }
                    log.info("get reward from app login doWelfare result={}, seq:{}", batchWelfareResult, seq);
                    actRedisDao.hIncrByKey(groupCode, makeKey(attr, SHOP_ITEM_HASH), id + "", 1);
                    actRedisDao.hIncrByKey(groupCode, makeKey(attr, SHOP_USER_ITEM_HASH), uid + "_" + id, 1);
                    if(shopItem.getAlert() > 0) {
                        sendNotice("活动id:" + attr.getActId() + ", 用户【" + uid + "】正在兑换【" + shopItem.getTitle() +"】", attr);
                    }
                    return Response.success(shopItem.getSuccessCode(), "发放成功");
                } else {
                    currencyClient.rollback(uid, seq, 1);
                    log.error("consume currency error seq:{}, uid:{}, cid :{}, ret:{}", seq, uid, id, ret);
                    return Response.success(1, "系统繁忙，请稍后再试！");
                }
            }
            return Response.success(1, "网络超时，请重试！");
        } catch (Exception e) {
            log.error("exchange excepiton@uid:{}, id:{}, e:{}",
                    uid, id, ExceptionUtils.getStackTrace(e));
            return Response.success(1, "请求过快");
        } finally {
            if (lock != null) {
                locker.unlock(lockName, lock);
            }
        }
    }

    @RequestMapping("/lottery")
    public Response<LotteryResp> lottery(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam(name = "fireId") String fireId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        FireworkComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }
        final String groupCode = getRedisGroupCode(actId);

        if (!actInfoService.inActTime(actId)) {
            return Response.fail(403, "不在活动时间内");
        }

        //是否抽过了
        boolean hasDraw = actRedisDao.hsetnx(groupCode, makeKey(attr, LOTTERY_TASK_HASH + uid), fireId, 1 + "");
        if (!hasDraw) {
            log.info("lottery hasDraw uid:{}, fireId:{}", uid, fireId);
            return Response.fail(1, "已经抽过了");
        }

        long lotteryCount = actRedisDao.incrValue(groupCode, makeKey(attr, LOTTERY_FIRE_COUNT+fireId), 1);

        LotteryResp lotteryResp = new LotteryResp();
        try {
            long consumeSize = actRedisDao.getRedisTemplate(groupCode).opsForHash().size(makeKey(attr, CONSUME_USER_HASH + uid));
            final long reverseLotteryTaskId;
            if (consumeSize <= 0) {
                reverseLotteryTaskId = attr.getCardReverseLotteryTasks().get(1);
            } else {
                reverseLotteryTaskId = attr.getCardReverseLotteryTasks().get(0);
            }

            boolean lottery = false;
            String seq = MD5SHAUtil.getMD5(uid + "_" + fireId + "_reverse");
            log.info("doBatchLottery hasDraw uid:{}, fireId:{}, seq:{}", uid, fireId, seq);
            BatchLotteryResult reverseResult = hdztAwardServiceClient.
                    doBatchLottery(seq, attr.getBuisId(), uid, reverseLotteryTaskId, 3);
            if(reverseResult.getCode() != 0) {
                log.error("reverse lottery error uid:{}, fireId:{}, ret:{}", uid, fireId, JsonUtil.toJson(reverseResult));
            } else {
                Map<Long, Long> reverseRecordIds = reverseResult.getRecordPackages();
                for (Long pid : reverseRecordIds.values()) {
                    if(attr.getCardReverseAwardPackage().contains(pid)) {
                        lottery = true;
                        break;
                    }
                }
            }

            if(lotteryCount > attr.getLotteryLimit()) {
                log.info("lottery warn uid:{}, count:{}, limit:{}, fireId:{}", uid, lotteryCount, attr.getLotteryLimit(), fireId);
                lottery = false;
            }

            FireworkVo fireworkVo = getFireWorkByFireId(fireId, attr);
            if (fireworkVo == null) {
                return Response.fail(1, "非法请求");
            }
            long endTime = actRedisDao.zscore(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, FIRE_TASK_SET), fireId);
            int fid = fireworkVo.getFid();
            if (endTime == 0 || endTime < System.currentTimeMillis()) {
                log.info("fire work is expire uid:{}, fireWork:{}", uid, JsonUtil.toJson(fireworkVo));
                int cardTp = getRandomExcludeFid(attr.getFireWorkList(), fid);

                lotteryResp.setAward(false);
                lotteryResp.setCardTp(cardTp);
                lotteryResp.setItems(Collections.emptyList());

                actRedisDao.hset(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, LOTTERY_TASK_HASH + uid), fireId, GsonUtil.toJson(lotteryResp) + " 抽奖超时");
                return Response.success(lotteryResp);
            }

            lotteryResp.setAward(lottery);
            List<String> items = new ArrayList<>();
            //发奖
            if (lottery) {
                //抽奖服务
                lotteryResp.setCardTp((int) fid);
                String lotterySeq = MD5SHAUtil.getMD5(uid + "_" + fireId);
                log.info("doBatchLottery uid:{}, fid:{}, seq:{}", uid, fireId, lotterySeq);
                BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(lotterySeq, attr.getBuisId(), uid, attr.getLotteryTaskId(), 3);
                if (result.getCode() != 0) {
                    log.error("lottery error uid:{}, fireId:{}, ret:{}", uid, fireId, JsonUtil.toJson(result));
                    int cardTp = getRandomExcludeFid(attr.getFireWorkList(), fid);
                    lotteryResp.setCardTp(cardTp);
                    lotteryResp.setItems(items);
                    return Response.success(lotteryResp);
                }
                Map<Long, AwardModelInfo> infoMap = packageInfoMap(attr);
                Map<Long, Long> recordIds = result.getRecordPackages();
                for (Long pid : recordIds.values()) {
                    AwardModelInfo awardModelInfo = infoMap.get(pid);
                    items.add(awardModelInfo.getPackageName());
                }
                actRedisDao.hIncrByKey(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, LOTTERY_ITEM_LOG), JsonUtil.toJson(items.get(0)), 1);
                lotteryResp.setItems(items);
                log.info("lottery user award uid:{}, id:{}, fid:{}, items:{}", uid, lotteryResp.getCardTp(), fid, JsonUtil.toJson(items));
            } else {
                int cardTp = getRandomExcludeFid(attr.getFireWorkList(), fid);
                lotteryResp.setCardTp(cardTp);
                lotteryResp.setItems(items);
                log.info("lottery user doesnt award uid:{}, id:{}, fid:{}", uid, lotteryResp.getCardTp(), fid);
            }
            actRedisDao.hset(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, LOTTERY_TASK_HASH + uid), fireId, GsonUtil.toJson(lotteryResp));
        } catch (Exception e) {
            log.error("lottery excepiton@uid:{}, fireId:{}, e:{}",
                    uid, fireId, ExceptionUtils.getStackTrace(e));
            return Response.fail(1, "系统繁忙，请稍后再试！");
        }
        return Response.success(lotteryResp);
    }

    /**
     * 关掉侧边的烟花
     *
     * @param actId
     * @param cmptIndex
     * @param fireId
     * @return
     */
    @RequestMapping("/close")
    public Response<?> closePlay(@RequestParam("actId") long actId, @RequestParam("cmptIndex") long cmptIndex, @RequestParam(name = "fireId") String fireId) {
        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "未登录");
        }
        FireworkComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }


        log.info("uid:{} close fireId:{} play", uid, fireId);
        actRedisDao.hset(redisConfigManager.getGroupCode(attr.getActId()), CLOSE_USER_HASH, uid + "", fireId);
        return Response.success(null);
    }

    public long getAnchorUid(List<Long> anchorRoleIds, Map<Long, String> actors) {
        if (CollectionUtils.isEmpty(anchorRoleIds) || MapUtils.isEmpty(actors)) {
            log.warn("getAnchorUid fail anchorRoleIds is null or actors is null: {} {}", anchorRoleIds, actors);
            return 0;
        }

        for (long anchorRoleId : anchorRoleIds) {
            if (StringUtils.isNumeric(actors.get(anchorRoleId))) {
                return Long.parseLong(actors.get(anchorRoleId));
            }
        }

        return 0;
    }

    public void broMp42App(long actId, String seq, int business, int bcType, long sid,
                           long ssid, List<Long> uids, String mp4Url, int broLevel) {
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(actId, seq, business,
                bcType, sid, ssid, "",
                Lists.newArrayList());
        appBannerEvent.setUid(0);
        appBannerEvent.setUidList(Lists.newArrayList());
        appBannerEvent.setContentType(5);
        appBannerEvent.setPushUidlist(uids);
        AppBannerMp4Config appBannerMp4Config = new AppBannerMp4Config();
        appBannerMp4Config.setUrl(mp4Url);
        appBannerMp4Config.setLevel(broLevel);
        appBannerEvent.setMp4Config(appBannerMp4Config);
        kafkaService.sendAppBannerKafka(appBannerEvent);
    }

    private LayerMemberItem getCmptLayerMemberItem(LayerBroadcastInfo layerBroadcastInfo) {
        if (layerBroadcastInfo == null || CollectionUtils.isEmpty(layerBroadcastInfo.getExtMemberItem())) {
            return null;
        }

        return layerBroadcastInfo.getExtMemberItem().stream()
                .filter(layerMemberItem -> LayerItemTypeKey.CMPT_ITEM.equals(layerMemberItem.getItemType()))
                .findAny().orElse(null);
    }

    private void refreshLayer(FireworkComponentAttr attr, Date now, long sid, long ssid) {
        LayerBroadcastInfo layerBroadcastInfo = actLayerInfoService.buildLayerInfo(attr.getActId(), sid, ssid, now, "manual");
        LayerMemberItem memberItem = getCmptLayerMemberItem(layerBroadcastInfo);
        if (memberItem == null) {
            log.warn("layerBroadcastInfo illegal null value:{}", layerBroadcastInfo);
            return;
        }

        Map<String, Object> ext = memberItem.getExt();
        if (ext == null) {
            ext = new HashMap<>(8);
            memberItem.setExt(ext);
        }

        LayerInfo.LayerBroadcast broadcastInfo = layerBroadcastInfo.toBroadcastInfoPb();
        log.info("broadcast layer info with ext:{}", ext);

        //广播信息
        GameecologyActivity.GameEcologyMsg msg =
                GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.LayerBroadcast_VALUE)
                        .setLayerBroadcast(broadcastInfo).build();
        if (ssid != 0) {
            svcSDKService.broadcastSub(sid, ssid, msg);
        }
        //顶级频道广播
        else {
            log.warn("ssid is empty");
        }
    }

    public FireworkVo getFireWorkByMemberId(String memberId, FireworkComponentAttr attr) {
        String fireWorkStr = actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, FIRE_CHANNEL_HASH), memberId);
        if (fireWorkStr == null) {
            return null;
        }
        FireworkVo fireWork = JsonUtil.toObject(fireWorkStr, FireworkVo.class);
        if (fireWork == null || fireWork.getEndTime() - System.currentTimeMillis() <= 0) {
            return null;
        }

        return fireWork;
    }

    public FireworkVo getFireWorkByFireId(String fireId, FireworkComponentAttr attr) {
        String jsonStr = actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, FIRE_TASK_HASH), fireId);
        if (StringUtils.startsWith(jsonStr, StringUtil.OPEN_BRACE)) {
            return JSON.parseObject(jsonStr, FireworkVo.class);
        }

        return null;
    }

    public Map<String, Long> getFireWorks(long uid, FireworkComponentAttr attr) {
        List<String> cids = attr.getFireWorkList().stream()
                .map(Firework::getCid)
                .collect(Collectors.toList());
        Map<String, Long> balance = currencyClient.balance(uid, 1, cids);
        for (String cid : cids) {
            if (!balance.containsKey(cid)) {
                balance.put(cid, 0L);
            }
        }
        return balance;
    }

    private void sendNotice(String msg, FireworkComponentAttr attr) {
        // 优先使用配置的如流群,否则使用默认
        if (attr.getGroupId() > 0 && StringUtils.isNotEmpty(attr.getRobotToken())) {
            String robotToken = attr.getRobotToken().trim();
            if (!robotToken.contains(ROBOT_TOKEN_PREFIX)) {
                robotToken = ROBOT_TOKEN_PREFIX + robotToken;
            }
            baiduInfoFlowRobotService.sendNotify(attr.getGroupId(), robotToken, msg, null);
        } else {
            baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, msg, null);
        }

        log.info("sendNotice done@msg:{},groupId:{}", msg, attr.getGroupId());
    }

    public Map<Long, AwardModelInfo> packageInfoMap(FireworkComponentAttr attr) {
        try {
            var result = hdztAwardServiceClient.queryAwardTasks(attr.getLotteryTaskId());
            return result == null ? Collections.emptyMap() : result;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }

    private static int getRandomExcludeFid(List<Firework> fireworks, int fid) {
        final int size = fireworks.size();

        if (size == 0) {
            return -1;
        }
        int index = -1;
        for (int i = 0; i < size; i++) {
            if (fireworks.get(i).getFid() == fid) {
                index = i;
                break;
            }
        }

        if (index == -1) {
            return fireworks.get(0).getFid();
        }

        int randomAdd = RandomUtils.nextInt(1, size);
        return fireworks.get((index + randomAdd) % size).getFid();
    }

    @Data
    public static class FireWorkResp {
        private List<FireworkVo> amounts;

        private int completeSet;

        private String avatar;

        private String nick;
    }

    @Data
    public static class ItemsResp {
        private List<ShopItemVo> items;
    }

    @Data
    public static class ChannelVo {
        private String avatar;

        private String nick;

        private long sid;

        private long ssid;
    }

    @Data
    public static class FireChannelResp {
        private List<ChannelVo> channels;
    }

    @Data
    public static class Label {
        private Integer label;


    }

    @Data
    public static class LotteryResp {
        private long cardTp;
        private boolean award;
        private List<String> items;
    }


}
