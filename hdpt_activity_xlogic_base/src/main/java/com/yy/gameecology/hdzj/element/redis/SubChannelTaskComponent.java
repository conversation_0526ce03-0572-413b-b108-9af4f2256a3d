package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.BabyMissionItem;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.actlayer.TaskItem;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.PromotTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.PairBean;
import com.yy.gameecology.common.consts.*;
import com.yy.gameecology.common.db.model.gameecology.ActTaskScore;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.AwardRecordInfo;
import com.yy.gameecology.hdzj.bean.BroadcastConfig;
import com.yy.gameecology.hdzj.bean.SunshineTaskConfig;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.SubChannelTaskComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.EnrollmentInfo;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RoleType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 主播成就，高光任务
 */
@UseRedisStore
@Component
@RestController
@RequestMapping("/hdzk/cmpt/SubChannelTaskComponent")
public class SubChannelTaskComponent extends BaseActComponent<SubChannelTaskComponentAttr> {

    @Autowired
    private EnrollmentNewService enrollmentNewService;

    @Autowired
    private BroadCastHelpService broadCastHelpService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private BigDataService bigDataService;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Override
    public Long getComponentId() {
        return ComponentId.SUB_CHANNEL_TASK;
    }

    private static final int INIT_MEMBER_TOP_N = 1000;

    /**
     * 用户的任务类型
     */
    private static final String MEMBER_TASK_TYPE = "member_task_type";

    /**
     * 总奖池总已发放
     */
    private static final String AWARD_RELEASE_AMOUNT = "award_release_amount";

    /**
     * :{yyyyMMdd}:{memberId}
     * 个人日已发放
     */
    private static final String DAY_AWARD_RELEASE_AMOUNT = "day_award_release_amount:%s:%s";

    /**
     * :{uid}
     * 奖励记录，给前端展示用
     */
    private static final String AWARD_RECORD_LIST_VIEW = "award_record_list_view:%s";

    /**
     * :{yyyyMMdd}
     * 需要发奖的key
     */
    private static final String AWARD_RECORD_SET_VIEW = "award_record_set_view:%s";

    /**
     * 任务名单是否初始化完成
     */
    private static final String MEMBER_TASK_TYPE_INIT = "member_task_type_init";

    /**
     * 待重放的榜单变化事件
     */
    private static final String REPLAY_EVENT_LIST = "replay_event";


    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, SubChannelTaskComponentAttr attr) {
        invokeOnRankingScoreChanged(event, attr);
    }

    /**
     * 晋级结算，初始化任务名单
     */
    @HdzjEventHandler(value = PromotTimeEnd.class, canRetry = true)
    public void onPromotTimeEnd(PromotTimeEnd event, SubChannelTaskComponentAttr attr) {
        invokePromotTimeEnd(event, attr);
    }

    public void invokePromotTimeEnd(PromotTimeEnd event, SubChannelTaskComponentAttr attr) {
        long actId = event.getActId();
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (!attr.getPromoteMemberTaskType().containsKey(rankId)) {
            return;
        }
        if (!attr.getPromoteMemberTaskType().get(rankId).containsKey(phaseId)) {
            return;
        }

        //初始化任务名单
        initMemberTaskType(actId, rankId, phaseId, attr);

        //打上初始化已完成标记
        String key = buildMemberTaskTypeInitKey(attr);
        actRedisDao.setNX(getRedisGroupCode(actId), key, DateUtil.getNowYyyyMMddHHmmss());

        //如流通知
        String msg = buildActRuliuMsg(actId, false, "强厅任务名单结算成功", "信息：强厅任务名单初始化成功。\n RankId:" + event.getRankId());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());

        //重放名单未结算前，有可能已经完成任务了的送礼事件
        replayEvent(attr);
    }

    private void initMemberTaskType(long actId, long rankId, long phaseId, SubChannelTaskComponentAttr attr) {
        Map<String, String> queryRankExt = Maps.newHashMap();
        queryRankExt.put(RankExtParaKey.RANK_TYPE_PROMOT, HdztRankType.PROMOT);
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, "", INIT_MEMBER_TOP_N, queryRankExt);
        if (org.springframework.util.CollectionUtils.isEmpty(ranks)) {
            SysEvHelper.waiting(50);
            ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, "", INIT_MEMBER_TOP_N, queryRankExt);
        }
        if (org.springframework.util.CollectionUtils.isEmpty(ranks)) {
            throw new RuntimeException("获取榜单数据失败");
        }
        String taskType = attr.getPromoteMemberTaskType().get(rankId).get(phaseId);
        String group = getRedisGroupCode(actId);
        Map<String, String> memberTaskType = ranks.stream().collect(Collectors.toMap(Rank::getMember, (x) -> taskType));
        actRedisDao.hmset(group, buildMemberTaskTypeKey(attr), memberTaskType);
    }

    /**
     * 重要：里面的数据操作，都要保证幂等性，可安全重试！！！
     */
    public void invokeOnRankingScoreChanged(RankingScoreChanged event, SubChannelTaskComponentAttr attr) {
        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }

        if (SysEvHelper.isDev()) {
            log.info("log SunshineTaskComponent handlerTaskEvent,event:{}", JSON.toJSONString(event));
        }

        long actId = event.getActId();
        //榜单更新时间
        Date rankUpdateDate = DateUtil.getDate(event.getOccurTime());
        String member = event.getMember();

        handleTask(event, attr, actId, member, rankUpdateDate);
    }

    /**
     * 个人突破和全站突破任务合并查询(挂件查询用)
     */
    public List<BabyMissionItem> queryTaskInfo(Date now, long actId, String member, long index) {
        SubChannelTaskComponentAttr attr = getComponentAttr(actId, index);
        BabyMissionItem mission = new BabyMissionItem();

        String taskTime = TimeKeyHelper.getTimeCode(attr.getTimeKey(), now);
        long score = Math.max(0, queryAnchorTaskScore(taskTime, actId, member, index));
        mission.setCompletedCount(score);

        List<ActTaskScore> taskScores = queryMemberTaskConfigSortByScore(actId, attr, member, taskTime);
        if (CollectionUtils.isEmpty(taskScores)) {
            return Lists.newArrayList();
        }
        List<TaskItem> taskConfig = convertTaskConfigView(attr, taskScores);
        mission.setTaskItems(taskConfig);

        PairBean pairBean = getCurLevelConfig(score, taskConfig);
        long curLevelConfig = pairBean.getSecond();

        mission.setTotalCount(curLevelConfig);
        mission.setLevel(pairBean.getFirst());
        int levelIndex = Convert.toInt(pairBean.getFirst()) - 1;
        if (levelIndex >= 0 && levelIndex < taskConfig.size()) {
            mission.setTaskName(taskConfig.get(levelIndex).getName());
        }

        return Lists.newArrayList(mission);
    }

    @GetMapping("/getTaskRecord/{actId}")
    public Response<Map<String, Object>> getTaskRecord(HttpServletRequest req, HttpServletResponse resp, @PathVariable long actId, long componentUseIndex
            , long sid, long ssid) {
        long uid = loginService.getLoginYYUid(req, resp);
        if (uid <= 0) {
            return Response.fail(100, "未登录");
        }
        return getTaskRecord(uid, actId, sid, ssid, componentUseIndex);
    }

    /**
     * 查看中奖名单,给产品线下发奖
     */
    @GetMapping("/queryAwardList/{actId}")
    public Response<List<String>> queryAwardList(@PathVariable long actId, long index) {
        SubChannelTaskComponentAttr attr = getComponentAttr(actId, index);

        ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
        List<String> result = Lists.newArrayList();
        long start = activityInfoVo.getBeginTime();
        String groupCode = getRedisGroupCode(actId);
        while (start <= activityInfoVo.getEndTime()) {
            String dayCode = DateUtil.format(new Date(start), DateUtil.PATTERN_TYPE2);
            String key = buildAwardRecordSetViewKey(dayCode, attr);
            Map<Object, Object> dayAward = actRedisDao.hGetAll(groupCode, key);
            for (Object memberInfo : dayAward.keySet()) {
                result.add(dayCode + "," + Convert.toString(memberInfo) + ",awardNum," + Convert.toString(dayAward.get(memberInfo)));
            }

            start = start + 24 * 60 * 60 * 1000;
        }

        return Response.success(result);
    }


    @GetMapping("/executeReplayEvent/{actId}")
    public Response<String> executeReplayEvent(@PathVariable long actId, long index) {
        SubChannelTaskComponentAttr attr = getComponentAttr(actId, index);
        replayEvent(attr);
        return Response.success(DateUtil.getNowYyyyMMddHHmmss());
    }


    private void handleTask(RankingScoreChanged event, SubChannelTaskComponentAttr attr, long actId, String member, Date eventTime) {
        String groupCode = (getRedisGroupCode(actId));
        String taskTime = DateUtil.format(eventTime, DateUtil.PATTERN_TYPE2);
        long roleId = getMemberRoleId(actId, member);
        List<ActTaskScore> memberTaskConfig = queryMemberTaskConfigSortByScore(actId, attr, member, taskTime);
        long rankScore = event.getRankScore();
        //预警
        String initKey = buildMemberTaskTypeInitKey(attr);
        boolean init = StringUtil.isNotBlank(actRedisDao.get(groupCode, initKey));
        if (!init && rankScore >= attr.getMinPassValue()) {
            String warnMsg = buildActRuliuMsg(actId, true, "强厅任务", "可能已完成任务,但任务名单仍未初始化成功，待结算完后会重放数据\n member:" + member + ",\n rankScore:" + rankScore);
            baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, warnMsg, Lists.newArrayList());

            //加入重放队列
            String replayKey = buildReplayKey(attr);
            String content = JSON.toJSONString(event);
            log.warn("add replay list:{}", content);
            actRedisDao.lPushWithSeq(groupCode, makeKey(attr, "seq:" + event.getSeq()), replayKey, content, attr.getSeqExpireSeconds());
            return;
        }
        //本次更新分数所过的任务
        List<ActTaskScore> completedList = calculateAnchorTask(memberTaskConfig, rankScore, event.getItemScore());
        if (CollectionUtils.isEmpty(completedList)) {
            return;
        }
        //扣减奖池，保存过任务记录,用于结算发奖
        long realAward = recordCompleteTask(taskTime, attr, memberTaskConfig, completedList, member, roleId);
        if (realAward <= 0) {
            log.warn("award-log-tag|奖池已经用完，本次不发放奖励,uid:{},actId:{},rankScore:{},itemScore:{}", member, attr.getActId(), event.getRankScore(), event.getItemScore());
            return;
        }
        RetryTool.withRetryCheck(actId, event.getSeq(), () -> {
            //发奖广播
            completedList.forEach(actTaskScore -> {
                sendBanner(attr, event, member, actTaskScore);
            });
        });
    }

    /**
     * 重放榜单变化事件
     */
    public void replayEvent(SubChannelTaskComponentAttr attr) {
        String groupCode = (getRedisGroupCode(attr.getActId()));
        String key = buildReplayKey(attr);
        String content = actRedisDao.rpop(groupCode, key);
        int counter = 0;
        while (StringUtil.isNotBlank(content)) {
            counter++;
            log.info("replayEvent:" + content);
            RankingScoreChanged event = JSON.parseObject(content, RankingScoreChanged.class);
            onRankingScoreChanged(event, attr);
            content = actRedisDao.rpop(groupCode, key);
        }
        String msg = buildActRuliuMsg(attr.getActId(), false, "强厅任务事件重放成功", "共重放榜单变化事件:" + counter + "个");
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_RUNNING_STATUS, msg, Lists.newArrayList());
    }

    /**
     * 查询用户过任务配置列表
     */
    private List<ActTaskScore> queryMemberTaskConfigSortByScore(long actId, SubChannelTaskComponentAttr attr, String member, String taskTime) {
        List<ActTaskScore> actTaskScores = Lists.newArrayList();
        String key = buildMemberTaskTypeKey(attr);
        String taskType = actRedisDao.hget(getRedisGroupCode(actId), key, member);
        //TODO 隐患点！！！结算后的瞬间由于为结算完，可能会读不到任务类型
        if (StringUtil.isEmpty(taskType)) {
            return Lists.newArrayList();
        }
        Map<Long, SunshineTaskConfig> levelScore = attr.getTaskConfig().get(taskType);
        for (Long level : levelScore.keySet()) {
            SunshineTaskConfig config = levelScore.get(level);
            ActTaskScore task = new ActTaskScore();
            task.setTaskType(taskType);
            task.setLevel(level);
            task.setRemark(config.getName());
            task.setScore(config.getPassValue());
            task.setAward(config.getAward() + "");
            actTaskScores.add(task);
        }

        //按分值排序
        actTaskScores.sort(Comparator.comparing(ActTaskScore::getScore));
        return actTaskScores;
    }

    private long getMemberRoleId(long actId, String member) {
        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(actId, 0L, RoleType.HALL.getValue(), member, 5);
        if (enrollmentInfo == null) {
            throw new RuntimeException("未找到报名信息actId:" + actId + ",memberId:" + member);
        }
        return enrollmentInfo.getDestRoleId();
    }

    private long recordCompleteTask(String taskTime, SubChannelTaskComponentAttr attr, List<ActTaskScore> memberTaskConfig, List<ActTaskScore> completedList, String member, long roleId) {
        log.info("recordCompleteTask,taskTime:{},list:{},member:{}", taskTime, JSON.toJSONString(completedList), member);
        //本次送礼触发总发放的奖励
        long totalAward = 0;
        String group = getRedisGroupCode(attr.getActId());
        long dayAwardLimit = attr.getRoleAwardDayLimit().getOrDefault(roleId, 0L);
        for (ActTaskScore complete : completedList) {
            long award = calCurTaskIncAward(memberTaskConfig, complete);
            long itemRealAward = 0;
            boolean dayLimitPartialAward = false;

            //日限额控制
            if (dayAwardLimit > 0) {
                String dayLimitSeq = makeKey(attr, "seq:daylimit:" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
                String dayLimitKey = buildMemberDayRelease(attr, taskTime, member);
                List<Long> memberResult = actRedisDao.incrValueWithLimitSeq(group, dayLimitSeq, dayLimitKey, award, dayAwardLimit, true, attr.getSeqExpireSeconds());
                if (memberResult.get(0) > 0) {
                    itemRealAward = memberResult.get(0) == 1 ? award : memberResult.get(1);
                }
                if (itemRealAward == 0) {
                    log.warn("award-log-tag|日限额已用完,本次任务关卡不发放奖励,member:{},act:{},award:{},itemRealAward:{},completeTask:{}", member, attr.getActId(), award, itemRealAward, JSON.toJSONString(complete));
                    continue;
                }
                dayLimitPartialAward = itemRealAward > 0 && itemRealAward < award;
                if (dayLimitPartialAward) {
                    award = itemRealAward;
                    log.info("award-log-tag|用户达到日限额，部分发放，actId:{},memberId:{},award:{},itemRealAward:{}", attr.getActId(), member, award, itemRealAward);
                }
            }

            //总扣奖池限额控制
            long poolLimit = attr.getAwardPoolConfig();
            String poolLimitSeq = makeKey(attr, "seq:poollimit::" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
            List<Long> result = actRedisDao.incrValueWithLimitSeq(group, poolLimitSeq, buildAwardReleaseKey(attr), award, poolLimit, true, attr.getSeqExpireSeconds());
            if (result.get(0) > 0) {
                itemRealAward = result.get(0) == 1 ? award : result.get(1);
            }
            if (itemRealAward == 0) {
                log.warn("award-log-tag|奖池已用完,本次任务关卡不发放奖励,member:{},act:{},award:{},itemRealAward:{},completeTask:{}", member, attr.getActId(), award, itemRealAward, JSON.toJSONString(complete));
                continue;
            }

            totalAward = totalAward + itemRealAward;
            //是否部分发奖
            boolean poolPartialAward = itemRealAward > 0 && itemRealAward < award;
            if (poolPartialAward) {
                log.info("award-log-tag|总奖池已用完，部分发放，actId:{},memberId:{},award:{},itemRealAward:{}", attr.getActId(), member, award, itemRealAward);
            }

            //发奖记录展示
            AwardRecordInfo recordInfo = new AwardRecordInfo();
            recordInfo.setDate(commonService.getNow(attr.getActId()).getTime());
            recordInfo.setTaskType(complete.getTaskType());
            recordInfo.setTaskLevel(complete.getLevel());
            recordInfo.setAward(itemRealAward);
            recordInfo.setAwardMode(poolPartialAward ? 3 : (dayLimitPartialAward ? 2 : 1));
            String recordSeq = makeKey(attr, "seq:record:" + taskTime + ":" + complete.getTaskType() + "_" + complete.getLevel() + "_" + member);
            actRedisDao.lPushWithSeq(group, recordSeq, buildAwardListViewKey(attr, member), JSON.toJSONString(recordInfo), attr.getSeqExpireSeconds());

            //待发放记录
            String needReleaseKey = buildAwardRecordSetViewKey(taskTime, attr);
            actRedisDao.hset(group, needReleaseKey, member + ",taskType:" + complete.getTaskType() + ",level:" + complete.getLevel(), itemRealAward + "");

            //奖励发放
            releaseAward(attr, complete, taskTime, member, itemRealAward);
            log.info("award-log-tag|award ok,actId:{},taskTime:{},taskType:{},member:{},itemRealAward:{}", attr.getActId(), taskTime, complete.getTaskType(), member, itemRealAward);

        }

        return totalAward;
    }

    /**
     * 奖励发放
     */
    private void releaseAward(SubChannelTaskComponentAttr attr, ActTaskScore score, String taskTime, String member, long amount) {
        //com.yy.gameecology.hdzj.element.component.SubChannelTaskComponent.queryAwardList
        log.info("releaseAward ,actId:{},taskTime:{},member:{},amount:{}", attr.getActId(), taskTime, member, amount);
        bigDataService.saveNoRankDataToFile(attr.getActId(), BusiId.GAME_ECOLOGY, System.currentTimeMillis(), member
                , RoleType.HALL, amount, BigDataScoreType.SUB_CHANNEL_AWARD, "分数:" + score.getScore());
    }


    /**
     * 专题页查询任务信息
     */
    public Response<Map<String, Object>> getTaskRecord(long uid, long actId, long sid, long ssid, long componentUseIndex) {
        Date now = commonService.getNow(actId);
        SubChannelTaskComponentAttr attr = getComponentAttr(actId, componentUseIndex);
        String group = getRedisGroupCode(actId);
        Map<String, Object> result = Maps.newHashMap();

        boolean startTimeBegin = now.after(attr.getStartTime());
        //任务时间未到
        if (!startTimeBegin) {
            result.put("missionStart", "0");
            return Response.success(result);
        }
        result.put("missionStart", "1");

        String memberId = sid + "_" + ssid;
        //任务信息
        List<BabyMissionItem> babyMissionItems = queryTaskInfo(now, actId, memberId, componentUseIndex);
        if (CollectionUtils.isEmpty(babyMissionItems)) {
            result.put("hasMission", "0");
            return Response.success(result);
        }
        result.put("hasMission", "1");
        result.put("missions", babyMissionItems);


        //当前奖池剩余数额
        result.put("awardLeft", getPoolLeft(actId, componentUseIndex));

        //奖励信息列表
        List<AwardRecordInfo> awardRecordInfos = Lists.newArrayList();
        List<String> awardRecord = actRedisDao.lrange(group, buildAwardListViewKey(attr, memberId), 0, 1000);
        if (CollectionUtils.isNotEmpty(awardRecord)) {
            for (String record : awardRecord) {
                AwardRecordInfo info = JSON.parseObject(record, AwardRecordInfo.class);
                awardRecordInfos.add(info);
            }
        }
        //奖励列表
        result.put("awardinfo", awardRecordInfos);
        //总奖励
        result.put("totalAward", awardRecordInfos.stream().mapToLong(AwardRecordInfo::getAward).sum());
        //今日奖励
        long beginTime = DateUtil.getDayFirstSecond(now).getTime();
        long endTime = beginTime + DateUtil.ONE_DAY_MILL_SECONDS;
        result.put("dayAward", awardRecordInfos
                .stream()
                .filter(p -> p.getDate() >= beginTime && p.getDate() < endTime)
                .mapToLong(AwardRecordInfo::getAward).sum());

        MemberInfo memberInfo = memberInfoService.getMemberInfo((long) BusiId.MAKE_FRIEND.getValue(), RoleType.HALL, sid + "_" + ssid);
        result.put("nick", memberInfo.getName());
        final String httpProtocal = "http://";
        if (StringUtil.isNotBlank(memberInfo.getLogo()) && memberInfo.getLogo().startsWith(httpProtocal)) {
            memberInfo.setLogo(memberInfo.getLogo().replace("http:", ""));
        }
        result.put("logo", memberInfo.getLogo());

        return Response.success(result);
    }

    /**
     * 奖池剩余数量
     */
    public long getPoolLeft(long actId, long index) {
        SubChannelTaskComponentAttr attr = getComponentAttr(actId, index);
        String group = getRedisGroupCode(attr.getActId());
        long award = Convert.toLong(actRedisDao.get(group, buildAwardReleaseKey(attr)), 0);
        return attr.getAwardPoolConfig() - award;
    }

    /**
     * 返回上一级任务分值
     */
    private long calOldAwardScore(List<ActTaskScore> memberTaskConfig, ActTaskScore complete) {
        for (int i = 0; i < memberTaskConfig.size(); i++) {
            ActTaskScore config = memberTaskConfig.get(i);
            if (config.getLevel().equals(complete.getLevel()) && config.getTaskType().equals(complete.getTaskType())) {
                return i == 0 ? 0 : memberTaskConfig.get(i - 1).getScore();
            }
        }
        return 0;
    }


    /**
     * 算出本次完成任务的增量奖励
     */
    private long calCurTaskIncAward(List<ActTaskScore> memberTaskConfig, ActTaskScore completeTask) {
        ActTaskScore preTask = null;
        for (int i = 0; i < memberTaskConfig.size(); i++) {
            ActTaskScore config = memberTaskConfig.get(i);
            if (config.getLevel().equals(completeTask.getLevel()) && config.getTaskType().equals(completeTask.getTaskType()) && i > 0) {
                preTask = memberTaskConfig.get(i - 1);
            }
        }

        long incAward = Convert.toLong(completeTask.getAward()) - Convert.toLong(preTask == null ? 0 : preTask.getAward());
        //避免出现一些变态配置，更高级的，奖励反而更少
        return Math.max(0, incAward);
    }


    private List<ActTaskScore> calculateAnchorTask(List<ActTaskScore> memberTaskConfig, long total, long score) {
        List<ActTaskScore> completedList = new ArrayList<>();

        long src = total - score;
        for (ActTaskScore actTaskScore : memberTaskConfig) {
            Long target = actTaskScore.getScore();
            //完成任务
            if (src < target && total >= target) {
                actTaskScore.setCompleted(1L);
                actTaskScore.setcTime(new Date());
                completedList.add(actTaskScore);
            }
        }
        return completedList;

    }


    /**
     * 当前阶段的总任务数
     */
    private PairBean getCurLevelConfig(long score, List<TaskItem> taskConfig) {
        long curMinScore = 0;
        long curMaxScore = 0;
        for (int i = 0; i < taskConfig.size(); i++) {
            TaskItem item = taskConfig.get(i);
            long curGapScore = item.getPassValue();
            curMaxScore = curMinScore + curGapScore;
            if (score >= curMinScore && score < curMaxScore) {
                return new PairBean(i + 1, curMaxScore);
            }

            curMinScore = curMinScore + curGapScore;
        }

        return new PairBean(score >= curMaxScore ? taskConfig.size() : 1, curMaxScore);
    }

    private List<TaskItem> convertTaskConfigView(SubChannelTaskComponentAttr attr, List<ActTaskScore> taskScores) {
        List<TaskItem> result = Lists.newLinkedList();
        for (int i = 0; i < taskScores.size(); i++) {
            ActTaskScore taskScore = taskScores.get(i);
            TaskItem item = new TaskItem();
            long passValue = i == 0 ? taskScore.getScore() : taskScore.getScore() - taskScores.get(i - 1).getScore();
            item.setPassValue(passValue);
            String taskName = taskScore.getRemark();
            item.setName(taskName);
            item.setRemark(taskName);
            JSONObject ext = null;
            if (StringUtil.isNotBlank(taskScore.getExtJson())) {
                ext = JSON.parseObject(taskScore.getExtJson());
            }
            if (ext == null) {
                ext = new JSONObject();
            }
            //这里是非分段
            String desc = String.format(attr.getAwardDescTips(), taskScore.getScore(), taskName);
            ext.put("desc", desc);
            ext.put("award", taskScore.getAward());
            item.setExtjson(JSON.toJSONString(ext));
            result.add(item);
        }
        return result;
    }

    /**
     * 查询任务榜分数
     */
    public long queryAnchorTaskScore(String taskTime, long actId, String member, long index) {
        SubChannelTaskComponentAttr attr = getComponentAttr(actId, index);
        Rank rank = hdztRankingThriftClient.queryPointedMembersRanking(actId, attr.getRankId(), attr.getPhaseId(), taskTime,
                member, Maps.newHashMap());
        return rank == null ? 0 : rank.getScore();
    }


    /**
     * 触发过任务常规高光、霸屏等横幅
     */
    private void sendBanner(SubChannelTaskComponentAttr attr, RankingScoreChanged event, String member, ActTaskScore taskScore) {
        String taskType = taskScore.getTaskType();
        if (!attr.getTaskBanner().containsKey(taskType)) {
            return;
        }
        if (!attr.getTaskBanner().get(taskType).containsKey(taskScore.getLevel())) {
            return;
        }
        List<BroadcastConfig> configs = attr.getTaskBanner().get(taskType).get(taskScore.getLevel());
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        for (BroadcastConfig config : configs) {
            log.info("invokeBro begin,actId:{},member:{},taskType:{},level:{},config:{}", attr.getActId(), member, taskType, taskScore.getLevel(), JSON.toJSONString(config));
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    invokeBro(attr, event, member, taskScore, config);
                }
            });
        }
    }

    private void invokeBro(SubChannelTaskComponentAttr attr, RankingScoreChanged event, String member, ActTaskScore taskScore, BroadcastConfig config) {
        String taskType = taskScore.getTaskType();
        //延迟广播
        if (config.getDelayMillSeconds() > 0) {
            log.info("invokeBro delay,actId:{},member:{},taskType:{},level:{},config:{}", attr.getActId(), member, taskType, taskScore.getLevel(), JSON.toJSONString(config));
            SysEvHelper.waiting(config.getDelayMillSeconds());
        }

        int broType = config.getBroType();
        String bannerUrl;
        //优先用任务配置,其次用默认
        if (StringUtil.isNotBlank(config.getBannerUrl())) {
            bannerUrl = config.getBannerUrl();
        } else {
            bannerUrl = attr.getDefaultBannerUrl();
        }

        EnrollmentInfo enrollmentInfo = enrollmentNewService.tryGetFirstEnrolMemberCache(attr.getActId(), 0L, RoleType.HALL.getValue(), member, 3);
        Assert.notNull(enrollmentInfo, "未找到成员信息:" + member);
        MemberInfo memberInfo = memberInfoService.getMemberInfo(enrollmentInfo.getRoleBusiId(), RoleType.findByValue((int) enrollmentInfo.getRoleType()), member);

        String nick = memberInfo.getName();
        long actId = attr.getActId();
        Long score = taskScore.getScore();


        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("nickName", nick);
        extMap.put("logo", memberInfo.getLogo());
        extMap.put("value", score + "");
        extMap.put("svgaUrl", bannerUrl);
        extMap.put("award", taskScore.getAward());
        extMap.put("ext", config.getExt());
        extMap.put("taskRemark", Convert.toString(taskScore.getRemark()));

        int bannerId = config.getBannerId();
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setBannerId(bannerId).setUserNick(nick)
                .setUserScore(score).setJsonData(JSON.toJSONString(extMap)).build();

        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();

        if (broType == BroadcastConfig.BroType.ACT_BUSI.code) {
            broadCastHelpService.broadcast(actId, BusiId.GAME_ECOLOGY, broType, 0L, 0L, bannerBroMsg);
        } else {

            String[] ssidStr = member.split("_");
            long sid = Convert.toLong(ssidStr[0]);
            long ssid = Convert.toLong(ssidStr[1]);
            broadCastHelpService.broadcast(actId, BusiId.findByValue(Convert.toInt(event.getBusiId())), broType, sid, ssid, bannerBroMsg);
        }
        log.info("log SunshineTaskComponent bro done uid:{} task:{}", member, JSON.toJSONString(taskScore));

        bigDataService.saveNoRankDataToFile(actId, BusiId.GAME_ECOLOGY, System.currentTimeMillis(), member
                , RoleType.HALL, score, BigDataScoreType.SUNSHINE_TASK_BANNER, config.getDesc() + "|" + config.getBannerId() + "|" + config.getBroType());
    }


    private String buildMemberDayRelease(SubChannelTaskComponentAttr attr, String taskTime, String member) {
        return makeKey(attr, String.format(DAY_AWARD_RELEASE_AMOUNT, taskTime, member));
    }

    /**
     * 奖池已发放奖励数量
     */
    private String buildAwardReleaseKey(SubChannelTaskComponentAttr attr) {
        return makeKey(attr, AWARD_RELEASE_AMOUNT);
    }

    /**
     * 发奖记录展示
     */
    private String buildAwardListViewKey(SubChannelTaskComponentAttr attr, String member) {
        return makeKey(attr, String.format(AWARD_RECORD_LIST_VIEW, member));
    }

    /**
     * 待发奖记录
     */
    private String buildAwardRecordSetViewKey(String taskTime, SubChannelTaskComponentAttr attr) {
        return makeKey(attr, String.format(AWARD_RECORD_SET_VIEW, taskTime));
    }

    /**
     * 成员的
     */
    private String buildMemberTaskTypeKey(SubChannelTaskComponentAttr attr) {
        return makeKey(attr, MEMBER_TASK_TYPE);
    }

    /**
     * 任务名单是否初始化完成
     */
    private String buildMemberTaskTypeInitKey(SubChannelTaskComponentAttr attr) {
        return makeKey(attr, MEMBER_TASK_TYPE_INIT);
    }

    /**
     * 待重放事件
     */
    private String buildReplayKey(SubChannelTaskComponentAttr attr) {
        return makeKey(attr, REPLAY_EVENT_LIST);
    }

}
