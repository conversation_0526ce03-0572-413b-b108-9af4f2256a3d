package com.yy.gameecology.hdzj.element.component.xmodule.aov;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.mq.HdzkAovGameEvent;
import com.yy.gameecology.activity.bean.mq.HdzkAovRoundSettledEvent;
import com.yy.gameecology.activity.bean.wzry.JoinGameResult;
import com.yy.gameecology.activity.client.yrpc.ZhuiwanRiskClient;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.activity.service.aov.award.AovAwardService;
import com.yy.gameecology.common.consts.aov.AovConst;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseAwardExtMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamMapper;
import com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamMemberMapper;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseAward;
import com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeamMember;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.bean.aov.AovAwardRecord;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.AovAwardComponentAttr;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("5119")
public class AovAwardComponent extends BaseActComponent<AovAwardComponentAttr> {

    @Resource
    private AovPhaseTeamMapper aovPhaseTeamMapper;

    @Resource
    private AovPhaseAwardExtMapper aovPhaseAwardExtMapper;

    @Resource
    private AovPhaseTeamMemberMapper aovPhaseTeamMemberMapper;

    @Autowired
    private AovAwardService aovAwardService;

    @Autowired
    private ZhuiwanRiskClient zhuiwanRiskClient;

    @Override
    public Long getComponentId() {
        return ComponentId.AOV_AWARD;
    }

    /**
     * 扫描 aov_match_award_record 表查询并更新已提交状态的数据为成功、或失败
     */
    @NeedRecycle(notRecycle = true, author = "liqingyang")
    @Scheduled(cron = "16 4 1/3 * * ?")
    public void refreshSubmittedAwardState() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        aovAwardService.refreshSubmittedAwardState();
    }

    @HdzjEventHandler(value = HdzkAovGameEvent.class, canRetry = true)
    public void handleAovGame(HdzkAovGameEvent event, AovAwardComponentAttr attr) {
        if (event.getState() != AovConst.GameState.RESULTED) {
            log.info("handleAovGameEvent skip game state not resulted gameId:{} state:{}", event.getGameId(), event.getState());
            return;
        }

        AovPhaseAward phaseAward = aovPhaseAwardExtMapper.selectByRoundNumAndMemberSize(event.getPhaseId(), 0, 1);
        if (phaseAward == null) {
            log.error("handleAovGame phaseAward cannot be found phaseId:{}", event.getPhaseId());
            throw new IllegalStateException("first award config not exist");
        }

        if (event.getCamp1Score() > 0) {
            for (HdzkAovGameEvent.GameMember gameMemberVO : event.getCamp1Members()) {
                aovAwardService.tryGrantFirstGameAward(attr, phaseAward, event.getGameId(), gameMemberVO.getUid(), event.getEndTime());
            }
        }

        if (event.getCamp2Score() > 0) {
            for (HdzkAovGameEvent.GameMember gameMemberVO : event.getCamp2Members()) {
                aovAwardService.tryGrantFirstGameAward(attr, phaseAward, event.getGameId(), gameMemberVO.getUid(), event.getEndTime());
            }
        }
    }

    @HdzjEventHandler(value = HdzkAovRoundSettledEvent.class, canRetry = true)
    public void handleAovRoundSettled(HdzkAovRoundSettledEvent event, AovAwardComponentAttr attr) {
        int roundNum = event.getRoundNum();
        List<AovPhaseAward> phaseAwards = aovPhaseAwardExtMapper.selectByRoundNum(event.getPhaseId(), roundNum);
        if (CollectionUtils.isEmpty(phaseAwards)) {
            log.info("handleAovRoundSettled round's award is empty roundNum:{}", roundNum);
            return;
        }

        List<Long> teamIds = event.getEliminatedTeamIds();

        // 判断是否满足
        int requireTeamCnt = MapUtils.getIntValue(attr.getRequireTeamCnt(), roundNum, 0);
        if (requireTeamCnt > teamIds.size()) {
            int teamCnt = aovPhaseTeamMapper.countTeam(event.getPhaseId(), AovConst.PhaseTeamState.SUCC, null);
            if (teamCnt < requireTeamCnt) {
                log.warn("handleAovRoundSettled team count not enough with roundNum:{} require team count:{} team count:{}", roundNum, requireTeamCnt, teamCnt);
                return;
            }
        }

        Map<Integer, AovPhaseAward> phaseAwardMap = phaseAwards.stream().collect(Collectors.toMap(AovPhaseAward::getMemberSize, Function.identity()));

        List<AovPhaseTeamMember> members = aovPhaseTeamMemberMapper.batchSelectGamedMembers(event.getPhaseId(), teamIds);
        Map<Long, List<Long>> teamMembers = new HashMap<>(teamIds.size());
        for (AovPhaseTeamMember member : members) {
            teamMembers.compute(member.getTeamId(), (k, v) -> {
                if (v == null) {
                    v = new ArrayList<>(7);
                }

                v.add(member.getMemberUid());

                return v;
            });
        }

        log.info("handleAovRoundSettled trying to grant award with roundNum:{} phaseAwardMap:{}", roundNum, phaseAwardMap);
        for (Map.Entry<Long, List<Long>> entry : teamMembers.entrySet()) {
            List<Long> uids = entry.getValue();
            int memberSize = uids.size();
            AovPhaseAward phaseAward = phaseAwardMap.get(memberSize);
            if (phaseAward == null) {
                log.error("handleAovRoundSettled cannot find phaseAward with roundNum:{} memberSize:{}", roundNum, memberSize);
                continue;
            }

            log.info("handleAovRoundSettled grant award with phaseAwardId:{} teamId:{} uids:{}", phaseAward.getId(), entry.getKey(), uids);

            for (long uid : uids) {
                aovAwardService.tryGrantRoundAward(attr, phaseAward, uid, event.getSettleTime());
            }
        }
    }

    @GetMapping("hasAward")
    public Response<Boolean> queryHasAward(@RequestParam(name = "actId") int actId,
                                           @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {
        AovAwardComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        int count = aovAwardService.countGrantedAward(actId, uid);
        return Response.success(count > 0);
    }

    @GetMapping("myAwardList")
    public Response<List<AovAwardRecord>> queryMyAwardList(@RequestParam(name = "actId") int actId,
                                                           @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx) {
        AovAwardComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        try {
            List<AovAwardRecord> result = aovAwardService.queryUserAwardList(actId, uid);
            return Response.success(result);
        } catch (Exception e) {
            log.error("queryMyAwardList fail:", e);
            return Response.fail(500, "服务器忙，请稍后再试");
        }
    }

    @RequestMapping("receiveAward")
    public Response<?> receiveAward(HttpServletRequest request,
                                    @RequestParam(name = "actId") int actId,
                                    @RequestParam(name = "cmptInx", required = false, defaultValue = "810") int cmptInx,
                                    @RequestParam(name = "awardId") long awardId,
                                    @RequestParam(name = "account") String account,
                                    @RequestParam(name = "mobile") String mobile,
                                    @RequestParam(name = "verifyCode", required = false) String verifyCode,
                                    @RequestParam(name = "recordId", required = false) String recordId,
                                    @RequestParam(name = "verifyToken", required = false) String verifyToken) {
        AovAwardComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "activity not exist");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need");
        }

        if (StringUtils.isEmpty(account)) {
            return Response.fail(400, "账号不能为空");
        }

        if (StringUtils.isEmpty(mobile)) {
            return Response.fail(400, "手机号不能为空");
        }

        // 风控
        try {
            zhuiwanRiskClient.doRiskCheck(zhuiwanRiskClient.fetchClientInfo(request), attr.getRiskStrategyKey(), uid, verifyCode, verifyToken, recordId);
        } catch (SuperException e) {
            log.warn("receiveAward risk awardId:{},uid:{},e:", awardId, uid, e);
            JoinGameResult joinGameResult = new JoinGameResult();
            joinGameResult.setRiskRecheck(e.getData());
            return Response.fail(e.getCode(), e.getMessage(), joinGameResult);
        }

        try {
            return aovAwardService.submitAwardAccountInfo(awardId, uid, account, mobile);
        } catch (BusinessException e) {
            log.warn("receiveAward warn:", e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Throwable e) {
            log.error("receiveAward fail:", e);
            return Response.fail(500, "服务器忙，请稍后再试");
        }
    }
}
