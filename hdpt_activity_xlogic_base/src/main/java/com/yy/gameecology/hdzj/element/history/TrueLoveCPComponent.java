package com.yy.gameecology.hdzj.element.history;

import com.google.common.collect.Maps;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.TrueLoveCpAttr;
import com.yy.thrift.hdztranking.Rank;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.06.24 15:25
 */
@UseRedisStore
@Component
public class TrueLoveCPComponent extends BaseActComponent<TrueLoveCpAttr> {

    public static final String HOUR_TOP1_CP = "hour_top1_cp";

    /**
     * 每日每小时 top 1 记录 hash hourly_top1_cp_record:day {hour:player_anchor_score}
     */
    public static final String HOURLY_TOP1_CP_RECORD = "hourly_top1_cp_record:%s";

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Override
    public Long getComponentId() {
        return ComponentId.TRUE_LOVE_CP;
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onPhaseTimeEndEvent(PhaseTimeEnd event, TrueLoveCpAttr attr) {
        long actId = event.getActId();
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        String groupCode = redisConfigManager.getGroupCode(actId);
        if (rankId != attr.getRankId() || phaseId != attr.getPhaseId()) {
            return;
        }

        log.info("onPhaseTimeEnd start -> event:{}, attr:{}", event, attr);

        String checkName = "onPhaseTimeEnd:" + event.getEkey();
        String checkValue = event.getTimestamp() + ":" + event.getSeq();
        //请求去重
        boolean firstRun = actRedisDao.setNX(groupCode, makeKey(attr, checkName), checkValue);
        if (!firstRun) {
            log.warn("TrueLoveCPAttrComponent onPhaseTimeEnd dup,return, event:{}, attr:{}", event, attr);
            return;
        }

        String dateStr = null;
        Date date = DateUtil.getDate(event.getEndTime());
        if (TimeKeyHelper.isSplitByHour(event.getTimeKey())) {
            dateStr = DateUtil.format(date, DateUtil.PATTERN_TYPE7);
        } else if (TimeKeyHelper.isSplitByDay(event.getTimeKey())) {
            dateStr = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
        }

        List<Rank> ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, 1, null);
        if (CollectionUtils.isEmpty(ranks)) {
            log.info("not found ranks for actId={},rankId={},phaseId={},dateStr={}", actId, rankId, phaseId, dateStr);
            return;
        }
        Rank rank = ranks.get(0);
        String member = rank.getMember();
        String[] pa = member.split("\\|");
        Date now = commonService.getNow(actId);
        String time = DateUtil.format(now);

        if (MapUtils.isNotEmpty(attr.getLeftAwardPackageIds())) {
            hdztAwardServiceClient.doBatchWelfare(Convert.toLong(pa[0]), attr.getLeftAwardPackageIds(), time, attr.getRetry(), Maps.newHashMap());
        }
        if (MapUtils.isNotEmpty(attr.getRightAwardPackageIds())) {
            hdztAwardServiceClient.doBatchWelfare(Convert.toLong(pa[1]), attr.getRightAwardPackageIds(), time, attr.getRetry(), Maps.newHashMap());
        }
        actRedisDao.set(groupCode, Const.addActivityPrefix(actId, HOUR_TOP1_CP), rank.getMember());

        String day = DateUtil.format(date, DateUtil.PATTERN_TYPE2);
        String key = String.format(Const.addActivityPrefix(actId, HOURLY_TOP1_CP_RECORD), day);
        String value = rank.getMember() + "|" + Convert.toLong(rank.getScore());
        actRedisDao.hset(groupCode, key, dateStr, value);

        //todo 告白动效 done 在赛果广播中处理
        //INSERT INTO gameecology.ge_broadcast_rank_result_config (act_id, rank_id, contri_rank_id, phase_id, uri, calc_type, calc_value, module_type, match_type, broadcast_type, skip_flag, text, status, extjson, remark) VALUES (2021084001, 5, '0', 30, 1006, 0, 1, 999, 10000, '900', 1, '{"userLogo":"##avatarInfo##","anchorLogo":"##cp.avatarInfo##"}', 1, '', '小时榜cp冠军特效（陪玩小时榜top1告白动效）');

        log.info("TrueLoveCPAttrComponent finish rankId:{}, phaseId:{}, dateStr:{}, cp:{}", rankId, phaseId, dateStr, rank.getMember());
    }

    public String lastBestCP(long actId) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        return actRedisDao.get(groupCode, Const.addActivityPrefix(actId, HOUR_TOP1_CP));
    }

    public Map<Object, Object> queryHourlyCPByDay(long actId, String day) {
        String groupCode = redisConfigManager.getGroupCode(actId);
        String key = String.format(Const.addActivityPrefix(actId, HOURLY_TOP1_CP_RECORD), day);
        return actRedisDao.hGetAll(groupCode, key);
    }
}
