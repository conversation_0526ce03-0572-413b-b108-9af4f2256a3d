package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.YoPopupMessage;
import com.yy.gameecology.activity.exception.BusinessException;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.zhuiwan.*;
import com.yy.thrift.zhuiwan_newfamily.BatchGetFamilyBasicInfoResp;
import com.yy.thrift.zhuiwan_newfamily.ChannelKey;
import com.yy.thrift.zhuiwan_newfamily.FamilyBasicInfo;
import com.yy.thrift.zhuiwan_newfamily.GetFamilyBasicInfoResp;
import com.yy.thrift.zhuiwan_skillcard.ChannelSeatInfo;
import com.yy.thrift.zhuiwan_skillcard.QueryChannelSeatRsp;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class ZhuiWanPrizeIssueServiceClient {
    private static final Logger log = LoggerFactory.getLogger(ZhuiWanPrizeIssueServiceClient.class);


    @Reference(protocol = "attach_nythrift", owner = "${thrift_zhuiwan_prize_issue_client_s2s_name}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "20"})
    private ZhuiWanPrizeIssueService.Iface proxy;

    @Reference(protocol = "attach_nythrift", owner = "${thrift_zhuiwan_prize_issue_client_s2s_name}", registry = "consumer-reg", timeout = 30000, parameters = {"threads", "10"}
            , retries = 2, cluster = "failover")
    private ZhuiWanPrizeIssueService.Iface readProxy;


    public ZhuiWanPrizeIssueService.Iface getProxy() {
        return proxy;
    }

    public ZhuiWanPrizeIssueService.Iface getReadProxy() {
        return readProxy;
    }

    /**
     * 新通用弹窗通知-区分app
     */
    private final static int POP_UP_MESSAGE_MIN_TYPE = 2022;

    public final static String ZHUIWAN_SEQ_PREFIX = "issue:";

    public final static String NOT_NEED_FILLIN = "信息已完善，无需重复操作！";

    /**
     * 注意，这个方法只适合非营销短信，如果是营销短信，请使用：
     * com.yy.gameecology.activity.client.thrift.UserinfoThriftClient#sendMarketSms(java.lang.String, java.lang.Long, java.lang.String)
     */
    @Deprecated
    public int sendSms(String seq, Long uid, String content) {
        Clock clock = new Clock();
        String uidWhiteList = "," + Const.GEPM.getParamValue("prize_issue_client_send_sms_white_list", "50042952") + ",";
        //非生产环境，发送短信uid白名单不通过
        if (!SysEvHelper.isDeploy()
                && !uidWhiteList.contains("," + uid + ",")) {
            log.warn("sendSms not in white list return,seq:{},uid:{},content:{}", seq, uid, content);
            return 0;
        }
        try {
            BaseRsp rsp = getProxy().sendSms(seq, Lists.newArrayList(uid), content);
            if (rsp == null || rsp.getResult() != 0) {
                log.error("sendSms error,rsp:{}", rsp);
            }
            log.info("sendSms done,seq:{},uid:{},content:{},ret:{},clock:{}", seq, uid, content, JSON.toJSONString(rsp), clock.tag());
            return rsp.getResult();
        } catch (Exception e) {
            log.error("sendSms error,seq:{},uid:{},content:{},e:{},clock:{}", seq, uid, content, e.getMessage(), clock.tag(), e);
            return -1;
        }
    }

    /**
     * 追玩应用内push(顶部toast)
     */
    public void sendPopupMessage(String seq, long uid, YoPopupMessage message) {
        try {
            BaseRsp rsp = getProxy().sendUnicast(seq, uid, JSON.toJSONString(message), POP_UP_MESSAGE_MIN_TYPE);
            if (rsp == null || rsp.getResult() != 0) {
                log.error("sendPopupMessage error,rsp:{}", rsp);
            }
            log.info("sendPopupMessage done,seq:{},uid:{},content:{},ret:{}", seq, uid, JSON.toJSONString(message), JSON.toJSONString(rsp));
        } catch (Exception e) {
            log.error("sendPopupMessage error,seq:{},uid:{},content:{},e:{}", seq, uid, JSON.toJSONString(message), e.getMessage(), e);
        }
    }

    /**
     * 获取随机的陪玩约会插件在线频道
     */
    public List<ChannelInfoVo> getRadomPeiwanPluginChannel(int size) {
        List<ChannelInfoVo> result = Lists.newArrayList();

        List<BaseChannel> baseChannels = batchGetPeiwanPluginChannel();
        Collections.shuffle(baseChannels);

        for (int i = 0; i < baseChannels.size() && i < size; i++) {
            ChannelInfoVo vo = new ChannelInfoVo();
            vo.setSid(baseChannels.get(i).getTopSid());
            vo.setSsid(baseChannels.get(i).getSubSid());
            result.add(vo);
        }

        return result;
    }

    @Cached(timeToLiveMillis = CacheTimeout.PW_PLUGIN_CHANNEL)
    public List<BaseChannel> batchGetPeiwanPluginChannel() {
        Clock clock = new Clock();

        try {
            List<BaseChannel> baseChannels = getReadProxy().batchGetPeiwanPluginChannel("gameecology:" + System.currentTimeMillis());
            return baseChannels;
        } catch (Exception e) {
            log.error("batchGetPeiwanPluginChannel error,e:{},clock:{}", e.getMessage(), clock.tag(), e);
            return Lists.newArrayList();
        }
    }

    /**
     * 查询家族基本信息
     **/
    @Cached(timeToLiveMillis = CacheTimeout.PW_PLUGIN_CHANNEL)
    public FamilyBasicInfo getFamilyBasicInfo(long familyId) {
        Map<String, String> data = Maps.newHashMap();
        data.put("familyId", familyId + "");
        try {
            SimpleResult simpleResult = getReadProxy().invoke(BusiId.SKILL_CARD.getValue(), 1, data, "");
            if (simpleResult.getCode() == 1) {
                GetFamilyBasicInfoResp resp = JSON.parseObject(simpleResult.getData().get("resp"), GetFamilyBasicInfoResp.class);

                return resp.getData();
            } else {
                log.error("getFamilyBasicInfo error,familyId:{},rsp:{}", familyId, simpleResult);
            }

            log.info("resp={},familyId={}", simpleResult, familyId);
        } catch (Exception ex) {
            log.error("getFamilyBasicInfo error,familyId={}", familyId, ex);
        }

        return null;
    }

    /**
     * 批量查询家族基本信息
     **/
    @Cached(timeToLiveMillis = CacheTimeout.PW_PLUGIN_CHANNEL)
    public Map<Long, FamilyBasicInfo> batchGetFamilyBasicInfo(List<Long> familyIds) {
        Map<Long, FamilyBasicInfo> familyBasicInfoMap = Maps.newHashMap();

        if (CollectionUtils.isEmpty(familyIds)) {
            log.warn("batchGetFamilyBasicInfo familyIds is empty");
            return familyBasicInfoMap;
        }
        Map<String, String> data = Maps.newHashMap();
        data.put("familyIds", JSON.toJSONString(familyIds));
        try {
            SimpleResult simpleResult = getReadProxy().invoke(BusiId.SKILL_CARD.getValue(), 2, data, "");
            if (simpleResult.getCode() == 1) {
                BatchGetFamilyBasicInfoResp resp = JSON.parseObject(simpleResult.getData().get("resp"), BatchGetFamilyBasicInfoResp.class);
                List<FamilyBasicInfo> infos = resp.getData();
                if (CollectionUtils.isEmpty(infos)) {
                    log.warn("batchGetFamilyBasicInfo familyIds is empty,familyIds:{}", JSON.toJSONString(familyIds));
                    return familyBasicInfoMap;
                }
                infos.forEach(info -> familyBasicInfoMap.put(info.getFamilyId(), info));
                return familyBasicInfoMap;
            } else {
                log.error("batchGetFamilyBasicInfo error,familyId:{},rsp:{}", JSON.toJSONString(familyIds), simpleResult);
            }

            log.info("resp={},familyIds={}", simpleResult, familyIds);
        } catch (Exception ex) {
            log.error("batchGetFamilyBasicInfo error,familyIds={}", familyIds, ex);
        }

        return Maps.newHashMap();
    }

    /**
     * 查询技能卡座位信息
     **/
    @Cached(timeToLiveMillis = CacheTimeout.PW_CHANNEL_SEAT)
    public List<ChannelSeatInfo> queryChannelSeat(long sid, long ssid) {
        Map<String, String> data = Maps.newHashMap();
        data.put("sid", sid + "");
        data.put("ssid", ssid + "");
        try {
            SimpleResult simpleResult = getReadProxy().invoke(BusiId.SKILL_CARD.getValue(), 3, data, "");
            if (simpleResult.getCode() == 1) {
                QueryChannelSeatRsp resp = JSON.parseObject(simpleResult.getData().get("resp"), QueryChannelSeatRsp.class);

                return resp.getData();
            } else {
                log.error("queryChannelSeat error,sid:{},ssid:{},resp:{}", sid, ssid, simpleResult);
            }
            log.info("resp={},sid={},ssid:{}", simpleResult, sid, ssid);
        } catch (Exception ex) {
            log.error("queryChannelSeat error,sid={},ssid={},e:{}", sid, ssid, ex.getMessage(), ex);
        }

        return null;
    }

    /**
     * 获取技能卡所有在线频道(有人上座即认为在线)
     **/
    @Cached(timeToLiveMillis = CacheTimeout.PW_PLUGIN_CHANNEL)
    public List<ChannelKey> listSkillCardAllOnlineChannel() {
        try {
            SimpleResult simpleResult = getReadProxy().invoke(BusiId.SKILL_CARD.getValue(), 4, Maps.newHashMap(), "");
            if (simpleResult.getCode() == 1) {
                List<ChannelKey> resp = JSON.parseArray(simpleResult.getData().get("resp"), ChannelKey.class);

                return resp;
            } else {
                log.error("listSkillCardAllOnlineChannel error,rsp:{}", simpleResult);
            }
            log.info("resp={}", simpleResult);
        } catch (Exception ex) {
            log.error("listSkillCardAllOnlineChannel error", ex);
        }

        return Collections.emptyList();
    }

    @Cached(timeToLiveMillis = CacheTimeout.PW_PLUGIN_CHANNEL)
    public boolean checkSingleUserNew(Long uid) {
        CheckUserNewReq req = new CheckUserNewReq();
        req.setUids(Lists.newArrayList(uid));
        try {
            CheckUserNewRsp userNewRsp = this.getReadProxy().getUserNewReq(req);
            if (userNewRsp.getCode() == 0) {
                log.info("checkSingleUserNew done, uid:{},resp:{}", uid, userNewRsp.toString());
                return userNewRsp.getUserStatusMap().get(uid);
            } else {
                log.error("checkSingleUserNew error,uid:{},rsp:{}", uid, userNewRsp);
            }
        } catch (TException e) {
            log.error("checkSingleUserNew error uid:{}", uid, e);
        }
        return true;
    }

    public Map<String, Long> getUserLoginTime(long uid, String hdid) {
        GetUserLoginTimeReq req = new GetUserLoginTimeReq();
        req.setUid(uid);
        req.setHdid(hdid);
        try {
            GetUserLoginTimeRsp rsp = getReadProxy().getUserLoginTime(req);
            if (rsp.getCode() == 0) {
                return rsp.getLoginTimeMap();
            } else {
                log.error("getUserLoginTime error,req:{},rsp:{}", req, rsp);
            }
        } catch (Exception e) {
            log.error("getUserLoginTime exception:", e);
        }

        return Collections.emptyMap();
    }

    public void reportUserOrient(long uid, String key, int expireDay) {
        ReportUserOrientReq req = new ReportUserOrientReq();
        req.setUid(uid);
        req.setKey(key);
        req.setExpireDay(expireDay);
        doReportUserOrient(req);
    }

    public void reportUserOrient(long uid, String key, Date expireTime) {
        ReportUserOrientReq req = new ReportUserOrientReq();
        req.setUid(uid);
        req.setKey(key);
        req.setExpireAtTime(expireTime.getTime());
        doReportUserOrient(req);
    }

    public void doReportUserOrient(ReportUserOrientReq req) {
        int retry = 3;
        while (retry-- > 0) {
            try {
                BaseRsp rsp = getProxy().reportUserOrient(req);
                if (rsp != null && rsp.getResult() == 0) {
                    return;
                } else {
                    log.error("reportUserOrient fail with rsp:{}", rsp);
                }
            } catch (Exception e) {
                log.error("reportUserOrient exception", e);
            }
        }
    }

    public List<DecorationStorage> getUserInUseDecorations(long uid, int type) {
        DecorationStorageReq req = new DecorationStorageReq(uid, type);
        try {
            DecorationStorageRsp rsp = getReadProxy().getUserDecorations(req);
            if (rsp != null && rsp.code == 0) {
                return rsp.getDecorations();
            } else {
                log.error("getUserInUseDecorations error,uid:{},type:{},rsp:{}", uid, type, rsp);
            }
            log.info("getUserInUseDecorations uid:{},type:{},rsp:{}", uid, type, rsp);
        } catch (Exception e) {
            log.error("getUserInUseDecorations exception:", e);
        }

        return Collections.emptyList();
    }

    /**
     * 定时推送：只能推送特定uid
     *
     * @param pushTime 推送的时间点,需要大于当前时间
     * @param pushUids 推送的uid列表,逗号分隔
     * @param fromId   系统消息-1234567，活动中心-10000，20000-Yo语音小助手
     **/
    public void sendTimingPush(int fromId, Date pushTime, String title, String content
            , String image, String link, long operateUid, String pushUids) {
        Map<String, String> data = Maps.newHashMap();
        data.put("fromId", fromId + "");
        data.put("pushTime", DateUtil.format(pushTime));
        data.put("title", title);
        data.put("content", content);
        data.put("image", image);
        data.put("link", link);
        data.put("operateUid", operateUid + "");
        data.put("pushUids", pushUids);
        try {
            SimpleResult simpleResult = getProxy().invoke(BusiId.ZHUI_WAN.getValue(), 5, data, "");
            if (simpleResult.getCode() != 1) {
                throw new SuperException("sendTimingPush error,reason = " + simpleResult.getReason(), 500);
            }
            log.info("sendTimingPush resp={},data:{}", simpleResult, JSON.toJSONString(data));
        } catch (Exception ex) {
            log.error("sendTimingPush error,data={},e:{}", JSON.toJSONString(data), ex.getMessage(), ex);
        }
    }

    public void addGameTeamLeader(long leaderUid, long sid, long ssid, Date time) {
        Map<String, String> data = Maps.newHashMap();
        data.put("leaderUid", leaderUid + "");
        data.put("sid", sid + "");
        data.put("ssid", ssid + "");
        if (time != null) {
            data.put("time", DateUtil.format(time));
        }

        try {
            SimpleResult simpleResult = getProxy().invoke(BusiId.ZHUI_WAN.getValue(), 6, data, "");
            if (simpleResult.getCode() != 1) {
                throw new SuperException("addGameTeamLeader error,reason = " + simpleResult.getReason(), 500);
            }
            log.info("resp={},data:{}", simpleResult, JSON.toJSONString(data));
        } catch (Exception ex) {
            log.error("addGameTeamLeader error,data={},e:{}", JSON.toJSONString(data), ex.getMessage(), ex);
        }
    }

    public ChannelKey queryGameTeamLeader(long uid) {
        Map<String, String> data = Maps.newHashMap();
        data.put("uid", uid + "");
        try {
            SimpleResult simpleResult = getReadProxy().invoke(BusiId.ZHUI_WAN.getValue(), 7, data, "");
            if (simpleResult.getCode() != 1 || MapUtils.isEmpty(simpleResult.getData())) {
                return null;
            }
            long sid = Convert.toLong(simpleResult.getData().get("sid"), 0);
            long ssid = Convert.toLong(simpleResult.getData().get("ssid"), 0);
            log.info("resp={},data:{}", simpleResult, JSON.toJSONString(data));
            if (sid <= 0 || ssid <= 0) {
                return null;
            }
            ChannelKey channelKey = new ChannelKey();
            channelKey.setTopSid(sid);
            channelKey.setSubSid(ssid);

            return channelKey;
        } catch (Exception ex) {
            log.error("queryGameTeamLeader error,data={},e:{}", JSON.toJSONString(data), ex.getMessage(), ex);
        }

        return null;
    }


    public BaseRsp fillInUserAccountInfo(long uid, String orderSeq, String gameAccount, String buyerMobile) {
        FillInUserAccountInfoReq req = new FillInUserAccountInfoReq();
        orderSeq = ZHUIWAN_SEQ_PREFIX + orderSeq;
        req.setUid(uid);
        req.setSeqId(orderSeq);
        req.setGameAccount(gameAccount);
        req.setBuyerMobile(buyerMobile);
        try {
            BaseRsp rsp = getProxy().fillInUserAccountInfo(req);
            log.info("fillInUserAccountInfo error,uid:{},orderSeqs:{},gameAccount:{},buyerMobile:{},rsp:{}", uid, orderSeq, gameAccount, buyerMobile, JSON.toJSONString(rsp));

            return rsp;
        } catch (Exception e) {
            log.error("fillInUserAccountInfo exception,uid:{},orderSeq:{},gameAccount:{},e:{}", uid, orderSeq, gameAccount, e.getMessage(), e);
            return null;
        }
    }

    public BaseRsp fillInUserAccountInfo(long uid, String orderSeq, String gameAccount, String gameSubAccount, String buyerMobile, String gameServerName, int gameAreaId) {
        FillInUserAccountInfoReq req = new FillInUserAccountInfoReq();
        orderSeq = ZHUIWAN_SEQ_PREFIX + orderSeq;
        req.setUid(uid);
        req.setSeqId(orderSeq);
        req.setGameAccount(gameAccount);
        req.setBuyerMobile(buyerMobile);
        req.setGameServerName(gameServerName);
        req.setGameAreaId(gameAreaId);
        req.setGameSubAccount(gameSubAccount);
        try {
            BaseRsp rsp = getProxy().fillInUserAccountInfo(req);
            log.info("fillInUserAccountInfo error,uid:{},orderSeqs:{},gameAccount:{},buyerMobile:{},gameServerName:{},gameAreaId:{},gameSubAccount:{},rsp:{}", uid, orderSeq, gameAccount, buyerMobile, gameServerName, gameAreaId, gameSubAccount, JSON.toJSONString(rsp));

            return rsp;
        } catch (Exception e) {
            log.error("fillInUserAccountInfo exception,uid:{},orderSeq:{},gameAccount:{},e:{}", uid, orderSeq, gameAccount, e.getMessage(), e);
            return null;
        }
    }

    public BaseRsp fillInUserAccountInfoV2(long uid, String orderSeq, String gameAccount, String gameSubAccount, String buyerMobile,
                                           String gameNick, String gameServerName, String gameAreaName, int gameAreaId) {
        FillInUserAccountInfoReq req = new FillInUserAccountInfoReq();
        orderSeq = ZHUIWAN_SEQ_PREFIX + orderSeq;
        req.setUid(uid);
        req.setSeqId(orderSeq);
        req.setGameAccount(gameAccount);
        req.setGameSubAccount(gameSubAccount);
        req.setBuyerMobile(buyerMobile);
        req.setGameNick(gameNick);
        req.setGameServerName(gameServerName);
        req.setGameAreaId(gameAreaId);
        req.setGameAreaName(gameAreaName);
        try {
            BaseRsp rsp = getProxy().fillInUserAccountInfo(req);
            log.info("fillInUserAccountInfo info,uid:{},orderSeqs:{},gameAccount:{}, buyerMobile:{}," +
                    "gameNick:{},gameServerName:{},gameAreaId:{},gameSubAccount:{},gameAreaName:{}, rsp:{}",
                    uid, orderSeq, gameAccount, buyerMobile, gameNick, gameServerName, gameAreaId, gameSubAccount,
                    gameAreaName, JSON.toJSONString(rsp));

            return rsp;
        } catch (Exception e) {
            log.error("fillInUserAccountInfo exception,uid:{},orderSeq:{},gameAccount:{},e:{}", uid, orderSeq, gameAccount, e.getMessage(), e);
            return null;
        }
    }

    public UserReleaseWithAccountInfoRsp getUserReleaseWithAccountInfo(long uid, String orderSeq) {
        orderSeq = ZHUIWAN_SEQ_PREFIX + orderSeq;
        UserReleaseWithAccountInfoReq req = new UserReleaseWithAccountInfoReq();
        req.setUid(uid);
        req.setSeqId(orderSeq);
        try {
            return getProxy().getUserReleaseWithAccountInfo(req);
        } catch (Exception e) {
            log.error("getUserReleaseWithAccountInfo exception,uid:{},orderSeq:{}", uid, orderSeq, e);
            return null;
        }

    }

    public void updateAccountInfo(long uid, String gameAccount, String gameSubAccount, String buyerMobile, String gameServerName, int gameAreaId, List<String> issueSeqs) {
        gameAccount = gameAccount.trim();
        buyerMobile = buyerMobile.trim();
        if (StringUtil.isEmpty(gameAccount) || StringUtil.isEmpty(buyerMobile)) {
            throw new BusinessException(500, "账号不能为空");
        }

        for (String issueSeq : issueSeqs) {
            BaseRsp rsp = fillInUserAccountInfo(uid, issueSeq, gameAccount, gameSubAccount, buyerMobile, gameServerName, gameAreaId);
            log.info("fillInUserAccountInfo uid:{},issueSeq:{},rsp:{}", uid, issueSeq, JsonUtil.toJson(rsp));
            if (rsp == null) {
                throw new BusinessException(500, "网络异常");
            } else if (rsp.getResult() != 0) {
                if (rsp.getMessage().contains(NOT_NEED_FILLIN)) {
                    log.info("updateAccountInfo not need fill in ,uid:{},issueSeq:{}", uid, issueSeq);
                } else {
                    throw new BusinessException(500, rsp.getMessage());
                }
            }
        }

    }

    public UserBatchReleaseRsp batchGetUserReleaseInfo(long uid, List<String> orderSeqs) {
        UserBatchReleaseReq req = new UserBatchReleaseReq();
        List<String> seqIds = orderSeqs.stream().map(v -> ZHUIWAN_SEQ_PREFIX + v).collect(Collectors.toList());
        req.setUid(uid);
        req.setSeqIdList(seqIds);
        try {
            UserBatchReleaseRsp rsp = getProxy().batchQueryUserReleaseWithAccountInfo(req);
            if (rsp.getCode() != 0) {
                log.error("batchGetUserReleaseInfo error,uid:{},seqIds:{},code:{},mas:{}", uid, JsonUtil.toJson(seqIds), rsp.getCode(), rsp.getMsg());
            }
            return rsp;
        } catch (Exception e) {
            log.error("batchGetUserReleaseInfo exception,uid:{},seqIds:{},e:{}", uid, JsonUtil.toJson(seqIds), e.getMessage(), e);
            return null;
        }
    }

    public BatchGetUserOnlineRsp batchGetUserOnline(List<Long> uids) {
        try {
            BatchGetUserOnlineReq req = new BatchGetUserOnlineReq();
            req.setUids(uids);
            return getProxy().batchGetUserOnline(req);
        } catch (Exception e) {
            log.error("batchGetUserOnline exception,uids:{},e:{}", JsonUtil.toJson(uids),e.getMessage(),e);
            return null;
        }
    }

    public long queryUserPostCount(long uid, Date startTime, Date endTime,long topicId) {
        QueryUserPostCountReq req = new QueryUserPostCountReq();
        req.setUid(uid);
        req.setStartTime(startTime.getTime());
        req.setEndTime(endTime.getTime());
        req.setTopicId(topicId);
        try {
            long res = getProxy().queryUserPostCount(req);
            log.info("queryUserPostCount req:{},count:{}", req, res);
            return res;
        } catch (Exception e) {
            log.error("queryUserPostCount exception,uid:{},start:{},end:{},e:{}", uid, startTime, endTime, e.getMessage(), e);
            return -1L;
        }
    }

}
