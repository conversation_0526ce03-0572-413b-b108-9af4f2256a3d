package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.event.*;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.yrpc.DanmakuActivityClient;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.client.WebdbUinfoClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.UserAchievementAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvagConfig;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.gameecology.hdzj.utils.JSONUtils;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@UseRedisStore
@RequestMapping("/5111")
@RestController
@Component
public class UserAchievementComponent extends BaseActComponent<UserAchievementAttr> {

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private DanmakuActivityClient danmakuActivityClient;

    private static final int USER_ACHIEVE_BANNER_ID = 52000;

    private final String TASK_COMPLETE_ZSET = "task_complete_%s";

    //宝箱频道
    private final String TASK_CHANNEL_LIST = "task_channel_list";

    private final static long LAY_OUT_ACT = 2024101002L;

    private final String BOX_INFO_HASH = "box_info";

    private final String BOX_DRAW_HIS = "box_draw_his_%s";

    private final String BOX_DRAW_REC = "box_draw_rec_%s";

    private static final String TASK_COMPLETE_LOCK = "task_complete_lock:%s";

    private static final String DRAW_LOCK = "draw_lock:%s";

    private static final String JOB_LOCK = "job_lock";

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Override
    public Long getComponentId() {
        return ComponentId.USER_ACHIEVEMENT;
    }

    @RequestMapping("/testBro")
    public Response testBroBestCp(long actId, long cmptInx, long uid, int level, long sid, long ssid) {
        if (SysEvHelper.isDeploy()) {
            return Response.success(null);
        }
        WebdbUserInfo webdbUserInfo = webdbUinfoClient.getUserInfo(uid);
        UserAchievementAttr attr = getComponentAttr(actId, cmptInx);
        broBanner(attr, sid, ssid, uid, System.currentTimeMillis()+"", webdbUserInfo, level);
        zaddTaskInfo(attr, level, sid, ssid, uid, System.currentTimeMillis()+"");
        return Response.success(null);
    }

    @Scheduled(cron = "0/2 * * * * ? ")
    @NeedRecycle(author = "guanqihua", notRecycle = true)
    @Report
    public void broLayout() {
        Set<Long> actIds = getComponentEffectActIds();
        if (CollectionUtils.isEmpty(actIds)) {
            return;
        }

        for (long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                continue;
            }

            Secret lock = null;
            UserAchievementAttr attr = tryGetUniqueComponentAttr(actId);
            String lockName = makeKey(attr, String.format(JOB_LOCK));
            try {
                lock = locker.lock(lockName, 5, UUID.randomUUID().toString(), 10);
                if(lock == null) {
                    continue;
                }
                long nowtime = System.currentTimeMillis();
                //没有全局宝箱
                Set<String> allBox = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, String.format(TASK_COMPLETE_ZSET, 0)), nowtime, Long.MAX_VALUE);
                if(allBox.isEmpty()) {
                    //查看有频道宝箱
                    Set<String> sidSsid = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                            makeKey(attr, TASK_CHANNEL_LIST), nowtime, Long.MAX_VALUE);
                    //对没有频道宝箱的发挂件关闭消息
                    List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(Template.Jiaoyou);
                    if (CollectionUtils.isEmpty(channelInfos)) {
                        return;
                    }
                    for (ChannelInfo channelInfo : channelInfos) {
                        String key = channelInfo.getSid() + "_" + channelInfo.getSsid();
                        //把有频道宝箱的频道过滤掉不关闭
                        if (sidSsid.contains(key)) {
                            continue;
                        }
                        JyLayerPushEvent jyLayerPushEvent = new JyLayerPushEvent();
                        jyLayerPushEvent.setProducerSeqID(UUID.randomUUID().toString());
                        jyLayerPushEvent.setProducerTime(nowtime / 1000);
                        jyLayerPushEvent.setEventType(1); //通知类型 1-子频道广播 2-uid单播通知
                        jyLayerPushEvent.setFromService(attr.getActId() + "-bro");
                        jyLayerPushEvent.setFromIP(SystemUtil.getIp());
                        jyLayerPushEvent.setSid(channelInfo.getSid());
                        jyLayerPushEvent.setSsid(channelInfo.getSsid());
                        jyLayerPushEvent.setStatus(2); //1 -打开 2 -关闭
                        jyLayerPushEvent.setActivityID(LAY_OUT_ACT); //2024101002 用来做这个活动id标识
                        threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
                            @Override
                            public void run() {
                                kafkaService.sendJiaoyouLayerKafka(jyLayerPushEvent);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                log.warn("broLayout error {}", e.getMessage(), e);
            }  finally {
                locker.unlock(lockName, lock, 5);
            }

        }
    }

    @RequestMapping("/queryLayerStatus")
    public Response queryLayerStatus(HttpServletRequest request, HttpServletResponse response, long actId, long cmptInx, long sid, long ssid, long uid) {
        log.info("queryLayerStatus actId:{}, cmptInx:{}, sid:{}, ssid:{}, uid:{}", actId, cmptInx, sid, ssid, uid);
        uid = uid > 0 ? uid : getLoginYYUid();
        Date now = new Date();
        long activityId = 2024101001L;
        UserAchievementAttr attr = getComponentAttr(activityId, cmptInx);
        List<Box> boxes = new ArrayList<>();
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(TASK_COMPLETE_ZSET, 0)), now.getTime(), Long.MAX_VALUE);
        for (String raw : rawSet) {
            Box box = JsonUtils.deserialize(raw, Box.class);
            boxes.add(box);
        }
        rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(TASK_COMPLETE_ZSET, sid+"_"+ssid)), now.getTime(), Long.MAX_VALUE);
        for (String raw : rawSet) {
            Box box = JsonUtils.deserialize(raw, Box.class);
            boxes.add(box);
        }
        boolean show = false;
        for (Box box : boxes) {
            show = StringUtil.isEmpty(actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(BOX_DRAW_REC, box.getBoxId())), String.valueOf(uid)));
            if(show) { //有还没抽的显示
                break;
            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("showStatus", show ? 1 : 0);
        result.put("time", System.currentTimeMillis());
        result.put("grey", commonService.isGrey(activityId));
        return Response.success(result);
    }

    @RequestMapping("/listBox")
    public Response listBox(HttpServletRequest request, HttpServletResponse response, long actId, long cmptInx, long sid, long ssid) {
        Long uid = getLoginYYUid(request, response);
        UserAchievementAttr attr = getComponentAttr(actId, cmptInx);
        Date now = new Date();
        List<Box> boxes = new ArrayList<>();
        List<Long> uids = new ArrayList<>();
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(TASK_COMPLETE_ZSET, 0)), now.getTime(), Long.MAX_VALUE);
        for (String raw : rawSet) {
            Box box = JsonUtils.deserialize(raw, Box.class);
            boxes.add(box);
        }
        rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(TASK_COMPLETE_ZSET, sid+"_"+ssid)), now.getTime(), Long.MAX_VALUE);
        for (String raw : rawSet) {
            Box box = JsonUtils.deserialize(raw, Box.class);
            boxes.add(box);
        }
        for (Box box : boxes) {
            box.setRev(!StringUtil.isEmpty(actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(BOX_DRAW_REC, box.getBoxId())), String.valueOf(uid))));
            uids.add(box.getUid());
        }
        Map<Long, UserBaseInfo> userInfos = commonService.batchGetUserInfos(List.copyOf(uids), false);
        for (Box box : boxes) {
            if(userInfos.containsKey(box.getUid())) {
                box.setName(userInfos.get(box.getUid()).getNick());
                box.setLogo(userInfos.get(box.getUid()).getHdLogo());
                box.setLeftSec(box.getEndTime() - now.getTime() <= 0L ? 0 : (int)(box.getEndTime() - now.getTime())/1000);
            }
        }
        boxes.sort((o1, o2) -> Math.toIntExact((o2.getEndTime() - o1.getEndTime())));
        ListRsp rsp = new ListRsp();
        if(attr.getBusiId() == BusiId.MAKE_FRIEND.getValue() && attr.getExcludeDanmaku() == 1) {
            List<ChannelInfo> danmakuChannel = danmakuActivityClient.queryAllChannelInfo();
            Set<String> exclude = danmakuChannel.stream().map(channel -> channel.getSid() + StringUtil.UNDERSCORE + channel.getSsid()).collect(Collectors.toSet());
            String currentChannel = sid + StringUtil.UNDERSCORE + ssid;
            if (exclude.contains(currentChannel)) {
                return Response.success(rsp);
            }
        }
        rsp.setBox(boxes);
        return Response.success(rsp);
    }

    @RequestMapping("/draw")
    public Response draw(HttpServletRequest request, HttpServletResponse response,
                         long actId, long cmptInx, String boxId) {
        DrawRsp drawRsp = new DrawRsp();
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            return Response.fail(400, "未登录");
        }
        UserAchievementAttr attr = getComponentAttr(actId, cmptInx);
        Date now = new Date();
        actRedisDao.hsetnx(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(BOX_DRAW_REC, boxId)), String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
        String retRaw = actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, BOX_INFO_HASH), boxId);
        if(StringUtil.isBlank(retRaw)) {
            Gift gift = new Gift();
            gift.setAward(false);
            drawRsp.setGift(gift);
            return Response.success(drawRsp);
        }
        try {
            checkLayOut(attr, now, uid);
        } catch (Exception e) {
            log.warn("checkLayOut uid:{}, e:{}", uid, e.getMessage(), e);
        }
        Box box = JsonUtils.deserialize(retRaw, Box.class);
        if(box.getEndTime() < now.getTime()) {
            log.info("draw box expire uid:{}, box:{}", uid, JsonUtil.toJson(box));
            Gift gift = new Gift();
            gift.setAward(false);
            drawRsp.setGift(gift);
            return Response.success(drawRsp);
        }
        boolean first = actRedisDao.hsetnx(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(BOX_DRAW_HIS, boxId)), String.valueOf(uid), DateUtil.getNowYyyyMMddHHmmss());
        if(first) {
            Secret lock = null;
            String lockName = makeKey(attr, String.format(DRAW_LOCK, String.valueOf(uid)));
            lock = locker.lock(lockName, 5, boxId + "_" + uid, 10);
            try {
                int size = 0;
                Map<Object, Object> rawMap = actRedisDao.hGetAll(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, String.format(BOX_DRAW_HIS, boxId)));
                if(rawMap != null) {
                    for (Object key : rawMap.keySet()) {
                        if(!Convert.toString(key).equals(Convert.toString(box.getUid()))
                                && !Convert.toString(key).equals(Convert.toString(uid))) {
                            size++;
                        }
                    }
                }
                if(size >= attr.getLotteryCnt() && !Convert.toString(box.getUid()).equals(Convert.toString(uid))) {
                    log.info("draw box too more size:{}, uid:{}, box:{}", size, uid, JsonUtil.toJson(box));
                    Gift gift = new Gift();
                    gift.setAward(false);
                    drawRsp.setGift(gift);
                    return Response.success(drawRsp);
                }

                long taskId = box.getUid() == uid ? attr.getUserLotteryTaskId() : attr.getLotteryTaskId();
                String time = DateUtil.format(now);
                String lotterySeq = MD5SHAUtil.getMD5(makeKey(attr, boxId + "_lottery")) + "_" + uid;
                BatchLotteryResult result = hdztAwardServiceClient.doLottery(time, attr.getBusiId(),
                        uid, taskId, 1, 0, lotterySeq);
                log.info("box draw boxId:{} uid:{}, creator:{}, ret:{}", boxId, uid, box.getUid(), JsonUtil.toJson(result));
                Response<List<LotteryAward>> lotteryResponse = lotteryAward(result, attr);
                if (lotteryResponse.success()) {
                    if (!lotteryResponse.getData().isEmpty()) {
                        LotteryAward award = lotteryResponse.getData().get(0);
                        Gift gift = new Gift();
                        gift.setName(award.getName());
                        gift.setPic(award.getImg());
                        gift.setAward(true);
                        if(attr.getSpecialPackageIds().contains(award.getPackageId())) {
                            gift.setTp(1);
                        }
                        drawRsp.setGift(gift);
                        return Response.success(drawRsp);
                    }
                }
            } catch (Exception e) {
                log.error("onTaskProgressChanged error {}", e.getMessage(), e);
            }  finally {
                locker.unlock(lockName, lock, 5);
            }
            Gift gift = new Gift();
            gift.setAward(false);
            drawRsp.setGift(gift);
            return Response.success(drawRsp);
        } else {
            return Response.fail(400, "重复请求");
        }
    }

    public void checkLayOut(UserAchievementAttr attr, Date now, long uid) {
        long nowtime = now.getTime();
        List<Box> boxes = new ArrayList<>();
        Set<String> rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, String.format(TASK_COMPLETE_ZSET, 0)), nowtime, Long.MAX_VALUE);
        for (String raw : rawSet) {
            Box box = JsonUtils.deserialize(raw, Box.class);
            boxes.add(box);
        }
        boolean allshow = false;
        for (Box boxT : boxes) {
            allshow = StringUtil.isEmpty(actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, String.format(BOX_DRAW_REC, boxT.getBoxId())), String.valueOf(uid)));
            if(allshow) { //有还没抽的显示
                break;
            }
        }
        //如果没有全局宝箱 / 全局宝箱已给抽 就看看当前是否有频道宝箱还没抽
        if(!allshow) {
            Set<String> sidSsid = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                    makeKey(attr, TASK_CHANNEL_LIST), nowtime - 1000, Long.MAX_VALUE);
            for (String s : sidSsid) {
                boxes = new ArrayList<>();
                rawSet = actRedisDao.zrevRangeByScore(redisConfigManager.getGroupCode(attr.getActId()),
                        makeKey(attr, String.format(TASK_COMPLETE_ZSET, s)), nowtime, Long.MAX_VALUE);
                for (String raw : rawSet) {
                    Box box = JsonUtils.deserialize(raw, Box.class);
                    boxes.add(box);
                }
                boolean channelshow = false;
                for (Box boxT : boxes) {
                    channelshow = StringUtil.isEmpty(actRedisDao.hget(redisConfigManager.getGroupCode(attr.getActId()),
                            makeKey(attr, String.format(BOX_DRAW_REC, boxT.getBoxId())), String.valueOf(uid)));
                    if (channelshow) { //有还没抽的显示
                        break;
                    }
                }

                if(!channelshow) {
                    JyLayerPushEvent jyLayerPushEvent = new JyLayerPushEvent();
                    jyLayerPushEvent.setProducerSeqID(UUID.randomUUID().toString());
                    jyLayerPushEvent.setProducerTime(nowtime/1000);
                    jyLayerPushEvent.setEventType(2); //通知类型 1-子频道广播 2-uid单播通知
                    jyLayerPushEvent.setUid(uid);
                    jyLayerPushEvent.setFromService(attr.getActId()+"-bro");
                    jyLayerPushEvent.setFromIP(SystemUtil.getIp());
                    jyLayerPushEvent.setStatus(2); //1 -打开 2 -关闭
                    jyLayerPushEvent.setActivityID(LAY_OUT_ACT); //2024101002 用来做这个活动id标识
                    threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
                        @Override
                        public void run() {
                            kafkaService.sendJiaoyouLayerKafka(jyLayerPushEvent);
                        }
                    });
                }
            }
            //全局宝箱没有，且当前子频道没宝箱。
            if(sidSsid.isEmpty()) {
                List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(Template.Jiaoyou);
                if (CollectionUtils.isEmpty(channelInfos)) {
                    return;
                }
                for (ChannelInfo channelInfo : channelInfos) {
                    String key = channelInfo.getSid() + "_" + channelInfo.getSsid();
                    //把有频道宝箱的频道过滤掉不关闭
                    if (sidSsid.contains(key)) {
                        continue;
                    }
                    JyLayerPushEvent jyLayerPushEvent = new JyLayerPushEvent();
                    jyLayerPushEvent.setProducerSeqID(UUID.randomUUID().toString());
                    jyLayerPushEvent.setProducerTime(nowtime / 1000);
                    jyLayerPushEvent.setEventType(2); //通知类型 1-子频道广播 2-uid单播通知
                    jyLayerPushEvent.setUid(uid);
                    jyLayerPushEvent.setFromService(attr.getActId() + "-bro");
                    jyLayerPushEvent.setFromIP(SystemUtil.getIp());
                    jyLayerPushEvent.setStatus(2); //1 -打开 2 -关闭
                    jyLayerPushEvent.setActivityID(LAY_OUT_ACT); //2024101002 用来做这个活动id标识
                    threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
                        @Override
                        public void run() {
                            kafkaService.sendJiaoyouLayerKafka(jyLayerPushEvent);
                        }
                    });
                }
            }
        }
    }


    @RequestMapping("/achievement")
    public Response achievement(HttpServletRequest request, HttpServletResponse response,
                                long actId, long cmptInx) {
        AchievementRsp rsp = new AchievementRsp();
        Long uid = getLoginYYUid(request, response);
        if(uid <= 0L) {
            Info info = new Info();
            rsp.setInfo(info);
            return Response.success(rsp);
        }
        UserAchievementAttr attr = getComponentAttr(actId, cmptInx);
        WebdbUserInfo userInfo = webdbUinfoClient.getUserInfo(uid);
        Info info = new Info();
        info.setNick(userInfo.getNick());
        info.setAvatar(userInfo.getAvatar());
        long score = 0L;
        List<Rank> list =
                hdztRankingThriftClient.queryRankingCache(actId, attr.getContributorRankId(), attr.getContributorPhaseId(), "", 0, Convert.toString(uid), null);
        if(list != null && !list.isEmpty() && list.get(0).getMember().equals(Convert.toString(uid))) {
            score = list.get(0).getScore();
        }
        info.setScore(score >=0 ? score : 0L);
        List<Long> values = attr.getLevelScoreMap().values().stream().sorted((o1, o2) -> Math.toIntExact((o1 - o2))).toList();
        for(int i = 0; i < values.size(); i++) {
            if(score >= values.get(i)) {
                info.setLevel(i+1);
            }
        }
        rsp.setInfo(info);
        return Response.success(rsp);
    }

    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, UserAchievementAttr attr) {
        if (event.getRankId() != attr.getTaskRankId()) {
            log.info("onTaskProgressChanged rankId:{} not match", event.getRankId());
            return;
        }
        log.info("onTaskProgressChanged, task:{}", JSONUtils.toJsonString(event));
        long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
        if (startIndex == currIndex) {
            return;
        }
        String seq = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();
        String member = event.getMember();
        WebdbUserInfo userInfo = webdbUinfoClient.getUserInfo(Long.parseLong(member));
        String sidSsidStr = event.getActors().get(attr.getTingActor());
        String[] sidSsidArray = sidSsidStr.split("_");
        long sid = Convert.toLong(sidSsidArray[0]);
        long ssid = Convert.toLong(sidSsidArray[1]);
        Secret lock = null;
        String lockName = makeKey(attr, String.format(TASK_COMPLETE_LOCK, member));
        lock = locker.lock(lockName, 5, event.getSeq(), 10);
        try {
            if (lock != null) {
                for (long taskId = startIndex + 1; taskId <= currIndex; taskId++) {
                    UserAchievementAttr.BroType broType = attr.getLevelBroMap().get((int)taskId);
                    zaddTaskInfo(attr, broType.getMp4BroType(), sid, ssid, Long.parseLong(member), seq+"_"+taskId);
                }
                broBanner(attr, sid, ssid, Long.parseLong(member), seq, userInfo, (int)currIndex);
            }
        } catch (Exception e) {
            log.error("onTaskProgressChanged error {}", e.getMessage(), e);
        }  finally {
            locker.unlock(lockName, lock, 5);
        }
    }

    private void broBanner(UserAchievementAttr attr, long sid, long ssid, long uid, String seq,
                           WebdbUserInfo userInfo, int level) {
        String nick = userInfo.getNick();
        long actId = attr.getActId();
        String logo = WebdbUtils.getLogo(userInfo);
        Map<String, Object> extMap = Maps.newHashMapWithExpectedSize(10);
        extMap.put("userNick", org.apache.commons.codec.binary.Base64.encodeBase64String(nick.getBytes()));
        extMap.put("userLogo", logo);
        extMap.put("sid", sid);
        extMap.put("ssid", ssid);
        extMap.put("level", level);
        GameecologyActivity.BannerBroadcast bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(actId).setUserUid(uid).setUserNick(nick).setUserLogo(logo)
                .setBannerId(USER_ACHIEVE_BANNER_ID).setBannerType(0)
                .setUserScore(0).setJsonData(JSON.toJSONString(extMap))
                .build();
        GameecologyActivity.GameEcologyMsg bannerBroMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(bannerBroadcast).build();
        BusiId busiId = BusiId.findByValue((int) attr.getBusiId());

        if(attr.getBannerSvag() != null) {
            List<BannerSvagConfig> list = new ArrayList<>(attr.getBannerSvag().values());
            BannerSvagConfig svagConfig = list.get(level - 1);
            UserAchievementAttr.BroType broType = attr.getLevelBroMap().get(level);
            AppBannerSvgaConfig2 broSvgaConfig = new AppBannerSvgaConfig2();
            UserBaseInfo userInfoT = new UserBaseInfo();
            userInfoT.setUid(Convert.toLong(userInfo.getUid()));
            userInfoT.setNick(userInfo.getNick());
            UserBaseInfo anchorUserInfoT = new UserBaseInfo();
            //svga内嵌文字
            Set<Long> uids = new HashSet<>();
            uids.add(uid);
            List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, svagConfig, userInfoT, anchorUserInfoT);
            broSvgaConfig.setContentLayers(broContentLayers);
            //svga内嵌图片
            MemberInfo memberInfo = new MemberInfo();
            memberInfo.setHdLogo(logo);
            List<Map<String, String>> broImgLayers = getSvgaImageConfig(attr, memberInfo);
            broSvgaConfig.setImgLayers(broImgLayers);

            broSvgaConfig.setLoops(attr.getLoops());
            AppBannerLayout layout = new AppBannerLayout();
            layout.setType(attr.getLayoutType());
            if (StringUtil.isNotBlank(attr.getLayoutMargin())) {
                layout.setMargin(JSON.parseObject(attr.getLayoutMargin(), new TypeReference<Map<String, List<Integer>>>() {
                }));
            }
            broSvgaConfig.setLayout(layout);
            broSvgaConfig.setWhRatio(attr.getWhRatio());
            broSvgaConfig.setClickLayerName(svagConfig.getClickLayerName());
            broSvgaConfig.setSvgaURL(svagConfig.getSvgaURL());
            broSvgaConfig.setJumpSvgaURL(svagConfig.getJumpSvgaURL());
            broSvgaConfig.setMiniURL(svagConfig.getMiniURL());
            broSvgaConfig.setJumpMiniURL(svagConfig.getJumpMiniURL());

            AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, attr.getBroBusiId(),
                    broType.getSvgaBroType(), sid, ssid, "",
                    Lists.newArrayList());
            appBannerEvent.setUid(uid);
            appBannerEvent.setUidList(List.copyOf(uids));
            appBannerEvent.setAppId(KafkaService.getTurnoverAppId((int) attr.getBusiId()));

            List<Map<String, String>> layerKeys = new ArrayList<>();
            for (Map<String, AppBannerSvgaText> broContentLayer : broContentLayers) {
                for (String key : broContentLayer.keySet()) {
                    Map<String, String> map = new HashMap<>();
                    map.put(key, broContentLayer.get(key).getText());
                    layerKeys.add(map);
                }
            }
            for (Map<String, String> broImgLayer : broImgLayers) {
                for (String key : broImgLayer.keySet()) {
                    Map<String, String> map = new HashMap<>();
                    map.put(key, broImgLayer.get(key));
                    layerKeys.add(map);
                }
            }
            if(attr.isSvga()) {
                appBannerEvent.setContentType(6);
                appBannerEvent.setSvgaConfig(broSvgaConfig);
            } else {
                appBannerEvent.setContentType(5);
                AppBannerMp4Config appBannerMp4Config = new AppBannerMp4Config();
                appBannerMp4Config.setUrl(svagConfig.getSvgaURL());
                appBannerMp4Config.setLevel(999);
                appBannerMp4Config.setLayerExtKeyValues(layerKeys);
                appBannerEvent.setMp4Config(appBannerMp4Config);
            }
            broadCastHelpService.broadcastExcludeDanmaku(seq, attr.getActId(), busiId, broType.getSvcBroType(), sid, ssid, bannerBroMsg, attr.getExcludeDanmaku());
            if (attr.getBusiId() == BusiId.MAKE_FRIEND.getValue() && attr.getExcludeDanmaku() == 1) {
                kafkaService.sendAppBannerKafkaExcludeDanmuku(appBannerEvent);
            } else {
                kafkaService.sendAppBannerKafka(appBannerEvent);
            }
        }
    }

    private void zaddTaskInfo(UserAchievementAttr attr, int broType, long sid, long ssid, long uid, String seq) {
        String taskCompletePuzzleKey = String.format(TASK_COMPLETE_ZSET, 0);
        long nowtime = System.currentTimeMillis();
        double score = System.currentTimeMillis() + 120000;
        //
        if (broType != 4) {
            taskCompletePuzzleKey = String.format(TASK_COMPLETE_ZSET, sid + "_" + ssid);
            actRedisDao.zAdd(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, TASK_CHANNEL_LIST), sid + "_" + ssid, score);
            // 给子频道推开起挂件
            JyLayerPushEvent jyLayerPushEvent = new JyLayerPushEvent();
            jyLayerPushEvent.setProducerSeqID(UUID.randomUUID().toString());
            jyLayerPushEvent.setProducerTime(nowtime/1000);
            jyLayerPushEvent.setSid(sid);
            jyLayerPushEvent.setSsid(ssid);
            jyLayerPushEvent.setEventType(1); //通知类型 1-子频道广播 2-uid单播通知
            jyLayerPushEvent.setUid(0);
            jyLayerPushEvent.setFromService(attr.getActId() + "-bro");
            jyLayerPushEvent.setFromIP(SystemUtil.getIp());
            jyLayerPushEvent.setStatus(1); //1 -打开 2 -关闭
            jyLayerPushEvent.setActivityID(LAY_OUT_ACT); //2024101002 用来做这个活动id标识
            threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
                @Override
                public void run() {
                    kafkaService.sendJiaoyouLayerKafka(jyLayerPushEvent);
                }
            });
        } else { //全频道广播 给全频道开启挂件
            List<ChannelInfo> channelInfos = commonService.queryOnlineChannel(Template.Jiaoyou);
            if (CollectionUtils.isEmpty(channelInfos)) {
                return;
            }
            for (ChannelInfo channelInfo : channelInfos) {
                JyLayerPushEvent jyLayerPushEvent = new JyLayerPushEvent();
                jyLayerPushEvent.setProducerSeqID(UUID.randomUUID().toString());
                jyLayerPushEvent.setProducerTime(nowtime/1000);
                jyLayerPushEvent.setSid(channelInfo.getSid());
                jyLayerPushEvent.setSsid(channelInfo.getSsid());
                jyLayerPushEvent.setEventType(1); //通知类型 1-子频道广播 2-uid单播通知
                jyLayerPushEvent.setUid(0);
                jyLayerPushEvent.setFromService(attr.getActId() + "-bro");
                jyLayerPushEvent.setFromIP(SystemUtil.getIp());
                jyLayerPushEvent.setStatus(1); //1 -打开 2 -关闭
                jyLayerPushEvent.setActivityID(LAY_OUT_ACT); //2024101002 用来做这个活动id标识
                threadPoolManager.get(Const.GENERAL_POOL).execute(new Runnable() {
                    @Override
                    public void run() {
                        kafkaService.sendJiaoyouLayerKafka(jyLayerPushEvent);
                    }
                });
            }
        }
        long now = System.currentTimeMillis();
        Box box = new Box();
        box.setUid(uid);
        box.setBoxId(seq);
        box.setEndTime(now + 120000);
        actRedisDao.zAdd(redisConfigManager.getGroupCode(attr.getActId()),
                makeKey(attr, taskCompletePuzzleKey), JsonUtil.toJson(box), score);
        actRedisDao.hset(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, BOX_INFO_HASH), seq, JsonUtil.toJson(box));
        log.info("add zset puzzle zkey:{}, babyCpInfo:{}", makeKey(attr, taskCompletePuzzleKey), JsonUtil.toJson(box));
    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(UserAchievementAttr attr, BannerSvagConfig svagConfig,
                                                                   UserBaseInfo userInfo, UserBaseInfo anchorUserInfo) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();
        String contentLayerCodes = svagConfig.getContentLayerCodes();
        if (StringUtil.isNotBlank(contentLayerCodes)) {
            String[] contentLayerCodeArr = contentLayerCodes.split(",");
            for (String contentLayerCode : contentLayerCodeArr) {
                Map<String, AppBannerSvgaText> broSvgaTextLayer = Maps.newHashMap();
                BannerSvgaTextConfig textConfig = attr.getSvgaText().get(contentLayerCode);
                if (textConfig == null) {
                    continue;
                }
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
                //配置动态替换文本
                String text = contextReplace(textConfig.getText(), userInfo, anchorUserInfo);
                if (attr.getTextDynamicValue() != null) {
                    Map<String, String> replaceValue = attr.getTextDynamicValue();
                    for (String key : replaceValue.keySet()) {
                        text = text.replace(key, replaceValue.get(key));
                    }
                }
                appBannerSvgaText.setText(text);
                appBannerSvgaText.setNameCountLimit(textConfig.getNameCountLimit());
                appBannerSvgaText.setGravity(textConfig.getGravity());
                if (StringUtil.isNotBlank(textConfig.getImages())) {
                    appBannerSvgaText.setImgs(Lists.newArrayList(textConfig.getImages().split(",")));
                }
                if (StringUtil.isNotBlank(textConfig.getFontSize())) {
                    appBannerSvgaText.setFontSize(JSON.parseObject(textConfig.getFontSize(), Map.class));
                }
                broSvgaTextLayer.put(textConfig.getKey(), appBannerSvgaText);

                if (MapUtils.isNotEmpty(broSvgaTextLayer)) {
                    broContentLayers.add(broSvgaTextLayer);
                }
            }
        }
        return broContentLayers;
    }

    private List<Map<String, String>> getSvgaImageConfig(UserAchievementAttr attr, MemberInfo memberInfo) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        if (MapUtils.isEmpty(attr.getSvgaImgLayers())) {
            return broImgLayers;
        }
        Map<String, String> imageMap = attr.getSvgaImgLayers();
        for (String imageKey : imageMap.keySet()) {
            Map<String, String> broImgLayer = Maps.newHashMap();
            String image = imageMap.get(imageKey);
            String value = replaceImage(image, memberInfo);
            broImgLayer.put(imageKey, value);
            broImgLayers.add(broImgLayer);
        }
        return broImgLayers;
    }

    private String contextReplace(String context, UserBaseInfo userInfo, UserBaseInfo anchorUserInfo) {
        context = context.replace("{nick}", String.format("{%s:n}", userInfo.getUid()));
        context = context.replace("{anchorNick}", String.format("{%s:n}", anchorUserInfo.getUid()));
        return context;
    }

    private String replaceImage(String context, MemberInfo memberInfo) {
        return context.replace("{header}", Convert.toString(memberInfo.getHdLogo()));
    }

    public Response<List<LotteryAward>> lotteryAward(BatchLotteryResult batchLotteryResult, UserAchievementAttr attr) {
        if (batchLotteryResult.getCode() != 0) {
            return Response.fail(3, batchLotteryResult.getReason());
        }
        List<LotteryAward> awardList = Lists.newArrayList();
        Map<Long, AwardModelInfo> packageInfoMap = packageInfoMap(attr);
        Map<Long, Long> recordIds = batchLotteryResult.getRecordPackages();
        Map<Long, Integer> pidCount = Maps.newHashMap();
        for (Long pid : recordIds.values()) {
            pidCount.merge(pid, 1, Integer::sum);
        }
        for (Map.Entry<Long, Integer> entry : pidCount.entrySet()) {
            AwardModelInfo awardModelInfo = packageInfoMap.get(entry.getKey());
            if (awardModelInfo != null) {
                if(awardModelInfo.getPackageName().contains("谢谢参与")) {
                    continue;
                }
                LotteryAward award = new LotteryAward();
                award.setPackageId(awardModelInfo.getPackageId());
                award.setName(awardModelInfo.getPackageName());
                award.setImg(awardModelInfo.getPackageImage());
                award.setNum(entry.getValue() == 1 ? 0 : entry.getValue());
                awardList.add(award);
            }
        }
        return Response.success(awardList);
    }

    public Map<Long, AwardModelInfo> packageInfoMap(UserAchievementAttr attr) {
        try {

            Map<Long, AwardModelInfo> tasks = hdztAwardServiceClient.queryAwardTasks(attr.getLotteryTaskId());
            var userTasks = hdztAwardServiceClient.queryAwardTasks(attr.getUserLotteryTaskId());

            Map<Long, AwardModelInfo> packageInfoMap = new HashMap<>(17);
            if (MapUtils.isNotEmpty(tasks)) {
                packageInfoMap.putAll(tasks);
            }

            if (MapUtils.isNotEmpty(userTasks)) {
                packageInfoMap.putAll(userTasks);
            }

            return packageInfoMap;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.EMPTY_MAP;
    }

    @Data
    public static class ListRsp {
        private List<Box> box;
    }

    @Data
    public static class DrawRsp {
        private Gift gift;
    }

    @Data
    public static class AchievementRsp {
        private Info info;
    }

    @Data
    public static class Box {
        private String boxId;
        private boolean rev;
        private long endTime;
        private int leftSec;
        private long uid;
        private String name;
        private String logo;
    }

    @Data
    public static class Gift {
        private String name;
        private String pic;
        private int tp;
        private boolean award;
    }

    @Data
    public static class LotteryAward {
        private long packageId;

        private String name;

        private String img;

        private int num;
    }

    @Data
    public static class Info {
        private String nick;

        private String avatar;

        private long score;

        private int level;
    }
}
