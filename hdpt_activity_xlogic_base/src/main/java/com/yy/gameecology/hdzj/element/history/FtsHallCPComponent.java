package com.yy.gameecology.hdzj.element.history;

import cn.hutool.core.convert.Convert;
import cn.hutool.http.HtmlUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.JiaoyouComboEndEvent;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerLayout;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaConfig2;
import com.yy.gameecology.activity.bean.event.AppBannerSvgaText;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.ActivityTimeStart;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeStart;
import com.yy.gameecology.activity.bean.mq.FtsCompereOnlineEvent;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.service.BaiduInfoFlowRobotService;
import com.yy.gameecology.activity.service.DelaySvcSDKServiceV2;
import com.yy.gameecology.activity.service.KafkaService;
import com.yy.gameecology.common.bean.UserBaseInfo;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.consts.PBCommonNoticeType;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.LotteryUtils;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.FtsHallCPComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.fts_base_info_bridge.UserInfo;
import com.yy.thrift.hdztranking.BusiId;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

@UseRedisStore
@Slf4j
@RestController
@RequestMapping("5097")
public class FtsHallCPComponent extends BaseActComponent<FtsHallCPComponentAttr> implements InitializingBean {

    private static final String[] TEXT_SEARCH_LIST = {"{userNick}", "{anchorNick}", "{rewardCount}", "{rewardUnit}"};

    private static final String[] IMAGE_SEARCH_LIST = {"{userAvatar}", "{anchorAvatar}", "{rewardIcon}"};

    private static final String ANCHOR_FIRST_NOTICE = "anchor_first_notice";

    private static final String TOTAL_STEP = "total_step";

    private static final String GRADE_RECORD = "grade_record:%d";

    private static final String USER_LIMIT = "user_daily_limit:%s";

    private static final String WORTH_LIMIT = "worth_daily_limit:%s";

    private static final String REWARD_RECORD = "reword_record";

    private static final String REWARD_SEQ = "reward_5097:%d:%d";

    /**
     * 使用一个简单的lua脚本，将前一天剩余的钱转到下一天
     */
    private static final String TRANSFER_BALANCE_SCRIPT = """
            local balance = tonumber(redis.call('GET', KEYS[1]) or 0)
            if balance > 0 then
                redis.call('SET', KEYS[1], '0')
                redis.call('INCRBY', KEYS[2],  balance)
            end
            return balance
            """;

    private static final RedisScript<Long> TRANSFER_SCRIPT = new DefaultRedisScript<>(TRANSFER_BALANCE_SCRIPT, Long.class);

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private DelaySvcSDKServiceV2 delaySvcSDKServiceV2;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Override
    public Long getComponentId() {
        return ComponentId.HALL_CP;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        registerDelayQueue("transfer_balance", this::transferBalances);
    }

    @HdzjEventHandler(value = ActivityTimeStart.class, canRetry = false)
    public void handleActivityStart(ActivityTimeStart ignored, FtsHallCPComponentAttr attr) {
        Map<String, Integer> limit = attr.getDailyWorthLimit();
        if (MapUtils.isEmpty(limit)) {
            log.warn("daily worth limit not config");
            return;
        }

        Map<String, String> map = new HashMap<>(limit.size());
        for (Map.Entry<String, Integer> entry : limit.entrySet()) {
            String worthLimitKey = makeKey(attr, String.format(WORTH_LIMIT, entry.getKey()));
            map.put(worthLimitKey, String.valueOf(entry.getValue()));
        }

        actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId())).opsForValue().multiSet(map);
        log.info("handle activity start init limit success:{}", limit);
    }

    @HdzjEventHandler(value = PhaseTimeStart.class, canRetry = false)
    public void handleRollingPhaseTimeStart(PhaseTimeStart event, FtsHallCPComponentAttr attr) {
        if (event.getPhaseId() == attr.getRollingPhaseId() && event.getRankId() == attr.getRollingRankId()) {
            final Date date = DateUtil.getDate(event.getStartTime(), DateUtil.DEFAULT_PATTERN);
            publishDelayEvent(attr, "transfer_balance", date, System.currentTimeMillis() + 10 * DateUtils.MILLIS_PER_SECOND);
        }
    }

    public void transferBalances(FtsHallCPComponentAttr attr, Object event) {
        if (!(event instanceof Date date)) {
            return;
        }
        String yesterday = DateFormatUtils.format(DateUtils.addDays(date, -1), DateUtil.PATTERN_TYPE2);
        int today = Integer.parseInt(DateFormatUtils.format(date, DateUtil.PATTERN_TYPE2));
        String fromKey = makeKey(attr, String.format(WORTH_LIMIT, yesterday));
        String toKey = makeKey(attr, String.format(WORTH_LIMIT, today));

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        Long rs = redisTemplate.execute(TRANSFER_SCRIPT, ImmutableList.of(fromKey, toKey));
        log.info("transfer balance from:{}, to:{}, rs:{}", yesterday, today, rs);
        String msg = String.format("【殿堂CP】每日余额流转完成，流转金额：%d", rs);
        String inflowMsg = buildActRuliuMsg(attr.getActId(), false, "每日奖池余额流转", msg);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT, inflowMsg, Lists.newArrayList());

        if (attr.getBreakDates().contains(today)) {
            String stepKey = makeKey(attr, TOTAL_STEP);
            String value = redisTemplate.opsForValue().get(stepKey);
            long currentStep = 0;
            if (StringUtils.isNumeric(value)) {
                currentStep = Long.parseLong(value);
            }

            if (currentStep % attr.getAwardStep() != 0) {
                long incr = attr.getAwardStep() - currentStep % attr.getAwardStep();
                redisTemplate.opsForValue().increment(stepKey, incr);
                log.info("divide day skip step size:{}", incr);
            }
        }
    }

    @HdzjEventHandler(value = FtsCompereOnlineEvent.class, canRetry = true)
    public void handleAnchorStartShow(FtsCompereOnlineEvent event, FtsHallCPComponentAttr attr) {
        log.info("handleAnchorStartShow with {}", event);
        Date now = commonService.getNow(attr.getActId());
        int date = Integer.parseInt(DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2));
        if (date < attr.getStartDate() || date > attr.getEndDate()) {
            log.info("handleAnchorStartShow in play time skip");
            return;
        }
        String key = makeKey(attr, ANCHOR_FIRST_NOTICE);
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        boolean rs = redisTemplate.opsForHash().putIfAbsent(key, String.valueOf(event.getUid()), DateUtil.getNowYyyyMMddHHmmss());
        if (rs) {
            GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                    .setActId(attr.getActId())
                    .setNoticeType(PBCommonNoticeType.HALL_CP_FIRST_NOTICE)
                    .setNoticeValue(attr.getFirstNoticeMsg());

            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                    .setCommonNoticeResponse(panel).build();
            delaySvcSDKServiceV2.unicastUid(event.getUid(), msg, 5);
            log.info("first notice send unicast with uid:{}", event.getUid());
        }
    }

    @HdzjEventHandler(value = JiaoyouComboEndEvent.class, canRetry = true)
    public void handleFtsComboEndEvent(JiaoyouComboEndEvent event, FtsHallCPComponentAttr attr) {
        log.info("handleFtsComboEndEvent with event:{}", event.outline());
        String giftId = MapUtils.getString(event.getExpand(), "propId");
        if (!attr.getGiftIds().contains(giftId)) {
            return;
        }

        long priceSum = MapUtils.getLongValue(event.getExpand(), "priceSum");
        final long threshold = attr.getThreshold() * 100;
        if (threshold > priceSum) {
            return;
        }

        Date time = new Date(event.getTimestamp() * 1000);
        if (!SysEvHelper.isDeploy() || commonService.isGrey(attr.getActId())) {
            time = commonService.getNow(attr.getActId());
        }

        int date = Integer.parseInt(DateFormatUtils.format(time, DateUtil.PATTERN_TYPE2));
        if (attr.getBreakDates().contains(date)) {
            log.info("skip break date");
            return;
        }

        final long anchorUid = event.getRecvUid(), userUid = event.getSendUid();
        Pair<Long, Long> signInfo = ftsBaseInfoBridgeClient.getFtsCompereSign(anchorUid);
        if (signInfo == null || signInfo.getLeft() <= 0) {
            log.info("handleFtsComboEndEvent anchorUid not sign:{}", anchorUid);
            return;
        }

        final StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(attr.getActId()));
        String seq = makeKey(attr, event.getSeqId());
        Boolean set = redisTemplate.opsForValue().setIfAbsent(seq, DateUtil.getNowYyyyMMddHHmmss(), 1, TimeUnit.DAYS);
        if (!Boolean.TRUE.equals(set)) {
            log.warn("already invoke seq:{}", seq);
            return;
        }

        long incrStep = priceSum / threshold;

        String totalKey = makeKey(attr, TOTAL_STEP);
        final long currentStep = Objects.requireNonNull(redisTemplate.opsForValue().increment(totalKey, incrStep));
        final long fromStep = currentStep - incrStep;
        long startStep = fromStep;
        FtsHallCPComponentAttr.RewardItem hit = null;
        for (long step = fromStep + 1; step <= currentStep; step++) {
            if (step % attr.getAwardStep() == 0) {
                // 新增一条记录
                addGradeRecord(anchorUid, userUid, startStep, step, attr, redisTemplate);

                // 尝试发奖
                FtsHallCPComponentAttr.RewardItem thisHit = reward(anchorUid, userUid, step, time, attr);
                hit = thisHit == null ? hit : thisHit;

                startStep = step;
            }
        }

        if (currentStep % attr.getAwardStep() != 0) {
            addGradeRecord(anchorUid, userUid, startStep, currentStep, attr, redisTemplate);
        }

        // send notice
        sendGradeNotice(attr.getActId(), anchorUid, userUid, incrStep, attr.getGradeNoticeTpl());

        // 全服CP动画
        if (hit != null) {
            final FtsHallCPComponentAttr.RewardItem reward = hit;
            threadPoolManager.get(Const.IMPORTANT_POOL).execute(() -> sendCPHighlight(attr, anchorUid, userUid, currentStep, reward));
        }
    }

    private void addGradeRecord(long anchorUid, long userUid, long fromStep, long currentStep, FtsHallCPComponentAttr attr, StringRedisTemplate redisTemplate) {
        long group = currentStep / attr.getAwardStep();
        long currentGrade = currentStep % attr.getAwardStep();
        if (currentGrade == 0) {
            group--;
            currentGrade = attr.getAwardStep();
        }

        long addGrade = Math.min(currentStep - fromStep, attr.getAwardStep());
        GradeRecord gradeRecord = new GradeRecord(anchorUid, userUid, group, addGrade, currentGrade);
        String key = makeKey(attr, String.format(GRADE_RECORD, group));
        String value = JSON.toJSONString(gradeRecord);
        redisTemplate.opsForZSet().add(key, value, currentStep);
    }

    private FtsHallCPComponentAttr.RewardItem reward(long anchorUid, long userUid, long currentStep, Date time, FtsHallCPComponentAttr attr) {
        log.info("start reward with anchorUid:{} userUid:{} currentStep:{} time:{}", anchorUid, userUid, currentStep, time);
        List<FtsHallCPComponentAttr.RewardItem> rewardItems = attr.getRewardItems();
        String dateStr = DateFormatUtils.format(time, DateUtil.PATTERN_TYPE2);
        String userLimitKey = makeKey(attr, String.format(USER_LIMIT, dateStr));
        String worthLimitKey = makeKey(attr, String.format(WORTH_LIMIT, dateStr));
        List<String> hashKeys = new ArrayList<>(rewardItems.size());
        List<Long> packageIds = new ArrayList<>(rewardItems.size());
        for (FtsHallCPComponentAttr.RewardItem item : rewardItems) {
            if (item.getLimit() <= 0) {
                continue;
            }

            hashKeys.add(String.format("%d|%d:%d", userUid, anchorUid, item.getRewardPackageId()));
            packageIds.add(item.getRewardPackageId());
        }

        String groupCode = getRedisGroupCode(attr.getActId());
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(groupCode);
        List<String> values = redisTemplate.<String, String>opsForHash().multiGet(userLimitKey, hashKeys);
        Map<Long, Integer> sentMap = new HashMap<>(packageIds.size());
        for (int i = 0; i < packageIds.size(); i++) {
            sentMap.put(packageIds.get(i), Convert.toInt(values.get(i), 0));
        }

        final int worthLimit;
        String value = redisTemplate.opsForValue().get(worthLimitKey);
        if (StringUtils.isNumeric(value)) {
            worthLimit = Integer.parseInt(value);
        } else {
            log.error("worth limit not init!");
            worthLimit = attr.getDailyWorthLimit().getOrDefault(dateStr, 0);
        }

        Map<FtsHallCPComponentAttr.RewardItem, Integer> candidates = new HashMap<>(rewardItems.size());
        for (FtsHallCPComponentAttr.RewardItem item : rewardItems) {
            int sent = sentMap.getOrDefault(item.getRewardPackageId(), 0);
            // 单项数量达到上限
            if (item.getLimit() > 0 && sent >= item.getLimit()) {
                continue;
            }

            // 超过每日总价值余量
            if (item.getWorth() > worthLimit) {
                continue;
            }

            candidates.put(item, item.getProbability());
        }

        FtsHallCPComponentAttr.RewardItem hit = doReward(attr, candidates, anchorUid, userUid, currentStep, groupCode, worthLimitKey, userLimitKey);
        if (hit != null) {
            RewardRecord record = new RewardRecord(currentStep, anchorUid, userUid, hit.getRewardPackageId(), time);
            String recordKey = makeKey(attr, REWARD_RECORD);
            redisTemplate.opsForZSet().add(recordKey, JSON.toJSONString(record), currentStep);
        }

        log.info("done reward with anchorUid:{} userUid:{} currentStep:{} time:{} hit:{}", anchorUid, userUid, currentStep, time, hit);

        return hit;
    }

    private FtsHallCPComponentAttr.RewardItem doReward(FtsHallCPComponentAttr attr, Map<FtsHallCPComponentAttr.RewardItem, Integer> candidates, long anchorUid, long userUid, long currentStep, String groupCode, String worthLimitKey, String userLimitKey) {
        if (MapUtils.isEmpty(candidates)) {
            log.error("trying to reward but candidates is empty");
            return null;
        }
        FtsHallCPComponentAttr.RewardItem hit = LotteryUtils.lottery(candidates);
        // 尝试增加
        if (hit.getWorth() > 0) {
            List<Long> result = actRedisDao.incrValueWithLimit(groupCode, worthLimitKey, -hit.getWorth(), 0, false);
            if (result.get(0) != 1) {
                log.error("trying to reward decr worth limit fail, will redo it");
                candidates.remove(hit);
                return doReward(attr, candidates, anchorUid, userUid, currentStep, groupCode, worthLimitKey, userLimitKey);
            }
        }

        if (hit.getLimit() > 0) {
            String field = String.format("%d|%d:%d", userUid, anchorUid, hit.getRewardPackageId());
            List<Long> result = actRedisDao.hIncrWithLimit(groupCode, userLimitKey, field, 1, hit.getLimit());
            if (result.get(0) != 1) {
                log.error("trying to reward incr user limit fail, will rollback worth decr and redo it");
                actRedisDao.incrValue(groupCode, worthLimitKey, hit.getWorth());
                candidates.remove(hit);
                return doReward(attr, candidates, anchorUid, userUid, currentStep, groupCode, worthLimitKey, userLimitKey);
            }
        }

        // 执行发奖
        if (anchorUid == userUid) {
            String seq = String.format(REWARD_SEQ, currentStep, anchorUid);
            hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.MAKE_FRIEND.getValue(), anchorUid, attr.getRewardTaskId(), 2, hit.getRewardPackageId(), seq, Collections.emptyMap());
        } else {
            String anchorSeq = String.format(REWARD_SEQ, currentStep, anchorUid);
            String userSeq = String.format(REWARD_SEQ, currentStep, userUid);
            hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.MAKE_FRIEND.getValue(), anchorUid, attr.getRewardTaskId(), 1, hit.getRewardPackageId(), anchorSeq, Map.of("cpUid", String.valueOf(userUid)));
            hdztAwardServiceClient.doWelfareV2(DateUtil.getNowYyyyMMddHHmmss(), BusiId.MAKE_FRIEND.getValue(), userUid, attr.getRewardTaskId(), 1, hit.getRewardPackageId(), userSeq, Map.of("cpUid", String.valueOf(anchorUid)));
        }

        return hit;
    }

    private void sendCPHighlight(FtsHallCPComponentAttr attr, long anchorUid, long userUid, long step, FtsHallCPComponentAttr.RewardItem hit) {
        Map<Long, UserInfo> userInfoMap = ftsBaseInfoBridgeClient.getFtsUserInfoMap(List.of(anchorUid, userUid));
        UserInfo anchorInfo = userInfoMap.get(anchorUid), userInfo = userInfoMap.get(userUid);
        if (anchorInfo == null) {
            anchorInfo = new UserInfo();
            anchorInfo.uid = anchorUid;
            anchorInfo.nick = "神秘主持";
            anchorInfo.avatar_url = Const.IMAGE.DEFAULT_USER_LOGO;
        }

        if (userInfo == null) {
            userInfo = new UserInfo();
            userInfo.uid = userUid;
            userInfo.nick = "神秘用户";
            userInfo.avatar_url = Const.IMAGE.DEFAULT_USER_LOGO;
        }
        sendBroadcast(attr, anchorInfo, userInfo, hit);
        String seq = makeKey(attr, String.format("step_bro:%d", step));
        sendAppBroadcast(seq, attr, anchorInfo, userInfo, hit);
    }

    private void sendBroadcast(FtsHallCPComponentAttr attr, UserInfo anchorInfo, UserInfo userInfo, FtsHallCPComponentAttr.RewardItem hit) {
        JSONObject jsonData = new JSONObject(5);
        jsonData.put("rewardName", hit.getRewardName());
        jsonData.put("rewardIcon", hit.getRewardIcon());
        jsonData.put("rewardCount", hit.getRewardCount());
        jsonData.put("rewardUnit", hit.getRewardUnit());
        jsonData.put("svgaUrl", attr.getSvgaURL());
        GameecologyActivity.BannerBroadcast broadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                .setActId(attr.getActId())
                .setBannerId(5097001)
                .setBannerType(0)
                .setAnchorNick(anchorInfo.nick)
                .setAnchorLogo(anchorInfo.avatar_url)
                .setUserNick(userInfo.nick)
                .setUserLogo(userInfo.avatar_url)
                .setJsonData(jsonData.toJSONString())
                .build();

        GameecologyActivity.GameEcologyMsg bannerMsg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                .setBannerBroadcast(broadcast)
                .build();
        if (attr.isExcludeDanmaku()) {
            broadCastHelpService.broadcastExcludeDanmakuChannel(bannerMsg);
        } else {
            svcSDKService.broadcastTemplate(Template.Jiaoyou, bannerMsg);
        }
    }

    private void sendAppBroadcast(String seq, FtsHallCPComponentAttr attr, UserInfo anchorInfo, UserInfo userInfo, FtsHallCPComponentAttr.RewardItem hit) {
        AppBannerEvent2 bannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq, KafkaService.getBroBusiId(BusiId.MAKE_FRIEND), 3);
        bannerEvent.setUid(0);
        bannerEvent.setContentType(6);
        bannerEvent.setPushUidlist(Collections.emptyList());
        AppBannerSvgaConfig2 svgaConfig = buildSvgaConfig(attr, anchorInfo, userInfo, hit);
        bannerEvent.setSvgaConfig(svgaConfig);

        if (attr.isExcludeDanmaku()) {
            kafkaService.sendAppBannerKafkaExcludeDanmuku(bannerEvent);
        } else {
            kafkaService.sendAppBannerKafka(bannerEvent);
        }
    }

    private AppBannerSvgaConfig2 buildSvgaConfig(FtsHallCPComponentAttr attr, UserInfo anchorInfo, UserInfo userInfo, FtsHallCPComponentAttr.RewardItem hit) {
        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        List<BannerSvgaTextConfig> svgaTexts = attr.getSvgaTexts();
        List<Map<String, AppBannerSvgaText>> contentLayers = new ArrayList<>(svgaTexts.size());
        if (CollectionUtils.isNotEmpty(svgaTexts)) {
            String[] textReplace = {userInfo.nick, anchorInfo.nick, String.valueOf(hit.getRewardCount()), hit.getRewardUnit()};
            for (BannerSvgaTextConfig svgaText : svgaTexts) {
                AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
                String text = StringUtils.replaceEach(svgaText.getText(), TEXT_SEARCH_LIST, textReplace);
                appBannerSvgaText.setText(text);
                appBannerSvgaText.setNameCountLimit(svgaText.getNameCountLimit());
                appBannerSvgaText.setGravity(svgaText.getGravity());
                if (StringUtils.isNotBlank(svgaText.getImages())) {
                    appBannerSvgaText.setImgs(Splitter.on(',').trimResults().omitEmptyStrings().splitToList(svgaText.getImages()));
                }

                if (StringUtils.startsWith(svgaText.getFontSize(), StringUtil.OPEN_BRACE)) {
                    appBannerSvgaText.setFontSize(JSON.parseObject(svgaText.getFontSize(), new TypeReference<HashMap<String, Integer>>(){}));
                }

                contentLayers.add(Map.of(svgaText.getKey(), appBannerSvgaText));
            }
        }
        svgaConfig.setContentLayers(contentLayers);

        Map<String, String> svgaImages = attr.getSvgaImages();
        Map<String, String> imageMap = new HashMap<>(svgaImages.size());
        if (MapUtils.isNotEmpty(svgaImages)) {
            String[] imageReplace = { userInfo.avatar_url, anchorInfo.avatar_url, hit.getRewardIcon() };
            for (Map.Entry<String, String> entry : svgaImages.entrySet()) {
                String imageKey = entry.getKey();
                String image = StringUtils.replaceEach(entry.getValue(), IMAGE_SEARCH_LIST, imageReplace);
                imageMap.put(imageKey, image);
            }
        }

        svgaConfig.setImgLayers(Collections.singletonList(imageMap));

        AppBannerLayout layout = new AppBannerLayout();
        layout.setType(attr.getLayoutType());
        if (StringUtil.isNotBlank(attr.getLayoutMargin())) {
            layout.setMargin(JSON.parseObject(attr.getLayoutMargin(), new TypeReference<HashMap<String, List<Integer>>>() {
            }));
        }

        svgaConfig.setLayout(layout);

        svgaConfig.setLoops(attr.getLoops());
        svgaConfig.setWhRatio(attr.getWhRatio());

        svgaConfig.setClickLayerName(attr.getClickLayerName());
        svgaConfig.setSvgaURL(attr.getSvgaURL());
        svgaConfig.setMiniURL(attr.getMiniURL());
        svgaConfig.setJumpSvgaURL(attr.getJumpSvgaURL());
        svgaConfig.setJumpMiniURL(attr.getJumpMiniURL());

        return svgaConfig;
    }

    private void sendGradeNotice(long actId, long anchorUid, long userUid, long incrStep, String gradeNoticeTpl) {
        Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(List.of(anchorUid, userUid), false);
        UserBaseInfo anchor = userInfoMap.get(anchorUid);
        UserBaseInfo user = userInfoMap.get(userUid);

        String userNick = HtmlUtil.escape(user == null ? "" : user.getNick());
        String anchorNick = HtmlUtil.escape(anchor == null ? "" : anchor.getNick());
        String anchorMsg = String.format(gradeNoticeTpl, userNick, incrStep);
        String userMsg = String.format(gradeNoticeTpl, anchorNick, incrStep);
        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(PBCommonNoticeType.HALL_CP_GRADE_NOTICE)
                .setNoticeValue(anchorMsg);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.unicastUid(anchorUid, msg);
        log.info("sendGradeNotice with:{} to:{}", anchorMsg, anchorUid);

        if (anchorUid == userUid) {
            return;
        }
        panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(actId)
                .setNoticeType(PBCommonNoticeType.HALL_CP_GRADE_NOTICE)
                .setNoticeValue(userMsg);
        msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();
        svcSDKService.unicastUid(userUid, msg);
        log.info("sendGradeNotice with:{} to:{}", userMsg, userUid);
    }

    @GetMapping("getRewardBalance")
    public Response<String> getRewardBalance(@RequestParam(name = "actId") int actId,
                                              @RequestParam(name = "cmptIndex") int cmptIndex) {
        FtsHallCPComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        Date now = commonService.getNow(actId);
        String worthLimitKey = makeKey(attr, String.format(WORTH_LIMIT, DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2)));
        String value = actRedisDao.get(getRedisGroupCode(actId), worthLimitKey);
        int balance = StringUtils.isNumeric(value) ? Integer.parseInt(value) : 0;

        BigDecimal decimal = new BigDecimal(balance).divide(BigDecimal.TEN, 1, RoundingMode.HALF_UP);

        return Response.success(decimal.toString());
    }

    @GetMapping("getGradeCpList")
    public Response<GroupGrade> getGradeCpList(@RequestParam(name = "actId") int actId,
                                               @RequestParam(name = "cmptIndex") int cmptIndex,
                                               @RequestParam(name = "group", required = false) Long group) {
        FtsHallCPComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        Date now = commonService.getNow(actId);
        final int date = Integer.parseInt(DateFormatUtils.format(now, DateUtil.PATTERN_TYPE2));
        ActivityInfoVo actInfo = actInfoService.queryActivityInfo(actId);
        if (date < attr.getStartDate() || now.getTime() <= actInfo.getBeginTime()) {
            return Response.success(new GroupGrade(0, Collections.emptyList(), 0));
        }

        int state;
        boolean breaking = false;
        if (date > attr.getEndDate() || now.getTime() >= actInfo.getEndTime()) {
            state = 2 * attr.getBreakDates().size() + 2;
        } else {
            state = 1;
            for (int breakDate : attr.getBreakDates()) {
                if (breakDate > date) {
                    continue;
                }

                if (breakDate < date) {
                    state += 2;
                }

                if (breakDate == date) {
                    breaking = true;
                    state += 1;
                }
            }
        }

        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));
        if (group == null) {
            String totalKey = makeKey(attr, TOTAL_STEP);
            String value = redisTemplate.opsForValue().get(totalKey);
            long totalStep = 0;
            if (StringUtils.isNumeric(value)) {
                totalStep = Long.parseLong(value);
            }

            group = totalStep / attr.getAwardStep();
            if (breaking && group > 0) {
                group--;
            }
        }

        String key = makeKey(attr, String.format(GRADE_RECORD, group));
        Set<String> values = redisTemplate.opsForZSet().range(key, 0, -1);
        if (CollectionUtils.isEmpty(values)) {
            return Response.success(new GroupGrade(group, Collections.emptyList(), state));
        }

        List<GradeRecord> records = values.stream().map(value -> JSON.parseObject(value, GradeRecord.class)).toList();
        Set<Long> uids = new HashSet<>(records.size() * 2);
        for (GradeRecord record : records) {
            uids.add(record.anchorUid);
            uids.add(record.userUid);
        }

        Map<Long, UserBaseInfo> userInfoMap = commonService.batchGetUserInfos(List.copyOf(uids), false);
        List<GradeRecordDetail> gradeRecords = new ArrayList<>(records.size());
        for (GradeRecord record : records) {
            GradeRecordDetail item = new GradeRecordDetail(record);
            if (userInfoMap.get(record.anchorUid) != null) {
                item.setAnchorNick(userInfoMap.get(record.anchorUid).getNick());
                item.setAnchorAvatar(userInfoMap.get(record.anchorUid).getLogo());
            }

            if (userInfoMap.get(record.userUid) != null) {
                item.setUserNick(userInfoMap.get(record.userUid).getNick());
                item.setUserAvatar(userInfoMap.get(record.userUid).getLogo());
            }

            if (record.currentGrade == attr.getAwardStep()) {
                long expectedStep = (group + 1) * attr.getAwardStep();
                FtsHallCPComponentAttr.RewardItem groupReward = getGroupReward(attr, expectedStep, redisTemplate);
                if (groupReward != null) {
                    item.setRewardName(groupReward.getRewardName());
                    item.setRewardIcon(groupReward.getRewardIcon());
                    item.setRewardCount(groupReward.getRewardCount());
                    item.setRewardUnit(groupReward.getRewardUnit());
                }
            }

            gradeRecords.add(item);
        }

        return Response.success(new GroupGrade(group, gradeRecords, state));
    }

    @GetMapping("getRewardRecords")
    public Response<List<RewardRecordDetail>> getRewardRecords(@RequestParam(name = "actId") int actId,
                                                              @RequestParam(name = "cmptIndex") int cmptIndex,
                                                              @RequestParam(name = "count", required = false, defaultValue = "50") int count) {
        FtsHallCPComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        count = Math.min(100, count);
        StringRedisTemplate redisTemplate = actRedisDao.getRedisTemplate(getRedisGroupCode(actId));

        String recordKey = makeKey(attr, REWARD_RECORD);
        Set<String> values = redisTemplate.opsForZSet().reverseRange(recordKey, 0, count - 1);
        if (CollectionUtils.isEmpty(values)) {
            return Response.success(Collections.emptyList());
        }
        List<RewardRecord> records = values.stream().filter(value -> StringUtils.startsWith(value, StringUtil.OPEN_BRACE)).map(value -> JSON.parseObject(value, RewardRecord.class)).toList();
        Set<Long> uids = new HashSet<>(records.size() * 2);
        for (RewardRecord record : records) {
            uids.add(record.anchorUid);
            uids.add(record.userUid);
        }

        Map<Long, WebdbUserInfo> userInfoMap = commonService.batchYyUserInfo(List.copyOf(uids));
        List<RewardRecordDetail> result = new ArrayList<>(records.size());
        for (RewardRecord record : records) {
            RewardRecordDetail item = new RewardRecordDetail(record);

            WebdbUserInfo userInfo = userInfoMap.get(record.userUid);
            if (userInfo != null) {
                item.setUserNick(userInfo.getNick());
                item.setUserAvatar(userInfo.getAvatar());
            }

            WebdbUserInfo anchorInfo = userInfoMap.get(record.anchorUid);
            if (anchorInfo != null) {
                item.setAnchorNick(anchorInfo.getNick());
                item.setAnchorAvatar(anchorInfo.getAvatar());
            }

            FtsHallCPComponentAttr.RewardItem rewardItem = getRewardItem(attr, record.packageId);
            if (rewardItem != null) {
                item.setRewardName(rewardItem.getRewardName());
                item.setRewardIcon(rewardItem.getRewardIcon());
                item.setRewardCount(rewardItem.getRewardCount());
                item.setRewardUnit(rewardItem.getRewardUnit());
            }

            result.add(item);
        }

        return Response.success(result);
    }

    @GetMapping("testAppBanner")
    public Response<Integer> testAppBanner(@RequestParam(name = "actId") int actId,
                                           @RequestParam(name = "cmptIndex") int cmptIndex,
                                           @RequestParam(name = "anchorUid") long anchorUid,
                                           @RequestParam(name = "userUid") long userUid,
                                           @RequestParam(name = "packageId") long packageId) {
        if (SysEvHelper.isDeploy() && !commonService.isGrey((long) actId)) {
            return Response.fail(401, "not allow");
        }
        FtsHallCPComponentAttr attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        Map<Long, UserInfo> userInfoMap = ftsBaseInfoBridgeClient.getFtsUserInfoMap(List.of(anchorUid, userUid));
        UserInfo anchorInfo = userInfoMap.get(anchorUid), userInfo = userInfoMap.get(userUid);
        String seq = UUID.randomUUID().toString();
        FtsHallCPComponentAttr.RewardItem hit = attr.getRewardItems().stream().filter(item -> item.getRewardPackageId() == packageId).findFirst().orElse(null);
        if (hit == null) {
            return Response.fail(400, "packageId not exist");
        }
        sendAppBroadcast(seq, attr, anchorInfo, userInfo, hit);

        return Response.success(1);
    }

    private FtsHallCPComponentAttr.RewardItem getGroupReward(FtsHallCPComponentAttr attr, long currentStep, StringRedisTemplate redisTemplate) {
        String recordKey = makeKey(attr, REWARD_RECORD);
        Set<String> rewards = redisTemplate.opsForZSet().rangeByScore(recordKey, currentStep, currentStep);
        if (CollectionUtils.isNotEmpty(rewards)) {
            RewardRecord record = rewards.stream().findFirst().filter(r -> StringUtils.startsWith(r, StringUtil.OPEN_BRACE)).map(r -> JSON.parseObject(r, RewardRecord.class)).orElse(null);
            if (record != null) {
                return getRewardItem(attr, record.packageId);
            }
        }

        return null;
    }

    private FtsHallCPComponentAttr.RewardItem getRewardItem(FtsHallCPComponentAttr attr, long packageId) {
        return attr.getRewardItems().stream().filter(item -> item.getRewardPackageId() == packageId).findFirst().orElse(null);
    }

    @Getter
    @Setter
    public static class GradeRecord {
        protected long anchorUid;
        protected long userUid;
        protected long group;
        protected long addGrade;
        protected long currentGrade;

        public GradeRecord() {
        }

        public GradeRecord(long anchorUid, long userUid, long group, long addGrade, long currentGrade) {
            this.anchorUid = anchorUid;
            this.userUid = userUid;
            this.group = group;
            this.addGrade = addGrade;
            this.currentGrade = currentGrade;
        }
    }

    @Getter
    @Setter
    public static class GradeRecordDetail extends GradeRecord {
        protected String anchorNick;
        protected String anchorAvatar;
        protected String userNick;
        protected String userAvatar;
        protected String rewardName;
        protected String rewardIcon;
        protected int rewardCount;
        protected String rewardUnit;

        public GradeRecordDetail() {}

        public GradeRecordDetail(GradeRecord record) {
            this.anchorUid = record.anchorUid;
            this.userUid = record.userUid;
            this.group = record.group;
            this.addGrade = record.addGrade;
            this.currentGrade = record.currentGrade;
        }
    }

    @Getter
    @Setter
    public static class RewardRecordDetail extends RewardRecord {
        protected String anchorNick;
        protected String anchorAvatar;
        protected String userNick;
        protected String userAvatar;
        protected String rewardName;
        protected String rewardIcon;
        protected int rewardCount;
        protected String rewardUnit;

        public RewardRecordDetail() {
        }

        public RewardRecordDetail(RewardRecord record) {
            super(record.currentStep, record.anchorUid, record.userUid, record.packageId, record.date);
        }
    }

    @Getter
    @Setter
    public static class RewardRecord {
        protected long currentStep;
        protected long anchorUid;
        protected long userUid;
        protected long packageId;
        @JSONField(format = "yyyy-MM-dd HH:mm:ss")
        protected Date date;

        public RewardRecord() {
        }

        public RewardRecord(long currentStep, long anchorUid, long userUid, long packageId, Date date) {
            this.currentStep = currentStep;
            this.anchorUid = anchorUid;
            this.userUid = userUid;
            this.packageId = packageId;
            this.date = date;
        }
    }

    @Getter
    @Setter
    public static class GroupGrade {
        protected long group;
        protected List<GradeRecordDetail> gradeRecords;
        /**
         * <pre>
         * 当前时间所在的段：
         * 0- 活动开始前
         * 1- 第一阶段
         * 2- 第一阶段结束，第二阶段开始前
         * 3- 第二阶段
         * 4- 第二阶段结束，第三阶段开始前
         * 5- 第三阶段
         * ……
         * </pre>
         */
        protected int state;

        public GroupGrade() {
        }

        public GroupGrade(long group, List<GradeRecordDetail> gradeRecords, int state) {
            this.group = group;
            this.gradeRecords = gradeRecords;
            this.state = state;
        }
    }
}
