package com.yy.gameecology.hdzj.element.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableSortedMap;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.SendGiftEvent;
import com.yy.gameecology.activity.client.thrift.WeidengServiceClient;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.service.ActInfoService;
import com.yy.gameecology.activity.service.SvcSDKService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.TailLightComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * 勋章组件
 *
 * <AUTHOR>
 * @date 2021/15 14:25
 */
@UseRedisStore
@Component
public class TailLightComponent extends BaseActComponent<TailLightComponentAttr> {

    @Autowired
    private WeidengServiceClient weidengServiceClient;
    @Autowired
    private SvcSDKService svcSDKService;
    @Autowired
    private ActRedisGroupDao actRedisDao;
    @Autowired
    private ActInfoService actInfoService;


    public static final String TAIL_LIGHT_LEVEL = "tail_light_level";

    public static final String TAIL_LIGHT_TIPS_ = "tail_light_tips_%s";

    @Override
    public Long getComponentId() {
        return ComponentId.TAILLIGHT_TASK;
    }

    /**
     * web点亮或者查询尾灯--目前只能点亮一级
     *
     * @param uid
     * @param query
     * @return
     */
    public JSONObject tailLightWeb(long actId, long uid, boolean query) {

        TailLightComponentAttr attr = getUniqueComponentAttr(actId);
        if (attr == null) {
            log.error("tailLightWeb error,not find config ,actId:{}", actId);
            return new JSONObject();
        }
        return query ? queryTailLightByWeb(attr, uid) : tailLightByWeb(attr, uid);
    }


    /**
     * pc 端查询下一级尾灯弹窗、点亮二级尾灯，pb协议
     *
     * @param actId
     * @param uid
     * @param query
     * @return
     */
    public GameecologyActivity.Act202008_TailLightResp.Builder tailLight(long actId, long uid, boolean query) {
        List<TailLightComponentAttr> attrs = getAllComponentAttrs(actId);
        if (attrs.isEmpty() || !actInfoService.inActTime(actId)) {
            return null;
        }


        if (attrs.size() != 1) {
            log.error("tailLightWeb error,not find config ,actId:{}", actId);
            return null;
        }
        TailLightComponentAttr attr = attrs.get(0);
        if (query) {
            //弹窗-下一级尾灯
            return tipTailLight(attr, uid);
        } else {
            //点亮二级尾灯
            tailLightLevel(attr, uid, 2);
            return null;
        }
    }

    /**
     * 活动期间送收费礼物发3级尾灯
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = SendGiftEvent.class, canRetry = true)
    public void onSendGiftEvent(SendGiftEvent event, TailLightComponentAttr attr) {
        long actId = attr.getActId();
        if (!actInfoService.inActTime(actId)) {
            //不在时间内
            log.info("onSendGiftEvent info not in activity time event = {}", JSON.toJSONString(event));
            return;
        }

        if (!ArrayUtils.contains(attr.getGiftIds(), event.getGiftId())) {
            return;
        }
        tailLightLevel(attr, event.getSendUid(), 3);
    }

    /**
     * 点亮尾灯-判断并发送尾灯
     *
     * @param attr
     * @param userUid
     * @param level
     * @return
     */
    private boolean tailLight(TailLightComponentAttr attr, long userUid, int level) {
        Long packageId = attr.getLevelPackageMap().get(level);
        if (packageId == null) {
            log.error("tailLight error, packageId not find .actId:{},userId:{},level:{}", attr.getActId(), userUid, level);
            return false;
        }
        String key = makeKey(attr, TAIL_LIGHT_LEVEL);
        String groupCode = getRedisGroupCode(attr.getActId());
        long oldLevel = actRedisDao.hSetGrownReturnOld(groupCode, key, userUid + "", level);
        return oldLevel < level ? weidengServiceClient.lightUpWeidengByHdzt(BusiId.GAME_ECOLOGY, userUid, attr.getTaskId(), packageId) : false;
    }

    /**
     * 查询用户当前等级
     *
     * @param request
     * @param userUid
     * @return
     */
    private int queryTailLightLevel(TailLightComponentAttr request, long userUid) {
        String key = makeKey(request, TAIL_LIGHT_LEVEL);
        String groupCode = getRedisGroupCode(request.getActId());
        return Convert.toInt(actRedisDao.hget(groupCode, key, userUid + ""), 0);
    }

    /**
     * web端查询勋章等级
     *
     * @param attr
     * @param uid
     * @return
     */
    private JSONObject queryTailLightByWeb(TailLightComponentAttr attr, long uid) {
        JSONObject data = new JSONObject();
        int level = queryTailLightLevel(attr, uid);
        data.put("level", level);
        data.put("status", 0);
        data.put("msg", "ok");
        return data;
    }


    /**
     * 通过web点亮1级勋章，后面可以做成配置
     *
     * @param attr
     * @param uid
     * @return
     */
    private JSONObject tailLightByWeb(TailLightComponentAttr attr, long uid) {
        JSONObject data = new JSONObject();

        int level = queryTailLightLevel(attr, uid);
        data.put("level", level);
        //错误状态
        data.put("status", 1);

        if (!actInfoService.inActTime(attr.getActId())) {
            data.put("msg", "不在活动期间");
            return data;
        }

        if (tailLight(attr, uid, 1)) {
            data.put("level", 1);
            data.put("status", 0);
            data.put("msg", "成功点亮1级尾灯！");
            return data;
        }

        data.put("msg", "很遗憾，点亮失败，请刷新重试！");
        return data;
    }


    /**
     * 点亮尾灯并单播提醒用户
     *
     * @param attr
     * @param userUid
     * @param level
     */
    private void tailLightLevel(TailLightComponentAttr attr, long userUid, int level) {

        boolean success = tailLight(attr, userUid, level);

        if (success) {
            String url = attr.getMaterialMap().get(level);
            if (StringUtil.isBlank(url)) {
                log.error("tipTailLight error,not find material,actId:{},cmptId:{},cmptUseInx{},level:{} MaterialMap:{}"
                        , attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), level, JSON.toJSONString(attr.getMaterialMap()));
                return;
            }
            //发送提示弹窗
            GameecologyActivity.Act202008_TailLightTips.Builder tips = GameecologyActivity.Act202008_TailLightTips.newBuilder()
                    .setLevel(level).setUrl(url);
            GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                    .setUri(GameecologyActivity.PacketType.kAct202008_TailLightTips_VALUE)
                    .setAct202008TailLightTips(tips)
                    .build();
            log.info("tailLightLevel uid:{} level:{}", userUid, level);
            svcSDKService.unicastUid(userUid, msg);
        }

    }

    /**
     * tip、引导用户点亮尾灯
     *
     * @param attr
     * @param uid
     * @return
     */
    private GameecologyActivity.Act202008_TailLightResp.Builder tipTailLight(TailLightComponentAttr attr, long uid) {

        String uidStr = String.valueOf(uid);
        int level = queryTailLightLevel(attr, uid);
        int nextLevel = level + 1;
        //找到最近的需要tip的等级
        Map.Entry<Integer, Integer> entry = ImmutableSortedMap.copyOf(attr.getLevelTipCountMap()).ceilingEntry(nextLevel);

        if (entry != null) {
            Integer tipLevel = entry.getKey();
            String url = attr.getMaterialMap().get(tipLevel);
            if (StringUtil.isBlank(url)) {
                log.error("tipTailLight error,not find material,actId:{},cmptId:{},cmptUseInx{},level:{} MaterialMap:{}"
                        , attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), tipLevel, JSON.toJSONString(attr.getMaterialMap()));
                return null;
            }
            String nextLevelTipKey = makeKey(attr, String.format(TAIL_LIGHT_TIPS_, tipLevel));

            String groupCode = getRedisGroupCode(attr.getActId());
            if (actRedisDao.hIncrByKey(groupCode, nextLevelTipKey, uidStr, 1) <= entry.getValue()) {
                return GameecologyActivity.Act202008_TailLightResp.newBuilder().setLevel(level).setQuery(true)
                        .setUrl(url);
            }
        }
        return null;
    }
}

